using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Furion.FriendlyException;
using Furion.Logging;
using IotGateway.Plugin.Core;
using IotGateway.Plugin.Core.Models;
using IotGateway.SerialPortByTcp.Models;
using IotGateway.SerialPortByTcp.Utils;
using Microsoft.Extensions.Logging;

namespace IotGateway.SerialPortByTcp;

/// <summary>
///     串口TCP服务器插件
/// </summary>
public class SerialPortByTcpPlugin : PluginBase
{
    /// <summary>
    ///     日志记录器
    /// </summary>
    private ILogger<SerialPortByTcpPlugin>? _logger;

    /// <summary>
    ///     配置文件路径
    /// </summary>
    private readonly string _configPath;

    /// <summary>
    ///     配置
    /// </summary>
    private readonly SerialPortByTcpConfig _config;

    /// <summary>
    ///     协议配置
    /// </summary>
    private ProtocolConfigItem _protocolConfig;

    /// <summary>
    ///     TCP服务器
    /// </summary>
    private TcpServer? _tcpServer;

    /// <summary>
    ///     取消令牌源
    /// </summary>
    private CancellationTokenSource? _cancellationTokenSource;

    /// <summary>
    ///     客户端连接集合
    /// </summary>
    private readonly ConcurrentDictionary<string, TcpClient> _clients = new();

    /// <summary>
    ///     客户端权限信息字典
    /// </summary>
    private readonly ConcurrentDictionary<string, ClientAuthInfo> _clientAuthInfos = new();

    /// <summary>
    ///     配置属性
    /// </summary>
    public object? Configuration => _config;

    /// <summary>
    ///     构造函数
    /// </summary>
    public SerialPortByTcpPlugin()
    {
        try
        {
            // 初始化日志记录器
            InitLogger();

            _configPath = Path.Combine(AppContext.BaseDirectory, "Configs", "serialporttcp.json");
            _config = LoadConfiguration() ?? new SerialPortByTcpConfig();
            _configuration = _config;

            // 初始化协议配置
            InitProtocolConfig();
        }
        catch (Exception ex)
        {
            // 确保构造函数不会抛出异常
            Console.WriteLine($"串口TCP插件初始化失败: {ex.Message}");
        }
    }



    /// <summary>
    ///     初始化日志记录器
    /// </summary>
    private void InitLogger()
    {
        try
        {
            var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole()
                    .SetMinimumLevel(LogLevel.Debug);
            });
            _logger = loggerFactory.CreateLogger<SerialPortByTcpPlugin>();
        }
        catch (Exception ex)
        {
        }
    }

    /// <summary>
    ///     初始化协议配置
    /// </summary>
    private void InitProtocolConfig()
    {
        try
        {
            string configPath = Path.Combine(AppContext.BaseDirectory, "Configs", "protocol.json");

            // 确保配置文件目录存在
            string configDir = Path.GetDirectoryName(configPath);
            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            // 加载协议配置
            var protocolConfig = ProtocolConfig.Load(configPath);
            _protocolConfig = protocolConfig.GetItem(_config.ProtocolType);

            // 使用Console输出，避免_logger为null的情况
            _logger?.LogInformation("协议配置加载成功: Type={Type}, PacketPrefix={PacketPrefix}, PacketSuffix={PacketSuffix}",
                _config.ProtocolType,
                string.IsNullOrEmpty(_protocolConfig.PacketPrefix) ? "无" : _protocolConfig.PacketPrefix,
                string.IsNullOrEmpty(_protocolConfig.PacketSuffix) ? "无" : _protocolConfig.PacketSuffix);
        }
        catch (Exception ex)
        {
            // 使用默认的协议配置
            _protocolConfig = new ProtocolConfigItem();
        }
    }

    /// <summary>
    ///     设置DNC配置（支持外部动态注入DNC配置，保证_dncConfigs赋值）
    /// </summary>
    /// <param name="dncConfig">DNC配置</param>
    /// <returns>设置结果</returns>
    public override async Task SetDncConfigAsync(DncConfig dncConfig)
    {
        // 调用基类方法，维护_dncConfigs集合
        await base.SetDncConfigAsync(dncConfig);

        try
        {
            _logger?.LogInformation("接收到DNC配置: {DeviceCode}, 启用: {Enabled}", dncConfig.DeviceCode, dncConfig.Enabled);

            // 如果配置是启用的，为这个配置创建目录
            if (dncConfig.Enabled)
            {
                // 确保用户的发送和接收目录存在
                string sendPath = GetUserSpecificPath(dncConfig.DeviceCode, _config.SendPath);
                string receivePath = GetUserSpecificPath(dncConfig.DeviceCode, _config.ReceivePath);

                EnsureDirectoryExists(sendPath);
                EnsureDirectoryExists(receivePath);
                _logger?.LogInformation("用户 {DeviceCode} 的目录创建完成，发送目录: {SendPath}, 接收目录: {ReceivePath}",
                    dncConfig.DeviceCode, sendPath, receivePath);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError("SetDncConfigAsync异常: {ErrorMessage}", ex.Message);
        }
    }

    /// <summary>
    ///     加载配置
    /// </summary>
    private SerialPortByTcpConfig? LoadConfiguration()
    {
        if (File.Exists(_configPath))
        {
            var json = File.ReadAllText(_configPath);
            var config = JsonSerializer.Deserialize<SerialPortByTcpConfig>(json);
            _logger?.LogInformation("TCP配置加载成功: Port={Port}, MaxConnections={MaxConnections}",
                config?.Port, config?.MaxConnections);
            return config;
        }

        return null;
    }

    /// <summary>
    ///     保存配置
    /// </summary>
    private void SaveConfiguration()
    {
        _logger?.LogInformation("正在保存TCP配置...");
        var directory = Path.GetDirectoryName(_configPath);
        if (!Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory!);
            _logger?.LogInformation("创建配置目录: {Directory}", directory);
        }

        var json = JsonSerializer.Serialize(_config, new JsonSerializerOptions
        {
            WriteIndented = true
        });
        File.WriteAllText(_configPath, json);
        _logger?.LogInformation("TCP配置保存成功");

    }

    /// <summary>
    ///     插件名称
    /// </summary>
    public override string Name => "Serial Port TCP";

    /// <summary>
    ///     插件版本
    /// </summary>
    public override string Version => "1.0.0";

    /// <summary>
    ///     插件描述
    /// </summary>
    public override string Description => "串口TCP服务器插件";

    /// <summary>
    ///     插件分类
    /// </summary>
    public override string Category => "Dnc";

    /// <summary>
    ///     初始化
    /// </summary>
    public override async Task InitializeAsync()
    {
        _logger?.LogInformation("TCP服务器初始化开始: {Config}", JsonSerializer.Serialize(_config));

        // 如果自启动
        if (_config.Enabled)
        {
            _logger?.LogInformation("TCP服务器配置为自启动，准备启动服务器");
            await StartAsync();
        }

        await base.InitializeAsync();

    }

    /// <summary>
    ///     启动
    /// </summary>
    public override async Task StartAsync()
    {
        try
        {
            _logger?.LogInformation("正在启动TCP服务器...");

            if (_tcpServer != null)
            {
                Console.WriteLine("停止已有TCP服务器");
                await StopAsync();

                // 添加短暂延迟，确保端口完全释放
                await Task.Delay(1000);
            }

            // 检查端口是否可用
            if (!IsPortAvailable(_config.Port))
            {
                var errorMessage = $"端口 {_config.Port} 已被占用，无法启动TCP服务器";
                throw Oops.Oh(errorMessage);
            }

            foreach (var dncConfig in _dncConfigs)
            {
                // 确保文件目录存在
                string sendPath = GetUserSpecificPath(dncConfig.DeviceCode, _config.SendPath);
                // 
                string receivePath = GetUserSpecificPath(dncConfig.DeviceCode, _config.ReceivePath);
                EnsureDirectoryExists(sendPath);
                EnsureDirectoryExists(receivePath);
                _logger?.LogInformation("文件目录检查完成，发送目录: {SendPath}, 接收目录: {ReceivePath}", sendPath, receivePath);
            }

            // 初始化TCP服务器
            _tcpServer = new TcpServer(IPAddress.Any, _config.Port);

            // 创建取消令牌
            _cancellationTokenSource = new CancellationTokenSource();
            var token = _cancellationTokenSource.Token;

            // 启动监听
            _tcpServer.Start();
            _logger?.LogInformation("TCP服务器已启动，正在监听端口 {Port}", _config.Port);

            // 开始接受客户端连接
            _ = AcceptClientsAsync(token);

            await base.StartAsync();
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    /// 接受客户端连接的异步方法
    /// </summary>
    private async Task AcceptClientsAsync(CancellationToken cancellationToken)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                var client = await _tcpServer!.AcceptTcpClientAsync(cancellationToken);
                var clientEndPoint = client.Client.RemoteEndPoint?.ToString() ?? "未知客户端";

                _logger?.LogInformation("接受到新的客户端连接: {ClientEndPoint}", clientEndPoint);

                // 检查是否超过最大连接数
                if (_clients.Count >= _config.MaxConnections)
                {
                    client.Close();
                    continue;
                }

                // 添加到客户端字典
                _clients.TryAdd(clientEndPoint, client);
                _logger?.LogInformation("当前连接客户端数量: {Count}/{MaxConnections}", _clients.Count, _config.MaxConnections);

                // 设置客户端超时
                client.ReceiveTimeout = _config.ConnectionTimeout;
                client.SendTimeout = _config.ConnectionTimeout;
                // client.SendBufferSize = 1024 * 1024;  // 1m

                // 处理客户端连接
                _ = HandleClientAsync(client, clientEndPoint, cancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger?.LogInformation("接受客户端连接的任务已取消");
        }
    }

    /// <summary>
    /// 处理客户端连接
    /// </summary>
    private async Task HandleClientAsync(TcpClient client, string clientEndPoint, CancellationToken cancellationToken)
    {
        // 记录会话开始时间用于性能分析
        var sessionStartTime = DateTime.Now;
        var totalBytesReceived = 0L;
        var totalPacketsProcessed = 0;

        try
        {
            _logger?.LogInformation("[{SessionId}] 开始处理客户端 {ClientEndPoint} 连接，会话时间: {StartTime}",
                sessionStartTime.Ticks, clientEndPoint, sessionStartTime);

            bool authenticated = await AuthenticateClientAsync(client, clientEndPoint);
            if (!authenticated)
            {
                client.Close();
                _clients.TryRemove(clientEndPoint, out _);
                return;
            }
            else
            {
                _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 认证成功，认证耗时: {AuthTime}ms",
                    sessionStartTime.Ticks, clientEndPoint, (DateTime.Now - sessionStartTime).TotalMilliseconds);
            }

            // 创建会话级变量 - 使用实例级Dictionary存储每个会话的状态
            var sessionState = new Dictionary<string, object>
            {
                ["IsWaitFileUpload"] = false,
                ["UploadFileName"] = string.Empty,
                ["FileBuffer"] = new StringBuilder()
            };
            StringBuilder receivePacket = new StringBuilder();

            _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 会话状态初始化完成，初始状态: 等待文件上传={IsWaitFileUpload}, 上传文件名={UploadFileName}",
                sessionStartTime.Ticks, clientEndPoint, sessionState["IsWaitFileUpload"], sessionState["UploadFileName"]);

            // 获取用户特定的文件路径
            string sendPath = string.Empty;
            string receivePath = string.Empty;

            // 从认证信息中获取DeviceCode
            if (_clientAuthInfos.TryGetValue(clientEndPoint, out var authInfo) && !string.IsNullOrEmpty(authInfo.DeviceCode))
            {
                sendPath = GetUserSpecificPath(authInfo.DeviceCode, _config.SendPath);
                receivePath = GetUserSpecificPath(authInfo.DeviceCode, _config.ReceivePath);
            }

            // 确保目录存在
            EnsureDirectoryExists(sendPath);
            EnsureDirectoryExists(receivePath);

            _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 文件路径配置完成，发送路径: {SendPath}, 接收路径: {ReceivePath}",
                sessionStartTime.Ticks, clientEndPoint, sendPath, receivePath);

            await using var stream = client.GetStream();
            byte[] buffer = new byte[4096];

            while (!cancellationToken.IsCancellationRequested && client.Connected)
            {
                int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);

                if (bytesRead == 0)
                {
                    _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 已断开连接 - 收到0字节数据，会话统计: 总接收字节={TotalBytes}, 总处理包数={TotalPackets}, 会话持续时间={SessionDuration}ms",
                        sessionStartTime.Ticks, clientEndPoint, totalBytesReceived, totalPacketsProcessed, (DateTime.Now - sessionStartTime).TotalMilliseconds);
                    break;
                }

                totalBytesReceived += bytesRead;

                // 处理接收到的数据
                string dataReceived = BytesTools.ByteArrToASCII(buffer, 0, bytesRead);

                // 累加包数据
                receivePacket.Append(dataReceived);

                // 检查是否包含完整的数据包
                dataReceived = receivePacket.ToString();

                // 参考Class1中的逻辑，使用更严格的前后缀检查
                bool isPacketComplete = false;
                bool isFilePacketComplete = false;
                var indexPositions = (-1, -1, -1, -1); // (前缀位置, 后缀位置, 文件前缀位置, 文件后缀位置)

                if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketPrefix) && dataReceived.Contains(_protocolConfig.PacketPrefix))
                {
                    indexPositions.Item1 = dataReceived.IndexOf(_protocolConfig.PacketPrefix, StringComparison.Ordinal);
                }

                if (indexPositions.Item1 != -1 && !string.IsNullOrEmpty(_protocolConfig.PacketSuffix) && dataReceived.Contains(_protocolConfig.PacketSuffix))
                {
                    indexPositions.Item2 = dataReceived.IndexOf(_protocolConfig.PacketSuffix, indexPositions.Item1 + 1, StringComparison.Ordinal);
                }

                if (indexPositions.Item1 > -1 && indexPositions.Item2 > -1)
                {
                    isPacketComplete = true;
                }

                if (!string.IsNullOrWhiteSpace(_protocolConfig.FilePrefix) && dataReceived.Contains(_protocolConfig.FilePrefix))
                {
                    indexPositions.Item3 = dataReceived.IndexOf(_protocolConfig.FilePrefix, indexPositions.Item1 + 1, StringComparison.Ordinal);
                }

                if (indexPositions.Item3 != -1 && !string.IsNullOrWhiteSpace(_protocolConfig.FileSuffix) && dataReceived.Contains(_protocolConfig.FileSuffix))
                {
                    indexPositions.Item4 = dataReceived.IndexOf(_protocolConfig.FileSuffix, indexPositions.Item3 + 1, StringComparison.Ordinal);
                }

                if (indexPositions.Item3 > -1 && indexPositions.Item4 > -1)
                {
                    isFilePacketComplete = true;
                }

                // 当检测到完整的包才进行解析
                if (isPacketComplete || isFilePacketComplete)
                {
                    totalPacketsProcessed++;

                    _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 检测到完整数据包 #{PacketNumber}，内容长度: {Length}",
                        sessionStartTime.Ticks, clientEndPoint, totalPacketsProcessed, dataReceived.Length);

                    await ParseMessageAsync(client, clientEndPoint, dataReceived, sessionState, sendPath, receivePath);

                    // 清空已处理的数据
                    receivePacket.Clear();
                }
            }
        }
        finally
        {
            var sessionDuration = (DateTime.Now - sessionStartTime).TotalMilliseconds;

            // 断开连接并从字典中移除
            if (client.Connected)
            {
                client.Close();
                _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 连接主动关闭", sessionStartTime.Ticks, clientEndPoint);
            }

            _clients.TryRemove(clientEndPoint, out _);
            _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 会话结束 - 持续时间: {Duration}ms, 接收总字节: {TotalBytes}, 处理总包数: {TotalPackets}, 平均包大小: {AvgPacketSize} 字节, 当前总连接数: {CurrentConnections}",
                sessionStartTime.Ticks, clientEndPoint, sessionDuration, totalBytesReceived, totalPacketsProcessed,
                totalPacketsProcessed > 0 ? totalBytesReceived / totalPacketsProcessed : 0, _clients.Count);
        }
    }

    /// <summary>
    /// 解析消息
    /// </summary>
    private async Task ParseMessageAsync(TcpClient client, string clientEndPoint, string content,
        Dictionary<string, object> sessionState, string sendPath, string receivePath)
    {
        var messageStartTime = DateTime.Now;

        try
        {
            _logger?.LogInformation("[消息解析] {ClientEndPoint} 开始消息解析，消息长度: {ContentLength}, 开始时间: {StartTime}",
                clientEndPoint, content.Length, messageStartTime);

            // 提取会话状态变量
            bool isWaitFileUpload = (bool)sessionState["IsWaitFileUpload"];
            string uploadFileName = (string)sessionState["UploadFileName"];

            _logger?.LogInformation("[消息解析] {ClientEndPoint} 当前会话状态 - 等待文件上传: {IsWaitFileUpload}, 上传文件名: '{UploadFileName}'",
                clientEndPoint, isWaitFileUpload, uploadFileName);

            // 如果会话状态中不存在FileBuffer，创建一个
            if (!sessionState.ContainsKey("FileBuffer"))
            {
                sessionState["FileBuffer"] = new StringBuilder();
                _logger?.LogInformation("[消息解析] {ClientEndPoint} 文件缓冲区不存在，已创建新的缓冲区", clientEndPoint);
            }

            // 首先检查是否处于文件上传模式，如果是，则优先处理文件上传
            if (isWaitFileUpload && !string.IsNullOrEmpty(uploadFileName))
            {
                _logger?.LogInformation("[消息解析] {ClientEndPoint} 检测到文件上传模式，开始处理文件数据，目标文件名: '{FileName}'",
                    clientEndPoint, uploadFileName);

                // 将数据添加到文件缓冲区
                StringBuilder fileBuffer = (StringBuilder)sessionState["FileBuffer"];
                var beforeAppendLength = fileBuffer.Length;
                fileBuffer.Append(content);
                var afterAppendLength = fileBuffer.Length;

                _logger?.LogInformation("[消息解析] {ClientEndPoint} 文件数据追加到缓冲区 - 追加前: {BeforeLength} 字符, 追加内容: {AppendLength} 字符, 追加后: {AfterLength} 字符",
                    clientEndPoint, beforeAppendLength, content.Length, afterAppendLength);

                string fileBufferStr = fileBuffer.ToString();
                string prefix = _protocolConfig.FilePrefix;
                string suffix = _protocolConfig.FileSuffix;

                _logger?.LogInformation("[消息解析] {ClientEndPoint} 开始检查文件完整性 - 文件前缀: '{FilePrefix}', 文件后缀: '{FileSuffix}', 当前缓冲区大小: {BufferSize} 字符",
                    clientEndPoint, prefix ?? "null", suffix ?? "null", fileBufferStr.Length);

                // 检查是否同时包含文件前缀和后缀，这表示文件接收完成
                bool isFileComplete = false;
                int prefixPos = -1;
                int suffixPos = -1;

                // 先检查前缀
                if (!string.IsNullOrEmpty(prefix))
                {
                    prefixPos = fileBufferStr.IndexOf(prefix, StringComparison.Ordinal);
                    if (prefixPos >= 0)
                    {
                        _logger?.LogInformation("[消息解析] {ClientEndPoint} 在文件缓冲区中找到前缀 '{Prefix}' 在位置: {Position}",
                            clientEndPoint, prefix, prefixPos);

                        // 再检查后缀，且后缀位置必须在前缀之后
                        if (!string.IsNullOrEmpty(suffix))
                        {
                            suffixPos = fileBufferStr.IndexOf(suffix, prefixPos + prefix.Length, StringComparison.Ordinal);
                            if (suffixPos > prefixPos)
                            {
                                isFileComplete = true;
                                _logger?.LogInformation("[消息解析] {ClientEndPoint} 在文件缓冲区中找到后缀 '{Suffix}' 在位置: {Position}，文件数据完整",
                                    clientEndPoint, suffix, suffixPos);
                            }
                            else
                            {
                                _logger?.LogInformation("[消息解析] {ClientEndPoint} 未在正确位置找到后缀 '{Suffix}'，后缀搜索结果位置: {Position}",
                                    clientEndPoint, suffix, suffixPos);
                            }
                        }
                        else
                        {
                            _logger?.LogInformation("[消息解析] {ClientEndPoint} 文件后缀为空，仅依据前缀判断", clientEndPoint);
                        }
                    }
                    else
                    {
                        _logger?.LogInformation("[消息解析] {ClientEndPoint} 未在文件缓冲区中找到前缀 '{Prefix}'",
                            clientEndPoint, prefix);
                    }
                }
                else if (string.IsNullOrEmpty(prefix))
                {
                    _logger?.LogInformation("[消息解析] {ClientEndPoint} 文件前缀为空，仅检查后缀", clientEndPoint);

                    // 如果没有前缀限制，则只检查后缀
                    if (!string.IsNullOrEmpty(suffix))
                    {
                        suffixPos = fileBufferStr.IndexOf(suffix, StringComparison.Ordinal);
                        if (suffixPos >= 0)
                        {
                            isFileComplete = true;
                            _logger?.LogInformation("[消息解析] {ClientEndPoint} 找到后缀 '{Suffix}' 在位置: {Position}，文件数据完整",
                                clientEndPoint, suffix, suffixPos);
                        }
                        else
                        {
                            _logger?.LogInformation("[消息解析] {ClientEndPoint} 未找到后缀 '{Suffix}'",
                                clientEndPoint, suffix);
                        }
                    }
                    else
                    {
                        // 既没有前缀也没有后缀，则认为是完整的
                        isFileComplete = true;
                        _logger?.LogInformation("[消息解析] {ClientEndPoint} 文件前缀和后缀都为空，认为文件数据完整", clientEndPoint);
                    }
                }

                _logger?.LogInformation("[消息解析] {ClientEndPoint} 文件完整性检查结果 - 是否完整: {IsComplete}, 前缀位置: {PrefixPos}, 后缀位置: {SuffixPos}, 文件大小: {FileSize} 字符",
                    clientEndPoint, isFileComplete, prefixPos, suffixPos, fileBufferStr.Length);

                if (isFileComplete)
                {
                    var fileProcessStartTime = DateTime.Now;

                    // 只有同时包含前缀和后缀时才处理文件
                    _logger?.LogInformation("[消息解析] {ClientEndPoint} 检测到完整文件数据，开始文件处理 - 文件名: '{FileName}', 数据大小: {DataSize} 字符",
                        clientEndPoint, uploadFileName, fileBufferStr.Length);

                    ParseFile(clientEndPoint, fileBufferStr, uploadFileName, receivePath);

                    var fileProcessDuration = (DateTime.Now - fileProcessStartTime).TotalMilliseconds;
                    _logger?.LogInformation("[消息解析] {ClientEndPoint} 文件处理完成，耗时: {ProcessTime}ms",
                        clientEndPoint, fileProcessDuration);

                    // 文件处理完成后，重置状态
                    sessionState["IsWaitFileUpload"] = false;
                    sessionState["UploadFileName"] = string.Empty;
                    fileBuffer.Clear();

                    _logger?.LogInformation("[消息解析] {ClientEndPoint} 文件上传会话结束，状态已重置", clientEndPoint);
                }
                else
                {
                    _logger?.LogInformation("[消息解析] {ClientEndPoint} 文件数据不完整，继续等待更多数据 - 当前缓冲区大小: {BufferSize} 字符",
                        clientEndPoint, fileBufferStr.Length);
                }

                var fileHandlingDuration = (DateTime.Now - messageStartTime).TotalMilliseconds;
                _logger?.LogInformation("[消息解析] {ClientEndPoint} 文件上传消息处理完成，总耗时: {TotalTime}ms",
                    clientEndPoint, fileHandlingDuration);
                return; // 文件上传模式下，直接返回，不再进行指令解析
            }

            // 只有非文件上传模式才检查是否为指令
            var orderCheckStartTime = DateTime.Now;

            // 首先检查是否为指令
            bool isOrderProcessed = ParseOrder(client, clientEndPoint, content, ref isWaitFileUpload, ref uploadFileName, sendPath);

            var orderCheckDuration = (DateTime.Now - orderCheckStartTime).TotalMilliseconds;
            _logger?.LogInformation("[消息解析] {ClientEndPoint} 指令检查完成，是否为指令: {IsOrder}, 检查耗时: {CheckTime}ms",
                clientEndPoint, isOrderProcessed, orderCheckDuration);

            if (isOrderProcessed)
            {
                // 更新会话状态
                bool oldIsWaitFileUpload = (bool)sessionState["IsWaitFileUpload"];
                string oldUploadFileName = (string)sessionState["UploadFileName"];

                sessionState["IsWaitFileUpload"] = isWaitFileUpload;
                sessionState["UploadFileName"] = uploadFileName;

                _logger?.LogInformation("[消息解析] {ClientEndPoint} 指令处理完成，会话状态更新 - 等待文件上传: {OldState}→{NewState}, 上传文件名: '{OldFileName}'→'{NewFileName}'",
                    clientEndPoint, oldIsWaitFileUpload, isWaitFileUpload, oldUploadFileName, uploadFileName);

                // 重置文件缓冲区
                if (sessionState.ContainsKey("FileBuffer"))
                {
                    var bufferLength = ((StringBuilder)sessionState["FileBuffer"]).Length;
                    ((StringBuilder)sessionState["FileBuffer"]).Clear();
                    _logger?.LogInformation("[消息解析] {ClientEndPoint} 文件缓冲区已重置，清除了 {BufferLength} 字符",
                        clientEndPoint, bufferLength);
                }

                var processingDuration = (DateTime.Now - messageStartTime).TotalMilliseconds;
                _logger?.LogInformation("[消息解析] {ClientEndPoint} 指令消息处理完成，总耗时: {TotalTime}ms",
                    clientEndPoint, processingDuration);
                return;
            }

            // 如果既不是指令也不在文件上传模式
            var totalProcessingDuration = (DateTime.Now - messageStartTime).TotalMilliseconds;
            _logger?.LogInformation("[消息解析] {ClientEndPoint} 消息类型未识别，可能是无效数据或协议不匹配，消息长度: {ContentLength}, 处理耗时: {ProcessTime}ms",
                clientEndPoint, content.Length, totalProcessingDuration);

            // 记录未识别消息的前100个字符用于调试
            var contentPreview = content.Length > 100 ? content.Substring(0, 100) + "..." : content;
            _logger?.LogInformation("[消息解析] {ClientEndPoint} 未识别消息内容预览: {ContentPreview}",
                clientEndPoint, contentPreview);
        }
        catch
        {
        }
    }

    /// <summary>
    /// 解析指令
    /// </summary>
    private bool ParseOrder(TcpClient client, string clientEndPoint, string receiveMsg,
        ref bool isWaitFileUpload, ref string uploadFileName, string sendPath)
    {
        var parseStartTime = DateTime.Now;

        if (string.IsNullOrEmpty(_protocolConfig.OrderPrefix) || string.IsNullOrEmpty(_protocolConfig.OrderSuffix) || string.IsNullOrEmpty(receiveMsg))
        {
            _logger?.LogInformation("[指令解析] {ClientEndPoint} 指令解析前置检查失败 - 指令前缀: '{OrderPrefix}', 指令后缀: '{OrderSuffix}', 消息长度: {MessageLength}",
                clientEndPoint, _protocolConfig.OrderPrefix ?? "null", _protocolConfig.OrderSuffix ?? "null", receiveMsg?.Length ?? 0);
            return false;
        }

        try
        {
            _logger?.LogInformation("[指令解析] {ClientEndPoint} 开始指令解析，消息长度: {MessageLength}, 指令前缀: '{OrderPrefix}', 指令后缀: '{OrderSuffix}'",
                clientEndPoint, receiveMsg.Length, _protocolConfig.OrderPrefix, _protocolConfig.OrderSuffix);

            // 确保使用不区分大小写的正则表达式
            string regEx = _protocolConfig.OrderPrefix + "([a-zA-Z0-9\r\n. ]+)" + _protocolConfig.OrderSuffix;
            _logger?.LogInformation("[指令解析] {ClientEndPoint} 使用正则表达式: {RegEx}", clientEndPoint, regEx);

            var regexCheckStartTime = DateTime.Now;
            bool isOrder = Regex.IsMatch(receiveMsg, regEx, RegexOptions.IgnoreCase);
            var regexCheckDuration = (DateTime.Now - regexCheckStartTime).TotalMilliseconds;

            _logger?.LogInformation("[指令解析] {ClientEndPoint} 正则匹配结果: {IsOrder}, 匹配耗时: {CheckTime}ms",
                clientEndPoint, isOrder, regexCheckDuration);

            if (!isOrder)
            {
                _logger?.LogInformation("[指令解析] {ClientEndPoint} 消息不匹配指令格式，非指令消息", clientEndPoint);
                return false;
            }

            MatchCollection orderArr = Regex.Matches(receiveMsg, regEx, RegexOptions.IgnoreCase);
            if (orderArr.Count == 0)
            {
                return false;
            }

            _logger?.LogInformation("[指令解析] {ClientEndPoint} 找到 {MatchCount} 个匹配项", clientEndPoint, orderArr.Count);

            Group group = orderArr[0].Groups[1];
            string orderStr = group?.ToString().Replace("\r", "").Replace("\n", "").Replace(" ", "");

            _logger?.LogInformation("[指令解析] {ClientEndPoint} 提取原始指令字符串: '{RawOrder}', 清理后: '{CleanOrder}'",
                clientEndPoint, group?.ToString() ?? "null", orderStr ?? "null");

            if (string.IsNullOrEmpty(orderStr))
            {
                return false;
            }

            // 至少有2个字符，第一个字符是命令号，后面是参数
            if (orderStr.Length < 2)
            {
                return false;
            }

            string orderNum = orderStr.Substring(0, 1); // 命令号
            string fileName = orderStr.Substring(1, orderStr.Length - 1).Trim(); // 参数(文件名)

            _logger?.LogInformation("[指令解析] {ClientEndPoint} 指令解析成功 - 命令号: '{OrderNum}', 参数(文件名): '{FileName}'",
                clientEndPoint, orderNum, fileName);

            // 指令 2 网关=>机床 = 下载文件
            // 指令 3 网关=>机床 = 文件列表
            // 指令 4 机床=>网关 = 上传文件
            var orderType = orderNum == "2" ? "网关=>机床 = 下载文件"
                : orderNum == "3" ? "网关=>机床 = 文件列表"
                : orderNum == "4" ? "机床=>网关 = 上传文件"
                : orderNum == "5" ? "重新连接"
                : "未知指令";

            _logger?.LogInformation("[指令解析] {ClientEndPoint} 指令类型识别: {OrderType} (命令号: {OrderNum})",
                clientEndPoint, orderType, orderNum);

            // 根据不同的指令类型处理
            switch (orderNum)
            {
                case "2": // 发送指定NC文件
                    var case2StartTime = DateTime.Now;
                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 执行下载文件指令，文件名: '{FileName}', 发送路径: '{SendPath}'",
                        clientEndPoint, fileName, sendPath);

                    string filePath = Path.Combine(sendPath, fileName);
                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 构建完整文件路径: '{FilePath}'", clientEndPoint, filePath);

                    bool fileExists = File.Exists(filePath);
                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 文件存在性检查: {FileExists}", clientEndPoint, fileExists);

                    if (fileExists)
                    {
                        var fileInfo = new FileInfo(filePath);
                        _logger?.LogInformation("[指令执行] {ClientEndPoint} 找到NC文件: '{FileName}', 文件大小: {FileSize} 字节, 最后修改: {LastWrite}, 延时: {Delay}ms",
                            clientEndPoint, fileName, fileInfo.Length, fileInfo.LastWriteTime, _config.SendDelayTime);

                        var delayStartTime = DateTime.Now;
                        SpinWait.SpinUntil(() => false, _config.SendDelayTime); // 延时
                        var delayDuration = (DateTime.Now - delayStartTime).TotalMilliseconds;

                        _logger?.LogInformation("[指令执行] {ClientEndPoint} 发送延时完成，实际延时: {ActualDelay}ms",
                            clientEndPoint, delayDuration);

                        // 使用服务器直接发送文件
                        var sendStartTime = DateTime.Now;
                        bool sendResult = SendFileToClientByServer(clientEndPoint, filePath);
                        var sendDuration = (DateTime.Now - sendStartTime).TotalMilliseconds;

                        _logger?.LogInformation("[指令执行] {ClientEndPoint} 文件发送完成，结果: {SendResult}, 发送耗时: {SendTime}ms",
                            clientEndPoint, sendResult, sendDuration);
                    }
                    else
                    {

                        // 使用服务器发送响应
                        var errorResponseStartTime = DateTime.Now;
                        string errorMessage = $"文件不存在: {fileName}";
                        bool errorSendResult = SendResponseToClientByServer(clientEndPoint, errorMessage);
                        var errorResponseDuration = (DateTime.Now - errorResponseStartTime).TotalMilliseconds;

                        _logger?.LogInformation("[指令执行] {ClientEndPoint} 错误响应发送完成，消息: '{ErrorMessage}', 结果: {SendResult}, 耗时: {ResponseTime}ms",
                            clientEndPoint, errorMessage, errorSendResult, errorResponseDuration);
                    }

                    var case2Duration = (DateTime.Now - case2StartTime).TotalMilliseconds;
                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 下载文件指令执行完成，总耗时: {TotalTime}ms",
                        clientEndPoint, case2Duration);
                    break;

                case "3": // 获取NC文件列表
                    var case3StartTime = DateTime.Now;
                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 执行文件列表指令，模式: '{Pattern}', 发送路径: '{SendPath}'",
                        clientEndPoint, fileName, sendPath);

                    var listStartTime = DateTime.Now;
                    var filesList = GetFileListContent(sendPath, fileName);
                    var listDuration = (DateTime.Now - listStartTime).TotalMilliseconds;

                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 文件列表获取完成，耗时: {ListTime}ms, 列表长度: {ListLength} 字符",
                        clientEndPoint, listDuration, filesList?.Length ?? 0);

                    if (!string.IsNullOrEmpty(filesList))
                    {
                        _logger?.LogInformation("[指令执行] {ClientEndPoint} 获取NC文件列表成功，模式: '{Pattern}', 列表大小: {ListSize} 字符",
                            clientEndPoint, fileName, filesList.Length);

                        var listDelayStartTime = DateTime.Now;
                        SpinWait.SpinUntil(() => false, _config.SendDelayTime); // 延时
                        var listDelayDuration = (DateTime.Now - listDelayStartTime).TotalMilliseconds;

                        _logger?.LogInformation("[指令执行] {ClientEndPoint} 列表发送延时完成，实际延时: {ActualDelay}ms",
                            clientEndPoint, listDelayDuration);

                        // 使用服务器发送响应
                        var listSendStartTime = DateTime.Now;
                        bool listSendResult = SendResponseToClientByServer(clientEndPoint, filesList);
                        var listSendDuration = (DateTime.Now - listSendStartTime).TotalMilliseconds;

                        _logger?.LogInformation("[指令执行] {ClientEndPoint} 文件列表发送完成，结果: {SendResult}, 发送耗时: {SendTime}ms",
                            clientEndPoint, listSendResult, listSendDuration);
                    }
                    else
                    {
                        // 使用服务器发送响应
                        var emptyResponseStartTime = DateTime.Now;
                        string emptyMessage = "文件列表为空";
                        bool emptySendResult = SendResponseToClientByServer(clientEndPoint, emptyMessage);
                        var emptyResponseDuration = (DateTime.Now - emptyResponseStartTime).TotalMilliseconds;

                        _logger?.LogInformation("[指令执行] {ClientEndPoint} 空列表响应发送完成，消息: '{EmptyMessage}', 结果: {SendResult}, 耗时: {ResponseTime}ms",
                            clientEndPoint, emptyMessage, emptySendResult, emptyResponseDuration);
                    }

                    var case3Duration = (DateTime.Now - case3StartTime).TotalMilliseconds;
                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 文件列表指令执行完成，总耗时: {TotalTime}ms",
                        clientEndPoint, case3Duration);
                    break;

                case "4": // 准备接收NC文件
                    var case4StartTime = DateTime.Now;
                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 执行上传准备指令，目标文件名: '{FileName}'",
                        clientEndPoint, fileName);

                    bool oldIsWaitFileUpload = isWaitFileUpload;
                    string oldUploadFileName = uploadFileName;

                    isWaitFileUpload = true;
                    uploadFileName = fileName;

                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 文件上传状态更新 - 等待上传: {OldWait}→{NewWait}, 文件名: '{OldName}'→'{NewName}'",
                        clientEndPoint, oldIsWaitFileUpload, isWaitFileUpload, oldUploadFileName, uploadFileName);

                    // 使用服务器发送响应
                    var readyResponseStartTime = DateTime.Now;
                    string readyMessage = $"准备接收文件: {fileName}";
                    bool readySendResult = SendResponseToClientByServer(clientEndPoint, readyMessage);
                    var readyResponseDuration = (DateTime.Now - readyResponseStartTime).TotalMilliseconds;

                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 准备响应发送完成，消息: '{ReadyMessage}', 结果: {SendResult}, 耗时: {ResponseTime}ms",
                        clientEndPoint, readyMessage, readySendResult, readyResponseDuration);

                    var case4Duration = (DateTime.Now - case4StartTime).TotalMilliseconds;
                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 上传准备指令执行完成，总耗时: {TotalTime}ms",
                        clientEndPoint, case4Duration);
                    break;

                case "5": // 重新连接
                    var case5StartTime = DateTime.Now;
                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 执行重新连接指令，准备关闭当前连接", clientEndPoint);

                    // 移除认证信息
                    bool authRemoved = _clientAuthInfos.TryRemove(clientEndPoint, out var removedAuthInfo);
                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 认证信息移除结果: {AuthRemoved}, 移除的用户: '{Username}'",
                        clientEndPoint, authRemoved, removedAuthInfo?.Username ?? "未知");

                    client.Close();
                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 客户端连接已关闭", clientEndPoint);

                    var case5Duration = (DateTime.Now - case5StartTime).TotalMilliseconds;
                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 重新连接指令执行完成，总耗时: {TotalTime}ms",
                        clientEndPoint, case5Duration);
                    break;

                default:
                    var defaultStartTime = DateTime.Now;

                    // 使用服务器发送响应
                    var unknownResponseStartTime = DateTime.Now;
                    string unknownMessage = $"未知指令: {orderNum}";
                    bool unknownSendResult = SendResponseToClientByServer(clientEndPoint, unknownMessage);
                    var unknownResponseDuration = (DateTime.Now - unknownResponseStartTime).TotalMilliseconds;

                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 未知指令响应发送完成，消息: '{UnknownMessage}', 结果: {SendResult}, 耗时: {ResponseTime}ms",
                        clientEndPoint, unknownMessage, unknownSendResult, unknownResponseDuration);

                    var defaultDuration = (DateTime.Now - defaultStartTime).TotalMilliseconds;
                    _logger?.LogInformation("[指令执行] {ClientEndPoint} 未知指令处理完成，总耗时: {TotalTime}ms",
                        clientEndPoint, defaultDuration);
                    break;
            }

            var totalParseDuration = (DateTime.Now - parseStartTime).TotalMilliseconds;
            _logger?.LogInformation("[指令解析] {ClientEndPoint} 指令解析和执行完成，类型: {OrderType}, 总耗时: {TotalTime}ms",
                clientEndPoint, orderType, totalParseDuration);

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 解析并保存上传的文件
    /// </summary>
    private void ParseFile(string clientEndPoint, string receiveMsg, string fileName, string receivePath)
    {
        try
        {
            _logger?.LogInformation($"{clientEndPoint} 接收到文件上传数据，文件名: {fileName}, 数据长度: {receiveMsg.Length}");

            if (string.IsNullOrEmpty(receiveMsg))
            {
                return;
            }

            string filePath = Path.Combine(receivePath, fileName);

            // 提取文件内容
            string fileContent = receiveMsg;
            string prefix = _protocolConfig.FilePrefix;
            string suffix = _protocolConfig.FileSuffix;

            _logger?.LogInformation($"{clientEndPoint} 文件处理开始，前缀: {prefix}, 后缀: {suffix}");

            // 参考Class1.cs中的文件处理逻辑
            bool hasProcessed = false;

            // 1. 如果存在前缀和后缀，提取它们之间的内容
            if (!string.IsNullOrEmpty(prefix) && !string.IsNullOrEmpty(suffix))
            {
                int startPos = fileContent.IndexOf(prefix, StringComparison.Ordinal);
                if (startPos >= 0)
                {
                    // 从前缀后面开始查找后缀
                    int endPos = fileContent.IndexOf(suffix, startPos + prefix.Length, StringComparison.Ordinal);
                    if (endPos > startPos)
                    {
                        // 提取从前缀开始到后缀结束的完整内容
                        fileContent = fileContent.Substring(startPos, endPos + suffix.Length - startPos);
                        _logger?.LogInformation($"{clientEndPoint} 从文件中提取了完整数据，起始位置: {startPos}, 结束位置: {endPos + suffix.Length}, 提取长度: {fileContent.Length}");
                        hasProcessed = true;
                    }
                }
            }

            // 2. 如果只有前缀没有后缀，从前缀开始截取到末尾，并确保加上后缀
            else if (!string.IsNullOrEmpty(prefix) && string.IsNullOrEmpty(suffix))
            {
                int startPos = fileContent.IndexOf(prefix, StringComparison.Ordinal);
                if (startPos >= 0)
                {
                    fileContent = fileContent.Substring(startPos);
                    _logger?.LogInformation($"{clientEndPoint} 从前缀位置截取文件内容，起始位置: {startPos}, 截取长度: {fileContent.Length}");
                    hasProcessed = true;
                }
            }

            // 3. 如果只有后缀没有前缀，从开始截取到后缀，并确保加上前缀
            else if (string.IsNullOrEmpty(prefix) && !string.IsNullOrEmpty(suffix))
            {
                int endPos = fileContent.LastIndexOf(suffix, StringComparison.Ordinal);
                if (endPos >= 0)
                {
                    fileContent = fileContent.Substring(0, endPos + suffix.Length);
                    _logger?.LogInformation($"{clientEndPoint} 截取到后缀位置，结束位置: {endPos + suffix.Length}, 截取长度: {fileContent.Length}");
                    hasProcessed = true;
                }
            }
            else
            {
                _logger?.LogInformation($"{clientEndPoint} 文件无需前后缀处理");
                hasProcessed = true;
            }

            // 4. 如果处理后文件内容缺少前缀，添加前缀
            if (!string.IsNullOrEmpty(prefix) && !fileContent.StartsWith(prefix))
            {
                fileContent = prefix + fileContent;
                _logger?.LogInformation($"{clientEndPoint} 添加前缀: {prefix}");
            }

            // 5. 如果处理后文件内容缺少后缀，添加后缀
            if (!string.IsNullOrEmpty(suffix) && !fileContent.EndsWith(suffix))
            {
                fileContent = fileContent + suffix;
                _logger?.LogInformation($"{clientEndPoint} 添加后缀: {suffix}");
            }

            // 记录处理后的文件内容长度和大小
            _logger?.LogInformation($"{clientEndPoint} 处理后的文件内容长度: {fileContent.Length}, 内容(前100字符): {(fileContent.Length > 100 ? fileContent.Substring(0, 100) + "..." : fileContent)}");

            // 保存文件
            SaveFileContent(filePath, fileContent);
            _logger?.LogInformation($"{clientEndPoint} 保存文件成功: {filePath}, 文件内容长度: {fileContent.Length}");
        }
        catch
        {
        }
    }

    /// <summary>
    /// 获取文件列表内容
    /// </summary>
    private string GetFileListContent(string folderPath, string pattern)
    {
        try
        {
            if (!Directory.Exists(folderPath))
            {
                return string.Empty;
            }

            string[] files = Directory.GetFiles(folderPath);
            if (files.Length == 0)
            {
                return string.Empty;
            }

            var sb = new StringBuilder();

            // 添加文件列表前缀
            sb.Append(_protocolConfig.FilePrefix);

            // 添加列表头部
            string listHead = string.Format(_protocolConfig.ListFileHead, pattern);
            sb.Append(listHead);

            // 添加文件列表
            int index = 0;
            foreach (var file in files)
            {
                string fileName = Path.GetFileName(file);
                sb.AppendFormat("N{0}({1}){2}", index, fileName, _protocolConfig.LineBreak);
                index++;
            }

            // 添加文件列表后缀
            sb.Append(_protocolConfig.FileSuffix);
            sb.Append(_protocolConfig.LineBreak);

            return sb.ToString();
        }
        catch
        {
            return string.Empty;
        }
    }

    /// <summary>
    /// 保存文件内容
    /// </summary>
    private void SaveFileContent(string filePath, string content)
    {
        try
        {
            // 确保目录存在
            string directory = Path.GetDirectoryName(filePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                _logger?.LogInformation($"创建文件目录: {directory}");
            }

            // 写入文件
            using (var fs = new FileStream(filePath, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None))
            using (var sw = new StreamWriter(fs, Encoding.Default))
            {
                fs.SetLength(0);
                sw.Write(content);
            }

            _logger?.LogInformation($"文件保存成功: {filePath}");
        }
        catch
        {
        }
    }

    /// <summary>
    /// 确保目录存在
    /// </summary>
    private void EnsureDirectoryExists(string directoryPath)
    {
        if (!Directory.Exists(directoryPath))
        {
            Directory.CreateDirectory(directoryPath);
            _logger?.LogInformation($"创建目录: {directoryPath}");
        }
    }

    /// <summary>
    /// 客户端认证
    /// </summary>
    private async Task<bool> AuthenticateClientAsync(TcpClient client, string clientEndPoint)
    {
        try
        {
            var stream = client.GetStream();
            byte[] authRequest = Encoding.UTF8.GetBytes("AUTH_REQUIRED\r\n");
            await stream.WriteAsync(authRequest, 0, authRequest.Length);

            byte[] buffer = new byte[1024];
            // int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);

            _logger?.LogInformation("客户端 {ClientEndPoint} 尝试连接", clientEndPoint);

            // 解析凭据 (格式: "username:password")
            string[] parts = clientEndPoint.Split(':', 2);
            if (parts.Length != 2)
            {
                byte[] authFailedMsg = Encoding.UTF8.GetBytes("AUTH_FAILED\r\n");
                await stream.WriteAsync(authFailedMsg, 0, authFailedMsg.Length);
                return false;
            }

            string username = parts[0];
            string password = parts[1];

            _logger?.LogInformation("客户端 {ClientEndPoint} 尝试认证用户: {Username}", clientEndPoint, username);

            // 然后检查DNC配置
            var dncConfigs = _dncConfigs;
            if (dncConfigs != null && dncConfigs.Any())
            {
                _logger?.LogInformation("客户端 {ClientEndPoint} 尝试通过DNC配置认证，可用配置数: {Count}", clientEndPoint, dncConfigs.Count);

                // 查找匹配的DNC配置
                var matchedConfig = dncConfigs.FirstOrDefault(c => c.Enabled && c.IpAddress == username);

                if (matchedConfig != null)
                {
                    _logger?.LogInformation("客户端 {ClientEndPoint} 通过DNC配置认证成功: {Username}, 设备: {DeviceCode}",
                        clientEndPoint, username, matchedConfig.DeviceCode);

                    // 设置认证信息
                    _clientAuthInfos.TryAdd(clientEndPoint, new ClientAuthInfo
                    {
                        Username = username,
                        DeviceCode = matchedConfig.DeviceCode,
                        Authenticated = true
                    });

                    byte[] authSuccess = Encoding.UTF8.GetBytes("AUTH_SUCCESS\r\n");
                    await stream.WriteAsync(authSuccess, 0, authSuccess.Length);
                    return true;
                }
            }

            // 认证失败
            _logger?.LogWarning("客户端 {ClientEndPoint} 认证失败，用户名或密码错误: {Username}", clientEndPoint, username);
            byte[] authFailed = Encoding.UTF8.GetBytes("AUTH_FAILED\r\n");
            await stream.WriteAsync(authFailed, 0, authFailed.Length);
            return false;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "客户端 {ClientEndPoint} 认证过程发生错误", clientEndPoint);

            try
            {
                // 尝试发送错误响应
                var stream = client.GetStream();
                byte[] authError = Encoding.UTF8.GetBytes("AUTH_FAILED\r\n");
                await stream.WriteAsync(authError, 0, authError.Length);
            }
            catch { /* 忽略发送错误的异常 */ }

            return false;
        }
    }

    /// <summary>
    /// 检查端口是否可用
    /// </summary>
    /// <param name="port">要检查的端口</param>
    /// <returns>如果端口可用返回true，否则返回false</returns>
    private bool IsPortAvailable(int port)
    {
        try
        {
            // 尝试在指定端口上创建一个TCP监听器
            using var socket = new Socket(
                AddressFamily.InterNetwork,
                SocketType.Stream,
                ProtocolType.Tcp);

            socket.SetSocketOption(
                SocketOptionLevel.Socket,
                SocketOptionName.ReuseAddress,
                true);

            socket.Bind(new IPEndPoint(IPAddress.Any, port));
            socket.Close();
            return true;
        }
        catch (SocketException)
        {
            return false;
        }
    }

    /// <summary>
    ///     停止
    /// </summary>
    public override async Task StopAsync()
    {
        Log.Information("正在停止TCP服务器...");

        try
        {
            // 取消所有后台任务
            if (_cancellationTokenSource != null)
            {
                if (!_cancellationTokenSource.IsCancellationRequested)
                {
                    _cancellationTokenSource.Cancel();
                }
                _cancellationTokenSource.Dispose();
                _cancellationTokenSource = null;
            }

            // 关闭所有连接的客户端
            int clientCount = _clients.Count;
            foreach (var client in _clients)
            {
                try
                {
                    client.Value.Close();
                    Log.Information("关闭客户端 {ClientEndPoint} 连接", client.Key);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "关闭客户端 {ClientEndPoint} 连接时发生错误", client.Key);
                }
            }
            _clients.Clear();
            Log.Information("已关闭所有 {ClientCount} 个客户端连接", clientCount);

            // 停止TCP服务器
            if (_tcpServer != null)
            {
                try
                {
                    _tcpServer.Stop();
                    _tcpServer = null;
                    Log.Information("TCP服务器已停止");
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "停止TCP服务器时发生错误");
                }
            }

            await Task.Delay(500); // 短暂延迟，确保资源释放

            // 确保垃圾回收运行，释放所有未使用的资源
            GC.Collect();
            GC.WaitForPendingFinalizers();

            Log.Information("TCP服务器已停止");

            // 清空客户端认证信息
            _clientAuthInfos.Clear();

            await base.StopAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "停止TCP服务器时发生错误");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     更新配置
    /// </summary>
    public override async Task UpdateConfigurationAsync(object configuration)
    {
        try
        {
            if (configuration is JsonElement jsonElement)
            {
                // 将JsonElement反序列化为SerialPortByTcpConfig对象
                var config = jsonElement.Deserialize<SerialPortByTcpConfig>(new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (config != null)
                {
                    _config.Port = config.Port;
                    _config.Enabled = config.Enabled;
                    _config.MaxConnections = config.MaxConnections;
                    _config.ConnectionTimeout = config.ConnectionTimeout;
                    _config.RequireAuthentication = config.RequireAuthentication;
                    _config.SendPath = config.SendPath;
                    _config.ReceivePath = config.ReceivePath;

                    // 更新协议类型并重新加载协议配置
                    if (!string.IsNullOrEmpty(config.ProtocolType) && _config.ProtocolType != config.ProtocolType)
                    {
                        _config.ProtocolType = config.ProtocolType;
                        InitProtocolConfig();
                    }

                    // 保存更新后的配置
                    SaveConfiguration();

                    // 如果服务正在运行且配置发生变更，需要重启服务
                    await StopAsync();

                    // 如果启用，则重新启动服务
                    if (_config.Enabled)
                    {
                        await StartAsync();
                    }
                }
            }
            else if (configuration is SerialPortByTcpConfig config)
            {
                // 直接使用SerialPortByTcpConfig对象
                _config.Port = config.Port;
                _config.Enabled = config.Enabled;
                _config.MaxConnections = config.MaxConnections;
                _config.ConnectionTimeout = config.ConnectionTimeout;
                _config.RequireAuthentication = config.RequireAuthentication;
                _config.SendPath = config.SendPath;
                _config.ReceivePath = config.ReceivePath;

                // 更新协议类型并重新加载协议配置
                if (!string.IsNullOrEmpty(config.ProtocolType) && _config.ProtocolType != config.ProtocolType)
                {
                    _config.ProtocolType = config.ProtocolType;
                    InitProtocolConfig();
                }

                SaveConfiguration(); // 保存配置
                await StopAsync();

                if (_config.Enabled)
                {
                    await StartAsync();
                }
            }

            await base.UpdateConfigurationAsync(configuration);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "更新TCP配置失败");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    /// 使用服务器发送响应消息到客户端
    /// </summary>
    private bool SendResponseToClientByServer(string clientEndPoint, string message)
    {
        if (_tcpServer == null)
        {
            _logger?.LogWarning("TCP服务器未初始化，无法发送响应");
            return false;
        }

        try
        {
            // 准备要发送的数据
            var dataList = new List<byte[]>();

            // 添加前缀
            if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketPrefix))
            {
                dataList.Add(BytesTools.ASCIIToASCIIByteArr(_protocolConfig.PacketPrefix));
            }

            // 添加消息内容
            dataList.Add(BytesTools.ASCIIToASCIIByteArr(message));

            // 添加后缀
            if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketSuffix))
            {
                dataList.Add(BytesTools.ASCIIToASCIIByteArr(_protocolConfig.PacketSuffix));
            }

            // 计算总长度
            int totalLength = dataList.Sum(arr => arr.Length);

            // 合并为一个字节数组
            byte[] data = new byte[totalLength];
            int offset = 0;
            foreach (var arr in dataList)
            {
                Buffer.BlockCopy(arr, 0, data, offset, arr.Length);
                offset += arr.Length;
            }

            // 使用TCP服务器发送数据
            bool result = _tcpServer.SendToClient(clientEndPoint, data);

            if (result)
            {
                Log.Information($"已通过服务器发送响应: {message}");
            }
            else
            {
                _logger?.LogWarning($"通过服务器发送响应失败: {message}");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "通过服务器发送响应消息失败");
            return false;
        }
    }

    /// <summary>
    /// 通过服务器将文件发送到客户端
    /// </summary>
    private bool SendFileToClientByServer(string clientEndPoint, string filePath)
    {
        if (_tcpServer == null || !File.Exists(filePath))
        {
            _logger?.LogWarning($"发送文件失败，TCP服务器未初始化或文件不存在: {filePath}");
            return false;
        }

        try
        {
            // 准备所有要发送的数据
            var dataSegments = new List<byte[]>();

            // 添加数据包前缀
            if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketPrefix))
            {
                dataSegments.Add(BytesTools.ASCIIToASCIIByteArr(_protocolConfig.PacketPrefix));
                Log.Information($"{DateTime.Now} 发送前缀: {_protocolConfig.PacketPrefix}");
            }

            // 读取文件内容
            using (var sr = new StreamReader(filePath, Encoding.Default))
            {
                string line;
                string lineBreak = _protocolConfig.LineBreak;

                while ((line = sr.ReadLine()) != null)
                {
                    string getLine = line;
                    // 添加换行符
                    if (line.IndexOf(lineBreak, StringComparison.Ordinal) == -1)
                    {
                        getLine = line + lineBreak;
                    }

                    dataSegments.Add(BytesTools.ASCIIToASCIIByteArr(getLine));
                    Log.Information($"{DateTime.Now} 添加行: {line}");
                }
            }

            // 添加数据包后缀
            if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketSuffix))
            {
                dataSegments.Add(BytesTools.ASCIIToASCIIByteArr(_protocolConfig.PacketSuffix));
                Log.Information($"{DateTime.Now} 发送后缀: {_protocolConfig.PacketSuffix}");
            }

            // 计算总长度
            int totalLength = dataSegments.Sum(arr => arr.Length);

            // 合并为一个字节数组
            byte[] data = new byte[totalLength];
            int offset = 0;
            foreach (var segment in dataSegments)
            {
                Buffer.BlockCopy(segment, 0, data, offset, segment.Length);
                offset += segment.Length;
            }

            // 使用TCP服务器发送数据
            bool result = _tcpServer.SendToClient(clientEndPoint, data);

            if (result)
            {
                Log.Information($"通过服务器发送文件成功: {filePath}");
            }
            else
            {
                _logger?.LogWarning($"通过服务器发送文件失败: {filePath}");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"通过服务器发送文件时发生错误: {filePath}");
            return false;
        }
    }

    /// <summary>
    /// 获取用户特定的文件路径
    /// </summary>
    /// <param name="clientEndPoint">客户端端点</param>
    /// <param name="basePath">基本路径模板</param>
    /// <returns>实际文件路径</returns>
    private string GetUserSpecificPath(string clientEndPoint, string basePath)
    {
        // 如果有设备编码，使用设备编码
        string userIdentifier = clientEndPoint;
        // 替换路径中的占位符
        string path = string.Format(basePath, userIdentifier);
        // 返回完整路径
        return Path.Combine(AppContext.BaseDirectory, path);
    }
}

/// <summary>
/// 客户端连接信息
/// </summary>
public class ClientInfo
{
    /// <summary>
    /// 客户端标识
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// IP地址
    /// </summary>
    public string IpAddress { get; set; } = string.Empty;

    /// <summary>
    /// 端口号
    /// </summary>
    public int Port { get; set; }

    /// <summary>
    /// 连接时间
    /// </summary>
    public DateTime ConnectTime { get; set; }

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected { get; set; }
}

/// <summary>
/// 客户端认证信息
/// </summary>
public class ClientAuthInfo
{
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 设备编码
    /// </summary>
    public string DeviceCode { get; set; } = string.Empty;

    /// <summary>
    /// 是否已认证
    /// </summary>
    public bool Authenticated { get; set; }

    /// <summary>
    /// 认证时间
    /// </summary>
    public DateTime AuthTime { get; set; } = DateTime.Now;
}