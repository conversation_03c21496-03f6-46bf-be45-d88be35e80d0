using System;
using System.Text;

namespace IotGateway.SerialPortByTcp.Utils
{
  /// <summary>
  /// 字节工具类
  /// </summary>
  public static class BytesTools
  {
    /// <summary>
    /// 字节数组转ASCII字符串
    /// </summary>
    /// <param name="byteArr">字节数组</param>
    /// <param name="offset">偏移量</param>
    /// <param name="length">长度</param>
    /// <returns>ASCII字符串</returns>
    public static string ByteArrToASCII(byte[] byteArr, int offset, int length)
    {
      if (byteArr == null || byteArr.Length == 0 || offset < 0 || length <= 0 || offset + length > byteArr.Length)
      {
        return string.Empty;
      }

      return Encoding.ASCII.GetString(byteArr, offset, length);
    }

    /// <summary>
    /// 字节数组转十六进制字符串
    /// </summary>
    /// <param name="byteArr">字节数组</param>
    /// <param name="offset">偏移量</param>
    /// <param name="length">长度</param>
    /// <param name="addSpace">是否添加空格</param>
    /// <returns>十六进制字符串</returns>
    public static string ByteArrToHex(byte[] byteArr, int offset, int length, bool addSpace = false)
    {
      if (byteArr == null || byteArr.Length == 0 || offset < 0 || length <= 0 || offset + length > byteArr.Length)
      {
        return string.Empty;
      }

      StringBuilder sb = new StringBuilder();
      for (int i = offset; i < offset + length; i++)
      {
        sb.Append(byteArr[i].ToString("X2"));
        if (addSpace && i < offset + length - 1)
        {
          sb.Append(' ');
        }
      }
      return sb.ToString();
    }

    /// <summary>
    /// ASCII字符串转字节数组
    /// </summary>
    /// <param name="asciiStr">ASCII字符串</param>
    /// <returns>字节数组</returns>
    public static byte[] ASCIIToASCIIByteArr(string asciiStr)
    {
      if (string.IsNullOrEmpty(asciiStr))
      {
        return Array.Empty<byte>();
      }

      return Encoding.ASCII.GetBytes(asciiStr);
    }

    /// <summary>
    /// 十六进制字符串转字节数组
    /// </summary>
    /// <param name="hexStr">十六进制字符串</param>
    /// <returns>字节数组</returns>
    public static byte[] HexToByteArr(string hexStr)
    {
      if (string.IsNullOrEmpty(hexStr))
      {
        return Array.Empty<byte>();
      }

      // 移除所有空格
      hexStr = hexStr.Replace(" ", "");

      // 确保字符串长度是偶数
      if (hexStr.Length % 2 != 0)
      {
        hexStr = "0" + hexStr;
      }

      byte[] byteArr = new byte[hexStr.Length / 2];
      for (int i = 0; i < byteArr.Length; i++)
      {
        byteArr[i] = Convert.ToByte(hexStr.Substring(i * 2, 2), 16);
      }

      return byteArr;
    }
  }
}