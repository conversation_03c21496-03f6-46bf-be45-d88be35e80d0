using System;
using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace IotGateway.SerialPortByTcp.Utils
{
  /// <summary>
  /// TCP服务器类，使用原生Socket实现，不依赖于TcpListener
  /// </summary>
  public class TcpServer : IDisposable
  {
    /// <summary>
    /// 服务器Socket
    /// </summary>
    private Socket _serverSocket;
    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger _logger;
    private bool _isRunning;
    private bool _disposed;
    private readonly ConcurrentDictionary<string, Socket> _clientSockets = new ConcurrentDictionary<string, Socket>();
    private readonly IPEndPoint _localEndPoint;
    private CancellationTokenSource _acceptCts;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="ipAddress">监听的IP地址</param>
    /// <param name="port">监听的端口</param>
    /// <param name="logger">日志记录器</param>
    public TcpServer(IPAddress ipAddress, int port, ILogger logger = null)
    {
      _logger = logger;
      _isRunning = false;
      _disposed = false;
      _localEndPoint = new IPEndPoint(ipAddress, port);
    }

    /// <summary>
    /// 启动TCP服务器
    /// </summary>
    public void Start()
    {
      if (_disposed)
      {
        throw new ObjectDisposedException(nameof(TcpServer));
      }

      if (_isRunning)
      {
        _logger?.LogWarning("TCP服务器已经在运行中");
        return;
      }

      try
      {
        // 创建服务器Socket
        _serverSocket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);

        // 设置Socket选项
        _serverSocket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);

        _serverSocket.SendBufferSize = 8192;
        _serverSocket.ReceiveBufferSize = 8192;
        // 绑定到指定端口
        _serverSocket.Bind(_localEndPoint);

        // 开始监听，设置最大挂起连接队列长度
        _serverSocket.Listen(100);

        // 创建新的取消令牌源
        _acceptCts = new CancellationTokenSource();

        _isRunning = true;
        _logger?.LogInformation("TCP服务器已启动，监听地址: {LocalEndPoint}", _localEndPoint);
      }
      catch (Exception ex)
      {
        _logger?.LogError(ex, "启动TCP服务器失败");
        throw;
      }
    }

    /// <summary>
    /// 停止TCP服务器
    /// </summary>
    public void Stop()
    {
      if (_disposed)
      {
        return;
      }

      if (!_isRunning)
      {
        _logger?.LogWarning("TCP服务器未运行");
        return;
      }

      try
      {
        // 取消所有接受操作
        _acceptCts?.Cancel();

        // 关闭服务器Socket
        _serverSocket?.Close();
        _serverSocket = null;

        // 关闭所有客户端连接
        foreach (var clientSocket in _clientSockets)
        {
          try
          {
            clientSocket.Value.Close();
            _logger?.LogDebug("关闭客户端连接: {ClientEndPoint}", clientSocket.Key);
          }
          catch (Exception ex)
          {
            _logger?.LogError(ex, "关闭客户端连接时发生错误: {ClientEndPoint}", clientSocket.Key);
          }
        }

        int clientCount = _clientSockets.Count;
        _clientSockets.Clear();
        _isRunning = false;
        _logger?.LogInformation("TCP服务器已停止，关闭了 {ClientCount} 个客户端连接", clientCount);
      }
      catch (Exception ex)
      {
        _logger?.LogError(ex, "停止TCP服务器失败");
        throw;
      }
    }

    /// <summary>
    /// 向指定客户端发送数据
    /// </summary>
    /// <param name="clientEndPoint">客户端端点标识（如"127.0.0.1:12345"）</param>
    /// <param name="data">要发送的数据</param>
    /// <returns>发送是否成功</returns>
    public bool SendToClient(string clientEndPoint, byte[] data)
    {
      if (_disposed)
      {
        _logger?.LogWarning("TCP服务器已释放，无法发送数据");
        return false;
      }

      if (!_isRunning)
      {
        _logger?.LogWarning("TCP服务器未运行，无法发送数据");
        return false;
      }

      try
      {
        // 查找客户端Socket
        if (!_clientSockets.TryGetValue(clientEndPoint, out Socket clientSocket))
        {
          _logger?.LogWarning("客户端 {ClientEndPoint} 未找到，无法发送数据", clientEndPoint);
          return false;
        }

        // 检查Socket是否已连接
        if (!IsSocketConnected(clientSocket))
        {
          _logger?.LogWarning("客户端 {ClientEndPoint} 已断开连接，无法发送数据", clientEndPoint);
          _clientSockets.TryRemove(clientEndPoint, out _);
          _logger?.LogWarning(" 客户端 {ClientEndPoint} 连接已断开并从客户端列表移除", clientEndPoint);
          return false;
        }

        // 发送数据
        int bytesSent = clientSocket.Send(data);
        _logger?.LogDebug("向客户端 {ClientEndPoint} 发送了 {BytesSent} 字节数据", clientEndPoint, bytesSent);
        return bytesSent == data.Length;
      }
      catch (Exception ex)
      {
        _logger?.LogError(ex, "向客户端 {ClientEndPoint} 发送数据时发生错误", clientEndPoint);
        _clientSockets.TryRemove(clientEndPoint, out _);
        _logger?.LogWarning(" 发送数据异常，客户端 {ClientEndPoint} 已从客户端列表移除", clientEndPoint);
        return false;
      }
    }

    /// <summary>
    /// 向指定客户端异步发送数据
    /// </summary>
    /// <param name="clientEndPoint">客户端端点标识（如"127.0.0.1:12345"）</param>
    /// <param name="data">要发送的数据</param>
    /// <returns>发送是否成功</returns>
    public async Task<bool> SendToClientAsync(string clientEndPoint, byte[] data)
    {
      if (_disposed)
      {
        _logger?.LogWarning("TCP服务器已释放，无法发送数据");
        return false;
      }

      if (!_isRunning)
      {
        _logger?.LogWarning("TCP服务器未运行，无法发送数据");
        return false;
      }

      try
      {
        // 查找客户端Socket
        if (!_clientSockets.TryGetValue(clientEndPoint, out Socket clientSocket))
        {
          _logger?.LogWarning("客户端 {ClientEndPoint} 未找到，无法发送数据", clientEndPoint);
          return false;
        }

        // 检查Socket是否已连接
        if (!IsSocketConnected(clientSocket))
        {
          _logger?.LogWarning("客户端 {ClientEndPoint} 已断开连接，无法发送数据", clientEndPoint);
          _clientSockets.TryRemove(clientEndPoint, out _);
          _logger?.LogWarning(" 客户端 {ClientEndPoint} 连接已断开并从客户端列表移除", clientEndPoint);
          return false;
        }

        // 异步发送数据
        int bytesSent = await clientSocket.SendAsync(new ArraySegment<byte>(data), SocketFlags.None);
        _logger?.LogDebug("向客户端 {ClientEndPoint} 异步发送了 {BytesSent} 字节数据", clientEndPoint, bytesSent);
        return bytesSent == data.Length;
      }
      catch (Exception ex)
      {
        _logger?.LogError(ex, "向客户端 {ClientEndPoint} 异步发送数据时发生错误", clientEndPoint);
        _clientSockets.TryRemove(clientEndPoint, out _);
        _logger?.LogWarning("发送数据异常，客户端 {ClientEndPoint} 已从客户端列表移除", clientEndPoint);
        return false;
      }
    }

    /// <summary>
    /// 判断Socket是否仍然连接
    /// </summary>
    private bool IsSocketConnected(Socket socket)
    {
      try
      {
        return !(socket.Poll(1, SelectMode.SelectRead) && socket.Available == 0);
      }
      catch (SocketException ex)
      {
        _logger?.LogWarning("检测Socket连接状态时发生异常: {Message}", ex.Message);
        return false;
      }
    }

    /// <summary>
    /// 异步接受TCP客户端连接
    /// </summary>
    public async Task<TcpClient> AcceptTcpClientAsync(CancellationToken cancellationToken = default)
    {
      if (_disposed)
      {
        throw new ObjectDisposedException(nameof(TcpServer));
      }

      if (!_isRunning)
      {
        throw new InvalidOperationException("TCP服务器未启动");
      }

      try
      {
        // 接受客户端连接
        Socket clientSocket = await _serverSocket.AcceptAsync();

        // 获取客户端端点信息
        string clientEndPoint = clientSocket.RemoteEndPoint?.ToString() ?? "未知客户端";

        // 添加到客户端字典
        _clientSockets.TryAdd(clientEndPoint, clientSocket);

        // 更加突出的连接日志
        _logger?.LogInformation("🔌 新客户端连接成功: {ClientEndPoint}", clientEndPoint);

        // 记录当前连接数量
        _logger?.LogInformation("当前连接客户端数量: {Count}", _clientSockets.Count);

        // 创建并返回TcpClient
        var tcpClient = new TcpClient
        {
          Client = clientSocket
        };

        return tcpClient;
      }
      catch (OperationCanceledException)
      {
        _logger?.LogInformation("接受客户端连接的操作已取消");
        throw;
      }
      catch (Exception ex)
      {
        _logger?.LogError(ex, "接受客户端连接时发生异常");
        throw;
      }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
      if (_disposed)
      {
        return;
      }

      try
      {
        if (_isRunning)
        {
          Stop();
        }

        _acceptCts?.Dispose();
        _acceptCts = null;

        _disposed = true;

        _logger?.LogInformation("TCP服务器资源已释放");
      }
      catch (Exception ex)
      {
        _logger?.LogError(ex, "释放TCP服务器资源时发生错误");
      }

      GC.SuppressFinalize(this);
    }
  }
}