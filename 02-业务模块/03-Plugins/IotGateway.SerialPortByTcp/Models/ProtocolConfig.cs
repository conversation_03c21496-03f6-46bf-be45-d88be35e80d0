using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace IotGateway.SerialPortByTcp.Models
{
  /// <summary>
  /// 协议配置项
  /// </summary>
  public class ProtocolConfigItem
  {
    /// <summary>
    /// 协议类型
    /// </summary>
    public string Type { get; set; } = "General";

    /// <summary>
    /// 包头字符
    /// </summary>
    public string PacketPrefix { get; set; } = "12";

    /// <summary>
    /// 包尾字符
    /// </summary>
    public string PacketSuffix { get; set; } = "14";

    /// <summary>
    /// 文件首字符
    /// </summary>
    public string FilePrefix { get; set; } = "%";

    /// <summary>
    /// 文件尾字符
    /// </summary>
    public string FileSuffix { get; set; } = "%";

    /// <summary>
    /// 文件扩展名，如.MPF
    /// </summary>
    public string FileExtension { get; set; } = "";

    /// <summary>
    /// 指令首字符
    /// </summary>
    public string OrderPrefix { get; set; } = "Q";

    /// <summary>
    /// 指令尾字符
    /// </summary>
    public string OrderSuffix { get; set; } = "V";

    /// <summary>
    /// 获取网关NC文件列表头部格式
    /// </summary>
    public string ListFileHead { get; set; } = "O{0}\n";

    /// <summary>
    /// 换行符
    /// </summary>
    public string LineBreak { get; set; } = "\n";
  }

  /// <summary>
  /// 协议配置
  /// </summary>
  public class ProtocolConfig
  {
    /// <summary>
    /// 协议配置项集合
    /// </summary>
    public List<ProtocolConfigItem> Configs { get; set; } = new List<ProtocolConfigItem>();

    /// <summary>
    /// 加载协议配置
    /// </summary>
    /// <param name="configPath">配置文件路径</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>协议配置</returns>
    public static ProtocolConfig Load(string configPath)
    {
      try
      {
        if (!File.Exists(configPath))
        {
          return CreateDefault();
        }

        string json = File.ReadAllText(configPath);
        var config = JsonSerializer.Deserialize<ProtocolConfig>(json);

        if (config == null || config.Configs == null || config.Configs.Count == 0)
        {
          return CreateDefault();
        }

        return config;
      }
      catch
      {
        return CreateDefault();
      }
    }

    /// <summary>
    /// 创建默认配置
    /// </summary>
    /// <returns>默认协议配置</returns>
    private static ProtocolConfig CreateDefault()
    {
      return new ProtocolConfig
      {
        Configs = new List<ProtocolConfigItem>
                {
                    new ProtocolConfigItem
                    {
                        Type = "General",
                        PacketPrefix = "\u0012",
                        PacketSuffix = "\u0014",
                        FilePrefix = "%",
                        FileSuffix = "%",
                        FileExtension = "",
                        OrderPrefix = "Q",
                        OrderSuffix = "V",
                        ListFileHead = "O{0}\n",
                        LineBreak = "\n"
                    }
                }
      };
    }

    /// <summary>
    /// 获取指定类型的配置项
    /// </summary>
    /// <param name="type">协议类型</param>
    /// <returns>协议配置项</returns>
    public ProtocolConfigItem GetItem(string type)
    {
      // 先获取通用配置作为基础
      var generalConfig = Configs.FirstOrDefault(c => c.Type == "General") ?? new ProtocolConfigItem();

      // 如果请求的就是通用配置，则直接返回
      if (type == "General")
      {
        return generalConfig;
      }

      // 获取指定类型的配置
      var specificConfig = Configs.FirstOrDefault(c => c.Type == type);
      if (specificConfig == null)
      {
        return generalConfig; // 如果找不到特定类型配置，则返回通用配置
      }

      // 合并配置：用特定类型的配置覆盖通用配置中的非空值
      if (!string.IsNullOrEmpty(specificConfig.PacketPrefix))
        generalConfig.PacketPrefix = specificConfig.PacketPrefix;
      if (!string.IsNullOrEmpty(specificConfig.PacketSuffix))
        generalConfig.PacketSuffix = specificConfig.PacketSuffix;
      if (!string.IsNullOrEmpty(specificConfig.FilePrefix))
        generalConfig.FilePrefix = specificConfig.FilePrefix;
      if (!string.IsNullOrEmpty(specificConfig.FileSuffix))
        generalConfig.FileSuffix = specificConfig.FileSuffix;
      if (!string.IsNullOrEmpty(specificConfig.FileExtension))
        generalConfig.FileExtension = specificConfig.FileExtension;
      if (!string.IsNullOrEmpty(specificConfig.OrderPrefix))
        generalConfig.OrderPrefix = specificConfig.OrderPrefix;
      if (!string.IsNullOrEmpty(specificConfig.OrderSuffix))
        generalConfig.OrderSuffix = specificConfig.OrderSuffix;
      if (!string.IsNullOrEmpty(specificConfig.ListFileHead))
        generalConfig.ListFileHead = specificConfig.ListFileHead;
      if (!string.IsNullOrEmpty(specificConfig.LineBreak))
        generalConfig.LineBreak = specificConfig.LineBreak;

      return generalConfig;
    }
  }
}