using IotGateway.Plugin.Core;

namespace IotGateway.SerialPortByTcp.Models;

/// <summary>
/// 串口TCP服务器配置
/// </summary>
public class SerialPortByTcpConfig : IPluginConfig
{
  /// <summary>
  /// 服务端口
  /// </summary>
  public int Port { get; set; } = 8080;

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool Enabled { get; set; } = false;

  /// <summary>
  /// 允许的最大客户端连接数
  /// </summary>
  public int MaxConnections { get; set; } = 10;

  /// <summary>
  /// 连接超时时间(毫秒)
  /// </summary>
  public int ConnectionTimeout { get; set; } = 30000;

  /// <summary>
  /// 是否需要认证
  /// </summary>
  public bool RequireAuthentication { get; set; } = false;

  /// <summary>
  /// 协议类型
  /// </summary>
  public string ProtocolType { get; set; } = "General";

  /// <summary>
  /// 发送延迟时间(毫秒)
  /// </summary>
  public int SendDelayTime { get; set; } = 7000;

  /// <summary>
  /// 文件发送路径
  /// </summary>
  public string SendPath { get; set; } = "RootFiles/{0}/SEND";

  /// <summary>
  /// 文件接收路径
  /// </summary>
  public string ReceivePath { get; set; } = "RootFiles/{0}/REC";

  /// <summary>
  /// 获取配置Schema
  /// </summary>
  public object GetConfigurationSchema()
  {
    return new
    {
      properties = new
      {
        SendDelayTime = new
        {
          type = "integer",
          title = "发送延迟时间(毫秒)",
          description = "发送延迟时间(毫秒)",
          minimum = 0,
          maximum = 100000,
          defaultValue = 10000
        },
        Port = new
        {
          type = "integer",
          title = "端口号",
          description = "TCP服务器监听端口",
          minimum = 1,
          maximum = 65535,
          defaultValue = 8080,
        },
        Enabled = new
        {
          type = "boolean",
          title = "启用状态",
          description = "是否启用TCP服务器"
        },
        MaxConnections = new
        {
          type = "integer",
          title = "最大连接数",
          description = "允许的最大客户端连接数",
          minimum = 1,
          maximum = 100,
          defaultValue = 10
        },
        ConnectionTimeout = new
        {
          type = "integer",
          title = "连接超时(毫秒)",
          description = "客户端连接超时时间(毫秒)",
          minimum = 1000,
          maximum = 300000,
          defaultValue = 30000
        },
        RequireAuthentication = new
        {
          type = "boolean",
          title = "需要认证",
          description = "是否需要用户名密码认证"
        }
      },
      required = new[] { "Port", "MaxConnections", "ConnectionTimeout" }
    };
  }
}