using System.Collections.Generic;

namespace IotGateway.SmbServer.Models;

/// <summary>
/// SMB配置数据传输对象
/// </summary>
public class SmbConfigsDto
{
  /// <summary>
  /// 构造函数
  /// </summary>
  public SmbConfigsDto()
  {
    ShareNameList = new List<string>();
    SharePathList = new List<string>();
    Users = new List<UserDto>();
  }

  /// <summary>
  /// 用户列表
  /// </summary>
  public List<UserDto> Users { get; set; }

  /// <summary>
  /// 共享名称列表
  /// </summary>
  public List<string> ShareNameList { get; set; }

  /// <summary>
  /// 共享路径列表
  /// </summary>
  public List<string> SharePathList { get; set; }
}