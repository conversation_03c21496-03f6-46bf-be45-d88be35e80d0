namespace IotGateway.SmbServer.Models;

/// <summary>
///     设备配置信息
/// </summary>
public class DeviceConfigInfo
{
  /// <summary>
  ///     设备ID
  /// </summary>
  public string DeviceId { get; set; } = string.Empty;

  /// <summary>
  ///     设备名称
  /// </summary>
  public string DeviceName { get; set; } = string.Empty;

  /// <summary>
  ///     工作文件夹
  /// </summary>
  public string WorkFolder { get; set; } = string.Empty;

  /// <summary>
  ///     发送文件夹
  /// </summary>
  public string SendFolder { get; set; } = string.Empty;

  /// <summary>
  ///     接收文件夹
  /// </summary>
  public string ReceiveFolder { get; set; } = string.Empty;
}

/// <summary>
///     设备配置信息扩展方法
/// </summary>
public static class DeviceConfigInfoExtensions
{
  /// <summary>
  ///     转换为设备信息
  /// </summary>
  /// <param name="device">设备</param>
  /// <param name="workFolder">工作文件夹</param>
  /// <param name="sendFolder">发送文件夹</param>
  /// <param name="receiveFolder">接收文件夹</param>
  /// <returns>设备配置信息</returns>
  public static DeviceConfigInfo ToDeviceInfo(this T_DncDevice device, string workFolder, string sendFolder, string receiveFolder)
    {
        return new DeviceConfigInfo
        {
            DeviceId = device.NcDeviceId,
            DeviceName = device.NcDeviceName,
            WorkFolder = workFolder,
            SendFolder = sendFolder,
            ReceiveFolder = receiveFolder
        };
    }
}

/// <summary>
///     设备
/// </summary>
public class T_DncDevice
{
  /// <summary>
  ///     设备ID
  /// </summary>
  public string NcDeviceId { get; set; } = string.Empty;

  /// <summary>
  ///     设备名称
  /// </summary>
  public string NcDeviceName { get; set; } = string.Empty;

  /// <summary>
  ///     设备类型
  /// </summary>
  public string NcDeviceType { get; set; } = string.Empty;

  /// <summary>
  ///     是否激活
  /// </summary>
  public bool IsActive { get; set; } = true;
}