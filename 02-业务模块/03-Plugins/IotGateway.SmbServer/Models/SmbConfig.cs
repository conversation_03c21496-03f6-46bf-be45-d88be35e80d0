using System.Collections.Generic;
using System.ComponentModel;
using IotGateway.Plugin.Core;

namespace IotGateway.SmbServer.Models;

/// <summary>
///     SMB配置类
/// </summary>
public class SmbConfig : IPluginConfig
{
    /// <summary>
    ///     构造函数
    /// </summary>
    public SmbConfig()
    {
        ShareNameMethod = NameMethodEnum.DeviceNoSubFolder;
        FileSuffixType = FileSuffixCaseType.Lower;
    }

    /// <summary>
    ///     是否启用
    /// </summary>
    public bool Enabled { get; set; } = false;

    /// <summary>
    ///     共享名方式
    /// </summary>
    [DisplayName("共享名方式")]
    public NameMethodEnum ShareNameMethod { get; set; }

    /// <summary>
    ///     文件后缀类型
    /// </summary>
    [DisplayName("文件后缀类型")]
    public FileSuffixCaseType FileSuffixType { get; set; }

    /// <summary>
    ///     账号1
    /// </summary>
    [DisplayName("1_账号")]
    public string UserName_1 { get; set; } = string.Empty;

    /// <summary>
    ///     密码1
    /// </summary>
    [DisplayName("1_密码")]
    [PasswordPropertyText]
    public string UserPassword_1 { get; set; } = string.Empty;

    /// <summary>
    ///     账号2
    /// </summary>
    [DisplayName("2_账号")]
    public string UserName_2 { get; set; } = "guest";

    /// <summary>
    ///     密码2
    /// </summary>
    [DisplayName("2_密码")]
    [PasswordPropertyText]
    public string UserPassword_2 { get; set; } = string.Empty;

    /// <summary>
    ///     账号3
    /// </summary>
    [DisplayName("3_账号")]
    public string UserName_3 { get; set; } = "admin";

    /// <summary>
    ///     密码3
    /// </summary>
    [DisplayName("3_密码")]
    [PasswordPropertyText]
    public string UserPassword_3 { get; set; } = "123456";

    /// <summary>
    ///     获取配置Schema
    /// </summary>
    /// <returns>配置Schema</returns>
    public object GetConfigurationSchema()
    {
        return new
        {
            properties = new
            {
                UserName_1 = new
                {
                    type = "string",
                    title = "账号1",
                    description = "SMB服务账号1"
                },
                UserPassword_1 = new
                {
                    type = "string",
                    title = "密码1",
                    description = "SMB服务密码1",
                    format = "password"
                },
                UserName_2 = new
                {
                    type = "string",
                    title = "账号2",
                    description = "SMB服务账号2"
                },
                UserPassword_2 = new
                {
                    type = "string",
                    title = "密码2",
                    description = "SMB服务密码2",
                    format = "password"
                },
                UserName_3 = new
                {
                    type = "string",
                    title = "账号3",
                    description = "SMB服务账号3"
                },
                UserPassword_3 = new
                {
                    type = "string",
                    title = "密码3",
                    description = "SMB服务密码3",
                    format = "password"
                },
                Enabled = new
                {
                    type = "boolean",
                    title = "启用状态",
                    description = "是否启用SMB服务"
                },
                ShareNameMethod = new
                {
                    type = "select",
                    title = "共享名方式",
                    description = "设置共享名称的方式",
                    @enums = new[]
                    {
                        new {label = "设备号",value = 0},
                        new {label = "设备号+子目录",value = 1},
                    },
                    
                },
                FileSuffixType = new
                {
                    type = "select",
                    title = "文件后缀类型",
                    description = "文件后缀的大小写处理方式",
                    @enums = new[]
                    {
                       new {label = "大写",value = 0},
                       new {label = "小写",value = 1},
                       new {label = "保持原样",value = 2}
                    }
                }
            },
            required = new[] { "UserName_2", "UserName_3" }
        };
    }
}