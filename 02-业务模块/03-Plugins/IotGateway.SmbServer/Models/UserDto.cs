using SMBLibrary;

namespace IotGateway.SmbServer.Models;

/// <summary>
///     用户数据传输对象
/// </summary>
public class UserDto
{
  /// <summary>
  ///     用户名
  /// </summary>
  public string Name { get; set; } = string.Empty;

  /// <summary>
  ///     密码
  /// </summary>
  public string Password { get; set; } = string.Empty;

  /// <summary>
  ///     访问权限
  /// </summary>
  public AccessMask Access { get; set; } = AccessMask.GENERIC_ALL;
}