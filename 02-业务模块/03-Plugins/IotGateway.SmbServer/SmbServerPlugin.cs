using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Furion.FriendlyException;
using IotGateway.Plugin.Core;
using IotGateway.Plugin.Core.Models;
using IotGateway.SmbServer.Core;
using IotGateway.SmbServer.Models;
using Microsoft.Extensions.Logging;
using SMBLibrary;
using FileAttributes = System.IO.FileAttributes;

namespace IotGateway.SmbServer;

/// <summary>
///     SMB服务器插件
/// </summary>
public class SmbServerPlugin : PluginBase
{
    #region 私有字段

    private ILogger<SmbServerPlugin>? _logger;
    private readonly SmbConfig _pluginConfig;
    private SmbHelper? _smbServer;
    private bool _isStop = true;
    private ConcurrentDictionary<string, DeviceConfigInfo> _deviceDict = new();
    private const string SEND = "SEND";
    private const string REC = "REC";
    private readonly string _configPath;

    #endregion

    #region 构造函数

    /// <summary>
    ///     构造函数
    /// </summary>
    public SmbServerPlugin()
    {
        _configPath = Path.Combine(AppContext.BaseDirectory, "Configs", "smbserver.json");
        _pluginConfig = LoadConfiguration() ?? new SmbConfig();
        _configuration = _pluginConfig;

        try
        {
            // 确保编码提供程序已注册
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            // 初始化日志记录器
            InitLogger();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"SMB插件初始化失败: {ex.Message}");
        }
    }

    #endregion

    #region 插件接口实现

    /// <summary>
    ///     插件ID
    /// </summary>
    public string Id => "Smb";

    /// <summary>
    ///     插件名称
    /// </summary>
    public override string Name => "SMB服务";

    /// <summary>
    ///     插件描述
    /// </summary>
    public override string Description => "提供SMB文件共享服务";

    /// <summary>
    ///     插件版本
    /// </summary>
    public override string Version => "1.0.0";

    /// <summary>
    ///     插件分类
    /// </summary>
    public override string Category => "Dnc";

    #endregion

    #region 插件生命周期方法

    /// <summary>
    ///     初始化
    /// </summary>
    public override async Task InitializeAsync()
    {
        try
        {
            _logger?.LogInformation("SMB初始化开始: {Config}", JsonSerializer.Serialize(_pluginConfig));

            // 如果自启动
            if (_pluginConfig.Enabled)
            {
                _logger?.LogInformation("SMB服务器配置为自启动，准备启动服务器");
                await StartAsync();
            }

            await base.InitializeAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "SMB服务器初始化失败");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     启动
    /// </summary>
    public override async Task StartAsync()
    {
        try
        {
            _logger?.LogInformation("正在启动SMB服务...");
            Console.WriteLine("[SMB服务] 启动中...");
            Console.WriteLine("[SMB服务] 监听地址: 0.0.0.0:445");

            // 创建根目录
            var rootPath = Path.Combine(AppContext.BaseDirectory, "RootFiles");
            if (!Directory.Exists(rootPath)) Directory.CreateDirectory(rootPath);
            Console.WriteLine($"[SMB调试] RootFiles根目录: {rootPath}");

            // 初始化设备配置
            InitNcDevice();
            Console.WriteLine($"[SMB调试] 设备字典数量: {_deviceDict.Count}");

            // 构建用户和共享配置
            List<UserDto> userList = new List<UserDto>();
            if (!string.IsNullOrEmpty(_pluginConfig.UserName_1))
            {
                Console.WriteLine($"[SMB调试] 用户1: {_pluginConfig.UserName_1}");
                userList.Add(new UserDto
                {
                    Name = _pluginConfig.UserName_1,
                    Password = _pluginConfig.UserPassword_1,
                    Access = AccessMask.GENERIC_ALL
                });
            }

            if (!string.IsNullOrEmpty(_pluginConfig.UserName_2))
            {
                Console.WriteLine($"[SMB调试] 用户2: {_pluginConfig.UserName_2}");
                userList.Add(new UserDto
                {
                    Name = _pluginConfig.UserName_2,
                    Password = _pluginConfig.UserPassword_2,
                    Access = AccessMask.GENERIC_ALL
                });
            }

            if (!string.IsNullOrEmpty(_pluginConfig.UserName_3))
            {
                Console.WriteLine($"[SMB调试] 用户3: {_pluginConfig.UserName_3}");
                userList.Add(new UserDto
                {
                    Name = _pluginConfig.UserName_3,
                    Password = _pluginConfig.UserPassword_3,
                    Access = AccessMask.GENERIC_ALL
                });
            }

            var configDto = new SmbConfigsDto
            {
                Users = userList
            };

            if (_deviceDict.Count > 0)
                foreach (var device in _deviceDict.Values)
                    if (_pluginConfig.ShareNameMethod == NameMethodEnum.DeviceNoSubFolder)
                    {
                        Console.WriteLine($"[SMB调试] 设备注册共享: {device.DeviceId} => {device.WorkFolder}");
                        configDto.ShareNameList.Add(device.DeviceId);
                        configDto.SharePathList.Add(device.WorkFolder);
                    }
                    else if (_pluginConfig.ShareNameMethod == NameMethodEnum.DeviceWithSubFolder)
                    {
                        configDto.ShareNameList.Add($"{device.DeviceId}{SEND}");
                        configDto.SharePathList.Add(Path.Combine(device.WorkFolder, SEND));
                        Console.WriteLine($"[SMB调试] 注册共享: {device.DeviceId}_{SEND} => {Path.Combine(device.WorkFolder, SEND)}");
                        configDto.ShareNameList.Add($"{device.DeviceId}{REC}");
                        configDto.SharePathList.Add(Path.Combine(device.WorkFolder, REC));
                        Console.WriteLine($"[SMB调试] 注册共享: {device.DeviceId}_{REC} => {Path.Combine(device.WorkFolder, REC)}");
                    }

            // 启动SMB服务
            Console.WriteLine($"[SMB调试] 构建SmbHelper实例，用户数: {userList.Count}，共享数: {configDto.ShareNameList.Count}");
            _smbServer = new SmbHelper(configDto, _logger);
            _smbServer.Start();
            _isStop = false;
            _logger?.LogInformation("SMB服务已启动");
            Console.WriteLine("[SMB服务] 启动完成，等待客户端连接...");

            await base.StartAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "SMB服务启动失败");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     停止
    /// </summary>
    public override async Task StopAsync()
    {
        try
        {
            _logger?.LogInformation("正在停止SMB服务...");
            _isStop = true;
            _smbServer?.Stop();
            _smbServer = null;
            await Task.Delay(500);
            _logger?.LogInformation("SMB服务已停止");
            await base.StopAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "停止SMB服务时发生错误");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     更新配置
    /// </summary>
    public override async Task UpdateConfigurationAsync(object configuration)
    {
        await base.UpdateConfigurationAsync(configuration);

        try
        {
            Console.WriteLine("[SMB调试] 更新配置 :{configuration}");

            if (configuration is JsonElement jsonElement)
            {
                // 将JsonElement反序列化为FtpConfig对象
                var config = jsonElement.Deserialize<SmbConfig>(new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (config != null)
                {
                    _pluginConfig.ShareNameMethod = config.ShareNameMethod; // 共享名字方式
                    _pluginConfig.Enabled = config.Enabled; // 是否启用
                    _pluginConfig.FileSuffixType = config.FileSuffixType; // 发送文件路径
                    _pluginConfig.UserName_1 = config.UserName_1; // 用户名
                    _pluginConfig.UserPassword_1 = config.UserPassword_1; // 密码
                    _pluginConfig.UserName_2 = config.UserName_2; // 用户名
                    _pluginConfig.UserPassword_2 = config.UserPassword_2; // 密码
                    _pluginConfig.UserName_3 = config.UserName_3; // 用户名
                    _pluginConfig.UserPassword_3 = config.UserPassword_3; // 密码

                    // 保存更新后的配置
                    SaveConfiguration();

                    await StopAsync();
                    // 如果服务正在运行，需要重启以应用新配置
                    if (_pluginConfig.Enabled) await StartAsync();
                }
            }
            else if (configuration is SmbConfig config)
            {
                // 保持原有的处理逻辑
                _pluginConfig.ShareNameMethod = config.ShareNameMethod;
                _pluginConfig.Enabled = config.Enabled;
                _pluginConfig.FileSuffixType = config.FileSuffixType;
                _pluginConfig.UserName_1 = config.UserName_1;
                _pluginConfig.UserPassword_1 = config.UserPassword_1;
                _pluginConfig.UserName_2 = config.UserName_2;
                _pluginConfig.UserPassword_2 = config.UserPassword_2;
                _pluginConfig.UserName_3 = config.UserName_3;
                _pluginConfig.UserPassword_3 = config.UserPassword_3;

                SaveConfiguration(); // 保存配置
                await StopAsync();

                if (_pluginConfig.Enabled) await StartAsync();
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "更新SMB配置时发生错误");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     保存配置
    /// </summary>
    private void SaveConfiguration()
    {
        try
        {
            _logger?.LogInformation("正在保存SMB配置...");
            var directory = Path.GetDirectoryName(_configPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
                _logger?.LogInformation("创建配置目录: {Directory}", directory);
            }

            var json = JsonSerializer.Serialize(_pluginConfig, new JsonSerializerOptions
            {
                WriteIndented = true
            });
            File.WriteAllText(_configPath, json);
            _logger?.LogInformation("SMB配置保存成功");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "保存SMB配置失败");
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    ///     初始化日志记录器
    /// </summary>
    private void InitLogger()
    {
        try
        {
            var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole()
                    .SetMinimumLevel(LogLevel.Debug);
            });
            _logger = loggerFactory.CreateLogger<SmbServerPlugin>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"日志初始化失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     加载配置
    /// </summary>
    private SmbConfig? LoadConfiguration()
    {
        try
        {
            if (File.Exists(_configPath))
            {
                var json = File.ReadAllText(_configPath);
                var config = JsonSerializer.Deserialize<SmbConfig>(json);
                Console.WriteLine("已加载SMB配置");
                return config;
            }

            Console.WriteLine("SMB配置文件不存在，将使用默认配置");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载SMB配置失败: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    ///     初始化NC设备
    /// </summary>
    private void InitNcDevice()
    {
        try
        {
            _deviceDict = new ConcurrentDictionary<string, DeviceConfigInfo>();
            Console.WriteLine("开始初始化NC设备");
            if (_dncConfigs != null && _dncConfigs.Count > 0)
            {
                Console.WriteLine($"找到 {_dncConfigs.Count} 个可用的DNC配置");
                foreach (var config in _dncConfigs.Where(c => c.Enabled && c.ProtocolType.Equals(Name)))
                {
                    Console.WriteLine($"初始化设备 {config.DeviceCode}");
                    var workFolder = GetNcDeviceFolder(config.DeviceCode);
                    var deviceInfo = new DeviceConfigInfo
                    {
                        DeviceId = config.DeviceCode,
                        DeviceName = config.DeviceName,
                        WorkFolder = workFolder,
                        SendFolder = SEND,
                        ReceiveFolder = REC
                    };
                    _deviceDict.TryAdd(config.DeviceCode, deviceInfo);
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError("SMB初始化设备配置异常: {Message}", ex.Message);
        }
    }

    /// <summary>
    ///     获取NC设备文件夹
    /// </summary>
    /// <param name="ncDeviceId">设备ID</param>
    /// <returns>设备文件夹路径</returns>
    private string GetNcDeviceFolder(string ncDeviceId)
    {
        var deviceNcPath = Path.Combine(AppContext.BaseDirectory, "RootFiles", ncDeviceId);
        try
        {
            Console.WriteLine($"开始初始化设备 {ncDeviceId} 文件夹");
            if (!Directory.Exists(deviceNcPath))
            {
                Console.WriteLine($"创建设备 {ncDeviceId} 文件夹：{deviceNcPath}");
                Directory.CreateDirectory(deviceNcPath);
                Console.WriteLine($"创建设备 {ncDeviceId} 发送文件夹：{Path.Combine(deviceNcPath, SEND)}");
                Directory.CreateDirectory(Path.Combine(deviceNcPath, SEND));
                Console.WriteLine($"创建设备 {ncDeviceId} 接收文件夹：{Path.Combine(deviceNcPath, REC)}");
                Directory.CreateDirectory(Path.Combine(deviceNcPath, REC));


                SetFileReadonly(deviceNcPath, true);
                Console.WriteLine($"设置设备 {ncDeviceId} 文件夹只读");

                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    SetFileReadonly(deviceNcPath, true);
                    Console.WriteLine($"设置设备 {ncDeviceId} 文件夹只读");
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    try
                    {
                        RunLinuxCmd($"sudo chmod -R 777 {deviceNcPath}");
                        RunLinuxCmd($"sudo chmod -R 777 {Path.Combine(deviceNcPath, SEND)}");
                        RunLinuxCmd($"sudo chmod -R 777 {Path.Combine(deviceNcPath, REC)}");
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogInformation("Linux系统执行脚本失败: {Message}", ex.ToString());
                    }

                    _logger?.LogInformation("Linux系统执行脚本");
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError("SMB获取设备目录异常: {Message}", ex.Message);
        }

        return deviceNcPath;
    }

    /// <summary>
    ///     运行Linux命令
    /// </summary>
    /// <param name="cmd">命令</param>
    /// <returns>命令输出</returns>
    public static string RunLinuxCmd(string cmd)
    {
        var process = new Process
        {
            StartInfo = new ProcessStartInfo("/bin/bash", "")
        };

        try
        {
            process.StartInfo.RedirectStandardInput = true;
            process.StartInfo.RedirectStandardOutput = true;
            process.StartInfo.UseShellExecute = false;
            process.Start();

            process.StandardInput.WriteLine(cmd);
            process.StandardInput.Close();

            var result = process.StandardOutput.ReadToEnd();
            process.WaitForExit();
            process.Dispose();

            return result;
        }
        catch (Exception ex)
        {
            throw new Exception("SMB运行LINUX命令异常: " + ex.Message);
        }
    }

    /// <summary>
    ///     设置文件只读
    /// </summary>
    /// <param name="fullpath">文件路径</param>
    /// <param name="flag">是否只读</param>
    public static void SetFileReadonly(string fullpath, bool flag)
    {
        var file = new FileInfo(fullpath);
        if (flag)
            file.Attributes |= FileAttributes.ReadOnly;
        else
            file.Attributes &= ~FileAttributes.ReadOnly;
    }

    
    /// <summary>
    ///     设置DNC配置（支持外部动态注入DNC配置，保证_dncConfigs赋值）
    /// </summary>
    /// <param name="dncConfig">DNC配置</param>
    /// <returns>设置结果</returns>
    public override async Task SetDncConfigAsync(DncConfig dncConfig)
    {
        // 调用基类方法，维护_dncConfigs集合
        await base.SetDncConfigAsync(dncConfig);

        try
        {
            Console.WriteLine($"[SMB调试] 接收到DNC配置: {dncConfig.DeviceCode}, 启用: {dncConfig.Enabled}");
            if (dncConfig.Enabled)
            {
                // 增量初始化单个设备目录
                var workFolder = GetNcDeviceFolder(dncConfig.DeviceCode);
                var deviceInfo = new DeviceConfigInfo
                {
                    DeviceId = dncConfig.DeviceCode,
                    DeviceName = dncConfig.DeviceName,
                    WorkFolder = workFolder,
                    SendFolder = SEND,
                    ReceiveFolder = REC
                };
                _deviceDict.AddOrUpdate(dncConfig.DeviceCode, deviceInfo, (k, v) => deviceInfo);
                Console.WriteLine($"[SMB调试] 已初始化设备 {dncConfig.DeviceCode} 目录并注册到_deviceDict");
            }
            else
            {
                // 禁用则移除
                DeviceConfigInfo removed;
                _deviceDict.TryRemove(dncConfig.DeviceCode, out removed);
                Console.WriteLine($"[SMB调试] 已移除禁用设备 {dncConfig.DeviceCode} 配置");
            }

            // 修正点：每次设备配置变更后自动重启SMB服务，确保共享即时生效
            if (_pluginConfig.Enabled)
            {
                Console.WriteLine("[SMB调试] DNC配置变更，自动重启SMB服务以刷新共享...");
                await StopAsync();
                await StartAsync();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[SMB调试] SetDncConfigAsync异常: {ex.Message}");
            _logger?.LogError(ex, "SetDncConfigAsync异常");
        }
    }

    #endregion
}