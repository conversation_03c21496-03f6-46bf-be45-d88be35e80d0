using System;
using System.Net;
using IotGateway.SmbServer.Models;
using Microsoft.Extensions.Logging;
using SMBLibrary;
using SMBLibrary.Authentication.GSSAPI;
using SMBLibrary.Authentication.NTLM;
using SMBLibrary.Server;
using SMBLibrary.Win32;
using Utilities;

namespace IotGateway.SmbServer.Core;

/// <summary>
///     SMB帮助类
/// </summary>
public class SmbHelper
{
    private readonly ILogger _logger;
    private readonly SmbConfigsDto _smbConfig;
    private readonly SMBServer? _server;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="configs">SMB配置</param>
    /// <param name="logger">日志记录器</param>
    public SmbHelper(SmbConfigsDto configs, ILogger logger)
    {
        _logger = logger;
        _smbConfig = configs;

        try
        {
            if (configs != null && configs.SharePathList != null && configs.SharePathList.Count > 0)
            {
                var shares = new SMBShareCollection();
                EventHandler<AccessRequestArgs> accessHandler = null;

                for (var i = 0; i < configs.SharePathList.Count; i++)
                {
                    var shareName = configs.ShareNameList[i];
                    var sharePath = configs.SharePathList[i] + "/";

                    Console.WriteLine($"[SmbHelper] shareName: {shareName}, sharePath: {sharePath}");
                    // 使用NTDirectoryFileSystem替代自定义文件系统
                    var share = new FileSystemShare(shareName, new NTDirectoryFileSystem(sharePath));

                    accessHandler = (sender, args) =>
                    {
                        var user = configs.Users.Find(x => x.Name == args.UserName);
                        if (user != null)
                        {
                            // 转换AccessMask为FileAccess类型进行比较
                            var requestedAccess = (uint)args.RequestedAccess;
                            var userAccess = (uint)user.Access;
                            if ((requestedAccess & userAccess) == requestedAccess) args.Allow = true;
                        }
                    };

                    share.AccessRequested += accessHandler;
                    shares.Add(share);
                }

                IGSSMechanism authMechanism = new IndependentNTLMAuthenticationProvider(userName =>
                {
                    var user = configs.Users.Find(x => x.Name == userName);
                    return user?.Password;
                });

                var securityProvider = new GSSProvider(authMechanism);
                _server = new SMBServer(shares, securityProvider);

                // 注册日志和连接事件
                _server.LogEntryAdded += OnLogEntryAdded;
                _server.ConnectionRequested += OnConnect;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("SMB 初始化异常: {Message}", ex.Message);
        }
    }

    /// <summary>
    ///     连接请求处理
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnConnect(object sender, ConnectionRequestEventArgs e)
    {
        try
        {
            // 可以在这里添加连接请求处理逻辑
        }
        catch (Exception ex)
        {
            _logger.LogError("SMB 请求连接时异常: {Message}", ex.Message);
        }
    }

    /// <summary>
    ///     日志事件处理
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="entry">日志条目</param>
    private void OnLogEntryAdded(object sender, LogEntry entry)
    {
        try
        {
            switch (entry.Severity)
            {
                case Severity.Critical:
                case Severity.Error:
                    _logger.LogError("errror ======" + entry.Message);
                    break;
                case Severity.Warning:
                    _logger.LogWarning(entry.Message);
                    break;
                case Severity.Information:
                    _logger.LogInformation(entry.Message);
                    break;
                case Severity.Verbose:
                case Severity.Debug:
                case Severity.Trace:
                    _logger.LogDebug("Debug:------" + entry.Message);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("SMB 日志输出异常: {Message}", ex.Message);
        }
    }

    /// <summary>
    ///     启动SMB服务
    /// </summary>
    public void Start()
    {
        try
        {
            if (_server != null)
            {
                _server.Start(IPAddress.Any, SMBTransportType.DirectTCPTransport, true, true);
                _logger.LogInformation("SMB服务已启动");
            }
            else
            {
                _logger.LogWarning("SMB服务未初始化，无法启动");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("SMB 开始连接异常: {Message}", ex.Message);
            throw;
        }
    }

    /// <summary>
    ///     停止SMB服务
    /// </summary>
    public void Stop()
    {
        try
        {
            if (_server != null)
            {
                _server.Stop();
                _logger.LogInformation("SMB服务已停止");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("SMB 停止连接异常: {Message}", ex.Message);
            throw;
        }
    }
}