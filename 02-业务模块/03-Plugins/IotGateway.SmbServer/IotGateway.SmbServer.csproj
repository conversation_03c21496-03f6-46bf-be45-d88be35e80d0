<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <OutputPath>..\..\..\01-应用服务\IotGateway/bin/$(Configuration)/$(TargetFramework)/plugins/services/smb/</OutputPath>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\IotGateway.Plugin.Core\IotGateway.Plugin.Core.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Reference Include="SMBLibrary">
            <HintPath>..\..\..\..\..\..\微信文件\WeChat Files\wxid_bj6miryt25il21\FileStorage\File\2025-04\海德汉-smb\hyiotdnc\plugins\HyIotGatewayDnc.Plugin.Smb\SMBLibrary.dll</HintPath>
        </Reference>
        <Reference Include="SMBLibrary.Adapters">
            <HintPath>..\..\..\..\..\..\微信文件\WeChat Files\wxid_bj6miryt25il21\FileStorage\File\2025-04\海德汉-smb\hyiotdnc\plugins\HyIotGatewayDnc.Plugin.Smb\SMBLibrary.Adapters.dll</HintPath>
        </Reference>
        <Reference Include="SMBLibrary.Win32">
            <HintPath>..\..\..\..\..\..\微信文件\WeChat Files\wxid_bj6miryt25il21\FileStorage\File\2025-04\海德汉-smb\hyiotdnc\plugins\HyIotGatewayDnc.Plugin.Smb\SMBLibrary.Win32.dll</HintPath>
        </Reference>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Models\"/>
    </ItemGroup>

    <ItemGroup>
      <None Remove="IotGateway.SmbServer.csproj.DotSettings" />
    </ItemGroup>

</Project> 