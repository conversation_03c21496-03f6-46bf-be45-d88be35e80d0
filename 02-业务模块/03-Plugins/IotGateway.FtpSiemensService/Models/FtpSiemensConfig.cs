using IotGateway.Plugin.Core;

namespace IotGateway.FtpSiemensService.Models;

/// <summary>
/// FTP服务器配置
/// </summary>
public class FtpSiemensConfig : IPluginConfig
{
  /// <summary>
  /// 服务端口
  /// </summary>
  public int Port { get; set; } = 21;

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool Enabled { get; set; } = false;

  /// <summary>
  /// 文件发送路径
  /// </summary>
  public string SendPath { get; set; } = "RootFiles/{0}/SEND";

  /// <summary>
  /// 文件接收路径
  /// </summary>
  public string ReceivePath { get; set; } = "RootFiles/{0}/REC";

  /// <summary>
  /// 获取配置Schema
  /// </summary>
  public object GetConfigurationSchema()
  {
    return new
    {
      properties = new
      {
        Port = new
        {
          type = "integer",
          title = "端口号",
          description = "FTP服务器监听端口",
          minimum = 1,
          maximum = 65535,
          defaultValue = 21,
        },
        Enabled = new
        {
          type = "boolean",
          title = "启用状态",
          description = "是否启用FTP服务"
        },
      },
      required = new[] { "Port", "RootPath" }
    };
  }
}