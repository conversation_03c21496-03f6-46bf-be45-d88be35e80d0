using System;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using IotGateway.FtpSiemensService.Models;
using Microsoft.Extensions.Logging;

namespace IotGateway.FtpSiemensService.Core;

/// <summary>
/// FTP命令处理器
/// </summary>
public class FtpSiemensCommandHandler
{
  private readonly FtpSiemensSession _siemensSession;
  private readonly FtpSiemensServerPlugin _plugin;
  private readonly ILogger _logger;
  private readonly FtpSiemensConfig _siemensConfig;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="siemensSession">FTP会话</param>
  /// <param name="plugin">FTP服务器插件</param>
  /// <param name="logger">日志记录器</param>
  public FtpSiemensCommandHandler(FtpSiemensSession siemensSession, FtpSiemensServerPlugin plugin, ILogger logger)
  {
    _siemensSession = siemensSession;
    _plugin = plugin;
    _logger = logger;
    _siemensConfig = (FtpSiemensConfig)plugin.Configuration;
  }

  /// <summary>
  /// 处理FTP命令
  /// </summary>
  /// <param name="commandLine">命令行</param>
  public async Task HandleCommandAsync(string commandLine)
  {
    try
    {
      // 解析命令和参数
      var cmdParts = commandLine.Split(new[] { ' ' }, 2);
      var rawCommand = cmdParts[0];

      // 处理可能包含NULL字符(0x00)和其他控制字符的命令
      // 移除所有ASCII控制字符（0x00-0x1F和0x7F）
      var cleanCommand = new string(rawCommand.Where(c => !char.IsControl(c)).ToArray());
      var command = cleanCommand.Trim().ToUpperInvariant();

      var parameter = cmdParts.Length > 1 ? cmdParts[1] : string.Empty;

      // 记录命令和参数，增加更详细的日志
      _logger.LogInformation("命令解析 raw: [{Raw}] clean: [{Clean}] cmd: {Command} param: {Parameter}",
          rawCommand, cleanCommand, command, parameter);

      // 如果清理后的命令为空，认为是无效命令
      if (string.IsNullOrEmpty(command))
      {
        _logger.LogWarning("命令清理后为空");
        await _siemensSession.SendResponseAsync("500 Invalid command");
        return;
      }

      // 根据命令类型处理
      _logger.LogInformation("开始处理命令: {Command}", command);
      command = command.Replace("\0", "");
      switch (command)
      {
        case "USER":
          await HandleUserAsync(parameter);
          break;
        case "PASS":
          _logger.LogInformation("进入PASS命令处理分支");
          await HandlePassAsync(parameter);
          break;
        case "SYST":
          await _siemensSession.SendResponseAsync("215 UNIX Type: L8");
          break;
        case "FEAT":
          await HandleFeatAsync();
          break;
        case "PWD":
        case "XPWD":
          await HandlePwdAsync();
          break;
        case "CWD":
        case "XCWD":
          await HandleCwdAsync(parameter);
          break;
        case "CDUP":
        case "XCUP":
          await HandleCdupAsync();
          break;
        case "TYPE":
          await HandleTypeAsync(parameter);
          break;
        case "PASV":
          await HandlePasvAsync();
          break;
        case "PORT":
          await HandlePortAsync(parameter);
          break;
        case "LIST":
        case "NLST":
          await HandleListAsync(parameter, command == "LIST");
          break;
        case "RETR":
          await HandleRetrAsync(parameter);
          break;
        case "STOR":
          await HandleStorAsync(parameter);
          break;
        case "DELE":
          await HandleDeleAsync(parameter);
          break;
        case "MKD":
        case "XMKD":
          await HandleMkdAsync(parameter);
          break;
        case "RMD":
        case "XRMD":
          await HandleRmdAsync(parameter);
          break;
        case "QUIT":
          await _siemensSession.SendResponseAsync("221 Goodbye");
          break;
        case "NOOP":
          await _siemensSession.SendResponseAsync("200 NOOP command successful");
          break;
        case "SIZE":
          await HandleSizeAsync(parameter);
          break;
        case "MDTM":
          await HandleMdtmAsync(parameter);
          break;
        default:
          await _siemensSession.SendResponseAsync($"502 Command '{command}' not implemented");
          break;
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理FTP命令时出错");
      await _siemensSession.SendResponseAsync("500 Error processing command");
    }
  }

  /// <summary>
  /// 处理USER命令
  /// </summary>
  private async Task HandleUserAsync(string username)
  {
    _siemensSession.SiemensUser.Username = username;
    _siemensSession.SiemensUser.LoginState = 1; // 等待密码

    await _siemensSession.SendResponseAsync($"331 Password required for {username}");
  }

  /// <summary>
  /// 处理PASS命令
  /// </summary>
  private async Task HandlePassAsync(string password)
  {
    if (_siemensSession.SiemensUser.LoginState != 1)
    {
      await _siemensSession.SendResponseAsync("503 Login with USER first");
      return;
    }

    var authenticated = await AuthenticateUserAsync(_siemensSession.SiemensUser.Username, password);
    if (authenticated)
    {
      // 创建用户目录
      CreateUserDirectories(_siemensSession.SiemensUser);

      await _siemensSession.SendResponseAsync("230 Logged on:DNCSERVER");
    }
    else
    {
      await _siemensSession.SendResponseAsync("530 Password incorrect.");
    }
  }

  /// <summary>
  /// 处理FEAT命令
  /// </summary>
  private async Task HandleFeatAsync()
  {
    await _siemensSession.SendResponseAsync("211- Features:");
    await _siemensSession.SendResponseAsync(" UTF8");
    await _siemensSession.SendResponseAsync(" PASV");
    await _siemensSession.SendResponseAsync(" SIZE");
    await _siemensSession.SendResponseAsync(" TYPE A;I");
    await _siemensSession.SendResponseAsync(" MDTM");
    await _siemensSession.SendResponseAsync("211 End");
  }

  /// <summary>
  /// 处理PWD命令
  /// </summary>
  private async Task HandlePwdAsync()
  {
    var relativePath = GetRelativePath(_siemensSession.SiemensUser.CurrentDirectory);
    await _siemensSession.SendResponseAsync($"257 \"{relativePath}\" is current directory");
  }

  /// <summary>
  /// 处理CWD命令
  /// </summary>
  private async Task HandleCwdAsync(string path)
  {
    if (!_siemensSession.SiemensUser.IsAuthenticated)
    {
      await _siemensSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var targetPath = ResolvePath(path);
    _siemensSession.SiemensUser.CurrentDirectory = targetPath;
    await _siemensSession.SendResponseAsync($"250 Directory changed to \"{GetRelativePath(targetPath)}\"");
  }

  /// <summary>
  /// 处理CDUP命令
  /// </summary>
  private async Task HandleCdupAsync()
  {
    if (!_siemensSession.SiemensUser.IsAuthenticated)
    {
      await _siemensSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var parent = Directory.GetParent(_siemensSession.SiemensUser.CurrentDirectory);
    if (parent != null && IsPathAllowed(parent.FullName))
    {
      _siemensSession.SiemensUser.CurrentDirectory = parent.FullName;
      await _siemensSession.SendResponseAsync($"250 Directory changed to \"{GetRelativePath(_siemensSession.SiemensUser.CurrentDirectory)}\"");
    }
    else
    {
      await _siemensSession.SendResponseAsync("550 Cannot go above siemensUser root directory");
    }
  }

  /// <summary>
  /// 处理TYPE命令
  /// </summary>
  private async Task HandleTypeAsync(string type)
  {
    switch (type)
    {
      case "A":
        _siemensSession.SiemensUser.TransferType = "A";
        await _siemensSession.SendResponseAsync("200 Type set to A");
        break;
      case "I":
        _siemensSession.SiemensUser.TransferType = "I";
        await _siemensSession.SendResponseAsync("200 Type set to I");
        break;
      default:
        await _siemensSession.SendResponseAsync($"504 Type {type} not implemented");
        break;
    }
  }

  /// <summary>
  /// 处理PASV命令
  /// </summary>
  private async Task HandlePasvAsync()
  {
    if (!_siemensSession.SiemensUser.IsAuthenticated)
    {
      await _siemensSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var localEndpoint = (IPEndPoint)_siemensSession.Client.Client.LocalEndPoint;

    // 创建被动监听器
    _siemensSession.SiemensUser.DataListener?.Stop();
    _siemensSession.SiemensUser.DataListener = new TcpListener(localEndpoint.Address, 0);
    _siemensSession.SiemensUser.DataListener.Start();

    var pasvEndpoint = (IPEndPoint)_siemensSession.SiemensUser.DataListener.LocalEndpoint;
    byte[] ip = localEndpoint.Address.GetAddressBytes();
    int port = pasvEndpoint.Port;

    byte portHigh = (byte)((port & 65280) >> 8);
    byte portLow = (byte)(port & 255);
    _siemensSession.SiemensUser.IsPassiveMode = true;

    // 添加详细日志
    _logger.LogInformation("TCP 数据连接已打开（被动模式）--{LocalAddress}：{LocalPort}",
        localEndpoint.Address.ToString(), pasvEndpoint.Port);

    await _siemensSession.SendResponseAsync($"227 Entering Passive Mode({ip[0]},{ip[1]},{ip[2]},{ip[3]},{portHigh},{portLow})");
  }

  /// <summary>
  /// 处理PORT命令
  /// </summary>
  private async Task HandlePortAsync(string parameter)
  {
    if (!_siemensSession.SiemensUser.IsAuthenticated)
    {
      await _siemensSession.SendResponseAsync("530 Not logged in");
      return;
    }

    try
    {
      var parts = parameter.Split(',').Select(int.Parse).ToArray();
      if (parts.Length != 6)
      {
        await _siemensSession.SendResponseAsync("501 Invalid PORT command");
        return;
      }

      string ip = $"{parts[0]}.{parts[1]}.{parts[2]}.{parts[3]}";
      int port = (parts[4] * 256) + parts[5];

      _siemensSession.SiemensUser.DataEndPoint = new IPEndPoint(IPAddress.Parse(ip), port);
      _siemensSession.SiemensUser.IsPassiveMode = false;

      await _siemensSession.SendResponseAsync("200 PORT command successful");
    }
    catch
    {
      await _siemensSession.SendResponseAsync("501 Invalid PORT command");
    }
  }

  /// <summary>
  /// 处理LIST和NLST命令
  /// </summary>
  private async Task HandleListAsync(string path, bool isList)
  {
    if (!_siemensSession.SiemensUser.IsAuthenticated)
    {
      await _siemensSession.SendResponseAsync("530 Not logged in");
      return;
    }

    // 检查是否为特殊模式".[^.]*"
    if (path.Contains(".[^.]*"))
    {
      await _siemensSession.SendResponseAsync("150 Connection accepted");
      await _siemensSession.SendResponseAsync("226 Transfer complete");
      return;
    }

    // 处理"-a"参数
    string targetPath;
    if (path == "-a" || string.IsNullOrWhiteSpace(path))
    {
      targetPath = _siemensSession.SiemensUser.CurrentDirectory;
    }
    else
    {
      targetPath = ResolvePath(path);
    }

    if (!Directory.Exists(targetPath) || !IsPathAllowed(targetPath))
    {
      // 检查是否为单个文件的情况
      if (File.Exists(targetPath) && IsPathAllowed(targetPath))
      {
        try
        {
          // 保存当前的二进制传输模式
          var isBinary = _siemensSession.SiemensUser.TransferType == "I";
          _siemensSession.SiemensUser.TransferType = "A"; // 切换到ASCII模式

          await _siemensSession.SendResponseAsync("150 Opening ASCII data connection");

          using var dataClient = _siemensSession.CreateDataConnection();
          if (dataClient == null)
          {
            await _siemensSession.SendResponseAsync("425 Can't open data connection");
            return;
          }

          _logger.LogInformation("采用被动模式返回LIST单个文件信息");

          using var dataStream = dataClient.GetStream();
          using var writer = new StreamWriter(dataStream, Encoding.ASCII);

          // 生成单个文件的列表信息
          var fileInfo = new FileInfo(targetPath);
          var culture = CultureInfo.CreateSpecificCulture("en-GB");
          var listing = string.Format(CultureInfo.InvariantCulture,
              "-rwx------ 1 <USER> <GROUP> {0,12} {1} {2}\r\n",
              fileInfo.Length,
              fileInfo.CreationTime.ToString("MMM dd HH:mm", culture),
              fileInfo.Name);

          _logger.LogInformation("向用户发送(字符串信息)：[{Content}]", listing.TrimEnd());

          await writer.WriteAsync(listing);
          await writer.FlushAsync();

          _logger.LogInformation("发送完毕");

          await _siemensSession.SendResponseAsync("226 Transfer complete");

          // 恢复二进制传输模式
          if (isBinary)
          {
            _siemensSession.SiemensUser.TransferType = "I";
          }

          return;
        }
        catch (Exception ex)
        {
          _logger.LogError(ex, "处理单个文件LIST命令时出错: {Path}", path);
          await _siemensSession.SendResponseAsync("550 Failed to get file listing");
          return;
        }
      }

      await _siemensSession.SendResponseAsync($"550 Directory not found: {path}");
      return;
    }

    try
    {
      // 保存当前的二进制传输模式
      var isBinary = _siemensSession.SiemensUser.TransferType == "I";
      _siemensSession.SiemensUser.TransferType = "A"; // 切换到ASCII模式

      await _siemensSession.SendResponseAsync("150 Opening ASCII data connection");

      using var dataClient = _siemensSession.CreateDataConnection();
      if (dataClient == null)
      {
        await _siemensSession.SendResponseAsync("425 Can't open data connection");
        return;
      }

      // 记录使用的模式
      _logger.LogInformation("采用被动模式返回LIST目录和文件列表");

      using var dataStream = dataClient.GetStream();
      using var writer = new StreamWriter(dataStream, Encoding.ASCII);

      // 获取目录列表
      var listing = GenerateDirectoryListing(targetPath, isList);

      // 记录发送内容
      _logger.LogInformation("向用户发送(字符串信息)：[{Content}]", listing.TrimEnd());

      // 发送目录列表
      await writer.WriteAsync(listing);
      await writer.FlushAsync();

      // 记录发送完毕
      _logger.LogInformation("发送完毕");

      await _siemensSession.SendResponseAsync("226 Transfer complete");

      // 恢复二进制传输模式
      if (isBinary)
      {
        _siemensSession.SiemensUser.TransferType = "I";
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理LIST命令时出错: {Path}", path);
      await _siemensSession.SendResponseAsync("550 Failed to get directory listing");
    }
  }

  /// <summary>
  /// 处理RETR命令
  /// </summary>
  private async Task HandleRetrAsync(string path)
  {
    if (!_siemensSession.SiemensUser.IsAuthenticated)
    {
      await _siemensSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var filePath = ResolvePath(path);
    if (!File.Exists(filePath) || !IsPathAllowed(filePath))
    {
      await _siemensSession.SendResponseAsync($"550 Files not found: {path}");
      return;
    }

    try
    {
      using var dataClient = _siemensSession.CreateDataConnection();
      if (dataClient == null)
      {
        await _siemensSession.SendResponseAsync("425 Can't open data connection");
        return;
      }
      await _siemensSession.SendResponseAsync("150 Opening BINARY mode data connection for download)");

      await using var dataStream = dataClient.GetStream();
      await using var fileStream = File.OpenRead(filePath);

      // 发送文件
      await fileStream.CopyToAsync(dataStream);

      await _siemensSession.SendResponseAsync("226 Transfer complete");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理RETR命令时出错: {Path}", path);
      await _siemensSession.SendResponseAsync("550 Failed to send file");
    }
  }

  /// <summary>
  /// 处理STOR命令
  /// </summary>
  private async Task HandleStorAsync(string path)
  {
    if (!_siemensSession.SiemensUser.IsAuthenticated)
    {
      await _siemensSession.SendResponseAsync("530 Not logged in");
      return;
    }

    // 确定上传的目标文件名
    string filename = Path.GetFileName(path);
    if (string.IsNullOrWhiteSpace(filename))
    {
      await _siemensSession.SendResponseAsync("553 Invalid file name");
      return;
    }

    // 确定目标目录
    string targetDir = _siemensSession.SiemensUser.CurrentDirectory;
    if (!Directory.Exists(targetDir) || !IsPathAllowed(targetDir))
    {
      await _siemensSession.SendResponseAsync("550 Directory not accessible");
      return;
    }

    // 构建完整的文件路径
    string filePath = Path.Combine(targetDir, filename);

    try
    {
      await _siemensSession.SendResponseAsync("150 Opening BINARY mode data connection for upload");

      using var dataClient = _siemensSession.CreateDataConnection();
      if (dataClient == null)
      {
        await _siemensSession.SendResponseAsync("425 Can't open data connection");
        return;
      }

      _logger.LogInformation("采用被动模式返回LIST目录和文件列表");
      _logger.LogInformation("接收用户上传数据（文件流）：[...");

      using var dataStream = dataClient.GetStream();
      using var fileStream = File.Create(filePath);

      // 接收文件
      await dataStream.CopyToAsync(fileStream);

      _logger.LogInformation("...]接收完毕");

      await _siemensSession.SendResponseAsync("226 Transfer complete");

      // 可以添加更多文件上传相关信息
      var deviceId = _siemensSession.SiemensUser.DeviceCode;
      var ipAddress = _siemensSession.RemoteEndPoint.Split(':')[0];

      // 添加可能的文件监控日志
      _logger.LogInformation("监测到文件变化：{{\n  \"FilePath\": \"{FilePath}\",\n  \"DeviceId\": \"{DeviceId}\",\n  \"Ip\": \"{IpAddress}\"\n}}",
          filePath, deviceId, ipAddress);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理STOR命令时出错: {Path}", path);
      await _siemensSession.SendResponseAsync("550 Failed to store file");
    }
  }

  /// <summary>
  /// 处理DELE命令
  /// </summary>
  private async Task HandleDeleAsync(string path)
  {
    if (!_siemensSession.SiemensUser.IsAuthenticated)
    {
      await _siemensSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var filePath = ResolvePath(path);
    if (!File.Exists(filePath) || !IsPathAllowed(filePath))
    {
      await _siemensSession.SendResponseAsync($"550 Files not found: {path}");
      return;
    }

    try
    {
      File.Delete(filePath);
      await _siemensSession.SendResponseAsync("250 Files deleted");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理DELE命令时出错: {Path}", path);
      await _siemensSession.SendResponseAsync("550 Failed to delete file");
    }
  }

  /// <summary>
  /// 处理MKD命令
  /// </summary>
  private async Task HandleMkdAsync(string path)
  {
    if (!_siemensSession.SiemensUser.IsAuthenticated)
    {
      await _siemensSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var dirPath = ResolvePath(path);
    if (!IsPathAllowed(dirPath))
    {
      await _siemensSession.SendResponseAsync("550 Permission denied");
      return;
    }

    try
    {
      Directory.CreateDirectory(dirPath);
      await _siemensSession.SendResponseAsync($"257 \"{path}\" created");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理MKD命令时出错: {Path}", path);
      await _siemensSession.SendResponseAsync("550 Failed to create directory");
    }
  }

  /// <summary>
  /// 处理RMD命令
  /// </summary>
  private async Task HandleRmdAsync(string path)
  {
    if (!_siemensSession.SiemensUser.IsAuthenticated)
    {
      await _siemensSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var dirPath = ResolvePath(path);
    if (!Directory.Exists(dirPath) || !IsPathAllowed(dirPath))
    {
      await _siemensSession.SendResponseAsync($"550 Directory not found: {path}");
      return;
    }

    try
    {
      Directory.Delete(dirPath, false);
      await _siemensSession.SendResponseAsync("250 Directory removed");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理RMD命令时出错: {Path}", path);
      await _siemensSession.SendResponseAsync("550 Failed to remove directory");
    }
  }

  /// <summary>
  /// 验证用户身份
  /// </summary>
  private async Task<bool> AuthenticateUserAsync(string username, string password)
  {
    try
    {
      _logger.LogInformation("开始验证用户 {Username} 的身份", username);

      // 通过DNC配置验证
      var dncConfigs = await _plugin.GetDncConfigsAsync();
      if (dncConfigs == null || !dncConfigs.Any())
      {
        _logger.LogWarning("没有可用的DNC配置");
        return false;
      }

      _logger.LogInformation("获取到 {Count} 个DNC配置", dncConfigs.Count);

      // 记录所有可用设备代码，帮助调试
      if (_logger.IsEnabled(LogLevel.Debug))
      {
        foreach (var config in dncConfigs)
        {
          _logger.LogInformation("DNC配置: 设备={DeviceCode}, 用户={Username}, 启用={Enabled}",
              config.DeviceCode, config.Username, config.Enabled);
        }
      }

      var matchedConfig = dncConfigs.FirstOrDefault(c =>
          c.Enabled &&
          c.Username == username &&
          c.Password == password);

      if (matchedConfig != null)
      {
        _logger.LogInformation("用户 {Username} 验证成功, 设备: {DeviceCode}",
            username, matchedConfig.DeviceCode);

        _siemensSession.SiemensUser.IsAuthenticated = true;
        _siemensSession.SiemensUser.LoginState = 2;
        _siemensSession.SiemensUser.DeviceCode = matchedConfig.DeviceCode;
        _siemensSession.SiemensUser.Role = string.IsNullOrEmpty(matchedConfig.AccessPermission) ?
            "siemensUser" : matchedConfig.AccessPermission;

        return true;
      }
      else
      {
        _logger.LogWarning("用户 {Username} 验证失败，未找到匹配的DNC配置", username);
        return false;
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "验证用户时出错");
      return false;
    }
  }

  /// <summary>
  /// 创建用户目录
  /// </summary>
  private void CreateUserDirectories(FtpSiemensUser siemensUser)
  {
    try
    {
      // 确定用户标识符
      string userIdentifier = GetUserIdentifier(siemensUser);

      // 设置用户工作目录
      siemensUser.WorkDirectory = Path.Combine(AppContext.BaseDirectory, "RootFiles", userIdentifier);
      siemensUser.CurrentDirectory = siemensUser.WorkDirectory;

      // 确保目录存在
      Directory.CreateDirectory(siemensUser.WorkDirectory);

      // 创建SEND和REC子目录
      string sendPath = Path.Combine(siemensUser.WorkDirectory, "SEND");
      string recPath = Path.Combine(siemensUser.WorkDirectory, "REC");

      Directory.CreateDirectory(sendPath);
      Directory.CreateDirectory(recPath);

      _logger.LogInformation("用户 {Username} 的目录已创建: {WorkDir}",
          siemensUser.Username, siemensUser.WorkDirectory);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "创建用户目录时出错");
    }
  }

  /// <summary>
  /// 获取用户标识符
  /// </summary>
  private string GetUserIdentifier(FtpSiemensUser siemensUser)
  {
    if (!string.IsNullOrEmpty(siemensUser.DeviceCode))
      return siemensUser.DeviceCode;

    if (!string.IsNullOrEmpty(siemensUser.Username) && siemensUser.Username != "USER")
      return siemensUser.Username;

    return "USER";
  }

  /// <summary>
  /// 解析路径
  /// </summary>
  private string ResolvePath(string path)
  {
    if (string.IsNullOrWhiteSpace(path))
      return _siemensSession.SiemensUser.CurrentDirectory;

    // 记录原始路径参数
    _logger.LogInformation("ResolvePath: 原始参数 = {OrigPath}", path);
    _logger.LogInformation("ResolvePath: 工作目录 = {WorkDir}", _siemensSession.SiemensUser.WorkDirectory);
    _logger.LogInformation("ResolvePath: 当前目录 = {CurrentDir}", _siemensSession.SiemensUser.CurrentDirectory);

    // 特殊处理 REC 和 SEND 目录的文件
    if (path.StartsWith("REC/", StringComparison.OrdinalIgnoreCase) ||
        path.StartsWith("SEND/", StringComparison.OrdinalIgnoreCase))
    {
      string directPath = Path.Combine(_siemensSession.SiemensUser.WorkDirectory, path);
      _logger.LogInformation("ResolvePath: 特殊处理目录路径 = {DirectPath}", directPath);

      // 检查文件是否存在
      bool fileExists = File.Exists(directPath);
      _logger.LogInformation("ResolvePath: 特殊处理路径文件存在检查 = {Exists}", fileExists);

      if (fileExists)
        return directPath;
    }

    string targetPath;
    // 处理根路径
    if (path.StartsWith("/"))
    {
      targetPath = Path.Combine(_siemensSession.SiemensUser.WorkDirectory, path.TrimStart('/'));
      _logger.LogInformation("ResolvePath: 根路径处理结果 = {TargetPath}", targetPath);
    }
    else
    {
      // 处理相对路径
      targetPath = Path.Combine(_siemensSession.SiemensUser.CurrentDirectory, path);
      _logger.LogInformation("ResolvePath: 相对路径处理结果 = {TargetPath}", targetPath);

      // 如果使用相对路径找不到文件，尝试从工作目录开始
      if (!File.Exists(targetPath) && !Directory.Exists(targetPath))
      {
        string altPath = Path.Combine(_siemensSession.SiemensUser.WorkDirectory, path);
        _logger.LogInformation("ResolvePath: 相对路径未找到, 尝试替代路径 = {AltPath}", altPath);

        // 如果替代路径存在，使用它
        if (File.Exists(altPath) || Directory.Exists(altPath))
        {
          _logger.LogInformation("ResolvePath: 使用替代路径");
          targetPath = altPath;
        }
      }
    }

    // 规范化路径
    targetPath = Path.GetFullPath(targetPath);
    _logger.LogInformation("ResolvePath: 最终规范化路径 = {FinalPath}", targetPath);

    return targetPath;
  }

  /// <summary>
  /// 获取相对路径
  /// </summary>
  private string GetRelativePath(string path)
  {
    // 规范化路径
    path = Path.GetFullPath(path);
    var workDir = Path.GetFullPath(_siemensSession.SiemensUser.WorkDirectory);

    if (path.Equals(workDir, StringComparison.OrdinalIgnoreCase))
      return "/";

    if (path.StartsWith(workDir, StringComparison.OrdinalIgnoreCase))
    {
      var relativePath = path.Substring(workDir.Length)
          .Replace('\\', '/');

      if (!relativePath.StartsWith("/"))
        relativePath = "/" + relativePath;

      return relativePath;
    }

    return path;
  }

  /// <summary>
  /// 检查路径是否允许访问
  /// </summary>
  private bool IsPathAllowed(string path)
  {
    var normalized = Path.GetFullPath(path);
    var workDir = Path.GetFullPath(_siemensSession.SiemensUser.WorkDirectory);

    return normalized.StartsWith(workDir, StringComparison.OrdinalIgnoreCase);
  }

  /// <summary>
  /// 生成目录列表字符串
  /// </summary>
  private string GenerateDirectoryListing(string directoryPath, bool isList)
  {
    if (isList)
    {
      var listing = new StringBuilder();
      var culture = CultureInfo.CreateSpecificCulture("en-GB");

      // 目录项
      foreach (var dir in Directory.GetDirectories(directoryPath))
      {
        var info = new DirectoryInfo(dir);
        listing.AppendFormat(CultureInfo.InvariantCulture,
            "drwx------ 1 <USER> <GROUP> {0,12} {1} {2}\r\n",
            "0", // 使用0而非<DIR>以匹配CommandLIST
            info.CreationTime.ToString("MMM dd HH:mm", culture), // 使用CreationTime而非LastWriteTime
            info.Name);
      }

      // 文件项
      foreach (var file in Directory.GetFiles(directoryPath))
      {
        var info = new FileInfo(file);
        listing.AppendFormat(CultureInfo.InvariantCulture,
            "-rwx------ 1 <USER> <GROUP> {0,12} {1} {2}\r\n",
            info.Length,
            info.CreationTime.ToString("MMM dd HH:mm", culture), // 使用CreationTime而非LastWriteTime
            info.Name);
      }
      return listing.ToString();
    }
    else
    {
      var listing = new StringBuilder();
      var entries = Directory.GetFileSystemEntries(directoryPath)
          .Select(Path.GetFileName)
          .OrderBy(name => name);

      foreach (var name in entries)
      {
        listing.Append(name + "\r\n");
      }

      return listing.ToString();
    }
  }

  /// <summary>
  /// 处理MDTM命令 - 获取文件或目录的最后修改时间
  /// </summary>
  /// <param name="parameter">文件或目录路径</param>
  private async Task HandleMdtmAsync(string parameter)
  {
    if (!_siemensSession.SiemensUser.IsAuthenticated)
    {
      await _siemensSession.SendResponseAsync("530 Not logged in");
      return;
    }

    if (string.IsNullOrEmpty(parameter))
    {
      await _siemensSession.SendResponseAsync("501 Path parameter required");
      return;
    }

    // 记录原始参数
    _logger.LogInformation("MDTM命令处理: 原始参数 = {OriginalParameter}", parameter);
    _logger.LogInformation("MDTM命令处理: 当前工作目录 = {WorkDir}", _siemensSession.SiemensUser.WorkDirectory);
    _logger.LogInformation("MDTM命令处理: 当前目录 = {CurrentDir}", _siemensSession.SiemensUser.CurrentDirectory);

    // 处理以"/"开头的路径
    if (parameter[0] == '/')
    {
      _logger.LogInformation("MDTM命令处理: 绝对路径处理前 = {Parameter}", parameter);
      parameter = parameter.TrimStart('/');
      _logger.LogInformation("MDTM命令处理: 绝对路径处理后 = {Parameter}", parameter);
    }

    // 解析路径
    string path = ResolvePath(parameter);
    _logger.LogInformation("MDTM命令处理: ResolvePath解析结果 = {Path}", path);
    path = Path.GetFullPath(path);
    _logger.LogInformation("MDTM命令处理: GetFullPath规范化后 = {Path}", path);

    if (!IsPathAllowed(path))
    {
      _logger.LogWarning("MDTM命令处理: 路径权限检查失败 = {Path}", path);
      await _siemensSession.SendResponseAsync("550 Access denied");
      return;
    }

    try
    {
      // 检查父目录是否存在
      string? parentDir = Path.GetDirectoryName(path);
      if (!string.IsNullOrEmpty(parentDir))
      {
        bool parentExists = Directory.Exists(parentDir);
        _logger.LogInformation("MDTM命令处理: 父目录 {ParentDir} {Exists}",
            parentDir, parentExists ? "存在" : "不存在");

        if (parentExists)
        {
          // 列出父目录中的文件
          _logger.LogInformation("MDTM命令处理: 父目录中的文件:");
          foreach (var file in Directory.GetFiles(parentDir))
          {
            _logger.LogInformation("  - {FileName} ({Size} bytes)",
                Path.GetFileName(file), new FileInfo(file).Length);
          }
        }
      }

      // 先检查文件是否物理存在
      bool fileExists = File.Exists(path);
      _logger.LogInformation("MDTM命令处理: Files.Exists({Path}) = {Result}", path, fileExists);

      // 尝试使用其他方式检查
      bool dirExists = Directory.Exists(path);
      _logger.LogInformation("MDTM命令处理: Directory.Exists({Path}) = {Result}", path, dirExists);

      if (fileExists)
      {
        // 处理文件
        var fileInfo = new FileInfo(path);
        // 检查文件是否可读
        bool canRead = false;
        try
        {
          using (var fs = fileInfo.OpenRead())
          {
            canRead = true;
          }
        }
        catch (Exception ex)
        {
          _logger.LogWarning("MDTM命令处理: 文件无法读取 {Path}: {Error}", path, ex.Message);
        }

        _logger.LogInformation("MDTM命令处理: 文件 {Path} 大小 = {Size} 字节, 可读 = {CanRead}",
            path, fileInfo.Length, canRead);

        string modificationTime = fileInfo.LastWriteTimeUtc.ToString("yyyyMMddHHmmss");
        await _siemensSession.SendResponseAsync($"213 {modificationTime}");

        _logger.LogInformation("MDTM命令: 返回文件 {Path} 的修改时间 {Time}", parameter, modificationTime);
      }
      else if (dirExists)
      {
        // 处理目录
        var dirInfo = new DirectoryInfo(path);
        string modificationTime = dirInfo.LastWriteTimeUtc.ToString("yyyyMMddHHmmss");
        await _siemensSession.SendResponseAsync($"213 {modificationTime}");

        _logger.LogInformation("MDTM命令: 返回目录 {Path} 的修改时间 {Time}", parameter, modificationTime);
      }
      else
      {
        // 尝试另一种解析方式
        string altPath = Path.Combine(_siemensSession.SiemensUser.WorkDirectory, parameter);
        _logger.LogInformation("MDTM命令处理: 尝试替代路径 = {AltPath}", altPath);
        bool altFileExists = File.Exists(altPath);
        _logger.LogInformation("MDTM命令处理: Files.Exists({AltPath}) = {Result}", altPath, altFileExists);

        if (altFileExists)
        {
          var fileInfo = new FileInfo(altPath);
          string modificationTime = fileInfo.LastWriteTimeUtc.ToString("yyyyMMddHHmmss");
          await _siemensSession.SendResponseAsync($"213 {modificationTime}");
          _logger.LogInformation("MDTM命令: 使用替代路径返回文件 {Path} 的修改时间 {Time}", altPath, modificationTime);
        }
        else
        {
          // 文件或目录不存在
          _logger.LogWarning("MDTM命令: 未找到文件或目录 {Path}", parameter);
          await _siemensSession.SendResponseAsync($"550 \"{parameter}\" Not Found");
        }
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理MDTM命令时出错: {Path}", parameter);
      await _siemensSession.SendResponseAsync("550 Error getting modification time");
    }
  }

  /// <summary>
  /// 处理SIZE命令 - 获取文件大小
  /// </summary>
  /// <param name="parameter">文件路径</param>
  private async Task HandleSizeAsync(string parameter)
  {
    // 验证用户登录状态
    if (!_siemensSession.SiemensUser.IsAuthenticated)
    {
      await _siemensSession.SendResponseAsync("530 Not logged in");
      return;
    }

    // 验证参数
    if (string.IsNullOrEmpty(parameter))
    {
      await _siemensSession.SendResponseAsync("501 Path parameter required");
      return;
    }

    // 记录原始参数
    _logger.LogInformation("SIZE命令处理: 原始参数 = {OriginalParameter}", parameter);
    _logger.LogInformation("SIZE命令处理: 当前工作目录 = {WorkDir}", _siemensSession.SiemensUser.WorkDirectory);
    _logger.LogInformation("SIZE命令处理: 当前目录 = {CurrentDir}", _siemensSession.SiemensUser.CurrentDirectory);

    // 处理以'/'开头的路径
    if (parameter[0] == '/')
    {
      _logger.LogInformation("SIZE命令处理: 绝对路径处理前 = {Parameter}", parameter);
      parameter = parameter.TrimStart('/');
      _logger.LogInformation("SIZE命令处理: 绝对路径处理后 = {Parameter}", parameter);
    }

    // 解析路径并确保安全
    string path = ResolvePath(parameter);
    _logger.LogInformation("SIZE命令处理: ResolvePath解析结果 = {Path}", path);
    path = Path.GetFullPath(path);
    _logger.LogInformation("SIZE命令处理: GetFullPath规范化后 = {Path}", path);

    // 验证路径访问权限
    if (!IsPathAllowed(path))
    {
      _logger.LogWarning("SIZE命令处理: 路径权限检查失败 = {Path}", path);
      await _siemensSession.SendResponseAsync("550 Access denied");
      return;
    }

    try
    {
      // 检查父目录是否存在
      string? parentDir = Path.GetDirectoryName(path);
      if (!string.IsNullOrEmpty(parentDir))
      {
        bool parentExists = Directory.Exists(parentDir);
        _logger.LogInformation("SIZE命令处理: 父目录 {ParentDir} {Exists}",
            parentDir, parentExists ? "存在" : "不存在");

        if (parentExists)
        {
          // 列出父目录中的文件
          _logger.LogInformation("SIZE命令处理: 父目录中的文件:");
          foreach (var file in Directory.GetFiles(parentDir))
          {
            _logger.LogInformation("  - {FileName} ({Size} bytes)",
                Path.GetFileName(file), new FileInfo(file).Length);
          }
        }
      }

      // 检查文件是否存在
      bool fileExists = File.Exists(path);
      _logger.LogInformation("SIZE命令处理: Files.Exists({Path}) = {Result}", path, fileExists);

      if (fileExists)
      {
        // 获取文件信息并返回大小
        var fileInfo = new FileInfo(path);
        // 检查文件是否可读
        bool canRead = false;
        try
        {
          using (var fs = fileInfo.OpenRead())
          {
            canRead = true;
          }
        }
        catch (Exception ex)
        {
          _logger.LogWarning("SIZE命令处理: 文件无法读取 {Path}: {Error}", path, ex.Message);
        }

        _logger.LogInformation("SIZE命令处理: 文件 {Path} 大小 = {Size} 字节, 可读 = {CanRead}",
            path, fileInfo.Length, canRead);

        await _siemensSession.SendResponseAsync($"213 {fileInfo.Length}");

        // 记录日志
        _logger.LogInformation("SIZE命令: 返回文件 {Path} 的大小 {Size} 字节", parameter, fileInfo.Length);
      }
      else
      {
        // 尝试另一种解析方式
        string altPath = Path.Combine(_siemensSession.SiemensUser.WorkDirectory, parameter);
        _logger.LogInformation("SIZE命令处理: 尝试替代路径 = {AltPath}", altPath);
        bool altFileExists = File.Exists(altPath);
        _logger.LogInformation("SIZE命令处理: Files.Exists({AltPath}) = {Result}", altPath, altFileExists);

        if (altFileExists)
        {
          var fileInfo = new FileInfo(altPath);
          await _siemensSession.SendResponseAsync($"213 {fileInfo.Length}");
          _logger.LogInformation("SIZE命令: 使用替代路径返回文件 {Path} 的大小 {Size} 字节",
              altPath, fileInfo.Length);
        }
        else
        {
          // 文件不存在
          _logger.LogWarning("SIZE命令: 未找到文件 {Path}", parameter);
          await _siemensSession.SendResponseAsync("550 Not Found");
        }
      }
    }
    catch (Exception ex)
    {
      // 异常处理
      _logger.LogError(ex, "处理SIZE命令时出错: {Path}", parameter);
      await _siemensSession.SendResponseAsync("550 Error getting file size");
    }
  }
}

