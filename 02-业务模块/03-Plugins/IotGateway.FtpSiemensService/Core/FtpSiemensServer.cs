using System;
using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using IotGateway.FtpSiemensService.Models;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Linq;
using System.IO;

namespace IotGateway.FtpSiemensService.Core;

/// <summary>
/// FTP服务器实现
/// </summary>
public class FtpSiemensServer : IDisposable
{
  private readonly FtpSiemensServerPlugin _plugin;
  private readonly FtpSiemensConfig _siemensConfig;
  private readonly ILogger _logger;
  private TcpListener? _listener;
  private CancellationTokenSource? _cancellationTokenSource;
  private readonly ConcurrentDictionary<string, FtpSiemensSession> _sessions = new();

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="plugin">FTP服务器插件</param>
  /// <param name="siemensConfig">FTP配置</param>
  /// <param name="logger">日志记录器</param>
  public FtpSiemensServer(FtpSiemensServerPlugin plugin, FtpSiemensConfig siemensConfig, ILogger logger)
  {
    _plugin = plugin;
    _siemensConfig = siemensConfig;
    _logger = logger;
  }

  /// <summary>
  /// 启动FTP服务器
  /// </summary>
  public async Task StartAsync()
  {
    try
    {
      _cancellationTokenSource = new CancellationTokenSource();
      _listener = new TcpListener(IPAddress.Any, _siemensConfig.Port);
      _listener.Start();

      _logger.LogInformation("FTP服务器启动在端口 {Port}", _siemensConfig.Port);

      // 开始接受客户端连接
      _ = AcceptClientsAsync(_cancellationTokenSource.Token);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "启动FTP服务器时出错");
      throw;
    }
  }

  /// <summary>
  /// 停止FTP服务器
  /// </summary>
  public async Task StopAsync()
  {
    try
    {
      // 取消所有操作
      _cancellationTokenSource?.Cancel();

      // 关闭监听器
      _listener?.Stop();

      // 关闭所有会话
      foreach (var session in _sessions.Values)
      {
        try
        {
          session.Dispose();
        }
        catch (Exception ex)
        {
          _logger.LogWarning(ex, "关闭会话时出错");
        }
      }

      _sessions.Clear();

      _logger.LogInformation("FTP服务器已停止");

      await Task.CompletedTask;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "停止FTP服务器时出错");
    }
  }

  /// <summary>
  /// 接受客户端连接
  /// </summary>
  private async Task AcceptClientsAsync(CancellationToken cancellationToken)
  {
    try
    {
      while (!cancellationToken.IsCancellationRequested)
      {
        try
        {
          // 接受客户端连接
          var client = await _listener.AcceptTcpClientAsync();
          var clientEndPoint = client.Client.RemoteEndPoint?.ToString() ?? "Unknown";
          var localEndPoint = client.Client.LocalEndPoint?.ToString() ?? "Unknown";

          _logger.LogInformation("客户端（{ClientEndPoint}）与本机（{LocalEndPoint}）建立Ftp连接",
              clientEndPoint, localEndPoint);

          // 创建会话
          var session = new FtpSiemensSession(client, _logger);
          _sessions.TryAdd(clientEndPoint, session);

          // 处理客户端
          _ = HandleClientAsync(session, clientEndPoint, cancellationToken);
        }
        catch (Exception ex) when (cancellationToken.IsCancellationRequested)
        {
          // 正常取消
          break;
        }
        catch (Exception ex)
        {
          _logger.LogError(ex, "接受客户端连接时出错");
        }
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "接受客户端连接循环出错");
    }
  }

  /// <summary>
  /// 处理客户端连接
  /// </summary>
  private async Task HandleClientAsync(FtpSiemensSession siemensSession, string clientEndPoint, CancellationToken cancellationToken)
  {
    try
    {
      // 发送欢迎消息
      await siemensSession.SendResponseAsync("220 FTP Server v1.0");

      // 创建命令处理器
      var commandHandler = new FtpSiemensCommandHandler(siemensSession, _plugin, _logger);

      // 命令处理循环
      string? line;
      while (!cancellationToken.IsCancellationRequested)
      {
        try
        {
          // 读取一行，安全处理可能的null值
          string? rawLine = await siemensSession.Reader.ReadLineAsync();

          // 如果读取到null，表示连接已关闭
          if (rawLine == null)
          {
            _logger.LogInformation("客户端({ClientEndPoint})连接已关闭", clientEndPoint);
            break;
          }

          // 安全地清理控制字符
          line = rawLine.Trim('\0');

          // 记录原始命令
          _logger.LogInformation("收到客户端({ClientEndPoint})消息: [{Command}]", clientEndPoint, line);

          // 处理空命令
          if (string.IsNullOrEmpty(line))
          {
            await siemensSession.SendResponseAsync("500 Empty command");
            continue;
          }

          // 处理命令
          await commandHandler.HandleCommandAsync(line);

          // 检查是否是QUIT命令（不区分大小写）
          if (line.StartsWith("QUIT", StringComparison.OrdinalIgnoreCase))
            break;
        }
        catch (IOException ioEx)
        {
          _logger.LogError(ioEx, "读取客户端({ClientEndPoint})命令时发生IO错误", clientEndPoint);
          break; // IO错误通常意味着连接已断开
        }
        catch (ObjectDisposedException dispEx)
        {
          _logger.LogError(dispEx, "读取客户端({ClientEndPoint})命令时流已关闭", clientEndPoint);
          break; // 流已关闭，退出循环
        }
        catch (Exception cmdEx)
        {
          _logger.LogError(cmdEx, "处理客户端({ClientEndPoint})命令时出错", clientEndPoint);
          try
          {
            // 尝试发送错误响应但不退出循环
            await siemensSession.SendResponseAsync("500 Command processing error");
          }
          catch
          {
            // 如果发送错误响应也失败，则退出循环
            break;
          }
        }
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理客户端 {ClientEndPoint} 时出错", clientEndPoint);
    }
    finally
    {
      try
      {
        siemensSession.Dispose();
      }
      catch (Exception ex)
      {
        _logger.LogWarning(ex, "关闭会话时出错");
      }

      _sessions.TryRemove(clientEndPoint, out _);
      _logger.LogInformation("客户端 {ClientEndPoint} 已断开连接", clientEndPoint);
    }
  }

  /// <summary>
  /// 释放资源
  /// </summary>
  public void Dispose()
  {
    _listener?.Stop();
    _cancellationTokenSource?.Cancel();
    _cancellationTokenSource?.Dispose();

    foreach (var session in _sessions.Values)
    {
      try
      {
        session.Dispose();
      }
      catch
      {
        // 忽略错误
      }
    }

    _sessions.Clear();

    GC.SuppressFinalize(this);
  }
}