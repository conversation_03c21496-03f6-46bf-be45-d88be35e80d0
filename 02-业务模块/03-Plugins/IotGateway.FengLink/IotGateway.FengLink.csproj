<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
        <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\04-架构核心\IotGateway.Core\IotGateway.Core.csproj" />
      <ProjectReference Include="..\..\01-Business\IotGateway.Application.Entity\IotGateway.Application.Entity.csproj" />
      <ProjectReference Include="..\..\01-Business\IotGatewayTerminal.Application\IotGatewayTerminal.Application.csproj" />
      <ProjectReference Include="..\IotGateway.Plugin.Core\IotGateway.Plugin.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="SharpCompress" Version="0.37.2" />
    </ItemGroup>

</Project>
