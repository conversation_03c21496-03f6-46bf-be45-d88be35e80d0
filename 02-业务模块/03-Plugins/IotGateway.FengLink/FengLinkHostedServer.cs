using System.Net.NetworkInformation;
using System.Text;
using Common.Models;
using Feng.Common.Util;
using Feng.IotGateway.Application.Drivers.Dtos;
using Feng.IotGateway.Core.Const;
using Feng.IotGateway.Core.Entity;
using Feng.IotGateway.Core.Extension;
using Feng.IotGateway.Core.Models.Networks;
using Feng.IotGateway.Core.Service.Config;
using Feng.IotGateway.Core.Util;
using Feng.IotGateway.WebSocket.Const;
using Furion;
using Furion.ClayObject;
using Furion.DataEncryption;
using Furion.EventBus;
using Furion.FriendlyException;
using Furion.Logging;
using Furion.RemoteRequest.Extensions;
using Furion.TaskQueue;
using Furion.UnifyResult;
using IotGateway.Application.Entity;
using IotGateway.EdgeDevice;
using IotGateway.FengLink.Models;
using Microsoft.Extensions.Hosting;
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Packets;
using MQTTnet.Protocol;
using Newtonsoft.Json;
using SharpCompress.Common;
using SharpCompress.Readers;
using SqlSugar;
using TDengIne;

namespace IotGateway.FengLink;

/// <summary>
///     连接FengLink
/// </summary>
public class FengLinkHostedServer : IHostedService
{
    /// <summary>
    /// 
    /// </summary>
    private IMqttClient Client;

    /// <summary>
    /// 
    /// </summary>
    private MqttClientOptions clientOptions;

    /// <summary>
    /// </summary>
    private readonly ISqlSugarClient _db;

    /// <summary>
    /// 
    /// </summary>
    private SetAuthorizeOutput _authorize;

    /// <summary>
    /// 
    /// </summary>
    private readonly IEventPublisher _eventPublisher;

    /// <summary>
    /// 
    /// </summary>
    private readonly ExecuteService _executeService;

    /// <summary>
    ///     采集协议底层单例服务
    /// </summary>
    private readonly DriverHostedService _driverService;

#if !DEBUG
    private readonly string httpUrl = "http://127.0.0.1:8093";
#elif DEBUG
    private readonly string httpUrl = "http://127.0.0.1:5004";
#endif

    /// <summary>
    ///     
    /// </summary>
    /// <param name="driverService"></param>
    /// <param name="db"></param>
    /// <param name="eventPublisher"></param>
    /// <param name="executeService"></param>
    public FengLinkHostedServer(DriverHostedService driverService, ISqlSugarClient db, IEventPublisher eventPublisher, ExecuteService executeService)
    {
        _driverService = driverService;
        _db = db;
        _eventPublisher = eventPublisher;
        _executeService = executeService;
    }

    /// <summary>
    /// 启动服务
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        await Task.Run(async () =>
        {
            Console.WriteLine("┌──────────────────────────────────────────────────┐");
            Console.WriteLine("│           FengLink服务启动中...                   │");
            Console.WriteLine("└──────────────────────────────────────────────────┘");

            // 延迟启动
            await Task.Delay(5000, cancellationToken);
            Console.WriteLine("[FLink] 开始初始化服务...");

            // 增加个简单3次循环取值
            for (int i = 0; i < 3; i++)
            {
                // 获取授权信息
                _authorize = MachineUtil.Authorize;
                // 打印日志
                Console.WriteLine($"[FLink] 授权信息检查 [{i + 1}/3]: {_authorize?.Sn ?? "未获取到"}");
                // 如果授权信息不为空，则跳出循环
                if (_authorize != null)
                    break;
                // 如果授权信息为空，则等待3秒
                Console.WriteLine("[FLink] 等待3秒后重试获取授权信息...");
                await Task.Delay(3000, cancellationToken);
            }

            // 是否开启网关管理
            var isFlinkOpen = await App.GetService<SysConfigService>().GetConfigValue<bool>(ConfigConst.SysFLinkOpen);
            Console.WriteLine($"[FLink] 网关管理状态: {(isFlinkOpen ? "已开启" : "已关闭")}");

            if (!isFlinkOpen)
            {
                Console.WriteLine("[FLink] 网关管理未开启，服务不会启动");
                return;
            }

            // 订阅消息
            Console.WriteLine("[FLink] 正在订阅消息...");
            await Subscribe(cancellationToken);
            Console.WriteLine("[FLink] 消息订阅完成");

            // 连接mqtt 
            var ip = await App.GetService<SysConfigService>().GetConfigValue<string>(ConfigConst.SysFLinkIp);
            var port = await App.GetService<SysConfigService>().GetConfigValue<int>(ConfigConst.SysFLinkPort);
            Console.WriteLine($"[FLink] MQTT服务器信息: {ip}:{port}");

            // 创建mqtt客户端
            Console.WriteLine("[FLink] 正在创建MQTT客户端...");
            Client = new MqttFactory().CreateMqttClient();

            // 创建mqtt客户端选项
            var clientId = _authorize.Sn ?? Guid.NewGuid().ToString("N");
            Console.WriteLine($"[FLink] 客户端ID: {clientId}");

            clientOptions = new MqttClientOptionsBuilder()
                .WithClientId(clientId)
                .WithTcpServer(ip, port)
                .WithCredentials("fengedge", "123456")
                .WithTimeout(TimeSpan.FromSeconds(5))
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(60))
                .WithCleanSession()
                .Build();

            // 消息回调
            Console.WriteLine("[FLink] 正在注册事件处理程序...");
            Client.ApplicationMessageReceivedAsync += Client_ApplicationMessageReceived;
            // 连接成功
            Client.ConnectedAsync += OnConnected;
            // 连接断开
            Client.DisconnectedAsync += OnDisconnectedAsync;

            try
            {
                // 连接mqtt
                Console.WriteLine("[FLink] 正在连接到MQTT服务器...");
                await Client.ConnectAsync(clientOptions, cancellationToken);
                Console.WriteLine("[FLink] MQTT连接请求已发送");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[FLink] MQTT连接错误: {ex.Message}");
                Log.Error($"【FLink】 连接错误:【{ex.Message}】");
                Console.WriteLine($"[FLink] 错误详情: {ex.StackTrace}");
            }
        }, cancellationToken);
    }

    /// <summary>
    ///     实时数据订阅
    /// </summary>
    private async Task Subscribe(CancellationToken cancellationToken)
    {
        // 订阅实时数据
        await MessageCenter.Subscribe(EventConst.FLinkSendDeviceData, async context =>
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    return;
                // 发送消息
                var data = context.GetPayload<PayLoad>();
                // 检查mqtt连接
                MqttConnect();
                // 发送消息
                await Client.PublishAsync(new MqttApplicationMessage
                {
                    Topic = $"/{_authorize?.Sn}/up/dev/upload/online",
                    QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce,
                    PayloadSegment = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(data))
                }, cancellationToken);
            }
            catch (Exception)
            {
                // ignored
            }

            await Task.CompletedTask;
        }, cancellationToken: cancellationToken);
    }

    /// <summary>
    ///     MQTT连接成功事件
    /// </summary>
    [SuppressMonitor]
    private async Task OnConnected(MqttClientConnectedEventArgs args)
    {
        Console.WriteLine("┌──────────────────────────────────────────────────┐");
        Console.WriteLine("│           MQTT连接已建立成功                      │");
        Console.WriteLine("└──────────────────────────────────────────────────┘");
        Console.WriteLine($"[FLink] 连接状态: {args.ConnectResult.ResultCode}");

        Console.WriteLine("[FLink] 正在订阅主题...");
        // 记录要订阅的主题列表
        var topicsToSubscribe = new List<string>
        {
            "getConfig/+",
            "setConfig/+",
            "reboot/+",
            "ping/+",
            "ota/+",
            $"online/{_authorize?.Sn}"
        };

        foreach (var topic in topicsToSubscribe)
        {
            Console.WriteLine($"[FLink] 准备订阅: {topic}");
        }

        await Client.SubscribeAsync(new MqttClientSubscribeOptions
        {
            TopicFilters = new List<MqttTopicFilter>
            {
                // 要求配置上报
                new() {Topic = "getConfig/+", QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce},
                // 配置主动下发
                new() {Topic = "setConfig/+", QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce},
                // 指令下发
                new() {Topic = "reboot/+", QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce},
                // ping
                new() {Topic = "ping/+", QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce},
                // ota升级
                new() {Topic = "ota/+", QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce},
                // online升级
                new() {Topic = $"online/{_authorize?.Sn}", QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce},
            }
        });
        Console.WriteLine("[FLink] 主题订阅完成");
        Console.WriteLine($"[FLink] MQTT客户端ID: {Client.Options.ClientId}");
        Console.WriteLine($"[FLink] MQTT服务器: {Client.Options.ChannelOptions}");
        Console.WriteLine("[FLink] MQTT连接成功！系统已准备就绪，等待消息...");
    }

    /// <summary>
    ///     MQTT断开连接事件
    /// </summary>
    [SuppressMonitor]
    private async Task OnDisconnectedAsync(MqttClientDisconnectedEventArgs args)
    {
        Console.WriteLine("┌──────────────────────────────────────────────────┐");
        Console.WriteLine("│           MQTT连接已断开                          │");
        Console.WriteLine("└──────────────────────────────────────────────────┘");

        if (args.Exception != null)
        {
            Console.WriteLine($"[FLink] 断开原因: {args.Exception.Message}");
        }

        if (args.ClientWasConnected)
        {
            Console.WriteLine("[FLink] 客户端之前是连接状态，正在尝试重新连接...");
        }
        else
        {
            Console.WriteLine("[FLink] 客户端未能成功建立连接");
        }

        Console.WriteLine($"[FLink] 断开类型: {args.Reason}");

        try
        {
            Console.WriteLine("[FLink] 尝试重新连接MQTT服务器...");
            await Client.ConnectAsync(clientOptions);
            Console.WriteLine("[FLink] 重连请求已发送");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[FLink] 重连失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查mqtt是否正常连接
    /// </summary>
    protected virtual void MqttConnect()
    {
        // 检查mqtt客户端是否为空
        if (Client == null)
        {
            Console.WriteLine("[FLink] MQTT客户端为空，无法连接");
            return;
        }

        // 检查mqtt是否已连接
        if (Client.IsConnected)
        {
            Console.WriteLine("[FLink] MQTT已连接，无需重连");
            return;
        }

        // 如果未连接，则尝试连接
        Console.WriteLine("[FLink] MQTT未连接，开始尝试重连...");
        if (!Client.IsConnected)
        {
            // 加锁
            var lockTaken = false;
            try
            {
                // 尝试加锁
                Console.WriteLine("[FLink] 正在获取连接锁...");
                lockTaken = Monitor.TryEnter(this);
                if (lockTaken)
                {
                    Console.WriteLine("[FLink] 已获取连接锁");
                    // 检查mqtt是否已连接
                    if (!Client.IsConnected)
                    {
                        Console.WriteLine("[FLink] 开始执行MQTT重连...");
                        // 连接mqtt
                        var result = Client.ConnectAsync(clientOptions).Result;
                        Console.WriteLine($"[FLink] MQTT重连结果: {result.ResultCode}");
                    }
                    else
                    {
                        Console.WriteLine("[FLink] 锁获取后发现MQTT已连接，跳过重连");
                    }
                }
                else
                {
                    Console.WriteLine("[FLink] 未能获取连接锁，另一线程可能正在进行连接");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[FLink] MQTT重连异常: {ex.Message}");
                // // 连接失败就强制释放断开连接
                // ReConnect();
            }
            finally
            {
                if (lockTaken)
                {
                    Console.WriteLine("[FLink] 释放连接锁");
                    Monitor.Exit(this);
                }
            }
        }
    }

    /// <summary>
    ///     消息回调
    /// </summary>
    /// <param name="e"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private async Task Client_ApplicationMessageReceived(MqttApplicationMessageReceivedEventArgs e)
    {
        // 获取主题
        var topic = e.ApplicationMessage.Topic;
        // 获取消息体
        var payLoad = e.ApplicationMessage.ConvertPayloadToString();
        // 记录日志
        Console.WriteLine("┌──────────────────────────────────────────────────┐");
        Console.WriteLine($"│           收到MQTT消息: {topic}                   │");
        Console.WriteLine("└──────────────────────────────────────────────────┘");
        Log.Information($"[FLink] 收到消息: Topic={topic}");

        try
        {
            Console.WriteLine($"[FLink] 消息QoS: {e.ApplicationMessage.QualityOfServiceLevel}");
            Console.WriteLine($"[FLink] 保留消息: {e.ApplicationMessage.Retain}");

            var payloadSummary = payLoad.Length > 200
                ? payLoad.Substring(0, 200) + "..."
                : payLoad;
            Console.WriteLine($"[FLink] 消息内容: {payloadSummary}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[FLink] 解析消息详情失败: {ex.Message}");
        }

        // 检查是否为ping指令
        if (topic.Contains("ping") && topic.Contains(_authorize?.Sn))
        {
            // 记录日志
            Console.WriteLine("[FLink] 收到ping指令，准备执行ping操作");
            Log.Information("[FLink] 收到ping指令，准备执行ping操作");
            // 执行ping操作
            await Ping(payLoad, topic);
            // 记录日志
            Console.WriteLine("[FLink] ping操作执行完成");
            Log.Information("[FLink] ping操作执行完成");
        }
        // 实时上报一次数据
        else if (topic.Contains("online") && topic.Contains(_authorize?.Sn))
        {
            // 记录日志
            Console.WriteLine("[FLink] 收到实时数据上报请求，准备发布刷新事件");
            Log.Information("[FLink] 收到实时数据上报请求，准备发布刷新事件");
            // 发布刷新事件
            await MessageCenter.PublishAsync(string.Format(ConstMethod.FLinkRequestRefreshRealTimeDeviceData, payLoad), "");
            Console.WriteLine("[FLink] 实时数据刷新事件已发布");
            Log.Information("[FLink] 实时数据刷新事件已发布");
        }
        // 上报配置 
        else if (topic.Contains("getConfig") && topic.Contains(_authorize?.Sn))
        {
            // 记录日志
            Console.WriteLine("[FLink] 收到上报配置指令，准备收集配置信息");
            Log.Information("[FLink] 收到上报配置指令，准备收集配置信息");
            // 收集配置信息
            var status = await UploadConfig();
            // 记录日志
            Console.WriteLine($"[FLink] 配置信息收集完成，上报状态:{status}");
            Log.Information($"[FLink] 配置信息收集完成，上报状态:{status}");
            // 发送响应
            Console.WriteLine($"[FLink] 正在发送配置上报响应到主题: {topic}/response");
            await Client.PublishAsync(new MqttApplicationMessage
            {
                Topic = $"{topic}/response",
                QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce,
                PayloadSegment = Encoding.UTF8.GetBytes(status.ToString())
            });
            Console.WriteLine("[FLink] 配置上报响应已发送");
            Log.Information("[FLink] 配置上报响应已发送");
        }
        // 下发配置
        else if (topic.Contains("setConfig") && topic.Contains(_authorize?.Sn))
        {
            Console.WriteLine("[FLink] 收到下发配置指令，准备保存配置内容");
            Log.Information("[FLink] 收到下发配置指令，准备保存配置内容");

            // 先保存配置内容
            var configContent = payLoad;
            Console.WriteLine($"[FLink] 配置内容长度: {configContent.Length} 字节");
            Log.Information("[FLink] 配置内容已保存，准备发送确认响应");

            // 立即回复确认
            Console.WriteLine($"[FLink] 正在发送配置确认响应到主题: {topic}/response");
            await Client.PublishAsync(new MqttApplicationMessage
            {
                Topic = $"{topic}/response",
                QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce,
                PayloadSegment = Encoding.UTF8.GetBytes("True")
            });
            Console.WriteLine("[FLink] 确认响应已发送，准备异步处理配置");
            Log.Information("[FLink] 确认响应已发送，准备异步处理配置");

            // 异步处理配置
            Console.WriteLine("[FLink] 将配置处理任务加入队列");
            await TaskQueued.EnqueueAsync(async (_, token) =>
            {
                try
                {
                    Console.WriteLine("[FLink] 开始处理配置内容");
                    Log.Information("[FLink] 开始处理配置内容");
                    var status = await DownConfig(configContent);
                    Console.WriteLine($"[FLink] 配置处理完成，状态:{status}");
                    Log.Information($"[FLink] 配置处理完成，状态:{status}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[FLink] 配置处理失败，错误信息:{ex.Message}");
                    Log.Error($"[FLink] 配置处理失败，错误信息:{ex.Message}");
                    Console.WriteLine($"[FLink] 错误堆栈:{ex.StackTrace}");
                    Log.Error($"[FLink] 错误堆栈:{ex.StackTrace}");
                }
            });
        }
        else if (topic.Contains("reboot") && topic.Contains(_authorize?.Sn))
        {
            Console.WriteLine("[FLink] 收到重启指令，准备发送确认响应");
            Log.Information("[FLink] 收到重启指令，准备发送确认响应");
            Console.WriteLine($"[FLink] 正在发送重启确认响应到主题: {topic}/response");
            await Client.PublishAsync(new MqttApplicationMessage
            {
                Topic = $"{topic}/response",
                QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce,
                PayloadSegment = Encoding.UTF8.GetBytes("True")
            });
            Console.WriteLine("[FLink] 确认响应已发送，准备执行重启");
            Log.Information("[FLink] 确认响应已发送，准备执行重启");
            Console.WriteLine("[FLink] 正在执行系统重启命令...");
            await ShellUtil.Bash("reboot");
            Console.WriteLine("[FLink] 重启命令已执行");
            Log.Information("[FLink] 重启命令已执行");
        }
        else if (topic.Contains("ota") && topic.Contains(_authorize?.Sn))
        {
            Console.WriteLine("[FLink] 收到OTA升级指令，准备解析升级记录");
            Log.Information("[FLink] 收到OTA升级指令，准备解析升级记录");
            var updateFileRecord = JsonConvert.DeserializeObject<UpdateFileRecord>(payLoad);
            if (updateFileRecord == null)
            {
                Console.WriteLine("[FLink] 升级记录解析失败，放弃处理");
                Log.Warning("[FLink] 升级记录解析失败，放弃处理");
                return;
            }

            Console.WriteLine($"[FLink] 升级记录解析成功，RecordId: {updateFileRecord.RecordId}");
            Log.Information("[FLink] 升级记录解析成功，准备保存记录");
            updateFileRecord.IsDown = false;
            updateFileRecord.Id = Yitter.IdGenerator.YitIdHelper.NextId();
            Console.WriteLine("[FLink] 正在保存升级记录...");
            await _db.Insertable(updateFileRecord).ExecuteCommandAsync();
            Console.WriteLine("[FLink] 升级记录已保存，准备发送确认响应");
            Log.Information("[FLink] 升级记录已保存，准备发送确认响应");

            Console.WriteLine($"[FLink] 正在发送OTA确认响应到主题: {topic}/response");
            await Client.PublishAsync(new MqttApplicationMessage
            {
                Topic = $"{topic}/response",
                QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce,
                PayloadSegment = Encoding.UTF8.GetBytes("True")
            });
            Console.WriteLine("[FLink] 确认响应已发送，准备执行升级任务");
            Log.Information("[FLink] 确认响应已发送，准备执行升级任务");

            Console.WriteLine("[FLink] 启动OTA升级任务");
            await OtaTask(updateFileRecord);
            Console.WriteLine("[FLink] 升级任务已启动");
            Log.Information("[FLink] 升级任务已启动");
        }
        else
        {
            Console.WriteLine($"[FLink] 收到未知主题消息: {topic}，不进行处理");
            Log.Warning($"[FLink] 收到未知主题消息: {topic}");
        }
    }

    /// <summary>
    ///     解压文件
    /// </summary>
    /// <param name="fileByte"></param>
    /// <param name="fileName">文件名称</param>
    private static void DeCompressionFile(byte[] fileByte, string fileName)
    {
        var dirPath = $"/usr/local/src/{fileName}/";
        Directory.CreateDirectory(dirPath);
        try
        {
            var option = new ReaderOptions
            {
                ArchiveEncoding = new ArchiveEncoding { Default = Encoding.UTF8 },
                LeaveStreamOpen = true
            };

            var reader = ReaderFactory.Open(new MemoryStream(fileByte), option);
            while (reader.MoveToNextEntry())
                if (reader.Entry.IsDirectory)
                {
                    Directory.CreateDirectory(Path.Combine(dirPath, reader.Entry.Key));
                }
                else
                {
                    // 创建父级目录，防止Entry文件,解压时由于目录不存在报异常
                    var file = Path.Combine(dirPath, reader.Entry.Key);
                    Directory.CreateDirectory(Path.GetDirectoryName(file) ?? string.Empty);
                    reader.WriteEntryToFile(file);
                }
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    #region 回调方法

    /// <summary>
    /// ota升级任务
    /// </summary>
    /// <param name="updateFileRecord"></param>
    private async Task OtaTask(UpdateFileRecord updateFileRecord)
    {
        Console.WriteLine("┌──────────────────────────────────────────────────┐");
        Console.WriteLine("│           开始OTA更新任务                         │");
        Console.WriteLine("└──────────────────────────────────────────────────┘");

        await TaskQueued.EnqueueAsync(async (_, token) =>
        {
            var ip = await App.GetService<SysConfigService>().GetConfigValue<string>(ConfigConst.SysFLinkIp);
            Console.WriteLine($"[OTA更新] 开始从服务器 {ip} 下载更新包");
            Log.Information($"[OTA更新] 开始从服务器 {ip} 下载更新包");

            // 使用唯一的临时文件名
            var tempFilePath = Path.Combine(Path.GetTempPath(), "");
            Console.WriteLine($"[OTA更新] 临时文件路径: {tempFilePath}");

            try
            {
                Console.WriteLine("[OTA更新] 创建HTTP客户端，超时设置: 10分钟");
                using var client = new HttpClient { Timeout = TimeSpan.FromSeconds(60 * 10) };
                var url = $"http://{ip}:9081/firmwareManage/upload/download?RecordId={updateFileRecord.RecordId}";

                Console.WriteLine($"[OTA更新] 请求URL: {url}");
                Log.Information($"[OTA更新] 请求URL: {url}");

                // 使用流式下载以减少内存占用
                Console.WriteLine("[OTA更新] 开始流式下载...");
                using var response = await client.GetAsync(url, HttpCompletionOption.ResponseHeadersRead);

                // 输出详细的错误信息
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"[OTA更新] HTTP请求失败: {(int)response.StatusCode} ({response.StatusCode})");
                    Log.Error($"[OTA更新] HTTP状态码: {(int)response.StatusCode} ({response.StatusCode})");
                    Console.WriteLine($"[OTA更新] 错误响应内容: {errorContent}");
                    Log.Error($"[OTA更新] 错误响应内容: {errorContent}");
                    Console.WriteLine($"[OTA更新] RecordId: {updateFileRecord.RecordId}");
                    Log.Error($"[OTA更新] RecordId: {updateFileRecord.RecordId}");
                    response.EnsureSuccessStatusCode();
                }

                // 获取文件信息
                var totalBytes = response.Content.Headers.ContentLength ?? -1L;
                // 打印文件大小
                var totalMB = Math.Round(totalBytes / (1024.0 * 1024.0), 2);
                var fileName = response.Content.Headers.ContentDisposition?.FileName;
                Console.WriteLine($"[OTA更新] 文件大小: {totalMB}MB");
                Log.Information($"[OTA更新] 文件大小: {totalMB}MB");
                Console.WriteLine($"[OTA更新] 文件名称: {fileName ?? "未知"}");
                Log.Information($"[OTA更新] 文件名称: {fileName}");
                tempFilePath = Path.Combine(Path.GetTempPath(), fileName);
                Console.WriteLine($"[OTA更新] 完整临时文件路径: {tempFilePath}");

                // 确保文件流正确关闭
                Console.WriteLine("[OTA更新] 开始读取文件流...");
                using (var stream = await response.Content.ReadAsStreamAsync())
                using (var fileStream = new FileStream(tempFilePath, FileMode.Create, FileAccess.Write, FileShare.None))
                {
                    var buffer = new byte[81920];
                    var totalBytesRead = 0L;
                    var lastProgressReport = 0;
                    Console.WriteLine("[OTA更新] 开始写入文件...");

                    while (true)
                    {
                        var bytesRead = await stream.ReadAsync(buffer);
                        if (bytesRead == 0) break;

                        await fileStream.WriteAsync(buffer.AsMemory(0, bytesRead));
                        totalBytesRead += bytesRead;

                        var progressPercentage = (int)((totalBytesRead * 100) / totalBytes);
                        if (progressPercentage > lastProgressReport + 4)
                        {
                            var downloadedMB = Math.Round(totalBytesRead / (1024.0 * 1024.0), 2);
                            Console.WriteLine($"[OTA更新] 下载进度: {progressPercentage}% ({downloadedMB}MB/{totalMB}MB)");
                            Log.Information($"[OTA更新] 下载进度: {progressPercentage}% ({downloadedMB}MB/{totalMB}MB)");
                            lastProgressReport = progressPercentage;
                        }
                    }

                    // 确保文件完全写入
                    Console.WriteLine("[OTA更新] 确保文件完全写入...");
                    await fileStream.FlushAsync();
                }

                Console.WriteLine($"[OTA更新] 文件下载完成: {tempFilePath}");
                Log.Information($"[OTA更新] 文件下载完成: {tempFilePath}");

                // 更新记录
                Console.WriteLine("[OTA更新] 更新数据库记录...");
                updateFileRecord.IsDown = true;
                updateFileRecord.FileName = fileName;
                await _db.Updateable(updateFileRecord).ExecuteCommandAsync(token);
                Console.WriteLine("[OTA更新] 数据库记录已更新");

                // 安装更新包
                Console.WriteLine("[OTA更新] 开始安装更新包...");
                await PackageInstall(updateFileRecord);
                Console.WriteLine("[OTA更新] 更新包安装完成");

                // 清理临时文件
                if (File.Exists(tempFilePath))
                {
                    Console.WriteLine("[OTA更新] 清理临时文件...");
                    File.Delete(tempFilePath);
                    Console.WriteLine("[OTA更新] 临时文件已清理");
                    Log.Information("[OTA更新] 临时文件已清理");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("┌──────────────────────────────────────────────────┐");
                Console.WriteLine("│           OTA更新失败                            │");
                Console.WriteLine("└──────────────────────────────────────────────────┘");
                Console.WriteLine($"[OTA更新] 错误: {ex.Message}");
                Log.Error($"[OTA更新] 错误: {ex.Message}");
                Console.WriteLine($"[OTA更新] 堆栈: {ex.StackTrace}");
                Log.Error($"[OTA更新] 堆栈: {ex.StackTrace}");

                // 清理临时文件
                try
                {
                    if (File.Exists(tempFilePath))
                    {
                        Console.WriteLine("[OTA更新] 清理临时文件...");
                        File.Delete(tempFilePath);
                        Console.WriteLine("[OTA更新] 临时文件已清理");
                        Log.Information("[OTA更新] 临时文件已清理");
                    }
                }
                catch (Exception cleanupEx)
                {
                    Console.WriteLine($"[OTA更新] 清理临时文件失败: {cleanupEx.Message}");
                    Log.Error($"[OTA更新] 清理临时文件失败: {cleanupEx.Message}");
                }

                throw;
            }
        }, 1000);
    }

    /// <summary>
    ///     安装更新包
    /// </summary>
    /// <param name="id"></param>
    public async Task PackageInstall(UpdateFileRecord updateFileRecord)
    {
        if (!updateFileRecord.IsDown)
            throw Oops.Oh("更新包还未下载！");
        // 已经完成升级
        if (updateFileRecord.IsUpdate)
            throw Oops.Oh("已经完成升级！");

        var fileName = Path.Combine(Path.GetTempPath(), updateFileRecord.FileName); ;
        var backupTimestamp = System.DateTime.Now.ToString("yyyyMMddHHmmss");
        var backupPath = $"/usr/local/src/backup_{backupTimestamp}";
        // 读取文件
        var fileByte = await File.ReadAllBytesAsync(fileName);
        // 打印文件大小
        Log.Information($"[安装]--文件大小: {fileByte.Length} bytes");
        // 解密
        var decryptFileByte = AESEncryption.Decrypt(fileByte, "201001");
        // 打印解密后文件大小
        Log.Information($"[安装]--解密后文件大小: {decryptFileByte.Length} bytes");
        // 解压文件
        DeCompressionFile(decryptFileByte, fileName);

        try
        {
            // 创建备份目录
            await ShellUtil.Bash($"mkdir -p {backupPath}");

            // 备份现有文件
            await ShellUtil.Bash($"cp -r /usr/local/src/iotgatewayhtml {backupPath}/");
            await ShellUtil.Bash($"cp -r /usr/local/src/iotgateway {backupPath}/");
            Log.Warning("[远程更新]--创建备份完成");

            // 更新前端文件
            await ShellUtil.Bash($"cp -r /usr/local/src/{fileName}/iotgatewayhtml/ /usr/local/src/");
            Log.Warning("[远程更新]--前端文件更新完成");

            // 更新后端文件
            await ShellUtil.Bash("rm -rf /usr/local/src/iotgateway/*");
            await ShellUtil.Bash($"cp -r /usr/local/src/{fileName}/iotgateway/ /usr/local/src/");
            Log.Warning("[远程更新]--后端文件更新完成");

            // 删除更新包
            await ShellUtil.Bash($"rm -rf /usr/local/src/{fileName}");
            Log.Warning("[远程更新]--删除更新包");

            updateFileRecord.IsUpdate = true;
            await _db.Updateable(updateFileRecord).ExecuteCommandAsync();

            // 更新成功后删除备份
            await ShellUtil.Bash($"rm -rf {backupPath}");
            Log.Warning("[远程更新]--删除备份文件");

            // 延迟5秒重启
            await TaskQueued.EnqueueAsync(async (_, _) =>
            {
                await ShellUtil.Bash("reboot");
                Log.Warning("[远程更新]--网关重启");
            }, 5000);
        }
        catch (Exception ex)
        {
            Log.Error($"[远程更新]--更新失败: {ex.Message}");

            try
            {
                // 回滚到备份
                await ShellUtil.Bash($"rm -rf /usr/local/src/iotgatewayhtml/*");
                await ShellUtil.Bash($"rm -rf /usr/local/src/iotgateway/*");
                await ShellUtil.Bash($"cp -r {backupPath}/iotgatewayhtml/* /usr/local/src/iotgatewayhtml/");
                await ShellUtil.Bash($"cp -r {backupPath}/iotgateway/* /usr/local/src/iotgateway/");
                Log.Warning("[远程更新]--回滚完成");

                // 清理备份
                await ShellUtil.Bash($"rm -rf {backupPath}");
            }
            catch (Exception rollbackEx)
            {
                Log.Error($"[远程更新]--回滚失败: {rollbackEx.Message}");
            }

            throw Oops.Oh($"更新失败: {ex.Message}");
        }
    }

    /// <summary>
    ///  上传网关配置
    /// </summary>
    private async Task<bool> UploadConfig()
    {
        try
        {
            // 协议列表
            var driverList = await _db.CopyNew().Queryable<Feng.IotGateway.Core.Entity.Driver>().Where(w =>
                    _driverService.DriverInfos.Select(s => s.Type.FullName).Contains(w.AssembleName))
                .ToListAsync();

            var dynDriverList = new List<dynamic>();
            foreach (var driver in driverList)
            {
                var dynDriver = new
                {
                    driver.DriverName,
                    driver.Id,
                    driver.DriverType,
                    Methods = _driverService.driverMethods.TryGetValue(driver.AssembleName, out var value) ? value : new List<DriverMethodsItem>(),
                    Funcs = _driverService.driverFunc.TryGetValue(driver.AssembleName, out var value2) ? value2 : new List<DriverFuncItem>(),
                    Configs = _driverService.driverConfig.TryGetValue(driver.AssembleName, out var value3) ? value3 : new List<DeviceConfig>()
                };
                dynDriverList.Add(dynDriver);
            }

            // 设备列表
            var deviceList = await _db.CopyNew().Queryable<Device>()
                .Includes(w => w.DeviceVariable)
                .Includes(w => w.DeviceConfigs)
                .Includes(w => w.DeviceEvent)
                .ToListAsync();

            // 网络配置
            var netWork = await $"{httpUrl}/network/list"
                .SetHttpMethod(HttpMethod.Get)
                .SetContentType("application/json")
                .SetClient(() => new HttpClient { Timeout = TimeSpan.FromSeconds(5) })
                .SetContentEncoding(Encoding.UTF8)
                .GetAsAsync<RESTfulResult<NetworkSettingModel>>();
            // 转发配置
            var transPondList = await _db.CopyNew().Queryable<TransPond>()
                .Includes(w => w.TransPondTopic)
                .ToListAsync();
            var scriptList = await _db.CopyNew().Queryable<SysScript>().ToListAsync();

            // 网关配置
            var gatewayConfig = new
            {
                Name = _authorize.Ident,
                _authorize.Sn,
                Model = EnumUtil.GetEnumDesc(MachineUtil.CurrentSystemEnvironment),
                MachineUtil.Version,
                RunTime = MachineUtil.ServiceOnlineTime,
                Config = JsonConvert.SerializeObject(new Dictionary<string, object>
                {
                    {
                        "network", Clay.Object(new
                        {
                            eth0 = MachineUtil.Clay.Eth0,
                            eth1 = MachineUtil.Clay.Eth1,
                            eth2 = MachineUtil.Clay.Eth2,
                            wlan0 = MachineUtil.Clay.Wifi
                        }).ToString()
                    }
                }),
                Script = scriptList
            };
            var script = await _db.CopyNew().Queryable<EdgeComputingScript>().ToListAsync();
            var scriptStrategy = await _db.CopyNew().Queryable<ScriptExecutionStrategy>().ToListAsync();
            // 上报配置内容
            var obj = new
            {
                gateway = gatewayConfig,
                driver = dynDriverList,
                device = deviceList,
                netWork = netWork != null ? JsonConvert.SerializeObject(netWork.Data) : "",
                transPond = transPondList,
                script,
                scriptStrategy
            };
            Feng.Common.Extension.Console.WriteLine("即将上报配置！");
            var status = await Client.PublishAsync(new MqttApplicationMessage
            {
                Topic = $"/{_authorize?.Sn}/up/dev/set/config",
                QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce,
                PayloadSegment = Encoding.UTF8.GetBytes(obj.ToJson())
            });
            return status.ReasonCode == MqttClientPublishReasonCode.Success;
        }
        catch (Exception e)
        {
            Console.WriteLine($"上报网关配置Error：{e.Message}");
            return false;
        }
    }

    /// <summary>
    /// 下发网关配置内容
    /// </summary>
    /// <param name="payLoad"></param>
    private async Task<bool> DownConfig(string payLoad)
    {
        try
        {
            Console.WriteLine("┌──────────────────────────────────────────────────┐");
            Console.WriteLine("│           下发网关配置处理开始                    │");
            Console.WriteLine("└──────────────────────────────────────────────────┘");

            dynamic clay = Clay.Parse(payLoad);
            if (!clay.IsDefined("editCon"))
            {
                Console.WriteLine("[FLink] 下发配置 - 缺少editCon字段，无法处理配置");
                Log.Warning("[FLink] 下发配置 - 缺少editCon字段，无法处理配置");
                return false;
            }

            List<string> editCon = clay.editCon;
            Console.WriteLine($"[FLink] 下发配置 - 配置类型: {string.Join(", ", editCon)}");
            Log.Information($"[FLink] 下发配置 - 配置类型: {string.Join(", ", editCon)}");

            var tasks = new List<Task>();

            // 设备配置处理
            if (editCon.Contains("deviceList") && clay.IsDefined("deviceListConfigStr"))
            {
                Console.WriteLine("[FLink] 下发配置 - 添加设备配置处理任务");
                tasks.Add(HandleDeviceConfig(clay.deviceListConfigStr.ToString()));
            }

            // 脚本配置处理
            if (editCon.Contains("scriptConfig") && clay.IsDefined("scriptConfigStr"))
            {
                Console.WriteLine("[FLink] 下发配置 - 添加脚本配置处理任务");
                tasks.Add(HandleScriptConfig(clay.scriptConfigStr.ToString()));
            }

            // 脚本策略配置处理
            if (editCon.Contains("scriptStrategyConfig") && clay.IsDefined("scriptStrategyConfigStr"))
            {
                Console.WriteLine("[FLink] 下发配置 - 添加脚本策略配置处理任务");
                var scriptStrategyConfigStr = clay.scriptStrategyConfigStr.ToString();
                Console.WriteLine($"[FLink] 下发配置 - 脚本策略配置内容长度: {scriptStrategyConfigStr.Length} 字节");
                tasks.Add(HandleScriptStrategyConfig(scriptStrategyConfigStr));
            }
            else if (editCon.Contains("scriptStrategyConfig"))
            {
                Console.WriteLine("[FLink] 下发配置 - 包含scriptStrategyConfig但缺少scriptStrategyConfigStr字段");
                Log.Warning("[FLink] 下发配置 - 包含scriptStrategyConfig但缺少scriptStrategyConfigStr字段");
            }

            // 转发配置处理
            if (editCon.Contains("transPondConfig") && clay.IsDefined("transPondConfigStr"))
            {
                Console.WriteLine("[FLink] 下发配置 - 添加转发配置处理任务");
                tasks.Add(HandleTransPondConfig(clay.transPondConfigStr.ToString()));
            }

            // 网络配置处理
            if (editCon.Contains("netWorkConfig") && clay.IsDefined("netWorkConfigStr"))
            {
                Console.WriteLine("[FLink] 下发配置 - 添加网络配置处理任务");
                tasks.Add(HandleNetworkConfig(clay.netWorkConfigStr.ToString()));
            }

            // 等待所有配置处理完成
            Console.WriteLine($"[FLink] 下发配置 - 等待 {tasks.Count} 个配置处理任务完成");
            await Task.WhenAll(tasks);
            Console.WriteLine("[FLink] 下发配置 - 所有配置处理任务已完成");
            return true;
        }
        catch (Exception e)
        {
            Console.WriteLine($"[FLink] 下发网关配置内容出错: {e.Message}");
            Console.WriteLine($"[FLink] 错误堆栈: {e.StackTrace}");
            Log.Error($"[FLink] 下发网关配置内容出错:{e.Message}");
            Log.Error($"[FLink] 错误堆栈:{e.StackTrace}");
            return false;
        }
    }

    // 处理设备配置
    private async Task HandleDeviceConfig(string configStr)
    {
        try
        {
            Log.Information("[FLink] 开始处理设备配置");
            // 设备列表
            var deviceList = await _db.CopyNew().Queryable<Device>().ToListAsync();

            // 停止采集线程
            await _eventPublisher.PublishAsync(EventConst.StopDeviceThreadAll, "");
            Log.Information("[FLink] 已停止所有设备采集线程");

            // 删除旧表
            foreach (var device in deviceList)
            {
                if (!device.Release) continue;
                try
                {
                    var sql = $"DROP TABLE IF EXISTS {device.DeviceName};";
                    _executeService.ExecuteCommand(sql);
                    Log.Information($"[FLink] 成功删除设备表: {device.DeviceName}");
                }
                catch (Exception e)
                {
                    Log.Error($"[FLink] 删除设备表失败: {device.DeviceName}, 错误:{e.Message}");
                }
            }

            // 删除旧设备配置
            await _db.CopyNew().DeleteNav(deviceList)
                .Include(e => e.DeviceConfigs)
                .Include(w => w.DeviceEvent)
                .Include(e => e.DeviceEventLog)
                .Include(e => e.DeviceVariable)
                .ExecuteCommandAsync();
            Log.Information("[FLink] 已删除旧设备配置");

            // 创建新的设备配置
            List<Device> newDeviceList = JsonConvert.DeserializeObject<List<Device>>(configStr);
            Log.Information($"[FLink] 解析到{newDeviceList?.Count ?? 0}个新设备配置");

            foreach (var newDevice in newDeviceList)
            {
                try
                {
                    // 设置必填字段的默认值
                    newDevice.Release = false;
                    newDevice.DeviceInfo ??= new Dictionary<string, string>(); // DeviceInfo不能为null
                    newDevice.DeviceInfoExtensions ??= new();

                    // 确保子项的必填字段
                    if (newDevice.DeviceVariable != null)
                    {

                    }

                    await _db.CopyNew().InsertNav(newDevice)
                        .Include(w => w.DeviceConfigs)
                        .Include(w => w.DeviceVariable)
                        .Include(w => w.DeviceEvent)
                        .ExecuteCommandAsync();

                    Log.Information($"[FLink] 成功插入设备配置: {newDevice.DeviceName}");

                    await _eventPublisher.PublishAsync(EventConst.CreateDeviceThread, newDevice.Id);
                    Log.Information($"[FLink] 已创建设备采集线程: {newDevice.DeviceName}");
                }
                catch (Exception e)
                {
                    Log.Error($"[FLink] 插入设备配置失败: {newDevice.DeviceName}, 错误:{e.Message}");
                    throw;
                }
            }

            Log.Information("[FLink] 设备配置处理完成");
        }
        catch (Exception e)
        {
            Log.Error($"[FLink] 设备配置处理失败:{e.Message}");
            Log.Error($"[FLink] 错误堆栈:{e.StackTrace}");
            throw;
        }
    }

    // 处理脚本配置
    private async Task HandleScriptConfig(string configStr)
    {
        try
        {
            Console.WriteLine("[FLink] 开始处理脚本配置");
            Console.WriteLine("[FLink] 删除旧的脚本配置");
            await _db.CopyNew().Deleteable<EdgeComputingScript>().ExecuteCommandAsync();
            Console.WriteLine("[FLink] 删除旧的脚本配置完成");

            Console.WriteLine($"[FLink] 开始反序列化脚本配置,原始内容：{configStr}");
            List<EdgeComputingScript> scriptList = JsonConvert.DeserializeObject<List<EdgeComputingScript>>(configStr);
            Console.WriteLine($"[FLink] 反序列化脚本配置完成,scriptList:{scriptList.Count}");

            Console.WriteLine("[FLink] 开始插入脚本配置");
            await _db.CopyNew().Insertable(scriptList).ExecuteCommandAsync();
            Console.WriteLine("[FLink] 插入脚本配置完成");

            Console.WriteLine("[FLink] 脚本配置处理完成");
        }
        catch (Exception e)
        {
            Console.WriteLine($"[FLink] 脚本配置处理失败: {e.Message}");
            Console.WriteLine($"[FLink] 错误堆栈: {e.StackTrace}");
            Log.Error($"[FLink] 脚本配置处理失败:{e.Message}");
            Log.Error($"[FLink] 错误堆栈:{e.StackTrace}");
            throw;
        }
    }

    // 处理脚本策略配置
    private async Task HandleScriptStrategyConfig(string configStr)
    {
        try
        {
            Console.WriteLine("┌──────────────────────────────────────────────────┐");
            Console.WriteLine("│           处理脚本策略配置                        │");
            Console.WriteLine("└──────────────────────────────────────────────────┘");

            Console.WriteLine($"[FLink] 脚本策略配置 - 接收到配置内容长度: {configStr.Length} 字节");
            Log.Information($"[FLink] 脚本策略配置 - 接收到配置内容长度: {configStr.Length} 字节");

            // 尝试解析配置字符串
            Console.WriteLine("[FLink] 脚本策略配置 - 尝试解析配置JSON");
            try
            {
                var testParse = JsonConvert.DeserializeObject<List<ScriptExecutionStrategy>>(configStr);
                Console.WriteLine($"[FLink] 脚本策略配置 - JSON解析成功，项目数量: {testParse?.Count ?? 0}");
                Log.Information($"[FLink] 脚本策略配置 - JSON解析成功，项目数量: {testParse?.Count ?? 0}");
            }
            catch (Exception parseEx)
            {
                Console.WriteLine($"[FLink] 脚本策略配置 - JSON解析失败: {parseEx.Message}");
                Log.Error($"[FLink] 脚本策略配置 - JSON解析失败: {parseEx.Message}");
            }

            Console.WriteLine("[FLink] 脚本策略配置 - 查询现有策略");
            var scriptExecutionStrategyList = await _db.CopyNew().Queryable<ScriptExecutionStrategy>().ToListAsync();
            Console.WriteLine($"[FLink] 脚本策略配置 - 找到 {scriptExecutionStrategyList.Count} 个现有策略");
            Log.Information($"[FLink] 脚本策略配置 - 找到 {scriptExecutionStrategyList.Count} 个现有策略");

            Console.WriteLine("[FLink] 脚本策略配置 - 删除现有策略");
            var deleteResult = await _db.CopyNew().Deleteable(scriptExecutionStrategyList).ExecuteCommandAsync();
            Console.WriteLine($"[FLink] 脚本策略配置 - 删除结果: {deleteResult}");
            Log.Information($"[FLink] 脚本策略配置 - 删除结果: {deleteResult}");

            Console.WriteLine("[FLink] 脚本策略配置 - 反序列化新策略");
            List<ScriptExecutionStrategy> scriptStrategyList = JsonConvert.DeserializeObject<List<ScriptExecutionStrategy>>(configStr);
            Console.WriteLine($"[FLink] 脚本策略配置 - 成功反序列化 {scriptStrategyList?.Count ?? 0} 个新策略");
            Log.Information($"[FLink] 脚本策略配置 - 成功反序列化 {scriptStrategyList?.Count ?? 0} 个新策略");

            if (scriptStrategyList != null && scriptStrategyList.Count > 0)
            {
                Console.WriteLine("[FLink] 脚本策略配置 - 插入新策略");
                var insertResult = await _db.CopyNew().Insertable(scriptStrategyList).ExecuteCommandAsync();
                Console.WriteLine($"[FLink] 脚本策略配置 - 插入结果: {insertResult}");
                Log.Information($"[FLink] 脚本策略配置 - 插入结果: {insertResult}");
            }
            else
            {
                Console.WriteLine("[FLink] 脚本策略配置 - 没有新策略需要插入");
                Log.Information("[FLink] 脚本策略配置 - 没有新策略需要插入");
            }

            Console.WriteLine("[FLink] 脚本策略配置 - 处理完成");
            Log.Information("[FLink] 脚本策略配置 - 处理完成");
        }
        catch (Exception e)
        {
            Console.WriteLine($"[FLink] 脚本策略配置处理失败: {e.Message}");
            Console.WriteLine($"[FLink] 错误堆栈: {e.StackTrace}");
            Log.Error($"[FLink] 脚本策略配置处理失败:{e.Message}");
            Log.Error($"[FLink] 错误堆栈:{e.StackTrace}");
            throw;
        }
    }

    // 处理转发配置
    private async Task HandleTransPondConfig(string configStr)
    {
        try
        {
            // 删除旧的转发配置
            await _db.CopyNew().Deleteable<TransPond>().ExecuteCommandAsync();
            // 创建新的转发配置
            List<TransPond> transPondList = JsonConvert.DeserializeObject<List<TransPond>>(configStr);
            // 设置TransPondTopic的TransPondId
            foreach (var transPond in transPondList)
            {
                transPond.TransPondTopic ??= new List<TransPondTopic>();
            }
            // 插入新的转发配置
            await _db.CopyNew().Insertable(transPondList)
                .ExecuteCommandAsync();
        }
        catch (Exception e)
        {
            Log.Error($"[FLink] 转发配置处理失败:{e.Message}");
            throw;
        }
    }

    // 处理网络配置
    private async Task HandleNetworkConfig(string configStr)
    {
        try
        {
            NetworkSettingModel networkSettingModel = JsonConvert.DeserializeObject<NetworkSettingModel>(configStr);
            await $"{httpUrl}/network/save"
                .SetHttpMethod(HttpMethod.Post)
                .SetBody(networkSettingModel)
                .SetContentType("application/json")
                .SetClient(() => new HttpClient { Timeout = TimeSpan.FromSeconds(15) })
                .SetContentEncoding(Encoding.UTF8)
                .OnException((_, errors, errorMsg) =>
                {
                    if (errors != null) Log.Error($"[flink]-网络配置出错：StatusCode:{errors.StatusCode},errorMsg:{errors}");
                })
                .PostAsStringAsync();
        }
        catch (Exception e)
        {
            Log.Error($"[FLink] 网络配置处理失败:{e.Message}");
            throw;
        }
    }

    /// <summary>
    /// ping
    /// </summary>
    /// <param name="payLoad"></param>
    /// <param name="topic"></param>
    private async Task Ping(string payLoad, string topic)
    {
        try
        {
            Console.WriteLine("┌──────────────────────────────────────────────────┐");
            Console.WriteLine("│           执行Ping操作                           │");
            Console.WriteLine("└──────────────────────────────────────────────────┘");

            var objPing = JsonConvert.DeserializeObject<PingModel>(payLoad);
            if (objPing == null)
            {
                Console.WriteLine($"[Ping] ping指令无内容或格式错误, payload: {payLoad}");
                Log.Information($"ping无内容, payLoad:{payLoad}");
                return;
            }

            Console.WriteLine($"[Ping] 目标IP: {objPing.Ip}");
            if (string.IsNullOrEmpty(objPing.NetworkName))
            {
                Console.WriteLine($"[Ping] 使用默认网卡ping IP: {objPing.Ip}");
                Log.Information($"使用不指定网卡ping, ip:{objPing.Ip}");

                Console.WriteLine("[Ping] 创建Ping发送器");
                var pingSender = new Ping();
                var timeOut = 1000;
                Console.WriteLine($"[Ping] 超时设置: {timeOut}ms");

                Console.WriteLine("[Ping] 发送Ping请求...");
                var reply = pingSender.Send(objPing.Ip, timeOut);
                var status = reply!.Status == IPStatus.Success;

                Console.WriteLine($"[Ping] Ping结果: {reply.Status}, 延迟: {reply.RoundtripTime}ms");
                Console.WriteLine($"[Ping] 发送响应到主题: {topic}/response, 结果: {status}");

                await Client.PublishAsync(new MqttApplicationMessage
                {
                    Topic = $"{topic}/response",
                    QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce,
                    PayloadSegment = Encoding.UTF8.GetBytes(status.ToString())
                });

                Console.WriteLine("[Ping] 响应已发送");
            }
            else
            {
                string networkName = objPing.NetworkName;
                Console.WriteLine($"[Ping] 使用指定网卡: {networkName} ping IP: {objPing.Ip}");

                var cmd = $"ping -c 4 -I {networkName.ToLower()} {objPing.Ip}";
                Console.WriteLine($"[Ping] 执行命令: {cmd}");
                Log.Information($"[ping]--cmd：{cmd}");

                Console.WriteLine("[Ping] 开始执行命令...");
                var output = await ShellUtil.Bash(cmd);
                var outputValue = output.Trim().Split("\n").ToList();

                Console.WriteLine("[Ping] 命令执行完成，获得结果:");
                foreach (var line in outputValue)
                {
                    Console.WriteLine($"[Ping] {line}");
                }

                Console.WriteLine($"[Ping] 发送响应到主题: {topic}/response");
                await Client.PublishAsync(new MqttApplicationMessage
                {
                    Topic = $"{topic}/response",
                    QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce,
                    PayloadSegment = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(outputValue))
                });

                Console.WriteLine("[Ping] 响应已发送");
            }
        }
        catch (Exception e)
        {
            Console.WriteLine($"[Ping] 执行Ping操作失败: {e.Message}");
            Console.WriteLine($"[Ping] 错误堆栈: {e.StackTrace}");
            Log.Error("ping:" + e.Message);
        }
    }

    #endregion

    #region 私有方法

    #endregion

    /// <summary>
    ///     停止
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        // 释放mqtt连接
        Client?.DisconnectAsync();
        Client?.Dispose();
        return Task.CompletedTask;
    }
}