using System.Net;
using System.Net.Sockets;

namespace IotGateway.FtpSiemensImplant.Models;

/// <summary>
/// FTP用户类，用于存储用户会话信息
/// </summary>
public class FtpSiemensImplantUser
{
  /// <summary>
  /// 用户名
  /// </summary>
  public string Username { get; set; } = string.Empty;

  /// <summary>
  /// 设备编码
  /// </summary>
  public string DeviceCode { get; set; } = string.Empty;

  /// <summary>
  /// 工作目录（根目录）
  /// </summary>
  public string WorkDirectory { get; set; } = string.Empty;

  /// <summary>
  /// 当前目录
  /// </summary>
  public string CurrentDirectory { get; set; } = string.Empty;

  /// <summary>
  /// 是否已认证
  /// </summary>
  public bool IsAuthenticated { get; set; } = false;

  /// <summary>
  /// 登录状态: 0=未登录, 1=等待密码, 2=已登录
  /// </summary>
  public int LoginState { get; set; } = 0;

  /// <summary>
  /// 用户角色/权限
  /// </summary>
  public string Role { get; set; } = "siemensImplantUser";

  /// <summary>
  /// 数据连接端点（用于主动模式）
  /// </summary>
  public IPEndPoint? DataEndPoint { get; set; }

  /// <summary>
  /// 数据连接监听器（用于被动模式）
  /// </summary>
  public TcpListener? DataListener { get; set; }

  /// <summary>
  /// 是否使用被动模式
  /// </summary>
  public bool IsPassiveMode { get; set; } = false;

  /// <summary>
  /// 数据传输类型（ASCII或二进制）
  /// </summary>
  public string TransferType { get; set; } = "I"; // 默认ASCII模式
}