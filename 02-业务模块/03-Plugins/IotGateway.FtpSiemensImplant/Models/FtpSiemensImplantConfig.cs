using IotGateway.Plugin.Core;

namespace IotGateway.FtpSiemensImplant.Models;

/// <summary>
/// FTP服务器配置
/// </summary>
public class FtpSiemensImplantConfig : IPluginConfig
{
  /// <summary>
  /// 服务端口
  /// </summary>
  public int Port { get; set; } = 21;

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool Enabled { get; set; } = false;

  /// <summary>
  /// 文件发送路径
  /// </summary>
  public string SendPath { get; set; } = "RootFiles/{0}/SEND";

  /// <summary>
  /// 文件接收路径
  /// </summary>
  public string ReceivePath { get; set; } = "RootFiles/{0}/REC";

  /// <summary>
  /// SCP服务器主机名或IP地址
  /// </summary>
  public string ScpHostname { get; set; } = "localhost";

  /// <summary>
  /// SCP服务器端口
  /// </summary>
  public int ScpPort { get; set; } = 22;

  /// <summary>
  /// SCP服务器用户名
  /// </summary>
  public string ScpUsername { get; set; } = "root";

  /// <summary>
  /// SCP服务器密码
  /// </summary>
  public string ScpPassword { get; set; } = "";

  /// <summary>
  /// SCP服务器工作路径
  /// </summary>
  public string ScpWorkPath { get; set; } = "/tmp";

  /// <summary>
  /// 获取配置Schema
  /// </summary>
  public object GetConfigurationSchema()
  {
    return new
    {
      properties = new
      {
        Port = new
        {
          type = "integer",
          title = "端口号",
          description = "FTP服务器监听端口",
          minimum = 1,
          maximum = 65535,
          defaultValue = 21,
        },
        Enabled = new
        {
          type = "boolean",
          title = "启用状态",
          description = "是否启用FTP服务"
        },
        ScpHostname = new
        {
          type = "string",
          title = "SCP主机名",
          description = "SCP服务器主机名或IP地址"
        },
        ScpPort = new
        {
          type = "integer",
          title = "SCP端口",
          description = "SCP服务器端口",
          minimum = 1,
          maximum = 65535,
          defaultValue = 22
        },
        ScpUsername = new
        {
          type = "string",
          title = "SCP用户名",
          description = "SCP服务器登录用户名"
        },
        ScpPassword = new
        {
          type = "string",
          title = "SCP密码",
          description = "SCP服务器登录密码"
        },
        ScpWorkPath = new
        {
          type = "string",
          title = "SCP工作目录",
          description = "SCP服务器上的工作目录路径"
        }
      },
      required = new[] { "Port", "Enabled", "ScpHostname", "ScpPort", "ScpUsername", "ScpPassword", "ScpWorkPath" }
    };
  }
}