using System;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using IotGateway.FtpSiemensImplant.Models;
using Microsoft.Extensions.Logging;
using Renci.SshNet;

namespace IotGateway.FtpSiemensImplant.Core;

/// <summary>
/// FTP命令处理器
/// </summary>
public class FtpSiemensImplantCommandHandler : IDisposable
{
  private readonly FtpSiemensImplantSession _siemensImplantSession;
  private readonly FtpSiemensImplantServerPlugin _plugin;
  private readonly ILogger _logger;
  private readonly ScpClient? _scpClient;
  private bool _disposed = false;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="siemensImplantSession">FTP会话</param>
  /// <param name="plugin">FTP服务器插件</param>
  /// <param name="logger">日志记录器</param>
  public FtpSiemensImplantCommandHandler(FtpSiemensImplantSession siemensImplantSession, FtpSiemensImplantServerPlugin plugin, ILogger logger)
  {
    _siemensImplantSession = siemensImplantSession;
    _plugin = plugin;
    _logger = logger;

    // 初始化SCP客户端
    var config = plugin.GetConfig();

    try
    {
      _logger.LogInformation("初始化SCP客户端, 服务器: {Hostname}:{Port}, 用户名: {Username}, 密码: {Password}",
        config.ScpHostname, config.ScpPort, config.ScpUsername, config.ScpPassword);

      // 创建认证方法
      AuthenticationMethod authenticationMethod = new PasswordAuthenticationMethod(config.ScpUsername, config.ScpPassword);
      // 创建连接信息
      ConnectionInfo connectionInfo = new ConnectionInfo(
          config.ScpHostname,
          config.ScpPort,
          config.ScpUsername,
          new AuthenticationMethod[] { authenticationMethod }
      );
      _scpClient = new ScpClient(connectionInfo);
      _logger.LogInformation("SCP客户端初始化成功, 服务器: {Hostname}:{Port}",
        config.ScpHostname, config.ScpPort);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "初始化SCP客户端失败");
    }
  }

  /// <summary>
  /// 处理FTP命令
  /// </summary>
  /// <param name="commandLine">命令行</param>
  public async Task HandleCommandAsync(string commandLine)
  {
    try
    {
      // 解析命令和参数
      var cmdParts = commandLine.Split(new[] { ' ' }, 2);
      var command = cmdParts[0].ToUpperInvariant();
      var parameter = cmdParts.Length > 1 ? cmdParts[1] : string.Empty;

      // 记录命令和参数
      _logger.LogInformation("来自{RemoteEndPoint}：[{Command}] ", _siemensImplantSession.RemoteEndPoint, command);
      _logger.LogInformation("Command: {Command} param: {Parameter}", command, parameter);

      // 根据命令类型处理
      switch (command)
      {
        case "USER":
          await HandleUserAsync(parameter);
          break;
        case "PASS":
          await HandlePassAsync(parameter);
          break;
        case "SYST":
          await _siemensImplantSession.SendResponseAsync("215 UNIX Type: L8");
          break;
        case "FEAT":
          await HandleFeatAsync();
          break;
        case "PWD":
        case "XPWD":
          await HandlePwdAsync();
          break;
        case "CWD":
        case "XCWD":
          await HandleCwdAsync(parameter);
          break;
        case "CDUP":
        case "XCUP":
          await HandleCdupAsync();
          break;
        case "TYPE":
          await HandleTypeAsync(parameter);
          break;
        case "PASV":
          await HandlePasvAsync();
          break;
        case "PORT":
          await HandlePortAsync(parameter);
          break;
        case "LIST":
        case "NLST":
          await HandleListAsync(parameter, command == "LIST");
          break;
        case "RETR":
          await HandleRetrAsync(parameter);
          break;
        case "STOR":
          await HandleStorAsync(parameter);
          break;
        case "DELE":
          await HandleDeleAsync(parameter);
          break;
        case "MKD":
        case "XMKD":
          await HandleMkdAsync(parameter);
          break;
        case "RMD":
        case "XRMD":
          await HandleRmdAsync(parameter);
          break;
        case "QUIT":
          await _siemensImplantSession.SendResponseAsync("221 Goodbye");
          break;
        case "NOOP":
          await _siemensImplantSession.SendResponseAsync("200 NOOP command successful");
          break;
        case "ALLO":
          await HandleAlloAsync(parameter);
          break;
        case "SIZE":
          await HandleSizeAsync(parameter);
          break;
        default:
          await _siemensImplantSession.SendResponseAsync($"502 Command '{command}' not implemented");
          break;
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理FTP命令时出错");
      await _siemensImplantSession.SendResponseAsync("500 Error processing command");
    }
  }

  /// <summary>
  /// 处理ALLO命令，用于预分配存储空间（在现代FTP服务器中通常是冗余的）
  /// </summary>
  /// <param name="parameter">命令参数</param>
  private async Task HandleAlloAsync(string parameter)
  {
    await _siemensImplantSession.SendResponseAsync("202 The command has not been implemented yet and is redundant on this site");
  }

  /// <summary>
  /// 处理SIZE命令，用于获取文件大小
  /// </summary>
  /// <param name="parameter">命令参数（文件路径）</param>
  private async Task HandleSizeAsync(string parameter)
  {
    if (!_siemensImplantSession.SiemensImplantUser.IsAuthenticated)
    {
      await _siemensImplantSession.SendResponseAsync("530 Not logged in");
      return;
    }

    // 处理可能的前导斜杠
    if (parameter.StartsWith("/"))
    {
      parameter = parameter.TrimStart('/');
    }

    // 构建完整的文件路径
    string filePath = ResolvePath(parameter);

    // 检查文件是否存在并且允许访问
    if (File.Exists(filePath) && IsPathAllowed(filePath))
    {
      // 获取文件信息
      FileInfo info = new FileInfo(filePath);
      // 返回文件大小
      await _siemensImplantSession.SendResponseAsync($"213 {info.Length}");
    }
    else
    {
      // 文件不存在或不允许访问
      await _siemensImplantSession.SendResponseAsync("550 Not Found");
    }
  }

  /// <summary>
  /// 处理USER命令
  /// </summary>
  private async Task HandleUserAsync(string username)
  {
    _siemensImplantSession.SiemensImplantUser.Username = username;
    _siemensImplantSession.SiemensImplantUser.LoginState = 1; // 等待密码

    await _siemensImplantSession.SendResponseAsync($"331 Password required for {username}");
  }

  /// <summary>
  /// 处理PASS命令
  /// </summary>
  private async Task HandlePassAsync(string password)
  {
    if (_siemensImplantSession.SiemensImplantUser.LoginState != 1)
    {
      await _siemensImplantSession.SendResponseAsync("503 Login with USER first");
      return;
    }

    var authenticated = await AuthenticateUserAsync(_siemensImplantSession.SiemensImplantUser.Username, password);
    if (authenticated)
    {
      // 创建用户目录
      CreateUserDirectories(_siemensImplantSession.SiemensImplantUser);

      await _siemensImplantSession.SendResponseAsync("230 SiemensImplantUser logged in, proceed");
    }
    else
    {
      await _siemensImplantSession.SendResponseAsync("530 Login incorrect");
    }
  }

  /// <summary>
  /// 处理FEAT命令
  /// </summary>
  private async Task HandleFeatAsync()
  {
    await _siemensImplantSession.SendResponseAsync("211- Features:");
    await _siemensImplantSession.SendResponseAsync(" UTF8");
    await _siemensImplantSession.SendResponseAsync(" PASV");
    await _siemensImplantSession.SendResponseAsync(" SIZE");
    await _siemensImplantSession.SendResponseAsync(" TYPE A;I");
    await _siemensImplantSession.SendResponseAsync("211 End");
  }

  /// <summary>
  /// 处理PWD命令
  /// </summary>
  private async Task HandlePwdAsync()
  {
    var relativePath = GetRelativePath(_siemensImplantSession.SiemensImplantUser.CurrentDirectory);
    await _siemensImplantSession.SendResponseAsync($"257 \"{relativePath}\" is current directory");
  }

  /// <summary>
  /// 处理CWD命令
  /// </summary>
  private async Task HandleCwdAsync(string path)
  {
    if (!_siemensImplantSession.SiemensImplantUser.IsAuthenticated)
    {
      await _siemensImplantSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var targetPath = ResolvePath(path);
    if (Directory.Exists(targetPath) && IsPathAllowed(targetPath))
    {
      _siemensImplantSession.SiemensImplantUser.CurrentDirectory = targetPath;
      await _siemensImplantSession.SendResponseAsync($"250 Directory changed to \"{GetRelativePath(targetPath)}\"");
    }
    else
    {
      await _siemensImplantSession.SendResponseAsync($"550 Directory not found: {path}");
    }
  }

  /// <summary>
  /// 处理CDUP命令
  /// </summary>
  private async Task HandleCdupAsync()
  {
    if (!_siemensImplantSession.SiemensImplantUser.IsAuthenticated)
    {
      await _siemensImplantSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var parent = Directory.GetParent(_siemensImplantSession.SiemensImplantUser.CurrentDirectory);
    if (parent != null && IsPathAllowed(parent.FullName))
    {
      _siemensImplantSession.SiemensImplantUser.CurrentDirectory = parent.FullName;
      await _siemensImplantSession.SendResponseAsync($"250 Directory changed to \"{GetRelativePath(_siemensImplantSession.SiemensImplantUser.CurrentDirectory)}\"");
    }
    else
    {
      await _siemensImplantSession.SendResponseAsync("550 Cannot go above siemensImplantUser root directory");
    }
  }

  /// <summary>
  /// 处理TYPE命令
  /// </summary>
  private async Task HandleTypeAsync(string type)
  {
    switch (type)
    {
      case "A":
        _siemensImplantSession.SiemensImplantUser.TransferType = "A";
        await _siemensImplantSession.SendResponseAsync("200 Type set to A");
        break;
      case "I":
        _siemensImplantSession.SiemensImplantUser.TransferType = "I";
        await _siemensImplantSession.SendResponseAsync("200 Type set to I");
        break;
      default:
        await _siemensImplantSession.SendResponseAsync($"504 Type {type} not implemented");
        break;
    }
  }

  /// <summary>
  /// 处理PASV命令
  /// </summary>
  private async Task HandlePasvAsync()
  {
    if (!_siemensImplantSession.SiemensImplantUser.IsAuthenticated)
    {
      await _siemensImplantSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var localEndpoint = (IPEndPoint)_siemensImplantSession.Client.Client.LocalEndPoint;

    // 创建被动监听器
    _siemensImplantSession.SiemensImplantUser.DataListener?.Stop();
    _siemensImplantSession.SiemensImplantUser.DataListener = new TcpListener(localEndpoint.Address, 0);
    _siemensImplantSession.SiemensImplantUser.DataListener.Start();

    var pasvEndpoint = (IPEndPoint)_siemensImplantSession.SiemensImplantUser.DataListener.LocalEndpoint;
    byte[] ip = localEndpoint.Address.GetAddressBytes();
    int port = pasvEndpoint.Port;

    int portHigh = port / 256;
    int portLow = port % 256;

    _siemensImplantSession.SiemensImplantUser.IsPassiveMode = true;

    await _siemensImplantSession.SendResponseAsync($"227 Entering Passive Mode ({ip[0]},{ip[1]},{ip[2]},{ip[3]},{portHigh},{portLow})");
  }

  /// <summary>
  /// 处理PORT命令
  /// </summary>
  private async Task HandlePortAsync(string parameter)
  {
    if (!_siemensImplantSession.SiemensImplantUser.IsAuthenticated)
    {
      await _siemensImplantSession.SendResponseAsync("530 Not logged in");
      return;
    }

    try
    {
      var parts = parameter.Split(',').Select(int.Parse).ToArray();
      if (parts.Length != 6)
      {
        await _siemensImplantSession.SendResponseAsync("501 Invalid PORT command");
        return;
      }

      string ip = $"{parts[0]}.{parts[1]}.{parts[2]}.{parts[3]}";
      int port = (parts[4] * 256) + parts[5];

      _siemensImplantSession.SiemensImplantUser.DataEndPoint = new IPEndPoint(IPAddress.Parse(ip), port);
      _siemensImplantSession.SiemensImplantUser.IsPassiveMode = false;

      await _siemensImplantSession.SendResponseAsync("200 PORT command successful");
    }
    catch
    {
      await _siemensImplantSession.SendResponseAsync("501 Invalid PORT command");
    }
  }

  /// <summary>
  /// 处理LIST和NLST命令
  /// </summary>
  private async Task HandleListAsync(string path, bool isList)
  {
    if (!_siemensImplantSession.SiemensImplantUser.IsAuthenticated)
    {
      await _siemensImplantSession.SendResponseAsync("530 Not logged in");
      return;
    }

    string targetPath = string.IsNullOrWhiteSpace(path) ?
        _siemensImplantSession.SiemensImplantUser.CurrentDirectory : ResolvePath(path);

    if (!Directory.Exists(targetPath) || !IsPathAllowed(targetPath))
    {
      await _siemensImplantSession.SendResponseAsync($"550 Directory not found: {path}");
      return;
    }

    try
    {
      await _siemensImplantSession.SendResponseAsync("150 Opening ASCII data connection");

      using var dataClient = _siemensImplantSession.CreateDataConnection();
      if (dataClient == null)
      {
        await _siemensImplantSession.SendResponseAsync("425 Can't open data connection");
        return;
      }

      // 记录使用的模式（主动或被动）
      _logger.LogInformation("采用{Mode}模式向用户发送{Command}目录和文件列表",
          _siemensImplantSession.SiemensImplantUser.IsPassiveMode ? "被动" : "主动",
          isList ? "LIST" : "NLST");

      using var dataStream = dataClient.GetStream();
      using var writer = new StreamWriter(dataStream, Encoding.ASCII);

      // 获取目录列表
      var listing = GenerateDirectoryListing(targetPath, isList);
      // NLST命令 - 简单格式，直接记录文件名
      _logger.LogInformation("向用户发送(字符串信息)：[{Content}]", listing);

      // 发送目录列表
      await writer.WriteAsync(listing);
      await writer.FlushAsync();

      // 记录发送完毕
      _logger.LogInformation("发送完毕");

      await _siemensImplantSession.SendResponseAsync("226 Transfer complete");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理LIST命令时出错: {Path}", path);
      await _siemensImplantSession.SendResponseAsync("550 Failed to get directory listing");
    }
  }

  /// <summary>
  /// 处理RETR命令
  /// </summary>
  private async Task HandleRetrAsync(string path)
  {
    if (!_siemensImplantSession.SiemensImplantUser.IsAuthenticated)
    {
      await _siemensImplantSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var filePath = ResolvePath(path);
    if (!File.Exists(filePath) || !IsPathAllowed(filePath))
    {
      await _siemensImplantSession.SendResponseAsync($"550 Files not found: {path}");
      return;
    }

    try
    {
      using var dataClient = _siemensImplantSession.CreateDataConnection();
      if (dataClient == null)
      {
        await _siemensImplantSession.SendResponseAsync("425 Can't open data connection");
        return;
      }

      _logger.LogInformation("向用户发送(文件流)：[...");

      var isBinary = _siemensImplantSession.SiemensImplantUser.TransferType == "I";
      if (isBinary)
      {
        await _siemensImplantSession.SendResponseAsync($"150 Opening BINARY mode data connection for download");
      }
      else
      {
        await _siemensImplantSession.SendResponseAsync($"150 Opening ASCII mode data connection for download");
      }
      // 发送文件
      SendFileToClient(dataClient, isBinary, null);
      _logger.LogInformation("...]发送完毕！");
      await _siemensImplantSession.SendResponseAsync("226 Transfer complete");

      try
      {
        // 获取文件名
        var fileName = Path.GetFileName(filePath);
        // 转换为西门子格式文件名
        var remoteFileName = "_N_" + fileName.ToUpper().Replace('.', '_');
        // 连接SCP服务器
        if (!_scpClient.IsConnected)
        {
          _scpClient.Connect();
        }
        _logger.LogInformation($"连接SCP服务器状态：{_scpClient.IsConnected}");
        if (!_scpClient.IsConnected)
        {
          return;
        }
        var config = _plugin.GetConfig();
        var remotePath = config.ScpWorkPath + "/" + remoteFileName;
        _logger.LogInformation("SCP上传文件路径：{RemotePath} ,filePath:{FilePath}", remotePath, filePath);
        _scpClient.Upload(new FileInfo(filePath), remotePath);

        // 断开连接
        _scpClient.Disconnect();

        _logger.LogInformation("SCP上传文件完成：{FilePath} 到 {RemotePath}", filePath, remotePath);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "SCP上传文件失败：{FilePath}", filePath);
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理RETR命令时出错: {Path}", path);
      await _siemensImplantSession.SendResponseAsync("550 Failed to send file");
    }
  }

  /// <summary>
  /// 向客户端发送文件流
  /// </summary>
  /// <param name="dataClient">数据连接客户端</param>
  /// <param name="fileStream">文件流</param>
  /// <param name="isBinary">是否为二进制模式</param>
  private void SendFileToClient(TcpClient dataClient, bool isBinary, FileStream? fileStream)
  {
    _logger.LogInformation("[SendFileToClient] 开始向客户端发送文件流");
    _logger.LogInformation($"[SendFileToClient] 传输模式: {(isBinary ? "二进制" : "ASCII")}, 文件流: {(fileStream != null ? "存在" : "空")}");

    try
    {
      using var networkStream = dataClient.GetStream();

      // 二进制模式传输
      if (isBinary)
      {
        _logger.LogInformation("[SendFileToClient] 进入二进制传输模式");

        if (fileStream != null)
        {
          _logger.LogInformation($"[SendFileToClient] 开始读取文件流，总长度: {fileStream.Length} 字节");
          byte[] buffer = new byte[1024];
          int totalRead = 0;
          int chunkCounter = 0;

          using var binaryReader = new BinaryReader(fileStream);
          using var binaryWriter = new BinaryWriter(networkStream);

          int bytesRead;
          while ((bytesRead = binaryReader.Read(buffer, 0, buffer.Length)) > 0)
          {
            chunkCounter++;
            totalRead += bytesRead;

            _logger.LogInformation($"[SendFileToClient] 读取第 {chunkCounter} 块数据，本次 {bytesRead} 字节，累计 {totalRead}/{fileStream.Length} 字节");

            binaryWriter.Write(buffer, 0, bytesRead);
            binaryWriter.Flush();

            _logger.LogDebug($"[SendFileToClient] 已发送 {bytesRead} 字节数据");
          }

          _logger.LogInformation($"[SendFileToClient] 文件流读取完成，共发送 {chunkCounter} 个数据块，总计 {totalRead} 字节");
        }
        else
        {
          _logger.LogWarning("[SendFileToClient] 二进制模式下检测到空文件流，执行空数据刷新");
          using var binaryWriter = new BinaryWriter(networkStream);
          binaryWriter.Flush();
        }
      }
      // ASCII模式传输
      else
      {
        _logger.LogInformation("[SendFileToClient] 进入ASCII传输模式");

        if (fileStream != null)
        {
          _logger.LogInformation($"[SendFileToClient] 开始读取文本流，总长度: {fileStream.Length} 字节");
          int lineCounter = 0;

          using var streamReader = new StreamReader(fileStream);
          using var streamWriter = new StreamWriter(networkStream) { AutoFlush = true };

          string line;
          while ((line = streamReader.ReadLine()) != null)
          {
            lineCounter++;
            _logger.LogInformation($"[SendFileToClient] 读取第 {lineCounter} 行，长度: {line.Length} 字符");

            streamWriter.WriteLine(line);
            _logger.LogDebug($"[SendFileToClient] 已发送第 {lineCounter} 行数据");
          }

          _logger.LogInformation($"[SendFileToClient] 文本流读取完成，共发送 {lineCounter} 行");
        }
        else
        {
          _logger.LogWarning("[SendFileToClient] ASCII模式下检测到空文件流，发送空行");
          using var streamWriter = new StreamWriter(networkStream) { AutoFlush = true };
          streamWriter.WriteLine("");
        }
      }

      _logger.LogInformation("[SendFileToClient] 文件流发送操作完成");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "[SendFileToClient] 发送文件流时发生异常");
      throw;
    }
    finally
    {
      _logger.LogInformation("[SendFileToClient] 资源清理开始");
      // 数据连接会在调用方通过using语句关闭
      _logger.LogInformation("[SendFileToClient] 资源清理完成");
    }
  }

  /// <summary>
  /// 处理STOR命令
  /// </summary>
  private async Task HandleStorAsync(string path)
  {
    if (!_siemensImplantSession.SiemensImplantUser.IsAuthenticated)
    {
      await _siemensImplantSession.SendResponseAsync("530 Not logged in");
      return;
    }

    // 确定上传的目标文件名
    string filename = Path.GetFileName(path);
    if (string.IsNullOrWhiteSpace(filename))
    {
      await _siemensImplantSession.SendResponseAsync("553 Invalid file name");
      return;
    }

    // 确定目标目录（改为REC子目录）
    string targetDir = Path.Combine(_siemensImplantSession.SiemensImplantUser.WorkDirectory, "REC");
    if (!Directory.Exists(targetDir))
    {
      // 确保REC目录存在
      try
      {
        Directory.CreateDirectory(targetDir);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "创建REC目录时出错");
        await _siemensImplantSession.SendResponseAsync("550 Directory not accessible");
        return;
      }
    }

    // 检查目录是否允许访问
    if (!IsPathAllowed(targetDir))
    {
      await _siemensImplantSession.SendResponseAsync("550 Directory not accessible");
      return;
    }

    // 构建完整的文件路径
    string filePath = Path.Combine(targetDir, filename);

    try
    {
      await _siemensImplantSession.SendResponseAsync($"150 Opening data connection for {filename}");

      using var dataClient = _siemensImplantSession.CreateDataConnection();
      if (dataClient == null)
      {
        await _siemensImplantSession.SendResponseAsync("425 Can't open data connection");
        return;
      }

      using var dataStream = dataClient.GetStream();
      using var fileStream = File.Create(filePath);

      // 接收文件
      await dataStream.CopyToAsync(fileStream);

      await _siemensImplantSession.SendResponseAsync("226 Transfer complete");

      _logger.LogInformation("文件上传成功: {FilePath}", filePath);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理STOR命令时出错: {Path}", path);
      await _siemensImplantSession.SendResponseAsync("550 Failed to store file");
    }
  }

  /// <summary>
  /// 处理DELE命令
  /// </summary>
  private async Task HandleDeleAsync(string path)
  {
    if (!_siemensImplantSession.SiemensImplantUser.IsAuthenticated)
    {
      await _siemensImplantSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var filePath = ResolvePath(path);
    if (!File.Exists(filePath) || !IsPathAllowed(filePath))
    {
      await _siemensImplantSession.SendResponseAsync($"550 Files not found: {path}");
      return;
    }

    try
    {
      File.Delete(filePath);
      await _siemensImplantSession.SendResponseAsync("250 Files deleted");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理DELE命令时出错: {Path}", path);
      await _siemensImplantSession.SendResponseAsync("550 Failed to delete file");
    }
  }

  /// <summary>
  /// 处理MKD命令
  /// </summary>
  private async Task HandleMkdAsync(string path)
  {
    if (!_siemensImplantSession.SiemensImplantUser.IsAuthenticated)
    {
      await _siemensImplantSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var dirPath = ResolvePath(path);
    if (!IsPathAllowed(dirPath))
    {
      await _siemensImplantSession.SendResponseAsync("550 Permission denied");
      return;
    }

    try
    {
      Directory.CreateDirectory(dirPath);
      await _siemensImplantSession.SendResponseAsync($"257 \"{path}\" created");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理MKD命令时出错: {Path}", path);
      await _siemensImplantSession.SendResponseAsync("550 Failed to create directory");
    }
  }

  /// <summary>
  /// 处理RMD命令
  /// </summary>
  private async Task HandleRmdAsync(string path)
  {
    if (!_siemensImplantSession.SiemensImplantUser.IsAuthenticated)
    {
      await _siemensImplantSession.SendResponseAsync("530 Not logged in");
      return;
    }

    var dirPath = ResolvePath(path);
    if (!Directory.Exists(dirPath) || !IsPathAllowed(dirPath))
    {
      await _siemensImplantSession.SendResponseAsync($"550 Directory not found: {path}");
      return;
    }

    try
    {
      Directory.Delete(dirPath, false);
      await _siemensImplantSession.SendResponseAsync("250 Directory removed");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理RMD命令时出错: {Path}", path);
      await _siemensImplantSession.SendResponseAsync("550 Failed to remove directory");
    }
  }

  /// <summary>
  /// 验证用户身份
  /// </summary>
  private async Task<bool> AuthenticateUserAsync(string username, string password)
  {
    try
    {
      // 通过DNC配置验证
      var dncConfigs = await _plugin.GetDncConfigsAsync();
      if (dncConfigs == null || !dncConfigs.Any())
      {
        _logger.LogWarning("没有可用的DNC配置");
        return false;
      }

      var matchedConfig = dncConfigs.FirstOrDefault(c =>
          c.Enabled &&
          c.Username == username &&
          c.Password == password);

      if (matchedConfig != null)
      {
        _logger.LogInformation("用户 {Username} 验证成功, 设备: {DeviceCode}",
            username, matchedConfig.DeviceCode);

        _siemensImplantSession.SiemensImplantUser.IsAuthenticated = true;
        _siemensImplantSession.SiemensImplantUser.LoginState = 2;
        _siemensImplantSession.SiemensImplantUser.DeviceCode = matchedConfig.DeviceCode;
        _siemensImplantSession.SiemensImplantUser.Role = string.IsNullOrEmpty(matchedConfig.AccessPermission) ?
            "siemensImplantUser" : matchedConfig.AccessPermission;

        return true;
      }

      return false;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "验证用户时出错");
      return false;
    }
  }

  /// <summary>
  /// 创建用户目录
  /// </summary>
  private void CreateUserDirectories(FtpSiemensImplantUser siemensImplantUser)
  {
    try
    {
      // 确定用户标识符
      string userIdentifier = GetUserIdentifier(siemensImplantUser);

      // 设置用户工作目录
      siemensImplantUser.WorkDirectory = Path.Combine(AppContext.BaseDirectory, "RootFiles", userIdentifier);
      siemensImplantUser.CurrentDirectory = siemensImplantUser.WorkDirectory;

      // 确保目录存在
      Directory.CreateDirectory(siemensImplantUser.WorkDirectory);

      // 创建SEND和REC子目录
      string sendPath = Path.Combine(siemensImplantUser.WorkDirectory, "SEND");
      string recPath = Path.Combine(siemensImplantUser.WorkDirectory, "REC");

      Directory.CreateDirectory(sendPath);
      Directory.CreateDirectory(recPath);

      _logger.LogInformation("用户 {Username} 的目录已创建: {WorkDir}",
          siemensImplantUser.Username, siemensImplantUser.WorkDirectory);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "创建用户目录时出错");
    }
  }

  /// <summary>
  /// 获取用户标识符
  /// </summary>
  private string GetUserIdentifier(FtpSiemensImplantUser siemensImplantUser)
  {
    if (!string.IsNullOrEmpty(siemensImplantUser.DeviceCode))
      return siemensImplantUser.DeviceCode;

    if (!string.IsNullOrEmpty(siemensImplantUser.Username) && siemensImplantUser.Username != "USER")
      return siemensImplantUser.Username;

    return "USER";
  }

  /// <summary>
  /// 解析路径
  /// </summary>
  private string ResolvePath(string path)
  {
    if (string.IsNullOrWhiteSpace(path))
      return _siemensImplantSession.SiemensImplantUser.CurrentDirectory;

    string targetPath;
    // 处理根路径
    if (path.StartsWith("/"))
      targetPath = Path.Combine(_siemensImplantSession.SiemensImplantUser.WorkDirectory, path.TrimStart('/'));
    else
      // 处理相对路径
      targetPath = Path.Combine(_siemensImplantSession.SiemensImplantUser.CurrentDirectory, path);

    // 规范化路径
    targetPath = Path.GetFullPath(targetPath);

    return targetPath;
  }

  /// <summary>
  /// 获取相对路径
  /// </summary>
  private string GetRelativePath(string path)
  {
    // 规范化路径
    path = Path.GetFullPath(path);
    var workDir = Path.GetFullPath(_siemensImplantSession.SiemensImplantUser.WorkDirectory);

    if (path.Equals(workDir, StringComparison.OrdinalIgnoreCase))
      return "/";

    if (path.StartsWith(workDir, StringComparison.OrdinalIgnoreCase))
    {
      var relativePath = path.Substring(workDir.Length)
          .Replace('\\', '/');

      if (!relativePath.StartsWith("/"))
        relativePath = "/" + relativePath;

      return relativePath;
    }

    return path;
  }

  /// <summary>
  /// 检查路径是否允许访问
  /// </summary>
  private bool IsPathAllowed(string path)
  {
    var normalized = Path.GetFullPath(path);
    var workDir = Path.GetFullPath(_siemensImplantSession.SiemensImplantUser.WorkDirectory);

    return normalized.StartsWith(workDir, StringComparison.OrdinalIgnoreCase);
  }

  /// <summary>
  /// 生成目录列表字符串
  /// </summary>
  private string GenerateDirectoryListing(string directoryPath, bool isList)
  {
    if (isList)
    {
      var listing = new StringBuilder();
      var culture = CultureInfo.CreateSpecificCulture("en-GB");

      // 目录项
      foreach (var dir in Directory.GetDirectories(directoryPath))
      {
        var info = new DirectoryInfo(dir);
        listing.AppendFormat(CultureInfo.InvariantCulture,
            "drwx------ 1 <USER> <GROUP>       {0} {1} {2}\r\n",
            "0",
            info.CreationTime.ToString("MMM dd HH:mm", culture),
            info.Name);
      }

      // 文件项
      foreach (var file in Directory.GetFiles(directoryPath))
      {
        var info = new FileInfo(file);
        listing.AppendFormat(CultureInfo.InvariantCulture,
            "-rwx------ 1 <USER> <GROUP>       {0} {1} {2}\r\n",
            info.Length,
            info.CreationTime.ToString("MMM dd HH:mm", culture),
            info.Name);
      }

      return listing.ToString();
    }
    else
    {
      var listing = new StringBuilder();
      var entries = Directory.GetFileSystemEntries(directoryPath)
          .Select(Path.GetFileName)
          .OrderBy(name => name);

      foreach (var name in entries)
      {
        listing.Append(name + "\r\n");
      }

      return listing.ToString();
    }
  }

  /// <summary>
  /// 释放资源
  /// </summary>
  public void Dispose()
  {
    Dispose(true);
    GC.SuppressFinalize(this);
  }

  /// <summary>
  /// 释放资源
  /// </summary>
  /// <param name="disposing">是否释放托管资源</param>
  protected virtual void Dispose(bool disposing)
  {
    if (!_disposed)
    {
      if (disposing)
      {
        // 释放托管资源
        if (_scpClient != null)
        {
          try
          {
            if (_scpClient.IsConnected)
            {
              _scpClient.Disconnect();
            }
            _scpClient.Dispose();
            _logger.LogDebug("SCP客户端资源已释放");
          }
          catch (Exception ex)
          {
            _logger.LogError(ex, "释放SCP客户端资源时出错");
          }
        }
      }

      // 释放非托管资源

      _disposed = true;
    }
  }

  /// <summary>
  /// 终结器
  /// </summary>
  ~FtpSiemensImplantCommandHandler()
  {
    Dispose(false);
  }
}

