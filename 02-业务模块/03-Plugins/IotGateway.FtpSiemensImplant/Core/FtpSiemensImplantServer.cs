using System;
using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using IotGateway.FtpSiemensImplant.Models;
using Microsoft.Extensions.Logging;

namespace IotGateway.FtpSiemensImplant.Core;

/// <summary>
/// FTP服务器实现
/// </summary>
public class FtpSiemensImplantServer : IDisposable
{
  private readonly FtpSiemensImplantServerPlugin _plugin;
  private readonly FtpSiemensImplantConfig _siemensImplantConfig;
  private readonly ILogger _logger;
  private TcpListener? _listener;
  private CancellationTokenSource? _cancellationTokenSource;
  private readonly ConcurrentDictionary<string, FtpSiemensImplantSession> _sessions = new();

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="plugin">FTP服务器插件</param>
  /// <param name="siemensImplantConfig">FTP配置</param>
  /// <param name="logger">日志记录器</param>
  public FtpSiemensImplantServer(FtpSiemensImplantServerPlugin plugin, FtpSiemensImplantConfig siemensImplantConfig, ILogger logger)
  {
    _plugin = plugin;
    _siemensImplantConfig = siemensImplantConfig;
    _logger = logger;
  }

  /// <summary>
  /// 启动FTP服务器
  /// </summary>
  public async Task StartAsync()
  {
    try
    {
      _cancellationTokenSource = new CancellationTokenSource();
      _listener = new TcpListener(IPAddress.Any, _siemensImplantConfig.Port);
      _listener.Start();

      _logger.LogInformation("FTP服务器启动在端口 {Port}", _siemensImplantConfig.Port);

      // 开始接受客户端连接
      _ = AcceptClientsAsync(_cancellationTokenSource.Token);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "启动FTP服务器时出错");
      throw;
    }
  }

  /// <summary>
  /// 停止FTP服务器
  /// </summary>
  public async Task StopAsync()
  {
    try
    {
      // 取消所有操作
      _cancellationTokenSource?.Cancel();

      // 关闭监听器
      _listener?.Stop();

      // 关闭所有会话
      foreach (var session in _sessions.Values)
      {
        try
        {
          session.Dispose();
        }
        catch (Exception ex)
        {
          _logger.LogWarning(ex, "关闭会话时出错");
        }
      }

      _sessions.Clear();

      _logger.LogInformation("FTP服务器已停止");

      await Task.CompletedTask;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "停止FTP服务器时出错");
    }
  }

  /// <summary>
  /// 接受客户端连接
  /// </summary>
  private async Task AcceptClientsAsync(CancellationToken cancellationToken)
  {
    try
    {
      while (!cancellationToken.IsCancellationRequested)
      {
        try
        {
          // 接受客户端连接
          var client = await _listener.AcceptTcpClientAsync();
          var clientEndPoint = client.Client.RemoteEndPoint?.ToString() ?? "Unknown";

          _logger.LogInformation("客户端已连接: {ClientEndPoint}", clientEndPoint);

          // 创建会话
          var session = new FtpSiemensImplantSession(client, _logger);
          _sessions.TryAdd(clientEndPoint, session);

          // 处理客户端
          _ = HandleClientAsync(session, clientEndPoint, cancellationToken);
        }
        catch (Exception ex) when (cancellationToken.IsCancellationRequested)
        {
          // 正常取消
          break;
        }
        catch (Exception ex)
        {
          _logger.LogError(ex, "接受客户端连接时出错");
        }
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "接受客户端连接循环出错");
    }
  }

  /// <summary>
  /// 处理客户端连接
  /// </summary>
  private async Task HandleClientAsync(FtpSiemensImplantSession siemensImplantSession, string clientEndPoint, CancellationToken cancellationToken)
  {
    try
    {
      // 发送欢迎消息
      await siemensImplantSession.SendResponseAsync("220 FTP Server v1.0");

      // 创建命令处理器
      var commandHandler = new FtpSiemensImplantCommandHandler(siemensImplantSession, _plugin, _logger);

      // 命令处理循环
      string? line;
      while (!cancellationToken.IsCancellationRequested &&
             (line = await siemensImplantSession.Reader.ReadLineAsync()) != null)
      {
        // 处理命令
        await commandHandler.HandleCommandAsync(line);

        // 检查是否是QUIT命令
        if (line.StartsWith("QUIT", StringComparison.OrdinalIgnoreCase))
          break;
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理客户端 {ClientEndPoint} 时出错", clientEndPoint);
    }
    finally
    {
      try
      {
        siemensImplantSession.Dispose();
      }
      catch (Exception ex)
      {
        _logger.LogWarning(ex, "关闭会话时出错");
      }

      _sessions.TryRemove(clientEndPoint, out _);
      _logger.LogInformation("客户端 {ClientEndPoint} 已断开连接", clientEndPoint);
    }
  }

  /// <summary>
  /// 获取活动会话数
  /// </summary>
  public int GetActiveSessionCount()
  {
    return _sessions.Count;
  }

  /// <summary>
  /// 释放资源
  /// </summary>
  public void Dispose()
  {
    _listener?.Stop();
    _cancellationTokenSource?.Cancel();
    _cancellationTokenSource?.Dispose();

    foreach (var session in _sessions.Values)
    {
      try
      {
        session.Dispose();
      }
      catch
      {
        // 忽略错误
      }
    }

    _sessions.Clear();

    GC.SuppressFinalize(this);
  }
}