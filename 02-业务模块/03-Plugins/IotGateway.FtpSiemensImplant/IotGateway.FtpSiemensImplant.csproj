<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <OutputPath>..\..\..\01-应用服务\IotGateway/bin/$(Configuration)/$(TargetFramework)/plugins/services/ftpsiemensImplant/</OutputPath>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\IotGateway.Plugin.Core\IotGateway.Plugin.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="Renci.SshNet">
        <HintPath>C:\Users\<USER>\Desktop\项目依赖包\西门子植入式\Renci.SshNet.dll</HintPath>
      </Reference>
      <Reference Include="SshNet.Security.Cryptography">
        <HintPath>C:\Users\<USER>\Desktop\项目依赖包\西门子植入式\SshNet.Security.Cryptography.dll</HintPath>
      </Reference>
    </ItemGroup>

<!--    <Target Name="CopyDependencies" AfterTargets="Build">-->
<!--        <ItemGroup>-->
<!--            <SSHNETFiles Include="$(NuGetPackageRoot)SSH.NET\2020.0.2\lib\*\*.dll"/>-->
<!--            <SshNetSecurityCryptographyFiles Include="$(NuGetPackageRoot)SshNet.Security.Cryptography\1.3.0\lib\*\*.dll"/>-->
<!--        </ItemGroup>-->
<!--        <Copy SourceFiles="@(SSHNETFiles)" DestinationFolder="$(OutputPath)"/>-->
<!--        <Copy SourceFiles="@(SshNetSecurityCryptographyFiles)" DestinationFolder="$(OutputPath)"/>-->
<!--    </Target>-->
</Project> 