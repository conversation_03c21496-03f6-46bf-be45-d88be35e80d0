using System.Text;
using Common.Extension;
using Feng.Common.Extension;
using Feng.IotGateway.Core.Models;
using Feng.IotGateway.Core.Util;
using Furion.JsonSerialization;
using Furion.Templates;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.HostApd;

/// <summary>
///     热点服务
/// </summary>
public class HostService
{
    private readonly CancellationTokenSource _tokenSource = new();
    private readonly string _ssid;
    private readonly string _password;

    /// <summary>
    ///     执行记录
    /// </summary>
    /// <remarks>默认只保存 200 条</remarks>
    public Queue<RunRecordLine> ActionRecord { get; set; } = new();
    
    /// <summary>
    ///     初始化
    /// </summary>
    /// <param name="config">热点配置信息</param>
    public HostService(string config)
    {
        var hostConfig = JSON.Deserialize<HostConfig>(config);
        _ssid = hostConfig.Ssid;
        _password = hostConfig.WpaPassphrase;
        Start();
    }

    /// <summary>
    ///     添加执行记录
    /// </summary>
    /// <param name="title"></param>
    /// <param name="message"></param>
    /// <param name="errorMessage">错误消息</param>
    private void AddToActionRecord(string title,string message, string errorMessage = "")
    {
        if (ActionRecord.Count > 200)
            ActionRecord.Dequeue();

        var tmp = TP.Wrapper(title,
            $"##时间## 【{DateTime.NowString()}】",
            $"##消息内容## 【{message}】",
            $"{(errorMessage.IsNotNull() ? "##错误信息##" + "【" + errorMessage + "】" : "")}");
        ActionRecord.Enqueue(new RunRecordLine
        {
            Time = DateTime.ShangHai(),
            Message = tmp
        });
    }

    /// <summary>
    /// 修改配置文件内容
    /// </summary>
    public void Start()
    {
        //第三步
        _ = SaveConfig();
        AddToActionRecord("更新热点配置","SUCCESS");
        
        //第一步
        var action = ShellUtil.Bash("/etc/init.d/S70wifiap_server_ctrl restart").Result;
        //保存日志
        AddToActionRecord("开启热点服务",action);
        
        //第二步
        var wifiApSetting = ShellUtil.Bash("/hogo/sh/wifiapsetting.sh").Result;
        AddToActionRecord("重置网络设置",wifiApSetting ?? "SUCCESS");
        //cat /etc/hostapd.conf
    }

    /// <summary>
    ///     停止线程
    /// </summary>
    public void Stop()
    {
        var action = ShellUtil.Bash("/etc/init.d/S70wifiap_server_ctrl stop").Result;
        //保存日志
        AddToActionRecord("停止热点服务",action);
        
        _tokenSource.Cancel();
    }

    /// <summary>
    ///     修改热点配置内容
    /// </summary>
    /// <returns></returns>
    private async Task SaveConfig()
    {
        var configFile = File.OpenText("/etc/hostapd.conf");

        var builder = new StringBuilder();
        while (!configFile.EndOfStream)
        {
            var line = await configFile.ReadLineAsync();
            if (line != null && line.StartsWith("ssid="))
            {
                line = "ssid=" + (_ssid ?? "test");
            }
            if (line != null && line.StartsWith("wpa_passphrase="))
            {
                line = "wpa_passphrase=" + (_password ?? "12345678");
            }
            
            builder.AppendLine(line);

        }

        configFile.Dispose();
        await File.WriteAllTextAsync("/etc/hostapd.conf", builder.ToString());
    }
    
    /// <summary>
    ///     配置信息
    /// </summary>
    public class HostConfig
    {
        /// <summary>
        /// </summary>
        public string Ssid { get; set; }

        /// <summary>
        /// </summary>
        public string WpaPassphrase { get; set; }
    }
}