using IotGateway.Plugin.Core;

namespace IotGateway.Nfs.Models
{
  /// <summary>
  /// NFS服务器配置
  /// </summary>
  public class NfsConfig : IPluginConfig
  {
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = false;

    /// <summary>
    /// 共享目录路径
    /// </summary>
    public string SharePath { get; set; } = "RootFiles";

    /// <summary>
    /// 获取配置Schema
    /// </summary>
    /// <returns></returns>
    public object GetConfigurationSchema()
    {
      return new
      {
        properties = new
        {
          Enabled = new { type = "boolean", title = "是否启用", description = "是否启用NFS服务" },
          SharePath = new { type = "string", title = "共享目录路径", description = "NFS服务共享目录路径" },
        },
        required = new[] { "Enabled", "SharePath" }
      };
    }
  }
}