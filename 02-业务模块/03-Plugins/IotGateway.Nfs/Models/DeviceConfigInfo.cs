using System.Collections.Generic;

namespace IotGateway.Nfs.Models
{
  /// <summary>
  /// 设备配置信息
  /// </summary>
  public class DeviceConfigInfo
  {
    /// <summary>
    /// 设备ID
    /// </summary>
    public string DeviceId { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string User { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    /// IP地址
    /// </summary>
    public string Ip { get; set; }

    /// <summary>
    /// 工作文件夹
    /// </summary>
    public string WorkFolder { get; set; }

    /// <summary>
    /// 访问权限列表
    /// </summary>
    public List<string> AccessRightList { get; set; }
  }
}