using System;
using System.IO;
using System.Linq;

namespace RPCV2Lib
{
    public static class FileTable
    {
        private static HandleTable _files;

        public static void Initialize(int size) => _files = new HandleTable(size);

        /// <summary>
        /// 标准化路径格式，统一使用平台相关的路径分隔符
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>标准化后的路径</returns>
        private static string NormalizePath(string path)
        {
            if (string.IsNullOrEmpty(path)) return path;

            // 将所有路径分隔符统一为当前平台的分隔符
            string normalizedPath = path.Replace('/', Path.DirectorySeparatorChar)
                                      .Replace('\\', Path.DirectorySeparatorChar);

            // 去除多余的路径分隔符
            while (normalizedPath.Contains(Path.DirectorySeparatorChar.ToString() + Path.DirectorySeparatorChar.ToString()))
            {
                normalizedPath = normalizedPath.Replace(
                    Path.DirectorySeparatorChar.ToString() + Path.DirectorySeparatorChar.ToString(),
                    Path.DirectorySeparatorChar.ToString());
            }

            return normalizedPath;
        }

        public static FileEntry LookupFileEntry(fhandle fh)
        {
            try
            {
                return _files[fh.Index] as FileEntry;
            }
            catch (IndexOutOfRangeException)
            {
                Console.WriteLine($"fileHandles,LookupFileEntry({fh.Index}) failed");
                return null;
            }
        }

        public static fhandle LookupFileHandle(string name)
        {
            string normalizedName = NormalizePath(name);

            for (uint i = 0; i < _files.Length; i++)
            {
                if (_files[i] is FileEntry entry)
                {
                    string normalizedEntryName = NormalizePath(entry.Name);
                    if (normalizedEntryName == normalizedName)
                    {
                        Console.WriteLine($"fileHandles,LookupFileHandle({name}) found as {normalizedName} -> index {i}");
                        return new fhandle(i);
                    }
                }
            }
            Console.WriteLine($"fileHandles,LookupFileHandle({name}) as {normalizedName} failed");
            return null;
        }

        public static fhandle Add(FileEntry file)
        {
            // 确保FileEntry中的路径也是标准化的
            file.Name = NormalizePath(file.Name);
            var handle = new fhandle(_files.Add(file));
            Console.WriteLine($"fileHandles,Add({file.Name}) -> index {handle.Index}");
            return handle;
        }

        public static void Rename(string from, string to)
        {
            var fhFrom = LookupFileHandle(from);
            var fhTo = LookupFileHandle(to);

            if (fhFrom != null)
            {
                if (fhTo == null)
                {
                    LookupFileEntry(fhFrom).Name = NormalizePath(to);
                }
                else
                {
                    Remove(fhTo);
                    LookupFileEntry(fhFrom).Name = NormalizePath(to);
                }
            }
            else if (fhTo == null)
            {
                Add(new FileEntry(to));
            }
        }

        public static void Remove(fhandle fh) => _files.Remove(fh.Index);
    }
}