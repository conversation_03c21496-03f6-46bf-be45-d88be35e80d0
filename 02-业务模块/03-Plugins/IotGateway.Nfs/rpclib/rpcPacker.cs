using System;
using System.Buffers.Binary;
using System.Text;

namespace RPCV2Lib
{
    public sealed class rpcPacker : IDisposable
    {
        private byte[] _buffer;
        private uint _currentOffset;
        private const int InitialSize = 1024;

        public byte[] Data => _buffer;
        public uint Length => _currentOffset;

        public rpcPacker()
        {
            _buffer = new byte[InitialSize];
        }

        public void setUint32(uint value)
        {
            EnsureCapacity(4);
            BinaryPrimitives.WriteUInt32BigEndian(_buffer.AsSpan((int)_currentOffset), value);
            _currentOffset += 4;
        }

        public void SetString(string value)
        {
            if (value == null) throw new ArgumentNullException(nameof(value));

            var byteCount = Encoding.ASCII.GetByteCount(value);
            EnsureCapacity(4 + byteCount + 3); // 预计算最大可能空间

            setUint32((uint)byteCount);
            var bytesWritten = Encoding.ASCII.GetBytes(value, _buffer.AsSpan((int)_currentOffset));
            _currentOffset += (uint)bytesWritten;

            AddPadding(byteCount);
        }

        public void SetData(byte[] buffer, int length)
        {
            if (buffer == null) throw new ArgumentNullException(nameof(buffer));
            if (length < 0 || length > buffer.Length)
                throw new ArgumentOutOfRangeException(nameof(length));

            EnsureCapacity(4 + length + 3);
            setUint32((uint)length);
            
            Buffer.BlockCopy(buffer, 0, _buffer, (int)_currentOffset, length);
            _currentOffset += (uint)length;
            
            AddPadding(length);
        }

        private void AddPadding(int dataLength)
        {
            int padding = (4 - (dataLength % 4)) % 4;
            if (padding == 0) return;

            EnsureCapacity(padding);
            _currentOffset += (uint)padding;
        }

        private void EnsureCapacity(int required)
        {
            if (_currentOffset + required < _buffer.Length) return;

            int newSize = Math.Max(_buffer.Length * 2, (int)(_currentOffset + required));
            Array.Resize(ref _buffer, newSize);
        }

        public static uint CalculateStringSize(string value)
        {
            if (value == null) return 4;
            
            int byteCount = Encoding.ASCII.GetByteCount(value);
            int padding = (4 - (byteCount % 4)) % 4;
            return (uint)(4 + byteCount + padding);
        }

        public void Dump(string header)
        {
            // 保持原始 dump 逻辑，假设 rpcCracker 已优化
            rpcCracker.DumpInternal(header, _buffer, (int)_currentOffset);
        }

        public void Dispose()
        {
            Array.Clear(_buffer, 0, _buffer.Length);
            _currentOffset = 0;
        }
    }
}