using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;

namespace RPCV2Lib
{
    public abstract class rpcd : IDisposable
    {
        private readonly UdpClient _udpClient;
        private readonly uint _programId;
        private static uint _requestCount;

        protected abstract void ProcessProcedure(uint proc, rpcCracker cracker, rpcPacker reply, IPEndPoint remoteEP);
        
        protected virtual bool AcceptProgram(uint program) => false;

        protected rpcd(Ports port, Progs program)
        {
            _programId = (uint)program;
            _udpClient = new UdpClient(new IPEndPoint(IPAddress.Any, (int)port));
        }

        public void Run()
        {
            while (true)
            {
                try
                {
                    var remoteEP = new IPEndPoint(IPAddress.Any, 0);
                    byte[] data = _udpClient.Receive(ref remoteEP);
                    
                    Console.WriteLine($"{_programId}: Received connection from: {remoteEP}");
                    
                    var cracker = new rpcCracker(data);
                    using var reply = ProcessRpcRequest(cracker, remoteEP);
                    
                    Console.WriteLine($"{_programId}: Sending reply to: {remoteEP}");
                    
                    int sent = _udpClient.Send(reply.Data, (int)reply.Length, remoteEP);
                    if (sent != reply.Length)
                    {
                        Console.WriteLine($"*** Partial send. Length:{reply.Length}, Sent:{sent}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"UDP processing error: {ex.Message}");
                }
            }
        }

        private rpcPacker ProcessRpcRequest(rpcCracker cracker, IPEndPoint remoteEP)
        {
            var xid = cracker.get_uint32();
            var messageType = cracker.get_uint32();
            var rpcVersion = cracker.get_uint32();
            var program = cracker.get_uint32();
            var version = cracker.get_uint32();
            var procedure = cracker.get_uint32();

            LogRequestDetails(++_requestCount, xid, messageType, rpcVersion, program, version, procedure, remoteEP);

            if (messageType > 0) return GenerateGarbageReply(xid);
            if (rpcVersion != 2) return GenerateRpcMismatchReply(xid);
            if (_programId != program && !AcceptProgram(program)) return GenerateProgramMismatchReply(xid);

            SkipAuthenticationData(cracker);

            try
            {
                var reply = CreateSuccessReply(xid);
                if (procedure > 0) ProcessProcedure(procedure, cracker, reply, remoteEP);
                return reply;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Procedure error: {ex.Message}, Remote: {remoteEP}");
                return GenerateProcedureUnavailableReply(xid);
            }
        }

        private void LogRequestDetails(uint count, uint xid, uint type, uint rpcVersion, 
            uint program, uint version, uint procedure, IPEndPoint remoteEP)
        {
            Console.WriteLine($"{count} > {_programId}: " +
                     $"xid:{xid}, type:{type}, rpcvers:{rpcVersion}, " +
                     $"prog:{program}, vers:{version}, proc:{procedure}, " +
                     $"remote:{remoteEP}");
        }

        private void SkipAuthenticationData(rpcCracker cracker)
        {
            // Skip credentials and verifier
            SkipAuthSection(cracker);
            SkipAuthSection(cracker);
        }

        private static void SkipAuthSection(rpcCracker cracker)
        {
            cracker.get_uint32(); // flavor
            cracker.jump(cracker.get_uint32()); // auth data
        }

        #region Reply Generation
        private rpcPacker CreateSuccessReply(uint xid) => CreateAcceptReply(xid, 0);
        
        private rpcPacker GenerateProgramMismatchReply(uint xid)
        {
            var reply = CreateAcceptReply(xid, 2);
            reply.setUint32(_programId);
            reply.setUint32(_programId);
            return reply;
        }

        private rpcPacker GenerateProcedureUnavailableReply(uint xid) => CreateAcceptReply(xid, 3);
        private rpcPacker GenerateGarbageReply(uint xid) => CreateAcceptReply(xid, 4);
        
        private rpcPacker CreateAcceptReply(uint xid, uint status)
        {
            var reply = new rpcPacker();
            reply.setUint32(xid);
            reply.setUint32(1);    // REPLY
            reply.setUint32(0);    // MSG_ACCEPTED
            reply.setUint32(0);    // AUTH_NULL
            reply.setUint32(0);    // Verifier
            reply.setUint32(status);// Accept status
            return reply;
        }

        private rpcPacker GenerateRpcMismatchReply(uint xid)
        {
            var reply = new rpcPacker();
            reply.setUint32(xid);
            reply.setUint32(1);    // REPLY
            reply.setUint32(1);    // MSG_DENIED
            reply.setUint32(0);    // RPC_MISMATCH
            reply.setUint32(2);    // High version
            reply.setUint32(2);    // Low version
            return reply;
        }
        #endregion

        public void Dispose()
        {
            _udpClient?.Close();
            _udpClient?.Dispose();
        }
    }
}