using System;
using System.Collections.Generic;

namespace RPCV2Lib;

public class HandleTable
{
    private object[] _objects;
    private readonly Stack<uint> _freeIndices = new();

    public HandleTable(int initialSize)
    {
        _objects = new object[initialSize];
    }

    public object this[uint index] => _objects[index];

    public uint Add(object obj)
    {
        var index = _freeIndices.Count > 0 ? _freeIndices.Pop() : GetNextAvailableIndex();
        _objects[index] = obj;
        Console.WriteLine($"HandleTable.Add:{index}");
        return index;
    }

    public void Remove(uint index)
    {
        Console.WriteLine($"HandleTable.Remove:{index}");
        _objects[index] = null;
        _freeIndices.Push(index);
    }

    public uint Length { get; private set; } = 1;

    private uint GetNextAvailableIndex()
    {
        if (Length >= _objects.Length) Array.Resize(ref _objects, _objects.Length * 2);
        return Length++;
    }
}