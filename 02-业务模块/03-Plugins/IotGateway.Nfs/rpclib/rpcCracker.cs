using System;
using System.Text;

namespace RPCV2Lib
{
    public sealed class rpcCracker
    {
        private uint _offset;
        private readonly byte[] _data;

        public rpcCracker(byte[] data)
        {
            _data = data ?? throw new ArgumentNullException(nameof(data));
        }

        public void jump(uint offset) => _offset = Math.Min(_offset + offset, (uint)_data.Length);

        public uint get_uint32()
        {
            ValidateBuffer(4);
            uint value = ConvertBigEndian(_data, (int)_offset);
            _offset += 4;
            return value;
        }

        public char get_char()
        {
            ValidateBuffer(1);
            return (char)_data[_offset++];
        }

        public string get_String()
        {
            uint length = get_uint32();
            ValidateBuffer(length);
            
            var sb = new StringBuilder((int)length);
            for (int i = 0; i < length; i++)
            {
                sb.Append((char)_data[_offset++]);
            }
            
            AlignTo4ByteBoundary(ref length);
            return sb.ToString();
        }

        public byte[] getData()
        {
            uint length = get_uint32();
            ValidateBuffer(length);
            
            byte[] buffer = new byte[length];
            Buffer.BlockCopy(_data, (int)_offset, buffer, 0, (int)length);
            _offset += length;
            
            AlignTo4ByteBoundary(ref length);
            return buffer;
        }

        private static uint ConvertBigEndian(byte[] data, int offset)
        {
            return (uint)((data[offset] << 24) | 
                         (data[offset + 1] << 16) | 
                         (data[offset + 2] << 8) | 
                          data[offset + 3]);
        }

        public void Dump(string header) => DumpInternal(header, _data, (int)_offset);

        public static void DumpInternal(string header, byte[] data, int length)
        {
            Console.WriteLine($"{header}, length:{length}");
            
            for (int i = 0; i < data.Length; i += 4)
            {
                uint value = i + 3 < data.Length 
                    ? ConvertBigEndian(data, i) 
                    : 0;
                
                Console.WriteLine($"{i:X4} {ByteToHex(data, i, 4)}: {value}");
            }
        }

        private static string ByteToHex(byte[] data, int offset, int count)
        {
            var hex = new StringBuilder(count * 3);
            for (int i = 0; i < count && offset + i < data.Length; i++)
            {
                hex.Append($"{data[offset + i]:X2} ");
            }
            return hex.ToString().TrimEnd();
        }

        private void ValidateBuffer(uint required)
        {
            if (_offset + required > _data.Length)
                throw new IndexOutOfRangeException("Attempt to read beyond buffer");
        }

        private void AlignTo4ByteBoundary(ref uint length)
        {
            uint remainder = length % 4;
            if (remainder > 0) _offset += 4 - remainder;
        }
    }
}