namespace RPCV2Lib
{
    public class fhandle
    {
        private readonly uint _index;

        public fhandle(uint index) => _index = index;

        public fhandle(rpcCracker cracker)
        {
            _index = cracker.get_uint32();
            
            // 协议要求读取8个uint32，但后续7个未实际使用
            for (int i = 0; i < 7; i++)
            {
                cracker.get_uint32(); // 使用丢弃操作符跳过无用字段
            }
        }

        public void Pack(rpcPacker packer)
        {
            packer.setUint32(_index);
            
            // 协议要求填充7个零的uint32字段
            for (int i = 0; i < 7; i++)
            {
                packer.setUint32(0U);
            }
        }

        public uint Index => _index;
    }
}