using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using RPCV2Lib;

namespace mountV1;

public class mountd : rpcd
{
    private IPEndPoint _remoteHost;
    private readonly Dictionary<string, string> _map;

    public mountd() : base(Ports.mountd, Progs.mountd)
    {
    }

    public mountd(Dictionary<string, string> map) : base(Ports.mountd, Progs.mountd)
    {
        _map = map;
    }

    protected override void ProcessProcedure(uint proc, rpcCracker cracker, rpcPacker reply, IPEndPoint remoteHost)
    {
        _remoteHost = remoteHost;

        switch (proc)
        {
            case 0U: throw new BadProc();
            case 1U: Mount(cracker, reply); break;
            case 2U: throw new BadProc();
            case 3U: UMount(cracker, reply); break;
            case 4U: throw new BadProc();
            case 5U: throw new BadProc();
            default: throw new BadProc();
        }
    }

    private void Mount(rpcCracker cracker, rpcPacker reply)
    {
        var length = cracker.get_uint32();
        var dirPath = "";

        for (var i = 0U; i < length; i += 1U) dirPath += cracker.get_char().ToString();

        if (!_map.ContainsKey(_remoteHost.Address.ToString()))
        {
            reply.setUint32(0U);
        }
        else
        {
            var basePath = _map[_remoteHost.Address.ToString()];
            var name = Path.GetFileName(basePath);

            if (dirPath.Length > name.Length)
                if (name.Equals(dirPath.Substring(0, name.Length), StringComparison.CurrentCultureIgnoreCase))
                {
                    var subPath = dirPath.Substring(name.Length);
                    var usePath = Path.Combine(basePath, subPath);

                    if (!Directory.Exists(usePath)) Directory.CreateDirectory(usePath);

                    var fh = FileTable.Add(new FileEntry(usePath));
                    reply.setUint32(0U);
                    fh.Pack(reply);
                    return;
                }

            reply.setUint32(2U);
            reply.setUint32(0U);
        }
    }

    private void UMount(rpcCracker cracker, rpcPacker reply)
    {
        var length = cracker.get_uint32();
        var dirPath = "";

        for (var i = 0U; i < length; i += 1U) dirPath += cracker.get_char().ToString();

    }

    private enum Procs : uint
    {
        Null,
        Mount,
        Dump,
        UMount,
        UMountAll,
        Export
    }
}