using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Feng.IotGateway.Core.Extension;
using Furion.FriendlyException;
using IotGateway.Nfs.Models;
using IotGateway.Plugin.Core;
using IotGateway.Plugin.Core.Models;
using Microsoft.Extensions.Logging;
using mountV1;
using nfsV2;
using RPCV2Lib;

namespace IotGateway.Nfs;

/// <summary>
///     NFS服务器插件
/// </summary>
public class NfsPlugin : PluginBase
{
    /// <summary>
    ///     配置文件路径
    /// </summary>
    private readonly string _configPath;

    /// <summary>
    ///     配置
    /// </summary>
    private readonly NfsConfig _nfsConfig;

    /// <summary>
    ///     DNC配置列表
    /// </summary>
    private List<DncConfig> _dncConfigs = new();

    /// <summary>
    ///     NFS服务相关的线程和组件
    /// </summary>
    private Thread? _portMapperThread;
    private Thread? _mountdThread;
    private Thread? _nfsdThread;
    private Dictionary<string, string> _deviceMap = new();

    /// <summary>
    ///     服务监控线程
    /// </summary>
    private Thread? _monitoringThread;
    private bool _stopMonitoring = false;

    /// <summary>
    ///     配置属性
    /// </summary>
    public object? Configuration => _nfsConfig;

    /// <summary>
    ///     日志记录器
    /// </summary>
    private ILogger<NfsPlugin>? _logger;

    /// <summary>
    ///     获取配置
    /// </summary>
    public NfsConfig NfsConfig => _nfsConfig;

    /// <summary>
    ///     构造函数
    /// </summary>
    public NfsPlugin()
    {
        try
        {
            _configPath = Path.Combine(AppContext.BaseDirectory, "Configs", "nfsserver.json");
            _nfsConfig = LoadConfiguration() ?? new NfsConfig();

            // 设置基类_configuration字段，确保GetConfigurationAsync()能正确返回配置
            _configuration = _nfsConfig;

            // 延迟创建日志记录器
            InitLogger();
        }
        catch (Exception ex)
        {
            // 确保构造函数不会抛出异常
            Console.WriteLine($"NFS插件初始化失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     初始化日志记录器
    /// </summary>
    private void InitLogger()
    {
        try
        {
            var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole()
                    .SetMinimumLevel(LogLevel.Debug);
            });
            _logger = loggerFactory.CreateLogger<NfsPlugin>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"日志初始化失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     加载配置
    /// </summary>
    private NfsConfig? LoadConfiguration()
    {
        try
        {
            if (File.Exists(_configPath))
            {
                var json = File.ReadAllText(_configPath);
                var config = JsonSerializer.Deserialize<NfsConfig>(json);
                return config;
            }

            _logger?.LogWarning("NFS配置文件不存在，将使用默认配置");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "加载NFS配置失败");
        }

        return null;
    }

    /// <summary>
    ///     保存配置
    /// </summary>
    private void SaveConfiguration()
    {
        try
        {
            _logger?.LogInformation("正在保存NFS配置...");
            var directory = Path.GetDirectoryName(_configPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
                _logger?.LogInformation("创建配置目录: {Directory}", directory);
            }

            var json = JsonSerializer.Serialize(_nfsConfig, new JsonSerializerOptions
            {
                WriteIndented = true
            });
            File.WriteAllText(_configPath, json);
            _logger?.LogInformation("NFS配置保存成功");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "保存NFS配置失败");
        }
    }

    /// <summary>
    ///     插件名称
    /// </summary>
    public override string Name => "NFS_Server";

    /// <summary>
    ///     插件版本
    /// </summary>
    public override string Version => "1.0.0";

    /// <summary>
    ///     插件描述
    /// </summary>
    public override string Description => "NFS文件服务";

    /// <summary>
    ///     插件分类
    /// </summary>
    public override string Category => "Dnc";

    /// <summary>
    ///     初始化
    /// </summary>
    public override async Task InitializeAsync()
    {
        try
        {
            _logger?.LogInformation("NFS初始化开始: {NfsConfig}", JsonSerializer.Serialize(_nfsConfig));

            // 注册编码提供程序，确保能够处理不同的编码
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            // 检查端口可用性
            if (!CheckPortAvailability(111))
            {
                throw new InvalidOperationException("端口111不可用，NFS服务无法启动");
            }

            // 如果自启动
            if (_nfsConfig.Enabled)
            {
                _logger?.LogInformation("NFS服务器配置为自启动，准备启动服务器");
                await StartAsync();
            }

            await base.InitializeAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "NFS服务器初始化失败");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     启动
    /// </summary>
    /// <returns></returns>
    public override async Task StartAsync()
    {
        try
        {
            // 确保根目录存在
            var rootPath = Path.Combine(AppContext.BaseDirectory, "RootFiles");
            if (!Directory.Exists(rootPath))
            {
                Directory.CreateDirectory(rootPath);
                _logger?.LogInformation("创建根目录: {RootPath}", rootPath);
            }

            // 确保配置已加载
            _dncConfigs = await GetDncConfigsAsync();

            // 构建设备映射
            BuildDeviceMap();

            // 初始化FileTable
            FileTable.Initialize(1024);

            // 为每个启用的设备添加根目录文件句柄，确保文件句柄1可用
            foreach (var device in _dncConfigs.Where(d => d.Enabled))
            {
                var devicePath = _deviceMap[device.IpAddress];
                var rootEntry = new FileEntry(devicePath);
                var rootHandle = FileTable.Add(rootEntry);
            }

            if (_deviceMap.Count > 0)
            {
                _logger?.LogInformation("启动NFS服务，设备映射数量: {Count}", _deviceMap.Count);

                // 启动portmapper线程
                _portMapperThread = new Thread(new ThreadStart(new IotGateway.Nfs.portmapper.portmapper().Run))
                {
                    Name = "NFS-PortMapper"
                };
                _portMapperThread.Start();

                // 等待一小段时间确保portmapper启动
                await Task.Delay(1000);

                // 启动mountd线程
                _mountdThread = new Thread(new ThreadStart(new mountd(_deviceMap).Run))
                {
                    Name = "NFS-Mountd"
                };
                _mountdThread.Start();

                // 等待一小段时间确保mountd启动
                await Task.Delay(1000);

                // 启动nfsd线程
                _nfsdThread = new Thread(new ThreadStart(new nfsd().Run))
                {
                    Name = "NFS-Nfsd"
                };
                _nfsdThread.Start();

                // 等待一小段时间确保nfsd启动
                await Task.Delay(1000);

                // 设置设备状态
                foreach (var device in _dncConfigs.Where(d => d.Enabled))
                {
                    // 检查设备目录状态
                    var devicePath = _deviceMap[device.IpAddress];
                    if (Directory.Exists(devicePath))
                    {
                        var sendDir = Path.Combine(devicePath, "SEND");
                        var recDir = Path.Combine(devicePath, "REC");
                    }
                }
                _logger?.LogInformation("NFS服务所有线程启动完成，等待客户端连接...");

                // 启动服务监控线程
                StartNfsMonitoring();
            }
            await base.StartAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "NFS服务器启动失败");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     停止
    /// </summary>
    /// <returns></returns>
    public override async Task StopAsync()
    {
        _logger?.LogInformation("正在停止NFS服务器...");

        // 停止监控线程
        StopNfsMonitoring();
        if (_monitoringThread != null && _monitoringThread.IsAlive)
        {
            if (!_monitoringThread.Join(TimeSpan.FromSeconds(5)))
            {
                _logger?.LogWarning("监控线程未能在5秒内正常停止");
            }
            _monitoringThread = null;
        }

        try
        {
            // 停止线程
            if (_portMapperThread != null && _portMapperThread.IsAlive)
            {
                _portMapperThread.Interrupt();
                if (!_portMapperThread.Join(TimeSpan.FromSeconds(5)))
                {
                    _logger?.LogWarning("PortMapper线程未能在5秒内正常停止");
                }
                _portMapperThread = null;
            }

            if (_mountdThread != null && _mountdThread.IsAlive)
            {
                _mountdThread.Interrupt();
                if (!_mountdThread.Join(TimeSpan.FromSeconds(5)))
                {
                    _logger?.LogWarning("Mountd线程未能在5秒内正常停止");
                }
                _mountdThread = null;
            }

            if (_nfsdThread != null && _nfsdThread.IsAlive)
            {
                _nfsdThread.Interrupt();
                if (!_nfsdThread.Join(TimeSpan.FromSeconds(5)))
                {
                    _logger?.LogWarning("Nfsd线程未能在5秒内正常停止");
                }
                _nfsdThread = null;
            }

            // 清理设备映射
            _deviceMap.Clear();

            // 确保垃圾回收运行，释放所有未使用的资源
            GC.Collect();
            GC.WaitForPendingFinalizers();

            _logger?.LogInformation("NFS服务器已成功停止");
            await base.StopAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "停止NFS服务器时发生错误");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     更新配置
    /// </summary>
    /// <param name="configuration"></param>
    /// <returns></returns>
    public override async Task UpdateConfigurationAsync(object configuration)
    {
        try
        {
            if (configuration is JsonElement jsonElement)
            {
                // 将JsonElement反序列化为NfsConfig对象
                var config = jsonElement.Deserialize<NfsConfig>(new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (config != null)
                {
                    _nfsConfig.Enabled = config.Enabled;
                    _nfsConfig.SharePath = config.SharePath;

                    // 保存更新后的配置
                    SaveConfiguration();

                    await StopAsync();
                    // 如果服务正在运行，需要重启以应用新配置
                    if (_nfsConfig.Enabled) await StartAsync();
                }
            }
            else if (configuration is NfsConfig config)
            {
                // 保持原有的处理逻辑
                _nfsConfig.Enabled = config.Enabled;
                _nfsConfig.SharePath = config.SharePath;

                SaveConfiguration(); // 保存配置
                await StopAsync();

                if (_nfsConfig.Enabled) await StartAsync();
            }

            await base.UpdateConfigurationAsync(configuration);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "更新NFS配置失败");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     确保目录存在
    /// </summary>
    /// <param name="path">目录路径</param>
    public void EnsureDirectoryExists(string path)
    {
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
        }
    }

    /// <summary>
    ///     设置DNC配置
    /// </summary>
    /// <param name="dncConfig">DNC配置</param>
    /// <returns>设置结果</returns>
    public override async Task SetDncConfigAsync(DncConfig dncConfig)
    {
        // 先调用基类方法，将配置添加到_dncConfigs中
        await base.SetDncConfigAsync(dncConfig);

        // 如果配置是启用的，为这个配置创建目录
        if (dncConfig.Enabled)
        {
            // 确保用户的共享目录存在
            var sharePath = Path.Combine(_nfsConfig.SharePath, dncConfig.DeviceCode);
            EnsureDirectoryExists(sharePath);

            // 创建发送和接收子目录
            EnsureDirectoryExists(Path.Combine(sharePath, "SEND"));
            EnsureDirectoryExists(Path.Combine(sharePath, "REC"));
        }

        // 重新构建设备映射（如果NFS服务正在运行）
        if (_portMapperThread != null || _mountdThread != null || _nfsdThread != null)
        {
            BuildDeviceMap();
        }
    }

    /// <summary>
    ///     获取DNC配置
    /// </summary>
    /// <returns>DNC配置集合</returns>
    public override async Task<List<DncConfig>> GetDncConfigsAsync()
    {
        // 记录当前DNC配置状态
        _logger?.LogInformation("获取DNC配置, 当前配置数量: {Count}", _dncConfigs.Count);

        // 如果没有配置，尝试从数据库加载
        if (_dncConfigs.Count == 0)
            try
            {
                // 这里可以添加从其他来源加载配置的逻辑，如数据库
                _logger?.LogInformation("DNC配置为空，尝试从其他来源加载");

                // 调用基类方法获取配置
                _dncConfigs = await base.GetDncConfigsAsync();

                _logger?.LogInformation("加载完成，配置数量: {Count}", _dncConfigs.Count);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "加载DNC配置时出错");
            }

        return _dncConfigs;
    }

    /// <summary>
    ///     检查端口是否可用
    /// </summary>
    /// <param name="port">要检查的端口</param>
    /// <returns>端口是否可用</returns>
    private bool CheckPortAvailability(int port)
    {
        try
        {
            IPGlobalProperties properties = IPGlobalProperties.GetIPGlobalProperties();
            IPEndPoint[] tcpEndPoints = properties.GetActiveTcpListeners();
            IPEndPoint[] udpEndPoints = properties.GetActiveUdpListeners();

            // 检查TCP端口
            foreach (IPEndPoint endPoint in tcpEndPoints)
            {
                if (endPoint.Port == port)
                {
                    _logger?.LogError("TCP端口 {Port} 已被占用", port);
                    return false;
                }
            }

            // 检查UDP端口
            foreach (IPEndPoint endPoint in udpEndPoints)
            {
                if (endPoint.Port == port)
                {
                    _logger?.LogError("UDP端口 {Port} 已被占用", port);
                    return false;
                }
            }

            _logger?.LogInformation("端口 {Port} 可用", port);
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "检查端口 {Port} 可用性时发生错误", port);
            return false;
        }
    }

    /// <summary>
    ///     构建设备映射
    /// </summary>
    private void BuildDeviceMap()
    {
        _deviceMap.Clear();
        _logger?.LogInformation("构建设备映射, 当前配置数量: {Count}", _dncConfigs.Count);

        if (_dncConfigs != null && _dncConfigs.Count > 0)
        {
            foreach (var device in _dncConfigs)
            {
                _logger?.LogInformation("处理设备配置: {Device}", device.ToJson());

                if (device.Enabled)
                {
                    var devicePath = Path.Combine(_nfsConfig.SharePath, device.DeviceCode);

                    // 确保设备目录存在
                    if (!Directory.Exists(devicePath))
                    {
                        Directory.CreateDirectory(devicePath);
                        _logger?.LogInformation("创建设备主目录: {DevicePath}", devicePath);
                    }

                    // 确保SEND和REC子目录存在
                    var sendDir = Path.Combine(devicePath, "SEND");
                    var recDir = Path.Combine(devicePath, "REC");

                    if (!Directory.Exists(sendDir))
                    {
                        Directory.CreateDirectory(sendDir);
                        _logger?.LogInformation("创建SEND目录: {SendDir}", sendDir);
                    }

                    if (!Directory.Exists(recDir))
                    {
                        Directory.CreateDirectory(recDir);
                        _logger?.LogInformation("创建REC目录: {RecDir}", recDir);
                    }

                    _deviceMap.Add(device.IpAddress, devicePath);
                    _logger?.LogInformation("添加设备映射: IP={IpAddress}, 路径={DevicePath}, 设备代码={DeviceCode}",
                        device.IpAddress, devicePath, device.DeviceCode);

                    // 验证目录权限
                    try
                    {
                        var testFile = Path.Combine(devicePath, $"test_{DateTime.Now:yyyyMMddHHmmss}.tmp");
                        File.WriteAllText(testFile, "test");
                        File.Delete(testFile);
                        _logger?.LogInformation("设备目录读写权限正常: {DevicePath}", devicePath);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning(ex, "设备目录权限检查失败: {DevicePath}", devicePath);
                    }
                }
                else
                {
                    _logger?.LogInformation("设备配置未启用，跳过: DeviceCode={DeviceCode}, IpAddress={IpAddress}",
                        device.DeviceCode, device.IpAddress);
                }
            }
        }
        else
        {
            _logger?.LogWarning("没有DNC配置可供处理");
        }

        _logger?.LogInformation("设备映射构建完成，总数: {Count}, 映射详情: {DeviceMap}",
            _deviceMap.Count, string.Join("; ", _deviceMap.Select(kvp => $"{kvp.Key}=>{kvp.Value}")));
    }

    /// <summary>
    ///     启动NFS服务监控
    /// </summary>
    private void StartNfsMonitoring()
    {
        _monitoringThread = new Thread(MonitoringThread)
        {
            Name = "NFS-Monitoring"
        };
        _monitoringThread.Start();
        _logger?.LogInformation("NFS监控线程已启动");
    }

    /// <summary>
    ///     NFS服务监控线程
    /// </summary>
    private void MonitoringThread()
    {
        while (!_stopMonitoring)
        {
            try
            {
                _logger?.LogInformation("检查NFS服务状态...");

                // 检查设备状态
                foreach (var device in _dncConfigs.Where(d => d.Enabled))
                {
                    var devicePath = _deviceMap[device.IpAddress];
                    if (Directory.Exists(devicePath))
                    {
                        var sendDir = Path.Combine(devicePath, "SEND");
                        var recDir = Path.Combine(devicePath, "REC");
                        _logger?.LogInformation("设备目录状态 - 主目录: {Exists}, SEND: {SendExists}, REC: {RecExists}",
                            Directory.Exists(devicePath), Directory.Exists(sendDir), Directory.Exists(recDir));
                    }
                    else
                    {
                        _logger?.LogWarning("设备目录不存在: {DevicePath}", devicePath);
                    }
                }

                Thread.Sleep(5000); // 等待5秒后再次检查
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "NFS监控线程发生错误");
                Thread.Sleep(5000); // 等待5秒后再次尝试
            }
        }
    }

    /// <summary>
    ///     停止NFS服务监控
    /// </summary>
    public void StopNfsMonitoring()
    {
        _stopMonitoring = true;
        _logger?.LogInformation("NFS监控线程已停止");
    }
}