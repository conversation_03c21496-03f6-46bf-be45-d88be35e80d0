using System;
using System.Net;
using RPCV2Lib;

namespace IotGateway.Nfs.portmapper
{
    public sealed class portmapper : rpcd
    {
        private const uint MOUNTD_PROG = 100005;
        private const uint NFS_PROG = 100003;
        private const uint UDP_PROTOCOL = 17;
        private const uint MOUNTD_PORT = 635;
        private const uint NFS_PORT = 2049;

        public portmapper()
            : base(Ports.portmapper, Progs.portmapper) { }

        protected override void ProcessProcedure(uint proc, rpcCracker cracker, rpcPacker reply, IPEndPoint remoteHost)
        {
            if (proc != 3)
                throw new BadProc();

            GetPort(cracker, reply);
        }

        private void GetPort(rpcCracker cracker, rpcPacker reply)
        {
            var prog = cracker.get_uint32();
            var vers = cracker.get_uint32();
            var prot = cracker.get_uint32();
            var port = cracker.get_uint32();


            uint registeredPort = prot switch
            {
                UDP_PROTOCOL => MapUdpService(prog, vers),
                _ => 0
            };

            reply.setUint32(registeredPort);
        }

        private static uint MapUdpService(uint prog, uint version) => (prog, version) switch
        {
            (MOUNTD_PROG, 1) => MOUNTD_PORT,
            (NFS_PROG, 2) => NFS_PORT,
            _ => 0
        };
    }
}