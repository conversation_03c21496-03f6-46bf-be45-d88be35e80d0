using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Furion.FriendlyException;
using Furion.Logging;
using IotGateway.Plugin.Core;
using IotGateway.Plugin.Core.Models;
using IotGateway.SerialPortByTcp.Models;
using IotGateway.SerialPortByTcp.Utils;
using Microsoft.Extensions.Logging;

namespace IotGateway.SerialPortByTcp.Siemens;

/// <summary>
///     串口TCP服务器插件
/// </summary>
public class SerialPortByTcpPlugin : PluginBase
{
    /// <summary>
    ///     日志记录器
    /// </summary>
    private ILogger<SerialPortByTcpPlugin>? _logger;

    /// <summary>
    ///     配置文件路径
    /// </summary>
    private readonly string _configPath;

    /// <summary>
    ///     配置
    /// </summary>
    private readonly SerialPortByTcpConfig _config;

    /// <summary>
    ///     协议配置
    /// </summary>
    private ProtocolConfigItem _protocolConfig;

    /// <summary>
    ///     TCP服务器
    /// </summary>
    private TcpServer? _tcpServer;

    /// <summary>
    ///     取消令牌源
    /// </summary>
    private CancellationTokenSource? _cancellationTokenSource;

    /// <summary>
    ///     客户端连接集合
    /// </summary>
    private readonly ConcurrentDictionary<string, TcpClient> _clients = new();

    /// <summary>
    ///     客户端权限信息字典
    /// </summary>
    private readonly ConcurrentDictionary<string, ClientAuthInfo> _clientAuthInfos = new();

    /// <summary>
    ///     静态变量：是否等待文件上传
    /// </summary>
    private static bool _isWaitUpload = false;

    /// <summary>
    ///     静态变量：上传文件名
    /// </summary>
    private static string _uploadFileName = string.Empty;

    /// <summary>
    ///     配置属性
    /// </summary>
    public object? Configuration => _config;

    /// <summary>
    ///     构造函数
    /// </summary>
    public SerialPortByTcpPlugin()
    {
        try
        {
            // 初始化日志记录器
            InitLogger();

            _configPath = Path.Combine(AppContext.BaseDirectory, "Configs", "serialporttcp.json");
            _config = LoadConfiguration() ?? new SerialPortByTcpConfig();
            _configuration = _config;

            // 初始化协议配置
            InitProtocolConfig();
        }
        catch (Exception ex)
        {
            // 确保构造函数不会抛出异常
            Console.WriteLine($"串口TCP插件初始化失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     初始化日志记录器
    /// </summary>
    private void InitLogger()
    {
        try
        {
            var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole()
                    .SetMinimumLevel(LogLevel.Debug);
            });
            _logger = loggerFactory.CreateLogger<SerialPortByTcpPlugin>();
        }
        catch (Exception ex)
        {
        }
    }

    /// <summary>
    ///     初始化协议配置
    /// </summary>
    private void InitProtocolConfig()
    {
        try
        {
            string configPath = Path.Combine(AppContext.BaseDirectory, "Configs", "protocol.json");

            // 确保配置文件目录存在
            string configDir = Path.GetDirectoryName(configPath);
            if (!Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }

            // 加载协议配置
            var protocolConfig = ProtocolConfig.Load(configPath);
            _logger?.LogInformation($"协议配置加载类型：{_config.ProtocolType}");
            _protocolConfig = protocolConfig.GetItem(_config.ProtocolType);

            // 详细输出协议配置信息，包括所有参数和转换后的字节信息
            _logger?.LogInformation("协议配置加载成功详情:");
            _logger?.LogInformation("  - 协议类型: {Type}", _config.ProtocolType);
            _logger?.LogInformation("  - 包前缀: {PacketPrefix}", string.IsNullOrEmpty(_protocolConfig.PacketPrefix) ? "无" : _protocolConfig.PacketPrefix);
            _logger?.LogInformation("  - 包后缀: {PacketSuffix}", string.IsNullOrEmpty(_protocolConfig.PacketSuffix) ? "无" : _protocolConfig.PacketSuffix);
            _logger?.LogInformation("  - 文件前缀: {FilePrefix}", string.IsNullOrEmpty(_protocolConfig.FilePrefix) ? "无" : _protocolConfig.FilePrefix);
            _logger?.LogInformation("  - 文件后缀: {FileSuffix}", string.IsNullOrEmpty(_protocolConfig.FileSuffix) ? "无" : _protocolConfig.FileSuffix);
            _logger?.LogInformation("  - 指令前缀: {OrderPrefix}", string.IsNullOrEmpty(_protocolConfig.OrderPrefix) ? "无" : _protocolConfig.OrderPrefix);
            _logger?.LogInformation("  - 指令后缀: {OrderSuffix}", string.IsNullOrEmpty(_protocolConfig.OrderSuffix) ? "无" : _protocolConfig.OrderSuffix);

            // 输出转换后的字节信息
            if (!string.IsNullOrEmpty(_protocolConfig.PacketPrefix))
            {
                var prefixBytes = ConvertToBytes(_protocolConfig.PacketPrefix);
                _logger?.LogInformation("  - 包前缀字节(十六进制): {PrefixHex}",
                    prefixBytes.Length > 0 ? BytesTools.ByteArrToHex(prefixBytes, 0, prefixBytes.Length, true) : "空");
            }

            if (!string.IsNullOrEmpty(_protocolConfig.PacketSuffix))
            {
                var suffixBytes = ConvertToBytes(_protocolConfig.PacketSuffix);
                _logger?.LogInformation("  - 包后缀字节(十六进制): {SuffixHex}",
                    suffixBytes.Length > 0 ? BytesTools.ByteArrToHex(suffixBytes, 0, suffixBytes.Length, true) : "空");
                _logger?.LogInformation("  - 包后缀字节(ASCII字符): {SuffixAscii}",
                    suffixBytes.Length > 0 ? string.Join(",", suffixBytes.Select(b => b < 32 || b > 126 ? $"\\x{b:X2}" : ((char)b).ToString())) : "空");
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "初始化协议配置失败，使用默认配置");
            // 使用默认的协议配置
            _protocolConfig = new ProtocolConfigItem();
        }
    }

    /// <summary>
    /// 将字符串转换为字节数组，支持Unicode转义字符
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>字节数组</returns>
    private byte[] ConvertToBytes(string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return Array.Empty<byte>();
        }

        // 处理Unicode转义字符，如 \u001a
        if (input.Contains("\\u"))
        {
            input = System.Text.RegularExpressions.Regex.Unescape(input);
        }

        return Encoding.ASCII.GetBytes(input);
    }

    /// <summary>
    ///     设置DNC配置（支持外部动态注入DNC配置，保证_dncConfigs赋值）
    /// </summary>
    /// <param name="dncConfig">DNC配置</param>
    /// <returns>设置结果</returns>
    public override async Task SetDncConfigAsync(DncConfig dncConfig)
    {
        // 调用基类方法，维护_dncConfigs集合
        await base.SetDncConfigAsync(dncConfig);

        try
        {
            _logger?.LogInformation("接收到DNC配置: {DeviceCode}, 启用: {Enabled}", dncConfig.DeviceCode, dncConfig.Enabled);

            // 如果配置是启用的，为这个配置创建目录
            if (dncConfig.Enabled)
            {
                // 确保用户的发送和接收目录存在
                string sendPath = GetUserSpecificPath(dncConfig.DeviceCode, _config.SendPath);
                string receivePath = GetUserSpecificPath(dncConfig.DeviceCode, _config.ReceivePath);

                EnsureDirectoryExists(sendPath);
                EnsureDirectoryExists(receivePath);
                _logger?.LogInformation("用户 {DeviceCode} 的目录创建完成，发送目录: {SendPath}, 接收目录: {ReceivePath}",
                    dncConfig.DeviceCode, sendPath, receivePath);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError("SetDncConfigAsync异常: {ErrorMessage}", ex.Message);
        }
    }

    /// <summary>
    ///     加载配置
    /// </summary>
    private SerialPortByTcpConfig? LoadConfiguration()
    {
        if (File.Exists(_configPath))
        {
            var json = File.ReadAllText(_configPath);
            var config = JsonSerializer.Deserialize<SerialPortByTcpConfig>(json);
            _logger?.LogInformation("TCP配置加载成功: Port={Port}, MaxConnections={MaxConnections}",
                config?.Port, config?.MaxConnections);
            return config;
        }

        return null;
    }

    /// <summary>
    ///     保存配置
    /// </summary>
    private void SaveConfiguration()
    {
        _logger?.LogInformation("正在保存TCP配置...");
        var directory = Path.GetDirectoryName(_configPath);
        if (!Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory!);
            _logger?.LogInformation("创建配置目录: {Directory}", directory);
        }

        var json = JsonSerializer.Serialize(_config, new JsonSerializerOptions
        {
            WriteIndented = true
        });
        File.WriteAllText(_configPath, json);
        _logger?.LogInformation("TCP配置保存成功");
    }

    /// <summary>
    ///     插件名称
    /// </summary>
    public override string Name => "Siemens Serial Port TCP";

    /// <summary>
    ///     插件版本
    /// </summary>
    public override string Version => "1.0.0";

    /// <summary>
    ///     插件描述
    /// </summary>
    public override string Description => "Siemens串口TCP服务器插件";

    /// <summary>
    ///     插件分类
    /// </summary>
    public override string Category => "Dnc";

    /// <summary>
    ///     初始化
    /// </summary>
    public override async Task InitializeAsync()
    {
        _logger?.LogInformation("TCP服务器初始化开始: {Config}", JsonSerializer.Serialize(_config));

        // 如果自启动
        if (_config.Enabled)
        {
            _logger?.LogInformation("TCP服务器配置为自启动，准备启动服务器");
            await StartAsync();
        }

        await base.InitializeAsync();
    }

    /// <summary>
    ///     启动
    /// </summary>
    public override async Task StartAsync()
    {
        try
        {
            _logger?.LogInformation("正在启动TCP服务器...");

            if (_tcpServer != null)
            {
                Console.WriteLine("停止已有TCP服务器");
                await StopAsync();

                // 添加短暂延迟，确保端口完全释放
                await Task.Delay(1000);
            }

            // 检查端口是否可用
            if (!IsPortAvailable(_config.Port))
            {
                var errorMessage = $"端口 {_config.Port} 已被占用，无法启动TCP服务器";
                throw Oops.Oh(errorMessage);
            }

            foreach (var dncConfig in _dncConfigs)
            {
                // 确保文件目录存在
                string sendPath = GetUserSpecificPath(dncConfig.DeviceCode, _config.SendPath);
                // 
                string receivePath = GetUserSpecificPath(dncConfig.DeviceCode, _config.ReceivePath);
                EnsureDirectoryExists(sendPath);
                EnsureDirectoryExists(receivePath);
                _logger?.LogInformation("文件目录检查完成，发送目录: {SendPath}, 接收目录: {ReceivePath}", sendPath, receivePath);
            }

            // 初始化TCP服务器
            _tcpServer = new TcpServer(IPAddress.Any, _config.Port);

            // 创建取消令牌
            _cancellationTokenSource = new CancellationTokenSource();
            var token = _cancellationTokenSource.Token;

            // 启动监听
            _tcpServer.Start();
            _logger?.LogInformation("TCP服务器已启动，正在监听端口 {Port}", _config.Port);

            // 开始接受客户端连接
            _ = AcceptClientsAsync(token);

            await base.StartAsync();
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    /// 接受客户端连接的异步方法
    /// </summary>
    private async Task AcceptClientsAsync(CancellationToken cancellationToken)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                var client = await _tcpServer!.AcceptTcpClientAsync(cancellationToken);
                var clientEndPoint = client.Client.RemoteEndPoint?.ToString() ?? "未知客户端";

                _logger?.LogInformation("接受到新的客户端连接: {ClientEndPoint}", clientEndPoint);

                // 检查是否超过最大连接数
                if (_clients.Count >= _config.MaxConnections)
                {
                    client.Close();
                    continue;
                }

                // 添加到客户端字典
                _clients.TryAdd(clientEndPoint, client);
                _logger?.LogInformation("当前连接客户端数量: {Count}/{MaxConnections}", _clients.Count, _config.MaxConnections);

                // 设置客户端超时
                client.ReceiveTimeout = _config.ConnectionTimeout;
                client.SendTimeout = _config.ConnectionTimeout;
                // client.SendBufferSize = 1024 * 1024;  // 1m

                // 处理客户端连接
                _ = HandleClientAsync(client, clientEndPoint, cancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger?.LogInformation("接受客户端连接的任务已取消");
        }
    }

    /// <summary>
    /// 处理客户端连接
    /// </summary>
    private async Task HandleClientAsync(TcpClient client, string clientEndPoint, CancellationToken cancellationToken)
    {
        // 记录会话开始时间用于性能分析
        var sessionStartTime = DateTime.Now;
        var totalBytesReceived = 0L;
        var totalPacketsProcessed = 0;

        try
        {
            _logger?.LogInformation("[{SessionId}] 开始处理客户端 {ClientEndPoint} 连接，会话时间: {StartTime}",
                sessionStartTime.Ticks, clientEndPoint, sessionStartTime);

            bool authenticated = await AuthenticateClientAsync(client, clientEndPoint);
            if (!authenticated)
            {
                client.Close();
                _clients.TryRemove(clientEndPoint, out _);
                return;
            }
            else
            {
                _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 认证成功，认证耗时: {AuthTime}ms",
                    sessionStartTime.Ticks, clientEndPoint, (DateTime.Now - sessionStartTime).TotalMilliseconds);
            }

            // 创建会话级变量 - 使用实例级Dictionary存储每个会话的状态
            var sessionState = new Dictionary<string, object>
            {
                ["IsWaitFileUpload"] = false,
                ["UploadFileName"] = string.Empty,
                ["FileBuffer"] = new StringBuilder()
            };
            StringBuilder receivePacket = new StringBuilder();

            // 获取用户特定的文件路径
            string sendPath = string.Empty;
            string receivePath = string.Empty;

            // 从认证信息中获取DeviceCode
            if (_clientAuthInfos.TryGetValue(clientEndPoint, out var authInfo) && !string.IsNullOrEmpty(authInfo.DeviceCode))
            {
                sendPath = GetUserSpecificPath(authInfo.DeviceCode, _config.SendPath);
                receivePath = GetUserSpecificPath(authInfo.DeviceCode, _config.ReceivePath);
            }

            // 确保目录存在
            EnsureDirectoryExists(sendPath);
            EnsureDirectoryExists(receivePath);

            _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 文件路径配置完成，发送路径: {SendPath}, 接收路径: {ReceivePath}",
                sessionStartTime.Ticks, clientEndPoint, sendPath, receivePath);

            await using var stream = client.GetStream();
            byte[] buffer = new byte[4096];

            while (!cancellationToken.IsCancellationRequested && client.Connected)
            {
                int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                if (bytesRead == 0)
                {
                    _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 已断开连接 - 收到0字节数据，会话统计: 总接收字节={TotalBytes}, 总处理包数={TotalPackets}, 会话持续时间={SessionDuration}ms",
                        sessionStartTime.Ticks, clientEndPoint, totalBytesReceived, totalPacketsProcessed, (DateTime.Now - sessionStartTime).TotalMilliseconds);
                    break;
                }
                _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 收到数据: {Data}",
                    sessionStartTime.Ticks, clientEndPoint, BytesTools.ByteArrToASCII(buffer, 0, bytesRead));

                totalBytesReceived += bytesRead;

                // 处理接收到的数据
                string dataReceived = BytesTools.ByteArrToASCII(buffer, 0, bytesRead);

                // 累加包数据
                receivePacket.Append(dataReceived);

                // 检查是否包含完整的数据包
                dataReceived = receivePacket.ToString();

                // 参考Class1中的逻辑，使用更严格的前后缀检查
                bool isPacketComplete = false;
                bool isFilePacketComplete = false;
                var indexPositions = (-1, -1, -1, -1); // (前缀位置, 后缀位置, 文件前缀位置, 文件后缀位置)

                if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketPrefix) && dataReceived.Contains(_protocolConfig.PacketPrefix))
                {
                    indexPositions.Item1 = dataReceived.IndexOf(_protocolConfig.PacketPrefix, StringComparison.Ordinal);
                }

                if (indexPositions.Item1 != -1 && !string.IsNullOrEmpty(_protocolConfig.PacketSuffix) && dataReceived.Contains(_protocolConfig.PacketSuffix))
                {
                    indexPositions.Item2 = dataReceived.IndexOf(_protocolConfig.PacketSuffix, indexPositions.Item1 + 1, StringComparison.Ordinal);
                }

                if (indexPositions.Item1 > -1 && indexPositions.Item2 > -1)
                {
                    isPacketComplete = true;
                }

                if (!string.IsNullOrWhiteSpace(_protocolConfig.FilePrefix) && dataReceived.Contains(_protocolConfig.FilePrefix))
                {
                    indexPositions.Item3 = dataReceived.IndexOf(_protocolConfig.FilePrefix, indexPositions.Item1 + 1, StringComparison.Ordinal);
                }

                if (indexPositions.Item3 != -1 && !string.IsNullOrWhiteSpace(_protocolConfig.FileSuffix) && dataReceived.Contains(_protocolConfig.FileSuffix))
                {
                    indexPositions.Item4 = dataReceived.IndexOf(_protocolConfig.FileSuffix, indexPositions.Item3 + 1, StringComparison.Ordinal);
                }

                if (indexPositions.Item3 > -1 && indexPositions.Item4 > -1)
                {
                    isFilePacketComplete = true;
                }

                // 当检测到完整的包才进行解析
                if (isPacketComplete || isFilePacketComplete)
                {
                    totalPacketsProcessed++;

                    _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 检测到完整数据包 #{PacketNumber}，内容长度: {Length}",
                        sessionStartTime.Ticks, clientEndPoint, totalPacketsProcessed, dataReceived.Length);

                    await ParseMessageAsync(client, clientEndPoint, dataReceived, sessionState, sendPath, receivePath);

                    // 清空已处理的数据
                    receivePacket.Clear();
                }
            }
        }
        finally
        {
            var sessionDuration = (DateTime.Now - sessionStartTime).TotalMilliseconds;

            // 断开连接并从字典中移除
            if (client.Connected)
            {
                client.Close();
                _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 连接主动关闭", sessionStartTime.Ticks, clientEndPoint);
            }

            _clients.TryRemove(clientEndPoint, out _);
            _logger?.LogInformation("[{SessionId}] 客户端 {ClientEndPoint} 会话结束 - 持续时间: {Duration}ms, 接收总字节: {TotalBytes}, 处理总包数: {TotalPackets}, 平均包大小: {AvgPacketSize} 字节, 当前总连接数: {CurrentConnections}",
                sessionStartTime.Ticks, clientEndPoint, sessionDuration, totalBytesReceived, totalPacketsProcessed,
                totalPacketsProcessed > 0 ? totalBytesReceived / totalPacketsProcessed : 0, _clients.Count);
        }
    }

    /// <summary>
    /// 解析消息
    /// </summary>
    private async Task ParseMessageAsync(TcpClient client, string clientEndPoint, string receiveMsg,
        Dictionary<string, object> sessionState, string sendPath, string receivePath)
    {
        var messageStartTime = DateTime.Now;

        try
        {
            _logger?.LogInformation("[消息解析] {ClientEndPoint} 开始消息解析，消息长度: {ContentLength}, 开始时间: {StartTime}",
                clientEndPoint, receiveMsg.Length, messageStartTime);

            if (!client.Connected)
            {
                _logger?.LogWarning("[消息解析] {ClientEndPoint} 连接已断开!", clientEndPoint);
                return;
            }

            if (receiveMsg.Length <= 2)
            {
                _logger?.LogWarning("[消息解析] {ClientEndPoint} 消息长度过短: {Length}", clientEndPoint, receiveMsg.Length);
                return;
            }

            // 记录消息内容（如果长度较短）
            if (receiveMsg.Length < 40)
            {
                _logger?.LogInformation("[消息解析] {ClientEndPoint} 消息字符: {Message}", clientEndPoint, receiveMsg);
            }

            string orderPrefix = _protocolConfig.OrderPrefix ?? "";
            string orderSuffix = _protocolConfig.OrderSuffix ?? "";

            if (string.IsNullOrEmpty(orderPrefix) || string.IsNullOrEmpty(orderSuffix))
            {
                _logger?.LogWarning("[消息解析] {ClientEndPoint} 指令前后缀字符不能为空! 前缀: '{OrderPrefix}', 后缀: '{OrderSuffix}'",
                    clientEndPoint, orderPrefix, orderSuffix);
                return;
            }

            // 构建正则表达式，使用文件名规则
            string regEx = orderPrefix + _config.FileNameRule + orderSuffix;
            _logger?.LogInformation("[消息解析] {ClientEndPoint} 使用正则表达式: {RegEx}", clientEndPoint, regEx);

            // 检查是否为指令且当前没有等待上传文件
            if (Regex.IsMatch(receiveMsg, regEx, RegexOptions.IgnoreCase) && string.IsNullOrWhiteSpace(_uploadFileName))
            {
                _logger?.LogInformation("[消息解析] {ClientEndPoint} 检测到指令格式，开始解析指令", clientEndPoint);

                MatchCollection orderArr = Regex.Matches(receiveMsg, regEx, RegexOptions.IgnoreCase);
                if (orderArr.Count == 0)
                {
                    _logger?.LogWarning("[消息解析] {ClientEndPoint} 正则匹配失败", clientEndPoint);
                    return;
                }

                Group group = orderArr[0].Groups[1];
                string orderStr = group?.ToString().Replace("\r", "").Replace("\n", "").Replace(" ", "");

                if (string.IsNullOrEmpty(orderStr))
                {
                    _logger?.LogWarning("[消息解析] {ClientEndPoint} 指令未找到: {Message}", clientEndPoint, receiveMsg);
                    return;
                }

                if (orderStr.Length < 2)
                {
                    _logger?.LogWarning("[消息解析] {ClientEndPoint} 指令长度不足: {OrderStr}", clientEndPoint, orderStr);
                    return;
                }

                string orderNum = orderStr.Substring(0, 1); // 命令号
                string fileName = orderStr.Substring(1, orderStr.Length - 1).Trim(); // 参数(文件名)

                _logger?.LogInformation("[消息解析] {ClientEndPoint} 指令解析成功 - 命令号: '{OrderNum}', 文件名: '{FileName}'",
                    clientEndPoint, orderNum, fileName);

                // 根据指令类型处理
                switch (orderNum)
                {
                    case "2": // 发送指定NC文件
                        await HandleDownloadFileCommand(client, clientEndPoint, fileName, sendPath);
                        break;

                    case "3": // 获取NC文件列表
                        await HandleFileListCommand(client, clientEndPoint, fileName, sendPath);
                        break;

                    case "4": // 准备接收NC文件
                        await HandleUploadPrepareCommand(client, clientEndPoint, fileName);
                        break;

                    case "5": // 重新连接
                        await HandleReconnectCommand(client, clientEndPoint);
                        break;

                    default:
                        _logger?.LogWarning("[消息解析] {ClientEndPoint} 指令码 [{OrderNum}] 未找到!", clientEndPoint, orderNum);
                        SendResponseToClientByServer(clientEndPoint, $"未知指令: {orderNum}");
                        break;
                }
            }
            else
            {
                // 如果不是指令，则尝试解析为NC文件内容
                await ParseMessageIsNC(clientEndPoint, receiveMsg, receivePath);
            }

            var totalProcessingDuration = (DateTime.Now - messageStartTime).TotalMilliseconds;
            _logger?.LogInformation("[消息解析] {ClientEndPoint} 消息解析完成，总耗时: {TotalTime}ms",
                clientEndPoint, totalProcessingDuration);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "[消息解析] {ClientEndPoint} 消息解析异常", clientEndPoint);
        }
    }

    /// <summary>
    /// 处理下载文件指令 (指令2)
    /// </summary>
    private async Task HandleDownloadFileCommand(TcpClient client, string clientEndPoint, string fileName, string sendPath)
    {
        try
        {
            string filePath = Path.Combine(sendPath, fileName);
            _logger?.LogInformation("[指令执行] {ClientEndPoint} 2 执行下载文件指令，文件路径: '{FilePath}'", clientEndPoint, filePath);

            if (File.Exists(filePath))
            {
                var fileInfo = new FileInfo(filePath);
                _logger?.LogInformation("[指令执行] {ClientEndPoint} 2 找到NC文件: '{FileName}', 文件大小: {FileSize} 字节, 延时: {Delay}ms",
                    clientEndPoint, fileName, fileInfo.Length, _config.SendDelayTime);

                // 延时
                if (IsCountDown(_config.SendDelayTime))
                {
                    await WriteNCToMachineTool(clientEndPoint, filePath, fileName);
                }
            }
            else
            {
                _logger?.LogWarning("[指令执行] {ClientEndPoint} 2 NC文件不存在,请检查NC路径配置 [{FilePath}]", clientEndPoint, filePath);
                SendResponseToClientByServer(clientEndPoint, $"文件不存在: {fileName}");
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "[指令执行] {ClientEndPoint} 2 下载文件指令执行异常", clientEndPoint);
        }
    }

    /// <summary>
    /// 处理文件列表指令 (指令3)
    /// </summary>
    private async Task HandleFileListCommand(TcpClient client, string clientEndPoint, string fileName, string sendPath)
    {
        try
        {
            _logger?.LogInformation("[指令执行] {ClientEndPoint} 3 执行文件列表指令，模式: '{Pattern}'", clientEndPoint, fileName);

            var (success, fileList) = GetFileList(clientEndPoint, sendPath, fileName);
            if (success && fileList != null)
            {
                _logger?.LogInformation("[指令执行] {ClientEndPoint} 3 获取NC文件: {Pattern}，目录列表完成，延时写入: {Delay} 毫秒!",
                    clientEndPoint, fileName, _config.SendDelayTime);

                // 延时
                if (IsCountDown(_config.SendDelayTime))
                {
                    await WriteNCListToMachineTool(clientEndPoint, fileList);
                }
            }
            else
            {
                _logger?.LogWarning("[指令执行] {ClientEndPoint} 3 获取文件列表失败", clientEndPoint);
                SendResponseToClientByServer(clientEndPoint, "文件列表为空");
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "[指令执行] {ClientEndPoint} 3 文件列表指令执行异常", clientEndPoint);
        }
    }

    /// <summary>
    /// 处理上传准备指令 (指令4)
    /// </summary>
    private async Task HandleUploadPrepareCommand(TcpClient client, string clientEndPoint, string fileName)
    {
        try
        {
            _isWaitUpload = true;
            _uploadFileName = fileName;

            _logger?.LogInformation("[指令执行] {ClientEndPoint} 4 获取NC文件名: {FileName}, 等待NC文件内容上传!",
                clientEndPoint, fileName);

            SendResponseToClientByServer(clientEndPoint, $"准备接收文件: {fileName}");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "[指令执行] {ClientEndPoint} 4 上传准备指令执行异常", clientEndPoint);
        }
    }

    /// <summary>
    /// 处理重新连接指令 (指令5)
    /// </summary>
    private async Task HandleReconnectCommand(TcpClient client, string clientEndPoint)
    {
        try
        {
            _logger?.LogInformation("[指令执行] {ClientEndPoint} 5 重新连接指令，准备关闭当前连接", clientEndPoint);

            // 移除认证信息
            _clientAuthInfos.TryRemove(clientEndPoint, out var removedAuthInfo);
            
            // 关闭连接
            client.Close();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "[指令执行] {ClientEndPoint} 5 重新连接指令执行异常", clientEndPoint);
        }
    }

    /// <summary>
    /// 写入NC文件到机床
    /// </summary>
    private async Task WriteNCToMachineTool(string clientEndPoint, string filePath, string fileName)
    {
        try
        {
            _logger?.LogInformation("[文件发送] {ClientEndPoint} 开始发送NC文件: {FileName}", clientEndPoint, fileName);

            // 检查客户端连接状态
            if (!_clients.TryGetValue(clientEndPoint, out var client) || !client.Connected)
            {
                _logger?.LogWarning("[文件发送] {ClientEndPoint} 连接失败!", clientEndPoint);
                return;
            }

            // 发送包前缀
            if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketPrefix))
            {
                byte[] prefixBytes = ConvertToBytes(_protocolConfig.PacketPrefix);
                _logger?.LogDebug("[文件发送] {ClientEndPoint} 发送包前缀! 前缀: {PacketPrefix}, 字节(十六进制): {PrefixBytes}",
                    clientEndPoint, _protocolConfig.PacketPrefix,
                    prefixBytes.Length > 0 ? BytesTools.ByteArrToHex(prefixBytes, 0, prefixBytes.Length, true) : "空");

                bool prefixResult = _tcpServer?.SendToClient(clientEndPoint, prefixBytes) ?? false;
                if (!prefixResult)
                {
                    _logger?.LogError("[文件发送] {ClientEndPoint} 发送包前缀失败", clientEndPoint);
                    return;
                }
            }

            // 读取并发送文件内容
            using var sr = new StreamReader(filePath, Encoding.Default);
            string strLine = _protocolConfig.LineBreak;
            string line;
            int lineNumber = 0;
            
            while ((line = sr.ReadLine()) != null)
            {
                lineNumber++;
                string getLine = line;
                if (line.IndexOf(strLine, StringComparison.Ordinal) == -1)
                {
                    getLine = line + strLine;
                }
                
                // 发送每一行
                byte[] lineBytes = BytesTools.ASCIIToASCIIByteArr(getLine);
                _logger?.LogDebug("[文件发送] {ClientEndPoint} 第{LineNumber}行 W[ASCII]: {LineContent}", 
                    clientEndPoint, lineNumber, getLine);
                
                bool lineResult = _tcpServer?.SendToClient(clientEndPoint, lineBytes) ?? false;
                if (!lineResult)
                {
                    _logger?.LogError("[文件发送] {ClientEndPoint} 发送第{LineNumber}行失败", clientEndPoint, lineNumber);
                    return;
                }
            }

            // 发送包后缀
            if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketSuffix))
            {
                byte[] suffixBytes = ConvertToBytes(_protocolConfig.PacketSuffix);
                _logger?.LogDebug("[文件发送] {ClientEndPoint} 发送包后缀! 后缀: {PacketSuffix}, 字节(十六进制): {SuffixBytes}, 配置的后缀是{ConfigSuffix} 反向转换得到的应该是{ReversedSuffix}字符",
                    clientEndPoint, _protocolConfig.PacketSuffix,
                    suffixBytes.Length > 0 ? BytesTools.ByteArrToHex(suffixBytes, 0, suffixBytes.Length, true) : "空",
                    _protocolConfig.PacketSuffix,
                    suffixBytes.Length > 0 ? string.Join(",", suffixBytes.Select(b => b < 32 || b > 126 ? $"\\x{b:X2}" : ((char)b).ToString())) : "空");

                bool suffixResult = _tcpServer?.SendToClient(clientEndPoint, suffixBytes) ?? false;
                if (!suffixResult)
                {
                    _logger?.LogError("[文件发送] {ClientEndPoint} 发送包后缀失败", clientEndPoint);
                    return;
                }
            }

            _logger?.LogInformation("[文件发送] {ClientEndPoint} 2 写入机床NC文件完成: {FileName}, 总行数: {LineCount}", 
                clientEndPoint, fileName, lineNumber);
        }
        catch (Exception ex)
        {
            _logger?.LogError("[文件发送] {ClientEndPoint} 2 写入机床NC文件异常: {ErrorMessage}", 
                clientEndPoint, ex.Message);
        }
    }

    /// <summary>
    /// 获取文件列表
    /// </summary>
    private (bool success, StringBuilder? fileList) GetFileList(string clientEndPoint, string sendPath, string fileName)
    {
        try
        {
            if (!Directory.Exists(sendPath))
            {
                _logger?.LogWarning("[文件列表] {ClientEndPoint} 3 未找到SEND文件夹: {SendPath}", clientEndPoint, sendPath);
                return (false, null);
            }

            string[] files = Directory.GetFiles(sendPath, "*");
            if (files.Length < 1)
            {
                _logger?.LogWarning("[文件列表] {ClientEndPoint} 3 NC文件列表为空, File: {FileName}!", clientEndPoint, fileName);
                return (false, null);
            }

            string strLine = _protocolConfig.LineBreak;
            var sbList = new StringBuilder();

            // 添加文件前缀
            sbList.Append(_protocolConfig.FilePrefix);

            // 添加列表头部
            string descHead = _protocolConfig.ListFileHead;
            sbList.AppendFormat(descHead, fileName);

            // 添加文件列表
            int indexNum = 0;
            foreach (string file in files)
            {
                if (File.Exists(file))
                {
                    try
                    {
                        string getName = Path.GetFileName(file);
                        sbList.Append($"N{indexNum}({getName}){strLine}");
                        indexNum++;
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "[文件列表] {ClientEndPoint} 3 获取文件异常 文件名: {FileName}", clientEndPoint, Path.GetFileName(file));
                    }
                }
            }

            // 添加文件后缀
            sbList.Append(_protocolConfig.FileSuffix + strLine);

            return (true, sbList);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "[文件列表] {ClientEndPoint} 3 获取文件列表异常", clientEndPoint);
            return (false, null);
        }
    }

    /// <summary>
    /// 写入NC文件列表到机床
    /// </summary>
    private async Task WriteNCListToMachineTool(string clientEndPoint, StringBuilder ncList)
    {
        try
        {
            // 检查客户端连接状态
            if (!_clients.TryGetValue(clientEndPoint, out var client) || !client.Connected)
            {
                _logger?.LogWarning("[文件列表] {ClientEndPoint} 连接失败!", clientEndPoint);
                return;
            }

            // 发送包前缀
            if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketPrefix))
            {
                byte[] prefixBytes = ConvertToBytes(_protocolConfig.PacketPrefix);
                _logger?.LogDebug("[文件列表] {ClientEndPoint} 发送包前缀! 前缀: {PacketPrefix}, 字节(十六进制): {PrefixBytes}",
                    clientEndPoint, _protocolConfig.PacketPrefix,
                    prefixBytes.Length > 0 ? BytesTools.ByteArrToHex(prefixBytes, 0, prefixBytes.Length, true) : "空");

                bool prefixResult = _tcpServer?.SendToClient(clientEndPoint, prefixBytes) ?? false;
                if (!prefixResult)
                {
                    _logger?.LogError("[文件列表] {ClientEndPoint} 发送包前缀失败", clientEndPoint);
                    return;
                }
            }

            // 发送列表内容
            byte[] listBytes = BytesTools.ASCIIToASCIIByteArr(ncList.ToString());
            _logger?.LogDebug("[文件列表] {ClientEndPoint} W[ASCII]: {ListContent}", 
                clientEndPoint, ncList.ToString());
            
            bool listResult = _tcpServer?.SendToClient(clientEndPoint, listBytes) ?? false;
            if (!listResult)
            {
                _logger?.LogError("[文件列表] {ClientEndPoint} 发送列表内容失败", clientEndPoint);
                return;
            }

            // 发送包后缀
            if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketSuffix))
            {
                byte[] suffixBytes = ConvertToBytes(_protocolConfig.PacketSuffix);
                _logger?.LogDebug("[文件列表] {ClientEndPoint} 发送包后缀! 后缀: {PacketSuffix}, 字节(十六进制): {SuffixBytes}, 配置的后缀是{ConfigSuffix} 反向转换得到的应该是{ReversedSuffix}字符",
                    clientEndPoint, _protocolConfig.PacketSuffix,
                    suffixBytes.Length > 0 ? BytesTools.ByteArrToHex(suffixBytes, 0, suffixBytes.Length, true) : "空",
                    _protocolConfig.PacketSuffix,
                    suffixBytes.Length > 0 ? string.Join(",", suffixBytes.Select(b => b < 32 || b > 126 ? $"\\x{b:X2}" : ((char)b).ToString())) : "空");

                bool suffixResult = _tcpServer?.SendToClient(clientEndPoint, suffixBytes) ?? false;
                if (!suffixResult)
                {
                    _logger?.LogError("[文件列表] {ClientEndPoint} 发送包后缀失败", clientEndPoint);
                    return;
                }
            }

            _logger?.LogInformation("[文件列表] {ClientEndPoint} 3 写入机床NC文件列表完成!", clientEndPoint);
        }
        catch (Exception ex)
        {
            _logger?.LogError("[文件列表] {ClientEndPoint} 3 写入机床NC列表异常: {ErrorMessage}", 
                clientEndPoint, ex.Message);
        }
    }

    /// <summary>
    /// 解析NC文件消息
    /// </summary>
    private async Task ParseMessageIsNC(string clientEndPoint, string receiveMsg, string receivePath)
    {
        try
        {
            if (_isWaitUpload && !string.IsNullOrEmpty(_uploadFileName))
            {
                _logger?.LogInformation("[文件上传] {ClientEndPoint} 4 非QV指令,是否接收: {IsWaitUpload}, 文件名: {UploadFileName}",
                    clientEndPoint, _isWaitUpload, _uploadFileName);

                if (Directory.Exists(receivePath))
                {
                    string filePath = Path.Combine(receivePath, _uploadFileName);
                    
                    // 处理文件内容前后缀
                    string processedContent = receiveMsg;
                    string prefix = string.Empty;
                    string suffix = string.Empty;

                    // 检查是否从指定位置开始
                    int startNum = receiveMsg.IndexOf(_protocolConfig.FilePrefix);
                    if (startNum > 0)
                    {
                        processedContent = receiveMsg.Substring(startNum);
                    }

                    // 检查并添加缺失的前缀和后缀
                    if (!processedContent.Contains(_protocolConfig.FilePrefix))
                    {
                        prefix = _protocolConfig.FilePrefix;
                    }
                    if (!processedContent.Contains(_protocolConfig.FileSuffix))
                    {
                        suffix = _protocolConfig.FileSuffix;
                    }

                    await SaveNCToPC(clientEndPoint, filePath, prefix + processedContent + suffix);
                }
                else
                {
                    _logger?.LogWarning("[文件上传] {ClientEndPoint} 4 接收目录 [{ReceivePath}] 不存在!", clientEndPoint, receivePath);
                }
            }
            else
            {
                _logger?.LogWarning("[文件上传] {ClientEndPoint} 4 非QV指令忽略不执行上传,是否接收: {IsWaitUpload}, 文件名: {UploadFileName}",
                    clientEndPoint, _isWaitUpload, _uploadFileName);
            }

            // 重置上传状态
            _isWaitUpload = false;
            _uploadFileName = string.Empty;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "[文件上传] {ClientEndPoint} 4 非QV指令处理异常", clientEndPoint);
        }
    }

    /// <summary>
    /// 保存NC文件到PC
    /// </summary>
    private async Task SaveNCToPC(string clientEndPoint, string filePath, string strMsg)
    {
        try
        {
            // 确保目录存在
            string directory = Path.GetDirectoryName(filePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using var fs = new FileStream(filePath, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None);
            using var sw = new StreamWriter(fs, Encoding.Default);
            
            fs.SetLength(0);
            await sw.WriteAsync(strMsg);

            _logger?.LogInformation("[文件保存] {ClientEndPoint} 4 保存NC文件: {FilePath}", clientEndPoint, filePath);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "[文件保存] {ClientEndPoint} 4 保存NC文件异常: {FilePath}", clientEndPoint, filePath);
        }
    }

    /// <summary>
    /// 延时倒计时
    /// </summary>
    private bool IsCountDown(int delayTime)
    {
        if (delayTime <= 1000)
        {
            delayTime = 8000;
        }
        SpinWait.SpinUntil(() => false, delayTime);
        return true;
    }

    /// <summary>
    /// 确保目录存在
    /// </summary>
    private void EnsureDirectoryExists(string directoryPath)
    {
        if (!Directory.Exists(directoryPath))
        {
            Directory.CreateDirectory(directoryPath);
            _logger?.LogInformation("创建目录: {DirectoryPath}", directoryPath);
        }
    }

    /// <summary>
    /// 客户端认证
    /// </summary>
    private async Task<bool> AuthenticateClientAsync(TcpClient client, string clientEndPoint)
    {
        try
        {
            var stream = client.GetStream();
            byte[] authRequest = Encoding.UTF8.GetBytes("AUTH_REQUIRED\r\n");
            await stream.WriteAsync(authRequest, 0, authRequest.Length);

            byte[] buffer = new byte[1024];
            // int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);

            _logger?.LogInformation("客户端 {ClientEndPoint} 尝试连接", clientEndPoint);

            // 解析凭据 (格式: "username:password")
            string[] parts = clientEndPoint.Split(':', 2);
            if (parts.Length != 2)
            {
                byte[] authFailedMsg = Encoding.UTF8.GetBytes("AUTH_FAILED\r\n");
                await stream.WriteAsync(authFailedMsg, 0, authFailedMsg.Length);
                return false;
            }

            string username = parts[0];
            string password = parts[1];

            _logger?.LogInformation("客户端 {ClientEndPoint} 尝试认证用户: {Username}", clientEndPoint, username);

            // 然后检查DNC配置
            var dncConfigs = _dncConfigs;
            if (dncConfigs != null && dncConfigs.Any())
            {
                _logger?.LogInformation("客户端 {ClientEndPoint} 尝试通过DNC配置认证，可用配置数: {Count}", clientEndPoint, dncConfigs.Count);

                // 查找匹配的DNC配置
                var matchedConfig = dncConfigs.FirstOrDefault(c => c.Enabled && c.IpAddress == username);

                if (matchedConfig != null)
                {
                    _logger?.LogInformation("客户端 {ClientEndPoint} 通过DNC配置认证成功: {Username}, 设备: {DeviceCode}",
                        clientEndPoint, username, matchedConfig.DeviceCode);

                    // 设置认证信息
                    _clientAuthInfos.TryAdd(clientEndPoint, new ClientAuthInfo
                    {
                        Username = username,
                        DeviceCode = matchedConfig.DeviceCode,
                        Authenticated = true
                    });

                    byte[] authSuccess = Encoding.UTF8.GetBytes("AUTH_SUCCESS\r\n");
                    await stream.WriteAsync(authSuccess, 0, authSuccess.Length);
                    return true;
                }
            }

            // 认证失败
            _logger?.LogWarning("客户端 {ClientEndPoint} 认证失败，用户名或密码错误: {Username}", clientEndPoint, username);
            byte[] authFailed = Encoding.UTF8.GetBytes("AUTH_FAILED\r\n");
            await stream.WriteAsync(authFailed, 0, authFailed.Length);
            return false;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "客户端 {ClientEndPoint} 认证过程发生错误", clientEndPoint);

            try
            {
                // 尝试发送错误响应
                var stream = client.GetStream();
                byte[] authError = Encoding.UTF8.GetBytes("AUTH_FAILED\r\n");
                await stream.WriteAsync(authError, 0, authError.Length);
            }
            catch { /* 忽略发送错误的异常 */ }

            return false;
        }
    }

    /// <summary>
    /// 检查端口是否可用
    /// </summary>
    /// <param name="port">要检查的端口</param>
    /// <returns>如果端口可用返回true，否则返回false</returns>
    private bool IsPortAvailable(int port)
    {
        try
        {
            // 尝试在指定端口上创建一个TCP监听器
            using var socket = new Socket(
                AddressFamily.InterNetwork,
                SocketType.Stream,
                ProtocolType.Tcp);

            socket.SetSocketOption(
                SocketOptionLevel.Socket,
                SocketOptionName.ReuseAddress,
                true);

            socket.Bind(new IPEndPoint(IPAddress.Any, port));
            socket.Close();
            return true;
        }
        catch (SocketException)
        {
            return false;
        }
    }

    /// <summary>
    ///     停止
    /// </summary>
    public override async Task StopAsync()
    {
        Log.Information("正在停止TCP服务器...");

        try
        {
            // 取消所有后台任务
            if (_cancellationTokenSource != null)
            {
                if (!_cancellationTokenSource.IsCancellationRequested)
                {
                    _cancellationTokenSource.Cancel();
                }
                _cancellationTokenSource.Dispose();
                _cancellationTokenSource = null;
            }

            // 关闭所有连接的客户端
            int clientCount = _clients.Count;
            foreach (var client in _clients)
            {
                try
                {
                    client.Value.Close();
                    Log.Information("关闭客户端 {ClientEndPoint} 连接", client.Key);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "关闭客户端 {ClientEndPoint} 连接时发生错误", client.Key);
                }
            }
            _clients.Clear();
            Log.Information("已关闭所有 {ClientCount} 个客户端连接", clientCount);

            // 停止TCP服务器
            if (_tcpServer != null)
            {
                try
                {
                    _tcpServer.Stop();
                    _tcpServer = null;
                    Log.Information("TCP服务器已停止");
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "停止TCP服务器时发生错误");
                }
            }

            await Task.Delay(500); // 短暂延迟，确保资源释放

            // 确保垃圾回收运行，释放所有未使用的资源
            GC.Collect();
            GC.WaitForPendingFinalizers();

            Log.Information("TCP服务器已停止");

            // 清空客户端认证信息
            _clientAuthInfos.Clear();

            await base.StopAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "停止TCP服务器时发生错误");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     更新配置
    /// </summary>
    public override async Task UpdateConfigurationAsync(object configuration)
    {
        try
        {
            if (configuration is JsonElement jsonElement)
            {
                // 将JsonElement反序列化为SerialPortByTcpConfig对象
                var config = jsonElement.Deserialize<SerialPortByTcpConfig>(new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (config != null)
                {
                    _config.Port = config.Port;
                    _config.Enabled = config.Enabled;
                    _config.MaxConnections = config.MaxConnections;
                    _config.ConnectionTimeout = config.ConnectionTimeout;
                    _config.RequireAuthentication = config.RequireAuthentication;
                    _config.SendPath = config.SendPath;
                    _config.ReceivePath = config.ReceivePath;
                    _config.SendDelayTime = config.SendDelayTime;
                    _config.FileNameRule = config.FileNameRule;

                    // 更新协议类型并重新加载协议配置
                    if (!string.IsNullOrEmpty(config.ProtocolType) && _config.ProtocolType != config.ProtocolType)
                    {
                        _config.ProtocolType = config.ProtocolType;
                        InitProtocolConfig();
                    }

                    // 保存更新后的配置
                    SaveConfiguration();

                    // 如果服务正在运行且配置发生变更，需要重启服务
                    await StopAsync();

                    // 如果启用，则重新启动服务
                    if (_config.Enabled)
                    {
                        await StartAsync();
                    }
                }
            }
            else if (configuration is SerialPortByTcpConfig config)
            {
                // 直接使用SerialPortByTcpConfig对象
                _config.Port = config.Port;
                _config.Enabled = config.Enabled;
                _config.MaxConnections = config.MaxConnections;
                _config.ConnectionTimeout = config.ConnectionTimeout;
                _config.RequireAuthentication = config.RequireAuthentication;
                _config.SendPath = config.SendPath;
                _config.ReceivePath = config.ReceivePath;
                _config.SendDelayTime = config.SendDelayTime;
                _config.FileNameRule = config.FileNameRule;

                // 更新协议类型并重新加载协议配置
                if (!string.IsNullOrEmpty(config.ProtocolType) && _config.ProtocolType != config.ProtocolType)
                {
                    _config.ProtocolType = config.ProtocolType;
                    InitProtocolConfig();
                }

                SaveConfiguration(); // 保存配置
                await StopAsync();

                if (_config.Enabled)
                {
                    await StartAsync();
                }
            }

            await base.UpdateConfigurationAsync(configuration);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "更新TCP配置失败");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    /// 使用服务器发送响应消息到客户端
    /// </summary>
    private bool SendResponseToClientByServer(string clientEndPoint, string message)
    {
        if (_tcpServer == null)
        {
            _logger?.LogWarning("TCP服务器未初始化，无法发送响应");
            return false;
        }

        try
        {
            // 准备要发送的数据
            var dataList = new List<byte[]>();

            // 添加前缀
            if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketPrefix))
            {
                dataList.Add(ConvertToBytes(_protocolConfig.PacketPrefix));
            }

            // 添加消息内容
            dataList.Add(BytesTools.ASCIIToASCIIByteArr(message));

            // 添加后缀
            if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketSuffix))
            {
                dataList.Add(ConvertToBytes(_protocolConfig.PacketSuffix));
            }

            // 计算总长度
            int totalLength = dataList.Sum(arr => arr.Length);

            // 合并为一个字节数组
            byte[] data = new byte[totalLength];
            int offset = 0;
            foreach (var arr in dataList)
            {
                Buffer.BlockCopy(arr, 0, data, offset, arr.Length);
                offset += arr.Length;
            }

            // 使用TCP服务器发送数据
            bool result = _tcpServer.SendToClient(clientEndPoint, data);

            if (result)
            {
                Log.Information($"已通过服务器发送响应: {message}");
            }
            else
            {
                _logger?.LogWarning($"通过服务器发送响应失败: {message}");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "通过服务器发送响应消息失败");
            return false;
        }
    }

    /// <summary>
    /// 通过服务器将文件发送到客户端
    /// </summary>
    private bool SendFileToClientByServer(string clientEndPoint, string filePath)
    {
        if (_tcpServer == null || !File.Exists(filePath))
        {
            _logger?.LogWarning($"发送文件失败，TCP服务器未初始化或文件不存在: {filePath}");
            return false;
        }

        try
        {
            // 准备所有要发送的数据
            var dataSegments = new List<byte[]>();

            // 添加数据包前缀
            if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketPrefix))
            {
                dataSegments.Add(ConvertToBytes(_protocolConfig.PacketPrefix));
                Log.Information($"{DateTime.Now} 发送前缀: {_protocolConfig.PacketPrefix}");
            }

            // 读取文件内容
            using (var sr = new StreamReader(filePath, Encoding.Default))
            {
                string line;
                string lineBreak = _protocolConfig.LineBreak;

                while ((line = sr.ReadLine()) != null)
                {
                    string getLine = line;
                    // 添加换行符
                    if (line.IndexOf(lineBreak, StringComparison.Ordinal) == -1)
                    {
                        getLine = line + lineBreak;
                    }

                    dataSegments.Add(BytesTools.ASCIIToASCIIByteArr(getLine));
                    Log.Information($"{DateTime.Now} 添加行: {line}");
                }
            }

            // 添加数据包后缀
            if (!string.IsNullOrWhiteSpace(_protocolConfig.PacketSuffix))
            {
                dataSegments.Add(ConvertToBytes(_protocolConfig.PacketSuffix));
                Log.Information($"{DateTime.Now} 发送后缀: {_protocolConfig.PacketSuffix}");
            }

            // 计算总长度
            int totalLength = dataSegments.Sum(arr => arr.Length);

            // 合并为一个字节数组
            byte[] data = new byte[totalLength];
            int offset = 0;
            foreach (var segment in dataSegments)
            {
                Buffer.BlockCopy(segment, 0, data, offset, segment.Length);
                offset += segment.Length;
            }

            // 使用TCP服务器发送数据
            bool result = _tcpServer.SendToClient(clientEndPoint, data);

            if (result)
            {
                Log.Information($"通过服务器发送文件成功: {filePath}");
            }
            else
            {
                _logger?.LogWarning($"通过服务器发送文件失败: {filePath}");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, $"通过服务器发送文件时发生错误: {filePath}");
            return false;
        }
    }

    /// <summary>
    /// 获取用户特定的文件路径
    /// </summary>
    /// <param name="clientEndPoint">客户端端点</param>
    /// <param name="basePath">基本路径模板</param>
    /// <returns>实际文件路径</returns>
    private string GetUserSpecificPath(string clientEndPoint, string basePath)
    {
        // 如果有设备编码，使用设备编码
        string userIdentifier = clientEndPoint;
        // 替换路径中的占位符
        string path = string.Format(basePath, userIdentifier);
        // 返回完整路径
        return Path.Combine(AppContext.BaseDirectory, path);
    }
}

/// <summary>
/// 客户端连接信息
/// </summary>
public class ClientInfo
{
    /// <summary>
    /// 客户端标识
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// IP地址
    /// </summary>
    public string IpAddress { get; set; } = string.Empty;

    /// <summary>
    /// 端口号
    /// </summary>
    public int Port { get; set; }

    /// <summary>
    /// 连接时间
    /// </summary>
    public DateTime ConnectTime { get; set; }

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected { get; set; }
}

/// <summary>
/// 客户端认证信息
/// </summary>
public class ClientAuthInfo
{
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 设备编码
    /// </summary>
    public string DeviceCode { get; set; } = string.Empty;

    /// <summary>
    /// 是否已认证
    /// </summary>
    public bool Authenticated { get; set; }

    /// <summary>
    /// 认证时间
    /// </summary>
    public DateTime AuthTime { get; set; } = DateTime.Now;
}