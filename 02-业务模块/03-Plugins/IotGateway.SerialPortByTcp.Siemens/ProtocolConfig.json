{
  "Configs": [
    {
      "Type": "General", //通用必须配置，其它协议只须指定个性配置项
      "PacketPrefix": "12", //包头字符
      "PacketSuffix": "14", //包尾字符
      "FilePrefix": "%", //文件首字符 \u0025
      "FileSuffix": "%", //文件尾字符
      "FileExtension": "", //文件扩展名，如.MPF 默认为.*
      "OrderPrefix": "Q", //指令首字符
      "OrderSuffix": "V", //指令尾字符
      "ListFileHead": "O{0}\n", //获取网关NC文件列表 {0}为替换字符系统会依据具体文件名称替换，可通过NC设备描述字段自定义此头格式。
      "LineBreak": "\n" //换行符
    },
    {
      "Type": "Okuma",
      "PacketPrefix": "", //包头字符
      "PacketSuffix": "" //包尾字符//ListFileHead $O5502.MIN%
    },
    {
      "Type": "Siemens",
      "PacketPrefix": "", //包头字符
      "PacketSuffix": "\u001a", //包尾字符
      "FileSuffix": "M30",
      "FileExtension": ".MPF",
      "ListFileHead": "_N_{0}_MPF\n;$PATH=/_N_MPF_DIR\n"
    }
  ]
}