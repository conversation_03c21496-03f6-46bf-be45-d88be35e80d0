using Furion.DependencyInjection;
using Furion.DynamicApiController;
using IotGateway.Plugin.Core.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

/// <summary>
///     DNC服务
/// </summary>
[ApiDescriptionSettings(Name = "DncService")]
[Route("dnc")]
public class DncService : ITransient, IDynamicApiController
{
    /// <summary>
    ///     获取指定目录下的文件列表
    /// </summary>
    /// <param name="deviceCode">设备编码（必填）</param>
    /// <returns>文件信息列表，包含文件名、大小、类型等</returns>
    [HttpGet("GetFiles")]
    [AllowAnonymous]
    public List<FtpFileInfo> GetFiles(string deviceCode = "")
    {
        if (string.IsNullOrEmpty(deviceCode))
        {
            throw new ArgumentException("deviceCode不能为空");
        }

        var fileList = new List<FtpFileInfo>();

        // 获取SEND目录的文件
        var sendFiles = GetDirectoryFiles("SEND", deviceCode);
        fileList.AddRange(sendFiles);

        // 获取REC目录的文件
        var recFiles = GetDirectoryFiles("REC", deviceCode);
        fileList.AddRange(recFiles);

        // 按文件名排序返回
        return fileList.OrderBy(f => f.FileName).ToList();
    }

    /// <summary>
    /// 获取指定目录下的文件列表
    /// </summary>
    /// <param name="source">目录名(SEND或REC)</param>
    /// <param name="deviceCode">设备编码（必填）</param>
    /// <returns>文件信息列表</returns>
    private List<FtpFileInfo> GetDirectoryFiles(string source, string deviceCode)
    {
        var fileList = new List<FtpFileInfo>();
        var path = GetFtpPath(deviceCode, string.Empty, source);

        // 检查目录是否存在
        if (!Directory.Exists(path))
        {
            // 创建目录
            Directory.CreateDirectory(path);
            return fileList;
        }

        // 获取目录中的所有文件
        var files = Directory.GetFiles(path);

        // 处理每个文件，提取详细信息
        foreach (var file in files)
            try
            {
                var fileInfo = new FileInfo(file);

                // 创建文件信息模型
                var fileModel = new FtpFileInfo
                {
                    FileName = fileInfo.Name, // 文件名
                    ModifiedTime = fileInfo.LastWriteTime, // 修改时间
                    Size = fileInfo.Length + " B", // 文件大小
                    FileType = fileInfo.Extension, // 文件类型
                    FullPath = fileInfo.FullName, // 文件路径
                    Source = source, // 源目录
                };

                // 添加到文件列表
                fileList.Add(fileModel);
            }
            catch (Exception)
            {
                // 忽略处理单个文件时的错误，继续处理其他文件
            }

        return fileList;
    }

    /// <summary>
    ///     预览文件内容
    /// </summary>
    /// <param name="FileName">文件名</param>
    /// <param name="source">文件来源目录(SEND或REC)，默认为SEND</param>
    /// <param name="deviceCode">设备编码（必填）</param>
    /// <returns>文件内容或Base64编码的二进制内容</returns>
    [HttpGet("PreviewFile")]
    [AllowAnonymous]
    public string PreviewFile(string FileName, string deviceCode, string source = "SEND")
    {
        if (string.IsNullOrEmpty(deviceCode))
        {
            return "预览文件时发生错误: deviceCode不能为空";
        }

        try
        {
            // 构建文件路径
            var path = GetFtpPath(deviceCode, FileName, source);

            // 检查文件是否存在
            if (!File.Exists(path)) throw new Exception($"文件 {FileName} 不存在");

            // 获取文件信息
            var fileInfo = new FileInfo(path);

            // 检查文件大小，防止加载过大的文件
            if (fileInfo.Length > 10 * 1024 * 1024) // 10MB限制
                throw new Exception("文件过大，无法预览");

            // 读取文本文件
            var content = File.ReadAllText(path);
            return content;
        }
        catch (Exception ex)
        {
            // 返回错误信息
            return $"预览文件时发生错误: {ex.Message}";
        }
    }

    /// <summary>
    ///     批量预览文件内容
    /// </summary>
    /// <param name="request">批量预览请求</param>
    /// <returns>文件内容映射</returns>
    [HttpPost("BatchPreviewFiles")]
    [AllowAnonymous]
    public BatchPreviewResult BatchPreviewFiles(BatchPreviewRequest request)
    {
        var result = new BatchPreviewResult
        {
            FileContents = new Dictionary<string, string>(),
            FailedFiles = new Dictionary<string, string>()
        };

        if (request.FileNames == null || !request.FileNames.Any())
        {
            result.FailedFiles.Add("错误", "未提供文件列表");
            return result;
        }

        if (string.IsNullOrEmpty(request.DeviceCode))
        {
            result.FailedFiles.Add("错误", "设备编码不能为空");
            return result;
        }

        foreach (var fileItem in request.FileNames)
        {
            if (fileItem == null || string.IsNullOrEmpty(fileItem.FileName))
            {
                result.FailedFiles.Add("错误", "文件名不能为空");
                continue;
            }

            try
            {
                // 构建文件路径
                var path = GetFtpPath(request.DeviceCode, fileItem.FileName, fileItem.Source);

                // 检查文件是否存在
                if (!File.Exists(path))
                {
                    result.FailedFiles.Add(fileItem.FileName, "文件不存在");
                    continue;
                }

                // 获取文件信息
                var fileInfo = new FileInfo(path);

                // 检查文件大小，防止加载过大的文件
                if (fileInfo.Length > 10 * 1024 * 1024) // 10MB限制
                {
                    result.FailedFiles.Add(fileItem.FileName, "文件过大，无法预览");
                    continue;
                }

                // 根据是否为二进制文件选择不同的读取方式
                if (request.IsBinary)
                {
                    // 读取二进制文件并转换为Base64字符串
                    var fileBytes = File.ReadAllBytes(path);
                    var base64String = Convert.ToBase64String(fileBytes);
                    result.FileContents.Add(fileItem.FileName, base64String);
                }
                else
                {
                    // 读取文本文件
                    var content = File.ReadAllText(path);
                    result.FileContents.Add(fileItem.FileName, content);
                }
            }
            catch (Exception ex)
            {
                // 记录错误信息
                result.FailedFiles.Add(fileItem.FileName, ex.Message);
            }
        }

        return result;
    }

    /// <summary>
    ///     批量备份文件
    /// </summary>
    /// <param name="request">批量备份请求</param>
    /// <returns>备份结果</returns>
    [HttpPost("BatchBackupFiles")]
    [AllowAnonymous]
    public BatchOperationResult BatchBackupFiles(BatchFileRequest request)
    {
        var result = new BatchOperationResult
        {
            SuccessFiles = new List<string>(),
            FailedFiles = new Dictionary<string, string>()
        };

        if (request.FileNames == null || !request.FileNames.Any())
        {
            result.FailedFiles.Add("错误", "未提供文件列表");
            return result;
        }

        if (string.IsNullOrEmpty(request.DeviceCode))
        {
            result.FailedFiles.Add("错误", "设备编码不能为空");
            return result;
        }

        try
        {
            // 创建备份目录，使用时间戳命名
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var backupFolderName = $"Backup_{timestamp}";
            var backupPath = Path.Combine(AppContext.BaseDirectory, "RootFiles", backupFolderName);

            // 确保备份目录存在
            if (!Directory.Exists(backupPath)) Directory.CreateDirectory(backupPath);

            // 处理每个文件
            foreach (var fileItem in request.FileNames)
            {
                if (fileItem == null || string.IsNullOrEmpty(fileItem.FileName))
                {
                    result.FailedFiles.Add("错误", "文件名不能为空");
                    continue;
                }

                try
                {
                    // 源文件路径
                    var sourcePath = GetFtpPath(request.DeviceCode, fileItem.FileName, fileItem.Source);

                    // 检查源文件是否存在
                    if (!File.Exists(sourcePath))
                    {
                        result.FailedFiles.Add(fileItem.FileName, "文件不存在");
                        continue;
                    }

                    // 目标文件路径
                    var targetPath = Path.Combine(backupPath, fileItem.FileName);

                    // 复制文件
                    File.Copy(sourcePath, targetPath, true);

                    // 记录成功
                    result.SuccessFiles.Add(fileItem.FileName);
                }
                catch (Exception ex)
                {
                    // 记录失败
                    result.FailedFiles.Add(fileItem.FileName, ex.Message);
                }
            }

            result.Message = $"备份完成，成功: {result.SuccessFiles.Count}，失败: {result.FailedFiles.Count}，备份目录: {backupFolderName}";
            result.Success = result.SuccessFiles.Count > 0;
        }
        catch (Exception ex)
        {
            result.Message = $"备份过程中发生错误: {ex.Message}";
            result.Success = false;
        }

        return result;
    }

    /// <summary>
    ///     批量删除文件
    /// </summary>
    /// <param name="request">批量删除请求</param>
    /// <returns>删除结果</returns>
    [HttpPost("BatchDeleteFiles")]
    [AllowAnonymous]
    public BatchOperationResult BatchDeleteFiles(BatchFileRequest request)
    {
        var result = new BatchOperationResult
        {
            SuccessFiles = new List<string>(),
            FailedFiles = new Dictionary<string, string>()
        };

        if (request.FileNames == null || !request.FileNames.Any())
        {
            result.FailedFiles.Add("错误", "未提供文件列表");
            return result;
        }

        if (string.IsNullOrEmpty(request.DeviceCode))
        {
            result.FailedFiles.Add("错误", "设备编码不能为空");
            return result;
        }

        // 处理每个文件
        foreach (var fileItem in request.FileNames)
        {
            if (fileItem == null || string.IsNullOrEmpty(fileItem.FileName))
            {
                result.FailedFiles.Add("错误", "文件名不能为空");
                continue;
            }

            try
            {
                // 文件路径
                var filePath = GetFtpPath(request.DeviceCode, fileItem.FileName, fileItem.Source);

                // 检查文件是否存在
                if (!File.Exists(filePath))
                {
                    result.FailedFiles.Add(fileItem.FileName, "文件不存在");
                    continue;
                }

                // 删除文件
                File.Delete(filePath);

                // 记录成功
                result.SuccessFiles.Add(fileItem.FileName);
            }
            catch (Exception ex)
            {
                // 记录失败
                result.FailedFiles.Add(fileItem.FileName, ex.Message);
            }
        }

        result.Message = $"删除完成，成功: {result.SuccessFiles.Count}，失败: {result.FailedFiles.Count}";
        result.Success = result.SuccessFiles.Count > 0;

        return result;
    }

    /// <summary>
    ///     上传文件
    /// </summary>
    /// <returns>上传结果</returns>
    [HttpPost("UploadFile")]
    [AllowAnonymous]
    public object UploadFile(UploadFile uploadFile)
    {
        try
        {
            // 检查参数是否为空
            if (string.IsNullOrEmpty(uploadFile.Content) || string.IsNullOrEmpty(uploadFile.FileName))
                return new { Success = false, Message = "文件内容或文件名不能为空" };

            // 检查设备编码
            if (string.IsNullOrEmpty(uploadFile.DeviceCode))
                return new { Success = false, Message = "设备编码不能为空" };

            // 解码Base64内容
            byte[] fileBytes;
            try
            {
                fileBytes = Convert.FromBase64String(uploadFile.Content);
            }
            catch (FormatException)
            {
                return new { Success = false, Message = "文件内容格式不正确，请提供有效的Base64编码字符串" };
            }

            // 检查文件大小（限制为50MB）
            if (fileBytes.Length > 50 * 1024 * 1024)
                return new { Success = false, Message = "文件大小超过限制（最大50MB）" };

            // 确保目标目录存在
            var source = "SEND";
            var targetDirectory = GetFtpPath(uploadFile.DeviceCode, string.Empty, source);
            Console.WriteLine($"上传文件目录: {targetDirectory}");

            // 确保目录存在
            if (!Directory.Exists(targetDirectory))
                Directory.CreateDirectory(targetDirectory);

            // 构建文件保存路径
            var fileName = Path.GetFileName(uploadFile.FileName);
            var filePath = GetFtpPath(uploadFile.DeviceCode, fileName, source);

            // 如果文件已存在，则删除
            if (File.Exists(filePath))
                File.Delete(filePath);

            // 保存文件
            File.WriteAllBytes(filePath, fileBytes);

            // 获取文件信息
            var fileInfo = new FileInfo(filePath);
            var fileModel = new FtpFileInfo
            {
                FileName = fileInfo.Name,
                ModifiedTime = fileInfo.LastWriteTime,
                Size = fileInfo.Length + " B",
                FileType = fileInfo.Extension,
                FullPath = fileInfo.FullName,
                Source = source,
            };

            return new { Success = true, Message = "文件上传成功", FileInfo = fileModel };
        }
        catch (Exception ex)
        {
            return new { Success = false, Message = $"文件上传失败: {ex.Message}" };
        }
    }

    /// <summary>
    /// 获取FTP文件路径
    /// </summary>
    /// <param name="deviceCode">设备编码</param>
    /// <param name="fileName">文件名</param>
    /// <param name="source">来源目录(SEND或REC)</param>
    /// <returns>完整文件路径</returns>
    private string GetFtpPath(string deviceCode, string fileName, string source)
    {
        // 验证source有效性，只允许SEND或REC
        if (string.IsNullOrEmpty(source) || (source != "SEND" && source != "REC"))
        {
            source = "SEND"; // 默认值
        }

        // 构建目录路径
        var dirPath = Path.Combine(AppContext.BaseDirectory, "RootFiles", deviceCode, source);

        // 如果fileName为空，返回目录路径
        if (string.IsNullOrEmpty(fileName))
        {
            return dirPath;
        }

        // 返回完整文件路径
        return Path.Combine(dirPath, fileName);
    }
}

/// <summary>
///     上传文件请求
/// </summary>
public class UploadFile
{
    /// <summary>
    ///     文件内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    ///     文件名
    /// </summary>
    public string FileName { get; set; }

    /// <summary>
    ///     设备编码（必填）
    /// </summary>
    public string DeviceCode { get; set; }
}

/// <summary>
///     文件项目
/// </summary>
public class FileItem
{
    /// <summary>
    ///     文件名
    /// </summary>
    public string FileName { get; set; }

    /// <summary>
    ///     文件来源目录(SEND或REC)，默认为SEND
    /// </summary>
    public string Source { get; set; } = "SEND";
}

/// <summary>
///     批量文件操作请求
/// </summary>
public class BatchFileRequest
{
    /// <summary>
    ///     文件列表，每个文件包含文件名和来源目录
    /// </summary>
    public List<FileItem> FileNames { get; set; }

    /// <summary>
    ///     设备编码（必填）
    /// </summary>
    public string DeviceCode { get; set; }
}

/// <summary>
///     批量预览请求
/// </summary>
public class BatchPreviewRequest : BatchFileRequest
{
    /// <summary>
    ///     是否为二进制文件，默认为false
    /// </summary>
    public bool IsBinary { get; set; } = false;
}

/// <summary>
///     批量预览结果
/// </summary>
public class BatchPreviewResult
{
    /// <summary>
    ///     文件内容映射，键为文件名，值为文件内容
    /// </summary>
    public Dictionary<string, string> FileContents { get; set; }

    /// <summary>
    ///     失败的文件，键为文件名，值为错误信息
    /// </summary>
    public Dictionary<string, string> FailedFiles { get; set; }
}

/// <summary>
///     批量操作结果
/// </summary>
public class BatchOperationResult
{
    /// <summary>
    ///     操作是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    ///     操作结果消息
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    ///     成功处理的文件列表
    /// </summary>
    public List<string> SuccessFiles { get; set; }

    /// <summary>
    ///     处理失败的文件，键为文件名，值为错误信息
    /// </summary>
    public Dictionary<string, string> FailedFiles { get; set; }
}