using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace IotGateway.Plugin.Core;

/// <summary>
/// 插件托管服务
/// </summary>
public class PluginHostedService : IHostedService
{
  /// <summary>
  /// 日志记录器
  /// </summary>
  private readonly ILogger<PluginHostedService> _logger;

  /// <summary>
  /// 插件加载器
  /// </summary>
  private readonly PluginLoader _pluginLoader;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="logger">日志记录器</param>
  /// <param name="pluginLoader">插件加载器</param>
  public PluginHostedService(ILogger<PluginHostedService> logger, PluginLoader pluginLoader)
  {
    _logger = logger;
    _pluginLoader = pluginLoader;
  }

  /// <summary>
  /// 启动插件托管服务
  /// </summary>
  /// <param name="cancellationToken">取消令牌</param>
  public async Task StartAsync(CancellationToken cancellationToken)
  {
    await Task.Run(async () =>
    {
      _logger.LogInformation("插件托管服务正在启动...");

      try
      {
        // 加载所有插件
        var plugins = await _pluginLoader.LoadPluginsAsync();
        _logger.LogInformation("加载了 {count} 个插件", plugins.Count());
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "加载插件时发生错误");
      }
    });
  }

  /// <summary>
  /// 停止插件托管服务
  /// </summary>
  /// <param name="cancellationToken">取消令牌</param>
  public async Task StopAsync(CancellationToken cancellationToken)
  {
    _logger.LogInformation("Plugin Host Service is stopping...");

    // 获取所有已加载的插件
    var plugins = _pluginLoader.GetLoadedPlugins();

    // 停止所有插件
    foreach (var plugin in plugins)
    {
      try
      {
        await plugin.StopAsync();
        _logger.LogInformation("Plugin {name} stopped successfully", plugin.Name);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error stopping plugin {name}", plugin.Name);
      }
    }
  }
}