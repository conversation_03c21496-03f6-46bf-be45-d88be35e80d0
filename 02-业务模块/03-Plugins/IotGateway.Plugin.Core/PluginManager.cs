using Furion.DependencyInjection;
using IotGateway.Plugin.Core.Models;
using Feng.IotGateway.Core.SqlSugar;
using System;
using System.Linq.Expressions;

namespace IotGateway.Plugin.Core;

/// <summary>
/// 插件管理器
/// </summary>
public class PluginManager : ITransient
{
  /// <summary>
  /// 插件加载器
  /// </summary>
  private readonly PluginLoader _pluginLoader;

  /// <summary>
  /// DNC配置仓储
  /// </summary>
  private readonly SqlSugarRepository<DncConfig> _dncConfigRepository;

  /// <summary>
  /// 构造函数
  /// </summary>
  public PluginManager(PluginLoader pluginLoader, SqlSugarRepository<DncConfig> dncConfigRepository)
  {
    _pluginLoader = pluginLoader;
    _dncConfigRepository = dncConfigRepository;
  }

  /// <summary>
  /// 初始化插件管理器并自动启动配置的插件
  /// </summary>
  public async Task InitializeAsync()
  {
    // 加载所有插件
    var plugins = await _pluginLoader.LoadPluginsAsync();

    // 获取所有启用的DNC配置
    var dncConfigs = await _dncConfigRepository.GetListAsync(x => x.Enabled);

    // 检查每个插件是否需要自动启动
    foreach (var plugin in plugins)
    {
      // 查找与插件关联的DNC配置
      var pluginConfigs = dncConfigs.Where(x => x.ProtocolType == plugin.Name).ToList();
      foreach (var dncConfig in pluginConfigs)
      {
        // 设置DNC配置
        await plugin.SetDncConfigAsync(dncConfig);
      }

      if (plugin.GetConfigurationAsync().Result is IPluginConfig pluginConfig && pluginConfig.Enabled)
      {
        await StartPluginAsync(plugin.Name);
      }
    }
  }

  /// <summary>
  /// 上传并安装插件
  /// </summary>
  /// <param name="pluginFile">插件文件路径</param>
  public async Task InstallPluginAsync(string pluginFile)
  {
    try
    {
      Console.WriteLine($"PluginManager - 开始安装插件，文件路径: {pluginFile}");

      // 检查文件是否存在
      if (!File.Exists(pluginFile))
      {
        Console.WriteLine($"PluginManager - 错误: 插件文件不存在: {pluginFile}");
        throw new FileNotFoundException($"插件文件不存在: {pluginFile}");
      }

      // 检查文件大小
      var fileInfo = new FileInfo(pluginFile);
      Console.WriteLine($"PluginManager - 插件文件大小: {fileInfo.Length} 字节");

      if (fileInfo.Length == 0)
      {
        Console.WriteLine("PluginManager - 错误: 插件文件大小为0");
        throw new InvalidOperationException("插件文件大小为0，无法安装");
      }

      // 安装插件
      Console.WriteLine("PluginManager - 调用插件加载器安装插件");
      await _pluginLoader.InstallPluginAsync(pluginFile);
      Console.WriteLine("PluginManager - 插件安装成功");
    }
    catch (Exception ex)
    {
      Console.WriteLine($"PluginManager - 安装插件时发生异常: {ex.GetType().Name} - {ex.Message}");
      Console.WriteLine($"PluginManager - 异常堆栈: {ex.StackTrace}");
      throw;
    }
  }

  /// <summary>
  /// 卸载插件
  /// </summary>
  public async Task UninstallPluginAsync(string pluginName)
  {
    await _pluginLoader.UninstallPluginAsync(pluginName);
  }

  /// <summary>
  /// 启动插件
  /// </summary>
  public async Task StartPluginAsync(string pluginName)
  {
    var plugin = _pluginLoader.GetPlugin(pluginName);
    if (plugin != null)
    {
      // 获取关联的DNC配置
      var dncConfigs = await _dncConfigRepository.GetListAsync(x => x.ProtocolType == pluginName && x.Enabled);
      foreach (var dncConfig in dncConfigs)
      {
        // 设置DNC配置
        await plugin.SetDncConfigAsync(dncConfig);
      }
      // 启动插件
      await plugin.StartAsync();
    }
  }

  /// <summary>
  /// 停止插件
  /// </summary>
  public async Task StopPluginAsync(string pluginName)
  {
    var plugin = _pluginLoader.GetPlugin(pluginName);
    if (plugin != null)
    {
      await plugin.StopAsync();
    }
  }

  /// <summary>
  /// 启用/禁用插件
  /// </summary>
  /// <param name="pluginName">插件名称</param>
  /// <param name="enable">是否启用</param>
  /// <returns>操作结果</returns>
  public async Task TogglePluginEnable(string pluginName, bool enable)
  {
    var plugin = _pluginLoader.GetPlugin(pluginName);
    if (plugin != null)
    {
      await plugin.ToggleEnableAsync(enable);
    }
  }

  /// <summary>
  /// 获取插件配置
  /// </summary>
  public async Task<(object? Schema, object? Config)> GetPluginConfigurationAsync(string pluginName)
  {
    var plugin = _pluginLoader.GetPlugin(pluginName);
    if (plugin == null)
      return (null, null);

    var config = await plugin.GetConfigurationAsync();
    var schema = await plugin.GetConfigurationSchemaAsync();
    return (schema, config);
  }

  /// <summary>
  /// 更新插件配置
  /// </summary>
  public async Task UpdatePluginConfigurationAsync(string pluginName, object configuration)
  {
    var plugin = _pluginLoader.GetPlugin(pluginName);
    if (plugin != null)
    {
      await plugin.UpdateConfigurationAsync(configuration);
    }
  }

  /// <summary>
  /// 获取所有已加载的插件
  /// </summary>
  public IEnumerable<IPlugin> GetPlugins()
  {
    return _pluginLoader.GetLoadedPlugins();
  }

  /// <summary>
  /// 获取FTP服务器文件列表
  /// </summary>
  public async Task<Dictionary<string, List<FtpFileInfo>>> GetFtpFiles(string pluginName, string? configId = null, string? folderType = null)
  {
    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [PluginManager] 开始处理GetFtpFiles请求");
    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [PluginManager] 查找插件: {pluginName}");

    var plugin = _pluginLoader.GetPlugin(pluginName);
    if (plugin != null)
    {
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [PluginManager] 找到插件: {plugin.GetType().Name}");
      var result = await plugin.GetFtpFiles(configId, folderType);
      Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [PluginManager] 插件返回结果 - 文件夹数量: {result.Count}");
      return result;
    }

    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [PluginManager] 警告: 未找到插件 {pluginName}");
    return new Dictionary<string, List<FtpFileInfo>>();
  }

  /// <summary>
  /// 获取FTP服务器文件内容
  /// </summary>
  public async Task<string> GetFileContent(string pluginName, string filePath)
  {
    var plugin = _pluginLoader.GetPlugin(pluginName);
    if (plugin != null)
    {
      return await plugin.GetFileContent(filePath);
    }
    return string.Empty;
  }

  /// <summary>
  /// 根据插件名称获取DNC配置列表
  /// </summary>
  /// <param name="pluginName">插件名称</param>
  /// <returns>DNC配置列表</returns>
  public async Task<List<DncConfig>> GetDncConfigsByPluginNameAsync(string pluginName)
  {
    return await _dncConfigRepository.GetListAsync(x => x.ProtocolType == pluginName && x.Enabled);
  }

  /// <summary>
  /// 根据条件获取DNC配置列表
  /// </summary>
  /// <param name="predicate">查询条件</param>
  /// <returns>DNC配置列表</returns>
  public async Task<List<DncConfig>> GetDncConfigsAsync(Expression<Func<DncConfig, bool>> predicate)
  {
    return await _dncConfigRepository.GetListAsync(predicate);
  }

  /// <summary>
  /// 更新DNC配置到插件
  /// </summary>
  /// <param name="dncConfig">DNC配置</param>
  /// <returns>操作结果</returns>
  public async Task UpdateDncConfigToPluginAsync(DncConfig dncConfig)
  {
    if (string.IsNullOrEmpty(dncConfig.ProtocolType))
      return;

    var plugin = _pluginLoader.GetPlugin(dncConfig.ProtocolType);
    if (plugin != null)
    {
      await plugin.SetDncConfigAsync(dncConfig);
    }
  }
}