using Furion;
using Microsoft.Extensions.DependencyInjection;

namespace IotGateway.Plugin.Core;

/// <summary>
/// 
/// </summary>
[AppStartup(1)]
public class Startup : AppStartup
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        // 
        services.AddHostedService<PluginHostedService>();
    }
}