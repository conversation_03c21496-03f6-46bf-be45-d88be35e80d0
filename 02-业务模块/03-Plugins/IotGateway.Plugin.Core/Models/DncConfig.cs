using Feng.IotGateway.Core.Entity;
using SqlSugar;

namespace IotGateway.Plugin.Core.Models;

/// <summary>
/// DNC配置实体
/// </summary>
[SugarTable("dnc_config", "DNC配置表")]
public class DncConfig : EntityBaseId
{
  /// <summary>
  /// 设备编号
  /// </summary>
  [SugarColumn(ColumnDescription = "设备编号", Length = 50, IsNullable = false)]
  public string DeviceCode { get; set; }

  /// <summary>
  /// 设备名称
  /// </summary>
  [SugarColumn(ColumnDescription = "设备名称", Length = 100, IsNullable = false)]
  public string DeviceName { get; set; }

  /// <summary>
  /// 协议类型
  /// </summary>
  [SugarColumn(ColumnDescription = "协议类型", Length = 50, IsNullable = false)]
  public string ProtocolType { get; set; }
  
  /// <summary>
  /// IP地址
  /// </summary>
  [SugarColumn(ColumnDescription = "IP地址", Length = 50, IsNullable = false)]
  public string IpAddress { get; set; }

  /// <summary>
  /// 用户名
  /// </summary>
  [SugarColumn(ColumnDescription = "用户名", Length = 50)]
  public string Username { get; set; }

  /// <summary>
  /// 密码
  /// </summary>
  [SugarColumn(ColumnDescription = "密码", Length = 100)]
  public string Password { get; set; }

  /// <summary>
  /// 访问权限
  /// </summary>
  [SugarColumn(ColumnDescription = "访问权限", Length = 50)]
  public string AccessPermission { get; set; }

  /// <summary>
  /// 启用状态
  /// </summary>
  [SugarColumn(ColumnDescription = "启用状态", IsNullable = false)]
  public bool Enabled { get; set; } = true;

  /// <summary>
  /// 端口号
  /// </summary>
  [SugarColumn(ColumnDescription = "端口号")]
  public int? Port { get; set; }

  /// <summary>
  /// 备注
  /// </summary>
  [SugarColumn(ColumnDescription = "备注", Length = 500)]
  public string Remark { get; set; }

  /// <summary>
  /// 创建时间
  /// </summary>
  [SugarColumn(ColumnDescription = "创建时间", IsNullable = false)]
  public DateTime CreateTime { get; set; } = DateTime.Now;

  /// <summary>
  /// 更新时间
  /// </summary>
  [SugarColumn(ColumnDescription = "更新时间")]
  public DateTime? UpdateTime { get; set; }
}