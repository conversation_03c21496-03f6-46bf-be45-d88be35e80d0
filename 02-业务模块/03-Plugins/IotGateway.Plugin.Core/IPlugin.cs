using IotGateway.Plugin.Core.Models;
using System.Collections.Generic;

namespace IotGateway.Plugin.Core;

/// <summary>
/// 插件接口
/// </summary>
public interface IPlugin
{
  /// <summary>
  /// 插件名称
  /// </summary>
  string Name { get; }

  /// <summary>
  /// 插件版本
  /// </summary>
  string Version { get; }

  /// <summary>
  /// 插件描述
  /// </summary>
  string Description { get; }

  /// <summary>
  /// 是否启用
  /// </summary>
  bool Enabled { get; }

  /// <summary>
  /// 插件分类
  /// </summary>
  string Category { get; }

  /// <summary>
  /// 初始化插件
  /// </summary>
  Task InitializeAsync();

  /// <summary>
  /// 启动插件
  /// </summary>
  Task StartAsync();

  /// <summary>
  /// 停止插件
  /// </summary>
  Task StopAsync();

  /// <summary>
  /// 获取插件配置
  /// </summary>
  Task<object> GetConfigurationAsync();

  /// <summary>
  /// 更新插件配置
  /// </summary>
  Task UpdateConfigurationAsync(object configuration);

  /// <summary>
  /// 获取插件配置Schema
  /// </summary>
  Task<object?> GetConfigurationSchemaAsync();

  /// <summary>
  /// 启用或禁用插件
  /// </summary>
  /// <param name="enable">是否启用</param>
  /// <returns>操作结果</returns>
  Task ToggleEnableAsync(bool enable);

  /// <summary>
  /// 获取FTP服务器文件列表
  /// </summary>
  /// <param name="configId">配置ID</param>
  /// <param name="folderType">文件夹类型(SEND/REC)</param>
  /// <returns>以文件夹为键，文件信息列表为值的字典</returns>
  Task<Dictionary<string, List<FtpFileInfo>>> GetFtpFiles(string? configId = null, string? folderType = null);

  /// <summary>
  /// 获取FTP服务器文件内容
  /// </summary>
  Task<string> GetFileContent(string filePath);

  /// <summary>
  /// 设置DNC配置
  /// </summary>
  /// <param name="dncConfig">DNC配置</param>
  /// <returns>设置结果</returns>
  Task SetDncConfigAsync(Models.DncConfig dncConfig);

  /// <summary>
  /// 获取DNC配置
  /// </summary>
  /// <returns>DNC配置，如有多个则返回第一个</returns>
  Task<Models.DncConfig> GetDncConfigAsync();

  /// <summary>
  /// 获取DNC配置集合
  /// </summary>
  /// <returns>DNC配置集合</returns>
  Task<List<Models.DncConfig>> GetDncConfigsAsync();

  /// <summary>
  /// 获取指定设备编码的DNC配置
  /// </summary>
  /// <param name="deviceCode">设备编码</param>
  /// <returns>DNC配置</returns>
  Task<Models.DncConfig> GetDncConfigByDeviceCodeAsync(string deviceCode);

  /// <summary>
  /// 移除DNC配置
  /// </summary>
  /// <param name="id">配置ID</param>
  /// <returns>操作结果</returns>
  Task RemoveDncConfigAsync(long id);
}