using System.ComponentModel.DataAnnotations;
using Feng.IotGateway.Core.SqlSugar;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Furion.FriendlyException;
using IotGateway.Plugin.Core.DncConfigServer.Dtos;
using IotGateway.Plugin.Core.Models;
using Mapster;
using Microsoft.AspNetCore.Mvc;

namespace IotGateway.Plugin.Core.DncConfigServer;

/// <summary>
/// DNC配置服务
/// </summary>
[ApiDescriptionSettings("设备管理")]
public class DncConfigService : IDynamicApiController, ITransient
{
  private readonly SqlSugarRepository<DncConfig> _dncConfigRepository;
  private readonly PluginManager _pluginManager;

  /// <summary>
  /// 构造函数
  /// </summary>
  public DncConfigService(SqlSugarRepository<DncConfig> dncConfigRepository, PluginManager pluginManager)
  {
    _dncConfigRepository = dncConfigRepository;
    _pluginManager = pluginManager;
  }

  /// <summary>
  /// 分页查询DNC配置
  /// </summary>
  /// <param name="input">查询条件</param>
  /// <returns>DNC配置列表</returns>
  [HttpGet("/dncConfig/page")]
  public async Task<SqlSugarPagedList<DncConfigOutput>> Page([FromQuery] PageDncConfigInput input)
  {
    var query = _dncConfigRepository.AsQueryable()
        .WhereIF(!string.IsNullOrEmpty(input.SearchValue), u => u.DeviceCode.Contains(input.SearchValue)
        || u.DeviceName.Contains(input.SearchValue)
        || u.ProtocolType.Contains(input.SearchValue)
        || u.IpAddress.Contains(input.SearchValue)
        || u.ProtocolType.Contains(input.SearchValue))
        .WhereIF(input.Enabled.HasValue, u => u.Enabled == input.Enabled.Value);

    var pageResult = await query.ToPagedListAsync(input.PageNo, input.PageSize);
    var result = pageResult.Adapt<SqlSugarPagedList<DncConfigOutput>>();
    return result;
  }

  /// <summary>
  /// 获取DNC配置详情
  /// </summary>
  /// <param name="id">主键Id</param>
  /// <returns>DNC配置详情</returns>
  [HttpGet("/dncConfig/detail")]
  public async Task<DncConfig> GetDetail([Required] long id)
  {
    var dncConfig = await _dncConfigRepository.GetByIdAsync(id);
    if (dncConfig == null)
      throw Oops.Oh("DNC配置不存在");

    return dncConfig.Adapt<DncConfig>();
  }

  /// <summary>
  /// 添加DNC配置
  /// </summary>
  /// <param name="input">添加参数</param>
  /// <returns></returns>
  [HttpPost("/dncConfig/add")]
  public async Task Add([Required] AddDncConfigInput input)
  {
    // 检查设备编号是否已存在
    var existsByCode = await _dncConfigRepository.IsAnyAsync(u => u.DeviceCode == input.DeviceCode);
    if (existsByCode)
      throw Oops.Oh("设备编号已存在");

    // 检查IP地址和端口是否已存在
    if (input.Port.HasValue)
    {
      var existsByIpAndPort = await _dncConfigRepository.IsAnyAsync(u =>
          u.IpAddress == input.IpAddress && u.Port == input.Port.Value);
      if (existsByIpAndPort)
        throw Oops.Oh("IP地址和端口组合已存在");
    }

    var dncConfig = input.Adapt<DncConfig>();
    dncConfig.CreateTime = DateTime.Now;

    await _dncConfigRepository.InsertAsync(dncConfig);

    // 如果配置了插件名称且配置已启用，则同步更新到插件
    if (!string.IsNullOrEmpty(dncConfig.ProtocolType) && dncConfig.Enabled)
    {
      await _pluginManager.UpdateDncConfigToPluginAsync(dncConfig);
    }
  }

  /// <summary>
  /// 更新DNC配置
  /// </summary>
  /// <param name="input">更新参数</param>
  /// <returns></returns>
  [HttpPost("/dncConfig/update")]
  public async Task Update([Required] UpdateDncConfigInput input)
  {
    var dncConfig = await _dncConfigRepository.GetByIdAsync(input.Id);
    if (dncConfig == null)
      throw Oops.Oh("DNC配置不存在");

    // 检查设备编号是否已存在（排除自身）
    var existsByCode = await _dncConfigRepository.IsAnyAsync(u =>
        u.DeviceCode == input.DeviceCode && u.Id != input.Id);
    if (existsByCode)
      throw Oops.Oh("设备编号已存在");

    // 检查IP地址和端口是否已存在（排除自身）
    if (input.Port.HasValue)
    {
      var existsByIpAndPort = await _dncConfigRepository.IsAnyAsync(u =>
          u.IpAddress == input.IpAddress && u.Port == input.Port.Value && u.Id != input.Id);
      if (existsByIpAndPort)
        throw Oops.Oh("IP地址和端口组合已存在");
    }

    input.Adapt(dncConfig);
    dncConfig.UpdateTime = DateTime.Now;

    await _dncConfigRepository.UpdateAsync(dncConfig);

    // 如果配置了插件名称且配置已启用，则同步更新到插件
    if (!string.IsNullOrEmpty(dncConfig.ProtocolType) && dncConfig.Enabled)
    {
      await _pluginManager.UpdateDncConfigToPluginAsync(dncConfig);
    }
  }

  /// <summary>
  /// 删除DNC配置
  /// </summary>
  /// <param name="input">删除参数</param>
  /// <returns></returns>
  [HttpPost("/dncConfig/delete")]
  public async Task Delete([Required] DeleteDncConfigInput input)
  {
    var dncConfig = await _dncConfigRepository.GetByIdAsync(input.Id);
    if (dncConfig == null)
      throw Oops.Oh("DNC配置不存在");

    await _dncConfigRepository.DeleteAsync(dncConfig);
  }

  /// <summary>
  /// 批量删除DNC配置
  /// </summary>
  /// <param name="ids">主键Id集合</param>
  /// <returns></returns>
  [HttpPost("/dncConfig/batchDelete")]
  public async Task BatchDelete([Required] List<long> ids)
  {
    if (ids == null || ids.Count == 0)
      throw Oops.Oh("请选择要删除的记录");

    await _dncConfigRepository.DeleteAsync(u => ids.Contains(u.Id));
  }

  /// <summary>
  /// 修改DNC配置启用状态
  /// </summary>
  /// <param name="input">修改参数</param>
  /// <returns></returns>
  [HttpPost("/dncConfig/changeEnabled")]
  public async Task ChangeEnabled([Required] ChangeEnabledInput input)
  {
    var dncConfig = await _dncConfigRepository.GetByIdAsync(input.Id);
    if (dncConfig == null)
      throw Oops.Oh("DNC配置不存在");

    dncConfig.Enabled = input.Enabled;
    dncConfig.UpdateTime = DateTime.Now;

    await _dncConfigRepository.UpdateAsync(dncConfig);

    // 如果配置了插件名称，根据启用状态同步到插件
    if (!string.IsNullOrEmpty(dncConfig.ProtocolType))
    {
      if (dncConfig.Enabled)
      {
        await _pluginManager.UpdateDncConfigToPluginAsync(dncConfig);
      }
    }
  }

  /// <summary>
  /// 根据插件名称获取DNC配置
  /// </summary>
  /// <param name="pluginName">插件名称</param>
  /// <returns>DNC配置</returns>
  [HttpGet("/dncConfig/getByPluginName")]
  public async Task<List<DncConfigOutput>> GetByPluginName([Required] string pluginName)
  {
    var dncConfigs = await _dncConfigRepository.GetListAsync(u => u.ProtocolType == pluginName && u.Enabled);
    if (dncConfigs == null || dncConfigs.Count == 0)
      throw Oops.Oh($"未找到插件 {pluginName} 相关的DNC配置");

    return dncConfigs.Adapt<List<DncConfigOutput>>();
  }
}