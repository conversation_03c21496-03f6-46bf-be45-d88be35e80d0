using System.ComponentModel.DataAnnotations;
using Feng.IotGateway.Core.Base;

namespace IotGateway.Plugin.Core.DncConfigServer.Dtos;

/// <summary>
/// DNC配置输出
/// </summary>
public class DncConfigOutput
{
  /// <summary>
  /// 主键
  /// </summary>
  public long Id { get; set; }

  /// <summary>
  /// 设备编号
  /// </summary>
  public string DeviceCode { get; set; }

  /// <summary>
  /// 设备名称
  /// </summary>
  public string DeviceName { get; set; }

  /// <summary>
  /// 协议类型
  /// </summary>
  public string ProtocolType { get; set; }

  /// <summary>
  /// IP地址
  /// </summary>
  public string IpAddress { get; set; }

  /// <summary>
  /// 用户名
  /// </summary>
  public string Username { get; set; }

  /// <summary>
  /// 访问权限
  /// </summary>
  public string AccessPermission { get; set; }

  /// <summary>
  /// 启用状态
  /// </summary>
  public bool Enabled { get; set; }

  /// <summary>
  /// 端口号
  /// </summary>
  public int? Port { get; set; }

  /// <summary>
  /// 备注
  /// </summary>
  public string Remark { get; set; }

  /// <summary>
  /// 创建时间
  /// </summary>
  public DateTime CreateTime { get; set; }

  /// <summary>
  /// 更新时间
  /// </summary>
  public DateTime? UpdateTime { get; set; }
}

/// <summary>
/// DNC配置添加输入
/// </summary>
public class AddDncConfigInput
{
  /// <summary>
  /// 设备编号
  /// </summary>
  [Required(ErrorMessage = "设备编号不能为空")]
  [MaxLength(50, ErrorMessage = "设备编号长度不能超过50")]
  public string DeviceCode { get; set; }

  /// <summary>
  /// 设备名称
  /// </summary>
  [Required(ErrorMessage = "设备名称不能为空")]
  [MaxLength(100, ErrorMessage = "设备名称长度不能超过100")]
  public string DeviceName { get; set; }

  /// <summary>
  /// 协议类型
  /// </summary>
  [Required(ErrorMessage = "协议类型不能为空")]
  [MaxLength(50, ErrorMessage = "协议类型长度不能超过50")]
  public string ProtocolType { get; set; }

  /// <summary>
  /// IP地址
  /// </summary>
  [Required(ErrorMessage = "IP地址不能为空")]
  [MaxLength(50, ErrorMessage = "IP地址长度不能超过50")]
  public string IpAddress { get; set; }

  /// <summary>
  /// 用户名
  /// </summary>
  [MaxLength(50, ErrorMessage = "用户名长度不能超过50")]
  public string Username { get; set; }

  /// <summary>
  /// 密码
  /// </summary>
  [MaxLength(100, ErrorMessage = "密码长度不能超过100")]
  public string Password { get; set; }

  /// <summary>
  /// 访问权限
  /// </summary>
  [MaxLength(50, ErrorMessage = "访问权限长度不能超过50")]
  public string AccessPermission { get; set; }

  /// <summary>
  /// 启用状态
  /// </summary>
  public bool Enabled { get; set; } = true;

  /// <summary>
  /// 端口号
  /// </summary>
  public int? Port { get; set; }

  /// <summary>
  /// 备注
  /// </summary>
  [MaxLength(500, ErrorMessage = "备注长度不能超过500")]
  public string Remark { get; set; }
}

/// <summary>
/// DNC配置更新输入
/// </summary>
public class UpdateDncConfigInput : AddDncConfigInput
{
  /// <summary>
  /// 主键
  /// </summary>
  [Required(ErrorMessage = "主键不能为空")]
  public long Id { get; set; }
}

/// <summary>
/// DNC配置删除输入
/// </summary>
public class DeleteDncConfigInput
{
  /// <summary>
  /// 主键Id
  /// </summary>
  [Required(ErrorMessage = "主键不能为空")]
  public long Id { get; set; }
}

/// <summary>
/// DNC配置分页查询输入
/// </summary>
public class PageDncConfigInput : BasePageInput
{
  /// <summary>
  /// 启用状态（空表示全部）
  /// </summary>
  public bool? Enabled { get; set; }
}

/// <summary>
/// DNC配置启用/禁用输入
/// </summary>
public class ChangeEnabledInput
{
  /// <summary>
  /// 主键Id
  /// </summary>
  [Required(ErrorMessage = "主键不能为空")]
  public long Id { get; set; }

  /// <summary>
  /// 启用状态
  /// </summary>
  public bool Enabled { get; set; }
}