namespace IotGateway.Plugin.Core;

/// <summary>
/// 插件相关常量
/// </summary>
public static class Constants
{
  /// <summary>
  /// 插件目录名称
  /// </summary>
  public const string PluginFolderName = "plugins/service";

  /// <summary>
  /// 获取插件目录的完整路径
  /// </summary>
  /// <returns>插件目录完整路径</returns>
  public static string GetPluginPath()
  {
    // 获取应用程序当前目录
    var baseDir = AppDomain.CurrentDomain.BaseDirectory;
    // 组合插件目录路径
    return Path.Combine(baseDir, PluginFolderName);
  }
}