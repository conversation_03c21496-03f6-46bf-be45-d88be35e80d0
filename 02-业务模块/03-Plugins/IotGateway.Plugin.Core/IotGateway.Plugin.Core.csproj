<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
        <DebugType>none</DebugType>
        <DocumentationFile>IotGateway.Plugin.Core.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <DocumentationFile>IotGateway.Plugin.Core.xml</DocumentationFile>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\04-架构核心\IotGateway.Core\IotGateway.Core.csproj" />
    </ItemGroup>

</Project> 