using System.Reflection;
using Furion.DependencyInjection;
using System.IO.Compression;
using IotGateway.Plugin.Core.Models;
using Feng.IotGateway.Core.SqlSugar;
using SqlSugar;

namespace IotGateway.Plugin.Core;

/// <summary>
/// 插件加载器
/// </summary>
public class PluginLoader : ISingleton
{
  /// <summary>
  /// 插件目录
  /// </summary>
  private readonly string _pluginPath;

  /// <summary>
  /// 已加载的插件列表
  /// </summary>
  private readonly Dictionary<string, IPlugin> _loadedPlugins;

  /// <summary>
  /// DNC配置仓储
  /// </summary>
  private readonly ISqlSugarClient _db;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="db">DNC配置仓储</param>
  public PluginLoader(ISqlSugarClient db)
  {
    _pluginPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "plugins/services");
    _loadedPlugins = new Dictionary<string, IPlugin>();
    _db = db;

    // 确保插件目录存在
    if (!Directory.Exists(_pluginPath))
    {
      Directory.CreateDirectory(_pluginPath);
    }
  }

  /// <summary>
  /// 加载插件
  /// </summary>
  /// <returns>加载的插件列表</returns>
  public async Task<IEnumerable<IPlugin>> LoadPluginsAsync()
  {
    // 清空已加载的插件列表，以便重新加载
    _loadedPlugins.Clear();
    // 记录已加载的程序集，避免重复加载
    HashSet<string> loadedAssemblies = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

    // 递归获取插件目录下所有dll文件
    var pluginFiles = GetPluginFiles(_pluginPath);
    foreach (var pluginFile in pluginFiles) // 遍历插件文件
    {
      try
      {
        // 检查是否已加载相同的程序集
        string assemblyName = Path.GetFileName(pluginFile);
        if (loadedAssemblies.Contains(assemblyName))
        {
          continue;
        }

        // 加载程序集
        Assembly assembly;
        try
        {
          assembly = Assembly.LoadFrom(pluginFile);
        }
        catch (Exception ex)
        {
          Console.WriteLine($"PluginLoader - 加载程序集失败: {ex.GetType().Name} - {ex.Message}");
          continue;
        }

        loadedAssemblies.Add(assemblyName);

        // 查找实现了IPlugin接口的类型
        Type[] allTypes;
        try
        {
          allTypes = assembly.GetTypes();
        }
        catch (ReflectionTypeLoadException ex)
        {
          foreach (var loaderEx in ex.LoaderExceptions.Where(e => e != null))
          {
            Console.WriteLine($"PluginLoader - 加载器异常: {loaderEx.Message}");
          }
          continue;
        }
        catch (Exception ex)
        {
          Console.WriteLine($"PluginLoader - 获取类型时发生异常: {ex.GetType().Name} - {ex.Message}");
          continue;
        }

        var pluginTypes = allTypes.Where(t => typeof(IPlugin).IsAssignableFrom(t) && !t.IsAbstract).ToList();

        foreach (var pluginType in pluginTypes)
        {
          try
          {
            // 创建插件实例
            object? pluginInstance;
            try
            {
              pluginInstance = Activator.CreateInstance(pluginType);
            }
            catch (Exception ex)
            {
              continue;
            }

            if (pluginInstance is IPlugin plugin)
            {

              // 检查是否存在同名插件
              if (_loadedPlugins.ContainsKey(plugin.Name))
              {
                continue;
              }

              await InitializePluginAsync(plugin);

              _loadedPlugins[plugin.Name] = plugin;
              Console.WriteLine($"PluginLoader - 成功加载插件: {plugin.Name} 从文件: {pluginFile}");
            }
          }
          catch (Exception ex)
          {
            Console.WriteLine($"PluginLoader - 创建插件实例失败 {pluginType.FullName}: {ex.Message}");
            Console.WriteLine($"PluginLoader - 异常堆栈: {ex.StackTrace}");
          }
        }
      }
      catch (ReflectionTypeLoadException ex)
      {
        Console.WriteLine($"PluginLoader - 加载插件类型失败 {pluginFile}: {ex.Message}");
      }
      catch (Exception ex)
      {
        Console.WriteLine($"PluginLoader - 加载插件失败 {pluginFile}: {ex.Message}");
        Console.WriteLine($"PluginLoader - 异常堆栈: {ex.StackTrace}");
      }
    }

    Console.WriteLine($"PluginLoader - 插件加载完成，共加载 {_loadedPlugins.Count} 个插件");

    // 加载DNC配置
    try
    {
      Console.WriteLine("PluginLoader - 开始加载DNC配置");
      var dncConfigs = await _db.Queryable<DncConfig>().Where(x => x.Enabled).ToListAsync();
      Console.WriteLine($"PluginLoader - 成功加载 {dncConfigs.Count} 个DNC配置");

      // 为每个插件设置对应的DNC配置
      foreach (var plugin in _loadedPlugins.Values)
      {
        var pluginConfigs = dncConfigs.Where(x => x.ProtocolType == plugin.Name).ToList();
        if (pluginConfigs.Any())
        {
          Console.WriteLine($"PluginLoader - 为插件 {plugin.Name} 设置 {pluginConfigs.Count} 个DNC配置");
          foreach (var dncConfig in pluginConfigs)
          {
            await plugin.SetDncConfigAsync(dncConfig);
          }
        }
        else
        {
          Console.WriteLine($"PluginLoader - 插件 {plugin.Name} 没有找到匹配的DNC配置");
        }
      }
    }
    catch (Exception ex)
    {
      Console.WriteLine($"PluginLoader - 加载DNC配置失败: {ex.Message}");
      Console.WriteLine($"PluginLoader - 异常堆栈: {ex.StackTrace}");
    }

    return _loadedPlugins.Values;
  }

  /// <summary>
  /// 递归获取目录下所有dll文件
  /// </summary>
  /// <param name="directory">起始目录</param>
  /// <returns>所有dll文件路径</returns>
  private IEnumerable<string> GetPluginFiles(string directory)
  {
    // 获取当前目录下的dll文件
    var files = Directory.GetFiles(directory, "*.dll");

    // 递归获取子目录中的dll文件
    foreach (var subDirectory in Directory.GetDirectories(directory))
    {
      files = files.Concat(GetPluginFiles(subDirectory)).ToArray();
    }

    return files;
  }

  /// <summary>
  /// 安装插件
  /// </summary>
  /// <param name="pluginFile">插件文件路径</param>
  public async Task InstallPluginAsync(string pluginFile)
  {
    try
    {
      Console.WriteLine($"PluginLoader - 开始安装插件，文件路径: {pluginFile}");

      // 检查文件是否存在
      if (!File.Exists(pluginFile))
      {
        Console.WriteLine($"PluginLoader - 错误: 插件文件不存在: {pluginFile}");
        throw new FileNotFoundException($"插件文件不存在: {pluginFile}");
      }

      // 检查文件大小
      var fileInfo = new FileInfo(pluginFile);
      Console.WriteLine($"PluginLoader - 插件文件大小: {fileInfo.Length} 字节");

      // 尝试从原始文件名中获取实际扩展名（用于处理临时文件的情况）
      string originalFileName = Path.GetFileName(pluginFile);
      string actualExtension = ".dll"; // 默认扩展名

      // 检查文件内容的前几个字节来判断是否为ZIP文件
      bool isZipFile = false;
      try
      {
        using (FileStream fs = new FileStream(pluginFile, FileMode.Open, FileAccess.Read))
        {
          if (fs.Length > 4)
          {
            byte[] header = new byte[4];
            fs.Read(header, 0, 4);
            // ZIP文件的魔数是 PK\x03\x04 (0x50 0x4B 0x03 0x04)
            if (header[0] == 0x50 && header[1] == 0x4B && header[2] == 0x03 && header[3] == 0x04)
            {
              isZipFile = true;
              actualExtension = ".zip";
              Console.WriteLine("PluginLoader - 检测到ZIP文件格式（通过文件头识别）");
            }
          }
        }
      }
      catch (Exception ex)
      {
        Console.WriteLine($"PluginLoader - 检查文件格式时出错: {ex.Message}");
      }

      // 如果文件名中包含.zip，也认为是ZIP文件
      if (originalFileName.ToLower().Contains(".zip"))
      {
        isZipFile = true;
        actualExtension = ".zip";
        Console.WriteLine("PluginLoader - 检测到ZIP文件格式（通过文件名识别）");
      }

      Console.WriteLine($"PluginLoader - 识别的文件类型: {(isZipFile ? "ZIP文件" : "DLL文件")}");

      if (isZipFile)
      {
        // 处理ZIP压缩包
        Console.WriteLine("PluginLoader - 开始解压安装ZIP文件");
        await InstallZipPluginAsync(pluginFile);
      }
      else
      {
        // 复制插件文件到插件目录，确保使用.dll扩展名
        string fileName = Path.GetFileNameWithoutExtension(pluginFile) + ".dll";
        var targetPath = Path.Combine(_pluginPath, fileName);
        Console.WriteLine($"PluginLoader - 复制DLL文件到目标路径: {targetPath}");

        try
        {
          File.Copy(pluginFile, targetPath, true);
          Console.WriteLine($"PluginLoader - 文件复制成功，目标文件大小: {new FileInfo(targetPath).Length} 字节");
        }
        catch (Exception ex)
        {
          Console.WriteLine($"PluginLoader - 复制文件时发生异常: {ex.GetType().Name} - {ex.Message}");
          throw;
        }
      }

      // 重新加载插件
      Console.WriteLine("PluginLoader - 开始重新加载所有插件");
      await LoadPluginsAsync();
      Console.WriteLine("PluginLoader - 插件重新加载完成");
    }
    catch (Exception ex)
    {
      Console.WriteLine($"PluginLoader - 安装插件过程中发生异常: {ex.GetType().Name} - {ex.Message}");
      Console.WriteLine($"PluginLoader - 异常堆栈: {ex.StackTrace}");
      throw;
    }
  }

  /// <summary>
  /// 安装ZIP格式的插件包
  /// </summary>
  /// <param name="zipFile">ZIP文件路径</param>
  private async Task InstallZipPluginAsync(string zipFile)
  {
    try
    {
      Console.WriteLine($"PluginLoader - 开始安装ZIP插件: {zipFile}");

      // 创建临时ZIP文件（如果原文件不是.zip扩展名）
      string tempZipFile = zipFile;
      bool useTempFile = false;

      if (!zipFile.ToLower().EndsWith(".zip"))
      {
        tempZipFile = Path.Combine(Path.GetTempPath(), Path.GetFileNameWithoutExtension(zipFile) + ".zip");
        Console.WriteLine($"PluginLoader - 创建临时ZIP文件: {tempZipFile}");
        File.Copy(zipFile, tempZipFile, true);
        useTempFile = true;
      }

      // 检查ZIP文件是否有效
      try
      {
        using (var archive = System.IO.Compression.ZipFile.OpenRead(tempZipFile))
        {
          Console.WriteLine($"PluginLoader - ZIP文件有效，包含 {archive.Entries.Count} 个条目");

          // 检查是否包含DLL文件
          var hasDllFiles = archive.Entries.Any(e => e.FullName.ToLower().EndsWith(".dll"));
          if (!hasDllFiles)
          {
            Console.WriteLine("PluginLoader - 警告: ZIP文件中未找到DLL文件");
          }
          else
          {
            Console.WriteLine("PluginLoader - ZIP文件中包含DLL文件");
          }
        }
      }
      catch (Exception ex)
      {
        Console.WriteLine($"PluginLoader - 错误: ZIP文件无效或已损坏: {ex.Message}");

        // 清理临时文件
        if (useTempFile && File.Exists(tempZipFile))
        {
          try { File.Delete(tempZipFile); } catch { }
        }

        throw new InvalidOperationException($"ZIP文件无效或已损坏: {ex.Message}", ex);
      }

      // 获取原始文件名（不带路径和扩展名）作为插件文件夹名
      string originalFileName = Path.GetFileNameWithoutExtension(zipFile);
      if (originalFileName.Contains("."))
      {
        // 如果文件名中还有点，可能是因为有多个扩展名，如ftp.plugin.tmp
        // 尝试提取真正的文件名
        var parts = originalFileName.Split('.');
        if (parts.Length > 1)
        {
          originalFileName = string.Join(".", parts.Take(parts.Length - 1));
        }
      }

      // 创建唯一的目标目录名称
      var pluginFolderName = originalFileName;
      var pluginDirectory = Path.Combine(_pluginPath, pluginFolderName);
      Console.WriteLine($"PluginLoader - 目标插件目录: {pluginDirectory}");

      // 确保目标目录存在
      if (!Directory.Exists(pluginDirectory))
      {
        Console.WriteLine($"PluginLoader - 创建插件目录: {pluginDirectory}");
        Directory.CreateDirectory(pluginDirectory);
      }
      else
      {
        Console.WriteLine($"PluginLoader - 插件目录已存在，将覆盖现有文件");

        // 尝试清空目录以避免冲突
        try
        {
          foreach (var file in Directory.GetFiles(pluginDirectory))
          {
            File.Delete(file);
          }
          Console.WriteLine("PluginLoader - 已清空现有插件目录中的文件");
        }
        catch (Exception ex)
        {
          Console.WriteLine($"PluginLoader - 清空目录时出错: {ex.Message}");
        }
      }

      try
      {
        // 解压ZIP文件到目标目录
        Console.WriteLine("PluginLoader - 开始解压ZIP文件...");
        ZipFile.ExtractToDirectory(tempZipFile, pluginDirectory, true);

        // 检查解压结果
        var extractedFiles = Directory.GetFiles(pluginDirectory, "*.*", SearchOption.AllDirectories);
        Console.WriteLine($"PluginLoader - 解压完成，共解压出 {extractedFiles.Length} 个文件");

        // 列出解压的DLL文件
        var dllFiles = extractedFiles.Where(f => Path.GetExtension(f).ToLowerInvariant() == ".dll").ToList();
        Console.WriteLine($"PluginLoader - 解压出的DLL文件数量: {dllFiles.Count}");
        foreach (var dll in dllFiles)
        {
          Console.WriteLine($"PluginLoader - 解压的DLL文件: {Path.GetFileName(dll)}");
        }

        if (dllFiles.Count == 0)
        {
          Console.WriteLine("PluginLoader - 警告: 解压后未找到DLL文件，插件可能无法加载");
        }
      }
      catch (Exception ex)
      {
        Console.WriteLine($"PluginLoader - 解压ZIP文件时发生异常: {ex.GetType().Name} - {ex.Message}");
        throw;
      }
      finally
      {
        // 清理临时文件
        if (useTempFile && File.Exists(tempZipFile))
        {
          try
          {
            File.Delete(tempZipFile);
            Console.WriteLine($"PluginLoader - 临时ZIP文件已删除: {tempZipFile}");
          }
          catch (Exception ex)
          {
            Console.WriteLine($"PluginLoader - 删除临时ZIP文件失败: {ex.Message}");
          }
        }
      }

      Console.WriteLine($"PluginLoader - 成功解压插件包到: {pluginDirectory}");
    }
    catch (Exception ex)
    {
      Console.WriteLine($"PluginLoader - 安装ZIP插件过程中发生异常: {ex.GetType().Name} - {ex.Message}");
      Console.WriteLine($"PluginLoader - 异常堆栈: {ex.StackTrace}");
      throw;
    }
  }

  /// <summary>
  /// 卸载插件
  /// </summary>
  /// <param name="pluginName">插件名称</param>
  public async Task UninstallPluginAsync(string pluginName)
  {
    if (_loadedPlugins.TryGetValue(pluginName, out var plugin))
    {
      // 停止插件
      await plugin.StopAsync();

      // 从已加载插件列表中移除
      _loadedPlugins.Remove(pluginName);

      // TODO: 删除插件文件
      // 注意：由于文件可能被锁定，可能需要在应用重启后才能删除
    }
  }

  /// <summary>
  /// 获取已加载的插件
  /// </summary>
  public IEnumerable<IPlugin> GetLoadedPlugins()
  {
    return _loadedPlugins.Values;
  }

  /// <summary>
  /// 获取指定插件
  /// </summary>
  public IPlugin? GetPlugin(string pluginName)
  {
    return _loadedPlugins.TryGetValue(pluginName, out var plugin) ? plugin : null;
  }

  /// <summary>
  /// 初始化插件
  /// </summary>
  private async Task InitializePluginAsync(IPlugin plugin)
  {
    try
    {
      await plugin.InitializeAsync();
    }
    catch (Exception ex)
    {
      Console.WriteLine($"Failed to initialize plugin {plugin.Name}: {ex.Message}");
    }
  }
}