using Common.Models;
using Feng.IotGateway.Core.Const;
using Feng.IotGateway.WebSocket;
using Furion.DependencyInjection;
using Furion.EventBus;
using Furion.FriendlyException;
using Furion.JsonSerialization;
using Furion.Logging;
using IotGateway.ModbusServer.Models;
using System.Text;
using HslCommunication.ModBus;
using IotGateway.Application.ServiceConfig.Dtos;

namespace IotGateway.ModbusServer;

/// <summary>
/// Modbus TCP服务端
/// 提供Modbus TCP服务器功能，支持动态配置更新和数据订阅
/// </summary>
public class ModbusServer : IDisposable, ISingleton
{
    /// <summary>
    ///     配置路径
    /// </summary>
    private const string ConfigPath = "/etc/DeviceConf/Configs/";
    /// <summary>
    /// Modbus TCP服务器实例
    /// </summary>
    private ModbusTcpServer? _modbusTcpServer;

    /// <summary>
    /// WebSocket消息发送服务
    /// </summary>
    private readonly SendMessageService _socket;

    /// <summary>
    /// 用于取消异步操作的令牌源
    /// </summary>
    private CancellationTokenSource _tokenSource;

    /// <summary>
    /// Modbus服务器配置
    /// </summary>
    private ModbusConfig? _config;

    /// <summary>
    /// Modbus地址映射配置
    /// Key: 分区名称
    /// Value: 该分区下的映射配置列表
    /// </summary>
    private Dictionary<string, List<ModbusMappingConfig>>? _mappingConfig;

    /// <summary>
    /// 服务是否正在运行
    /// </summary>
    private bool _isRunning;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="socket">WebSocket消息发送服务</param>
    public ModbusServer(SendMessageService socket)
    {
        _socket = socket;
        _ = SendLogAsync("已初始化全局异常处理程序");
    }

    /// <summary>
    /// 通过WebSocket发送日志信息
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <returns>异步任务</returns>
    private async Task SendLogAsync(string message)
    {
        try
        {
            await _socket.Send(JSON.Serialize(new
            {
                Message = $"[ModbusServer] {message}",
                Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")
            }), "modbus_log");
        }
        catch (Exception ex)
        {
            // 日志发送失败时，使用控制台记录错误，避免递归调用
            Console.WriteLine($"[ModbusServer] 发送日志失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 启动Modbus服务
    /// </summary>
    public void Start()
    {
        if (_isRunning)
        {
            _ = SendLogAsync("服务已在运行中，跳过启动");
            return;
        }

        var configFile = Path.Combine(ConfigPath, $"modbus.json");

        try
        {
            _ = SendLogAsync("开始启动服务...");

            // 初始化取消令牌源
            _tokenSource = new CancellationTokenSource();

            // 读取配置文件
            if (File.Exists(configFile))
            {
                var json = File.ReadAllText(configFile);
                var serviceConfig = JSON.Deserialize<ServiceConfigDetail>(json);
                _config = serviceConfig.ExtConfig != null
                    ? JSON.Deserialize<ModbusConfig>(JSON.Serialize(serviceConfig.ExtConfig.Config))
                    : new ModbusConfig();
                _mappingConfig = serviceConfig.MappingConfig != null
                    ? JSON.Deserialize<Dictionary<string, List<ModbusMappingConfig>>>(JSON.Serialize(serviceConfig.MappingConfig))
                    : null;

                _ = SendLogAsync($"已加载配置文件，端口: {_config?.Port ?? 502}");
            }
            else
            {
                _ = SendLogAsync("配置文件不存在，使用默认配置");
                _config = new ModbusConfig { Port = 502 };
            }

            _ = SendLogAsync("正在订阅配置更新事件...");
            _ = SubscribeConfigUpdate();

            _ = SendLogAsync($"正在创建TCP服务器，端口: {_config?.Port ?? 502}");
            _modbusTcpServer = new ModbusTcpServer();

            // 如果需要监控客户端连接状态，可以通过其他方式实现
            _modbusTcpServer.GetCommunicationServer().OnClientOnline += (client, session) => { _ = SendLogAsync($"客户端 {session.SessionID} 已连接"); };
            _modbusTcpServer.GetCommunicationServer().OnClientOffline += (client, session) => { _ = SendLogAsync($"客户端 {session.SessionID} 断开连接"); };
            
            // 显示客户端发送的命令消息，这个事件调用一次即可。
            _modbusTcpServer.OnDataReceived += (object sender, HslCommunication.Core.Net.PipeSession session, byte[] data) =>
            {
                _ = SendLogAsync($"客户端发送数据");
                // Console.WriteLine($"Receive<{session}>:" + HslCommunication.BasicFramework.SoftBasic.ByteToHexString(data, ' '));
            };
            _modbusTcpServer.OnDataSend += (sender, data) =>
            {
                // Console.WriteLine("Send:" + HslCommunication.BasicFramework.SoftBasic.ByteToHexString(data, ' '));
            };

            _modbusTcpServer.ServerStart(_config?.Port ?? 502);

            _ = SendLogAsync("正在启动数据订阅...");
            _ = Subscription();

            _isRunning = true;
            Log.Information($"Modbus服务已启动，监听端口：{_config?.Port ?? 502}");
            _ = SendLogAsync($"服务启动成功，监听端口：{_config?.Port ?? 502}");
        }
        catch (Exception ex)
        {
            Log.Error($"启动Modbus服务失败: {ex.Message}");
            _ = SendLogAsync($"启动服务失败: {ex.Message}");
            throw Oops.Oh("启动Modbus服务失败");
        }
    }

    /// <summary>
    /// 订阅配置更新事件
    /// 当配置发生变化时，动态更新服务器配置
    /// </summary>
    private async Task SubscribeConfigUpdate()
    {
        await SendLogAsync("开始订阅配置更新事件");
        await MessageCenter.Subscribe("ModbusConfigChanged", async context =>
        {
            try
            {
                if (_isRunning)
                {
                    await SendLogAsync("配置更新被取消");
                    return;
                }

                await SendLogAsync("收到配置更新事件");
                var payload = context.GetPayload<(ModbusConfig? Config, Dictionary<string, List<ModbusMappingConfig>>? MappingConfig)>();

                await SendLogAsync($"新配置端口: {payload.Config?.Port}, 映射配置数量: {payload.MappingConfig?.Count ?? 0}");
                _config = payload.Config;
                _mappingConfig = payload.MappingConfig;

                if (_modbusTcpServer != null && _config != null && _modbusTcpServer.Port != _config.Port)
                {
                    await SendLogAsync($"检测到端口变更 {_modbusTcpServer.Port} -> {_config.Port}，准备重启服务");
                    _modbusTcpServer.ServerClose();
                    _modbusTcpServer.ServerStart(_config.Port);
                    await SendLogAsync($"服务重启完成，新端口: {_config.Port}");
                }

                await SendLogAsync("配置更新完成");
            }
            catch (Exception ex)
            {
                await SendLogAsync($"更新配置时发生错误: {ex.Message}");
                Log.Error($"更新Modbus配置失败: {ex.Message}");
            }
        }, cancellationToken: _tokenSource.Token);
    }

    /// <summary>
    /// 订阅设备数据
    /// 接收设备数据并根据映射配置写入到Modbus地址
    /// </summary>
    private async Task Subscription()
    {
        await MessageCenter.Subscribe(EventConst.SendDeviceData, async context =>
        {
            try
            {
                if (_tokenSource.IsCancellationRequested)
                    return;

                await Task.Factory.StartNew(async () =>
                {
                    var payLoad = context.GetPayload<PayLoad>();
                    // 如果没有映射配置，直接返回
                    if (_mappingConfig == null)
                    {
                        await SendLogAsync($"接收到来自 {payLoad.DeviceName} 的数据，但没有映射配置");
                        return;
                    }

                    await SendLogAsync($"接收到来自 {payLoad.DeviceName} 的数据，包含 {payLoad.Values.Count} 个变量");

                    foreach (var (key, value) in payLoad.Values)
                    {
                        try
                        {
                            // 在所有分区中查找匹配的映射
                            ModbusMappingConfig? mapping = null;
                            foreach (var partition in _mappingConfig)
                            {
                                mapping = partition.Value.FirstOrDefault(m => m.DeviceName == payLoad.DeviceName && m.VariableName == key);
                                if (mapping != null)
                                {
                                    // 记录数据写入日志
                                    await _socket.Send(JSON.Serialize(new
                                    {
                                        Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                                        Device = mapping.DeviceName,
                                        Variable = key,
                                        value.Value,
                                        mapping.DataType,
                                        mapping.Address
                                    }), "modbusServer");
                                    // 根据数据类型写入到对应地址
                                    await WriteValueToModbus(mapping.Address, mapping.DataType, value.Value);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            await SendLogAsync($"映射变量 {key} 错误: {ex.Message}");
                            await _socket.Send(JSON.Serialize(new
                            {
                                Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                                Device = payLoad.DeviceName,
                                Variable = key,
                                Value = value.Value,
                                Error = ex.Message
                            }), "modbusServer");
                        }
                    }
                });
            }
            catch (Exception e)
            {
                await SendLogAsync($"处理设备数据时出错: {e.Message}");
                await _socket.Send(JSON.Serialize(new
                {
                    Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                    Error = e.Message
                }), "modbusServer");
            }
        }, cancellationToken: _tokenSource.Token);
    }

    /// <summary>
    /// 将值写入Modbus地址
    /// </summary>
    /// <param name="address">Modbus地址</param>
    /// <param name="dataType">数据类型</param>
    /// <param name="value">要写入的值</param>
    private async Task WriteValueToModbus(string address, string dataType, string? value)
    {
        if (_modbusTcpServer == null || !_isRunning)
        {
            await SendLogAsync($"无法写入值：服务未运行或服务器实例为空");
            return;
        }

        try
        {
            await SendLogAsync($"写入值到地址 {address}: 类型={dataType}, 值={value}");

            switch (dataType.ToLower())
            {
                case "bit":
                case "bool":
                    var boolValue = Convert.ToBoolean(value);
                    _modbusTcpServer.WriteCoil(address, boolValue);
                    break;
                case "uint16":
                    var uint16Value = Convert.ToUInt16(value);
                    await _modbusTcpServer.WriteAsync(address, uint16Value);
                    break;
                case "int16":
                    await _modbusTcpServer.WriteAsync(address, Convert.ToInt16(value));
                    break;
                case "uint32":
                case "int":
                    await _modbusTcpServer.WriteAsync(address, Convert.ToUInt32(value));
                    break;
                case "int32":
                    await _modbusTcpServer.WriteAsync(address, Convert.ToInt32(value));
                    break;
                case "float":
                    await _modbusTcpServer.WriteAsync(address, Convert.ToSingle(value));
                    break;
                case "uint64":
                    await _modbusTcpServer.WriteAsync(address, Convert.ToUInt64(value));
                    break;
                case "int64":
                    await _modbusTcpServer.WriteAsync(address, Convert.ToInt64(value));
                    break;
                case "double":
                    await _modbusTcpServer.WriteAsync(address, Convert.ToDouble(value));
                    break;
                case "string":
                    {
                        var strValue = value ?? "";
                        // 将字符串转换为字节数组
                        var byteArray = Encoding.UTF8.GetBytes(strValue);
                        // 如果字节数组长度小于固定长度，进行填充操作
                        if (byteArray.Length < 10 * 2)
                        {
                            Array.Resize(ref byteArray, 10 * 2);
                            await _modbusTcpServer.WriteAsync(address, byteArray);
                        }
                        await _modbusTcpServer.WriteAsync(address, value ?? "", value.Length, Encoding.UTF8);
                        break;
                    }

                default:
                    await SendLogAsync($"遇到不支持的数据类型: {dataType}");
                    throw new ArgumentException($"不支持的数据类型: {dataType}");
            }

            await SendLogAsync($"成功写入值到地址 {address}");
        }
        catch (Exception ex)
        {
            await SendLogAsync($"写入值到地址 {address} 失败: {ex.Message}");
            // 记录错误但不抛出异常，防止数据订阅线程崩溃
            Log.Error($"Modbus写入值失败，地址: {address}, 类型: {dataType}, 值: {value}, 错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 停止Modbus服务
    /// </summary>
    public void Stop()
    {
        if (!_isRunning)
        {
            _ = SendLogAsync("服务未运行，无需停止");
            return;
        }

        try
        {
            _ = SendLogAsync("开始停止服务...");

            // 先设置服务状态为停止，防止新的操作开始
            _isRunning = false;

            // 取消所有异步操作
            if (!_tokenSource.IsCancellationRequested)
            {
                _ = SendLogAsync("取消所有异步操作...");
                _tokenSource.Cancel();
            }

            // 等待足够时间让异步操作完成
            _ = SendLogAsync("等待异步操作完成...");
            Thread.Sleep(500);  // 增加等待时间

            // 解除事件订阅
            _ = SendLogAsync("解除事件订阅...");
            try
            {
                MessageCenter.Unsubscribe("ModbusConfigChanged");
            }
            catch (Exception ex)
            {
                _ = SendLogAsync($"解除事件订阅时发生异常: {ex.Message}");
            }

            // 再次等待确保异步操作结束
            Thread.Sleep(200);

            if (_modbusTcpServer != null)
            {
                _ = SendLogAsync("关闭TCP服务器...");

                try
                {
                    // 安全关闭服务器
                    _modbusTcpServer.ServerClose();
                    // 等待资源释放
                    Thread.Sleep(200);  // 增加等待时间
                }
                catch (Exception ex)
                {
                    _ = SendLogAsync($"关闭TCP服务器时发生异常: {ex.Message}");
                }
            }

            Log.Information("Modbus服务已停止");
            _ = SendLogAsync("服务已完全停止");

            // 额外等待以确保所有资源都被正确释放
            Thread.Sleep(200);
        }
        catch (Exception ex)
        {
            _ = SendLogAsync($"停止服务时发生错误: {ex.Message}");
            Log.Error($"停止Modbus服务失败: {ex.Message}");
            // 捕获但不抛出异常，避免程序崩溃
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        try
        {
            _ = SendLogAsync("开始释放资源...");

            // 尝试停止服务
            Stop();

            // 确保令牌源被取消并释放
            try
            {
                if (!_tokenSource.IsCancellationRequested)
                {
                    _tokenSource.Cancel();
                }
            }
            catch (Exception ex)
            {
                _ = SendLogAsync($"释放令牌源时发生错误: {ex.Message}");
                Log.Error($"释放Modbus服务令牌源失败: {ex.Message}");
            }

            // 确保服务状态标记为停止
            _isRunning = false;

            _ = SendLogAsync("资源释放完成");

            // 再次强制GC回收
            GC.Collect();
            GC.WaitForPendingFinalizers();
        }
        catch (Exception ex)
        {
            _ = SendLogAsync($"Dispose过程中发生未预期的错误: {ex.Message}");
            Log.Error($"Modbus服务Dispose失败: {ex.Message}");
            // 捕获但不抛出异常，避免程序崩溃
        }
    }
}