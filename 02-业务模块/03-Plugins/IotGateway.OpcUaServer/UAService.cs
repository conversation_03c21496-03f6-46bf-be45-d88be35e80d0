using Common.Models;
using Feng.IotGateway.Core.Const;
using Feng.IotGateway.OpcUaService.UA.Server;
using Feng.IotGateway.WebSocket;
using Furion.DependencyInjection;
using Furion.EventBus;
using Furion.FriendlyException;
using Furion.JsonSerialization;
using Furion.Logging;
using IotGateway.OpcUaServer.Models;
using Feng.IotGateway.Core.Extension;
using Feng.IotGateway.OpcUaService.Models;
using System.Collections.Concurrent;

namespace Feng.IotGateway.OpcUaService;

/// <summary>
/// 日志级别枚举
/// </summary>
public enum LogLevel
{
    /// <summary>
    /// 调试级别 - 详细的调试信息
    /// </summary>
    Debug = 0,
    /// <summary>
    /// 信息级别 - 一般信息
    /// </summary>
    Info = 1,
    /// <summary>
    /// 警告级别 - 警告信息
    /// </summary>
    Warn = 2,
    /// <summary>
    /// 错误级别 - 错误信息
    /// </summary>
    Error = 3,
    /// <summary>
    /// 静默级别 - 仅关键错误
    /// </summary>
    Silent = 4
}

/// <summary>
/// 服务配置详情
/// </summary>
public class ServiceConfigDetail
{
    /// <summary>
    /// 服务类型
    /// </summary>
    public string ServiceType { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 最大连接数
    /// </summary>
    public int MaxConnections { get; set; }

    /// <summary>
    /// 超时时间(ms)
    /// </summary>
    public int Timeout { get; set; }

    /// <summary>
    /// 扩展配置(JSON格式)
    /// </summary>
    public ExtConfig ExtConfig { get; set; }

    /// <summary>
    /// 映射配置
    /// </summary>
    public object MappingConfig { get; set; }
}

/// <summary>
/// 扩展配置
/// </summary>
public class ExtConfig
{
    /// <summary>
    /// 配置模式
    /// </summary>
    public ExtConfigSchema Schema { get; set; }

    /// <summary>
    /// 配置内容
    /// </summary>
    public object Config { get; set; }
}

/// <summary>
/// 扩展配置模式
/// </summary>
public class ExtConfigSchema
{
    /// <summary>
    /// 属性键值对
    /// </summary>
    public Dictionary<string, object> Properties { get; set; }

    /// <summary>
    /// 必填属性
    /// </summary>
    public string[] Required { get; set; }
}

/// <summary>
///     opcUa服务端
/// </summary>
public class UaService : IDisposable, ISingleton
{
    /// <summary>
    /// 可重用的WebSocket消息对象
    /// </summary>
    public class WebSocketMessage
    {
        public string Time { get; set; } = string.Empty;
        public string Device { get; set; } = string.Empty;
        public string Variable { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public string NodeId { get; set; } = string.Empty;
        public string? Message { get; set; }
        public string? Error { get; set; }
        public string? Status { get; set; }

        /// <summary>
        /// 重置对象状态以便重用
        /// </summary>
        public void Reset()
        {
            Time = string.Empty;
            Device = string.Empty;
            Variable = string.Empty;
            Value = string.Empty;
            DataType = string.Empty;
            NodeId = string.Empty;
            Message = null;
            Error = null;
            Status = null;
        }

        /// <summary>
        /// 设置为设备数据消息
        /// </summary>
        public void SetAsDeviceData(string time, string device, string variable, string value, string dataType, string nodeId, string status = "success")
        {
            Reset();
            Time = time;
            Device = device;
            Variable = variable;
            Value = value;
            DataType = dataType;
            NodeId = nodeId;
            Status = status;
        }

        /// <summary>
        /// 设置为日志消息
        /// </summary>
        public void SetAsLogMessage(string time, string message)
        {
            Reset();
            Time = time;
            Message = message;
        }

        /// <summary>
        /// 设置为错误消息
        /// </summary>
        public void SetAsErrorMessage(string time, string error)
        {
            Reset();
            Time = time;
            Error = error;
        }
    }

    /// <summary>
    /// 可重用的日志消息对象
    /// </summary>
    public class LogMessage
    {
        public string Message { get; set; } = string.Empty;
        public string Time { get; set; } = string.Empty;

        /// <summary>
        /// 重置对象状态以便重用
        /// </summary>
        public void Reset()
        {
            Message = string.Empty;
            Time = string.Empty;
        }

        /// <summary>
        /// 设置消息内容
        /// </summary>
        public void SetMessage(string message, string time)
        {
            Message = message;
            Time = time;
        }
    }

    /// <summary>
    ///     应用程序名称
    /// </summary>
    private const string ApplicationName = "ConsoleReferenceServer";

    /// <summary>
    ///     配置节名称
    /// </summary>
    private const string ConfigSectionName = "Quickstarts.ReferenceServer";
    /// <summary>
    ///     取消令牌源
    /// </summary>
    private CancellationTokenSource _tokenSource = new();
    /// <summary>
    ///     opcUa服务端
    /// </summary>
    private UaServer<ReferenceServer>? server;
    /// <summary>
    ///     节点管理器
    /// </summary>
    private ReferenceNodeManager? _uaNodeManager;
    /// <summary>
    ///     是否运行
    /// </summary>
    public bool IsRunning { get; private set; }

    /// <summary>
    ///     socket消息推送
    /// </summary>
    private readonly SendMessageService _socket;

    /// <summary>
    ///     配置路径
    /// </summary>
    private const string ConfigPath = "/etc/DeviceConf/Configs/";

    /// <summary>
    /// 设备映射缓存 - 按设备名称索引的映射列表
    /// </summary>
    private readonly ConcurrentDictionary<string, List<OpcUaTagMapping>> _deviceMappingCache = new();

    /// <summary>
    /// 设备变量缓存 - 按设备名称和变量名称的快速查找
    /// </summary>
    private readonly ConcurrentDictionary<string, Dictionary<string, OpcUaTagMapping>> _deviceVariableCache = new();

    /// <summary>
    /// 数据类型转换器缓存
    /// </summary>
    private readonly Dictionary<string, Func<string, object>> _typeConverterCache = new();

    /// <summary>
    /// 缓存构建锁
    /// </summary>
    private readonly object _cacheLock = new object();

    /// <summary>
    /// 缓存统计信息
    /// </summary>
    private volatile int _cacheHitCount = 0;
    private volatile int _cacheMissCount = 0;

    /// <summary>
    /// 当前日志级别，生产环境建议使用Info减少内存分配
    /// </summary>
    private LogLevel _currentLogLevel = LogLevel.Warn;

    /// <summary>
    /// 生产模式标志 - 在生产模式下最小化日志输出
    /// </summary>
    private bool _isProductionMode = true;

    /// <summary>
    /// 时间格式缓存
    /// </summary>
    private readonly string _dateTimeFormat = "yyyy-MM-dd HH:mm:ss.fff";

    /// <summary>
    /// 消息模板缓存
    /// </summary>
    private readonly ConcurrentDictionary<string, string> _messageTemplateCache = new();

    // 对象池管理
    /// <summary>
    /// WebSocket消息对象池
    /// </summary>
    private readonly ConcurrentQueue<WebSocketMessage> _webSocketMessagePool = new();

    /// <summary>
    /// 日志消息对象池
    /// </summary>
    private readonly ConcurrentQueue<LogMessage> _logMessagePool = new();

    /// <summary>
    /// 对象池最大容量
    /// </summary>
    private const int MaxPoolSize = 1000;

    /// <summary>
    /// 当前对象池统计
    /// </summary>
    private volatile int _webSocketPoolCount = 0;
    private volatile int _logPoolCount = 0;

    /// <summary>
    ///     释放资源
    /// </summary>
    public void Dispose()
    {
        try
        {
            // 停止服务
            Stop();

            // 确保内存监控定时器被释放
            StopMemoryMonitoring();

            // 清理所有缓存
            _deviceMappingCache.Clear();
            _deviceVariableCache.Clear();
            _typeConverterCache.Clear();
            ClearMessageTemplates();

            // 清空对象池
            while (_webSocketMessagePool.TryDequeue(out _))
            {
                Interlocked.Decrement(ref _webSocketPoolCount);
            }

            while (_logMessagePool.TryDequeue(out _))
            {
                Interlocked.Decrement(ref _logPoolCount);
            }

            // 重置计数器
            _cacheHitCount = 0;
            _cacheMissCount = 0;
            _webSocketPoolCount = 0;
            _logPoolCount = 0;
            _lastGcCount = 0;

            _ = SendLogAsync("所有资源已释放", LogLevel.Info);
        }
        catch (Exception ex)
        {
            // 使用Console而不是SendLogAsync避免循环调用
            Console.WriteLine($"[OPC UA] Dispose过程中发生错误: {ex.Message}");
        }
    }

    /// <summary>
    ///     停止服务
    /// </summary>
    public void Stop()
    {
        _ = SendLogAsync("正在停止OPC UA服务...");

        // 停止内存监控
        _ = SendLogAsync("正在停止内存监控...");
        StopMemoryMonitoring();
        _ = SendLogAsync("内存监控已停止");

        // 取消订阅
        _ = SendLogAsync("正在取消消息订阅...");
        _tokenSource.Cancel();
        _ = SendLogAsync("消息订阅已取消");

        // 停止服务器
        if (server != null)
        {
            _ = SendLogAsync("正在停止服务器...");
            server?.StopAsync().ConfigureAwait(false);
            _ = SendLogAsync("服务器已停止");
        }
        else
        {
            _ = SendLogAsync("服务器实例为空，无需停止");
        }

        // 清理缓存和对象池
        _ = SendLogAsync("正在清理缓存和对象池...");
        ClearMessageTemplates();
        CleanupObjectPools();
        _ = SendLogAsync("缓存和对象池已清理");

        // 释放资源
        _ = SendLogAsync("正在释放资源...");
        _tokenSource.Dispose();
        _ = SendLogAsync("资源已释放");

        // 清理节点管理器引用
        _uaNodeManager = null;
        _ = SendLogAsync("节点管理器引用已清理");

        // 更新状态
        IsRunning = false;
        _ = SendLogAsync($"OPC UA服务已停止，状态: {(IsRunning ? "运行中" : "已停止")}");
    }

    /// <summary>
    ///     初始化
    /// </summary>
    /// <param name="socket"></param>
    public UaService(SendMessageService socket)
    {
        _socket = socket;
    }

    /// <summary>
    /// 通过WebSocket发送日志信息
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="level">日志级别</param>
    /// <returns>异步任务</returns>
    private async Task SendLogAsync(string message, LogLevel level = LogLevel.Info)
    {
        // 检查日志级别，如果当前级别高于设定级别则跳过
        if (level < _currentLogLevel)
        {
            return;
        }

        LogMessage? logMessage = null;
        try
        {
            logMessage = GetLogMessage();
            logMessage.SetMessage($"[OPC UA] {message}", GetCurrentTimeString());

            var jsonData = JSON.Serialize(logMessage);
            await _socket.Send(jsonData, "opcua_log");
        }
        catch (Exception ex)
        {
            // 日志发送失败时，使用控制台记录错误，避免递归调用
            Console.WriteLine($"[OPC UA] 发送日志失败: {ex.Message}");
        }
        finally
        {
            // 归还对象到池中
            if (logMessage != null)
            {
                ReturnLogMessage(logMessage);
            }
        }
    }

    /// <summary>
    /// 发送设备数据到WebSocket（使用对象池优化）
    /// </summary>
    private async Task SendDeviceDataAsync(string device, string variable, string value, string dataType, string nodeId, string status = "success", string? error = null)
    {
        WebSocketMessage? wsMessage = null;
        try
        {
            wsMessage = GetWebSocketMessage();
            wsMessage.SetAsDeviceData(GetCurrentTimeString(), device, variable, value, dataType, nodeId, status);

            // 如果有错误信息，设置Error字段
            if (!string.IsNullOrEmpty(error))
            {
                wsMessage.Error = error;
            }

            var jsonData = JSON.Serialize(wsMessage);
            await _socket.Send(jsonData, "opcuaServer");
        }
        catch (Exception ex)
        {
            await SendLogAsync($"发送设备数据失败: {ex.Message}", LogLevel.Error);
        }
        finally
        {
            if (wsMessage != null)
            {
                ReturnWebSocketMessage(wsMessage);
            }
        }
    }

    /// <summary>
    /// 发送操作成功消息到WebSocket（使用对象池优化）
    /// </summary>
    private async Task SendOperationSuccessAsync(string message)
    {
        WebSocketMessage? wsMessage = null;
        try
        {
            wsMessage = GetWebSocketMessage();
            wsMessage.SetAsLogMessage(GetCurrentTimeString(), message);

            var jsonData = JSON.Serialize(wsMessage);
            await _socket.Send(jsonData, "opcua_log");
        }
        catch (Exception ex)
        {
            await SendLogAsync($"发送操作成功消息失败: {ex.Message}", LogLevel.Error);
        }
        finally
        {
            if (wsMessage != null)
            {
                ReturnWebSocketMessage(wsMessage);
            }
        }
    }

    /// <summary>
    ///     启动服务
    /// </summary>
    public void Start()
    {
        _ = SendLogAsync("正在启动OPC UA服务...");

        // 如果已经在运行，先停止服务
        if (IsRunning)
        {
            _ = SendLogAsync("服务已经在运行，先停止服务");
            Stop();
        }

        // 重新初始化 CancellationTokenSource
        _tokenSource = new CancellationTokenSource();
        _ = SendLogAsync("已重新初始化 CancellationTokenSource");

        // 初始化缓存和内存管理
        _ = SendLogAsync("正在初始化缓存系统...");
        InitializeMessageTemplates();
        InvalidateCache();
        StartMemoryMonitoring();
        _ = SendLogAsync("缓存系统和内存监控已启动");

        var config = GetConfig();
        _ = SendLogAsync($"已加载配置，端口: {config.Port}");

        // 检查LocalIpAddress是否为0.0.0.0，如果是则替换为localhost
        if (config.LocalIpAddress == "0.0.0.0")
        {
            config.LocalIpAddress = "localhost";
            _ = SendLogAsync($"检测到LocalIpAddress为0.0.0.0，已自动替换为localhost");
        }

        var server_instance = new ReferenceServer { Port = config.Port, LocalIpAddress = config.LocalIpAddress };
        _ = SendLogAsync("已创建ReferenceServer实例");

        // 初始化opcua 服务端
        server = new UaServer<ReferenceServer>(null)  // 保持传null作为TextWriter
        {
            AutoAccept = false,  // 自动接受连接
            Password = null   // 密码
        };
        _ = SendLogAsync("已创建UaServer实例");

        server.m_server = server_instance;  // 直接设置服务器实例
        _ = SendLogAsync("服务器实例设置完成");

        // 加载opcua 配置
        _ = SendLogAsync($"正在加载应用配置 (ApplicationName: {ApplicationName}, ConfigSectionName: {ConfigSectionName})...");
        server.LoadAsync(ApplicationName, ConfigSectionName).ConfigureAwait(false);
        _ = SendLogAsync("应用配置加载完成");

        // 检查证书
        _ = SendLogAsync("正在检查证书...");
        server.CheckCertificateAsync(false).ConfigureAwait(false);
        _ = SendLogAsync("证书检查完成");

        // 启动opcua 服务端
        _ = SendLogAsync("正在启动OPC UA服务端...");
        server.StartAsync().ConfigureAwait(false);
        _ = SendLogAsync("OPC UA服务端启动完成");

        // 启动后加载分组和变量
        _ = SendLogAsync("正在加载标签配置...");
        var tagConfig = GetTagConfigs();
        _ = SendLogAsync($"标签配置加载完成，共有 {tagConfig.Count} 个分组");

        // 初始化节点管理器
        _uaNodeManager = server?.m_server?.nodeManagers.Count > 0 ?
                         server?.m_server?.nodeManagers[0] as ReferenceNodeManager : null;

        if (_uaNodeManager == null)
        {
            _ = SendLogAsync("警告：节点管理器未初始化或为空，无法创建设备变量");
        }
        else
        {
            foreach (var group in tagConfig)
            {
                try
                {
                    _ = SendLogAsync($"正在创建设备分组: {group.Key}，包含 {group.Value.Count} 个标签");
                    CreateDeviceVariables(group.Value, group.Key);
                    _ = SendLogAsync($"设备分组 {group.Key} 创建完成");
                }
                catch (Exception ex)
                {
                    _ = SendLogAsync($"创建设备分组 {group.Key} 失败: {ex.Message}");
                    Log.Error($"启动OPC UA服务失败: {ex.Message}");
                }
            }
        }
        // 状态
        IsRunning = true;
        _ = SendLogAsync($"OPC UA服务已成功启动，状态: {(IsRunning ? "运行中" : "已停止")}");

        // 订阅消息 
        _ = SendLogAsync("正在启动消息订阅...");
        _ = Subscription();
        _ = SendLogAsync("消息订阅已启动");

        // 订阅配置更新事件
        _ = SendLogAsync("正在订阅配置更新事件...");
        _ = SubscribeConfigUpdate();
        _ = SendLogAsync("配置更新事件订阅已启动");
    }

    /// <summary>
    ///     动态订阅消息
    /// </summary>
    private async Task Subscription()
    {
        _ = SendLogAsync("开始初始化消息订阅...");
        await MessageCenter.Subscribe(EventConst.SendDeviceData, async context =>
        {
            try
            {
                if (_tokenSource.IsCancellationRequested)
                {
                    return; // 在生产模式下不记录取消日志
                }

                var payLoad = context.GetPayload<PayLoad>();

                // 使用缓存进行快速查找
                if (!_deviceVariableCache.TryGetValue(payLoad.DeviceName, out var deviceVariables))
                {
                    Interlocked.Increment(ref _cacheMissCount);

                    // 只在非生产模式或错误级别时记录缓存未命中
                    if (!_isProductionMode)
                    {
                        _ = SendLogAsync(string.Format(_messageTemplateCache["device_not_in_cache"], payLoad.DeviceName), LogLevel.Warn);
                    }

                    // 回退到原始方式
                    await ProcessDeviceDataFallback(payLoad);
                    return;
                }

                Interlocked.Increment(ref _cacheHitCount);

                // 处理每个变量值
                foreach (var (key, value) in payLoad.Values)
                {
                    if (!deviceVariables.TryGetValue(key, out var mapping))
                    {
                        continue; // 变量未找到映射，跳过
                    }

                    // 使用优化的发送方法
                    await SendDeviceDataAsync(mapping.DeviceName, key, value.Value, value.DataType.ToString(), mapping.NodeId);

                    try
                    {
                        // 使用缓存的类型转换器
                        var dataTypeKey = mapping.DataType.ToLower();

                        if (_typeConverterCache.TryGetValue(dataTypeKey, out var converter))
                        {
                            var convertedValue = converter(value.Value);

                            _uaNodeManager?.UpdateNode(mapping.NodeId, convertedValue);
                        }
                        else
                        {
                            _uaNodeManager?.UpdateNode(mapping.NodeId, value.Value);
                        }

                        // 在生产模式下不发送成功消息，减少WebSocket负载
                        if (!_isProductionMode)
                        {
                            await SendOperationSuccessAsync(string.Format(_messageTemplateCache["variable_update_success"], mapping.DeviceName, mapping.VariableName, value.Value));
                        }
                    }
                    catch (Exception ex)
                    {
                        // 只保留错误日志，这是必要的
                        _ = SendLogAsync(string.Format(_messageTemplateCache["node_update_failed"], mapping.NodeId, ex.Message), LogLevel.Error);
                        // 发送失败状态和错误信息
                        await SendDeviceDataAsync(mapping.DeviceName, key, value.Value, value.DataType.ToString(), mapping.NodeId, "failure", ex.Message);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Error($"OPC UA订阅处理失败: {e.Message}");

                // 只在严重错误时发送WebSocket消息
                WebSocketMessage? errorMessage = null;
                try
                {
                    errorMessage = GetWebSocketMessage();
                    errorMessage.SetAsErrorMessage(GetCurrentTimeString(), e.Message);

                    var jsonData = JSON.Serialize(errorMessage);
                    await _socket.Send(jsonData, "opcua_log");
                }
                catch (Exception sendEx)
                {
                    Console.WriteLine($"[OPC UA] 发送错误信息失败: {sendEx.Message}");
                }
                finally
                {
                    if (errorMessage != null)
                    {
                        ReturnWebSocketMessage(errorMessage);
                    }
                }
            }
        }, cancellationToken: _tokenSource.Token);

        _ = SendLogAsync("消息订阅初始化完成");
    }

    /// <summary>
    /// 回退处理方式（当缓存失效时使用原始查找逻辑）
    /// </summary>
    private async Task ProcessDeviceDataFallback(PayLoad payLoad)
    {
        try
        {
            // 只在非生产模式下记录回退处理日志
            if (!_isProductionMode)
            {
                _ = SendLogAsync(string.Format(_messageTemplateCache["fallback_processing"], payLoad.DeviceName), LogLevel.Warn);
            }

            // 设置成全局变量GetTagConfigs
            var tagConfig = GetTagConfigs();
            // 移除配置加载的日志记录

            // 遍历所有分组中的映射
            foreach (var group in tagConfig)
            {
                var mappings = group.Value
                    .Where(x => x.Enabled && x.DeviceName == payLoad.DeviceName)
                    .ToList();

                foreach (var (key, value) in payLoad.Values)
                {
                    var mapping = mappings.FirstOrDefault(x => x.VariableName == key);
                    if (mapping == null)
                    {
                        continue;
                    }

                    // 使用优化的发送方法
                    await SendDeviceDataAsync(mapping.DeviceName, key, value.Value, value.DataType.ToString(), mapping.NodeId);

                    try
                    {
                        var dataTypeKey = mapping.DataType.ToLower();

                        switch (dataTypeKey)
                        {
                            case "bit":
                                var bitResult = value.Value == "1";
                                _uaNodeManager?.UpdateNode(mapping.NodeId, bitResult);
                                break;
                            case "bool":
                                var boolResult = Convert.ToBoolean(value.Value.ToLower());
                                _uaNodeManager?.UpdateNode(mapping.NodeId, boolResult);
                                break;
                            case "int16":
                            case "bcd16":
                                _uaNodeManager?.UpdateNode(mapping.NodeId, Convert.ToInt16(value.Value));
                                break;
                            case "uint16":
                                _uaNodeManager?.UpdateNode(mapping.NodeId, Convert.ToUInt16(value.Value));
                                break;
                            case "int32":
                            case "bcd":
                            case "bcd32":
                                _uaNodeManager?.UpdateNode(mapping.NodeId, Convert.ToInt32(value.Value));
                                break;
                            case "float":
                                _uaNodeManager?.UpdateNode(mapping.NodeId, Convert.ToSingle(value.Value));
                                break;
                            case "uint32":
                                _uaNodeManager?.UpdateNode(mapping.NodeId, Convert.ToUInt32(value.Value));
                                break;
                            case "int64":
                            case "bcd64":
                                _uaNodeManager?.UpdateNode(mapping.NodeId, Convert.ToInt64(value.Value));
                                break;
                            case "double":
                                _uaNodeManager?.UpdateNode(mapping.NodeId, Convert.ToDouble(value.Value));
                                break;
                            case "byte":
                            case "string":
                            default:
                                _uaNodeManager?.UpdateNode(mapping.NodeId, value.Value);
                                break;
                        }

                        // 在生产模式下不发送成功消息
                        if (!_isProductionMode)
                        {
                            var successMessage = $"变量更新成功: 分组[{group.Key}], 设备[{mapping.DeviceName}], 变量[{mapping.VariableName}], 值[{value.Value}]";
                            await SendOperationSuccessAsync(successMessage);
                        }
                    }
                    catch (Exception ex)
                    {
                        // 只保留错误日志
                        _ = SendLogAsync(string.Format(_messageTemplateCache["node_update_failed"], mapping.NodeId, ex.Message), LogLevel.Error);
                        // 发送失败状态和错误信息
                        await SendDeviceDataAsync(mapping.DeviceName, key, value.Value, value.DataType.ToString(), mapping.NodeId, "failure", ex.Message);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            // 只保留错误日志
            _ = SendLogAsync($"回退处理失败: {ex.Message}", LogLevel.Error);
            Log.Error($"回退处理失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 为单个设备创建变量
    /// </summary>
    /// <param name="mappings">标签映射列表</param>
    /// <param name="groupName">分组名称</param>
    public void CreateDeviceVariables(List<OpcUaTagMapping> mappings, string groupName)
    {
        try
        {
            // 检查_uaNodeManager是否已初始化
            if (_uaNodeManager == null)
            {
                // 尝试初始化_uaNodeManager
                if (server?.m_server?.nodeManagers != null && server.m_server.nodeManagers.Count > 0)
                {
                    _uaNodeManager = server.m_server.nodeManagers[0] as ReferenceNodeManager;
                }
                else
                {
                    var errorMsg = "无法创建设备变量：节点管理器未初始化";
                    _ = SendLogAsync(errorMsg);
                    throw new InvalidOperationException(errorMsg);
                }
            }

            var groupNode = _uaNodeManager.GetRootNode();

            // 只有当有映射数据时才创建变量
            if (groupNode != null && mappings != null && mappings.Any())
            {
                _uaNodeManager.CreateDeviceVariables(groupNode, mappings, groupName);
            }

            // 刷新_tagConfigs
            _tagConfigs = GetTagConfigs();
            _ = SendLogAsync($"标签配置已刷新，当前有 {_tagConfigs.Count} 个分组");
        }
        catch (Exception ex)
        {
            var errorMsg = $"创建OPC UA变量失败: {ex.Message}";

            // 发送错误信息到WebSocket
            _ = _socket.Send(JSON.Serialize(new
            {
                Time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                Error = errorMsg,
                Device = "Unknown",
                Group = groupName
            }), "opcua_log");

            throw;
        }
    }

    /// <summary>
    /// 创建设备分组
    /// </summary>
    /// <param name="groupName">分组名称</param>
    public void CreateDeviceGroup(string groupName)
    {
        try
        {
            _ = SendLogAsync($"开始创建设备分组: {groupName}", LogLevel.Info);

            // 检查_uaNodeManager是否已初始化
            if (_uaNodeManager == null)
            {
                // 尝试初始化_uaNodeManager
                if (server?.m_server?.nodeManagers != null && server.m_server.nodeManagers.Count > 0)
                {
                    _uaNodeManager = server.m_server.nodeManagers[0] as ReferenceNodeManager;
                }
                else
                {
                    var errorMsg = "无法创建设备分组：节点管理器未初始化";
                    _ = SendLogAsync(errorMsg, LogLevel.Error);
                    throw new InvalidOperationException(errorMsg);
                }
            }

            var rootNode = _uaNodeManager.GetRootNode();
            if (rootNode != null)
            {
                // 检查分组是否已存在
                var existingGroup = _uaNodeManager.FindGroupNode(groupName);
                if (existingGroup != null)
                {
                    _ = SendLogAsync($"设备分组 {groupName} 已存在，跳过创建", LogLevel.Warn);
                    return;
                }

                // 创建新分组
                var groupNode = _uaNodeManager.CreateDeviceGroup(rootNode, groupName);
                if (groupNode != null)
                {
                    _ = SendLogAsync($"设备分组 {groupName} 创建成功", LogLevel.Info);
                }
                else
                {
                    throw new InvalidOperationException($"创建设备分组 {groupName} 失败");
                }
            }
            else
            {
                throw new InvalidOperationException("无法获取根节点");
            }
        }
        catch (Exception ex)
        {
            _ = SendLogAsync($"创建设备分组 {groupName} 失败: {ex.Message}", LogLevel.Error);
            Log.Error($"创建设备分组失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 获取配置
    /// </summary>
    public OpcUaConfig GetConfig()
    {
        try
        {
            var configFile = Path.Combine(ConfigPath, "opcua.json");
            if (File.Exists(configFile))
            {
                var json = File.ReadAllText(configFile);
                var serviceConfig = JSON.Deserialize<ServiceConfigDetail>(json);
                if (serviceConfig?.ExtConfig?.Config != null)
                {
                    return JSON.Deserialize<OpcUaConfig>(JSON.Serialize(serviceConfig.ExtConfig.Config)) ?? new OpcUaConfig();
                }
            }
        }
        catch (Exception ex)
        {
            Log.Error($"读取OPC UA配置失败: {ex.Message}");
        }
        return new OpcUaConfig();
    }

    /// <summary>
    /// 获取完整配置
    /// </summary>
    /// <returns>服务配置详情</returns>
    private ServiceConfigDetail GetFullConfig()
    {
        try
        {
            var configFile = Path.Combine(ConfigPath, "opcua.json");
            if (File.Exists(configFile))
            {
                var json = File.ReadAllText(configFile);
                return JSON.Deserialize<ServiceConfigDetail>(json) ?? CreateDefaultConfig();
            }
        }
        catch (Exception ex)
        {
            Log.Error($"读取OPC UA完整配置失败: {ex.Message}");
        }
        return CreateDefaultConfig();
    }

    /// <summary>
    /// 创建默认配置
    /// </summary>
    /// <returns>默认配置</returns>
    private ServiceConfigDetail CreateDefaultConfig()
    {
        var defaultConfig = new ServiceConfigDetail
        {
            ServiceType = "opcua",
            Enabled = false,
            MaxConnections = 100,
            Timeout = 5000,
            ExtConfig = new ExtConfig
            {
                Schema = new ExtConfigSchema
                {
                    Properties = new Dictionary<string, object>
                    {
                        {
                            "Port", new
                            {
                                type = "integer",
                                title = "端口号",
                                description = "OPC UA服务器端口号",
                                minimum = 1,
                                maximum = 65535,
                                defaultValue = 62541
                            }
                        },
                        {
                            "LocalIpAddress", new
                            {
                                type = "string",
                                title = "本地IP地址",
                                description = "OPC UA服务器本地IP地址",
                                defaultValue = "localhost"
                            }
                        }
                    },
                    Required = new[] { "Port", "LocalIpAddress" }
                },
                Config = new OpcUaConfig()
            },
            MappingConfig = new Dictionary<string, List<OpcUaTagMapping>>()
        };

        return defaultConfig;
    }

    /// <summary>
    /// 保存配置
    /// </summary>
    public void SaveConfig(OpcUaConfig config)
    {
        try
        {
            var configFile = Path.Combine(ConfigPath, "opcua.json");
            var directory = Path.GetDirectoryName(configFile);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 读取现有配置或使用默认配置
            var serviceConfig = GetFullConfig();

            // 更新配置
            if (serviceConfig.ExtConfig == null)
            {
                serviceConfig.ExtConfig = new ExtConfig
                {
                    Config = config
                };
            }
            else
            {
                serviceConfig.ExtConfig.Config = config;
            }

            // 保存配置
            var json = JSON.Serialize(serviceConfig);
            File.WriteAllText(configFile, json);

            // 如果配置启用了服务，则启动服务
            if (config.Enabled && !IsRunning)
            {
                Start();
            }
            // 如果配置禁用了服务，则停止服务
            else if (!config.Enabled && IsRunning)
            {
                Stop();
            }
            // 如果服务正在运行，且端口发生变化，则重启服务
            else if (IsRunning && (server?.m_server?.Port != config.Port || server?.m_server?.LocalIpAddress != config.LocalIpAddress))
            {
                Stop();
                Start();
            }
        }
        catch (Exception ex)
        {
            Log.Error($"保存OPC UA配置失败: {ex.Message}");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    /// 标签配置
    /// </summary>
    private Dictionary<string, List<OpcUaTagMapping>> _tagConfigs = new();

    /// <summary>
    /// 获取标签配置
    /// </summary>
    public Dictionary<string, List<OpcUaTagMapping>> GetTagConfigs()
    {
        try
        {
            // 每次调用都重新读取配置，确保数据最新
            var serviceConfig = GetFullConfig();
            if (serviceConfig.MappingConfig != null)
            {
                var configJson = serviceConfig.MappingConfig.ToJson();
                var newTagConfigs = configJson.ToObject<Dictionary<string, List<OpcUaTagMapping>>>();

                // 更新内存缓存
                if (newTagConfigs != null)
                {
                    _tagConfigs = newTagConfigs;
                }
                else
                {
                    _tagConfigs = new Dictionary<string, List<OpcUaTagMapping>>();
                }
            }
            else
            {
                _tagConfigs = new Dictionary<string, List<OpcUaTagMapping>>();
            }

            return _tagConfigs;
        }
        catch (Exception ex)
        {
            Log.Error($"获取标签配置失败: {ex.Message}");

            // 返回现有缓存或空配置
            return _tagConfigs ?? new Dictionary<string, List<OpcUaTagMapping>>();
        }
    }

    /// <summary>
    /// 删除分组
    /// </summary>
    public void DeleteDeviceGroup(string groupName)
    {
        try
        {
            _ = SendLogAsync($"开始删除分组: {groupName}", LogLevel.Info);

            // 调用底层节点管理器删除OPC UA节点
            if (_uaNodeManager != null)
            {
                try
                {
                    var deleteResult = _uaNodeManager.DeleteDeviceGroup(groupName);
                    if (deleteResult)
                    {
                        _ = SendLogAsync($"底层OPC UA分组节点 {groupName} 删除成功", LogLevel.Info);
                    }
                    else
                    {
                        _ = SendLogAsync($"底层OPC UA分组节点 {groupName} 删除失败或不存在", LogLevel.Warn);
                    }
                }
                catch (Exception ex)
                {
                    _ = SendLogAsync($"删除底层OPC UA分组节点失败: {ex.Message}", LogLevel.Error);
                    // 继续执行配置删除，不因底层删除失败而中断
                }
            }
            else
            {
                _ = SendLogAsync($"节点管理器未初始化，跳过底层节点删除", LogLevel.Warn);
            }

            // 从内存中删除分组
            _tagConfigs.Remove(groupName);

            // 保存更新后的配置
            var serviceConfig = GetFullConfig();
            serviceConfig.MappingConfig = _tagConfigs;

            var configFile = Path.Combine(ConfigPath, "opcua.json");
            var json = JSON.Serialize(serviceConfig);
            File.WriteAllText(configFile, json);

            // 强制刷新缓存
            _ = GetTagConfigs();

            _ = SendLogAsync($"分组 {groupName} 删除成功", LogLevel.Info);
        }
        catch (Exception ex)
        {
            _ = SendLogAsync($"删除分组 {groupName} 失败: {ex.Message}", LogLevel.Error);
            Log.Error($"删除分组失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 批量删除标签
    /// </summary>
    public void DeleteDeviceTags(string groupName, List<string> tagNames)
    {
        try
        {
            _ = SendLogAsync($"开始删除分组 {groupName} 中的 {tagNames.Count} 个标签", LogLevel.Info);

            // 调用底层节点管理器删除OPC UA变量节点
            if (_uaNodeManager != null)
            {
                foreach (var tagName in tagNames)
                {
                    try
                    {
                        // 构造节点ID，格式为 分组名.标签名
                        var nodeId = $"{groupName}.{tagName}";
                        var deleteResult = _uaNodeManager.DeleteDeviceVariable(nodeId);
                        if (deleteResult)
                        {
                            _ = SendLogAsync($"底层OPC UA变量节点 {nodeId} 删除成功", LogLevel.Debug);
                        }
                        else
                        {
                            _ = SendLogAsync($"底层OPC UA变量节点 {nodeId} 删除失败或不存在", LogLevel.Warn);
                        }
                    }
                    catch (Exception ex)
                    {
                        _ = SendLogAsync($"删除底层OPC UA变量节点 {tagName} 失败: {ex.Message}", LogLevel.Error);
                        // 继续执行其他标签的删除，不因个别标签删除失败而中断
                    }
                }
            }
            else
            {
                _ = SendLogAsync($"节点管理器未初始化，跳过底层节点删除", LogLevel.Warn);
            }

            // 从内存中删除标签
            if (_tagConfigs.ContainsKey(groupName))
            {
                _tagConfigs[groupName] = _tagConfigs[groupName].Where(x => !tagNames.Contains(x.VariableName)).ToList();
            }

            // 保存更新后的配置
            var serviceConfig = GetFullConfig();
            serviceConfig.MappingConfig = _tagConfigs;

            var configFile = Path.Combine(ConfigPath, "opcua.json");
            var json = JSON.Serialize(serviceConfig);
            File.WriteAllText(configFile, json);

            // 强制刷新缓存
            _ = GetTagConfigs();

            _ = SendLogAsync($"分组 {groupName} 中的标签删除成功", LogLevel.Info);
        }
        catch (Exception ex)
        {
            _ = SendLogAsync($"删除分组 {groupName} 中的标签失败: {ex.Message}", LogLevel.Error);
            Log.Error($"删除标签失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 保存标签配置
    /// </summary>
    public void SaveTagConfigs(Dictionary<string, List<OpcUaTagMapping>> configs)
    {
        try
        {
            _ = SendLogAsync($"开始保存标签配置，包含 {configs.Count} 个分组", LogLevel.Info);

            // 读取现有配置或使用默认配置
            var serviceConfig = GetFullConfig();

            // 更新映射配置
            serviceConfig.MappingConfig = configs;

            // 保存配置
            var configFile = Path.Combine(ConfigPath, "opcua.json");
            var directory = Path.GetDirectoryName(configFile);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var json = JSON.Serialize(serviceConfig);
            File.WriteAllText(configFile, json);

            // 更新内存缓存
            _tagConfigs = configs;

            // 强制刷新缓存确保一致性
            _ = GetTagConfigs();

            // 重建设备映射缓存
            InvalidateCache();

            // 如果服务未运行，则创建设备分组和变量
            if (!IsRunning)
            {
                // 重新创建启用的设备标签
                foreach (var group in configs)
                {
                    try
                    {
                        // 创建设备变量
                        CreateDeviceVariables(group.Value, group.Key);
                        _ = SendLogAsync($"为分组 {group.Key} 创建设备变量成功", LogLevel.Debug);
                    }
                    catch (Exception ex)
                    {
                        _ = SendLogAsync($"为分组 {group.Key} 创建设备变量失败: {ex.Message}", LogLevel.Error);
                    }
                }
            }

            _ = SendLogAsync("标签配置保存成功", LogLevel.Info);
        }
        catch (Exception ex)
        {
            _ = SendLogAsync($"保存标签配置失败: {ex.Message}", LogLevel.Error);
            Log.Error($"保存OPC UA标签配置失败: {ex.Message}");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    /// 订阅配置更新事件
    /// 当配置发生变化时，动态更新服务器配置
    /// </summary>
    private async Task SubscribeConfigUpdate()
    {
        await SendLogAsync("开始订阅配置更新事件");
        await MessageCenter.Subscribe("OpcUaConfigChanged", async context =>
        {
            try
            {
                if (_tokenSource.IsCancellationRequested)
                {
                    await SendLogAsync("取消令牌已请求，跳过配置更新");
                    return;
                }

                await SendLogAsync("收到配置更新事件");
                var payload = context.GetPayload<(OpcUaConfig Config, Dictionary<string, List<OpcUaTagMapping>> MappingConfig)>();

                await SendLogAsync($"新配置端口: {payload.Config?.Port}, 映射配置数量: {payload.MappingConfig?.Count ?? 0}");

                // 更新内存中的标签配置
                if (payload.MappingConfig != null)
                {
                    // 深拷贝配置防止引用问题
                    _tagConfigs = JSON.Deserialize<Dictionary<string, List<OpcUaTagMapping>>>(JSON.Serialize(payload.MappingConfig)) ?? new Dictionary<string, List<OpcUaTagMapping>>();

                    // 强制刷新缓存确保一致性
                    _ = GetTagConfigs();

                    // 重建设备映射缓存
                    InvalidateCache();
                    await SendLogAsync("设备映射缓存已重建");
                }
                else
                {
                    _tagConfigs = new Dictionary<string, List<OpcUaTagMapping>>();
                }

                // 如果服务正在运行，且端口发生变化，则重启服务
                if (IsRunning && (server?.m_server?.Port != payload.Config?.Port || server?.m_server?.LocalIpAddress != payload.Config?.LocalIpAddress))
                {
                    Stop();
                    Start();
                }
            }
            catch (Exception ex)
            {
                await SendLogAsync($"更新配置时发生错误: {ex.Message}", LogLevel.Error);
                Log.Error($"更新OPC UA配置失败: {ex.Message}");
            }
        }, cancellationToken: _tokenSource.Token);
    }

    /// <summary>
    /// 构建设备映射缓存
    /// </summary>
    private void BuildDeviceMappingCache()
    {
        try
        {
            var startTime = DateTime.Now;

            lock (_cacheLock)
            {
                // 清空现有缓存
                _deviceMappingCache.Clear();
                _deviceVariableCache.Clear();

                var tagConfigs = GetTagConfigs();
                if (tagConfigs == null || !tagConfigs.Any())
                {
                    return;
                }

                // 构建设备映射缓存
                foreach (var group in tagConfigs)
                {
                    foreach (var mapping in group.Value.Where(x => x.Enabled))
                    {
                        // 设备映射缓存
                        if (!_deviceMappingCache.ContainsKey(mapping.DeviceName))
                        {
                            _deviceMappingCache[mapping.DeviceName] = new List<OpcUaTagMapping>();
                        }
                        _deviceMappingCache[mapping.DeviceName].Add(mapping);

                        // 设备变量缓存
                        if (!_deviceVariableCache.ContainsKey(mapping.DeviceName))
                        {
                            _deviceVariableCache[mapping.DeviceName] = new Dictionary<string, OpcUaTagMapping>();
                        }
                        _deviceVariableCache[mapping.DeviceName][mapping.VariableName] = mapping;
                    }
                }

                var elapsed = DateTime.Now - startTime;
            }
        }
        catch (Exception ex)
        {
            Log.Error($"构建设备映射缓存失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 构建数据类型转换器缓存
    /// </summary>
    private void BuildTypeConverterCache()
    {
        try
        {
            var startTime = DateTime.Now;
            lock (_cacheLock)
            {
                _typeConverterCache.Clear();

                // 预编译所有支持的数据类型转换器
                _typeConverterCache["bool"] = value =>
                {
                    var result = Convert.ToBoolean(value); // 使用Convert.ToBoolean保持与回退方法一致
                    return result;
                };
                _typeConverterCache["bit"] = value =>
                {
                    var result = value == "1";
                    return result;
                };
                _typeConverterCache["int16"] = value => Convert.ToInt16(value);
                _typeConverterCache["bcd16"] = value => Convert.ToInt16(value);
                _typeConverterCache["uint16"] = value => Convert.ToUInt16(value);
                _typeConverterCache["int32"] = value => Convert.ToInt32(value);
                _typeConverterCache["bcd"] = value => Convert.ToInt32(value);
                _typeConverterCache["bcd32"] = value => Convert.ToInt32(value);
                _typeConverterCache["float"] = value => Convert.ToSingle(value);
                _typeConverterCache["uint32"] = value => Convert.ToUInt32(value);
                _typeConverterCache["int64"] = value => Convert.ToInt64(value);
                _typeConverterCache["bcd64"] = value => Convert.ToInt64(value);
                _typeConverterCache["double"] = value => Convert.ToDouble(value);
                _typeConverterCache["byte"] = value => value;
                _typeConverterCache["string"] = value => value;

                var elapsed = DateTime.Now - startTime;
            }
        }
        catch (Exception ex)
        {
            Log.Error($"构建数据类型转换器缓存失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 缓存失效和重建
    /// </summary>
    private void InvalidateCache()
    {
        try
        {
            var startTime = DateTime.Now;

            // 重置缓存统计
            _cacheHitCount = 0;
            _cacheMissCount = 0;

            // 清理和重新初始化消息模板缓存
            ClearMessageTemplates();
            InitializeMessageTemplates();

            // 重建所有缓存
            BuildDeviceMappingCache();
            BuildTypeConverterCache();

            // 清理过多的对象池对象
            CleanupObjectPools();

            var elapsed = DateTime.Now - startTime;
        }
        catch (Exception ex)
        {
            Log.Error($"缓存失效处理失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 从对象池获取WebSocket消息对象
    /// </summary>
    private WebSocketMessage GetWebSocketMessage()
    {
        if (_webSocketMessagePool.TryDequeue(out var message))
        {
            Interlocked.Decrement(ref _webSocketPoolCount);
            return message;
        }
        return new WebSocketMessage();
    }

    /// <summary>
    /// 将WebSocket消息对象返回对象池
    /// </summary>
    private void ReturnWebSocketMessage(WebSocketMessage message)
    {
        if (_webSocketPoolCount < MaxPoolSize)
        {
            message.Reset();
            _webSocketMessagePool.Enqueue(message);
            Interlocked.Increment(ref _webSocketPoolCount);
        }
    }

    /// <summary>
    /// 从对象池获取日志消息对象
    /// </summary>
    private LogMessage GetLogMessage()
    {
        if (_logMessagePool.TryDequeue(out var message))
        {
            Interlocked.Decrement(ref _logPoolCount);
            return message;
        }
        return new LogMessage();
    }

    /// <summary>
    /// 将日志消息对象返回对象池
    /// </summary>
    private void ReturnLogMessage(LogMessage message)
    {
        if (_logPoolCount < MaxPoolSize)
        {
            message.Reset();
            _logMessagePool.Enqueue(message);
            Interlocked.Increment(ref _logPoolCount);
        }
    }

    /// <summary>
    /// 获取缓存的时间字符串
    /// </summary>
    private string GetCurrentTimeString()
    {
        return DateTime.Now.ToString(_dateTimeFormat);
    }

    /// <summary>
    /// 初始化常用消息模板缓存
    /// </summary>
    private void InitializeMessageTemplates()
    {
        // 预缓存常用的消息模板
        _messageTemplateCache.TryAdd("device_data_received", "收到设备 {0} 的数据，包含 {1} 个变量");
        _messageTemplateCache.TryAdd("device_not_in_cache", "设备 {0} 未在缓存中找到，将回退到原始查找方式");
        _messageTemplateCache.TryAdd("node_update_start", "开始更新OPC UA节点 {0}，数据类型: {1}, 值: {2}");
        _messageTemplateCache.TryAdd("node_update_failed", "更新节点 {0} 失败: {1}");
        _messageTemplateCache.TryAdd("variable_update_success", "变量更新成功: 设备[{0}], 变量[{1}], 值[{2}]");
        _messageTemplateCache.TryAdd("fallback_processing", "使用回退方式处理设备 {0} 的数据");
        _messageTemplateCache.TryAdd("config_loaded", "加载标签配置，共有 {0} 个分组");
        _messageTemplateCache.TryAdd("cache_statistics", "缓存统计 - 命中: {0}, 未命中: {1}, 命中率: {2:F2}%");
        _messageTemplateCache.TryAdd("memory_usage", "内存使用情况 - 已用: {0}MB, GC次数: {1}");
    }

    /// <summary>
    /// 清理消息模板缓存
    /// </summary>
    private void ClearMessageTemplates()
    {
        _messageTemplateCache.Clear();
    }

    // 内存监控相关字段
    /// <summary>
    /// 内存监控定时器
    /// </summary>
    private Timer? _memoryMonitorTimer;

    /// <summary>
    /// 上次GC次数
    /// </summary>
    private long _lastGcCount = 0;

    /// <summary>
    /// 内存监控间隔（毫秒）
    /// </summary>
    private const int MemoryMonitorInterval = 120000; // 改为2分钟，减少监控频率

    /// <summary>
    /// 内存压力阈值（MB）
    /// </summary>
    private const long MemoryPressureThreshold = 500;

    /// <summary>
    /// 启动内存监控
    /// </summary>
    private void StartMemoryMonitoring()
    {
        _memoryMonitorTimer = new Timer(MonitorMemoryUsage, null, MemoryMonitorInterval, MemoryMonitorInterval);
    }

    /// <summary>
    /// 停止内存监控
    /// </summary>
    private void StopMemoryMonitoring()
    {
        _memoryMonitorTimer?.Dispose();
        _memoryMonitorTimer = null;
    }

    /// <summary>
    /// 监控内存使用情况（生产模式下减少日志输出）
    /// </summary>
    private void MonitorMemoryUsage(object? state)
    {
        try
        {
            var process = System.Diagnostics.Process.GetCurrentProcess();
            var workingSetMB = process.WorkingSet64 / 1024 / 1024;
            var currentGcCount = GC.CollectionCount(0) + GC.CollectionCount(1) + GC.CollectionCount(2);
            var gcSinceLastCheck = currentGcCount - _lastGcCount;
            _lastGcCount = currentGcCount;

            // 只在非生产模式或发生异常时记录内存统计
            if (!_isProductionMode || workingSetMB > MemoryPressureThreshold || gcSinceLastCheck > 20)
            {
                _ = SendLogAsync(string.Format(_messageTemplateCache["memory_usage"], workingSetMB, gcSinceLastCheck), LogLevel.Info);
            }
            // 如果内存使用超过阈值，触发自动清理
            if (workingSetMB > MemoryPressureThreshold)
            {
                PerformMemoryCleanup();
            }
        }
        catch (Exception ex)
        {
            _ = SendLogAsync($"内存监控异常: {ex.Message}", LogLevel.Error);
        }
    }

    /// <summary>
    /// 执行内存清理
    /// </summary>
    private void PerformMemoryCleanup()
    {
        try
        {
            // 清理对象池中的过多对象
            CleanupObjectPools();

            // 清理消息模板缓存中的过期项
            if (_messageTemplateCache.Count > 50)
            {
                var templatesToRemove = _messageTemplateCache.Keys.Skip(20).ToList();
                foreach (var key in templatesToRemove)
                {
                    _messageTemplateCache.TryRemove(key, out _);
                }
            }

            // 建议垃圾回收（但不强制）
            if (GC.GetTotalMemory(false) > 100 * 1024 * 1024) // 如果托管内存超过100MB
            {
                GC.Collect(0, GCCollectionMode.Optimized);
            }
        }
        catch (Exception ex)
        {
            _ = SendLogAsync($"内存清理失败: {ex.Message}", LogLevel.Error);
        }
    }

    /// <summary>
    /// 清理对象池
    /// </summary>
    private void CleanupObjectPools()
    {
        // 如果WebSocket对象池过大，清理一部分
        if (_webSocketPoolCount > MaxPoolSize / 2)
        {
            var itemsToRemove = _webSocketPoolCount / 4; // 清理四分之一
            for (int i = 0; i < itemsToRemove; i++)
            {
                if (_webSocketMessagePool.TryDequeue(out _))
                {
                    Interlocked.Decrement(ref _webSocketPoolCount);
                }
            }
        }

        // 如果日志对象池过大，清理一部分
        if (_logPoolCount > MaxPoolSize / 2)
        {
            var itemsToRemove = _logPoolCount / 4; // 清理四分之一
            for (int i = 0; i < itemsToRemove; i++)
            {
                if (_logMessagePool.TryDequeue(out _))
                {
                    Interlocked.Decrement(ref _logPoolCount);
                }
            }
        }
    }
}