namespace Feng.IotGateway.OpcUaService.Models;

/// <summary>
/// OPC UA标签映射配置
/// </summary>
public class OpcUaTagMapping
{
  /// <summary>
  /// 设备名称
  /// </summary>
  public string DeviceName { get; set; } 

  /// <summary>
  /// 设备变量标识
  /// </summary>
  public string VariableName { get; set; } 

  /// <summary>
  /// OPC UA节点ID
  /// </summary>
  public string NodeId { get; set; }

  /// <summary>
  /// 中文名称
  /// </summary>
  public string DisplayName { get; set; }

  /// <summary>
  /// 描述
  /// </summary>
  public string Description { get; set; }
  
  /// <summary>
  /// 数据类型
  /// </summary>
  public string DataType { get; set; }

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool Enabled { get; set; } = true;
}
