using Feng.IotGateway.Core.Base;
using Feng.IotGateway.OpcUaService.Models;

namespace IotGateway.OpcUaServer.Models;

/// <summary>
/// OPC UA服务详情
/// </summary>
public class OpcUaDetail
{
  /// <summary>
  /// 是否运行
  /// </summary>
  public bool IsRunning { get; set; }

  /// <summary>
  /// 配置信息
  /// </summary>
  public OpcUaConfig Config { get; set; } = null!;
}

/// <summary>
/// 获取标签配置输入
/// </summary>
public class GetTagConfigsInput : BasePageInput
{
  /// <summary>
  /// 分组名称
  /// </summary>
  public string GroupName { get; set; } = string.Empty;
}

/// <summary>
/// 创建设备变量输入
/// </summary>
public class CreateVariablesInput
{
  /// <summary>
  /// 分组名称
  /// </summary>
  public string GroupName { get; set; } = string.Empty;

  /// <summary>
  /// 设备变量列表
  /// </summary>
  public List<OpcUaTagMapping> DeviceVariables { get; set; } = new List<OpcUaTagMapping>();
}

/// <summary>
/// 删除分组输入
/// </summary>
public class DeleteGroupInput
{
  /// <summary>
  /// 分组名称
  /// </summary>
  public string GroupName { get; set; } = string.Empty;
}

/// <summary>
/// 添加分组输入
/// </summary>
public class AddGroupInput
{
  /// <summary>
  /// 分组名称
  /// </summary>
  public string GroupName { get; set; } = string.Empty;
}
/// <summary>
/// 删除标签输入
/// </summary>
public class DeleteTagsInput
{
  /// <summary>
  /// 分组名称
  /// </summary>
  public string GroupName { get; set; } = string.Empty;

  /// <summary>
  /// 标签名称列表
  /// </summary>
  public List<string> TagNames { get; set; } = new List<string>();
}
