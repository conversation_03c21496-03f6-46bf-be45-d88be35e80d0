using System.Runtime.Serialization;

namespace Feng.IotGateway.OpcUaService.UA.Server;

/// <summary>
///     Stores the configuration the data access node manager.
/// </summary>
[DataContract(Namespace = Namespaces.ReferenceServer)]
public class ReferenceServerConfiguration
{
    #region Public Properties

    /// <summary>
    ///     Whether the user dialog for accepting invalid certificates should be displayed.
    /// </summary>
    [DataMember(Order = 1)]
    public bool ShowCertificateValidationDialog { get; set; }

    #endregion

    #region Constructors

    /// <summary>
    ///     The default constructor.
    /// </summary>
    public ReferenceServerConfiguration()
    {
        Initialize();
    }

    /// <summary>
    ///     Initializes the object during deserialization.
    /// </summary>
    [OnDeserializing]
    private void Initialize(StreamingContext context)
    {
        Initialize();
    }

    /// <summary>
    ///     Sets private members to default values.
    /// </summary>
    private static void Initialize()
    {
    }

    #endregion
}