using System.Text;
using Opc.Ua;
using Opc.Ua.Configuration;
using Opc.Ua.Server;

namespace Feng.IotGateway.OpcUaService.UA.Server;

/// <summary>
/// </summary>
/// <typeparam name="T"></typeparam>
public class UaServer<T> where T : StandardServer, new()
{
    /// <summary>
    ///     Ctor of the server.
    /// </summary>
    /// <param name="writer">The text output.</param>
    public UaServer(TextWriter writer)
    {
        m_output = writer;
    }

    /// <summary>
    ///     应用程序实例
    /// </summary>
    public ApplicationInstance Application { get; private set; }

    /// <summary>
    ///     应用程序配置
    /// </summary>
    public ApplicationConfiguration Configuration => Application.ApplicationConfiguration;

    /// <summary>
    ///     自动接受
    /// </summary>  
    public bool AutoAccept { get; set; }
    /// <summary>
    ///     密码
    /// </summary>
    public string Password { get; set; }
    /// <summary>
    ///     退出码
    /// </summary>
    public ExitCode ExitCode { get; private set; }

    /// <summary>
    ///     加载应用配置
    /// </summary>
    public async Task LoadAsync(string applicationName, string configSectionName)
    {
        try
        {
            // 设置退出码
            ExitCode = ExitCode.ErrorNotStarted;

            // 创建消息对话框
            ApplicationInstance.MessageDlg = new ApplicationMessageDlg(m_output);
            // 创建密码提供者
            var passwordProvider = new CertificatePasswordProvider(Password);
            // 创建应用程序实例
            Application = new ApplicationInstance
            {
                ApplicationName = applicationName, // 应用程序名称
                ApplicationType = ApplicationType.Server, // 应用程序类型
                ConfigSectionName = configSectionName, // 配置节名称
                CertificatePasswordProvider = passwordProvider, // 证书密码提供者

            };

            // 加载应用程序配置
            await Application.LoadApplicationConfiguration(false).ConfigureAwait(false);

            // 修改端点配置
            if (m_server != null && Application.ApplicationConfiguration.ServerConfiguration != null)
            {
                try
                {
                    var referenceServer = m_server as ReferenceServer;
                    if (referenceServer != null && !string.IsNullOrEmpty(referenceServer.LocalIpAddress))
                    {
                        // 获取端点配置
                        var addresses = Application.ApplicationConfiguration.ServerConfiguration.BaseAddresses.ToList();
                        bool addressesModified = false;

                        for (int i = 0; i < addresses.Count; i++)
                        {
                            try
                            {
                                // 获取端点地址
                                var url = new Uri(addresses[i]);

                                // 只修改opc.tcp协议的端点
                                if (url.Scheme.Equals("opc.tcp", StringComparison.OrdinalIgnoreCase))
                                {
                                    // 处理特殊的IP地址情况
                                    string hostToUse = referenceServer.LocalIpAddress;
                                    if (hostToUse == "0.0.0.0")
                                    {
                                        // 对于0.0.0.0，在URL中使用localhost
                                        hostToUse = "localhost";
                                    }

                                    // 修改端口号和主机名
                                    var builder = new UriBuilder(url)
                                    {
                                        Port = referenceServer.Port > 0 ? referenceServer.Port : url.Port,  // 使用传入的端口，如果端口为0则保持原端口
                                        Host = hostToUse  // 使用处理后的主机名
                                    };

                                    addresses[i] = builder.Uri.ToString();
                                    addressesModified = true;
                                }
                            }
                            catch (Exception ex)
                            {
                                // 继续处理下一个地址
                            }
                        }

                        // 只有在地址被修改的情况下才更新配置
                        if (addressesModified)
                        {
                            Application.ApplicationConfiguration.ServerConfiguration.BaseAddresses = new StringCollection(addresses);
                        }
                    }
                    else
                    {
                    }
                }
                catch (Exception ex)
                {
                    // 记录错误但不中断启动过程
                    Console.WriteLine($"[OPC UA] 修改端点配置时出错: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            throw new ErrorExitException(ex.Message, ExitCode);
        }
    }

    /// <summary>
    ///     检查证书
    /// </summary>
    public async Task CheckCertificateAsync(bool renewCertificate)
    {
        try
        {
            var config = Application.ApplicationConfiguration;
            if (renewCertificate)
            {
                //await m_application.DeleteApplicationInstanceCertificate().ConfigureAwait(false);
            }

            // 应用实例证书无效
            var haveAppCertificate = await Application.CheckApplicationInstanceCertificate(false, 0).ConfigureAwait(false);
            if (!haveAppCertificate)
                throw new Exception("Application instance certificate invalid!");

            // 自动接受不信任的证书
            if (!config.SecurityConfiguration.AutoAcceptUntrustedCertificates)
                config.CertificateValidator.CertificateValidation += CertificateValidator_CertificateValidation;
        }
        catch (Exception ex)
        {
            throw new ErrorExitException(ex.Message, ExitCode);
        }
    }

    /// <summary>
    ///    启动服务.
    /// </summary>
    public async Task StartAsync()
    {
        try
        {
            // 创建服务.
            m_server ??= new T();
            // 启动服务
            await Application.Start(m_server).ConfigureAwait(false);

            // save state
            ExitCode = ExitCode.ErrorRunning;

            // 启动状态线程
            m_status = Task.Run(StatusThreadAsync);

            // 订阅连接状态通知事件
            m_server.CurrentInstance.SessionManager.SessionActivated += EventStatus;
            m_server.CurrentInstance.SessionManager.SessionClosing += EventStatus;
            m_server.CurrentInstance.SessionManager.SessionCreated += EventStatus;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[OPC UA Server] 启动服务失败: {ex.Message}\n{ex.StackTrace}");
            throw new ErrorExitException(ex.Message, ExitCode);
        }
    }

    /// <summary>
    ///     停止服务
    /// </summary>
    public async Task StopAsync()
    {
        try
        {
            // 如果服务器不为空
            if (m_server != null)
            {
                // 使用服务器
                using var server = m_server;
                // Stop status thread
                // 设置服务器为空
                m_server = null;
                // 等待状态线程完成
                await m_status.ConfigureAwait(false);

                // 停止服务器并释放
                server.Stop();
            }

            ExitCode = ExitCode.Ok;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[OPC UA Server] 停止服务失败: {ex.Message}\n{ex.StackTrace}");
            throw new ErrorExitException(ex.Message, ExitCode.ErrorStopping);
        }
    }

    /// <summary>
    ///     使用证书校验器
    ///     如果在配置中未选择自动接受
    /// </summary>
    private void CertificateValidator_CertificateValidation(CertificateValidator validator, CertificateValidationEventArgs e)
    {
        // 如果错误状态码为BadCertificateUntrusted
        if (e.Error.StatusCode == StatusCodes.BadCertificateUntrusted)
            // 如果自动接受
            if (AutoAccept)
            {
                // 接受证书
                e.Accept = true;
                return;
            }
    }

    /// <summary>
    ///     修改连接状态
    /// </summary>
    private void EventStatus(Session session, SessionEventReason reason)
    {
        // 更新最后事件时间
        m_lastEventTime = DateTime.UtcNow;
        // 打印连接状态
        PrintSessionStatus(session, reason.ToString());
    }

    /// <summary>
    ///     输出连接状态.
    /// </summary>
    private void PrintSessionStatus(Session session, string reason, bool lastContact = false)
    {
        // 锁定诊断锁
        lock (session.DiagnosticsLock)
        {
            // 创建字符串构建器 
            var item = new StringBuilder();
            // 添加原因和会话名称
            item.AppendFormat("{0,9}:{1,20}:", reason, session.SessionDiagnostics.SessionName);
            // 如果最后联系时间
            if (lastContact)
            {
                // 添加最后联系时间
                item.AppendFormat("Last Event:{0:HH:mm:ss}", session.SessionDiagnostics.ClientLastContactTime.ToLocalTime());
            }
            else
            {
                // 如果身份不为空
                if (session.Identity != null) item.AppendFormat(":{0,20}", session.Identity.DisplayName);
                // 添加会话ID
                item.AppendFormat(":{0}", session.Id);
            }
        }
    }

    /// <summary>
    ///     状态线程,每10秒打印一次
    /// </summary>
    private async Task StatusThreadAsync()
    {
        // 如果服务器不为空
        while (m_server != null)
        {
            // 如果最后事件时间大于10秒
            if (DateTime.UtcNow - m_lastEventTime > TimeSpan.FromMilliseconds(10000))
            {
                // 获取会话列表
                IList<Session> sessions = m_server.CurrentInstance.SessionManager.GetSessions();
                // 遍历会话列表
                for (var ii = 0; ii < sessions.Count; ii++)
                {
                    // 获取会话
                    var session = sessions[ii];
                    // 打印会话状态
                    PrintSessionStatus(session, "-Status-", true);
                }
                // 更新最后事件时间
                m_lastEventTime = DateTime.UtcNow;
            }

            try
            {
                // 等待1秒
                await Task.Delay(1000).ConfigureAwait(false);
            }
            catch
            {
                // 取消
                break;
            }
        }
    }

    #region Private Members

    /// <summary>
    ///     输出
    /// </summary>
    private readonly TextWriter m_output;
    /// <summary>
    ///     服务器
    /// </summary>
    public T m_server;
    /// <summary>
    ///     状态
    /// </summary>
    private Task m_status;
    /// <summary>
    ///     最后事件时间
    /// </summary>
    private DateTime m_lastEventTime;

    #endregion
}