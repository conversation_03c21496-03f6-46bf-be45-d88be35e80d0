using System.Reflection;
using System.Security.Cryptography.X509Certificates;
using Opc.Ua;
using Opc.Ua.Server;
using System.Net;
using System.Net.Sockets;

namespace Feng.IotGateway.OpcUaService.UA.Server;

/// <summary>
///     Implements the Quickstart Reference Server.
///     实现快速入门参考服务器
/// </summary>
/// <remarks>
///     Each server instance must have one instance of a StandardServer object which is
///     responsible for reading the configuration file, creating the endpoints and dispatching
///     incoming requests to the appropriate handler.
///     This sub-class specifies non-configurable metadata such as Product Name and initializes
///     the EmptyNodeManager which provides access to the data exposed by the Server.
///     每个服务器实例必须有一个标准服务器对象的实例，该对象是负责读取配置文件、创建端点和调度传入到适当处理程序的请求。这个子类指定不可配置的元数据，如产品名称并初始化提供对服务器公开的数据的访问的 空节点管理器。
/// </remarks>
public class ReferenceServer : ReverseConnectServer
{
    public ReferenceServer()
    {
    }
    public List<INodeManager> nodeManagers = new();

    // 添加端口属性
    public int Port { get; set; }
    public string LocalIpAddress { get; set; }

    #region Overridden Methods

    /// <summary>
    ///     Creates the node managers for the server.
    /// </summary>
    /// <remarks>
    ///     This method allows the sub-class create any additional node managers which it uses. The SDK
    ///     always creates a CoreNodeManager which handles the built-in nodes defined by the specification.
    ///     Any additional NodeManagers are expected to handle application specific nodes.
    /// </remarks>
    protected override MasterNodeManager CreateMasterNodeManager(IServerInternal server, ApplicationConfiguration configuration)
    {
        // create the custom node managers.
        // 添加参考节点管理器
        nodeManagers.Add(new ReferenceNodeManager(server, configuration));
        // 如果节点管理器工厂为空或者节点管理器工厂数量为0
        if (m_nodeManagerFactory == null || m_nodeManagerFactory.Count == 0) AddDefaultFactories();
        // 遍历节点管理器工厂
        foreach (var nodeManagerFactory in m_nodeManagerFactory) nodeManagers.Add(nodeManagerFactory.Create(server, configuration));

        // create master node manager.
        return new MasterNodeManager(server, configuration, null, nodeManagers.ToArray());
    }

    /// <summary>
    ///     Loads the non-configurable properties for the application.
    /// </summary>
    /// <remarks>
    ///     These properties are exposed by the server but cannot be changed by administrators.
    /// </remarks>
    protected override ServerProperties LoadServerProperties()
    {
        var properties = new ServerProperties();

        properties.ManufacturerName = "OPC Foundation";
        properties.ProductName = "Quickstart Reference Server";
        properties.ProductUri = "http://opcfoundation.org/Quickstart/ReferenceServer/v1.04";
        properties.SoftwareVersion = Utils.GetAssemblySoftwareVersion();
        properties.BuildNumber = Utils.GetAssemblyBuildNumber();
        properties.BuildDate = Utils.GetAssemblyTimestamp();

        return properties;
    }

    /// <summary>
    ///     Creates the resource manager for the server.
    /// </summary>
    protected override ResourceManager CreateResourceManager(IServerInternal server, ApplicationConfiguration configuration)
    {
        var resourceManager = new ResourceManager(server, configuration);

        var fields = typeof(StatusCodes).GetFields(BindingFlags.Public | BindingFlags.Static);

        foreach (var field in fields)
        {
            var id = field.GetValue(typeof(StatusCodes)) as uint?;

            if (id != null) resourceManager.Add(id.Value, "en-US", field.Name);
        }

        return resourceManager;
    }

    /// <summary>
    ///     Initializes the server before it starts up.
    /// </summary>
    /// <remarks>
    ///     This method is called before any startup processing occurs. The sub-class may update the
    ///     configuration object or do any other application specific startup tasks.
    /// </remarks>
    protected override void OnServerStarting(ApplicationConfiguration configuration)
    {
        // // 修改端点配置
        // if (configuration.ServerConfiguration != null)
        // {
        //     // 获取端点配置
        //     var addresses = configuration.ServerConfiguration.BaseAddresses.ToList();
        //     for (int i = 0; i < addresses.Count; i++)
        //     {
        //         // 获取端点地址
        //         var url = new Uri(addresses[i]);
        //
        //         // 处理特殊的IP地址情况
        //         string hostToUse = LocalIpAddress;
        //         if (LocalIpAddress == "0.0.0.0")
        //         {
        //             // 对于0.0.0.0，在URL中使用localhost或机器名
        //             hostToUse = "localhost";
        //             Console.WriteLine($"[OPC UA] 检测到LocalIpAddress为0.0.0.0，在端点URL中使用{hostToUse}");
        //         }
        //
        //         // 修改端口号和主机名
        //         var builder = new UriBuilder(url)
        //         {
        //             Port = Port,  // 使用传入的端口
        //             Host = hostToUse  // 使用处理后的主机名
        //         };
        //         Console.WriteLine($"[OPC UA] 原始端点URL: {url}, 修改后URL: {builder.Uri}");
        //         addresses[i] = builder.Uri.ToString();
        //     }
        //     configuration.ServerConfiguration.BaseAddresses = new StringCollection(addresses);
        // }

        base.OnServerStarting(configuration);

        // 创建用户身份验证器
        CreateUserIdentityValidators(configuration);
    }

    /// <summary>
    ///     服务器启动后调用
    /// </summary>
    protected override void OnServerStarted(IServerInternal server)
    {
        // 调用基类方法
        base.OnServerStarted(server);

        // 请求用户身份更改时的通知。所有有效用户默认接受。
        server.SessionManager.ImpersonateUser += SessionManager_ImpersonateUser;

        try
        {
            // 锁定状态锁
            lock (ServerInternal.Status.Lock)
            {
                // 允许更快的时间采样间隔
                ServerInternal.Status.Variable.CurrentTime.MinimumSamplingInterval = 250;
            }
        }
        catch
        {
        }
    }

    #endregion

    #region User Validation Functions

    /// <summary>
    ///     创建用于验证服务器支持的用户身份令牌的对象。
    /// </summary>
    private void CreateUserIdentityValidators(ApplicationConfiguration configuration)
    {
        for (var ii = 0; ii < configuration.ServerConfiguration.UserTokenPolicies.Count; ii++)
        {
            var policy = configuration.ServerConfiguration.UserTokenPolicies[ii];

            // create a validator for a certificate token policy.
            if (policy.TokenType == UserTokenType.Certificate)
                // check if user certificate trust lists are specified in configuration.
                if (configuration.SecurityConfiguration.TrustedUserCertificates != null &&
                    configuration.SecurityConfiguration.UserIssuerCertificates != null)
                {
                    var certificateValidator = new CertificateValidator();
                    certificateValidator.Update(configuration.SecurityConfiguration).Wait();
                    certificateValidator.Update(configuration.SecurityConfiguration.UserIssuerCertificates,
                        configuration.SecurityConfiguration.TrustedUserCertificates,
                        configuration.SecurityConfiguration.RejectedCertificateStore);

                    // set custom validator for user certificates.
                    m_userCertificateValidator = certificateValidator.GetChannelValidator();
                }
        }
    }

    /// <summary>
    ///     当客户端尝试更改其用户身份时调用。
    /// </summary>
    private void SessionManager_ImpersonateUser(Session session, ImpersonateEventArgs args)
    {
        // 检查是否为用户名令牌
        var userNameToken = args.NewIdentity as UserNameIdentityToken;
        // 如果用户名令牌不为空
        if (userNameToken != null)
        {
            // 验证密码
            args.Identity = VerifyPassword(userNameToken);
            // 设置已认证用户角色
            args.Identity.GrantedRoleIds.Add(ObjectIds.WellKnownRole_AuthenticatedUser);
            // 如果身份为系统配置身份
            if (args.Identity is SystemConfigurationIdentity)
            {
                // 设置配置管理员角色
                args.Identity.GrantedRoleIds.Add(ObjectIds.WellKnownRole_ConfigureAdmin);
                // 设置安全管理员角色
                args.Identity.GrantedRoleIds.Add(ObjectIds.WellKnownRole_SecurityAdmin);
            }
            // 返回
            return;
        }
        // 检查是否为x509用户令牌
        var x509Token = args.NewIdentity as X509IdentityToken;
        // 如果x509用户令牌不为空
        if (x509Token != null)
        {
            // 验证证书
            VerifyUserTokenCertificate(x509Token.Certificate);
            // 设置已认证用户角色
            args.Identity = new UserIdentity(x509Token);
            // 设置已认证用户角色
            args.Identity.GrantedRoleIds.Add(ObjectIds.WellKnownRole_AuthenticatedUser);
            // 返回
            return;
        }

        // 检查是否为匿名令牌
        if (args.NewIdentity is AnonymousIdentityToken || args.NewIdentity == null)
        {
            // 允许匿名认证并设置匿名角色
            args.Identity = new UserIdentity();
            args.Identity.GrantedRoleIds.Add(ObjectIds.WellKnownRole_Anonymous);
            // 返回
            return;
        }

        // 不支持的身份令牌类型
        throw ServiceResultException.Create(StatusCodes.BadIdentityTokenInvalid,
            "Not supported user token type: {0}.", args.NewIdentity);
    }

    /// <summary>
    ///     验证用户名令牌的密码。
    /// </summary>
    private IUserIdentity VerifyPassword(UserNameIdentityToken userNameToken)
    {
        var userName = userNameToken.UserName;
        var password = userNameToken.DecryptedPassword;
        if (string.IsNullOrEmpty(userName))
            // an empty username is not accepted.
            throw ServiceResultException.Create(StatusCodes.BadIdentityTokenInvalid,
                "Security token is not a valid username token. An empty username is not accepted.");

        if (string.IsNullOrEmpty(password))
            // an empty password is not accepted.
            throw ServiceResultException.Create(StatusCodes.BadIdentityTokenRejected,
                "Security token is not a valid username token. An empty password is not accepted.");

        // User with permission to configure server
        if (userName == "sysadmin" && password == "demo") return new SystemConfigurationIdentity(new UserIdentity(userNameToken));

        // standard users for CTT verification
        if (!((userName == "user1" && password == "password") ||
              (userName == "user2" && password == "password1")))
        {
            // construct translation object with default text.
            var info = new TranslationInfo(
                "InvalidPassword",
                "en-US",
                "Invalid username or password.",
                userName);

            // create an exception with a vendor defined sub-code.
            throw new ServiceResultException(new ServiceResult(
                StatusCodes.BadUserAccessDenied,
                "InvalidPassword",
                LoadServerProperties().ProductUri,
                new LocalizedText(info)));
        }

        return new UserIdentity(userNameToken);
    }

    /// <summary>
    ///     验证证书用户令牌是否受信任。
    /// </summary>
    private void VerifyUserTokenCertificate(X509Certificate2 certificate)
    {
        try
        {
            if (m_userCertificateValidator != null)
                m_userCertificateValidator.Validate(certificate);
            else
                CertificateValidator.Validate(certificate);
        }
        catch (Exception e)
        {
            TranslationInfo info;
            StatusCode result = StatusCodes.BadIdentityTokenRejected;
            var se = e as ServiceResultException;
            if (se != null && se.StatusCode == StatusCodes.BadCertificateUseNotAllowed)
            {
                info = new TranslationInfo(
                    "InvalidCertificate",
                    "en-US",
                    "'{0}' is an invalid user certificate.",
                    certificate.Subject);

                result = StatusCodes.BadIdentityTokenInvalid;
            }
            else
            {
                // construct translation object with default text.
                info = new TranslationInfo(
                    "UntrustedCertificate",
                    "en-US",
                    "'{0}' is not a trusted user certificate.",
                    certificate.Subject);
            }

            // create an exception with a vendor defined sub-code.
            throw new ServiceResultException(new ServiceResult(
                result,
                info.Key,
                LoadServerProperties().ProductUri,
                new LocalizedText(info)));
        }
    }

    /// <summary>
    ///     检查是否为节点管理器工厂类型
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    private static INodeManagerFactory IsINodeManagerFactoryType(Type type)
    {
        // 获取类型信息
        var nodeManagerTypeInfo = type.GetTypeInfo();
        // 如果类型是抽象的或者不是INodeManagerFactory的子类
        if (nodeManagerTypeInfo.IsAbstract ||
            !typeof(INodeManagerFactory).IsAssignableFrom(type))
            return null;
        // 创建节点管理器工厂
        return Activator.CreateInstance(type) as INodeManagerFactory;
    }

    /// <summary>
    ///     添加默认工厂
    /// </summary>
    private void AddDefaultFactories()
    {
        // 获取当前程序集
        var assembly = GetType().Assembly;
        // 获取所有导出的类型
        var factories = assembly.GetExportedTypes().Select(type => IsINodeManagerFactoryType(type)).Where(type => type != null);
        // 创建节点管理器工厂列表
        m_nodeManagerFactory = new List<INodeManagerFactory>();
        // 遍历工厂
        foreach (var nodeManagerFactory in factories) m_nodeManagerFactory.Add(nodeManagerFactory);
    }

    #endregion

    #region Private Fields

    /// <summary>
    ///     节点管理器工厂
    /// </summary>
    private IList<INodeManagerFactory> m_nodeManagerFactory;
    /// <summary>
    ///     用户证书验证器
    /// </summary>
    private ICertificateValidator m_userCertificateValidator;
    #endregion
}