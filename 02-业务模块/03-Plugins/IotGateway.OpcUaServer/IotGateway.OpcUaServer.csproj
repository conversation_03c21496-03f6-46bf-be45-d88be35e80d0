<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>Feng.IotGateway.OpcUaService</RootNamespace>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\04-架构核心\IotGateway.Core\IotGateway.Core.csproj"/>
        <ProjectReference Include="..\..\..\04-架构核心\IotGateway.WebSocket\IotGateway.WebSocket.csproj"/>
        <ProjectReference Include="..\IotGateway.Plugin.Core\IotGateway.Plugin.Core.csproj" />
    </ItemGroup>

</Project>
