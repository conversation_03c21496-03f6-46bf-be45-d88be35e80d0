using IotGateway.OpcUaServer.Models;
using IotGateway.Plugin.Core;

namespace IotGateway.OpcUaServer;

public class OpcUaServerPlugin : PluginBase
{
  private readonly OpcUaConfig _config;

  public OpcUaServerPlugin()
  {
    _config = new OpcUaConfig();
    _configuration = _config;
  }

  public override string Name => "OPC UA Server";
  public override string Version => "1.0.0";
  public override string Description => "OPC UA服务器插件";

  public override async Task StartAsync()
  {
    await base.StartAsync();
    // 启动OPC UA服务器的具体实现
  }

  public override async Task StopAsync()
  {
    // 停止OPC UA服务器的具体实现
    await base.StopAsync();
  }
}