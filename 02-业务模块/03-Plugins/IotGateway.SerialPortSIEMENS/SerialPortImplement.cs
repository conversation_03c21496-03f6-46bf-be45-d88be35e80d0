using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;
using System.Timers;
using Furion.Logging;
using HyIotGatewayDnc.Plugin.SerialPortSIEMENS;
using IotGateway.SerialPortSIEMENS.Models;
using NewLife.Log;
using SerialPortProtocol;
using SerialPortProtocol.Enum;
using SerialPortProtocol.Event;
using SerialPortProtocol.Utils;
using Timer = System.Timers.Timer;

namespace IotGateway.SerialPortSIEMENS;

public class SerialPortImplement
{
    // 属性声明
    public string NCDeviceRoot { get; set; } = string.Empty;
    public string NCDeviceRoot_Send { get; set; } = string.Empty;
    public string NCDeviceRoot_Rec { get; set; } = string.Empty;
    public bool IsAuto { get; set; } = true;
    public bool IsPrint { get; set; } = true;
    public bool IsCheckNet { get; set; } = false;
    public ProgressInfoDto ProgressInfo { get; set; } = new();

    public ISerialServer SerialServer { get; set; }

    // 受保护属性
    protected int _sendDelayTime { get; set; } = 10000;
    protected bool _isWriteOver { get; set; } = true;

    // 初始化方法
    internal SerialPortImplement InitServer(SerialPortServiceConfig config, SerialPortSIEMENSConfig deviceInfo)
    {
        _pluginConfig = config;
        _sendDelayTime = config.SendDelayTime;
        _deviceInfo = deviceInfo.SetConfig(config);

        if (deviceInfo == null) Log.Warning("DeviceInfo load failed!");

        // 路径配置
        NCDeviceRoot = deviceInfo.WorkFolder;
        NCDeviceRoot_Send = Path.Combine(deviceInfo.WorkFolder, deviceInfo.WorkFolderSend);
        NCDeviceRoot_Rec = Path.Combine(deviceInfo.WorkFolder, deviceInfo.WorkFolderRec);

        // 串口服务初始化
        SerialServer = new SerialServerBase();
        SerialServer.XON = deviceInfo.XON;
        SerialServer.XOFF = deviceInfo.XOFF;
        SerialServer.RtsEnable = deviceInfo.RtsEnable;
        SerialServer.DtrEnable = deviceInfo.DtrEnable;

        // 事件绑定（完整实现）
        SerialServer.WriteCacheQueueOver += delegate (WriteCacheQueue e)
        {
            Log.Information($"{_deviceInfo.DeviceId} CacheWriteCount：{e.CacheQueueCount} IsFinish：{e.IsFinish}");
            Log.Information($"{_deviceInfo.DeviceId} 停止[{DateTime.Now:yyyy-MM-dd HH:mm:ss}]");
            _sendList = new List<string>();
            _bufOfASCII = null;
            _isWriteOver = true;
        };

        SerialServer.ConnectChanged += delegate (ConnectChangedEvent e) { Log.Information($"{_deviceInfo.DeviceId} {e.Server.Option.PortName} 状态：{e.Status}"); };

        SerialServer.DataReadOver += delegate (ReadWriteEvent e)
        {
            var hex = BytesTools.ByteArrToHex(e.Data, e.Offset, e.Count);
            Log.Information($"{_deviceInfo.DeviceId} R[HEX]: {hex}");

            // 控制字符处理
            if (e.Count == 1 && e.Data[0] < 32)
            {
                var v = e.Data[0];
                Log.Information($"{_deviceInfo.DeviceId} {e.RWType}: {v}");

                if (v == SerialServer.XOFF)
                {
                    SerialServer.IsPause = true;
                    Log.Information($"{_deviceInfo.DeviceId} 暂停[{DateTime.Now:yyyy-MM-dd HH:mm:ss}]");
                }
                else if (v == SerialServer.XON)
                {
                    if (!IsAuto)
                    {
                        SerialServer.IsPause = false;
                        Log.Information($"{_deviceInfo.DeviceId} 发送[{DateTime.Now:yyyy-MM-dd HH:mm:ss}]");

                        if (SerialServer.WriteByteOffset == 0 &&
                            SerialServer.WriteLineOffset == 0 &&
                            _sendList.Count > 0)
                            try
                            {
                                new Thread(WriteMachineSerial).Start();
                            }
                            catch (Exception ex)
                            {
                                Log.Error($"{_deviceInfo.DeviceId} 异常：{ex.Message}");
                            }
                    }
                }
            }
            else // 数据包处理
            {
                var strRevNow = BytesTools.ByteArrToASCII(e.Data, e.Offset, e.Count);
                _strRevNC += strRevNow;

                // 协议解析
                var strStartMark = _deviceInfo.OrderPrefix ?? "Q";
                var strEndMark = _deviceInfo.OrderSuffix ?? "V";

                if (!_ncStart)
                {
                    var startIndex = _strRevNC.IndexOf(strStartMark);
                    if (startIndex > -1)
                    {
                        _ncStart = true;
                        _strRevNC = _strRevNC.Substring(startIndex + strStartMark.Length);
                    }
                }

                if (!_ncEnd)
                {
                    var endIndex = _strRevNC.IndexOf(strEndMark) != -1
                        ? _strRevNC.IndexOf(strEndMark)
                        : _strRevNC.IndexOf('\u001a');

                    if (endIndex > -1)
                    {
                        _ncEnd = true;
                        ProcessNC(strStartMark + _strRevNC);
                        _strRevNC = string.Empty;
                        _ncStart = false;
                        _ncEnd = false;
                    }
                }
            }
        };

        SerialServer.DataWriteOver += delegate { };
        return this;
    }

    // 串口操作方法
    // 串口打开方法（完整实现）
    public void OpenSerialPort()
    {
        try
        {
            if (SerialServer == null)
            {
                Log.Warning($"{_deviceInfo.DeviceId} 串口服务未初始化,OpenSerialPort");
                return;
            }

            if (!SerialServer.IsOpen)
            {
                SerialServer.Start(_deviceInfo.ToSerialModelBase());
                SerialServer.IsNetCheck = IsCheckNet;

                if (SerialServer.IsOpen)
                {
                    // 启动状态监控定时器
                    var t = new Timer(1000.0);
                    t.Elapsed += Execute;
                    t.AutoReset = true;
                    t.Enabled = true;
                    t.Start();
                }
                else
                {
                    Log.Warning($"{_deviceInfo.DeviceId} {_deviceInfo.PortName} 串口打开失败");
                }
            }
        }
        catch (Exception ex)
        {
            Log.Error($"{_deviceInfo.DeviceId} {_deviceInfo.PortName} 打开串口异常,{ex.Message}");
        }
    }

    public void Execute(object source, ElapsedEventArgs e)
    {
    }

    // 串口关闭方法（完整实现）
    public void CloseSerialPort()
    {
        try
        {
            if (SerialServer == null)
            {
                Log.Warning($"{_deviceInfo.DeviceId} 串口服务未初始化,CloseSerialPort");
                return;
            }

            if (SerialServer.IsOpen) SerialServer.Dispose();
        }
        catch (Exception ex)
        {
            Log.Error($"{_deviceInfo.DeviceId} 关闭串口异常,{ex.Message}");
        }
    }

    // 数据写入核心逻辑（完整实现）
    protected void WriteMachineSerial()
    {
        _isWriteOver = false;

        if (SerialServer.Option.WriteType == EmWriteType.BYTE)
            // 字节模式写入
            for (var i = 0; i < _bufOfASCII.Length; i++)
            {
                SerialServer.Write(_bufOfASCII, i, 1);
                Thread.Sleep(0);
                ProgressInfo.SetSend(100 * (i + 1) / _bufOfASCII.Length, i + 1);

                if (IsPrint) Log.Information($"{_deviceInfo.DeviceId} Progress:{ProgressInfo.SendRate},SendLine:{ProgressInfo.SendLine}");
            }
        else
            // 行模式写入
            for (var j = 0; j < _sendList.Count; j++)
            {
                var buf = BytesTools.ASCIIToASCIIByteArr(_sendList[j]);
                SerialServer.Write(buf);
                Thread.Sleep(0);
                ProgressInfo.SetSend(100 * (j + 1) / _sendList.Count, j + 1);

                if (IsPrint) Log.Information($"{_deviceInfo.DeviceId} Progress:{ProgressInfo.SendRate},SendLine:{ProgressInfo.SendLine}");
            }
    }


    // 文件保存方法（保持原始实现）
    protected void SaveComRecData(string strPathName, string message)
    {
        try
        {
            using (var fs = new FileStream(strPathName, FileMode.Append, FileAccess.Write, FileShare.None))
            {
                using (var sw = new StreamWriter(fs, Encoding.ASCII))
                {
                    fs.SetLength(0); // 注意：这里原始实现逻辑可能存在问题
                    sw.Write(message);
                }
            }

            Log.Information($"{_deviceInfo.DeviceId} Save NC File!{strPathName}");
        }
        catch (Exception ex)
        {
            Log.Error($"{_deviceInfo.DeviceId} {strPathName}->{ex.Message}");
        }
    }

    // NC文件列表获取（保持原始磁盘操作）
    protected List<NCInfoDto> GetPCList()
    {
        var PCList = new List<NCInfoDto>();
        var path = NCDeviceRoot_Send;

        if (!Directory.Exists(path))
        {
            Log.Warning($"{_deviceInfo.DeviceId} not found NC folder,{path}");
            return PCList;
        }

        foreach (var file in Directory.GetFiles(path, "*"))
        {
            var comFile = new NCInfoDto();
            FileInfo fileInfo = null;

            try
            {
                fileInfo = new FileInfo(file);
                comFile.Date = fileInfo.LastWriteTime.ToString();
                comFile.FileName = Path.GetFileName(fileInfo.Name);
            }
            catch (Exception ex)
            {
                Log.Error($"{_deviceInfo.DeviceId} {ex.Message}");
            }

            if (fileInfo?.Exists == true) comFile.FileSize = $"{Math.Ceiling(fileInfo.Length / 1024.0)} KB";
            PCList.Add(comFile);
        }

        return PCList;
    }

    // 延时检测方法（保持原始线程等待逻辑）
    protected bool IsCountDown(int delayTime)
    {
        if (delayTime <= 1000) delayTime = 8000;
        SpinWait.SpinUntil(() => false, delayTime);
        return true;
    }


    // 虚方法
    protected virtual void ProcessNC(string strRev)
    {
    }

    protected virtual void SendQ2NC(string fileNameCurrent, string strName)
    {
    }

    // 私有字段
    protected List<string> _sendList = new();
    protected byte[] _bufOfASCII;
    protected StringBuilder _strSendData = new();
    protected string _fileName = string.Empty;
    protected string _filePathName = string.Empty;
    protected bool _isRecState = false;
    protected string _strRevNC = string.Empty;
    protected bool _ncStart;
    protected bool _ncEnd;
    internal SerialPortServiceConfig _pluginConfig = new();
    protected SerialPortSIEMENSConfig _deviceInfo = new();
}