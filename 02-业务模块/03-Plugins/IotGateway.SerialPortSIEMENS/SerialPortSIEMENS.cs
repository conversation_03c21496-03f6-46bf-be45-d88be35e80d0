using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using Furion.Logging;
using SerialPortProtocol.Utils;

namespace IotGateway.SerialPortSIEMENS;

/// <summary>
/// </summary>
internal class SerialPortSIEMENS : SerialPortImplement
{
    protected override void ProcessNC(string strRev)
    {
        if (SerialServer == null)
        {
            Log.Warning(_deviceInfo.DeviceId + " SerialServer is Null!");
            return;
        }

        if (strRev.Length > 4)
        {
            var orderPrefix = _deviceInfo.OrderPrefix ?? "";
            var orderSuffix = _deviceInfo.OrderSuffix ?? "";
            if (string.IsNullOrEmpty(orderPrefix) || string.IsNullOrEmpty(orderSuffix))
            {
                Log.Warning(_deviceInfo.DeviceId + " order prefix or suffix is empty!");
                return;
            }

            var boolQW = strRev.Contains(orderPrefix);
            var boolVW = strRev.Contains(orderSuffix);
            var indexQW = strRev.IndexOf(orderPrefix);
            var indexVW = strRev.IndexOf(orderSuffix);
            if (indexVW - indexQW < 2 || indexVW - indexQW > 34)
            {
                boolQW = false;
                boolVW = false;
            }

            if (boolQW && boolVW)
            {
                var orderQV = strRev.Substring(indexQW, indexVW - indexQW + 2);
                Log.Information(_deviceInfo.DeviceId + " 收到QV指令: " + orderQV);
                var codeNum = orderQV.Substring(1, 1);
                if (codeNum == "2")
                {
                    var strName = orderQV.Substring(2, indexVW - indexQW - 2).Trim();
                    var fileNameCurrent = Path.Combine(NCDeviceRoot_Send, strName);
                    if (File.Exists(fileNameCurrent))
                    {
                        if (IsCountDown(_sendDelayTime)) SendQ2NC(fileNameCurrent, strName);
                    }
                    else
                    {
                        Log.Information(_deviceInfo.DeviceId + " NC文件不存在,请检查NC路径配置[" + fileNameCurrent + "]");
                    }
                }
                else if (codeNum == "3")
                {
                    _fileName = orderQV.Substring(2, indexVW - indexQW - 2).Trim();
                    var ListFile = GetPCList();
                    var strLine = "\n";
                    var sbList = new StringBuilder();
                    strRev = strRev.Substring(0, strRev.Length - 1);
                    sbList.Append("%_N_" + _fileName + "_MPF;\n");
                    var desc = _deviceInfo.DeviceDesc;
                    var pathStr = string.Empty;
                    if (!string.IsNullOrWhiteSpace(desc))
                    {
                        var regEx = "(\\$PATH=[a-zA-Z0-9/_]+);";
                        if (Regex.IsMatch(desc, regEx, RegexOptions.IgnoreCase))
                        {
                            var orderArr = Regex.Matches(desc, regEx, RegexOptions.IgnoreCase);
                            pathStr = orderArr[0].Groups[1].Value;
                        }
                    }

                    pathStr = string.IsNullOrWhiteSpace(pathStr) ? "$PATH=/_N_MPF_DIR" : pathStr;
                    sbList.Append(pathStr + "\n");
                    if (ListFile.Count > 0)
                    {
                        var addLine = 1;
                        var startLine = 0;
                        for (var i = 0; i < ListFile.Count; i++)
                        {
                            sbList.Append("N" + (startLine + i * addLine));
                            sbList.Append("(" + ListFile[i].FileName + ")");
                            sbList.Append(strLine);
                        }
                    }

                    sbList.Append("M30" + strLine);
                    sbList.Append("\u001a" + strLine);
                    if (IsCountDown(_sendDelayTime))
                    {
                        var bytes = Encoding.ASCII.GetBytes(sbList.ToString());
                        SerialServer.Write(bytes);
                    }
                }
                else if (codeNum == "4")
                {
                    _fileName = orderQV.Substring(2, indexVW - indexQW - 2).Trim();
                    _isRecState = true;
                }
            }
            else if (_isRecState && !string.IsNullOrEmpty(_fileName))
            {
                Log.Information($"{_deviceInfo.DeviceId} 非QV指令,是否接收：{_isRecState},文件名:{_fileName}");
                var strSaveDir = NCDeviceRoot_Rec;
                if (Directory.Exists(strSaveDir))
                {
                    var strSuffix2 = ".MPF";
                    var indexSiemStart = strRev.IndexOf("%") == -1 ? strRev.IndexOf(";") : strRev.IndexOf("%");
                    if (indexSiemStart >= 0)
                    {
                        strRev = strRev.Substring(indexSiemStart);
                        Thread.Sleep(50);
                        var indexSiemEnd = strRev.IndexOf("\u001a");
                        if (indexSiemEnd >= 0)
                        {
                            _filePathName = Path.Combine(strSaveDir, _fileName + strSuffix2);
                            SaveComRecData(_filePathName, strRev);
                            _isRecState = false;
                            _fileName = string.Empty;
                            _filePathName = string.Empty;
                        }
                    }
                }
                else
                {
                    Log.Warning(_deviceInfo.DeviceId + " 接收目录[\" + strSaveDir + \"]不存在!");
                }
            }
            else
            {
                Log.Warning("FileName:" + _fileName + ",内容:" + strRev);
                strRev = string.Empty;
            }
        }
    }

    protected override void SendQ2NC(string fileNameCurrent, string strName)
    {
        if (SerialServer == null)
        {
            Log.Warning(_deviceInfo.DeviceId + " SerialServer is Null!");
            return;
        }

        _sendList = new List<string>();
        using (var sr = new StreamReader(fileNameCurrent, Encoding.Default))
        {
            if (sr == null)
            {
                Log.Warning(_deviceInfo.DeviceId + " Read file failed,path: " + fileNameCurrent);
                return;
            }

            var strLine = "\n";
            string line;
            while ((line = sr.ReadLine()) != null)
            {
                var bytes = Encoding.ASCII.GetBytes(line + strLine);
                try
                {
                    SerialServer.Write(bytes);
                    _sendList.Add(line + "\n");
                }
                catch (Exception ex)
                {
                    Log.Error(_deviceInfo.DeviceId + " 发送数据异常: " + ex.Message);
                }
            }
        }

        _bufOfASCII = BytesTools.ASCIIToASCIIByteArr(_strSendData.ToString());
    }
}