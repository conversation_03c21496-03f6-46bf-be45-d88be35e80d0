using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Furion.FriendlyException;
using IotGateway.Plugin.Core;
using IotGateway.Plugin.Core.Models;
using IotGateway.SerialPortSIEMENS.Models;
using Microsoft.Extensions.Logging;

namespace IotGateway.SerialPortSIEMENS;

/// <summary>
///     西门子串口通信插件
/// </summary>
public class SerialPortSIEMENSPlugin : PluginBase
{
    /// <summary>
    ///     配置文件路径
    /// </summary>
    private readonly string _configPath;

    /// <summary>
    ///     配置
    /// </summary>
    private readonly SerialPortSIEMENSConfig _config;

    /// <summary>
    ///     设备配置
    /// </summary>
    private SerialPortServiceConfig _deviceConfig;

    /// <summary>
    ///     设备字典
    /// </summary>
    private ConcurrentDictionary<string, SerialPortSIEMENSConfig> _deviceDict = new();

    /// <summary>
    ///     西门子串口实现类
    /// </summary>
    private SerialPortSIEMENS _serialPortSIEMENS;

    /// <summary>
    ///     串口服务字典
    /// </summary>
    private readonly ConcurrentDictionary<string, SerialPortSIEMENS> _serialServerDict = new();

    /// <summary>
    ///     串口服务配置
    /// </summary>
    private SerialPortServiceConfig _pluginConfig = new();

    /// <summary>
    ///     是否接收状态
    /// </summary>
    private bool _isRecState = false;

    /// <summary>
    ///     ASCII数据缓冲区
    /// </summary>
    private byte[] _bufOfASCII;

    /// <summary>
    ///     写入完成标志
    /// </summary>
    private bool _isWriteOver = true;

    /// <summary>
    ///     日志记录器
    /// </summary>
    private ILogger<SerialPortSIEMENSPlugin>? _logger;

    /// <summary>
    ///     配置属性
    /// </summary>
    public object? Configuration => _config;

    /// <summary>
    ///     接收数据长度
    /// </summary>
    private int _receiveLength;

    /// <summary>
    ///     构造函数
    /// </summary>
    public SerialPortSIEMENSPlugin()
    {
        try
        {
            _configPath = Path.Combine(AppContext.BaseDirectory, "Configs", "serialportsiemens.json");
            _config = LoadConfiguration() ?? new SerialPortSIEMENSConfig();
            _configuration = _config;

            InitLogger();
        }
        catch (Exception ex)
        {
            // 确保构造函数不会抛出异常
            Console.WriteLine($"西门子串口插件初始化失败: {ex.Message}");
        }
    }
    /// <summary>
    ///     初始化日志记录器
    /// </summary>
    private void InitLogger()
    {
        try
        {
            var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole(options =>
                {
                    options.IncludeScopes = true;
                    options.TimestampFormat = "[yyyy-MM-dd HH:mm:ss.fff] ";
                    options.FormatterName = "CustomFormatter";
                })
                .AddSimpleConsole(options =>
                {
                    options.IncludeScopes = true;
                    options.SingleLine = false;
                    options.TimestampFormat = "[yyyy-MM-dd HH:mm:ss.fff] ";
                    options.UseUtcTimestamp = false;
                    options.ColorBehavior = Microsoft.Extensions.Logging.Console.LoggerColorBehavior.Enabled;
                })
                .SetMinimumLevel(LogLevel.Trace);
            });
            _logger = loggerFactory.CreateLogger<SerialPortSIEMENSPlugin>();

            _logger?.LogInformation("日志记录器初始化成功，级别：Trace，支持彩色输出");
            _logger?.LogInformation("调试日志已启用");
            _logger?.LogTrace("跟踪日志已启用");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"初始化日志记录器失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     初始化设备
    /// </summary>
    private void InitNcDevice()
    {
        try
        {
            _logger?.LogInformation("开始初始化设备");
            _logger?.LogInformation("DNC配置数量: {Count}", _dncConfigs?.Count ?? 0);

            foreach (var dncConfig in _dncConfigs.Where(c => c.Enabled && c.ProtocolType.Equals(Name)))
            {
                // 过滤
                _logger?.LogInformation("处理DNC配置: ID={Id}, DeviceCode={DeviceCode}, DeviceName={DeviceName}",
                    dncConfig.Id, dncConfig.DeviceCode, dncConfig.DeviceName);

                // 使用串口名称作为设备ID
                var deviceId = dncConfig.DeviceCode;
                _logger?.LogInformation("原始设备ID（串口名称）: {DeviceId}", deviceId);

                try
                {
                    // 获取设备工作目录（此方法会处理路径中的特殊字符）
                    var workFolder = GetNcDeviceFolder(deviceId);
                    _logger?.LogInformation("设备ID: {DeviceId}, 工作目录: {WorkFolder}", deviceId, workFolder);

                    // 创建设备配置
                    var deviceInfo = new SerialPortSIEMENSConfig
                    {
                        DeviceId = deviceId,
                        PortName = _config.PortName,
                        BaudRate = _config.BaudRate,
                        DataBits = _config.DataBits,
                        StopBits = _config.StopBits,
                        Parity = _config.Parity,
                        RtsEnable = _config.RtsEnable,
                        DtrEnable = _config.DtrEnable,
                        WorkFolder = workFolder,
                        WorkFolderSend = Path.Combine(workFolder, "SEND"),
                        WorkFolderRec = Path.Combine(workFolder, "REC")
                    };

                    // 添加到设备字典
                    _deviceDict[deviceId] = deviceInfo;

                    _logger?.LogInformation("初始化设备成功: DeviceId={DeviceId}, 串口={SerialPort}, 波特率={BaudRate}, 数据位={DataBits}, 停止位={StopBits}, 校验位={Parity}",
                        deviceId, _config.PortName, _config.BaudRate, _config.DataBits, _config.StopBits, _config.Parity);

                    _logger?.LogInformation("设备工作目录: 发送={SendFolder}, 接收={RecFolder}",
                            deviceInfo.WorkFolderSend, deviceInfo.WorkFolderRec);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "处理设备 {DeviceId} 时出错: {ErrorMessage}", deviceId, ex.Message);
                    if (ex.InnerException != null)
                    {
                        _logger?.LogError("内部异常: {InnerError}", ex.InnerException.Message);
                    }
                }
            }
            _logger?.LogInformation("设备初始化完成，共 {Count} 个设备", _deviceDict.Count);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "初始化设备失败: {ErrorMessage}", ex.Message);
            if (ex.InnerException != null)
            {
                _logger?.LogError("内部异常: {InnerError}", ex.InnerException.Message);
            }

            // 记录更多上下文信息
            _logger?.LogError("异常时状态: DNC配置数量={Count}, 配置={Config}",
                _dncConfigs?.Count ?? 0,
                JsonSerializer.Serialize(_config, new JsonSerializerOptions { WriteIndented = true }));
        }
    }

    /// <summary>
    ///     获取NC设备文件夹
    /// </summary>
    private string GetNcDeviceFolder(string ncDeviceId)
    {
        // 添加日志，记录原始设备ID
        _logger?.LogInformation("获取设备工作目录, 原始设备ID: {DeviceId}", ncDeviceId);

        // 清理设备ID，处理特殊情况，使其适合作为目录名
        string safeDeviceId = ncDeviceId;

        // 检查是否是设备文件路径（以/dev/开头）
        bool isDevicePath = safeDeviceId.StartsWith("/dev/");
        if (isDevicePath)
        {
            _logger?.LogInformation("检测到设备ID是Linux设备文件路径: {DeviceId}", ncDeviceId);
            // 对于Linux设备文件路径，添加特殊前缀以避免与实际设备文件冲突
            safeDeviceId = "device_" + safeDeviceId.Replace("/", "_").Replace("\\", "_");
            _logger?.LogInformation("特殊处理后的设备ID: {SafeDeviceId}", safeDeviceId);
        }
        // 检查是否包含路径分隔符（Linux或Windows格式）
        else if (safeDeviceId.Contains("/") || safeDeviceId.Contains("\\"))
        {
            _logger?.LogInformation("设备ID包含路径分隔符，需要清理: {DeviceId}", ncDeviceId);
            // 替换所有路径分隔符为下划线
            safeDeviceId = safeDeviceId.Replace("/", "_").Replace("\\", "_");
            _logger?.LogInformation("清理后的设备ID: {SafeDeviceId}", safeDeviceId);
        }

        var deviceNcPath = Path.Combine(AppContext.BaseDirectory, "RootFiles", safeDeviceId);
        _logger?.LogInformation("计算得到的设备工作目录路径: {DeviceNcPath}", deviceNcPath);

        if (!Directory.Exists(deviceNcPath))
        {
            try
            {
                _logger?.LogInformation("设备工作目录不存在，开始创建: {DeviceNcPath}", deviceNcPath);

                // 检查父目录是否存在
                var parentDir = Path.GetDirectoryName(deviceNcPath);
                if (!Directory.Exists(parentDir))
                {
                    _logger?.LogInformation("父目录不存在，创建父目录: {ParentDir}", parentDir);
                    Directory.CreateDirectory(parentDir);
                }

                // 创建设备工作目录
                _logger?.LogInformation("创建设备工作目录: {DeviceNcPath}", deviceNcPath);
                Directory.CreateDirectory(deviceNcPath);

                // 创建子目录
                var sendPath = Path.Combine(deviceNcPath, "SEND");
                _logger?.LogInformation("创建设备发送目录: {SendPath}", sendPath);
                Directory.CreateDirectory(sendPath);

                var recPath = Path.Combine(deviceNcPath, "REC");
                _logger?.LogInformation("创建设备接收目录: {RecPath}", recPath);
                Directory.CreateDirectory(recPath);

                _logger?.LogInformation("设备目录创建成功: {DeviceNcPath}", deviceNcPath);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "创建设备目录失败: {ErrorMessage}", ex.Message);

                // 添加更详细的异常信息
                _logger?.LogError("设备ID: {DeviceId}, 安全设备ID: {SafeDeviceId}, 尝试创建的路径: {DeviceNcPath}",
                    ncDeviceId, safeDeviceId, deviceNcPath);

                // 输出堆栈跟踪以帮助调试
                _logger?.LogError("异常堆栈: {StackTrace}", ex.StackTrace);

                if (ex.InnerException != null)
                {
                    _logger?.LogError("内部异常: {InnerError}, 堆栈: {InnerStackTrace}",
                        ex.InnerException.Message, ex.InnerException.StackTrace);
                }

                throw;
            }
        }
        else
        {
            _logger?.LogInformation("设备工作目录已存在: {DeviceNcPath}", deviceNcPath);
        }

        return deviceNcPath;
    }

    /// <summary>
    ///     注册设备
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="dncConfig">DNC配置</param>
    private void RegisterDevice(string deviceId, DncConfig dncConfig)
    {
        try
        {
            _logger?.LogInformation("开始注册设备: {DeviceId}", deviceId);

            // 获取设备工作目录
            var workFolder = GetNcDeviceFolder(deviceId);
            _logger?.LogInformation("设备 {DeviceId} 工作目录: {WorkFolder}", deviceId, workFolder);

            // 创建设备配置
            var deviceInfo = new SerialPortSIEMENSConfig
            {
                DeviceId = deviceId,
                PortName = _config.PortName,
                BaudRate = _config.BaudRate,
                DataBits = _config.DataBits,
                StopBits = _config.StopBits,
                Parity = _config.Parity,
                RtsEnable = _config.RtsEnable,
                DtrEnable = _config.DtrEnable,
                WorkFolder = workFolder,
                WorkFolderSend = Path.Combine(workFolder, "SEND"),
                WorkFolderRec = Path.Combine(workFolder, "REC")
            };

            // 添加到设备字典
            _deviceDict[deviceId] = deviceInfo;

            _logger?.LogInformation("设备注册成功: DeviceId={DeviceId}, 串口={SerialPort}, 波特率={BaudRate}, 数据位={DataBits}, 停止位={StopBits}, 校验位={Parity}",
                deviceId, _config.PortName, _config.BaudRate, _config.DataBits, _config.StopBits, _config.Parity);

            _logger?.LogInformation("设备工作目录: 发送={SendFolder}, 接收={RecFolder}",
                deviceInfo.WorkFolderSend, deviceInfo.WorkFolderRec);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "注册设备 {DeviceId} 失败: {ErrorMessage}", deviceId, ex.Message);
            if (ex.InnerException != null)
            {
                _logger?.LogError("内部异常: {InnerError}", ex.InnerException.Message);
            }
            throw;
        }
    }

    /// <summary>
    ///     设置DNC配置（支持外部动态注入DNC配置，保证_dncConfigs赋值）
    /// </summary>
    /// <param name="dncConfig">DNC配置</param>
    /// <returns>设置结果</returns>
    public override async Task SetDncConfigAsync(DncConfig dncConfig)
    {
        // 调用基类方法，维护_dncConfigs集合
        await base.SetDncConfigAsync(dncConfig);

        try
        {
            _logger?.LogInformation("接收到DNC配置: {DeviceCode}, 启用: {Enabled}", dncConfig.DeviceCode, dncConfig.Enabled);

            // 记录变更前的设备数量
            int deviceCountBefore = _deviceDict.Count;

            if (dncConfig.Enabled)
            {
                // 使用统一的注册设备方法
                RegisterDevice(dncConfig.DeviceCode, dncConfig);
                _logger?.LogInformation("设备 {DeviceCode} 已注册", dncConfig.DeviceCode);
            }
            else
            {
                // 禁用则移除
                SerialPortSIEMENSConfig removed;
                if (_deviceDict.TryRemove(dncConfig.DeviceCode, out removed))
                {
                    _logger?.LogInformation("已移除禁用设备 {DeviceCode} 配置", dncConfig.DeviceCode);
                }
                else
                {
                    _logger?.LogWarning("尝试移除不存在的设备 {DeviceCode}", dncConfig.DeviceCode);
                }
            }

            // 如果是已启用状态，并且设备列表发生变化，则重启服务
            if (_config.Enabled && deviceCountBefore != _deviceDict.Count)
            {
                _logger?.LogInformation("DNC配置变更，需要重启串口服务以应用新配置...");
                await StopAsync();
                await StartAsync();
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "SetDncConfigAsync异常: {ErrorMessage}", ex.Message);
            if (ex.InnerException != null)
            {
                _logger?.LogError("内部异常: {InnerError}", ex.InnerException.Message);
            }
        }
    }

    /// <summary>
    ///     加载配置
    /// </summary>
    private SerialPortSIEMENSConfig? LoadConfiguration()
    {
        try
        {
            if (File.Exists(_configPath))
            {
                var json = File.ReadAllText(_configPath);
                var config = JsonSerializer.Deserialize<SerialPortSIEMENSConfig>(json);
                _logger?.LogInformation("西门子串口配置加载成功: PortName={PortName}, BaudRate={BaudRate}",
                    config?.PortName, config?.BaudRate);
                return config;
            }

            _logger?.LogWarning("西门子串口配置文件不存在，将使用默认配置");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "加载西门子串口配置失败");
        }

        return null;
    }

    /// <summary>
    ///     保存配置
    /// </summary>
    private void SaveConfiguration()
    {
        try
        {
            _logger?.LogInformation("正在保存西门子串口配置...");
            var directory = Path.GetDirectoryName(_configPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
                _logger?.LogInformation("创建配置目录: {Directory}", directory);
            }

            var json = JsonSerializer.Serialize(_config, new JsonSerializerOptions
            {
                WriteIndented = true
            });
            File.WriteAllText(_configPath, json);
            _logger?.LogInformation("西门子串口配置保存成功");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "保存西门子串口配置失败");
        }
    }

    /// <summary>
    ///     插件名称
    /// </summary>
    public override string Name => "Serial Port SIEMENS";

    /// <summary>
    ///     插件版本
    /// </summary>
    public override string Version => "1.0.0";

    /// <summary>
    ///     插件描述
    /// </summary>
    public override string Description => "西门子串口通信插件";

    /// <summary>
    ///     插件分类
    /// </summary>
    public override string Category => "Dnc";

    /// <summary>
    ///     初始化
    /// </summary>
    public override async Task InitializeAsync()
    {
        try
        {
            _logger?.LogInformation("西门子串口插件初始化开始");
            _logger?.LogInformation("配置详情: {Config}", JsonSerializer.Serialize(_config, new JsonSerializerOptions { WriteIndented = true }));

            // 初始化串口服务配置 (只包含必要的控制参数)
            _pluginConfig = new SerialPortServiceConfig
            {
                SendDelayTime = _config.SendDelayTime,
                RtsEnable = _config.RtsEnable,
                DtrEnable = _config.DtrEnable
            };

            _logger?.LogInformation("初始化插件配置: {Config}", JsonSerializer.Serialize(_pluginConfig, new JsonSerializerOptions { WriteIndented = true }));

            // 如果自启动
            if (_config.Enabled)
            {
                _logger?.LogInformation("西门子串口插件配置为自启动，准备启动服务");
                await StartAsync();
            }
            else
            {
                _logger?.LogInformation("西门子串口插件配置为手动启动，跳过自动启动步骤");
            }

            _logger?.LogInformation("西门子串口插件初始化完成");
            await base.InitializeAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "西门子串口插件初始化失败: {ErrorMessage}", ex.Message);
            if (ex.InnerException != null)
            {
                _logger?.LogError("内部异常: {InnerError}", ex.InnerException.Message);
            }
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     启动
    /// </summary>
    public override async Task StartAsync()
    {
        try
        {
            _logger?.LogInformation("正在启动西门子串口插件...");

            // 初始化设备目录 - 移动到这里，只在启动时执行
            InitNcDevice();

            // 启动串口服务
            StartSerialServer();

            _logger?.LogInformation("西门子串口插件已启动");

            await base.StartAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "西门子串口插件启动失败: {ErrorMessage}", ex.Message);
            if (ex.InnerException != null)
            {
                _logger?.LogError("内部异常: {InnerError}", ex.InnerException.Message);
            }
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     停止
    /// </summary>
    public override async Task StopAsync()
    {
        _logger?.LogInformation("正在停止西门子串口插件...");

        try
        {
            // 停止串口服务
            StopSerialServer();

            _logger?.LogInformation("西门子串口插件已停止");

            await base.StopAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "停止西门子串口插件时发生错误: {ErrorMessage}", ex.Message);
            if (ex.InnerException != null)
            {
                _logger?.LogError("内部异常: {InnerError}", ex.InnerException.Message);
            }
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     更新配置
    /// </summary>
    public override async Task UpdateConfigurationAsync(object configuration)
    {
        try
        {
            _logger?.LogInformation("开始更新西门子串口插件配置...");
            // 关闭当前串口连接
            try
            {
                _logger?.LogInformation("停止当前串口服务，准备应用新配置");
                await StopAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "停止串口服务异常: {ErrorMessage}", ex.Message);
            }

            if (configuration is JsonElement jsonElement)
            {
                _logger?.LogInformation("收到JSON格式配置，准备解析");
                // 将JsonElement反序列化为SerialPortSIEMENSConfig对象
                var config = jsonElement.Deserialize<SerialPortSIEMENSConfig>(new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (config != null)
                {
                    _logger?.LogInformation("新配置解析成功: PortName={PortName}, BaudRate={BaudRate}, Enabled={Enabled}",
                        config.PortName, config.BaudRate, config.Enabled);

                    // 记录变更前的配置
                    _logger?.LogTrace("变更前配置: PortName={OldPortName}, BaudRate={OldBaudRate}, DataBits={OldDataBits}, " +
                        "StopBits={OldStopBits}, Parity={OldParity}, Enabled={OldEnabled}, SendDelayTime={OldSendDelayTime}, " +
                        "RtsEnable={OldRtsEnable}, DtrEnable={OldDtrEnable}",
                        _config.PortName, _config.BaudRate, _config.DataBits, _config.StopBits, _config.Parity,
                        _config.Enabled, _config.SendDelayTime, _config.RtsEnable, _config.DtrEnable);

                    // 更新配置
                    _config.PortName = config.PortName;
                    _config.BaudRate = config.BaudRate;
                    _config.DataBits = config.DataBits;
                    _config.StopBits = config.StopBits;
                    _config.Parity = config.Parity;
                    _config.Enabled = config.Enabled;
                    _config.SendDelayTime = config.SendDelayTime;
                    _config.RtsEnable = config.RtsEnable;
                    _config.DtrEnable = config.DtrEnable;

                    // 记录变更后的配置
                    _logger?.LogInformation("变更后配置: PortName={NewPortName}, BaudRate={NewBaudRate}, DataBits={NewDataBits}, " +
                        "StopBits={NewStopBits}, Parity={NewParity}, Enabled={NewEnabled}, SendDelayTime={NewSendDelayTime}, " +
                        "RtsEnable={NewRtsEnable}, DtrEnable={NewDtrEnable}",
                        _config.PortName, _config.BaudRate, _config.DataBits, _config.StopBits, _config.Parity,
                        _config.Enabled, _config.SendDelayTime, _config.RtsEnable, _config.DtrEnable);

                    // 保存更新后的配置
                    _logger?.LogInformation("保存更新后的配置");
                    SaveConfiguration();
                    // 更新设备字典
                    _logger?.LogInformation("更新设备字典");
                    InitNcDevice();

                    // 如果启用，则重新启动服务
                    if (_config.Enabled)
                    {
                        _logger?.LogInformation("配置已启用，正在重启串口服务");
                        await StartAsync();
                    }
                    else
                    {
                        _logger?.LogInformation("配置未启用，串口服务将保持停止状态");
                    }
                }
                else
                {
                    _logger?.LogWarning("配置解析失败，可能格式不正确");
                }
            }
            else if (configuration is SerialPortSIEMENSConfig config)
            {
                _logger?.LogInformation("收到对象格式配置: PortName={PortName}, BaudRate={BaudRate}, Enabled={Enabled}",
                    config.PortName, config.BaudRate, config.Enabled);

                // 记录变更前的配置
                _logger?.LogTrace("变更前配置: PortName={OldPortName}, BaudRate={OldBaudRate}, DataBits={OldDataBits}, " +
                    "StopBits={OldStopBits}, Parity={OldParity}, Enabled={OldEnabled}, SendDelayTime={OldSendDelayTime}, " +
                    "RtsEnable={OldRtsEnable}, DtrEnable={OldDtrEnable}",
                    _config.PortName, _config.BaudRate, _config.DataBits, _config.StopBits, _config.Parity,
                    _config.Enabled, _config.SendDelayTime, _config.RtsEnable, _config.DtrEnable);

                // 直接使用SerialPortSIEMENSConfig对象
                _config.PortName = config.PortName;
                _config.BaudRate = config.BaudRate;
                _config.DataBits = config.DataBits;
                _config.StopBits = config.StopBits;
                _config.Parity = config.Parity;
                _config.Enabled = config.Enabled;
                _config.SendDelayTime = config.SendDelayTime;
                _config.RtsEnable = config.RtsEnable;
                _config.DtrEnable = config.DtrEnable;

                // 记录变更后的配置
                _logger?.LogInformation("变更后配置: PortName={NewPortName}, BaudRate={NewBaudRate}, DataBits={NewDataBits}, " +
                    "StopBits={NewStopBits}, Parity={NewParity}, Enabled={NewEnabled}, SendDelayTime={NewSendDelayTime}, " +
                    "RtsEnable={NewRtsEnable}, DtrEnable={NewDtrEnable}",
                    _config.PortName, _config.BaudRate, _config.DataBits, _config.StopBits, _config.Parity,
                    _config.Enabled, _config.SendDelayTime, _config.RtsEnable, _config.DtrEnable);

                _logger?.LogInformation("保存配置");
                SaveConfiguration(); // 保存配置

                // 更新设备字典
                _logger?.LogInformation("更新设备字典");
                InitNcDevice();

                if (_config.Enabled)
                {
                    _logger?.LogInformation("配置已启用，正在重启串口服务");
                    await StartAsync();
                }
                else
                {
                    _logger?.LogInformation("配置未启用，串口服务将保持停止状态");
                }
            }
            else
            {
                _logger?.LogWarning("收到未知类型的配置: {ConfigType}", configuration?.GetType().FullName ?? "null");
            }

            _logger?.LogInformation("西门子串口插件配置更新完成");
            await base.UpdateConfigurationAsync(configuration);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "更新西门子串口配置失败: {ErrorMessage}", ex.Message);
            if (ex.InnerException != null)
            {
                _logger?.LogError("内部异常: {InnerError}", ex.InnerException.Message);
            }
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     启动串口服务
    /// </summary>
    private void StartSerialServer()
    {
        try
        {
            _logger?.LogInformation("开始启动串口服务...");
            _logger?.LogInformation("串口设备配置：");
            foreach (var device in _deviceDict)
            {
                _logger?.LogInformation("设备ID: {DeviceId}, 端口: {PortName}, 波特率: {BaudRate}, 数据位: {DataBits}, 停止位: {StopBits}, 校验位: {Parity}",
                    device.Key, device.Value.PortName, device.Value.BaudRate, device.Value.DataBits, device.Value.StopBits, device.Value.Parity);
            }

            using var enumerator = _deviceDict.GetEnumerator();
            while (enumerator.MoveNext())
            {
                var item = enumerator.Current;
                if (_serialServerDict.ContainsKey(item.Key))
                {
                    _logger?.LogInformation("设备 {DeviceId} 已存在于服务字典中，跳过", item.Key);
                    continue;
                }

                var deviceId = item.Key;
                _logger?.LogInformation("正在启动设备 {DeviceId} 的串口服务...", deviceId);

                Task.Factory.StartNew(() =>
                {
                    try
                    {
                        _logger?.LogInformation("正在为设备 {DeviceId} 创建串口服务实例...", deviceId);
                        _serialServerDict[deviceId] = new SerialPortSIEMENS();

                        if (_serialServerDict[deviceId] != null)
                        {
                            var serialPortImpl = _serialServerDict[deviceId];
                            _serialServerDict.AddOrUpdate(deviceId, serialPortImpl, (key, value) => serialPortImpl);
                            _logger?.LogInformation("设备 {DeviceId} 的串口服务实例创建成功", deviceId);
                        }

                        _logger?.LogTrace("设备 {DeviceId} 初始化服务并打开串口...", deviceId);
                        _serialServerDict[deviceId].InitServer(_pluginConfig, item.Value).OpenSerialPort();

                        _logger?.LogInformation("设备 {DeviceId} 打开串口服务成功！配置：{Config}",
                            deviceId,
                            JsonSerializer.Serialize(item.Value, new JsonSerializerOptions { WriteIndented = true }));
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "启动设备 {DeviceId} 的串口服务异常: {ErrorMessage}", deviceId, ex.Message);
                        if (ex.InnerException != null)
                        {
                            _logger?.LogError("内部异常: {InnerError}", ex.InnerException.Message);
                        }
                    }
                });
            }

            _logger?.LogInformation("串口服务启动请求已发送，共 {Count} 个设备", _deviceDict.Count);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "启动串口服务出错: {ErrorMessage}", ex.Message);
            if (ex.InnerException != null)
            {
                _logger?.LogError("内部异常: {InnerError}", ex.InnerException.Message);
            }
        }
    }

    /// <summary>
    ///     停止串口服务
    /// </summary>
    private void StopSerialServer()
    {
        try
        {
            _logger?.LogInformation("开始停止串口服务，当前活动服务数量: {Count}", _serialServerDict.Count);

            foreach (var item in _serialServerDict)
            {
                if (item.Value != null)
                {
                    try
                    {
                        _logger?.LogInformation("正在关闭设备 {DeviceId} 的串口服务...", item.Key);
                        // 关闭串口连接
                        item.Value.CloseSerialPort();
                        _logger?.LogInformation("设备 {DeviceId} 的串口服务已关闭", item.Key);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "关闭设备 {DeviceId} 的串口服务异常: {ErrorMessage}", item.Key, ex.Message);
                        if (ex.InnerException != null)
                        {
                            _logger?.LogError("内部异常: {InnerError}", ex.InnerException.Message);
                        }
                    }
                }
                else
                {
                    _logger?.LogWarning("设备 {DeviceId} 的串口服务实例为空", item.Key);
                }
            }

            _logger?.LogInformation("所有串口服务已停止");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "停止串口服务出错: {ErrorMessage}", ex.Message);
            if (ex.InnerException != null)
            {
                _logger?.LogError("内部异常: {InnerError}", ex.InnerException.Message);
            }
        }
    }
}