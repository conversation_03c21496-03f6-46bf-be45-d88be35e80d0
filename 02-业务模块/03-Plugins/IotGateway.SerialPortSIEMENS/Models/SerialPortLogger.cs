using System;
using System.Linq;
using System.Text;
using Microsoft.Extensions.Logging;

namespace IotGateway.SerialPortSIEMENS.Models
{
  /// <summary>
  /// 串口通信日志辅助类
  /// </summary>
  public static class SerialPortLogger
  {
    /// <summary>
    /// 记录发送的十六进制数据
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="data">数据</param>
    /// <param name="offset">偏移量</param>
    /// <param name="count">数据长度</param>
    public static void LogSendHex<T>(ILogger<T> logger, string deviceId, byte[] data, int offset, int count)
    {
      if (logger == null || data == null || offset < 0 || count <= 0 || offset + count > data.Length)
        return;

      var hexString = ByteArrayToHexString(data, offset, count);
      var asciiString = ByteArrayToAsciiString(data, offset, count);

      logger.LogDebug("设备 {DeviceId} 发送数据[HEX]: {HexData}", deviceId, hexString);
      logger.LogTrace("设备 {DeviceId} 发送数据[ASCII]: {AsciiData}", deviceId, asciiString);
      logger.LogTrace("设备 {DeviceId} 发送数据长度: {Length} 字节", deviceId, count);
    }

    /// <summary>
    /// 记录接收的十六进制数据
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="data">数据</param>
    /// <param name="offset">偏移量</param>
    /// <param name="count">数据长度</param>
    public static void LogReceiveHex<T>(ILogger<T> logger, string deviceId, byte[] data, int offset, int count)
    {
      if (logger == null || data == null || offset < 0 || count <= 0 || offset + count > data.Length)
        return;

      var hexString = ByteArrayToHexString(data, offset, count);
      var asciiString = ByteArrayToAsciiString(data, offset, count);

      logger.LogDebug("设备 {DeviceId} 接收数据[HEX]: {HexData}", deviceId, hexString);
      logger.LogTrace("设备 {DeviceId} 接收数据[ASCII]: {AsciiData}", deviceId, asciiString);
      logger.LogTrace("设备 {DeviceId} 接收数据长度: {Length} 字节", deviceId, count);
    }

    /// <summary>
    /// 记录串口状态变化
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="portName">端口名称</param>
    /// <param name="status">状态</param>
    public static void LogPortStatus<T>(ILogger<T> logger, string deviceId, string portName, string status)
    {
      if (logger == null)
        return;

      logger.LogInformation("设备 {DeviceId} 串口 {PortName} 状态: {Status}", deviceId, portName, status);
    }

    /// <summary>
    /// 记录详细的异常信息
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="ex">异常</param>
    /// <param name="operation">操作描述</param>
    public static void LogDetailedException<T>(ILogger<T> logger, string deviceId, Exception ex, string operation)
    {
      if (logger == null || ex == null)
        return;

      logger.LogError(ex, "设备 {DeviceId} 在 {Operation} 操作中发生异常: {ErrorMessage}",
          deviceId, operation, ex.Message);

      if (ex.InnerException != null)
      {
        logger.LogError("内部异常: {InnerErrorMessage}", ex.InnerException.Message);
      }

      logger.LogDebug("异常堆栈: {StackTrace}", ex.StackTrace);
    }

    /// <summary>
    /// 字节数组转十六进制字符串
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="offset">偏移量</param>
    /// <param name="count">数据长度</param>
    /// <returns>十六进制字符串</returns>
    private static string ByteArrayToHexString(byte[] data, int offset, int count)
    {
      if (data == null || offset < 0 || count <= 0 || offset + count > data.Length)
        return string.Empty;

      var sb = new StringBuilder();
      for (int i = offset; i < offset + count; i++)
      {
        sb.Append(data[i].ToString("X2"));
        if (i < offset + count - 1)
          sb.Append(' ');
      }
      return sb.ToString();
    }

    /// <summary>
    /// 字节数组转ASCII字符串
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="offset">偏移量</param>
    /// <param name="count">数据长度</param>
    /// <returns>ASCII字符串，不可打印字符以'.'代替</returns>
    private static string ByteArrayToAsciiString(byte[] data, int offset, int count)
    {
      if (data == null || offset < 0 || count <= 0 || offset + count > data.Length)
        return string.Empty;

      var sb = new StringBuilder();
      for (int i = offset; i < offset + count; i++)
      {
        char c = (char)data[i];
        // 判断是否为可打印ASCII字符
        if (data[i] >= 32 && data[i] <= 126)
          sb.Append(c);
        else
          sb.Append('.');
      }
      return sb.ToString();
    }

    /// <summary>
    /// 生成带有数据分析的详细日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="data">数据</param>
    /// <param name="offset">偏移量</param>
    /// <param name="count">数据长度</param>
    /// <param name="isReceive">是否为接收数据</param>
    public static void LogDataWithAnalysis<T>(ILogger<T> logger, string deviceId, byte[] data, int offset, int count, bool isReceive)
    {
      if (logger == null || data == null || offset < 0 || count <= 0 || offset + count > data.Length)
        return;

      var direction = isReceive ? "接收" : "发送";
      var hexString = ByteArrayToHexString(data, offset, count);
      var asciiString = ByteArrayToAsciiString(data, offset, count);

      logger.LogDebug("设备 {DeviceId} {Direction}数据详细分析:", deviceId, direction);
      logger.LogDebug("  - 十六进制: {HexData}", hexString);
      logger.LogDebug("  - ASCII文本: {AsciiData}", asciiString);
      logger.LogDebug("  - 数据长度: {Length} 字节", count);

      // 分析特殊字符
      var specialChars = data.Skip(offset).Take(count)
          .Where(b => b < 32 || b > 126)
          .Select(b => new { Value = b, Desc = GetControlCharDescription(b) })
          .ToList();

      if (specialChars.Any())
      {
        logger.LogDebug("  - 特殊字符分析:");
        foreach (var ch in specialChars)
        {
          logger.LogDebug("    * 0x{HexValue}: {Description}",
              ch.Value.ToString("X2"), ch.Desc);
        }
      }
    }

    /// <summary>
    /// 获取控制字符的描述
    /// </summary>
    /// <param name="value">字节值</param>
    /// <returns>控制字符描述</returns>
    private static string GetControlCharDescription(byte value)
    {
      return value switch
      {
        0x00 => "NUL (空字符)",
        0x01 => "SOH (标题开始)",
        0x02 => "STX (正文开始)",
        0x03 => "ETX (正文结束)",
        0x04 => "EOT (传输结束)",
        0x05 => "ENQ (询问)",
        0x06 => "ACK (确认)",
        0x07 => "BEL (响铃)",
        0x08 => "BS (退格)",
        0x09 => "HT (水平制表符)",
        0x0A => "LF (换行)",
        0x0B => "VT (垂直制表符)",
        0x0C => "FF (换页)",
        0x0D => "CR (回车)",
        0x0E => "SO (移出)",
        0x0F => "SI (移入)",
        0x10 => "DLE (数据链路转义)",
        0x11 => "DC1/XON (设备控制1/传输开始)",
        0x13 => "DC3/XOFF (设备控制3/传输暂停)",
        0x1A => "SUB (替换)",
        0x1B => "ESC (转义)",
        0x1C => "FS (文件分隔符)",
        0x1D => "GS (组分隔符)",
        0x1E => "RS (记录分隔符)",
        0x1F => "US (单元分隔符)",
        0x7F => "DEL (删除)",
        _ => $"未知控制字符 (0x{value:X2})"
      };
    }
  }
}