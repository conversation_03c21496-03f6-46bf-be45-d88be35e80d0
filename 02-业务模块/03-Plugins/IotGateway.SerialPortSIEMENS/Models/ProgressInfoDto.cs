using SerialPortProtocol.Enum;

namespace HyIotGatewayDnc.Plugin.SerialPortSIEMENS;

/// <summary>
///     进度信息数据传输对象（DTO）。
///     用于描述NC文件发送过程中的实时进度、速率、缓存等信息，便于界面展示和业务监控。
///     典型场景：大文件分段发送、行级进度统计、缓存队列监控等。
/// </summary>
public class ProgressInfoDto
{
    /// <summary>
    ///     写入类型（如整文件、按行）。决定进度统计和发送方式。
    /// </summary>
    public EmWriteType WriteType { get; set; }

    /// <summary>
    ///     当前发送速率。
    ///     单位由业务约定，通常为字节/秒或行/秒。
    /// </summary>
    public decimal SendRate { get; set; }

    /// <summary>
    ///     总行数。仅在按行发送时有效。
    /// </summary>
    public int LineCount { get; set; }

    /// <summary>
    ///     已发送行数。用于实时进度展示。
    /// </summary>
    public int SendLine { get; set; }

    /// <summary>
    ///     当前缓存队列中待发送的数据数量。
    ///     用于监控发送压力和流控。
    /// </summary>
    public int CacheSend { get; set; }

    /// <summary>
    ///     当前缓存队列剩余容量。
    ///     用于判断是否需要暂停写入或预警。
    /// </summary>
    public int CacheSurplus { get; set; }

    /// <summary>
    ///     当前正在发送或统计的文件路径。
    ///     便于界面展示和日志定位。
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    ///     设置写入类型（如整文件、按行）。
    /// </summary>
    /// <param name="writeType">写入类型（枚举EmWriteType）</param>
    /// <returns>返回自身，支持链式调用</returns>
    public ProgressInfoDto SetInit(EmWriteType writeType)
    {
        WriteType = writeType;
        return this;
    }

    /// <summary>
    ///     设置发送速率与当前已发送行数。
    /// </summary>
    /// <param name="rate">发送速率（单位：自定义/秒）</param>
    /// <param name="lineNum">已发送行数，仅在按行发送时有效</param>
    /// <returns>返回自身，支持链式调用</returns>
    public ProgressInfoDto SetSend(decimal rate, int lineNum)
    {
        SendRate = rate;
        var flag = WriteType == EmWriteType.LINE;
        if (flag) SendLine = lineNum;
        return this;
    }
}