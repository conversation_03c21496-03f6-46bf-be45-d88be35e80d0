using IotGateway.Plugin.Core;

namespace IotGateway.SerialPortSIEMENS.Models;

public class SerialPortServiceConfig 
{ 
    /// <summary>
    /// 
    /// </summary>
    public int SendDelayTime { get; set; } = 10000;

    /// <summary>
    /// 
    /// </summary>
    public bool RtsEnable { get; set; } = true;

    /// <summary>
    /// 
    /// </summary>
    public bool DtrEnable { get; set; } = false;
    //
    // /// <summary>
    // /// 
    // /// </summary>
    // public bool Enabled { get; set; }

    // /// <summary>
    // ///     获取配置Schema
    // /// </summary>
    // public object GetConfigurationSchema()
    // {
    //     return new
    //     {
    //         properties = new
    //         {
    //             SendDelayTime = new
    //             {
    //                 type = "integer",
    //                 title = "发送延迟时间(毫秒)",
    //                 description = "发送延迟时间(毫秒)",
    //                 minimum = 0,
    //                 maximum = 100000,
    //                 defaultValue = 10000
    //             },
    //             RtsEnable = new
    //             {
    //                 type = "boolean",
    //                 title = "RTS状态",
    //                 description = "是否启用RTS信号"
    //             },
    //             DtrEnable = new
    //             {
    //                 type = "boolean",
    //                 title = "DTR状态",
    //                 description = "是否启用DTR信号"
    //             },
    //             Enabled = new
    //             {
    //                 type = "boolean",
    //                 title = "启用状态",
    //                 description = "是否启用西门子串口通信插件"
    //             }
    //         },
    //         required = new[] { "PortName", "BaudRate", "DataBits", "StopBits", "Parity" }
    //     };
    // }
}