using IotGateway.Plugin.Core;

namespace IotGateway.SerialPortSIEMENS.Models;

/// <summary>
///     西门子串口通信配置
/// </summary>
public class SerialPortSIEMENSConfig : IPluginConfig
{
  /// <summary>
  ///     是否启用
  /// </summary>
  public bool Enabled { get; set; } = false;

  /// <summary>
  ///     串口名称
  /// </summary>
  public string PortName { get; set; } = "COM1";

  /// <summary>
  ///     波特率
  /// </summary>
  public int BaudRate { get; set; } = 9600;

  /// <summary>
  ///     数据位
  /// </summary>
  public int DataBits { get; set; } = 8;

  /// <summary>
  ///     停止位
  /// </summary>
  public string StopBits { get; set; } = "One";

  /// <summary>
  ///     校验位
  /// </summary>
  public string Parity { get; set; } = "None";

  /// <summary>
  ///     发送延迟时间(毫秒)
  /// </summary>
  public int SendDelayTime { get; set; } = 10000;

  /// <summary>
  ///     RTS状态
  /// </summary>
  public bool RtsEnable { get; set; } = true;

  /// <summary>
  ///     DTR状态
  /// </summary>
  public bool DtrEnable { get; set; }

  /// <summary>
  ///     设备ID
  /// </summary>
  public string DeviceId { get; set; } = string.Empty;

  /// <summary>
  ///     设备描述
  /// </summary>
  public string DeviceDesc { get; set; } = string.Empty;

  /// <summary>
  ///     用户名
  /// </summary>
  public string User { get; set; } = string.Empty;

  /// <summary>
  ///     密码
  /// </summary>
  public string Password { get; set; } = string.Empty;

  /// <summary>
  ///     IP地址
  /// </summary>
  public string Ip { get; set; } = string.Empty;

  /// <summary>
  ///     工作文件夹
  /// </summary>
  public string WorkFolder { get; set; } = string.Empty;

  /// <summary>
  ///     发送文件夹
  /// </summary>
  public string WorkFolderSend { get; set; } = "SEND";

  /// <summary>
  ///     接收文件夹
  /// </summary>
  public string WorkFolderRec { get; set; } = "REC";

  /// <summary>
  ///     握手方式
  /// </summary>
  public string Handshake { get; set; } = "None";

  /// <summary>
  ///     XON字符
  /// </summary>
  public int XON { get; set; } = 11;

  /// <summary>
  ///     XOFF字符
  /// </summary>
  public int XOFF { get; set; } = 13;

  /// <summary>
  ///     写入类型
  /// </summary>
  public string WriteType { get; set; } = "LINE";

  /// <summary>
  ///     指令前缀
  /// </summary>
  public string OrderPrefix { get; set; } = "Q";

  /// <summary>
  ///     指令后缀
  /// </summary>
  public string OrderSuffix { get; set; } = "V";

  /// <summary>
  ///     从配置设置参数
  /// </summary>
  internal SerialPortSIEMENSConfig SetConfig(SerialPortServiceConfig config)
  {
      RtsEnable = config.RtsEnable;
      DtrEnable = config.DtrEnable;
      return this;
  }

  /// <summary>
  ///     获取配置Schema
  /// </summary>
  public object GetConfigurationSchema()
    {
        return new
        {
            properties = new
            {
                PortName = new
                {
                    type = "string",
                    title = "串口名称",
                    description = "串口设备名称，如COM1",
                    defaultValue = "COM1"
                },
                BaudRate = new
                {
                    type = "select",
                    title = "波特率",
                    description = "串口通信波特率",
                    enums = new[]
                    {
                        new { label = "1200", value = 1200 },
                        new { label = "2400", value = 2400 },
                        new { label = "4800", value = 4800 },
                        new { label = "9600", value = 9600 },
                        new { label = "19200", value = 19200 },
                        new { label = "38400", value = 38400 },
                        new { label = "57600", value = 57600 },
                        new { label = "115200", value = 115200 }
                    },
                    defaultValue = 9600
                },
                DataBits = new
                {
                    type = "select",
                    title = "数据位",
                    description = "数据位长度",
                    enums = new[]
                    {
                        new { label = "5", value = 5 },
                        new { label = "6", value = 6 },
                        new { label = "7", value = 7 },
                        new { label = "8", value = 8 }
                    },
                    defaultValue = 8
                },
                StopBits = new
                {
                    type = "select",
                    title = "停止位",
                    description = "停止位设置",
                    enums = new[]
                    {
                        new { label = "One", value = "One" },
                        new { label = "Two", value = "Two" },
                        new { label = "OnePointFive", value = "OnePointFive" },
                        new { label = "None", value = "None" }
                    },
                    defaultValue = "One"
                },
                Parity = new
                {
                    type = "select",
                    title = "校验位",
                    description = "校验位设置",
                    enums = new[]
                    {
                        new { label = "None", value = "None" },
                        new { label = "Odd", value = "Odd" },
                        new { label = "Even", value = "Even" },
                        new { label = "Mark", value = "Mark" },
                        new { label = "Space", value = "Space" }
                    },
                    defaultValue = "None"
                },
                Enabled = new
                {
                    type = "boolean",
                    title = "启用状态",
                    description = "是否启用西门子串口通信插件"
                },
                SendDelayTime = new
                {
                    type = "integer",
                    title = "发送延迟时间(毫秒)",
                    description = "发送延迟时间(毫秒)",
                    minimum = 0,
                    maximum = 100000,
                    defaultValue = 10000
                },
                RtsEnable = new
                {
                    type = "boolean",
                    title = "RTS状态",
                    description = "是否启用RTS信号"
                },
                DtrEnable = new
                {
                    type = "boolean",
                    title = "DTR状态",
                    description = "是否启用DTR信号"
                },
                OrderPrefix = new
                {
                    type = "string",
                    title = "指令前缀",
                    description = "协议指令前缀",
                    defaultValue = "Q"
                },
                OrderSuffix = new
                {
                    type = "string",
                    title = "指令后缀",
                    description = "协议指令后缀",
                    defaultValue = "V"
                }
            },
            required = new[] { "PortName", "BaudRate", "DataBits", "StopBits", "Parity" }
        };
    }
}