namespace IotGateway.SerialPortSIEMENS.Models
{
  /// <summary>
  /// 协议配置项
  /// </summary>
  public class ProtocolConfigItem
  {
    /// <summary>
    /// 协议类型
    /// </summary>
    public string Type { get; set; } = "SIEMENS";

    /// <summary>
    /// 协议描述
    /// </summary>
    public string Description { get; set; } = "西门子数控系统串口协议";

    /// <summary>
    /// 指令前缀
    /// </summary>
    public string OrderPrefix { get; set; } = "Q";

    /// <summary>
    /// 指令后缀
    /// </summary>
    public string OrderSuffix { get; set; } = "V";

    /// <summary>
    /// 换行符
    /// </summary>
    public string LineBreak { get; set; } = "\n";

    /// <summary>
    /// 文件列表头格式
    /// </summary>
    public string ListFileHead { get; set; } = "NC File List ({0})";

    /// <summary>
    /// 文件前缀
    /// </summary>
    public string FilePrefix { get; set; } = "%";

    /// <summary>
    /// 文件后缀
    /// </summary>
    public string FileSuffix { get; set; } = "\u001a";
  }
}