<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <OutputPath>..\..\..\01-应用服务\IotGateway/bin/$(Configuration)/$(TargetFramework)/plugins/services/SerialPortSIEMENS/</OutputPath>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\IotGateway.Plugin.Core\IotGateway.Plugin.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Update="ProtocolConfig.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
      <Reference Include="SerialPortProtocol">
        <HintPath>C:\Users\<USER>\Desktop\项目依赖包\西门子串口\SerialPortProtocol.dll</HintPath>
      </Reference>
    </ItemGroup>

</Project> 