using System;
using IotGateway.SerialPortSIEMENS.Models;
using SerialPortProtocol.Enum;
using SerialPortProtocol.Model;

namespace IotGateway.SerialPortSIEMENS
{
    public static class DeviceConfigInfoExtend
    {
        public static SerialModelBase ToSerialModelBase(this SerialPortSIEMENSConfig config)
        {
            Enum.TryParse(config.WriteType, out EmWriteType writeType);
            
            return new SerialModelBase
            {
                WriteType = writeType,
                NetPingTimeout = 100,
                ComServerIP = config.Ip ?? "",
                PortName = config.PortName,
                BaudRate = config.BaudRate,
                DataBits = config.DataBits,
                StopBits = config.StopBits,
                Parity = config.Parity,
                Handshake = config.Handshake,
                RtsEnable = config.RtsEnable,
                DtrEnable = config.DtrEnable
            };
        }
    }
}