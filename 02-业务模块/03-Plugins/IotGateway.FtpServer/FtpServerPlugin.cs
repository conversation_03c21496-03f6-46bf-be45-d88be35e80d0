using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Furion.FriendlyException;
using IotGateway.FtpServer.Models;
using IotGateway.Plugin.Core;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using IotGateway.Plugin.Core.Models;

namespace IotGateway.FtpServer;

/// <summary>
///     FTP服务器插件
/// </summary>
public class FtpServerPlugin : PluginBase
{
    /// <summary>
    ///     配置文件路径
    /// </summary>
    private readonly string _configPath;

    /// <summary>
    ///     配置
    /// </summary>
    private readonly FtpConfig _config;

    /// <summary>
    ///     配置属性
    /// </summary>
    public object? Configuration => _config;

    /// <summary>
    ///     FTP服务器
    /// </summary>
    private Core.FtpServer? _ftpServer;

    /// <summary>
    ///     日志记录器
    /// </summary>
    private ILogger<FtpServerPlugin>? _logger;

    /// <summary>
    ///     FTP会话日志记录器
    /// </summary>
    private ILogger<Core.FtpSession>? _sessionLogger;

    /// <summary>
    ///     获取配置
    /// </summary>
    public FtpConfig Config => _config;

    /// <summary>
    ///     构造函数
    /// </summary>
    public FtpServerPlugin()
    {
        try
        {
            _configPath = Path.Combine(AppContext.BaseDirectory, "Configs", "ftpserver.json");
            _config = LoadConfiguration() ?? new FtpConfig();
            _configuration = _config;

            // 延迟创建日志记录器
            InitLogger();
        }
        catch (Exception ex)
        {
            // 确保构造函数不会抛出异常
            Console.WriteLine($"FTP插件初始化失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     初始化日志记录器
    /// </summary>
    private void InitLogger()
    {
        try
        {
            var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole()
                    .SetMinimumLevel(LogLevel.Debug);
            });
            _logger = loggerFactory.CreateLogger<FtpServerPlugin>();
            _sessionLogger = loggerFactory.CreateLogger<Core.FtpSession>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"日志初始化失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     加载配置
    /// </summary>
    private FtpConfig? LoadConfiguration()
    {
        try
        {
            if (File.Exists(_configPath))
            {
                var json = File.ReadAllText(_configPath);
                var config = JsonSerializer.Deserialize<FtpConfig>(json);
                return config;
            }

            _logger?.LogWarning("FTP配置文件不存在，将使用默认配置");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "加载FTP配置失败");
        }

        return null;
    }

    /// <summary>
    ///     保存配置
    /// </summary>
    private void SaveConfiguration()
    {
        try
        {
            _logger?.LogInformation("正在保存FTP配置...");
            var directory = Path.GetDirectoryName(_configPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
                _logger?.LogInformation("创建配置目录: {Directory}", directory);
            }

            var json = JsonSerializer.Serialize(_config, new JsonSerializerOptions
            {
                WriteIndented = true
            });
            File.WriteAllText(_configPath, json);
            _logger?.LogInformation("FTP配置保存成功");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "保存FTP配置失败");
        }
    }

    /// <summary>
    ///     插件名称
    /// </summary>
    public override string Name => "FTP Server";

    /// <summary>
    ///     插件版本
    /// </summary>
    public override string Version => "1.0.0";

    /// <summary>
    ///     插件描述
    /// </summary>
    public override string Description => "FTP服务器插件";

    /// <summary>
    ///     插件分类
    /// </summary>
    public override string Category => "Dnc";

    /// <summary>
    ///     初始化
    /// </summary>
    public override async Task InitializeAsync()
    {
        try
        {
            _logger?.LogInformation("FTP初始化开始: {Config}", JsonSerializer.Serialize(_config));
            // 如果自启动
            if (_config.Enabled)
            {
                _logger?.LogInformation("FTP服务器配置为自启动，准备启动服务器");
                await StartAsync();
            }

            await base.InitializeAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "FTP服务器初始化失败");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     启动
    /// </summary>
    /// <returns></returns>
    public override async Task StartAsync()
    {
        try
        {
            Console.WriteLine("正在启动FTP服务器...");
            _logger?.LogInformation("正在启动FTP服务器...");

            if (_ftpServer != null)
            {
                Console.WriteLine("停止已有FTP服务器");
                await StopAsync();

                // 添加短暂延迟，确保端口完全释放
                await Task.Delay(1000);
            }

            // 检查端口是否可用
            if (!IsPortAvailable(_config.Port))
            {
                var errorMessage = $"端口 {_config.Port} 已被占用，无法启动FTP服务器";
                _logger?.LogError(errorMessage);
                Console.WriteLine(errorMessage);
                throw Oops.Oh(errorMessage);
            }

            // 创建并启动FTP服务器
            _ftpServer = new Core.FtpServer(this, _config, _sessionLogger);
            await _ftpServer.StartAsync();

            _logger?.LogInformation("FTP服务器启动成功");
            Console.WriteLine("FTP服务器启动成功");

            await base.StartAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"FTP服务器启动失败: {ex.Message}");
            _logger?.LogError(ex, "FTP服务器启动失败");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    /// 检查端口是否可用
    /// </summary>
    /// <param name="port">要检查的端口</param>
    /// <returns>如果端口可用返回true，否则返回false</returns>
    private bool IsPortAvailable(int port)
    {
        try
        {
            // 尝试在指定端口上创建一个TCP监听器
            using var socket = new Socket(
                AddressFamily.InterNetwork,
                SocketType.Stream,
                ProtocolType.Tcp);

            socket.SetSocketOption(
                SocketOptionLevel.Socket,
                SocketOptionName.ReuseAddress,
                true);

            socket.Bind(new System.Net.IPEndPoint(System.Net.IPAddress.Any, port));
            socket.Close();
            return true;
        }
        catch (SocketException)
        {
            return false;
        }
    }

    /// <summary>
    ///     停止
    /// </summary>
    /// <returns></returns>
    public override async Task StopAsync()
    {
        _logger?.LogInformation("正在停止FTP服务器...");

        try
        {
            if (_ftpServer != null)
            {
                try
                {
                    // 添加短暂延迟，确保所有连接都有机会完成
                    await Task.Delay(500);

                    // 停止FTP服务器
                    await _ftpServer.StopAsync();
                }
                catch (OperationCanceledException)
                {
                    _logger?.LogWarning("停止FTP服务器超时，强制终止");
                }
                catch (SocketException ex)
                {
                    // 特别处理"Address already in use"错误
                    _logger?.LogWarning("停止FTP服务器时出现地址占用错误: {Message}", ex.Message);
                }

                _ftpServer = null;
                _logger?.LogInformation("FTP服务器已停止");
            }

            // 确保垃圾回收运行，释放所有未使用的资源
            GC.Collect();
            GC.WaitForPendingFinalizers();

            await base.StopAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "停止FTP服务器时发生错误");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     更新配置
    /// </summary>
    /// <param name="configuration"></param>
    /// <returns></returns>
    public override async Task UpdateConfigurationAsync(object configuration)
    {
        try
        {
            if (configuration is JsonElement jsonElement)
            {
                // 将JsonElement反序列化为FtpConfig对象
                var config = jsonElement.Deserialize<FtpConfig>(new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (config != null)
                {
                    _config.Port = config.Port; // 端口
                    _config.Enabled = config.Enabled; // 是否启用
                    _config.SendPath = config.SendPath; // 发送文件路径
                    _config.ReceivePath = config.ReceivePath; // 接收文件路径

                    // 保存更新后的配置
                    SaveConfiguration();

                    await StopAsync();
                    // 如果服务正在运行，需要重启以应用新配置
                    if (_config.Enabled)
                    {
                        await StartAsync();
                    }
                }
            }
            else if (configuration is FtpConfig config)
            {
                // 保持原有的处理逻辑
                _config.Port = config.Port;
                _config.Enabled = config.Enabled;
                _config.SendPath = config.SendPath;
                _config.ReceivePath = config.ReceivePath;

                SaveConfiguration(); // 保存配置
                await StopAsync();

                if (_config.Enabled)
                {
                    await StartAsync();
                }
            }

            await base.UpdateConfigurationAsync(configuration); // 更新配置
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "更新FTP配置失败");
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    /// 获取用户特定的文件路径
    /// </summary>
    /// <param name="user">用户信息</param>
    /// <param name="pathTemplate">路径模板</param>
    /// <returns>完整的文件路径</returns>
    public string GetUserSpecificPath(FtpUser user, string pathTemplate)
    {
        // 替换路径中的占位符
        string path = string.Format(pathTemplate, user.DeviceCode);
        // 返回完整路径
        return Path.Combine(AppContext.BaseDirectory, path);
    }

    /// <summary>
    /// 确保目录存在
    /// </summary>
    /// <param name="path">目录路径</param>
    public void EnsureDirectoryExists(string path)
    {
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
            _logger?.LogInformation("创建目录: {Path}", path);
        }
    }

    /// <summary>
    /// 设置DNC配置
    /// </summary>
    /// <param name="dncConfig">DNC配置</param>
    /// <returns>设置结果</returns>
    public override async Task SetDncConfigAsync(DncConfig dncConfig)
    {
        // 先调用基类方法，将配置添加到_dncConfigs中
        await base.SetDncConfigAsync(dncConfig);

        // 如果配置是启用的，为这个配置创建目录
        if (dncConfig.Enabled)
        {
            // 确保用户的发送和接收目录存在
            string sendPath = GetUserSpecificPath(new FtpUser
            {
                DeviceCode = dncConfig.DeviceCode,
                Username = dncConfig.Username,
                IsAuthenticated = true
            }, _config.SendPath);

            string recPath = GetUserSpecificPath(new FtpUser
            {
                DeviceCode = dncConfig.DeviceCode,
                Username = dncConfig.Username,
                IsAuthenticated = true
            }, _config.ReceivePath);

            EnsureDirectoryExists(sendPath);
            EnsureDirectoryExists(recPath);
            _logger?.LogInformation("用户 {DeviceCode} 的目录创建完成，发送目录: {SendPath}, 接收目录: {RecPath}",
                dncConfig.DeviceCode, sendPath, recPath);
        }
    }
}