using System;
using System.IO;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using IotGateway.FtpServer.Models;
using Microsoft.Extensions.Logging;

namespace IotGateway.FtpServer.Core;

/// <summary>
/// FTP会话类，管理与客户端的连接
/// </summary>
public class FtpSession : IDisposable
{
  /// <summary>
  /// TCP客户端连接
  /// </summary>
  public TcpClient Client { get; }

  /// <summary>
  /// 客户端网络流
  /// </summary>
  public NetworkStream Stream { get; }

  /// <summary>
  /// 用户信息
  /// </summary>
  public FtpUser User { get; } = new FtpUser();

  /// <summary>
  /// 命令流读取器
  /// </summary>
  public StreamReader Reader { get; }

  /// <summary>
  /// 命令流写入器
  /// </summary>
  public StreamWriter Writer { get; }

  /// <summary>
  /// 数据会话，用于传输文件
  /// </summary>
  public TcpClient? DataClient { get; set; }

  /// <summary>
  /// 客户端IP和端口
  /// </summary>
  public string RemoteEndPoint { get; }

  /// <summary>
  /// 日志记录器
  /// </summary>
  private readonly ILogger _logger;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="client">客户端连接</param>
  /// <param name="logger">日志记录器</param>
  public FtpSession(TcpClient client, ILogger logger)
  {
    Client = client;
    Stream = client.GetStream();
    Reader = new StreamReader(Stream, Encoding.ASCII);
    Writer = new StreamWriter(Stream, Encoding.ASCII) { AutoFlush = true };
    RemoteEndPoint = client.Client.RemoteEndPoint?.ToString() ?? "Unknown";
    _logger = logger;
  }

  /// <summary>
  /// 发送响应给客户端
  /// </summary>
  /// <param name="response">响应内容</param>
  public async Task SendResponseAsync(string response)
  {
    try
    {
      // 确保响应以 \r\n 结尾
      string formattedResponse = response.EndsWith("\r\n") ? response : response + "\r\n";
      await Writer.WriteAsync(formattedResponse);
      _logger.LogInformation("向客户端（{RemoteEndPoint}）发送[{Response}]", RemoteEndPoint, response);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "发送响应时出错: {Response}", response);
    }
  }

  /// <summary>
  /// 创建数据连接（被动模式）
  /// </summary>
  public TcpClient? CreateDataConnection()
  {
    try
    {
      if (User.IsPassiveMode && User.DataListener != null)
      {
        // 被动模式 - 服务器接受来自客户端的连接
        var dataClient = User.DataListener.AcceptTcpClient();
        User.DataListener.Stop();
        User.DataListener = null;
        return dataClient;
      }
      else if (User.DataEndPoint != null)
      {
        // 主动模式 - 服务器连接到客户端
        var dataClient = new TcpClient();
        dataClient.Connect(User.DataEndPoint);
        return dataClient;
      }

      return null;
    }
    catch (Exception)
    {
      return null;
    }
  }

  /// <summary>
  /// 释放资源
  /// </summary>
  public void Dispose()
  {
    Writer.Dispose();
    Reader.Dispose();
    Stream.Dispose();
    Client.Dispose();
    User.DataListener?.Stop();
    DataClient?.Dispose();

    GC.SuppressFinalize(this);
  }
}