using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using IotGateway.FtpServer.Models;
using IotGateway.Plugin.Core.Models;
using Microsoft.Extensions.Logging;

namespace IotGateway.FtpServer.Core;

/// <summary>
/// FTP命令处理器
/// </summary>
public class FtpCommandHandler
{
  private readonly FtpSession _session;
  private readonly FtpServerPlugin _plugin;
  private readonly ILogger _logger;
  private readonly FtpConfig _config;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="session">FTP会话</param>
  /// <param name="plugin">FTP服务器插件</param>
  /// <param name="logger">日志记录器</param>
  public FtpCommandHandler(FtpSession session, FtpServerPlugin plugin, ILogger logger)
  {
    _session = session;
    _plugin = plugin;
    _logger = logger;
    _config = (FtpConfig)plugin.Configuration;
  }

  /// <summary>
  /// 处理FTP命令
  /// </summary>
  /// <param name="commandLine">命令行</param>
  public async Task HandleCommandAsync(string commandLine)
  {
    try
    {

      // 解析命令和参数
      var cmdParts = commandLine.Split(new[] { ' ' }, 2);
      var command = cmdParts[0].ToUpperInvariant();
      var parameter = cmdParts.Length > 1 ? cmdParts[1] : string.Empty;

      // 记录命令和参数
      _logger.LogInformation("来自{RemoteEndPoint}：[{Command}] ", _session.RemoteEndPoint, command);
      _logger.LogInformation("Command: {Command} param: {Parameter}", command, parameter);

      // 根据命令类型处理
      switch (command)
      {
        case "USER":
          await HandleUserAsync(parameter);
          break;
        case "PASS":
          await HandlePassAsync(parameter);
          break;
        case "SYST":
          await _session.SendResponseAsync("215 UNIX Type: L8");
          break;
        case "FEAT":
          await HandleFeatAsync();
          break;
        case "PWD":
        case "XPWD":
          await HandlePwdAsync();
          break;
        case "CWD":
        case "XCWD":
          await HandleCwdAsync(parameter);
          break;
        case "CDUP":
        case "XCUP":
          await HandleCdupAsync();
          break;
        case "TYPE":
          await HandleTypeAsync(parameter);
          break;
        case "PASV":
          await HandlePasvAsync();
          break;
        case "PORT":
          await HandlePortAsync(parameter);
          break;
        case "LIST":
        case "NLST":
          await HandleListAsync(parameter, command == "LIST");
          break;
        case "RETR":
          await HandleRetrAsync(parameter);
          break;
        case "STOR":
          await HandleStorAsync(parameter);
          break;
        case "DELE":
          await HandleDeleAsync(parameter);
          break;
        case "MKD":
        case "XMKD":
          await HandleMkdAsync(parameter);
          break;
        case "RMD":
        case "XRMD":
          await HandleRmdAsync(parameter);
          break;
        case "QUIT":
          await _session.SendResponseAsync("221 Goodbye");
          break;
        case "NOOP":
          await _session.SendResponseAsync("200 NOOP command successful");
          break;
        default:
          await _session.SendResponseAsync($"502 Command '{command}' not implemented");
          break;
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理FTP命令时出错");
      await _session.SendResponseAsync("500 Error processing command");
    }
  }

  /// <summary>
  /// 处理USER命令
  /// </summary>
  private async Task HandleUserAsync(string username)
  {
    _session.User.Username = username;
    _session.User.LoginState = 1; // 等待密码

    await _session.SendResponseAsync($"331 Password required for {username}");
  }

  /// <summary>
  /// 处理PASS命令
  /// </summary>
  private async Task HandlePassAsync(string password)
  {
    if (_session.User.LoginState != 1)
    {
      await _session.SendResponseAsync("503 Login with USER first");
      return;
    }

    var authenticated = await AuthenticateUserAsync(_session.User.Username, password);
    if (authenticated)
    {
      // 创建用户目录
      CreateUserDirectories(_session.User);

      await _session.SendResponseAsync("230 User logged in, proceed");
    }
    else
    {
      await _session.SendResponseAsync("530 Login incorrect");
    }
  }

  /// <summary>
  /// 处理FEAT命令
  /// </summary>
  private async Task HandleFeatAsync()
  {
    await _session.SendResponseAsync("211- Features:");
    await _session.SendResponseAsync(" UTF8");
    await _session.SendResponseAsync(" PASV");
    await _session.SendResponseAsync(" SIZE");
    await _session.SendResponseAsync(" TYPE A;I");
    await _session.SendResponseAsync("211 End");
  }

  /// <summary>
  /// 处理PWD命令
  /// </summary>
  private async Task HandlePwdAsync()
  {
    var relativePath = GetRelativePath(_session.User.CurrentDirectory);
    await _session.SendResponseAsync($"257 \"{relativePath}\" is current directory");
  }

  /// <summary>
  /// 处理CWD命令
  /// </summary>
  private async Task HandleCwdAsync(string path)
  {
    if (!_session.User.IsAuthenticated)
    {
      await _session.SendResponseAsync("530 Not logged in");
      return;
    }

    var targetPath = ResolvePath(path);
    if (Directory.Exists(targetPath) && IsPathAllowed(targetPath))
    {
      _session.User.CurrentDirectory = targetPath;
      await _session.SendResponseAsync($"250 Directory changed to \"{GetRelativePath(targetPath)}\"");
    }
    else
    {
      await _session.SendResponseAsync($"550 Directory not found: {path}");
    }
  }

  /// <summary>
  /// 处理CDUP命令
  /// </summary>
  private async Task HandleCdupAsync()
  {
    if (!_session.User.IsAuthenticated)
    {
      await _session.SendResponseAsync("530 Not logged in");
      return;
    }

    var parent = Directory.GetParent(_session.User.CurrentDirectory);
    if (parent != null && IsPathAllowed(parent.FullName))
    {
      _session.User.CurrentDirectory = parent.FullName;
      await _session.SendResponseAsync($"250 Directory changed to \"{GetRelativePath(_session.User.CurrentDirectory)}\"");
    }
    else
    {
      await _session.SendResponseAsync("550 Cannot go above user root directory");
    }
  }

  /// <summary>
  /// 处理TYPE命令
  /// </summary>
  private async Task HandleTypeAsync(string type)
  {
    switch (type)
    {
      case "A":
        _session.User.TransferType = "A";
        await _session.SendResponseAsync("200 Type set to A");
        break;
      case "I":
        _session.User.TransferType = "I";
        await _session.SendResponseAsync("200 Type set to I");
        break;
      default:
        await _session.SendResponseAsync($"504 Type {type} not implemented");
        break;
    }
  }

  /// <summary>
  /// 处理PASV命令
  /// </summary>
  private async Task HandlePasvAsync()
  {
    if (!_session.User.IsAuthenticated)
    {
      await _session.SendResponseAsync("530 Not logged in");
      return;
    }

    var localEndpoint = (IPEndPoint)_session.Client.Client.LocalEndPoint;

    // 创建被动监听器
    _session.User.DataListener?.Stop();
    _session.User.DataListener = new TcpListener(localEndpoint.Address, 0);
    _session.User.DataListener.Start();

    var pasvEndpoint = (IPEndPoint)_session.User.DataListener.LocalEndpoint;
    byte[] ip = localEndpoint.Address.GetAddressBytes();
    int port = pasvEndpoint.Port;

    int portHigh = port / 256;
    int portLow = port % 256;

    _session.User.IsPassiveMode = true;

    await _session.SendResponseAsync($"227 Entering Passive Mode ({ip[0]},{ip[1]},{ip[2]},{ip[3]},{portHigh},{portLow})");
  }

  /// <summary>
  /// 处理PORT命令
  /// </summary>
  private async Task HandlePortAsync(string parameter)
  {
    if (!_session.User.IsAuthenticated)
    {
      await _session.SendResponseAsync("530 Not logged in");
      return;
    }

    try
    {
      var parts = parameter.Split(',').Select(int.Parse).ToArray();
      if (parts.Length != 6)
      {
        await _session.SendResponseAsync("501 Invalid PORT command");
        return;
      }

      string ip = $"{parts[0]}.{parts[1]}.{parts[2]}.{parts[3]}";
      int port = (parts[4] * 256) + parts[5];

      _session.User.DataEndPoint = new IPEndPoint(IPAddress.Parse(ip), port);
      _session.User.IsPassiveMode = false;

      await _session.SendResponseAsync("200 PORT command successful");
    }
    catch
    {
      await _session.SendResponseAsync("501 Invalid PORT command");
    }
  }

  /// <summary>
  /// 处理LIST和NLST命令
  /// </summary>
  private async Task HandleListAsync(string path, bool isList)
  {
    if (!_session.User.IsAuthenticated)
    {
      await _session.SendResponseAsync("530 Not logged in");
      return;
    }

    string targetPath = string.IsNullOrWhiteSpace(path) ?
        _session.User.CurrentDirectory : ResolvePath(path);

    if (!Directory.Exists(targetPath) || !IsPathAllowed(targetPath))
    {
      await _session.SendResponseAsync($"550 Directory not found: {path}");
      return;
    }

    try
    {
      await _session.SendResponseAsync("150 Opening ASCII data connection");

      using var dataClient = _session.CreateDataConnection();
      if (dataClient == null)
      {
        await _session.SendResponseAsync("425 Can't open data connection");
        return;
      }

      // 记录使用的模式（主动或被动）
      _logger.LogInformation("采用{Mode}模式向用户发送{Command}目录和文件列表",
          _session.User.IsPassiveMode ? "被动" : "主动",
          isList ? "LIST" : "NLST");

      using var dataStream = dataClient.GetStream();
      using var writer = new StreamWriter(dataStream, Encoding.ASCII);

      // 获取目录列表
      var listing = GenerateDirectoryListing(targetPath, isList);

      // 记录目录列表内容
      if (isList)
      {
        // LIST命令 - 详细格式，使用[CRLF]表示换行符
        _logger.LogInformation("向用户发送(字符串信息)：[{Content}]", listing.Replace("\r\n", "[CRLF]"));
      }
      else
      {
        // NLST命令 - 简单格式，直接记录文件名
        _logger.LogInformation("向用户发送(字符串信息)：[{Content}]", listing);
      }

      // 发送目录列表
      await writer.WriteAsync(listing);
      await writer.FlushAsync();

      // 记录发送完毕
      _logger.LogInformation("发送完毕");

      await _session.SendResponseAsync("226 Transfer complete");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理LIST命令时出错: {Path}", path);
      await _session.SendResponseAsync("550 Failed to get directory listing");
    }
  }

  /// <summary>
  /// 处理RETR命令
  /// </summary>
  private async Task HandleRetrAsync(string path)
  {
    if (!_session.User.IsAuthenticated)
    {
      await _session.SendResponseAsync("530 Not logged in");
      return;
    }

    var filePath = ResolvePath(path);
    if (!File.Exists(filePath) || !IsPathAllowed(filePath))
    {
      await _session.SendResponseAsync($"550 Files not found: {path}");
      return;
    }

    try
    {
      var fileInfo = new FileInfo(filePath);
      await _session.SendResponseAsync($"150 Opening data connection for {path} ({fileInfo.Length} bytes)");

      using var dataClient = _session.CreateDataConnection();
      if (dataClient == null)
      {
        await _session.SendResponseAsync("425 Can't open data connection");
        return;
      }

      using var dataStream = dataClient.GetStream();
      using var fileStream = File.OpenRead(filePath);

      // 发送文件
      await fileStream.CopyToAsync(dataStream);

      await _session.SendResponseAsync("226 Transfer complete");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理RETR命令时出错: {Path}", path);
      await _session.SendResponseAsync("550 Failed to send file");
    }
  }

  /// <summary>
  /// 处理STOR命令
  /// </summary>
  private async Task HandleStorAsync(string path)
  {
    if (!_session.User.IsAuthenticated)
    {
      await _session.SendResponseAsync("530 Not logged in");
      return;
    }

    // 确定上传的目标文件名
    string filename = Path.GetFileName(path);
    if (string.IsNullOrWhiteSpace(filename))
    {
      await _session.SendResponseAsync("553 Invalid file name");
      return;
    }

    // 确定目标目录
    string targetDir = _session.User.CurrentDirectory;
    if (!Directory.Exists(targetDir) || !IsPathAllowed(targetDir))
    {
      await _session.SendResponseAsync("550 Directory not accessible");
      return;
    }

    // 构建完整的文件路径
    string filePath = Path.Combine(targetDir, filename);

    try
    {
      await _session.SendResponseAsync($"150 Opening data connection for {filename}");

      using var dataClient = _session.CreateDataConnection();
      if (dataClient == null)
      {
        await _session.SendResponseAsync("425 Can't open data connection");
        return;
      }

      using var dataStream = dataClient.GetStream();
      using var fileStream = File.Create(filePath);

      // 接收文件
      await dataStream.CopyToAsync(fileStream);

      await _session.SendResponseAsync("226 Transfer complete");

      _logger.LogInformation("文件上传成功: {FilePath}", filePath);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理STOR命令时出错: {Path}", path);
      await _session.SendResponseAsync("550 Failed to store file");
    }
  }

  /// <summary>
  /// 处理DELE命令
  /// </summary>
  private async Task HandleDeleAsync(string path)
  {
    if (!_session.User.IsAuthenticated)
    {
      await _session.SendResponseAsync("530 Not logged in");
      return;
    }

    var filePath = ResolvePath(path);
    if (!File.Exists(filePath) || !IsPathAllowed(filePath))
    {
      await _session.SendResponseAsync($"550 Files not found: {path}");
      return;
    }

    try
    {
      File.Delete(filePath);
      await _session.SendResponseAsync("250 Files deleted");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理DELE命令时出错: {Path}", path);
      await _session.SendResponseAsync("550 Failed to delete file");
    }
  }

  /// <summary>
  /// 处理MKD命令
  /// </summary>
  private async Task HandleMkdAsync(string path)
  {
    if (!_session.User.IsAuthenticated)
    {
      await _session.SendResponseAsync("530 Not logged in");
      return;
    }

    var dirPath = ResolvePath(path);
    if (!IsPathAllowed(dirPath))
    {
      await _session.SendResponseAsync("550 Permission denied");
      return;
    }

    try
    {
      Directory.CreateDirectory(dirPath);
      await _session.SendResponseAsync($"257 \"{path}\" created");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理MKD命令时出错: {Path}", path);
      await _session.SendResponseAsync("550 Failed to create directory");
    }
  }

  /// <summary>
  /// 处理RMD命令
  /// </summary>
  private async Task HandleRmdAsync(string path)
  {
    if (!_session.User.IsAuthenticated)
    {
      await _session.SendResponseAsync("530 Not logged in");
      return;
    }

    var dirPath = ResolvePath(path);
    if (!Directory.Exists(dirPath) || !IsPathAllowed(dirPath))
    {
      await _session.SendResponseAsync($"550 Directory not found: {path}");
      return;
    }

    try
    {
      Directory.Delete(dirPath, false);
      await _session.SendResponseAsync("250 Directory removed");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理RMD命令时出错: {Path}", path);
      await _session.SendResponseAsync("550 Failed to remove directory");
    }
  }

  /// <summary>
  /// 验证用户身份
  /// </summary>
  private async Task<bool> AuthenticateUserAsync(string username, string password)
  {
    try
    {
      // 通过DNC配置验证
      var dncConfigs = await _plugin.GetDncConfigsAsync();
      if (dncConfigs == null || !dncConfigs.Any())
      {
        _logger.LogWarning("没有可用的DNC配置");
        return false;
      }

      var matchedConfig = dncConfigs.FirstOrDefault(c =>
          c.Enabled &&
          c.Username == username &&
          c.Password == password);

      if (matchedConfig != null)
      {
        _logger.LogInformation("用户 {Username} 验证成功, 设备: {DeviceCode}",
            username, matchedConfig.DeviceCode);

        _session.User.IsAuthenticated = true;
        _session.User.LoginState = 2;
        _session.User.DeviceCode = matchedConfig.DeviceCode;
        _session.User.Role = string.IsNullOrEmpty(matchedConfig.AccessPermission) ?
            "user" : matchedConfig.AccessPermission;

        return true;
      }

      return false;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "验证用户时出错");
      return false;
    }
  }

  /// <summary>
  /// 创建用户目录
  /// </summary>
  private void CreateUserDirectories(FtpUser user)
  {
    try
    {
      // 确定用户标识符
      string userIdentifier = GetUserIdentifier(user);

      // 设置用户工作目录
      user.WorkDirectory = Path.Combine(AppContext.BaseDirectory, "RootFiles", userIdentifier);
      user.CurrentDirectory = user.WorkDirectory;

      // 确保目录存在
      Directory.CreateDirectory(user.WorkDirectory);

      // 创建SEND和REC子目录
      string sendPath = Path.Combine(user.WorkDirectory, "SEND");
      string recPath = Path.Combine(user.WorkDirectory, "REC");

      Directory.CreateDirectory(sendPath);
      Directory.CreateDirectory(recPath);

      _logger.LogInformation("用户 {Username} 的目录已创建: {WorkDir}",
          user.Username, user.WorkDirectory);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "创建用户目录时出错");
    }
  }

  /// <summary>
  /// 获取用户标识符
  /// </summary>
  private string GetUserIdentifier(FtpUser user)
  {
    if (!string.IsNullOrEmpty(user.DeviceCode))
      return user.DeviceCode;

    if (!string.IsNullOrEmpty(user.Username) && user.Username != "USER")
      return user.Username;

    return "USER";
  }

  /// <summary>
  /// 解析路径
  /// </summary>
  private string ResolvePath(string path)
  {
    if (string.IsNullOrWhiteSpace(path))
      return _session.User.CurrentDirectory;

    string targetPath;
    // 处理根路径
    if (path.StartsWith("/"))
      targetPath = Path.Combine(_session.User.WorkDirectory, path.TrimStart('/'));
    else
      // 处理相对路径
      targetPath = Path.Combine(_session.User.CurrentDirectory, path);

    // 规范化路径
    targetPath = Path.GetFullPath(targetPath);

    return targetPath;
  }

  /// <summary>
  /// 获取相对路径
  /// </summary>
  private string GetRelativePath(string path)
  {
    // 规范化路径
    path = Path.GetFullPath(path);
    var workDir = Path.GetFullPath(_session.User.WorkDirectory);

    if (path.Equals(workDir, StringComparison.OrdinalIgnoreCase))
      return "/";

    if (path.StartsWith(workDir, StringComparison.OrdinalIgnoreCase))
    {
      var relativePath = path.Substring(workDir.Length)
          .Replace('\\', '/');

      if (!relativePath.StartsWith("/"))
        relativePath = "/" + relativePath;

      return relativePath;
    }

    return path;
  }

  /// <summary>
  /// 检查路径是否允许访问
  /// </summary>
  private bool IsPathAllowed(string path)
  {
    var normalized = Path.GetFullPath(path);
    var workDir = Path.GetFullPath(_session.User.WorkDirectory);

    return normalized.StartsWith(workDir, StringComparison.OrdinalIgnoreCase);
  }

  /// <summary>
  /// 生成目录列表字符串
  /// </summary>
  private string GenerateDirectoryListing(string directoryPath, bool isList)
  {
    if (isList)
    {
      var listing = new StringBuilder();
      var culture = CultureInfo.CreateSpecificCulture("en-GB");

      // 目录项
      foreach (var dir in Directory.GetDirectories(directoryPath))
      {
        var info = new DirectoryInfo(dir);
        listing.AppendFormat(CultureInfo.InvariantCulture,
            "drwx------ 1 <USER> <GROUP> {0,12} {1} {2}\r\n",
            "<DIR>",
            info.LastWriteTime.ToString("MMM dd HH:mm", culture),
            info.Name);
      }

      // 文件项
      foreach (var file in Directory.GetFiles(directoryPath))
      {
        var info = new FileInfo(file);
        listing.AppendFormat(CultureInfo.InvariantCulture,
            "-rwx------ 1 <USER> <GROUP> {0,12} {1} {2}\r\n",
            info.Length,
            info.LastWriteTime.ToString("MMM dd HH:mm", culture),
            info.Name);
      }

      return listing.ToString();
    }
    else
    {
      var listing = new StringBuilder();
      var entries = Directory.GetFileSystemEntries(directoryPath)
          .Select(Path.GetFileName)
          .OrderBy(name => name);

      foreach (var name in entries)
      {
        listing.Append(name + "\r\n");
      }

      return listing.ToString();
    }
  }
}

