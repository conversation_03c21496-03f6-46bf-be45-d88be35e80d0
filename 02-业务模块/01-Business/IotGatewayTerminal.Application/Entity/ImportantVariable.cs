using Feng.IotGateway.Core.Entity;
using SqlSugar;

namespace IotGatewayTerminal.Application.Entity;

/// <summary>
///     重要采集变量
/// </summary>
[SugarTable("important_variable", "重要采集变量")]
public class ImportantVariable : EntityBaseId
{
    /// <summary>
    /// 设备Id
    /// </summary>
    [SugarColumn(ColumnDescription = "设备Id")]
    public long DeviceId { get; set; }
    
    /// <summary>
    ///     设备变量Id
    /// </summary>
    [SugarColumn(ColumnDescription = "设备变量Id")]
    public long DeviceVariableId { get; set; }

    #region 关联对象

    /// <summary>
    ///     设备变量
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(DeviceVariableId))]
    public DeviceVariable? DeviceVariable { get; set; }

    #endregion
}