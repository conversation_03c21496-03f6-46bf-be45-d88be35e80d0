using Feng.IotGateway.Core.Base;
using Feng.IotGateway.Core.SqlSugar;
using IotGatewayTerminal.Application.Entity;
using TDengIne;
using TDengIne.Dto;
using Yitter.IdGenerator;

namespace IotGatewayTerminal.Application.Service;

/// <summary>
/// 设备扩展
/// </summary>
public class DeviceExService: IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<ImportantVariable> _device;
    /// <summary>
    /// 时序库读取
    /// </summary>
    private readonly ReadService _readService;

    public DeviceExService(ReadService readService, SqlSugarRepository<ImportantVariable> device)
    {
        _readService = readService;
        _device = device;
    }
    
    /// <summary>
    ///     采集明细
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceVariable/historical/list")]
    public async Task<dynamic> Page([FromQuery] GetHistoricalDataByLimitInput input)
    {
        return await _readService.GetHistoricalDataByLimit(input);
    }
    
    /// <summary>
    ///     关键参数集合
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceVariable/important/list")]
    public async Task<dynamic> ImportantList([FromQuery] BaseId input)
    {
        var importantList = await _device.AsQueryable().Where(w => w.DeviceId == input.Id).Includes(w => w.DeviceVariable)
            .ToListAsync();
        return importantList.Select(s => new
        {
            s.DeviceVariable?.Id,
            s.DeviceVariable?.Identifier,
            s.DeviceVariable?.Name,
        });
    }

    /// <summary>
    ///     设置为关键参数
    /// </summary>
    /// <returns></returns>
    [HttpPost("/deviceVariable/important/set")]
    public async Task ImportantSet(ImportantSetInput input)
    {
        await _device.InsertAsync(new ImportantVariable
        {
            DeviceId = input.DeviceId,
            DeviceVariableId = input.DeviceVariableId,
            Id = YitIdHelper.NextId()
        });
    }
    
    /// <summary>
    ///     取消设置关键参数
    /// </summary>
    /// <returns></returns>
    [HttpPost("/deviceVariable/important/cancel")]
    public async Task ImportantCancel(ImportantSetInput input)
    {
        await _device.DeleteAsync(w => w.DeviceId == input.DeviceId && w.DeviceVariableId == input.DeviceVariableId);
    }
}