using Feng.IotGateway.Core.Base;
using Feng.IotGateway.Core.Const;
using Feng.IotGateway.Core.Entity;
using Feng.IotGateway.Core.Service.Config;
using Feng.IotGateway.Core.Util;
using IotGatewayTerminal.Application.Dto;
using Yitter.IdGenerator;

namespace IotGatewayTerminal.Application.Service;

/// <summary>
///     系统设置
/// </summary>
[ApiDescriptionSettings(Order = 10, GroupName = "系统设置")]
public class SystemSettingService : IDynamicApiController, ITransient
{
    private readonly SysConfigService _sysConfigService;

    public SystemSettingService(SysConfigService sysConfigService)
    {
        _sysConfigService = sysConfigService;
    }

    /// <summary>
    ///     默认首页
    /// </summary>
    /// <returns></returns>
    [HttpGet("/system/get/index")]
    public async Task<SysConfig> GetIndexPage()
    {
        return await _sysConfigService.GetDetail(new BaseId {Id = 1300000000211});
    }

    /// <summary>
    ///     默认首页设置
    /// </summary>
    /// <returns></returns>
    [HttpPost("/system/set/index")]
    public async Task SetIndexPage(SysConfig input)
    {
        await _sysConfigService.UpdateConfig(input);
    }

    /// <summary>
    ///     外部链接地址
    /// </summary>
    /// <returns></returns>
    [HttpGet("/system/get/externalLink")]
    public async Task<SysConfig> GetExternalLink()
    {
        return await _sysConfigService.GetDetail(new BaseId { Id = 1300000000212 });
    }

    /// <summary>
    ///     修改外部链接地址
    /// </summary>
    /// <returns></returns>
    [HttpPost("/system/set/externalLink")]
    public async Task SetExternalLink(SysConfig input)
    {
        await _sysConfigService.UpdateConfig(input);
    }

    /// <summary>
    ///     解锁
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/system/unlock")]
    public async Task UnLock(UnLockInput input)
    {
        var sysUnLockPassword = await _sysConfigService.GetConfigValue<string>(ConfigConst.SysUnLockPassword);
        if (sysUnLockPassword != input.Password)
            throw Oops.Oh("密码错误");
    }

    /// <summary>
    ///     版本
    /// </summary>
    /// <returns></returns>
    [HttpGet("/system/version")]
    public async Task<dynamic> GetVersion()
    {
        return new
        {
            System = new
            {
                MachineUtil.Version,
                UpdateTime = "2024-09-13 10:00:00",
                Sn = await GetSn()
            }
        };
    }

    /// <summary>
    ///     获取Sn码
    /// </summary>
    /// <returns></returns>
    public static async Task<string> GetSn()
    {
        string sn;
        if (!File.Exists(GatewayFilePath.SnPath))
        {
            sn = YitIdHelper.NextId().ToString();
            await File.WriteAllTextAsync(GatewayFilePath.SnPath, $"{sn}");
            return sn;
        }

        sn = await File.ReadAllTextAsync(GatewayFilePath.SnPath);
        return sn;
    }
}