<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <DocumentationFile>bin\Debug\IotGatewayTerminal.Application.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
      <DocumentationFile>bin\Release\IotGatewayTerminal.Application.xml</DocumentationFile>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\IotGateway.EdgeDevice\IotGateway.EdgeDevice.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="IotGatewayTerminal.Application.csproj.DotSettings" />
    </ItemGroup>

</Project>
