namespace IotGateway.EdgeDevice.DeviceConfigServer;

/// <summary>
///     设备配置信息
/// </summary>
[ApiDescriptionSettings("设备管理")]
public class DeviceConfigService : ITransient, IDynamicApiController
{
    private readonly IEventPublisher _eventPublisher;
    private readonly SqlSugarRepository<DeviceConfig> _deviceConfig;
    private readonly SqlSugarRepository<Device> _device;


    public DeviceConfigService(IEventPublisher eventPublisher, SqlSugarRepository<DeviceConfig> deviceConfig, SqlSugarRepository<Device> device)
    {
        _eventPublisher = eventPublisher;
        _deviceConfig = deviceConfig;
        _device = device;
    }

    /// <summary>
    ///     修改设备配置信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/deviceConfig/update")]
    public async Task<bool> Update(DeviceConfigUpdateInput input)
    {
        var deviceConfig = await _deviceConfig.GetFirstAsync(f => f.Id == input.Id);
        if (deviceConfig == null)
            throw Oops.Oh(ErrorCode.D1002);
        if (!await _device.IsAnyAsync(a => a.Id == deviceConfig.DeviceId))
            throw Oops.Oh("设备已被删除！");

        deviceConfig.Value = input.Value;
        await _deviceConfig.CopyNew().UpdateAsync(deviceConfig);

        var device = await _device.AsQueryable().AsNavQueryable().Where(w => w.Id == deviceConfig.DeviceId)
            .Includes(w => w.DeviceConfigs)
            .FirstAsync();
        if (device == null)
            throw Oops.Oh("设备已被删除！");
        // 修改采集线程
        await _eventPublisher.PublishAsync(EventConst.UpdateDeviceThread, input);
        return true;
    }
}