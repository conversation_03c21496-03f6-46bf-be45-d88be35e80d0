using Common.Enums;

namespace Feng.IotGateway.Application.Drivers.Dtos;

/// <summary>
///     下拉菜单项
/// </summary>
public class DriverSelectListItem
{
    /// <summary>
    ///     The value to display
    /// </summary>
    public string Text { get; set; }

    /// <summary>
    ///     The value to be submitted
    /// </summary>
    public object Value { get; set; }

    /// <summary>
    ///     是否显示
    /// </summary>
    public bool Show { get; set; }

    /// <summary>
    ///     Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     版本
    /// </summary>
    public string Version { get; set; }

    /// <summary>
    ///     所属协议名称
    /// </summary>
    public string TypeName { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Desc { get; set; }

    /// <summary>
    ///     协议类型
    /// </summary>
    [JsonIgnore]
    public DriverTypeEnum DriverType { get; set; }

    /// <summary>
    ///     协议类型名称
    /// </summary>
    public string DriverTypeName => EnumUtil.GetEnumDesc(DriverType);
}

/// <summary>
/// 协议支持方法
/// </summary>
public class DriverMethodsItem
{
    /// <summary>
    /// 标识
    /// </summary>
    public string Identifier { get; set; }
    
    /// <summary>
    ///     The value to display
    /// </summary>
    public string Text { get; set; }

    /// <summary>
    ///     The value to be submitted
    /// </summary>
    public object Value { get; set; }
    
    /// <summary>
    ///     描述
    /// </summary>
    public string Desc { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public TransPondDataTypeEnum TransitionType { get; set; }
    
    /// <summary>
    /// 是否过滤
    /// </summary>
    public bool Filter { get; set; }

    /// <summary>
    /// 过滤字段
    /// </summary>
    public string FilterField { get; set; }

    /// <summary>
    /// 过滤值
    /// </summary>
    public string FilterValue { get; set; }
}

/// <summary>
/// 协议支持的函数方法
/// </summary>
public class DriverFuncItem
{
    /// <summary>
    /// 
    /// </summary>
    public string Code { get; set; }
    
    /// <summary>
    ///     The value to display
    /// </summary>
    public string Text { get; set; }

    /// <summary>
    ///     The value to be submitted
    /// </summary>
    public object Value { get; set; }
    
    /// <summary>
    /// 输入框类型
    /// </summary>
    public string Type { get; set; } = "select";

    /// <summary>
    /// 是否必填
    /// </summary>
    public bool Required { get; set; } = true;
    
    /// <summary>
    /// 是否显示
    /// </summary>
    public bool Display { get; set; } = true;

    /// <summary>
    /// 字段显示条件表达式
    /// </summary>
    public string DisplayExpress { get; set; } = "";
    
    /// <summary>
    ///     默认值
    /// </summary>
    public string DefaultValue { get; set; }
}