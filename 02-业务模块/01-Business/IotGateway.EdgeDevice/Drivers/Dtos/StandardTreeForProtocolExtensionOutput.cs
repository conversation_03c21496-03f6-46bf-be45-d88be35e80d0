namespace Feng.IotGateway.Application.Drivers.Dtos;

/// <summary>
/// 采集协议-扩展标准树返回参数
/// </summary>
public class StandardTreeForProtocolExtensionOutput
{
    /// <summary>
    /// 标识符-不保证唯一性
    /// </summary>
    public string Id { get; set; }
    
    /// <summary>
    ///  显示内容
    /// </summary>
    public string Text { get; set; }

    /// <summary>
    /// 值
    /// </summary>
    public string Value { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<StandardTreeForProtocolExtensionOutput> Children { get; set; }

    /// <summary>
    /// 扩展字段
    /// </summary>
    public object ExtendField1 { get; set; }
    
    /// <summary>
    /// 扩展字段
    /// </summary>
    public object ExtendField2 { get; set; }
    
    /// <summary>
    /// 扩展字段
    /// </summary>
    public object ExtendField3 { get; set; }
}