namespace IotGateway.EdgeDevice;

/// <summary>
///     采集协议底层单例服务
///     This class represents a singleton service for the underlying collection protocol.
/// </summary>
public class DriverHostedService : IHostedService, IDisposable
{
#if NET6_0
    private readonly string _driverCncPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"drivers/cnc/net6.0/");
    private readonly string _driverPlcPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"drivers/plc/net6.0/");
    // 不常用协议
    private readonly string _driverOtherPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"drivers/other/net6.0/");
    private readonly string _driverImmPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"drivers/Imm/net6.0/");
        
#elif NET8_0
    private readonly string _driverCncPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"drivers/cnc/net8.0/");
    private readonly string _driverPlcPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"drivers/plc/net8.0/");
    // 不常用协议
    private readonly string _driverOtherPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"drivers/other/net8.0/");
    private readonly string _driverImmPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"drivers/Imm/net8.0/");
#endif

    public List<string> DriverFiles = new();
    public List<DriverInfo> DriverInfos = new();

    /// <summary>
    /// </summary>
    private readonly ISqlSugarClient _db;

    /// <summary>
    ///     协议支持方法
    /// </summary>
    public Dictionary<string, List<DriverMethodsItem>> driverMethods = new();

    /// <summary>
    ///     协议函数
    /// </summary>
    public Dictionary<string, List<DriverFuncItem>> driverFunc = new();

    /// <summary>
    ///     协议配置
    /// </summary>
    public Dictionary<string, List<DeviceConfig>> driverConfig = new();

    private readonly SendMessageService _socket;

    private readonly SemaphoreSlim _loadLock = new(1, 1);
    private bool _isLoaded;

    public DriverHostedService(SendMessageService socket, ISqlSugarClient db)
    {
        _socket = socket;
        _db = db;
    }

    /// <summary>
    ///     添加设备增加默认点位值
    ///     This method adds default point values when a device is added
    /// </summary>
    /// <param name="device"></param>
    /// <param name="driver"></param>
    [NonAction]
    public void AddConfigs(Device device, Feng.IotGateway.Core.Entity.Driver driver)
    {
        device.DeviceConfigs ??= new List<DeviceConfig>();
        var driverInfo = DriverInfos.FirstOrDefault(x => x.Type.FullName == driver.AssembleName);
        if (driverInfo == null)
            throw Oops.Oh("暂时不支持该协议！");
        Type[] types = { typeof(DriverInfoDto) };
        object[] param =
        {
            new DriverInfoDto { Socket = _socket, DeviceId = device.Id, DeviceName = device.DeviceName, ConnectType = driver.ConnectType }
        };

        var constructor = driverInfo.Type.GetConstructor(types);
        if (constructor == null)
            throw Oops.Oh("该协议版本暂不支持！");
        var iObj = constructor.Invoke(param) as IDriver;
        // 生成设备连接配置
        CreateDeviceConfig(device, driverInfo, iObj);
    }

    /// <summary>
    ///     生成设备连接配置
    ///     This method creates device connection configurations.
    /// </summary>
    public void CreateDeviceConfig(Device device, DriverInfo driverInfo, IDriver iObj)
    {
        foreach (var property in driverInfo.Type.GetProperties())
        {
            var config = property.GetCustomAttribute<ConfigParameterAttribute>();
            if (config == null || (!config.Display && config.DisplayExpress.IsNullOrEmpty())) continue;

            var deviceConfig = device.DeviceConfigs.FirstOrDefault(x => x.DeviceConfigName == property.Name);
            if (deviceConfig == null)
            {
                var dapConfig = new DeviceConfig
                {
                    Id = YitIdHelper.NextId(),
                    DeviceId = device.Id,
                    DeviceConfigName = property.Name,
                    Description = config.Description,
                    GroupName = config.GroupName,
                    Remark = config.Remark,
                    IsRequired = config.Required,
                    Value = property.GetValue(iObj)?.ToString() ?? string.Empty,
                    Display = config.Display,
                    DisplayExpress = config.DisplayExpress,
                    Type = config.Type,
                    Order = config.Order
                };

                if (property.PropertyType.BaseType == typeof(Enum))
                {
                    var fields = property.PropertyType.GetFields(BindingFlags.Static | BindingFlags.Public);
                    var enumInfos = fields.ToDictionary(f => f.GetCustomAttribute<DescriptionAttribute>()?.Description ?? f.Name, f => (int)f.GetValue(null)!);
                    if (property.PropertyType == typeof(SerialNumberEnum) && CurrentSerialNumber.TryGetValue(MachineUtil.CurrentSystemEnvironment, out var keysToFilter))
                    {
                        enumInfos = enumInfos
                            .Where(kvp => keysToFilter.Value.Contains(kvp.Key))
                            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                        // 覆盖描述
                        dapConfig.Remark = keysToFilter.Remark;
                        // dapConfig.Value = ((int) keysToFilter.SerialNumber).ToString();
                        // 根据实际
                        var enumIntValue = Convert.ToInt32(property.GetValue(iObj));
                        dapConfig.Value = enumInfos.Values.Contains(enumIntValue) ? enumIntValue.ToString() : ((int)keysToFilter.SerialNumber).ToString();
                    }
                    else
                    {
                        dapConfig.Value = Convert.ToInt32(property.GetValue(iObj)).ToString();
                    }

                    dapConfig.EnumInfo = JSON.Serialize(enumInfos);
                }

                device.DeviceConfigs.Add(dapConfig);
            }
            else
            {
                // 调整配置
                deviceConfig.Description = config.Description;
                deviceConfig.GroupName = config.GroupName;
                deviceConfig.Remark = config.Remark;
                deviceConfig.IsRequired = config.Required;
                deviceConfig.Display = config.Display;
                deviceConfig.DisplayExpress = config.DisplayExpress;

                if (property.PropertyType.BaseType == typeof(Enum))
                {
                    var fields = property.PropertyType.GetFields(BindingFlags.Static | BindingFlags.Public);
                    var enumInfos = fields.ToDictionary(f => f.GetCustomAttribute<DescriptionAttribute>()?.Description ?? f.Name, f => (int)f.GetValue(null)!);
                    if (property.PropertyType == typeof(SerialNumberEnum) && CurrentSerialNumber.TryGetValue(MachineUtil.CurrentSystemEnvironment, out var keysToFilter))
                    {
                        enumInfos = enumInfos
                            .Where(kvp => keysToFilter.Value.Contains(kvp.Key))
                            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                        deviceConfig.Remark = keysToFilter.Remark;
                    }

                    deviceConfig.EnumInfo = JSON.Serialize(enumInfos);
                }

                var index = device.DeviceConfigs.FindIndex(x => x.Id == deviceConfig.Id);
                if (index != -1)
                    device.DeviceConfigs[index] = deviceConfig;
            }
        }
    }

    /// <summary>
    ///     不同型号硬件-所对应的串口
    ///     This dictionary maps different hardware models to their corresponding serial ports.
    /// </summary>
    public Dictionary<CurrentSystemEnvironmentEnum, CurrentSerialNumberModel> CurrentSerialNumber = new()
    {
        {
            CurrentSystemEnvironmentEnum.Px30,
            new CurrentSerialNumberModel
            {
                Value = new List<string> { "/dev/ttyS1", "/dev/ttyS3", "/dev/ttyS5", "/dev/ttyS0" },
                Remark = "ttyS1：TX1/RX1(232) A1/B1(485) \nttyS3：TX2/RX2(232) A2/B2(485) \nttyS5：A3/B3(485) \nttyS0：A4/B4(485)",
                SerialNumber = SerialNumberEnum.ttyS1
            }
        },
        {
            CurrentSystemEnvironmentEnum.FengEdge150,
            new CurrentSerialNumberModel
            {
                Value = new List<string> { "/dev/ttyS1", "/dev/ttyS2", "/dev/ttyS3", "/dev/ttyS4" },
                Remark = "ttyS1：T1/R1(232) \nttyS2：T0/R0(232) \nttyS3：A0/B0(485) \nttyS4：A1/B1(485)",
                SerialNumber = SerialNumberEnum.ttyS1
            }
        },
        {
            CurrentSystemEnvironmentEnum.FengEdge200,
            new CurrentSerialNumberModel
            {
                Value = new List<string> { "/dev/ttyS1", "/dev/ttyS3", "/dev/ttyS5", "/dev/ttyS0" },
                Remark = "ttyS1：TX1/RX1(232) A1/B1(485) \nttyS3：TX2/RX2(232) A2/B2(485) \nttyS5：A3/B3(485) \nttyS0：A4/B4(485)",
                SerialNumber = SerialNumberEnum.ttyS1
            }
        },
        {
            CurrentSystemEnvironmentEnum.FengEdge2000,
            new CurrentSerialNumberModel
            {
                Value = new List<string> { "/dev/ttyS3", "/dev/ttyS4", "/dev/ttyS0", "/dev/ttyS9" }, Remark = "ttyS3：A1/B1(485) \nttyS4：A2/B2(485) \nttyS0：RX1/TX1(232) \nttyS9：RX2/TX2(232)",
                SerialNumber = SerialNumberEnum.ttyS3
            }
        },

        {
            CurrentSystemEnvironmentEnum.Gt675X,
            new CurrentSerialNumberModel
            {
                Value = new List<string> { "/dev/ttyS1", "/dev/ttyS2", "/dev/ttyS3", "/dev/ttyS4" },
                Remark = "ttyS1：A1/B1(485) \nttyS2：A2/B2(485) \nttyS3：A1/B1(485) TX/RX(232) \nttyS4：A1/B1(485) TX/RX(232)",
                SerialNumber = SerialNumberEnum.ttyS1
            }
        },
        {
            CurrentSystemEnvironmentEnum.Wr610,
            new CurrentSerialNumberModel
            {
                Value = new List<string> { "/dev/ttyS1", "/dev/ttyS3", "/dev/ttyS5", "/dev/ttyS0" },
                Remark = "ttyS1：TX1/RX1(232) A1/B1(485) \nttyS3：TX2/RX2(232) A2/B2(485) \nttyS5：A3/B3(485) \nttyS0：A4/B4(485)",
                SerialNumber = SerialNumberEnum.ttyS1
            }
        },
        {
            CurrentSystemEnvironmentEnum.Hw356X,
            new CurrentSerialNumberModel
            {
                Value = new List<string> { "/dev/ttyS0", "/dev/ttyS3", "/dev/ttyS4", "/dev/ttyS5", "/dev/ttyS7", "/dev/ttyS8", "/dev/ttyS9" },
                Remark = "ttyS0：A0/B0(485/232) \nttyS3：A3/B3(485/232) \nttyS4：A4/B4(485/232) \nttyS5：A5/B5(485/232) \nttyS7：A7/B7(485/232) \nttyS8：A8/B8(485/232) \nttyS9：A9/B9(485/232)",
                SerialNumber = SerialNumberEnum.ttyS0
            }
        }
    };

    private readonly List<string> _ignoreMethods = new() { "Parse", "BatchRead", "Init", "Debug", "FileWrite", "FileRead", "FileRouteInfo", "WriteAsync", "Clear", "FileDel", "SetCurrentProgram" };

    /// <summary>
    ///     初始化全部驱动加载
    ///     This method initializes all driver loads.
    /// </summary>
    /// <exception cref="AppFriendlyException"></exception>
    [NonAction]
    private async Task LoadAllDrivers()
    {
        if (_isLoaded) return;

        await _loadLock.WaitAsync();
        try
        {
            if (_isLoaded) return;

            // 记录开始时间
            var startTime = DateTime.Now;

            List<Feng.IotGateway.Core.Entity.Driver> driverList;
            List<SysDictData> driverDicList;

            // 异步加载数据库数据
            Console.WriteLine("┌──────────────────────────────────────────────────┐");
            Console.WriteLine("│            正在从数据库加载驱动配置...          │");
            Console.WriteLine("└──────────────────────────────────────────────────┘");

            Task<object[]> dbTask = Task.WhenAll(
                Task.Run(() => _db.CopyNew().Queryable<Feng.IotGateway.Core.Entity.Driver>().ToList() as object),
                Task.Run(() => _db.CopyNew().Queryable<SysDictData>().ToList() as object)
            );

            try
            {
                object[] results = await dbTask;
                driverList = (List<Feng.IotGateway.Core.Entity.Driver>)results[0];
                driverDicList = ((List<SysDictData>)results[1]).Where(w => driverList.Select(s => s.Id).Contains(w.DictTypeId)).ToList();
            }
            catch (Exception e)
            {
                Log.Error("加载驱动数据失败: " + e.Message);
                return;
            }

            Console.WriteLine("┌──────────────────────────────────────────────────┐");
            Console.WriteLine("│              开始扫描驱动插件                    │");
            Console.WriteLine("└──────────────────────────────────────────────────┘");

            // 并行加载驱动文件
            var loadTasks = DriverFiles.Select(file =>
            {
                return Task.Run(() =>
                {
                    try
                    {
                        LoadDriverFile(file, driverList, driverDicList);
                    }
                    catch (Exception e)
                    {
                        Log.Error($"加载驱动文件失败 {file}: {e.Message}");
                    }
                });
            });

            await Task.WhenAll(loadTasks);

            _isLoaded = true;

            // 计算总耗时
            var timeSpan = DateTime.Now - startTime;

            Console.WriteLine("┌──────────────────────────────────────────────────┐");
            Console.WriteLine($"│     驱动插件加载完成, 共加载 {DriverInfos.Count} 个驱动        │");
            Console.WriteLine($"│     总耗时: {timeSpan.TotalSeconds:F2} 秒                      │");
            Console.WriteLine("└──────────────────────────────────────────────────┘");
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"驱动加载失败,一般是驱动项目引用的nuget或dll没有复制到驱动文件夹,Error【{ex.Message}】");
        }
        finally
        {
            _loadLock.Release();
        }
    }

    /// <summary>
    /// 加载单个驱动文件
    /// </summary>
    /// <param name="file">驱动文件路径</param>
    /// <param name="driverList">驱动列表</param>
    /// <param name="driverDicList">驱动字典列表</param>
    private void LoadDriverFile(string file, List<Feng.IotGateway.Core.Entity.Driver> driverList, List<SysDictData> driverDicList)
    {
        // 记录单个驱动加载开始时间
        var startTime = DateTime.Now;

        var dll = Assembly.LoadFrom(file);
        var getTypes = dll.GetTypes().Where(x => typeof(IDriver).IsAssignableFrom(x) && x.IsClass).ToList();

        foreach (var type in getTypes)
        {
            try
            {
                var driverInfo = new DriverInfo
                {
                    FileName = Path.GetFileName(file),
                    Type = type
                };

                LoadDriverMethods(driverInfo);
                LoadDriverConfigs(driverInfo, driverList);
                LoadDriverFunctions(driverInfo, driverList, driverDicList);

                lock (DriverInfos)
                {
                    DriverInfos.Add(driverInfo);
                }

                var loadTime = DateTime.Now - startTime;
                Console.WriteLine($"  ➤ 加载驱动: {driverInfo.FileName} (耗时: {loadTime.TotalMilliseconds:F0}ms)");
            }
            catch (Exception e)
            {
                Log.Error($"驱动加载出错,驱动名称:{type.FullName}---:" + e.Message + $"{e.StackTrace}");
            }
        }
    }

    /// <summary>
    ///     初始化协议
    ///     This method initializes the protocol
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            // 异步加载驱动文件列表
            await Task.Run(() =>
            {
                // 创建字典存储文件名和对应的文件信息（路径和最后修改时间）
                Dictionary<string, (string fullPath, DateTime lastModified)> driverFileDict = new();

                // 扫描所有驱动目录
                ScanDirectoryForDrivers(_driverCncPath, driverFileDict);
                ScanDirectoryForDrivers(_driverPlcPath, driverFileDict);
                ScanDirectoryForDrivers(_driverOtherPath, driverFileDict);
                ScanDirectoryForDrivers(_driverImmPath, driverFileDict);

                // 将过滤后的文件添加到DriverFiles列表
                DriverFiles.Clear();
                DriverFiles.AddRange(driverFileDict.Values.Select(v => v.fullPath));
            }, cancellationToken);

            // 异步加载驱动
            _ = Task.Run(async () =>
            {
                await LoadAllDrivers();
                Log.Information("驱动加载完成");
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            Log.Error($"驱动加载失败:{ex.Message}");
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>Task</returns>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        try
        {
            // 清理资源
            DriverFiles.Clear();
            DriverInfos.Clear();
            driverMethods.Clear();
            driverFunc.Clear();
            driverConfig.Clear();
            _isLoaded = false;

            Log.Information("驱动服务已停止");
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            Log.Error($"驱动服务停止时发生错误: {ex.Message}");
            return Task.CompletedTask;
        }
    }

    public void Dispose()
    {
        _db?.Dispose();
    }

    /// <summary>
    /// 加载驱动方法
    /// </summary>
    private void LoadDriverMethods(DriverInfo driverInfo)
    {
        var methods = driverInfo.Type.GetMethods()
            .Where(x => x.GetCustomAttribute(typeof(MethodAttribute)) != null)
            .ToList();

        var driverMethodItems = new List<DriverMethodsItem>();

        foreach (var method in methods)
        {
            var attribute = method.CustomAttributes.Count() == 1
                ? method.CustomAttributes.FirstOrDefault()?.ConstructorArguments
                : method.CustomAttributes.ToList()[method.CustomAttributes.Count() - 1].ConstructorArguments;

            if (attribute == null) continue;

            var desc = (string)attribute[3].Value;
            var text = (string)attribute[4].Value;

            if (_ignoreMethods.Contains(text)) continue;

            var driverMethodsItem = new DriverMethodsItem
            {
                Text = text ?? method.Name,
                Value = method.Name,
                Desc = desc,
                Identifier = (string)attribute[0].Value!,
                TransitionType = (TransPondDataTypeEnum)attribute[1].Value!
            };

            var value = attribute[5].Value;
            var filter = value != null && (bool)value;
            if (filter)
            {
                driverMethodsItem.FilterField = (string)attribute[6].Value;
                driverMethodsItem.FilterValue = (string)attribute[7].Value;
            }

            driverMethodItems.Add(driverMethodsItem);
        }

        lock (driverMethods)
        {
            driverMethods[driverInfo.Type.FullName!] = driverMethodItems;
        }
    }

    /// <summary>
    /// 加载驱动配置
    /// </summary>
    private void LoadDriverConfigs(DriverInfo driverInfo, List<Feng.IotGateway.Core.Entity.Driver> driverList)
    {
        var driver = driverList.FirstOrDefault(f => f.AssembleName == driverInfo.Type.FullName);
        if (driver == null) return;

        Type[] types = { typeof(DriverInfoDto) };
        var constructor = driverInfo.Type.GetConstructor(types);
        if (constructor == null) return;

        object[] param =
        {
            new DriverInfoDto
            {
                Socket = _socket,
                DeviceId = 0,
                DeviceName = "",
                ConnectType = driver.ConnectType
            }
        };

        var iObj = constructor.Invoke(param) as IDriver;
        if (iObj == null) return;

        var driverConfigs = new List<DeviceConfig>();

        foreach (var property in driverInfo.Type.GetProperties())
        {
            var config = property.GetCustomAttribute<ConfigParameterAttribute>();
            if (config == null || (!config.Display && config.DisplayExpress.IsNullOrEmpty())) continue;

            var dapConfig = CreateDeviceConfigFromProperty(property, config, iObj);
            driverConfigs.Add(dapConfig);
        }

        lock (driverConfig)
        {
            driverConfig[driverInfo.Type.FullName!] = driverConfigs;
        }
    }

    /// <summary>
    /// 创建设备配置
    /// </summary>
    private DeviceConfig CreateDeviceConfigFromProperty(PropertyInfo property, ConfigParameterAttribute config, IDriver iObj)
    {
        var dapConfig = new DeviceConfig
        {
            Id = YitIdHelper.NextId(),
            DeviceConfigName = property.Name,
            Description = config.Description,
            GroupName = config.GroupName,
            Remark = config.Remark,
            IsRequired = config.Required,
            Value = property.GetValue(iObj)?.ToString() ?? string.Empty,
            Display = config.Display,
            DisplayExpress = config.DisplayExpress,
            Type = config.Type,
            Order = config.Order
        };

        if (property.PropertyType.BaseType == typeof(Enum))
        {
            ConfigureEnumProperty(property, iObj, dapConfig);
        }

        return dapConfig;
    }

    /// <summary>
    /// 配置枚举属性
    /// </summary>
    private void ConfigureEnumProperty(PropertyInfo property, IDriver iObj, DeviceConfig dapConfig)
    {
        var fields = property.PropertyType.GetFields(BindingFlags.Static | BindingFlags.Public);
        var enumInfos = fields.ToDictionary(
            f => f.GetCustomAttribute<DescriptionAttribute>()?.Description ?? f.Name,
            f => (int)f.GetValue(null)!
        );

        if (property.PropertyType == typeof(SerialNumberEnum) &&
            CurrentSerialNumber.TryGetValue(MachineUtil.CurrentSystemEnvironment, out var keysToFilter))
        {
            enumInfos = enumInfos
                .Where(kvp => keysToFilter.Value.Contains(kvp.Key))
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            dapConfig.Remark = keysToFilter.Remark;
            var enumIntValue = Convert.ToInt32(property.GetValue(iObj));
            dapConfig.Value = enumInfos.Values.Contains(enumIntValue)
                ? enumIntValue.ToString()
                : ((int)keysToFilter.SerialNumber).ToString();
        }
        else
        {
            dapConfig.Value = Convert.ToInt32(property.GetValue(iObj)).ToString();
        }

        dapConfig.EnumInfo = JSON.Serialize(enumInfos);
    }

    /// <summary>
    /// 加载驱动功能
    /// </summary>
    private void LoadDriverFunctions(DriverInfo driverInfo, List<Feng.IotGateway.Core.Entity.Driver> driverList, List<SysDictData> driverDicList)
    {
        var driver = driverList.FirstOrDefault(f => f.AssembleName == driverInfo.Type.FullName);
        if (driver == null) return;

        var thisDriverDicList = driverDicList.Where(w => w.DictTypeId == driver.Id).ToList();
        var driverFuncList = thisDriverDicList.Select(driverDic => new DriverFuncItem
        {
            Value = driverDic.Value,
            Required = driverDic.Required,
            DisplayExpress = driverDic.DisplayExpress,
            Type = driverDic.Type,
            Code = driverDic.Code,
            DefaultValue = driverDic.DefaultValue,
            Display = driverDic.Display,
            Text = driverDic.Name
        }).ToList();

        lock (driverFunc)
        {
            driverFunc[driverInfo.Type.FullName!] = driverFuncList;
        }
    }

    /// <summary>
    /// 扫描目录中的驱动文件并处理重复项
    /// </summary>
    /// <param name="directoryPath">要扫描的目录路径</param>
    /// <param name="driverFileDict">存储驱动文件信息的字典</param>
    private void ScanDirectoryForDrivers(string directoryPath, Dictionary<string, (string fullPath, DateTime lastModified)> driverFileDict)
    {
        if (!Directory.Exists(directoryPath))
            return;

        var dllFiles = Directory.GetFiles(directoryPath).Where(x => Path.GetExtension(x) == ".dll");

        foreach (var file in dllFiles)
        {
            string fileName = Path.GetFileName(file);
            DateTime lastModified = File.GetLastWriteTime(file);

            // 如果文件名已存在，比较时间戳，保留较新的文件
            if (driverFileDict.TryGetValue(fileName, out var existingFile))
            {
                if (lastModified > existingFile.lastModified)
                {
                    // 发现更新的版本，记录日志
                    driverFileDict[fileName] = (file, lastModified);
                }
            }
            else
            {
                // 添加新文件
                driverFileDict.Add(fileName, (file, lastModified));
            }
        }
    }
}