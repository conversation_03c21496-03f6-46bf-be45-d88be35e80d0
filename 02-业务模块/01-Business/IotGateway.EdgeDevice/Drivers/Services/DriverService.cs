using Feng.Common.OpcUaHelper;
using Feng.IotGateway.Core.Service.Cache;
using Opc.Ua;

namespace IotGateway.EdgeDevice;

/// <summary>
///     采集协议
/// </summary>
[ApiDescriptionSettings("设备管理")]
public class DriverService : ITransient, IDynamicApiController
{
    /// <summary>
    ///     驱动
    /// </summary>
    private readonly SqlSugarRepository<Feng.IotGateway.Core.Entity.Driver> _driver;

    /// <summary>
    ///     驱动服务
    /// </summary>
    private readonly DriverHostedService _driverService;

    /// <summary>
    ///     缓存服务
    /// </summary>
    private readonly CacheService _cacheService;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="driverService"></param>
    /// <param name="driver"></param>
    /// <param name="cacheService"></param>
    public DriverService(DriverHostedService driverService, SqlSugarRepository<Feng.IotGateway.Core.Entity.Driver> driver, CacheService cacheService)
    {
        _driverService = driverService;
        _driver = driver;
        _cacheService = cacheService;
    }

    /// <summary>
    ///     采集协议
    /// </summary>
    /// <returns></returns>
    [HttpGet("/driver/select")]
    public async Task<List<DriverSelectListItem>> GetAllDrivers()
    {
        List<DriverSelectListItem> driverFilesComboSelect = new();
        var drivers = await _driver.AsQueryable().ToListAsync();
        if (_driverService.DriverFiles == null)
            return driverFilesComboSelect;
        foreach (var file in _driverService.DriverFiles)
        {
            var dll = Assembly.LoadFrom(file);
            // if (!dll.GetTypes().Any(x => typeof(IDriver).IsAssignableFrom(x) && x.IsClass)) continue;
            // 获取所有实现了IDriver接口的类
            foreach (var type in dll.GetTypes().Where(x => typeof(IDriver).IsAssignableFrom(x) && x.IsClass))
                try
                {
                    // 获取类名
                    var fileName = Path.GetFileName(file);
                    var item = new DriverSelectListItem { Text = fileName, Show = false, Id = 0 };
                    if (drivers.Any(x => x.FileName == Path.GetFileName(file)))
                    {
                        // 获取驱动
                        var driver = drivers.FirstOrDefault(f => f.FileName == Path.GetFileName(file));
                        if (driver == null)
                            continue;
                        item.Id = driver.Id; // 设置驱动Id
                        item.DriverType = driver.DriverType; // 设置驱动类型
                        item.Show = true; // 设置驱动是否显示
                        item.Text = driver.DriverName; // 设置驱动名称
                        var count = type.CustomAttributes.Count() - 1;
                        item.Version = type.CustomAttributes.ToList()[count].ConstructorArguments[1].ToString().Replace("\"", ""); // 设置驱动版本
                        item.TypeName = type.CustomAttributes.ToList()[count].ConstructorArguments[2].ToString().Replace("\"", ""); // 设置驱动类型名称
                    }
                    // 添加驱动
                    driverFilesComboSelect.Add(item);
                }
                catch (Exception e)
                {
                    Log.Error($"加载协议Error:【{e.Message}】");
                }
        }

        return driverFilesComboSelect;
    }

    /// <summary>
    ///     采集协议-扩展标准树
    /// </summary>
    /// <returns></returns>
    [HttpGet("/driver/ex/tree")]
    public async Task<List<StandardTreeForProtocolExtensionOutput>> StandardTreeForProtocolExtension([FromQuery] BaseId input)
    {
        List<StandardTreeForProtocolExtensionOutput> output = new();
        var device = await _driver.AsSugarClient().Queryable<Device>().Where(f => f.Id == input.Id)
            .Includes(w => w.Driver)
            .Includes(w => w.DeviceConfigs)
            .FirstAsync();
        if (device == null)
            return output;

        switch (device.Driver?.DriverName)
        {
            case "OpcUaClient":
                {
                    var url = device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "Uri")?.Value;
                    if (url == null)
                        return output;
                    try
                    {
                        var opcUa = new OpcUaClientHelper();
                        await opcUa.ConnectServer(url);
                        if (!opcUa.Connected)
                            return _cacheService.Get<List<StandardTreeForProtocolExtensionOutput>>("OpcUaClient:" + url);
                        var readNode = opcUa.BrowseNodeReference(ObjectIds.ObjectsFolder.ToString()).Select(
                            s => new StandardTreeForProtocolExtensionOutput
                            {
                                Id = s.BrowseName.Name + "_" + s.NodeId,
                                Value = s.NodeId.ToString(),
                                ExtendField1 = s.NodeClass,
                                Text = s.BrowseName.Name,
                                ExtendField2 = s.NodeClass == NodeClass.Variable
                                    ? opcUa.ReadNode(s.NodeId.ToString()).WrappedValue.TypeInfo.BuiltInType.ToString() ?? null
                                    : null
                            });
                        output = readNode.Where(w => w.Text != "Server").Select(treeNode => ChildNodes(opcUa, treeNode))
                            .Select(dummy => (StandardTreeForProtocolExtensionOutput)dummy).ToList();
                        _cacheService.Set("OpcUaClient:" + url, output);
                        opcUa.Disconnect();
                    }
                    catch (Exception e)
                    {
                        Log.Error("【采集协议-扩展标准树】:" + e.Message);
                        return _cacheService.Get<List<StandardTreeForProtocolExtensionOutput>>("OpcUaClient:" + url);
                    }
                }
                break;
        }

        return output;
    }

    /// <summary>
    ///     根据当前节点，加载子节点
    /// </summary>
    /// <param name="opcUa"></param>
    /// <param name="currTreeEntity">当前节点</param>
    private dynamic ChildNodes(OpcUaClientHelper opcUa, StandardTreeForProtocolExtensionOutput currTreeEntity)
    {
        // 节点是否还有下级
        var childNodes = opcUa.BrowseNodeReference(currTreeEntity.Value);
        if (childNodes.Count <= 0) return currTreeEntity;
        currTreeEntity.Children ??= new List<StandardTreeForProtocolExtensionOutput>();
        currTreeEntity.Children.AddRange(childNodes.Select(
            s => new StandardTreeForProtocolExtensionOutput
            {
                Id = s.BrowseName.Name + "_" + s.NodeId,
                Value = s.NodeId.ToString(),
                ExtendField1 = s.NodeClass,
                Text = s.BrowseName.Name,
                ExtendField2 = s.NodeClass == NodeClass.Variable
                    ? opcUa.ReadNode(s.NodeId.ToString()).WrappedValue.TypeInfo?.BuiltInType.ToString() ?? null
                    : null
            }));
        foreach (var treeEntity in currTreeEntity.Children) ChildNodes(opcUa, treeEntity);

        return currTreeEntity;
    }
}