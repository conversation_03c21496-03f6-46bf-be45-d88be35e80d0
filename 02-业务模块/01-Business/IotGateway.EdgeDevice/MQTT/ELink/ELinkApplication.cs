using IotGateway.Application.Entity;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.EdgeDevice;

/// <summary>
///     ELink平台
/// </summary>
public class ELinkApplication : ITransient
{
    /// <summary>
    ///     ELink下写属性
    /// </summary>
    /// <param name="e"></param>
    public async Task ReceiveWrite(MqttApplicationMessageReceivedEventArgs e)
    {
        try
        {
            var request = JSON.Deserialize<DeviceWriteRequest>(e.ApplicationMessage.ConvertPayloadToString());
            await MessageCenter.PublishAsync(string.Format(ConstMethod.DeviceWrite, request.DeviceName), request);
        }
        catch (Exception ex)
        {
            Log.Error($"ELink ReceiveWrite:Topic:{e.ApplicationMessage.Topic},Payload:{e.ApplicationMessage.ConvertPayloadToString()},error:{ex.Message}");
        }
    }

    /// <summary>
    ///     ELink同步时间
    /// </summary>
    /// <param name="payLoad">消息内容</param>
    public async Task ReceiveSysTime(string payLoad)
    {
        if (!MachineUtil.IsUnix())
            return;
        try
        {
            var value = JSON.Deserialize<ELinkTimeResp>(payLoad);
            //同步时间计算规则
            // 当前时间-设备发送时间 = 差异时间
            var diffTime = (DateTime.Now() - DateTime.ToTime(value.DeviceSendTime)).TotalMilliseconds;
            //平台返回时间 + 差异时间 = 需要设置的时间
            var addTime = DateTime.ToTime(value.HubSendTime).AddMilliseconds(diffTime);
            await ShellUtil.Bash($"date -s \"{addTime:yyyy-MM-dd HH:mm:ss}\" && hwclock -w --systohc ");

            Log.Information($"Elink同步时间成功:【{payLoad}】,设备发送时间 【{value.DeviceSendTime},{DateTime.ToTime(value.DeviceSendTime)}】, 平台发送时间 【{value.HubSendTime},{DateTime.ToTime(value.HubSendTime)}】 ");
        }
        catch (Exception ex)
        {
            Log.Information($"Elink同步时间失败:【{ex.Message}】,参数:【{payLoad}】");
        }
    }

    /// <summary>
    ///     发送数据
    /// </summary>
    /// <param name="input">发送消息内容</param>
    /// <param name="topic">发送Topic</param>
    /// <param name="client"></param>
    /// <returns></returns>
    public async Task<MqttClientPublishResult?> PublishAsync(string input, TransPondTopic topic, IMqttClient client)
    {
        if (!client.IsConnected)
        {
            return null;
        }
        try
        {
            var message = new MqttApplicationMessage
            {
                Topic = topic.Topic,
                PayloadSegment = Encoding.UTF8.GetBytes(input),
                Retain = false,
                QualityOfServiceLevel = topic.Qos
            };
            // 未设置超时时间
            if (topic.TimeOut == 0)
                return await client.PublishAsync(message);
            // 超时时间
            var ct = new CancellationTokenSource(TimeSpan.FromSeconds(topic.TimeOut)).Token;
            return await client.PublishAsync(message, ct);
        }
        catch (Exception e)
        {
            throw Oops.Bah($"发送超时:{e.Message}");
        }
    }

    /// <summary>
    ///     平台发送指令网关侧执行
    /// </summary>
    /// <param name="cmd">消息内容</param>
    public async Task Cmd(string cmd)
    {
        try
        {
            Log.Information($"【ELink-Cmd】 命令:【{cmd}】 ");
            if (!MachineUtil.IsUnix())
                await ShellUtil.Bash(cmd);
            else
                throw Oops.Oh("暂不支持在其他平台执行Cmd命令");
        }
        catch (Exception ex)
        {
            Log.Information($"【ELink-Cmd】 执行命令失败:【{ex.Message}】,命令:【{cmd}】");
        }
    }

    /// <summary>
    ///     设备指令
    /// </summary>
    /// <param name="cmdString">消息内容</param>
    public async Task DeviceCmd(string cmdString)
    {
        try
        {
            Log.Information($"【ELink-DeviceCmd】 设备命令:【{cmdString}】 ");
            var deviceCmd = JSON.Deserialize<ELinkCmdResp>(cmdString);
            // 执行指令
            var cmd = deviceCmd.Cmd;
            // 设备名称
            var deviceName = deviceCmd.DeviceName;
            // 设备重启
            if (cmd == "reboot")
            {
                Log.Information("【ELink-DeviceCmd】 reboot命令执行");
                await MessageCenter.PublishAsync(EventConst.CmdReStartDeviceThread, deviceName);
            }
        }
        catch (Exception ex)
        {
            Log.Information($"【ELink-DeviceCmd】 执行命令失败:【{ex.Message}】,命令:【{cmdString}】");
        }
    }
}