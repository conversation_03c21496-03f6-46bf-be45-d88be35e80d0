namespace IotGateway.Application;

/// <summary>
///     平台下发事件同步
/// </summary>
public class ELinkTimeResp
{
    /// <summary>
    ///     这个时间时设备发送的本地时间戳,精确到毫秒
    /// </summary>
    public long DeviceSendTime { get; set; }

    /// <summary>
    ///     平台收到的时间,精确到毫秒
    /// </summary>
    public long HubRecvTime { get; set; }

    /// <summary>
    ///     平台发送的时间,精确到毫秒。
    /// </summary>
    public long HubSendTime { get; set; }
}

/// <summary>
///     平台下发设备指令
/// </summary>
public class ELinkCmdResp
{
    /// <summary>
    ///     设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    ///     命令
    /// </summary>
    public string Cmd { get; set; }
}