using Common.Extension;
using IotGateway.Application.Entity;
using ValueType = FengIotMqttService.ProtoBufModels.ValueType;

namespace IotGateway.EdgeDevice;

/// <summary>
///     SuperOs 的回调方法
/// </summary>
public class SuperOsApplication : ITransient
{

    private readonly SendMessageService _send;

    public SuperOsApplication(SendMessageService send)
    {
        _send = send;
    }

    /// <summary>
    ///     SuperOs下写实体转换成标准下写实体
    /// </summary>
    public async Task ChangeToDeviceWriteRequest(ValueSequence input)
    {
        foreach (var thing in input.Values)
            try
            {
                // 根据规则切割出来采集设备名称
                var deviceName = thing.Name.Split("/")[1];
                var deviceRepo = App.GetService<SqlSugarRepository<Device>>();
                // 采集设备
                var device = await deviceRepo.AsQueryable().Where(w => w.DeviceName == deviceName)
                    .Includes(w => w.DeviceVariable)
                    .FirstAsync();
                if (device == null)
                {
                    Log.Error($"【SuperOS】下写数据,设备:【{deviceName}】不存在!");
                    continue;
                }

                // 根据规则切割出来属性名称
                var valueName = thing.Name.Split("/")[2];
                // 设备采集点
                var deviceVariable = device.DeviceVariable.FirstOrDefault(f => f.Identifier == valueName);
                if (deviceVariable == null)
                    continue;

                // 不同方式的数据来源
                switch (deviceVariable.ValueSource)
                {
                    case ValueSourceEnum.Get:
                        {
                            //写入设备属性
                            var resp = new DeviceWriteRequest
                            {
                                Params = new Dictionary<string, string>(),
                                DeviceName = deviceName
                            };

                            switch (deviceVariable.TransitionType)
                            {
                                case TransPondDataTypeEnum.Bool:
                                    resp.Params.Add(valueName, thing.Value.BoolVal.ToString());
                                    break;
                                case TransPondDataTypeEnum.Int:
                                    resp.Params.Add(valueName, thing.Value.IntVal.ToString());
                                    break;
                                case TransPondDataTypeEnum.Double:
                                    resp.Params.Add(valueName, thing.Value.DblVal.ToString(CultureInfo.InvariantCulture));
                                    break;
                                case TransPondDataTypeEnum.String:
                                    resp.Params.Add(valueName, thing.Value.StrVal);
                                    break;
                            }

                            //通过事件总线将消息发送出去
                            await MessageCenter.PublishAsync(string.Format(ConstMethod.DeviceWrite, resp.DeviceName), JSON.Serialize(resp));
                            break;
                        }
                    case ValueSourceEnum.Calculate:
                        {
                            //执行脚本属性
                            var resp = new DeviceWriteScriptRequest
                            {
                                Params = new Dictionary<string, string>(),
                                DeviceId = device.Id,
                                Script = deviceVariable.Script ?? ""
                            };

                            switch (deviceVariable.TransitionType)
                            {
                                case TransPondDataTypeEnum.Bool:
                                    resp.Params.Add(thing.Name, thing.Value.BoolVal.ToString());
                                    break;
                                case TransPondDataTypeEnum.Int:
                                    resp.Params.Add(thing.Name, thing.Value.IntVal.ToString());
                                    break;
                                case TransPondDataTypeEnum.Double:
                                    resp.Params.Add(thing.Name, thing.Value.DblVal.ToString(CultureInfo.InvariantCulture));
                                    break;
                                case TransPondDataTypeEnum.String:
                                    resp.Params.Add(thing.Name, thing.Value.StrVal);
                                    break;
                            }

                            await MessageCenter.PublishAsync(EventConst.WriteScriptVariable, resp);
                            break;
                        }
                    case ValueSourceEnum.Static:
                        //todo 静态属性
                        break;
                }
            }
            catch (Exception e)
            {
                Log.Error($"SuperOs操作下写数据,解析错误:【{e.Message}】");
            }
    }

    /// <summary>
    ///     设备属性上送
    /// </summary>
    /// <param name="transPondVariable"></param>
    /// <param name="client"></param>
    /// <param name="topic">发送Topic</param>
    public async Task Meta(List<MetaVariable> transPondVariable, IMqttClient client, TransPondTopic topic)
    {
        var metaTags = new MetaTagSequence();
        foreach (var variable in transPondVariable)
            try
            {
                metaTags.Tags.Add(new MetaTag
                {
                    Name = variable.Identifier,
                    Version = 1,
                    Description = variable.Description ?? "",
                    ShowName = variable.Name,
                    Type = TypeToSuperOsValueType(variable.TransitionType),
                    Unit = variable.Unit ?? ""
                });
            }
            catch (Exception e)
            {
                Log.Error($"【SupOS】 上报设备属性出错,【{e.Message}】");
            }

        //等待3s后在
        if (!client.IsConnected)
        {
            await TaskQueued.EnqueueAsync(async (_, _) => { await Meta(transPondVariable, client, topic); }, 3000);
            return;
        }

        try
        {
            //数据上送
            await client.PublishAsync(new MqttApplicationMessageBuilder()
                .WithTopic(topic.Topic)
                .WithPayload(metaTags.ToByteArray())
                .WithQualityOfServiceLevel(topic.Qos).Build());
            Log.Information($"【SupOS】 上报元数据共计：【{metaTags.Tags.Count}】条！");
        }
        catch (Exception e)
        {
            Log.Error("【SupOS】 上报元数据Error:" + e.Message);
        }
    }

    /// <summary>
    ///     设备属性上送 1.2版本
    /// </summary>
    /// <param name="transPondVariable">设备变量列表</param>
    /// <param name="client">MQTT客户端</param>
    /// <param name="topic">发送Topic</param>
    public async Task MetaNew(List<MetaVariable> transPondVariable, IMqttClient client, TransPondTopic topic)
    {
        try
        {
            Log.Information("【SupOS】 上报元数据 1.2版本");
            var metaTags = new MetaTagUpdateSequence() { Version = "2", Type = UpdateType.Full };
            var filteredVariables = transPondVariable;

            // 构建元数据标签
            foreach (var variable in filteredVariables)
            {
                try
                {
                    metaTags.Tags.Add(new MetaTag
                    {
                        Name = variable.Identifier,
                        Version = 1,
                        Description = variable.Description ?? "",
                        ShowName = variable.Name,
                        Type = TypeToSuperOsValueType(variable.TransitionType),
                        Unit = variable.Unit ?? ""
                    });
                }
                catch (Exception e)
                {
                    Log.Error($"【SupOS】 上报设备属性出错,【{e.Message}】");
                }
            }

            // 检查客户端连接状态
            if (!client.IsConnected)
            {
                await TaskQueued.EnqueueAsync(async (_, _) =>
                {
                    await Meta(transPondVariable, client, topic);
                }, 3000);
                return;
            }

            // 发送数据
            if (metaTags.Tags.Count != 0)
            {
                await client.PublishAsync(new MqttApplicationMessageBuilder()
                    .WithTopic(topic.Topic)
                    .WithPayload(metaTags.ToByteArray())
                    .WithQualityOfServiceLevel(topic.Qos).Build());
                Log.Information($"【SupOS】 上报元数据共计：【{metaTags.Tags.Count}】条！  ");
            }
            else
            {
                Log.Warning($"【SupOS】 没有找到匹配的元数据位号，请求的位号数量：【{transPondVariable.Count}】");
            }
        }
        catch (Exception e)
        {
            Log.Error($"【SupOS】 处理元数据通知出错：【{e.Message}】");
        }
    }


    /// <summary>
    ///     删除元数据
    /// </summary>
    /// <param name="transPondVariable">设备变量列表</param>
    /// <param name="client">MQTT客户端</param>
    /// <param name="topic">发送Topic</param>
    public async Task MetaDelete(List<MetaVariable> transPondVariable, IMqttClient client, TransPondTopic topic)
    {
        Log.Information("【SupOS】 上报元数据 1.2版本,删除元数据");
        var metaTags = new MetaTagUpdateSequence() { Version = "2", Type = UpdateType.Erase };
        var filteredVariables = transPondVariable;

        // 构建元数据标签
        foreach (var variable in filteredVariables)
        {
            try
            {
                metaTags.Tags.Add(new MetaTag
                {
                    Name = variable.Identifier,
                    Version = 1,
                    Description = variable.Description ?? "",
                    ShowName = variable.Name,
                    Type = TypeToSuperOsValueType(variable.TransitionType),
                    Unit = variable.Unit ?? ""
                });
            }
            catch (Exception e)
            {
                Log.Error($"【SupOS】 删除元数据出错,【{e.Message}】");
            }
        }
        // 发送数据
        if (metaTags.Tags.Count != 0)
        {
            await client.PublishAsync(new MqttApplicationMessageBuilder()
                .WithTopic(topic.Topic)
                .WithPayload(metaTags.ToByteArray())
                .WithQualityOfServiceLevel(topic.Qos).Build());
            Log.Information($"【SupOS】 删除元数据共计：【{metaTags.Tags.Count}】条！  ");
        }
    }
    /// <summary>
    ///     通用数据类型转SupOS支持类型
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    private static ValueType TypeToSuperOsValueType(TransPondDataTypeEnum type)
    {
        var valueType = type switch
        {
            TransPondDataTypeEnum.Bool => ValueType.Boolean,
            TransPondDataTypeEnum.Int => ValueType.Integer,
            TransPondDataTypeEnum.Double => ValueType.Double,
            TransPondDataTypeEnum.String => ValueType.String,
            TransPondDataTypeEnum.Bytes => ValueType.Bytes,
            _ => ValueType.Unknown
        };

        return valueType;
    }

    /// <summary>
    ///     发送数据到SupOS平台
    /// </summary>
    /// <param name="input">发送消息内容</param>
    /// <param name="topic">发送Topic</param>
    /// <param name="client">MQTT</param>
    /// <param name="transPond">转发配置</param>
    /// <returns></returns>
    public async Task<MqttClientPublishResult?> PublishSupOsAsync(VariableSendToPlatform input, TransPondTopic topic, IMqttClient client, TransPond transPond)
    {
        if (!client.IsConnected)
        {
            await _send.DeviceResp($"设备:【{input.DeviceName}】, MQTT状态判断是离线, 实际连接状态:【{client.IsConnected}】", transPond.Identifier, true);
            return null;
        }

        var value = new ValueSequence();
        foreach (var (key, item) in input.Params)
        {
            if (item.Value.IsNull()) continue;
            var namedValue = new NamedValue
            {
                Name = key,
                Value = new RtdValue { TimeStamp = Convert.ToInt64(item.Time), Quality = 0 }
            };
            try
            {
                switch (item.DataType)
                {
                    case TransPondDataTypeEnum.Bool:
                        namedValue.Value.BoolVal = Convert.ToBoolean(item.Value);
                        break;
                    case TransPondDataTypeEnum.Int:
                        namedValue.Value.IntVal = Convert.ToInt64(item.Value);
                        break;
                    case TransPondDataTypeEnum.Double:
                        namedValue.Value.DblVal = Convert.ToDouble(item.Value);
                        break;
                    case TransPondDataTypeEnum.String:
                    case TransPondDataTypeEnum.Bytes:
                    default:
                        namedValue.Value.StrVal = item.Value;
                        break;
                }

                value.Values.Add(namedValue);
            }
            catch (Exception ex)
            {
                await _send.DeviceResp($"【SupOS】解析数据:【{key}】 值:【{item.Value}】，转换类型:【{item.DataType}】 Error:【{ex.Message}】", transPond.Identifier, true);
            }
        }

        // 特殊情况，离线数据只有一个采集属性,并且转换失败,如果不认为成功就会无限循环的发送，所以如果已有一个的话就认为是成功的,直接删除垃圾数据
        if (!value.Values.Any())
            // return input.Params.Count > 1 ? null : new MqttClientPublishResult {ReasonCode = MqttClientPublishReasonCode.Success};
            return input.Params.Count > 1 ? null : new MqttClientPublishResult(null, MqttClientPublishReasonCode.Success, "", null);

        try
        {
            var message = new MqttApplicationMessage
            {
                Topic = topic.Topic,
                PayloadSegment = value.ToByteArray(),
                QualityOfServiceLevel = topic.Qos,
                Retain = false
            };
            // 未设置超时时间，直接发送
            if (topic.TimeOut == 0)
            {
                var pubSuss = await client.PublishAsync(message);
                if (pubSuss == null) Log.Information("【supOS】 发送数据到平台没有响应,返回的Null值");
                return pubSuss;
            }
            else
            {
                // 设置超时时间发送
                var ct = new CancellationTokenSource(TimeSpan.FromSeconds(topic.TimeOut)).Token;
                var pubSuss = await client.PublishAsync(message, ct);
                if (pubSuss == null) Log.Information("【supOS】 设置超时时间 发送数据到平台没有响应,返回的Null值");
                return pubSuss;
            }
        }
        catch (Exception e)
        {
            throw Oops.Bah($"【SupOS】 MQTT发送消息Error:{e.Message}");
        }
    }
}