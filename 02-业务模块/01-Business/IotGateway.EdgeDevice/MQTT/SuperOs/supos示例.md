```javascript
// 动态解析实时上报数据
var data = payload;
var output = {
  DeviceName: data.DeviceName,
  ParentTime: data.ParentTime,
  Params: {}
}
for (let key in data.Params) {
  // 默认格式 转发标识/设备名称/属性标识  例如：edge234/NS99/product
  var keySp = key.split('/');
  var newKey = key;
  if (keySp.length > 2) {
    newKey = keySp[0] + '_' + keySp[1] + '_' + keySp[2];
    output.Params[newKey] = data.Params[key];
  }
}
// 返回按标准格式返回即可
return output
```

```javascript
// 动态解析上报元数据
var data = payload;
var output = []
for (var i = 0; i < data.length; i++) {
  // 默认格式 转发标识/设备名称/属性标识  例如：edge234/NS99/product
  var keySp = data[i].Identifier.split('/');
  var newKey = data[i].Identifier;
  if (keySp.length > 2) {
    {
      newKey = keySp[0] + '_' + keySp[1] + '_' + keySp[2];
      data[i].Identifier = newKey;
    }
    output.push(data[i]);
  }
}
// 返回按标准格式返回即可
return output
```

```javascript
// 云端要求同步设备属性回复 和上面动态解析上报元数据保持一致即可
```


