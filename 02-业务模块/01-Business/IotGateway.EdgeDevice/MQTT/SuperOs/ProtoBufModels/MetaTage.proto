syntax = "proto3";

package Feng_Iot_MqttService.ProtoBufModels;

// 值类型枚举
enum ValueType {
  Unknown = 0;
  // 整数类型
  Integer = 1;
  // 浮点类型
  Double = 2;
  // 布尔类型
  Boolean = 3;
  // 字符串类型
  String = 4;
  // 字节流类型
  Bytes = 5;
}

// 元数据标签定义
message MetaTag {
  // 位号版本,位号信息发生变化时进行更新
  int32 version = 1;
  // 位号名称（字符开头,不能包含非法字符）
  string name = 2;
  // 位号可读名称 (任意字符,可选)
  string showName = 3;
  // 位号描述 (可选)
  string description = 4;
  // 位号值类型
  ValueType type = 5;
  // 位号单位 (可选)
  string unit = 6;
  // 位号量程值范围,如 0-100 (可选)
  string range = 7;
  // 位号默认值 (可选)
  RtdValue defaultValue = 8;
  // 位号存档置,值为 true 默认记存档 (可选)
  bool storage = 9;
  // 位号压缩设置 (可选)
  CompressSpec compress = 10;
}

// 设备属性列定义
message MetaTagSequence {
  string version = 2;
  repeated MetaTag tags = 1;
}

// 存档压缩定义
message CompressSpec {
  // 压缩设置启用标识 默认 false
  bool enable = 1;
  // 工程量参数
  float value = 2;
  // 最大上送周期 (s)
  int64 maxElapse = 3;
}

// 实时数据定义
message RtdValue {
  // UTC 毫秒时间戳
  int64 timeStamp = 1;
  // 质量码,0-标识正常值
  int64 quality = 2;
  
  oneof value {
    int64 intVal = 3;
    double dblVal = 4;
    bool boolVal = 5;
    string strVal = 6;
    bytes bytVal = 7;
  }
}

// 命名实时数据定义
message NamedValue {
  // 位号名称
  string name = 1;
  // 位号实时值
  RtdValue value = 2;
}

// 实时数据列定义
message ValueSequence {
  repeated NamedValue values = 1;
}

message ProtocolVersion //V1.2新增
{
  uint32 major = 1; //主版本号，表示重大变更或不兼容的更改。
  uint32 minor = 2; //次版本号，表示向后兼容的功能增强。
}

// 时间同步
message GatewayCoordinate {
  int64 localTimeStamp = 1;
  ProtocolVersion localVersion = 2; //网关使用的协议版本，V1.2新增字段
}

// 时间同步
message ServerCoordinate {
  int64 localTimeStamp = 1;
  int64 serverTimeStamp = 2;
  ProtocolVersion serverVersion =3; //服务端支持的最高协议版本，V1.2新增字段
}

// 事件数据
message RtdEvent {
  string topic = 1;
  bytes payload = 2;
}

// 网关主动推送元数据
message MetaTagUpdateSequence
{
  UpdateType type = 1;  //网关推送位号元数据的消息类型：1. 新增，2.删除，3.局部更新，4.全量更新
  string version = 2;
  repeated MetaTag tags = 3; 
}

// 服务端要求刷新网关位号数据
message MetaNotifySequence
{
  UpdateType type = 1; //期望网关响应时推送的消息类型。1. 新增，2.删除，3.局部更新，4.全量更新
  string version = 2;
  repeated string tagnames = 3; //当网关推送的实时数据或业务方查询数据时，服务端发现未存在的位号清单。网关接收此清单时，务必推送此清单中的所有位号元数据
}

enum UpdateType
{
  None = 0;     // 位置，忽略本次推送
  New = 1;      // 新增
  Erase = 2;    //删除
  Update = 3;   //局部更新
  Full = 4;     //全量更新
}
