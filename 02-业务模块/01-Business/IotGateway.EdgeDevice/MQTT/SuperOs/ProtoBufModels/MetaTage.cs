// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: MetaTage.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace FengIotMqttService.ProtoBufModels {

  /// <summary>Holder for reflection information generated from MetaTage.proto</summary>
  public static partial class MetaTageReflection {

    #region Descriptor
    /// <summary>Files descriptor for MetaTage.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static MetaTageReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cg5NZXRhVGFnZS5wcm90bxIjRmVuZ19Jb3RfTXF0dFNlcnZpY2UuUHJvdG9C",
            "dWZNb2RlbHMixQIKB01ldGFUYWcSDwoHdmVyc2lvbhgBIAEoBRIMCgRuYW1l",
            "GAIgASgJEhAKCHNob3dOYW1lGAMgASgJEhMKC2Rlc2NyaXB0aW9uGAQgASgJ",
            "EjwKBHR5cGUYBSABKA4yLi5GZW5nX0lvdF9NcXR0U2VydmljZS5Qcm90b0J1",
            "Zk1vZGVscy5WYWx1ZVR5cGUSDAoEdW5pdBgGIAEoCRINCgVyYW5nZRgHIAEo",
            "CRJDCgxkZWZhdWx0VmFsdWUYCCABKAsyLS5GZW5nX0lvdF9NcXR0U2Vydmlj",
            "ZS5Qcm90b0J1Zk1vZGVscy5SdGRWYWx1ZRIPCgdzdG9yYWdlGAkgASgIEkMK",
            "CGNvbXByZXNzGAogASgLMjEuRmVuZ19Jb3RfTXF0dFNlcnZpY2UuUHJvdG9C",
            "dWZNb2RlbHMuQ29tcHJlc3NTcGVjIl4KD01ldGFUYWdTZXF1ZW5jZRIPCgd2",
            "ZXJzaW9uGAIgASgJEjoKBHRhZ3MYASADKAsyLC5GZW5nX0lvdF9NcXR0U2Vy",
            "dmljZS5Qcm90b0J1Zk1vZGVscy5NZXRhVGFnIkAKDENvbXByZXNzU3BlYxIO",
            "CgZlbmFibGUYASABKAgSDQoFdmFsdWUYAiABKAISEQoJbWF4RWxhcHNlGAMg",
            "ASgDIpIBCghSdGRWYWx1ZRIRCgl0aW1lU3RhbXAYASABKAMSDwoHcXVhbGl0",
            "eRgCIAEoAxIQCgZpbnRWYWwYAyABKANIABIQCgZkYmxWYWwYBCABKAFIABIR",
            "Cgdib29sVmFsGAUgASgISAASEAoGc3RyVmFsGAYgASgJSAASEAoGYnl0VmFs",
            "GAcgASgMSABCBwoFdmFsdWUiWAoKTmFtZWRWYWx1ZRIMCgRuYW1lGAEgASgJ",
            "EjwKBXZhbHVlGAIgASgLMi0uRmVuZ19Jb3RfTXF0dFNlcnZpY2UuUHJvdG9C",
            "dWZNb2RlbHMuUnRkVmFsdWUiUAoNVmFsdWVTZXF1ZW5jZRI/CgZ2YWx1ZXMY",
            "ASADKAsyLy5GZW5nX0lvdF9NcXR0U2VydmljZS5Qcm90b0J1Zk1vZGVscy5O",
            "YW1lZFZhbHVlIi8KD1Byb3RvY29sVmVyc2lvbhINCgVtYWpvchgBIAEoDRIN",
            "CgVtaW5vchgCIAEoDSJ3ChFHYXRld2F5Q29vcmRpbmF0ZRIWCg5sb2NhbFRp",
            "bWVTdGFtcBgBIAEoAxJKCgxsb2NhbFZlcnNpb24YAiABKAsyNC5GZW5nX0lv",
            "dF9NcXR0U2VydmljZS5Qcm90b0J1Zk1vZGVscy5Qcm90b2NvbFZlcnNpb24i",
            "kAEKEFNlcnZlckNvb3JkaW5hdGUSFgoObG9jYWxUaW1lU3RhbXAYASABKAMS",
            "FwoPc2VydmVyVGltZVN0YW1wGAIgASgDEksKDXNlcnZlclZlcnNpb24YAyAB",
            "KAsyNC5GZW5nX0lvdF9NcXR0U2VydmljZS5Qcm90b0J1Zk1vZGVscy5Qcm90",
            "b2NvbFZlcnNpb24iKgoIUnRkRXZlbnQSDQoFdG9waWMYASABKAkSDwoHcGF5",
            "bG9hZBgCIAEoDCKjAQoVTWV0YVRhZ1VwZGF0ZVNlcXVlbmNlEj0KBHR5cGUY",
            "ASABKA4yLy5GZW5nX0lvdF9NcXR0U2VydmljZS5Qcm90b0J1Zk1vZGVscy5V",
            "cGRhdGVUeXBlEg8KB3ZlcnNpb24YAiABKAkSOgoEdGFncxgDIAMoCzIsLkZl",
            "bmdfSW90X01xdHRTZXJ2aWNlLlByb3RvQnVmTW9kZWxzLk1ldGFUYWcidgoS",
            "TWV0YU5vdGlmeVNlcXVlbmNlEj0KBHR5cGUYASABKA4yLy5GZW5nX0lvdF9N",
            "cXR0U2VydmljZS5Qcm90b0J1Zk1vZGVscy5VcGRhdGVUeXBlEg8KB3ZlcnNp",
            "b24YAiABKAkSEAoIdGFnbmFtZXMYAyADKAkqVQoJVmFsdWVUeXBlEgsKB1Vu",
            "a25vd24QABILCgdJbnRlZ2VyEAESCgoGRG91YmxlEAISCwoHQm9vbGVhbhAD",
            "EgoKBlN0cmluZxAEEgkKBUJ5dGVzEAUqQAoKVXBkYXRlVHlwZRIICgROb25l",
            "EAASBwoDTmV3EAESCQoFRXJhc2UQAhIKCgZVcGRhdGUQAxIICgRGdWxsEARi",
            "BnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::FengIotMqttService.ProtoBufModels.ValueType), typeof(global::FengIotMqttService.ProtoBufModels.UpdateType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::FengIotMqttService.ProtoBufModels.MetaTag), global::FengIotMqttService.ProtoBufModels.MetaTag.Parser, new[]{ "Version", "Name", "ShowName", "Description", "Type", "Unit", "Range", "DefaultValue", "Storage", "Compress" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::FengIotMqttService.ProtoBufModels.MetaTagSequence), global::FengIotMqttService.ProtoBufModels.MetaTagSequence.Parser, new[]{ "Version", "Tags" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::FengIotMqttService.ProtoBufModels.CompressSpec), global::FengIotMqttService.ProtoBufModels.CompressSpec.Parser, new[]{ "Enable", "Value", "MaxElapse" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::FengIotMqttService.ProtoBufModels.RtdValue), global::FengIotMqttService.ProtoBufModels.RtdValue.Parser, new[]{ "TimeStamp", "Quality", "IntVal", "DblVal", "BoolVal", "StrVal", "BytVal" }, new[]{ "Value" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::FengIotMqttService.ProtoBufModels.NamedValue), global::FengIotMqttService.ProtoBufModels.NamedValue.Parser, new[]{ "Name", "Value" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::FengIotMqttService.ProtoBufModels.ValueSequence), global::FengIotMqttService.ProtoBufModels.ValueSequence.Parser, new[]{ "Values" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::FengIotMqttService.ProtoBufModels.ProtocolVersion), global::FengIotMqttService.ProtoBufModels.ProtocolVersion.Parser, new[]{ "Major", "Minor" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::FengIotMqttService.ProtoBufModels.GatewayCoordinate), global::FengIotMqttService.ProtoBufModels.GatewayCoordinate.Parser, new[]{ "LocalTimeStamp", "LocalVersion" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::FengIotMqttService.ProtoBufModels.ServerCoordinate), global::FengIotMqttService.ProtoBufModels.ServerCoordinate.Parser, new[]{ "LocalTimeStamp", "ServerTimeStamp", "ServerVersion" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::FengIotMqttService.ProtoBufModels.RtdEvent), global::FengIotMqttService.ProtoBufModels.RtdEvent.Parser, new[]{ "Topic", "Payload" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::FengIotMqttService.ProtoBufModels.MetaTagUpdateSequence), global::FengIotMqttService.ProtoBufModels.MetaTagUpdateSequence.Parser, new[]{ "Type", "Version", "Tags" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::FengIotMqttService.ProtoBufModels.MetaNotifySequence), global::FengIotMqttService.ProtoBufModels.MetaNotifySequence.Parser, new[]{ "Type", "Version", "Tagnames" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  /// 值类型枚举
  /// </summary>
  public enum ValueType {
    [pbr::OriginalName("Unknown")] Unknown = 0,
    /// <summary>
    /// 整数类型
    /// </summary>
    [pbr::OriginalName("Integer")] Integer = 1,
    /// <summary>
    /// 浮点类型
    /// </summary>
    [pbr::OriginalName("Double")] Double = 2,
    /// <summary>
    /// 布尔类型
    /// </summary>
    [pbr::OriginalName("Boolean")] Boolean = 3,
    /// <summary>
    /// 字符串类型
    /// </summary>
    [pbr::OriginalName("String")] String = 4,
    /// <summary>
    /// 字节流类型
    /// </summary>
    [pbr::OriginalName("Bytes")] Bytes = 5,
  }

  public enum UpdateType {
    /// <summary>
    /// 位置，忽略本次推送
    /// </summary>
    [pbr::OriginalName("None")] None = 0,
    /// <summary>
    /// 新增
    /// </summary>
    [pbr::OriginalName("New")] New = 1,
    /// <summary>
    ///删除
    /// </summary>
    [pbr::OriginalName("Erase")] Erase = 2,
    /// <summary>
    ///局部更新
    /// </summary>
    [pbr::OriginalName("Update")] Update = 3,
    /// <summary>
    ///全量更新
    /// </summary>
    [pbr::OriginalName("Full")] Full = 4,
  }

  #endregion

  #region Messages
  /// <summary>
  /// 元数据标签定义
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class MetaTag : pb::IMessage<MetaTag>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MetaTag> _parser = new pb::MessageParser<MetaTag>(() => new MetaTag());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MetaTag> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::FengIotMqttService.ProtoBufModels.MetaTageReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MetaTag() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MetaTag(MetaTag other) : this() {
      version_ = other.version_;
      name_ = other.name_;
      showName_ = other.showName_;
      description_ = other.description_;
      type_ = other.type_;
      unit_ = other.unit_;
      range_ = other.range_;
      defaultValue_ = other.defaultValue_ != null ? other.defaultValue_.Clone() : null;
      storage_ = other.storage_;
      compress_ = other.compress_ != null ? other.compress_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MetaTag Clone() {
      return new MetaTag(this);
    }

    /// <summary>Field number for the "version" field.</summary>
    public const int VersionFieldNumber = 1;
    private int version_;
    /// <summary>
    /// 位号版本,位号信息发生变化时进行更新
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Version {
      get { return version_; }
      set {
        version_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 2;
    private string name_ = "";
    /// <summary>
    /// 位号名称（字符开头,不能包含非法字符）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "showName" field.</summary>
    public const int ShowNameFieldNumber = 3;
    private string showName_ = "";
    /// <summary>
    /// 位号可读名称 (任意字符,可选)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string ShowName {
      get { return showName_; }
      set {
        showName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "description" field.</summary>
    public const int DescriptionFieldNumber = 4;
    private string description_ = "";
    /// <summary>
    /// 位号描述 (可选)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Description {
      get { return description_; }
      set {
        description_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 5;
    private global::FengIotMqttService.ProtoBufModels.ValueType type_ = global::FengIotMqttService.ProtoBufModels.ValueType.Unknown;
    /// <summary>
    /// 位号值类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::FengIotMqttService.ProtoBufModels.ValueType Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "unit" field.</summary>
    public const int UnitFieldNumber = 6;
    private string unit_ = "";
    /// <summary>
    /// 位号单位 (可选)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Unit {
      get { return unit_; }
      set {
        unit_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "range" field.</summary>
    public const int RangeFieldNumber = 7;
    private string range_ = "";
    /// <summary>
    /// 位号量程值范围,如 0-100 (可选)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Range {
      get { return range_; }
      set {
        range_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "defaultValue" field.</summary>
    public const int DefaultValueFieldNumber = 8;
    private global::FengIotMqttService.ProtoBufModels.RtdValue defaultValue_;
    /// <summary>
    /// 位号默认值 (可选)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::FengIotMqttService.ProtoBufModels.RtdValue DefaultValue {
      get { return defaultValue_; }
      set {
        defaultValue_ = value;
      }
    }

    /// <summary>Field number for the "storage" field.</summary>
    public const int StorageFieldNumber = 9;
    private bool storage_;
    /// <summary>
    /// 位号存档置,值为 true 默认记存档 (可选)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Storage {
      get { return storage_; }
      set {
        storage_ = value;
      }
    }

    /// <summary>Field number for the "compress" field.</summary>
    public const int CompressFieldNumber = 10;
    private global::FengIotMqttService.ProtoBufModels.CompressSpec compress_;
    /// <summary>
    /// 位号压缩设置 (可选)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::FengIotMqttService.ProtoBufModels.CompressSpec Compress {
      get { return compress_; }
      set {
        compress_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MetaTag);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MetaTag other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Version != other.Version) return false;
      if (Name != other.Name) return false;
      if (ShowName != other.ShowName) return false;
      if (Description != other.Description) return false;
      if (Type != other.Type) return false;
      if (Unit != other.Unit) return false;
      if (Range != other.Range) return false;
      if (!object.Equals(DefaultValue, other.DefaultValue)) return false;
      if (Storage != other.Storage) return false;
      if (!object.Equals(Compress, other.Compress)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Version != 0) hash ^= Version.GetHashCode();
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (ShowName.Length != 0) hash ^= ShowName.GetHashCode();
      if (Description.Length != 0) hash ^= Description.GetHashCode();
      if (Type != global::FengIotMqttService.ProtoBufModels.ValueType.Unknown) hash ^= Type.GetHashCode();
      if (Unit.Length != 0) hash ^= Unit.GetHashCode();
      if (Range.Length != 0) hash ^= Range.GetHashCode();
      if (defaultValue_ != null) hash ^= DefaultValue.GetHashCode();
      if (Storage != false) hash ^= Storage.GetHashCode();
      if (compress_ != null) hash ^= Compress.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Version != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Version);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (ShowName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(ShowName);
      }
      if (Description.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Description);
      }
      if (Type != global::FengIotMqttService.ProtoBufModels.ValueType.Unknown) {
        output.WriteRawTag(40);
        output.WriteEnum((int) Type);
      }
      if (Unit.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(Unit);
      }
      if (Range.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(Range);
      }
      if (defaultValue_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(DefaultValue);
      }
      if (Storage != false) {
        output.WriteRawTag(72);
        output.WriteBool(Storage);
      }
      if (compress_ != null) {
        output.WriteRawTag(82);
        output.WriteMessage(Compress);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Version != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Version);
      }
      if (Name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Name);
      }
      if (ShowName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(ShowName);
      }
      if (Description.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(Description);
      }
      if (Type != global::FengIotMqttService.ProtoBufModels.ValueType.Unknown) {
        output.WriteRawTag(40);
        output.WriteEnum((int) Type);
      }
      if (Unit.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(Unit);
      }
      if (Range.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(Range);
      }
      if (defaultValue_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(DefaultValue);
      }
      if (Storage != false) {
        output.WriteRawTag(72);
        output.WriteBool(Storage);
      }
      if (compress_ != null) {
        output.WriteRawTag(82);
        output.WriteMessage(Compress);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Version != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Version);
      }
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (ShowName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(ShowName);
      }
      if (Description.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Description);
      }
      if (Type != global::FengIotMqttService.ProtoBufModels.ValueType.Unknown) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (Unit.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Unit);
      }
      if (Range.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Range);
      }
      if (defaultValue_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(DefaultValue);
      }
      if (Storage != false) {
        size += 1 + 1;
      }
      if (compress_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Compress);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MetaTag other) {
      if (other == null) {
        return;
      }
      if (other.Version != 0) {
        Version = other.Version;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.ShowName.Length != 0) {
        ShowName = other.ShowName;
      }
      if (other.Description.Length != 0) {
        Description = other.Description;
      }
      if (other.Type != global::FengIotMqttService.ProtoBufModels.ValueType.Unknown) {
        Type = other.Type;
      }
      if (other.Unit.Length != 0) {
        Unit = other.Unit;
      }
      if (other.Range.Length != 0) {
        Range = other.Range;
      }
      if (other.defaultValue_ != null) {
        if (defaultValue_ == null) {
          DefaultValue = new global::FengIotMqttService.ProtoBufModels.RtdValue();
        }
        DefaultValue.MergeFrom(other.DefaultValue);
      }
      if (other.Storage != false) {
        Storage = other.Storage;
      }
      if (other.compress_ != null) {
        if (compress_ == null) {
          Compress = new global::FengIotMqttService.ProtoBufModels.CompressSpec();
        }
        Compress.MergeFrom(other.Compress);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Version = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            ShowName = input.ReadString();
            break;
          }
          case 34: {
            Description = input.ReadString();
            break;
          }
          case 40: {
            Type = (global::FengIotMqttService.ProtoBufModels.ValueType) input.ReadEnum();
            break;
          }
          case 50: {
            Unit = input.ReadString();
            break;
          }
          case 58: {
            Range = input.ReadString();
            break;
          }
          case 66: {
            if (defaultValue_ == null) {
              DefaultValue = new global::FengIotMqttService.ProtoBufModels.RtdValue();
            }
            input.ReadMessage(DefaultValue);
            break;
          }
          case 72: {
            Storage = input.ReadBool();
            break;
          }
          case 82: {
            if (compress_ == null) {
              Compress = new global::FengIotMqttService.ProtoBufModels.CompressSpec();
            }
            input.ReadMessage(Compress);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Version = input.ReadInt32();
            break;
          }
          case 18: {
            Name = input.ReadString();
            break;
          }
          case 26: {
            ShowName = input.ReadString();
            break;
          }
          case 34: {
            Description = input.ReadString();
            break;
          }
          case 40: {
            Type = (global::FengIotMqttService.ProtoBufModels.ValueType) input.ReadEnum();
            break;
          }
          case 50: {
            Unit = input.ReadString();
            break;
          }
          case 58: {
            Range = input.ReadString();
            break;
          }
          case 66: {
            if (defaultValue_ == null) {
              DefaultValue = new global::FengIotMqttService.ProtoBufModels.RtdValue();
            }
            input.ReadMessage(DefaultValue);
            break;
          }
          case 72: {
            Storage = input.ReadBool();
            break;
          }
          case 82: {
            if (compress_ == null) {
              Compress = new global::FengIotMqttService.ProtoBufModels.CompressSpec();
            }
            input.ReadMessage(Compress);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 设备属性列定义
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class MetaTagSequence : pb::IMessage<MetaTagSequence>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MetaTagSequence> _parser = new pb::MessageParser<MetaTagSequence>(() => new MetaTagSequence());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MetaTagSequence> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::FengIotMqttService.ProtoBufModels.MetaTageReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MetaTagSequence() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MetaTagSequence(MetaTagSequence other) : this() {
      version_ = other.version_;
      tags_ = other.tags_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MetaTagSequence Clone() {
      return new MetaTagSequence(this);
    }

    /// <summary>Field number for the "version" field.</summary>
    public const int VersionFieldNumber = 2;
    private string version_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Version {
      get { return version_; }
      set {
        version_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "tags" field.</summary>
    public const int TagsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::FengIotMqttService.ProtoBufModels.MetaTag> _repeated_tags_codec
        = pb::FieldCodec.ForMessage(10, global::FengIotMqttService.ProtoBufModels.MetaTag.Parser);
    private readonly pbc::RepeatedField<global::FengIotMqttService.ProtoBufModels.MetaTag> tags_ = new pbc::RepeatedField<global::FengIotMqttService.ProtoBufModels.MetaTag>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::FengIotMqttService.ProtoBufModels.MetaTag> Tags {
      get { return tags_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MetaTagSequence);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MetaTagSequence other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Version != other.Version) return false;
      if(!tags_.Equals(other.tags_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Version.Length != 0) hash ^= Version.GetHashCode();
      hash ^= tags_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      tags_.WriteTo(output, _repeated_tags_codec);
      if (Version.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Version);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      tags_.WriteTo(ref output, _repeated_tags_codec);
      if (Version.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Version);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Version.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Version);
      }
      size += tags_.CalculateSize(_repeated_tags_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MetaTagSequence other) {
      if (other == null) {
        return;
      }
      if (other.Version.Length != 0) {
        Version = other.Version;
      }
      tags_.Add(other.tags_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            tags_.AddEntriesFrom(input, _repeated_tags_codec);
            break;
          }
          case 18: {
            Version = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            tags_.AddEntriesFrom(ref input, _repeated_tags_codec);
            break;
          }
          case 18: {
            Version = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 存档压缩定义
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CompressSpec : pb::IMessage<CompressSpec>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CompressSpec> _parser = new pb::MessageParser<CompressSpec>(() => new CompressSpec());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CompressSpec> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::FengIotMqttService.ProtoBufModels.MetaTageReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CompressSpec() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CompressSpec(CompressSpec other) : this() {
      enable_ = other.enable_;
      value_ = other.value_;
      maxElapse_ = other.maxElapse_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CompressSpec Clone() {
      return new CompressSpec(this);
    }

    /// <summary>Field number for the "enable" field.</summary>
    public const int EnableFieldNumber = 1;
    private bool enable_;
    /// <summary>
    /// 压缩设置启用标识 默认 false
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Enable {
      get { return enable_; }
      set {
        enable_ = value;
      }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 2;
    private float value_;
    /// <summary>
    /// 工程量参数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float Value {
      get { return value_; }
      set {
        value_ = value;
      }
    }

    /// <summary>Field number for the "maxElapse" field.</summary>
    public const int MaxElapseFieldNumber = 3;
    private long maxElapse_;
    /// <summary>
    /// 最大上送周期 (s)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long MaxElapse {
      get { return maxElapse_; }
      set {
        maxElapse_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CompressSpec);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CompressSpec other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Enable != other.Enable) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(Value, other.Value)) return false;
      if (MaxElapse != other.MaxElapse) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Enable != false) hash ^= Enable.GetHashCode();
      if (Value != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(Value);
      if (MaxElapse != 0L) hash ^= MaxElapse.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Enable != false) {
        output.WriteRawTag(8);
        output.WriteBool(Enable);
      }
      if (Value != 0F) {
        output.WriteRawTag(21);
        output.WriteFloat(Value);
      }
      if (MaxElapse != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(MaxElapse);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Enable != false) {
        output.WriteRawTag(8);
        output.WriteBool(Enable);
      }
      if (Value != 0F) {
        output.WriteRawTag(21);
        output.WriteFloat(Value);
      }
      if (MaxElapse != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(MaxElapse);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Enable != false) {
        size += 1 + 1;
      }
      if (Value != 0F) {
        size += 1 + 4;
      }
      if (MaxElapse != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(MaxElapse);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CompressSpec other) {
      if (other == null) {
        return;
      }
      if (other.Enable != false) {
        Enable = other.Enable;
      }
      if (other.Value != 0F) {
        Value = other.Value;
      }
      if (other.MaxElapse != 0L) {
        MaxElapse = other.MaxElapse;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Enable = input.ReadBool();
            break;
          }
          case 21: {
            Value = input.ReadFloat();
            break;
          }
          case 24: {
            MaxElapse = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Enable = input.ReadBool();
            break;
          }
          case 21: {
            Value = input.ReadFloat();
            break;
          }
          case 24: {
            MaxElapse = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 实时数据定义
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RtdValue : pb::IMessage<RtdValue>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RtdValue> _parser = new pb::MessageParser<RtdValue>(() => new RtdValue());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RtdValue> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::FengIotMqttService.ProtoBufModels.MetaTageReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RtdValue() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RtdValue(RtdValue other) : this() {
      timeStamp_ = other.timeStamp_;
      quality_ = other.quality_;
      switch (other.ValueCase) {
        case ValueOneofCase.IntVal:
          IntVal = other.IntVal;
          break;
        case ValueOneofCase.DblVal:
          DblVal = other.DblVal;
          break;
        case ValueOneofCase.BoolVal:
          BoolVal = other.BoolVal;
          break;
        case ValueOneofCase.StrVal:
          StrVal = other.StrVal;
          break;
        case ValueOneofCase.BytVal:
          BytVal = other.BytVal;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RtdValue Clone() {
      return new RtdValue(this);
    }

    /// <summary>Field number for the "timeStamp" field.</summary>
    public const int TimeStampFieldNumber = 1;
    private long timeStamp_;
    /// <summary>
    /// UTC 毫秒时间戳
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long TimeStamp {
      get { return timeStamp_; }
      set {
        timeStamp_ = value;
      }
    }

    /// <summary>Field number for the "quality" field.</summary>
    public const int QualityFieldNumber = 2;
    private long quality_;
    /// <summary>
    /// 质量码,0-标识正常值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Quality {
      get { return quality_; }
      set {
        quality_ = value;
      }
    }

    /// <summary>Field number for the "intVal" field.</summary>
    public const int IntValFieldNumber = 3;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long IntVal {
      get { return HasIntVal ? (long) value_ : 0L; }
      set {
        value_ = value;
        valueCase_ = ValueOneofCase.IntVal;
      }
    }
    /// <summary>Gets whether the "intVal" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasIntVal {
      get { return valueCase_ == ValueOneofCase.IntVal; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "intVal" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearIntVal() {
      if (HasIntVal) {
        ClearValue();
      }
    }

    /// <summary>Field number for the "dblVal" field.</summary>
    public const int DblValFieldNumber = 4;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double DblVal {
      get { return HasDblVal ? (double) value_ : 0D; }
      set {
        value_ = value;
        valueCase_ = ValueOneofCase.DblVal;
      }
    }
    /// <summary>Gets whether the "dblVal" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasDblVal {
      get { return valueCase_ == ValueOneofCase.DblVal; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "dblVal" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearDblVal() {
      if (HasDblVal) {
        ClearValue();
      }
    }

    /// <summary>Field number for the "boolVal" field.</summary>
    public const int BoolValFieldNumber = 5;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool BoolVal {
      get { return HasBoolVal ? (bool) value_ : false; }
      set {
        value_ = value;
        valueCase_ = ValueOneofCase.BoolVal;
      }
    }
    /// <summary>Gets whether the "boolVal" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasBoolVal {
      get { return valueCase_ == ValueOneofCase.BoolVal; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "boolVal" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearBoolVal() {
      if (HasBoolVal) {
        ClearValue();
      }
    }

    /// <summary>Field number for the "strVal" field.</summary>
    public const int StrValFieldNumber = 6;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string StrVal {
      get { return HasStrVal ? (string) value_ : ""; }
      set {
        value_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
        valueCase_ = ValueOneofCase.StrVal;
      }
    }
    /// <summary>Gets whether the "strVal" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasStrVal {
      get { return valueCase_ == ValueOneofCase.StrVal; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "strVal" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearStrVal() {
      if (HasStrVal) {
        ClearValue();
      }
    }

    /// <summary>Field number for the "bytVal" field.</summary>
    public const int BytValFieldNumber = 7;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString BytVal {
      get { return HasBytVal ? (pb::ByteString) value_ : pb::ByteString.Empty; }
      set {
        value_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
        valueCase_ = ValueOneofCase.BytVal;
      }
    }
    /// <summary>Gets whether the "bytVal" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasBytVal {
      get { return valueCase_ == ValueOneofCase.BytVal; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "bytVal" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearBytVal() {
      if (HasBytVal) {
        ClearValue();
      }
    }

    private object value_;
    /// <summary>Enum of possible cases for the "value" oneof.</summary>
    public enum ValueOneofCase {
      None = 0,
      IntVal = 3,
      DblVal = 4,
      BoolVal = 5,
      StrVal = 6,
      BytVal = 7,
    }
    private ValueOneofCase valueCase_ = ValueOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ValueOneofCase ValueCase {
      get { return valueCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearValue() {
      valueCase_ = ValueOneofCase.None;
      value_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RtdValue);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RtdValue other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (TimeStamp != other.TimeStamp) return false;
      if (Quality != other.Quality) return false;
      if (IntVal != other.IntVal) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(DblVal, other.DblVal)) return false;
      if (BoolVal != other.BoolVal) return false;
      if (StrVal != other.StrVal) return false;
      if (BytVal != other.BytVal) return false;
      if (ValueCase != other.ValueCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (TimeStamp != 0L) hash ^= TimeStamp.GetHashCode();
      if (Quality != 0L) hash ^= Quality.GetHashCode();
      if (HasIntVal) hash ^= IntVal.GetHashCode();
      if (HasDblVal) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(DblVal);
      if (HasBoolVal) hash ^= BoolVal.GetHashCode();
      if (HasStrVal) hash ^= StrVal.GetHashCode();
      if (HasBytVal) hash ^= BytVal.GetHashCode();
      hash ^= (int) valueCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (TimeStamp != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(TimeStamp);
      }
      if (Quality != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Quality);
      }
      if (HasIntVal) {
        output.WriteRawTag(24);
        output.WriteInt64(IntVal);
      }
      if (HasDblVal) {
        output.WriteRawTag(33);
        output.WriteDouble(DblVal);
      }
      if (HasBoolVal) {
        output.WriteRawTag(40);
        output.WriteBool(BoolVal);
      }
      if (HasStrVal) {
        output.WriteRawTag(50);
        output.WriteString(StrVal);
      }
      if (HasBytVal) {
        output.WriteRawTag(58);
        output.WriteBytes(BytVal);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (TimeStamp != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(TimeStamp);
      }
      if (Quality != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(Quality);
      }
      if (HasIntVal) {
        output.WriteRawTag(24);
        output.WriteInt64(IntVal);
      }
      if (HasDblVal) {
        output.WriteRawTag(33);
        output.WriteDouble(DblVal);
      }
      if (HasBoolVal) {
        output.WriteRawTag(40);
        output.WriteBool(BoolVal);
      }
      if (HasStrVal) {
        output.WriteRawTag(50);
        output.WriteString(StrVal);
      }
      if (HasBytVal) {
        output.WriteRawTag(58);
        output.WriteBytes(BytVal);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (TimeStamp != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(TimeStamp);
      }
      if (Quality != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Quality);
      }
      if (HasIntVal) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(IntVal);
      }
      if (HasDblVal) {
        size += 1 + 8;
      }
      if (HasBoolVal) {
        size += 1 + 1;
      }
      if (HasStrVal) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(StrVal);
      }
      if (HasBytVal) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(BytVal);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RtdValue other) {
      if (other == null) {
        return;
      }
      if (other.TimeStamp != 0L) {
        TimeStamp = other.TimeStamp;
      }
      if (other.Quality != 0L) {
        Quality = other.Quality;
      }
      switch (other.ValueCase) {
        case ValueOneofCase.IntVal:
          IntVal = other.IntVal;
          break;
        case ValueOneofCase.DblVal:
          DblVal = other.DblVal;
          break;
        case ValueOneofCase.BoolVal:
          BoolVal = other.BoolVal;
          break;
        case ValueOneofCase.StrVal:
          StrVal = other.StrVal;
          break;
        case ValueOneofCase.BytVal:
          BytVal = other.BytVal;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            TimeStamp = input.ReadInt64();
            break;
          }
          case 16: {
            Quality = input.ReadInt64();
            break;
          }
          case 24: {
            IntVal = input.ReadInt64();
            break;
          }
          case 33: {
            DblVal = input.ReadDouble();
            break;
          }
          case 40: {
            BoolVal = input.ReadBool();
            break;
          }
          case 50: {
            StrVal = input.ReadString();
            break;
          }
          case 58: {
            BytVal = input.ReadBytes();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            TimeStamp = input.ReadInt64();
            break;
          }
          case 16: {
            Quality = input.ReadInt64();
            break;
          }
          case 24: {
            IntVal = input.ReadInt64();
            break;
          }
          case 33: {
            DblVal = input.ReadDouble();
            break;
          }
          case 40: {
            BoolVal = input.ReadBool();
            break;
          }
          case 50: {
            StrVal = input.ReadString();
            break;
          }
          case 58: {
            BytVal = input.ReadBytes();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 命名实时数据定义
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class NamedValue : pb::IMessage<NamedValue>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<NamedValue> _parser = new pb::MessageParser<NamedValue>(() => new NamedValue());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<NamedValue> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::FengIotMqttService.ProtoBufModels.MetaTageReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NamedValue() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NamedValue(NamedValue other) : this() {
      name_ = other.name_;
      value_ = other.value_ != null ? other.value_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public NamedValue Clone() {
      return new NamedValue(this);
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int NameFieldNumber = 1;
    private string name_ = "";
    /// <summary>
    /// 位号名称
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 2;
    private global::FengIotMqttService.ProtoBufModels.RtdValue value_;
    /// <summary>
    /// 位号实时值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::FengIotMqttService.ProtoBufModels.RtdValue Value {
      get { return value_; }
      set {
        value_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as NamedValue);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(NamedValue other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Name != other.Name) return false;
      if (!object.Equals(Value, other.Value)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Name.Length != 0) hash ^= Name.GetHashCode();
      if (value_ != null) hash ^= Value.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Name.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Name);
      }
      if (value_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Name.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Name);
      }
      if (value_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Name);
      }
      if (value_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Value);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(NamedValue other) {
      if (other == null) {
        return;
      }
      if (other.Name.Length != 0) {
        Name = other.Name;
      }
      if (other.value_ != null) {
        if (value_ == null) {
          Value = new global::FengIotMqttService.ProtoBufModels.RtdValue();
        }
        Value.MergeFrom(other.Value);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Name = input.ReadString();
            break;
          }
          case 18: {
            if (value_ == null) {
              Value = new global::FengIotMqttService.ProtoBufModels.RtdValue();
            }
            input.ReadMessage(Value);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Name = input.ReadString();
            break;
          }
          case 18: {
            if (value_ == null) {
              Value = new global::FengIotMqttService.ProtoBufModels.RtdValue();
            }
            input.ReadMessage(Value);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 实时数据列定义
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ValueSequence : pb::IMessage<ValueSequence>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ValueSequence> _parser = new pb::MessageParser<ValueSequence>(() => new ValueSequence());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ValueSequence> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::FengIotMqttService.ProtoBufModels.MetaTageReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ValueSequence() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ValueSequence(ValueSequence other) : this() {
      values_ = other.values_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ValueSequence Clone() {
      return new ValueSequence(this);
    }

    /// <summary>Field number for the "values" field.</summary>
    public const int ValuesFieldNumber = 1;
    private static readonly pb::FieldCodec<global::FengIotMqttService.ProtoBufModels.NamedValue> _repeated_values_codec
        = pb::FieldCodec.ForMessage(10, global::FengIotMqttService.ProtoBufModels.NamedValue.Parser);
    private readonly pbc::RepeatedField<global::FengIotMqttService.ProtoBufModels.NamedValue> values_ = new pbc::RepeatedField<global::FengIotMqttService.ProtoBufModels.NamedValue>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::FengIotMqttService.ProtoBufModels.NamedValue> Values {
      get { return values_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ValueSequence);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ValueSequence other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!values_.Equals(other.values_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= values_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      values_.WriteTo(output, _repeated_values_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      values_.WriteTo(ref output, _repeated_values_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += values_.CalculateSize(_repeated_values_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ValueSequence other) {
      if (other == null) {
        return;
      }
      values_.Add(other.values_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            values_.AddEntriesFrom(input, _repeated_values_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            values_.AddEntriesFrom(ref input, _repeated_values_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ProtocolVersion : pb::IMessage<ProtocolVersion>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ProtocolVersion> _parser = new pb::MessageParser<ProtocolVersion>(() => new ProtocolVersion());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ProtocolVersion> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::FengIotMqttService.ProtoBufModels.MetaTageReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ProtocolVersion() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ProtocolVersion(ProtocolVersion other) : this() {
      major_ = other.major_;
      minor_ = other.minor_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ProtocolVersion Clone() {
      return new ProtocolVersion(this);
    }

    /// <summary>Field number for the "major" field.</summary>
    public const int MajorFieldNumber = 1;
    private uint major_;
    /// <summary>
    ///主版本号，表示重大变更或不兼容的更改。
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Major {
      get { return major_; }
      set {
        major_ = value;
      }
    }

    /// <summary>Field number for the "minor" field.</summary>
    public const int MinorFieldNumber = 2;
    private uint minor_;
    /// <summary>
    ///次版本号，表示向后兼容的功能增强。
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Minor {
      get { return minor_; }
      set {
        minor_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ProtocolVersion);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ProtocolVersion other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Major != other.Major) return false;
      if (Minor != other.Minor) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Major != 0) hash ^= Major.GetHashCode();
      if (Minor != 0) hash ^= Minor.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Major != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(Major);
      }
      if (Minor != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Minor);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Major != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(Major);
      }
      if (Minor != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Minor);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Major != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Major);
      }
      if (Minor != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Minor);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ProtocolVersion other) {
      if (other == null) {
        return;
      }
      if (other.Major != 0) {
        Major = other.Major;
      }
      if (other.Minor != 0) {
        Minor = other.Minor;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Major = input.ReadUInt32();
            break;
          }
          case 16: {
            Minor = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Major = input.ReadUInt32();
            break;
          }
          case 16: {
            Minor = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 时间同步
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class GatewayCoordinate : pb::IMessage<GatewayCoordinate>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GatewayCoordinate> _parser = new pb::MessageParser<GatewayCoordinate>(() => new GatewayCoordinate());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GatewayCoordinate> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::FengIotMqttService.ProtoBufModels.MetaTageReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GatewayCoordinate() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GatewayCoordinate(GatewayCoordinate other) : this() {
      localTimeStamp_ = other.localTimeStamp_;
      localVersion_ = other.localVersion_ != null ? other.localVersion_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GatewayCoordinate Clone() {
      return new GatewayCoordinate(this);
    }

    /// <summary>Field number for the "localTimeStamp" field.</summary>
    public const int LocalTimeStampFieldNumber = 1;
    private long localTimeStamp_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long LocalTimeStamp {
      get { return localTimeStamp_; }
      set {
        localTimeStamp_ = value;
      }
    }

    /// <summary>Field number for the "localVersion" field.</summary>
    public const int LocalVersionFieldNumber = 2;
    private global::FengIotMqttService.ProtoBufModels.ProtocolVersion localVersion_;
    /// <summary>
    ///网关使用的协议版本，V1.2新增字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::FengIotMqttService.ProtoBufModels.ProtocolVersion LocalVersion {
      get { return localVersion_; }
      set {
        localVersion_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GatewayCoordinate);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GatewayCoordinate other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (LocalTimeStamp != other.LocalTimeStamp) return false;
      if (!object.Equals(LocalVersion, other.LocalVersion)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (LocalTimeStamp != 0L) hash ^= LocalTimeStamp.GetHashCode();
      if (localVersion_ != null) hash ^= LocalVersion.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (LocalTimeStamp != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(LocalTimeStamp);
      }
      if (localVersion_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(LocalVersion);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (LocalTimeStamp != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(LocalTimeStamp);
      }
      if (localVersion_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(LocalVersion);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (LocalTimeStamp != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(LocalTimeStamp);
      }
      if (localVersion_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(LocalVersion);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GatewayCoordinate other) {
      if (other == null) {
        return;
      }
      if (other.LocalTimeStamp != 0L) {
        LocalTimeStamp = other.LocalTimeStamp;
      }
      if (other.localVersion_ != null) {
        if (localVersion_ == null) {
          LocalVersion = new global::FengIotMqttService.ProtoBufModels.ProtocolVersion();
        }
        LocalVersion.MergeFrom(other.LocalVersion);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            LocalTimeStamp = input.ReadInt64();
            break;
          }
          case 18: {
            if (localVersion_ == null) {
              LocalVersion = new global::FengIotMqttService.ProtoBufModels.ProtocolVersion();
            }
            input.ReadMessage(LocalVersion);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            LocalTimeStamp = input.ReadInt64();
            break;
          }
          case 18: {
            if (localVersion_ == null) {
              LocalVersion = new global::FengIotMqttService.ProtoBufModels.ProtocolVersion();
            }
            input.ReadMessage(LocalVersion);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 时间同步
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ServerCoordinate : pb::IMessage<ServerCoordinate>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ServerCoordinate> _parser = new pb::MessageParser<ServerCoordinate>(() => new ServerCoordinate());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ServerCoordinate> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::FengIotMqttService.ProtoBufModels.MetaTageReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ServerCoordinate() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ServerCoordinate(ServerCoordinate other) : this() {
      localTimeStamp_ = other.localTimeStamp_;
      serverTimeStamp_ = other.serverTimeStamp_;
      serverVersion_ = other.serverVersion_ != null ? other.serverVersion_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ServerCoordinate Clone() {
      return new ServerCoordinate(this);
    }

    /// <summary>Field number for the "localTimeStamp" field.</summary>
    public const int LocalTimeStampFieldNumber = 1;
    private long localTimeStamp_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long LocalTimeStamp {
      get { return localTimeStamp_; }
      set {
        localTimeStamp_ = value;
      }
    }

    /// <summary>Field number for the "serverTimeStamp" field.</summary>
    public const int ServerTimeStampFieldNumber = 2;
    private long serverTimeStamp_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long ServerTimeStamp {
      get { return serverTimeStamp_; }
      set {
        serverTimeStamp_ = value;
      }
    }

    /// <summary>Field number for the "serverVersion" field.</summary>
    public const int ServerVersionFieldNumber = 3;
    private global::FengIotMqttService.ProtoBufModels.ProtocolVersion serverVersion_;
    /// <summary>
    ///服务端支持的最高协议版本，V1.2新增字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::FengIotMqttService.ProtoBufModels.ProtocolVersion ServerVersion {
      get { return serverVersion_; }
      set {
        serverVersion_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ServerCoordinate);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ServerCoordinate other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (LocalTimeStamp != other.LocalTimeStamp) return false;
      if (ServerTimeStamp != other.ServerTimeStamp) return false;
      if (!object.Equals(ServerVersion, other.ServerVersion)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (LocalTimeStamp != 0L) hash ^= LocalTimeStamp.GetHashCode();
      if (ServerTimeStamp != 0L) hash ^= ServerTimeStamp.GetHashCode();
      if (serverVersion_ != null) hash ^= ServerVersion.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (LocalTimeStamp != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(LocalTimeStamp);
      }
      if (ServerTimeStamp != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(ServerTimeStamp);
      }
      if (serverVersion_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(ServerVersion);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (LocalTimeStamp != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(LocalTimeStamp);
      }
      if (ServerTimeStamp != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(ServerTimeStamp);
      }
      if (serverVersion_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(ServerVersion);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (LocalTimeStamp != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(LocalTimeStamp);
      }
      if (ServerTimeStamp != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(ServerTimeStamp);
      }
      if (serverVersion_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(ServerVersion);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ServerCoordinate other) {
      if (other == null) {
        return;
      }
      if (other.LocalTimeStamp != 0L) {
        LocalTimeStamp = other.LocalTimeStamp;
      }
      if (other.ServerTimeStamp != 0L) {
        ServerTimeStamp = other.ServerTimeStamp;
      }
      if (other.serverVersion_ != null) {
        if (serverVersion_ == null) {
          ServerVersion = new global::FengIotMqttService.ProtoBufModels.ProtocolVersion();
        }
        ServerVersion.MergeFrom(other.ServerVersion);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            LocalTimeStamp = input.ReadInt64();
            break;
          }
          case 16: {
            ServerTimeStamp = input.ReadInt64();
            break;
          }
          case 26: {
            if (serverVersion_ == null) {
              ServerVersion = new global::FengIotMqttService.ProtoBufModels.ProtocolVersion();
            }
            input.ReadMessage(ServerVersion);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            LocalTimeStamp = input.ReadInt64();
            break;
          }
          case 16: {
            ServerTimeStamp = input.ReadInt64();
            break;
          }
          case 26: {
            if (serverVersion_ == null) {
              ServerVersion = new global::FengIotMqttService.ProtoBufModels.ProtocolVersion();
            }
            input.ReadMessage(ServerVersion);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 事件数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class RtdEvent : pb::IMessage<RtdEvent>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RtdEvent> _parser = new pb::MessageParser<RtdEvent>(() => new RtdEvent());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RtdEvent> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::FengIotMqttService.ProtoBufModels.MetaTageReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RtdEvent() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RtdEvent(RtdEvent other) : this() {
      topic_ = other.topic_;
      payload_ = other.payload_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RtdEvent Clone() {
      return new RtdEvent(this);
    }

    /// <summary>Field number for the "topic" field.</summary>
    public const int TopicFieldNumber = 1;
    private string topic_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Topic {
      get { return topic_; }
      set {
        topic_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "payload" field.</summary>
    public const int PayloadFieldNumber = 2;
    private pb::ByteString payload_ = pb::ByteString.Empty;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString Payload {
      get { return payload_; }
      set {
        payload_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RtdEvent);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RtdEvent other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Topic != other.Topic) return false;
      if (Payload != other.Payload) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Topic.Length != 0) hash ^= Topic.GetHashCode();
      if (Payload.Length != 0) hash ^= Payload.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Topic.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Topic);
      }
      if (Payload.Length != 0) {
        output.WriteRawTag(18);
        output.WriteBytes(Payload);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Topic.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Topic);
      }
      if (Payload.Length != 0) {
        output.WriteRawTag(18);
        output.WriteBytes(Payload);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Topic.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Topic);
      }
      if (Payload.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(Payload);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RtdEvent other) {
      if (other == null) {
        return;
      }
      if (other.Topic.Length != 0) {
        Topic = other.Topic;
      }
      if (other.Payload.Length != 0) {
        Payload = other.Payload;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Topic = input.ReadString();
            break;
          }
          case 18: {
            Payload = input.ReadBytes();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Topic = input.ReadString();
            break;
          }
          case 18: {
            Payload = input.ReadBytes();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 网关主动推送元数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class MetaTagUpdateSequence : pb::IMessage<MetaTagUpdateSequence>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MetaTagUpdateSequence> _parser = new pb::MessageParser<MetaTagUpdateSequence>(() => new MetaTagUpdateSequence());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MetaTagUpdateSequence> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::FengIotMqttService.ProtoBufModels.MetaTageReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MetaTagUpdateSequence() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MetaTagUpdateSequence(MetaTagUpdateSequence other) : this() {
      type_ = other.type_;
      version_ = other.version_;
      tags_ = other.tags_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MetaTagUpdateSequence Clone() {
      return new MetaTagUpdateSequence(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private global::FengIotMqttService.ProtoBufModels.UpdateType type_ = global::FengIotMqttService.ProtoBufModels.UpdateType.None;
    /// <summary>
    ///网关推送位号元数据的消息类型：1. 新增，2.删除，3.局部更新，4.全量更新
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::FengIotMqttService.ProtoBufModels.UpdateType Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "version" field.</summary>
    public const int VersionFieldNumber = 2;
    private string version_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Version {
      get { return version_; }
      set {
        version_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "tags" field.</summary>
    public const int TagsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::FengIotMqttService.ProtoBufModels.MetaTag> _repeated_tags_codec
        = pb::FieldCodec.ForMessage(26, global::FengIotMqttService.ProtoBufModels.MetaTag.Parser);
    private readonly pbc::RepeatedField<global::FengIotMqttService.ProtoBufModels.MetaTag> tags_ = new pbc::RepeatedField<global::FengIotMqttService.ProtoBufModels.MetaTag>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::FengIotMqttService.ProtoBufModels.MetaTag> Tags {
      get { return tags_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MetaTagUpdateSequence);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MetaTagUpdateSequence other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Type != other.Type) return false;
      if (Version != other.Version) return false;
      if(!tags_.Equals(other.tags_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Type != global::FengIotMqttService.ProtoBufModels.UpdateType.None) hash ^= Type.GetHashCode();
      if (Version.Length != 0) hash ^= Version.GetHashCode();
      hash ^= tags_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Type != global::FengIotMqttService.ProtoBufModels.UpdateType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Version.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Version);
      }
      tags_.WriteTo(output, _repeated_tags_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Type != global::FengIotMqttService.ProtoBufModels.UpdateType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Version.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Version);
      }
      tags_.WriteTo(ref output, _repeated_tags_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Type != global::FengIotMqttService.ProtoBufModels.UpdateType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (Version.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Version);
      }
      size += tags_.CalculateSize(_repeated_tags_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MetaTagUpdateSequence other) {
      if (other == null) {
        return;
      }
      if (other.Type != global::FengIotMqttService.ProtoBufModels.UpdateType.None) {
        Type = other.Type;
      }
      if (other.Version.Length != 0) {
        Version = other.Version;
      }
      tags_.Add(other.tags_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Type = (global::FengIotMqttService.ProtoBufModels.UpdateType) input.ReadEnum();
            break;
          }
          case 18: {
            Version = input.ReadString();
            break;
          }
          case 26: {
            tags_.AddEntriesFrom(input, _repeated_tags_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Type = (global::FengIotMqttService.ProtoBufModels.UpdateType) input.ReadEnum();
            break;
          }
          case 18: {
            Version = input.ReadString();
            break;
          }
          case 26: {
            tags_.AddEntriesFrom(ref input, _repeated_tags_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 服务端要求刷新网关位号数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class MetaNotifySequence : pb::IMessage<MetaNotifySequence>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MetaNotifySequence> _parser = new pb::MessageParser<MetaNotifySequence>(() => new MetaNotifySequence());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MetaNotifySequence> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::FengIotMqttService.ProtoBufModels.MetaTageReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MetaNotifySequence() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MetaNotifySequence(MetaNotifySequence other) : this() {
      type_ = other.type_;
      version_ = other.version_;
      tagnames_ = other.tagnames_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MetaNotifySequence Clone() {
      return new MetaNotifySequence(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private global::FengIotMqttService.ProtoBufModels.UpdateType type_ = global::FengIotMqttService.ProtoBufModels.UpdateType.None;
    /// <summary>
    ///期望网关响应时推送的消息类型。1. 新增，2.删除，3.局部更新，4.全量更新
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::FengIotMqttService.ProtoBufModels.UpdateType Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "version" field.</summary>
    public const int VersionFieldNumber = 2;
    private string version_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Version {
      get { return version_; }
      set {
        version_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "tagnames" field.</summary>
    public const int TagnamesFieldNumber = 3;
    private static readonly pb::FieldCodec<string> _repeated_tagnames_codec
        = pb::FieldCodec.ForString(26);
    private readonly pbc::RepeatedField<string> tagnames_ = new pbc::RepeatedField<string>();
    /// <summary>
    ///当网关推送的实时数据或业务方查询数据时，服务端发现未存在的位号清单。网关接收此清单时，务必推送此清单中的所有位号元数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> Tagnames {
      get { return tagnames_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MetaNotifySequence);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MetaNotifySequence other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Type != other.Type) return false;
      if (Version != other.Version) return false;
      if(!tagnames_.Equals(other.tagnames_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Type != global::FengIotMqttService.ProtoBufModels.UpdateType.None) hash ^= Type.GetHashCode();
      if (Version.Length != 0) hash ^= Version.GetHashCode();
      hash ^= tagnames_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Type != global::FengIotMqttService.ProtoBufModels.UpdateType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Version.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Version);
      }
      tagnames_.WriteTo(output, _repeated_tagnames_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Type != global::FengIotMqttService.ProtoBufModels.UpdateType.None) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Version.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Version);
      }
      tagnames_.WriteTo(ref output, _repeated_tagnames_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Type != global::FengIotMqttService.ProtoBufModels.UpdateType.None) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (Version.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Version);
      }
      size += tagnames_.CalculateSize(_repeated_tagnames_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MetaNotifySequence other) {
      if (other == null) {
        return;
      }
      if (other.Type != global::FengIotMqttService.ProtoBufModels.UpdateType.None) {
        Type = other.Type;
      }
      if (other.Version.Length != 0) {
        Version = other.Version;
      }
      tagnames_.Add(other.tagnames_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Type = (global::FengIotMqttService.ProtoBufModels.UpdateType) input.ReadEnum();
            break;
          }
          case 18: {
            Version = input.ReadString();
            break;
          }
          case 26: {
            tagnames_.AddEntriesFrom(input, _repeated_tagnames_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Type = (global::FengIotMqttService.ProtoBufModels.UpdateType) input.ReadEnum();
            break;
          }
          case 18: {
            Version = input.ReadString();
            break;
          }
          case 26: {
            tagnames_.AddEntriesFrom(ref input, _repeated_tagnames_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
