```javascript
// 设备注册 (计算任务)

// 公网环境网关从DMP获取信息地址：http://dmp.cmsrict.com:39997/connection/info，userName:dmpHarborCon、password:dmp@123
var url = 'http://dmp.cmsrict.com:39997/connection/info';
//示例 请求头
var headers = {
    'Accept': 'application/json',
    'userName':'dmpHarborCon',  // 固定参数，上移提供
    'password':'dmp@123'        // 固定参数，上移提供
}
//示例 请求参数
var body = {
    'deviceId':'5130385898960601088', // 网关简码-请根据（上移平台中配置）实际进行修改
    'password':'QV<Hb1ef'             // 网关Password-请根据实际（上移平台中配置）进行修改 
}
var resultObj = http.Post(url, headers, body);
return  resultObj;

```

```javascript
// 上电消息 (计算任务)

var topic = '/v1/5127899053907525632/p/j'; // 5127899053907525632 该参数由上移平台提供，为网关简码
var payload = {
    'et': dateTime.Now('cst'),
    'ip': '***********'
}
//mqtt.connect('fengedge.cn',1883,'5130385898960601088','fXku87ZZw7','TCl8mcNcdBjEleHE');
// MQTT发起连接，参数顺序为: ip,端口,clientId（该参数使用上移平台提供,为网关简码），连接用户账号（该参数在调用设备注册后返回），连接用户密码（该参数在调用设备注册后返回）
mqtt.connect('***************',39999,'5127899053907525632','Ax4zYkzLJR','yCCiOLJdOcB70Jyx');  // 5127899053907525632  请修改为上移平台实际提供的值，为网关简码
var pubRest = mqtt.Publish(topic, JSON.stringify(payload), 1, false)
// 手动断开连接
mqtt.DisConnect();
return pubRest
```

```javascript
// 心跳上报 /v1/5130385898960601088/s/j   5130385898960601088 上移提供网关简码，请根据实际替换

var deviceConnectList = device.Status();
var daList = [];
for (var deviceConnect of deviceConnectList) {
    var da = {};
    da['id'] = deviceConnect.DeviceName;
    da['ds'] = deviceConnect.OnLine == true ? 0 : 1;
    daList.push(da);
}

var payload = {
    'ip': '***********',
    'et': dateTime.Now('cst'),
    'da': daList
}
return JSON.stringify(payload);

```

```javascript
// 实时数据 /v1/5130385898960601088/d/j       5130385898960601088 上移提供网关简码，请根据实际替换

var data = payload;

//根据网关侧数据类型转换成上移平台需要的数据值
function getValue(object) {
    switch (object.ValueType) {
        case 'Int':
            return parseInt(object.Value);
        case 'Double':
            return parseFloat(object.Value);
        default:
            return object.Value;
    }
}

//属性配置
var tagValue = {};

for (var key in data.Params) {
    //循环转换数据值
    tagValue[key] = getValue(data.Params[key]);
}

var pubData = {
    "et": dateTime.Now('cst'),
    "da": [{
        'id': data.DeviceName,
        'da': tagValue
    }]
}

return JSON.stringify(pubData);
```




