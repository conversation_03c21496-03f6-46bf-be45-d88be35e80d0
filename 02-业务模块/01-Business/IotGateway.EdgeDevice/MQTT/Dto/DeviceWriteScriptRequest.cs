namespace IotGateway.Application;

/// <summary>
/// 设备操作下写计算点位
/// </summary>
public class DeviceWriteScriptRequest
{
    /// <summary>
    /// 设备Id
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 脚本内容
    /// </summary>
    public string Script { get; set; }
    /// <summary>
    /// 参数
    /// </summary>
    public Dictionary<string, string> Params { get; set; } = new();
}