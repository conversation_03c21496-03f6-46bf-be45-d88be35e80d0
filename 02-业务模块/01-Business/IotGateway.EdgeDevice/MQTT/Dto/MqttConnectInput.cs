namespace IotGateway.Application;

/// <summary>
///     mqtt连接参数
/// </summary>
public class MqttConnectInput
{
    /// <summary>
    ///     连接Ip
    /// </summary>
    public string Ip { get; set; }

    /// <summary>
    ///     连接端口
    /// </summary>
    public short Port { get; set; }

    /// <summary>
    ///     连接Id
    /// </summary>
    public string ClientId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    ///     账号
    /// </summary>
    public string User { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    public string Password { get; set; }
}