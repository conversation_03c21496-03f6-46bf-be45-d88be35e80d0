using Common.Enums;

namespace IotGateway.Application;

/// <summary>
///     设备属性
/// </summary>
public class MetaVariable
{
    /// <summary>
    ///     标识符
    /// </summary>
    public string Identifier { get; set; }

    /// <summary>
    ///     变量名
    /// </summary>
    [Display(Name = "变量名")]
    public string Name { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [Display(Name = "描述")]
    public string? Description { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    [Display(Name = "数据类型")]
    public TransPondDataTypeEnum TransitionType { get; set; }

    /// <summary>
    ///     权限
    /// </summary>
    [Display(Name = "权限")]
    public ProtectTypeEnum ProtectType { get; set; }

    /// <summary>
    ///     单位
    /// </summary>
    [Display(Name = "单位")]
    public string? Unit { get; set; }
}