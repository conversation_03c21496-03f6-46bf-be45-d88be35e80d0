namespace IotGateway.Mqtt;

/// <summary>
///     解析后的平台属性
/// </summary>
public class VariableSendToPlatform
{
    /// <summary>
    ///     设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    ///     毫秒时间戳
    /// </summary>
    public long ParentTime { get; set; }

    /// <summary>
    ///     属性
    /// </summary>
    public Dictionary<string, TransPondParamValue> Params { get; set; } = new(StringComparer.OrdinalIgnoreCase);

    /// <summary>
    ///     驱动名
    /// </summary>
    [JsonIgnore]
    public string DriverName { get; set; }
}

/// <summary>
/// </summary>
public class TransPondParamValue
{
    /// <summary>
    ///     值
    /// </summary>
    public string? Value { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    public TransPondDataTypeEnum DataType { get; set; }

    /// <summary>
    ///     数据类型*（脚本解析用到,禁止删除）
    /// </summary>
    public string ValueType => EnumUtil.GetEnumDesc(DataType);

    /// <summary>
    ///     毫秒时间戳
    /// </summary>
    public long Time { get; set; }
}