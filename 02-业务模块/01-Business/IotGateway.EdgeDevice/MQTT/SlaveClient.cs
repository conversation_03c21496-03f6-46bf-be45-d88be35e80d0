using IotGateway.Application.Entity;
using DateTime = System.DateTime;

namespace IotGateway.EdgeDevice;

/// <summary>
///     转发MQTT配置.
/// </summary>
public class SlaveClient : IDisposable
{
    private readonly MqttClientOptions _clientOptions = new();
    private readonly ELinkApplication _eLinkApplication;
    private readonly RootCloudApplicationMessageReceived _rootCloudApplication;
    private readonly SendMessageService _send;
    private readonly SuperOsApplication _superOsApplication;
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private readonly TransPond _transPond;

    // 连接管理相关字段
    private volatile bool _isReconnecting = false;
    private int _reconnectAttempts = 0;
    private DateTime _lastReconnectAttempt = DateTime.MinValue;
    private readonly object _reconnectLock = new object();
    private readonly object _publishLock = new object();
    private readonly SemaphoreSlim _publishSemaphore = new SemaphoreSlim(1, 1);
    private const int MaxReconnectAttempts = 5;
    private const int BaseReconnectDelayMs = 1000; // 基础重连延迟1秒

    /// <summary>
    ///     Mqtt最近运行连接记录
    /// </summary>
    /// <remarks>默认只保存 100 条</remarks>
    public Queue<RunRecordLine> Timelines { get; set; } = new();

    /// <summary>
    /// </summary>
    private IServiceProvider Services { get; }

    /// <summary>
    ///     最后一次上报时间
    /// </summary>
    private DateTime _lastSendTime = DateTime.MinValue;

    /// <summary>
    ///     脚本执行引擎
    /// </summary>
    private readonly Jint.Engine _engine = new();

    /// <summary>
    ///     MQTT 连接状态
    /// </summary>
    public bool IsConnected => Client?.IsConnected ?? false;

    /// <summary>
    ///     MQTT Client
    /// </summary>
    private IMqttClient Client { get; }

    // 推送断开状态时间
    private DateTime _lastDisConnectTime = DateTime.MinValue;

    /// <summary>
    /// </summary>
    public SlaveClient(IServiceProvider services, TransPond transPond, SendMessageService send, SuperOsApplication superOsApplication, ELinkApplication eLinkApplication,
        RootCloudApplicationMessageReceived rootCloudApplication)
    {
        Services = services;
        _send = send;
        _superOsApplication = superOsApplication;
        _eLinkApplication = eLinkApplication;
        _rootCloudApplication = rootCloudApplication;
        _transPond = transPond;
        // 元数据上报
        _ = SubscribeMeta();
        _ = Subscribe();
        try
        {
            // 初始化MQTT
            Client = new MqttFactory().CreateMqttClient();
            _clientOptions = new MqttClientOptionsBuilder()
                .WithClientId(_transPond.MqttConfModel?.ClientId)
                .WithTcpServer(_transPond.MqttConfModel?.Ip, _transPond.MqttConfModel?.Port)
                .WithCredentials(_transPond.MqttConfModel?.UserName, _transPond.MqttConfModel?.Password)
                .WithTimeout(TimeSpan.FromSeconds(120))
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(_transPond.MqttConfModel!.KeepAlive))
                .Build();

            // 消息回调
            Client.ApplicationMessageReceivedAsync += Client_ApplicationMessageReceived;
            // 连接成功
            Client.ConnectedAsync += x => OnConnected();
            Client.ConnectAsync(_clientOptions).GetAwaiter().GetResult();

            // 手动重启MQTT服务后都主动上报一次元数据
            if (Client.IsConnected)
            {
                var metaVariable = PublishMeta().GetAwaiter().GetResult();
                // slave上报元数据
                MessageCenter.PublishAsync(EventConst.DeviceMeta, metaVariable).GetAwaiter().GetResult();
            }
        }
        catch (Exception ex)
        {
            Log.Error("Slave Initialize FAILED," + ex.Message);
        }
    }

    /// <summary>
    ///     消息回调
    /// </summary>
    /// <param name="e"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private async Task Client_ApplicationMessageReceived(MqttApplicationMessageReceivedEventArgs e)
    {
        try
        {
            var topic = e.ApplicationMessage.Topic;
            var level = e.ApplicationMessage.QualityOfServiceLevel;
            var getTopic = _transPond?.TransPondTopic.FirstOrDefault(f => f.Topic == topic);
            if (getTopic == null) return;

            switch (_transPond?.MqttConfModel?.IoTPlatformType)
            {
                case IoTPlatformType.SupOS:
                    {
                        switch (getTopic.TransPondTopicPurpose)
                        {
                            case TransPondTopicPurposeEnum.Variable:
                                {
                                    var writeModel = ValueSequence.Parser.ParseFrom(e.ApplicationMessage.PayloadSegment.ToArray());
                                    await _superOsApplication.ChangeToDeviceWriteRequest(writeModel);
                                }
                                break;
                            case TransPondTopicPurposeEnum.MetaPush:
                                {
                                    var metaVariable = await PublishMeta();
                                    var metaTopic = _transPond.TransPondTopic.FirstOrDefault(f =>
                                        f.TransPondTopicPurpose == TransPondTopicPurposeEnum.MetaPushReply);
                                    if (metaTopic == null)
                                        return;
                                    switch (_transPond.MqttConfModel?.IoTPlatformType)
                                    {
                                        case IoTPlatformType.SupOS:
                                            {
                                                await _superOsApplication.Meta(metaVariable, Client, metaTopic);
                                                break;
                                            }
                                        case IoTPlatformType.MQTT:
                                            break;
                                    }
                                }
                                break;
                            case TransPondTopicPurposeEnum.CloudRequestRefreshRealTimeDeviceData:
                                {
                                    using var scope = Services.CreateScope();
                                    var deviceRepo = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<Device>>();
                                    var deviceList = await deviceRepo.AsQueryable().Where(w => w.Enable).Includes(w => w.DeviceVariable).ToListAsync();
                                    foreach (var device in deviceList)
                                        try
                                        {
                                            await MessageCenter.PublishAsync(string.Format(ConstMethod.CloudRequestRefreshRealTimeDeviceData, device.DeviceName), "");
                                        }
                                        catch (Exception exception)
                                        {
                                            Log.Error("【SupOS-Slave】云端要求刷新设备实时数据Error", topic, level, exception.Message);
                                        }
                                }
                                break;
                        }

                        break;
                    }
            }
        }
        catch (Exception ex)
        {
            Log.Error($"【Slave】 ClientId:{e.ClientId} Topic:{e.ApplicationMessage.Topic},Payload:{e.ApplicationMessage.ConvertPayloadToString()}," + ex);
        }
    }

    /// <summary>
    ///     发送网关测设备采集点配置消息
    /// </summary>
    private async Task<List<MetaVariable>> PublishMeta()
    {
        using var scope = Services.CreateScope();
        var deviceRepo = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<Device>>();
        var deviceList = await deviceRepo.AsQueryable().Where(w => w.Enable)
            .Includes(w => w.DeviceVariable)
            .ToListAsync();
        var metaVariable = new List<MetaVariable>();
        foreach (var device in deviceList)
            foreach (var variable in device.DeviceVariable.Where(w => w.Enable && w.SendType != SendTypeEnum.Never).ToList())
                try
                {
                    metaVariable.Add(new MetaVariable
                    {
                        Description = variable.Description,
                        Identifier = _transPond.Identifier + "/" + device.DeviceName + "/" + variable.Identifier,
                        Name = variable.Name,
                        ProtectType = variable.DeviceVariableEx.ProtectType,
                        TransitionType = variable.TransitionType,
                        Unit = variable.Unit
                    });
                }
                catch (Exception e)
                {
                    Log.Error($"解析设备属性出现问题,Error:{e.Message},StackTrace:【{e.StackTrace}】,variable:【{JSON.Serialize(variable)}】");
                }

        return metaVariable;
    }

    /// <summary>
    ///     释放MQTT连接
    /// </summary>
    public void Dispose()
    {
        _cancellationTokenSource.Cancel();
        _cancellationTokenSource.Dispose();
        Client.DisconnectAsync();
        Client.Dispose();
        _engine?.Dispose();
        _publishSemaphore?.Dispose();
    }

    /// <summary>
    ///     slave 订阅
    /// </summary>
    private async Task Subscribe()
    {
        var cancellationToken = _cancellationTokenSource.Token;
        await MessageCenter.Subscribe(EventConst.SendDeviceData, async context =>
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    return;

                // 发送消息
                var data = context.GetPayload<PayLoad>();
                await PublishAsync(data);
            }
            catch (Exception ex)
            {
                await _send.DeviceResp($"【MQTT实时数据】 Error:【{ex.Message}】", _transPond.Identifier);
            }

            await Task.CompletedTask;
        }, cancellationToken: cancellationToken);
    }

    /// <summary>
    ///     slave 订阅元数据上报
    /// </summary>
    private async Task SubscribeMeta()
    {
        var cancellationToken = _cancellationTokenSource.Token;
        await MessageCenter.Subscribe(EventConst.DeviceMeta, async context =>
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    return;
                // 发送消息
                var metaVariable = context.GetPayload<List<MetaVariable>>();
                var metaTopic = _transPond.TransPondTopic.FirstOrDefault(f => f.TransPondTopicPurpose == TransPondTopicPurposeEnum.Meta);
                if (metaTopic == null)
                    return;

                if (!string.IsNullOrEmpty(metaTopic.Config))
                    await _publishSemaphore.WaitAsync(cancellationToken);
                try
                {
                    _engine.SetValue("payload", metaVariable);
                    var execValue = _engine.Evaluate(metaTopic.Config ?? "", new ScriptParsingOptions { Tolerant = true, AllowReturnOutsideFunction = true }).ToObject();
                    if (execValue != null)
                    {
                        if (_transPond.MqttConfModel?.IoTPlatformType == IoTPlatformType.SupOS)
                            metaVariable = execValue.Adapt<List<MetaVariable>>();
                        else
                            try
                            {
                                //数据上送
                                await Client.PublishAsync(new MqttApplicationMessageBuilder()
                                    .WithTopic(metaTopic.Topic)
                                    .WithPayload(Encoding.UTF8.GetBytes(execValue.ToString()))
                                    .WithQualityOfServiceLevel(metaTopic.Qos).Build(), cancellationToken);
                                Log.Information($"【MQTT】 上报元数据：{execValue}");
                            }
                            catch (Exception ex)
                            {
                                Log.Warning($"【MQTT】 上报元数据失败：{ex.Message}");
                            }
                    }
                }
                finally
                {
                    _publishSemaphore.Release();
                }

                switch (_transPond.MqttConfModel?.IoTPlatformType)
                {
                    case IoTPlatformType.SupOS:
                        {
                            await _superOsApplication.Meta(metaVariable, Client, metaTopic);
                            break;
                        }
                    default:
                        await Client.PublishAsync(new MqttApplicationMessageBuilder()
                            .WithTopic(metaTopic.Topic)
                            .WithPayload(Encoding.UTF8.GetBytes(metaVariable.ToJson()))
                            .WithQualityOfServiceLevel(metaTopic.Qos).Build(), cancellationToken);
                        Log.Information("【MQTT】 上报元数据");
                        break;
                }
            }
            catch (Exception ex)
            {
                await _send.DeviceResp($"【Slave属性上报】 Error:【{ex.Message}】", _transPond.Identifier);
            }

            await Task.CompletedTask;
        }, cancellationToken: cancellationToken);
    }

    /// <summary>
    /// </summary>
    [SuppressMonitor]
    private async Task OnConnected()
    {
        AddToTimeline("MQTT连接成功");
        await _send.DeviceResp("【Slave】MQTT连接成功", _transPond.Identifier);

        try
        {
            foreach (var topic in _transPond.TransPondTopic.Where(w => w.TransPondTopicType == TransPondTopicTypeEnum.Sub))
            {
                await Client.SubscribeAsync(topic.Topic, MqttQualityOfServiceLevel.AtLeastOnce, _cancellationTokenSource.Token);
                Log.Information($"【Slave】 MQTT订阅Topic:【{topic.TransPondTopicTypeName},{topic.Topic}】,成功");
            }
        }
        catch (Exception e)
        {
            Log.Error($"【Slave】 订阅Topic异常:【{e.Message}】");
        }
    }

    /// <summary>
    ///     添加连接事件
    /// </summary>
    /// <param name="message"></param>
    private void AddToTimeline(string message)
    {
        if (Timelines.Count > 200)
            Timelines.Dequeue();

        Timelines.Enqueue(new RunRecordLine
        {
            Time = Common.Extension.DateTime.ShangHai(),
            Message = message
        });
    }

    /// <summary>
    /// 获取重连延迟时间（指数退避）
    /// </summary>
    /// <returns>延迟毫秒数</returns>
    private int GetReconnectDelay()
    {
        return Math.Min(BaseReconnectDelayMs * (int)Math.Pow(2, _reconnectAttempts), 30000); // 最大30秒
    }

    /// <summary>
    /// 检查是否可以进行重连
    /// </summary>
    /// <returns>是否可以重连</returns>
    private bool CanAttemptReconnect()
    {
        if (_reconnectAttempts >= MaxReconnectAttempts)
            return false;

        var delay = GetReconnectDelay();
        return (Common.Extension.DateTime.Now() - _lastReconnectAttempt).TotalMilliseconds >= delay;
    }

    /// <summary>
    /// 尝试异步重连MQTT
    /// </summary>
    /// <returns>重连是否成功</returns>
    private async Task<bool> TryReconnectAsync()
    {
        if (_isReconnecting || !CanAttemptReconnect())
            return Client?.IsConnected ?? false;

        lock (_reconnectLock)
        {
            if (_isReconnecting)
                return Client?.IsConnected ?? false;

            _isReconnecting = true;
            _lastReconnectAttempt = Common.Extension.DateTime.Now();
            _reconnectAttempts++;
        }

        try
        {
            if (!IsConnected)
                AddToTimeline("MQTT断开连接");

            if (Client != null)
            {
                await Client.ConnectAsync(_clientOptions);
                if (Client.IsConnected)
                {
                    _reconnectAttempts = 0; // 重连成功，重置计数器
                    AddToTimeline("MQTT重连成功");
                    return true;
                }
            }
        }
        catch (Exception e)
        {
            await _send.DeviceResp($"【Slave】MQTT重新连接失败,Error:【{e.Message}】", _transPond.Identifier);
        }
        finally
        {
            _isReconnecting = false;
        }

        return false;
    }

    /// <summary>
    ///     将采集数据解析成不同平台需要的转发格式数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task PublishAsync(PayLoad input)
    {
        try
        {
            if (_transPond == null)
            {
                await _send.Send("MQTT主节点未配置,数据丢弃", input.DeviceName + "Resp");
                return;
            }

            switch (_transPond.MqttConfModel?.SendType)
            {
                case TransPondSendTypeEnum.Always:
                    {
                        input.Values = input.Values.Where(i => i.Value.Value != null)
                            .ToDictionary(i => i.Key, i => i.Value);
                        break;
                    }
                case TransPondSendTypeEnum.PubPeriod:
                    {
                        // 转发间隔
                        var pubPeriod = _transPond.MqttConfModel.PubPeriodUnit == "毫秒" ? _transPond.MqttConfModel.PubPeriod
                            : _transPond.MqttConfModel.PubPeriodUnit == "秒" ? _transPond.MqttConfModel.PubPeriod * 1000
                            : _transPond.MqttConfModel.PubPeriodUnit == "分钟" ? _transPond.MqttConfModel.PubPeriod * 1000 * 60
                            : _transPond.MqttConfModel.PubPeriod * 1000 * 60 * 60;
                        var nowTime = Common.Extension.DateTime.Now();
                        // 周期上报是否达到要求时间,未达到就不上报数据
                        if ((nowTime - _lastSendTime).TotalMilliseconds < pubPeriod)
                            return;
                        _lastSendTime = nowTime;

                        input.Values = input.Values.Where(i => i.Value.Value != null)
                            .ToDictionary(i => i.Key, i => i.Value);
                        break;
                    }
                case TransPondSendTypeEnum.Changed:
                    {
                        input.Values = input.Values.Where(i =>
                                i.Value.CookieValue != null &&
                                ((i.Value.Value != null && i.Value.CookieValue.ToString() != i.Value.Value) ||
                                 (i.Value.CookieValue == null && i.Value.Value != null)))
                            .ToDictionary(i => i.Key, i => i.Value);
                        break;
                    }
            }

            var pushVariable = new VariableSendToPlatform
            {
                ParentTime = input.Ts,
                DriverName = input.DriverName,
                DeviceName = input.DeviceName,
                Params = new Dictionary<string, TransPondParamValue>()
            };
            foreach (var (identifier, value) in input.Values)
            {
                var newIdentifier = identifier;
                // 不同平台的拼接名称处理
                switch (_transPond?.MqttConfModel?.IoTPlatformType)
                {
                    case IoTPlatformType.SupOS:
                        newIdentifier = _transPond.Identifier + "/" + input.DeviceName + "/" + identifier;
                        // newIdentifier = input.DeviceName + "_" + identifier;
                        break;
                }

                pushVariable.Params.Add(newIdentifier, new TransPondParamValue
                {
                    Value = value.Value,
                    Time = value.ReadTime,
                    DataType = value.TransitionType
                });
            }

            await PublishOnLineAsync(pushVariable);
        }
        catch (Exception ex)
        {
            await _send.DeviceResp($"设备:【{input.DeviceName}】,解析实时数据失败,原因:【{ex.Message}】", _transPond.Identifier);
        }
    }


    /// <summary>
    ///     发送实时采集消息
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private async Task PublishOnLineAsync(VariableSendToPlatform input)
    {
        try
        {
            MqttClientPublishResult? onLineSuccess = null;
            var onLine = _transPond.TransPondTopic.FirstOrDefault(f => f.TransPondTopicPurpose == TransPondTopicPurposeEnum.OnLine);
            if (onLine == null)
                return;
            var onLineTopic = onLine.Topic;
            if (!Client.IsConnected)
            {
                // 每5秒推送一次断开状态,减少流量
                if ((Common.Extension.DateTime.Now() - _lastDisConnectTime).TotalSeconds > 5)
                {
                    _lastDisConnectTime = Common.Extension.DateTime.Now();
                    _ = _send.DeviceResp("【Slave】MQTT断开连接", _transPond.Identifier);
                }

                // 尝试异步重连
                var reconnectSuccess = await TryReconnectAsync();
                if (!reconnectSuccess)
                    return;
            }

            try
            {
                switch (_transPond?.MqttConfModel?.IoTPlatformType)
                {
                    case IoTPlatformType.SupOS:
                        onLineSuccess = await _superOsApplication.PublishSupOsAsync(input, onLine, Client, _transPond);
                        break;
                    case IoTPlatformType.RootCloud:
                        onLineSuccess =
                            await _rootCloudApplication.PublishRootCloudAsync(input, onLine, Client, _transPond);
                        break;
                    case IoTPlatformType.MQTT:
                        {
                            if (onLine.Rule == TransPondTopicRuleEnum.Dynamic)
                            {
                                // 设备名称
                                if (onLineTopic.Contains("${deviceName}"))
                                    onLineTopic = onLineTopic.Replace("${deviceName}", input.DeviceName);
                                // 转发标识符
                                if (onLineTopic.Contains("${transPondId}"))
                                    onLineTopic = onLineTopic.Replace("${transPondId}", _transPond.Identifier);
                                // 协议名称
                                if (onLineTopic.Contains("${driverName}"))
                                    onLineTopic = onLineTopic.Replace("${driverName}", input.DriverName);
                            }

                            _engine.SetValue("payload", input);
                            if (onLine.Config is null or "")
                            {
                                onLineSuccess = _eLinkApplication.PublishAsync(JSON.Serialize(input), onLine, Client)
                                    .GetAwaiter().GetResult();
                            }
                            else
                            {
                                var getValue = _engine.Evaluate(onLine.Config, new ScriptParsingOptions { Tolerant = true, AllowReturnOutsideFunction = true })
                                    .ToObject();
                                if (getValue != null)
                                    onLineSuccess = _eLinkApplication.PublishAsync(getValue.ToString(), onLine, Client)
                                        .GetAwaiter().GetResult();
                            }

                            break;
                        }
                    case IoTPlatformType.ELink:
                        {
                            onLineSuccess = await _eLinkApplication.PublishAsync(JSON.Serialize(input), onLine, Client);
                            break;
                        }
                    case IoTPlatformType.IotSuite:
                        {
                            //todo 
                            break;
                        }
                }
            }
            catch (Exception e)
            {
                await _send.DeviceResp(
                    $"【ELink】 设备:【{input.DeviceName}】,推送到平台失败,上送Topic:【{onLineTopic}】,失败原因:【{e.Message}】",
                    _transPond.Identifier);
            }

            if (onLineSuccess?.ReasonCode == MqttClientPublishReasonCode.Success)
            {
                await _send.DeviceResp($"设备:【{input.DeviceName}】,MQTT推送实时数据成功,Topic:【{onLineTopic}】",
                    _transPond.Identifier);
            }
            else
            {
                if (onLineSuccess != null)
                    await _send.DeviceResp(
                        $"设备:【{input.DeviceName}】,MQTT送实时数据失败,上送Topic:【{onLineTopic}】,失败原因:【{onLineSuccess.ReasonString}】",
                        _transPond.Identifier);
            }
        }
        catch (Exception ex)
        {
            Log.Error($"设备:【{input.DeviceName}】, MQTT 推送实时数据Error:【{ex.Message}】");
        }
    }
}