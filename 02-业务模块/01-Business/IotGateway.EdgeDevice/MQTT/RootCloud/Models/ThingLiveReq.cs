namespace IotGateway.Application;

/// <summary>
///     实时数据上报参数
/// </summary>
public class ThingLiveReq
{
    /// <summary>
    /// </summary>
    [JsonPropertyName("body")]
    public LiveBodyReq Body { get; set; }
}

/// <summary>
/// </summary>
public class LiveBodyReq
{
    /// <summary>
    /// </summary>
    [JsonPropertyName("things")]
    public List<ThingsReq> Things { get; set; }
}

/// <summary>
/// </summary>
public class ThingsReq
{
    /// <summary>
    ///     在不同连接场景中,填写的内容有所不同,详细请参见概述中“id 填写规则”的内容。
    /// </summary>
    [JsonPropertyName("id")]
    public string Id { get; set; }

    /// <summary>
    ///     物的类型:
    ///     - Device:表示直连设备或非直连设备。
    ///     - Gateway:表示网关
    /// </summary>
    [JsonPropertyName("thingType")]
    public string ThingType { get; set; }

    /// <summary>
    ///     每一个 thing 都至少有一个项目。
    /// </summary>
    [JsonPropertyName("items")]
    public List<BodyItemsReq> Items { get; set; }
}

/// <summary>
/// </summary>
public class BodyItemsReq
{
    /// <summary>
    ///     质量戳 quality stamp,表示某个属性的值不可信。 多个属性时,以逗号隔开,
    ///     例如"qBad":["tag1","tag2"] ,其中 tag1, tag2 是物模型的属性。 不存该属性时,该字段为空,例如"qBad":[]
    /// </summary>
    [JsonPropertyName("qBad")]
    public List<string> QBad { get; set; }

    /// <summary>
    ///     从 UTC 时间 1970 年 1 月 1 日 0 时 0 分 0 秒起至现在的总毫秒数。若不填写,平台将自动使用服务器接收到数据的时间作为时间戳。
    /// </summary>
    [JsonPropertyName("ts")]
    public long Ts { get; set; }

    /// <summary>
    ///     属性集,填入模型属性的连接变量。 平台对 item 的判断遵循以下方式:
    ///     1、如果是值类型就直接取值,包括:integer, number, string, boolean。
    ///     2、如果是 object 类型,需要判断"type",取"value"的属性值。（平台暂不支持 object类型的属性）
    ///     3、如果是数组类型,则对每一个数组中的元素执行上述方式1~3 的判断
    /// </summary>
    [JsonPropertyName("properties")]
    public Dictionary<string, object> Properties { get; set; }
}