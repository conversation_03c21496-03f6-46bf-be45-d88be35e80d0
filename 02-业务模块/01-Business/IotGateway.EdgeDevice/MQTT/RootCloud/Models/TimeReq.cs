namespace IotGateway.Application;

/// <summary>
///     时间同步请求参数
/// </summary>
public class TimeReq
{
    /// <summary>
    ///     请求头
    /// </summary>
    [JsonPropertyName("header")]
    public HeaderReq Header { get; set; }
}

/// <summary>
///     请求头内容
/// </summary>
public class HeaderReq
{
    /// <summary>
    ///     消息 ID,建议使用设备随机生成的 UUID。
    /// </summary>
    [JsonPropertyName("msgId")]
    public string MsgId { get; set; }

    /// <summary>
    ///     从 UTC 时间 1970 年 1 月 1 日 0 时 0 分 0 秒起至现在的总毫秒数。
    /// </summary>
    [JsonPropertyName("ts")]
    public long Ts { get; set; }
}