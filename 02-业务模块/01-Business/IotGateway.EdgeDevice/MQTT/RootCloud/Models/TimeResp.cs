namespace IotGateway.Application;

/// <summary>
///     时间同步响应结果
/// </summary>
public class TimeResp
{
    /// <summary>
    ///     报文头部
    /// </summary>
    [JsonPropertyName("header")]
    public HeaderResp Header { get; set; }

    /// <summary>
    ///     请求主体
    /// </summary>
    [JsonPropertyName("body")]
    public BodyResp Body { get; set; }
}

/// <summary>
///     报文头部内容
/// </summary>
public class HeaderResp
{
    /// <summary>
    ///     消息 ID,由平台生成。
    /// </summary>
    [JsonPropertyName("msgId")]
    public string MsgId { get; set; }

    /// <summary>
    ///     从 UTC 时间 1970 年 1 月 1 日 0 时 0 分 0 秒起至现在的总毫秒数
    /// </summary>
    [JsonPropertyName("ts")]
    public long Ts { get; set; }
}

/// <summary>
///     请求主体内容
/// </summary>
public class BodyResp
{
    /// <summary>
    ///     返回值:
    ///     - 0:成功,The operation succeeded.
    ///     - 1:错误,The operation succeeded but there are one or more errors.
    /// </summary>
    [JsonPropertyName("hasError")]
    public int HasError { get; set; }

    /// <summary>
    ///     处理结果,如果 hasError>0,则 results 为 null。
    /// </summary>
    [JsonPropertyName("results")]
    public ResultsResp Results { get; set; }
}

/// <summary>
/// </summary>
public class ResultsResp
{
    /// <summary>
    ///     这个时间时设备发送的本地时间戳,精确到毫秒
    /// </summary>
    [JsonPropertyName("deviceSendTime")]
    public long DeviceSendTime { get; set; }

    /// <summary>
    ///     平台收到的时间,精确到毫秒
    /// </summary>
    [JsonPropertyName("hubRecvTime")]
    public long HubRecvTime { get; set; }

    /// <summary>
    ///     平台发送的时间,精确到毫秒。
    /// </summary>
    [JsonPropertyName("hubSendTime")]
    public long HubSendTime { get; set; }
}