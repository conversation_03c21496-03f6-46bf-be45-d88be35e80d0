using IotGateway.Application.Entity;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Mqtt;

/// <summary>
///     树根云平台消息回调
/// </summary>
public class RootCloudApplicationMessageReceived : ITransient
{
    private readonly SendMessageService _send;

    /// <summary>
    ///     最后一次同步消息Id
    /// </summary>
    private string _lastMsgId = "";

    public RootCloudApplicationMessageReceived(SendMessageService send)
    {
        _send = send;
    }

    /// <summary>
    ///     同步时间
    /// </summary>
    /// <param name="payLoad"></param>
    public async Task ReceiveSysTime(string payLoad)
    {
        var timeResp = JSON.Deserialize<TimeResp>(payLoad);
        if (timeResp == null) return;
        if (timeResp.Body.HasError > 0)
            Log.Error($"【RootCloud】同步时间返回结果失败:【{payLoad}】");

        if (_lastMsgId == timeResp.Header.MsgId)
            return;
        _lastMsgId = timeResp.Header.MsgId;
        //设备发起请求时间
        var deviceTime = DateTime.ToTime(timeResp.Body.Results.DeviceSendTime);
        //平台返回时间
        var hubTime = DateTime.ToTime(timeResp.Body.Results.HubSendTime);
        Log.Information($"RootCloud同步时间返回结果:【{payLoad}】,平台发送的时间：{hubTime}," +
                        $"设备发送的本地时间：{deviceTime}," +
                        $"平台收到的时间：{DateTime.ToTime(timeResp.Body.Results.HubRecvTime)} ");

        //当前时间
        var nowTime = DateTime.Now();
        Log.Information($"同步开始时间【{nowTime}】");
        //当前时间 - 设备发起同步时间 = 差异时间
        var diffTime = (nowTime - deviceTime).TotalMilliseconds;

        //当前时间 - (平台返回时间+本地发起请求差异时间) > 1秒 说明本地时间比服务器时间快，需要改慢 就要把现在读到的数据都丢弃掉
        if ((nowTime - hubTime.AddMilliseconds(diffTime)).TotalSeconds > 1)
            //删除缓存数据
            await MessageCenter.PublishAsync(EventConst.DeleteCache,
                DateTime.ToLong(hubTime.AddMilliseconds(diffTime)));
        var setTime = hubTime.AddMilliseconds(diffTime + (DateTime.Now() - nowTime).TotalMilliseconds);
        Log.Information($"同步结束时间【{DateTime.Now()}】,设置本地时间:【{setTime}】");
        await ShellUtil.Bash($"date -s \"{setTime:yyyy-MM-dd HH:mm:ss}\" && hwclock -w --systohc ");
    }

    /// <summary>
    ///     发送数据到RootCloud平台
    /// </summary>
    /// <param name="input">发送消息内容</param>
    /// <param name="topic">发送Topic</param>
    /// <param name="client"></param>
    /// <param name="transPond"></param>
    /// <returns></returns>
    public async Task<MqttClientPublishResult?> PublishRootCloudAsync(VariableSendToPlatform input, TransPondTopic topic, IMqttClient client, TransPond transPond)
    {
        if (!client.IsConnected) return null;

        var thingLiveReq = new ThingLiveReq
        {
            Body = new LiveBodyReq { Things = new List<ThingsReq>() }
        };
        var data = new ThingsReq { Id = input.DeviceName, Items = new List<BodyItemsReq>(), ThingType = "Device" };
        var bodyItem = new BodyItemsReq
        {
            Ts = Convert.ToInt64(input.ParentTime),
            QBad = new List<string>(),
            Properties = new Dictionary<string, object>()
        };
        foreach (var (key, item) in input.Params)
            try
            {
                if (item.Value == null) continue;
                switch (item.DataType)
                {
                    case TransPondDataTypeEnum.Bool:
                        bodyItem.Properties.Add(key, Convert.ToBoolean(item.Value));
                        break;
                    case TransPondDataTypeEnum.Int:
                        bodyItem.Properties.Add(key, Convert.ToInt64(item.Value));
                        break;
                    case TransPondDataTypeEnum.Double:
                        bodyItem.Properties.Add(key, Convert.ToDouble(item.Value));
                        break;
                    case TransPondDataTypeEnum.String:
                        bodyItem.Properties.Add(key, item.Value);
                        break;
                    case TransPondDataTypeEnum.Bytes:
                        break;
                }
            }
            catch (Exception ex)
            {
                await _send.DeviceResp(
                    $"【RootCloud】解析数据 设备:【{input.DeviceName}】 Key:【{key}】 值:【{item.Value}】，转换类型:【{item.DataType}】 Error:【{ex.Message}】",
                    transPond.Identifier);
            }

        data.Items.Add(bodyItem);
        thingLiveReq.Body.Things.Add(data);

        try
        {
            var message = new MqttApplicationMessage
            {
                Topic = topic.Topic,
                Payload = Encoding.UTF8.GetBytes(JSON.Serialize(thingLiveReq)),
                QualityOfServiceLevel = topic.Qos, // 跟云平台统一使用QOS1,不允许修改
                Retain = false
            };
            if (topic.TimeOut == 0)
                return await client.PublishAsync(message);
            var ct = new CancellationTokenSource(TimeSpan.FromSeconds(topic.TimeOut)).Token;
            return await client.PublishAsync(message, ct);
        }
        catch (Exception e)
        {
            throw Oops.Bah($"发送超时:{e.Message}");
        }
    }
}