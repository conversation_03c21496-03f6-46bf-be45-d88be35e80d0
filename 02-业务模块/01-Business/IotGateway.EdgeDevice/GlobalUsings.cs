global using Furion;
global using Furion.DependencyInjection;
global using Furion.DynamicApiController;
global using Furion.FriendlyException;
global using Mapster;
global using Microsoft.AspNetCore.Http;
global using Microsoft.AspNetCore.Mvc;
global using SqlSugar;
global using System.ComponentModel.DataAnnotations;
global using Feng.IotGateway.Core.Base;
global using System.Text.Json.Serialization;
global using Feng.IotGateway.Core.Enums;
global using Feng.IotGateway.Core.Util;
global using Feng.IotGateway.Core.Entity;
global using System.Reflection;
global using DriversInterface;
global using Feng.IotGateway.Core.SqlSugar;
global using Yitter.IdGenerator;
global using Furion.DatabaseAccessor;
global using Microsoft.Extensions.Hosting;
global using DynamicExpresso;
global using Feng.IotGateway.WebSocket;
global using Microsoft.Extensions.DependencyInjection;
global using Furion.JsonSerialization;
global using System.Diagnostics;
global using Furion.LinqBuilder;
global using Feng.IotGateway.Core.Const;
global using Feng.IotGateway.WebSocket.Const;
global using Feng.Common.Util;
global using Furion.EventBus;
global using Furion.Logging;
global using Driver.Core.Attributes;
global using Driver.Core.Models;
global using Feng.Common.Extension;
global using System.Globalization;
global using Driver.Core.Write.Dto;
global using Feng.IotGateway.Application.Drivers.Dtos;
global using Console = Feng.Common.Extension.Console;
global using System.Collections.Concurrent;
global using Jint;
global using Feng.IotGateway.Core.Extension;
global using Furion.TaskQueue;
global using Microsoft.AspNetCore.Authorization;
global using System.ComponentModel;
global using Furion.Templates;
global using NewLife.Threading;
global using TDengIne;
global using Common.Enums;
global using Common.Models;
global using Driver.Core;
global using Feng.IotGateway.Core.Service.Config;
global using IotGateway.Engine;
global using IotGateway.MQTT;
global using FengIotMqttService.ProtoBufModels;
global using Google.Protobuf;
global using IotGateway.Application;
global using MQTTnet.Protocol;
global using MQTTnet;
global using MQTTnet.Client;
global using System.Text;
global using Feng.IotGateway.Core.Models;
global using IotGateway.Mqtt;
global using System.Net.NetworkInformation;
global using System.Runtime.InteropServices;
global using Furion.DataEncryption;
global using System.Net;
global using System.Text.RegularExpressions;
global using HslCommunication.BasicFramework;