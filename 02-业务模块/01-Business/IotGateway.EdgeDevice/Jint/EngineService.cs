using Common.Extension;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.EdgeDevice;

/// <summary>
///     执行脚本
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2022-10-01
/// </summary>
[ApiDescriptionSettings("边缘计算")]
public class EngineService : ITransient, IDynamicApiController
{
    private readonly EngineSingletonService _engine;
    private readonly SqlSugarRepository<Device> _device;

    public EngineService(EngineSingletonService engine, SqlSugarRepository<Device> device)
    {
        _engine = engine;
        _device = device;
    }

    /// <summary>
    ///     设备调试-执行自定义脚本
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/engine/action")]
    [Obsolete]
    public async Task<ActionOutput> DeviceAction([FromBody] DeviceActionInput input)
    {
        var device = await _device.AsQueryable().FirstAsync(f => f.Id == input.DeviceId);
        if (device != null)
            if (input.Content.Contains("${this.DeviceName}"))
                input.Content = input.Content.Replace("${this.DeviceName}", $"{device.DeviceName}");

        var completionValue = _engine.Engine.Evaluate(input.Content, new ScriptParsingOptions {Tolerant = true, AllowReturnOutsideFunction = true}).ToObject();
        // 处理Nan的情况
        if (completionValue is double && double.IsNaN(Convert.ToDouble(completionValue)))
            completionValue = 0;

        var result = new ActionOutput {Value = completionValue, Logs = _engine.Log.Logs};
        return result;
    }

    /// <summary>
    ///     其他执行脚本-执行自定义脚本
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/engine/other/action")]
    [Obsolete]
    public Task<ActionOutput> Action([FromBody] ActionInput input)
    {
        var completionValue = _engine.Engine.Evaluate(input.Content, new ScriptParsingOptions {Tolerant = true, AllowReturnOutsideFunction = true}).ToObject();
        // 处理Nan的情况
        if (completionValue is double && double.IsNaN(Convert.ToDouble(completionValue)))
            completionValue = 0;
        var result = new ActionOutput {Value = completionValue, Logs = _engine.Log.Logs};
        return Task.FromResult(result);
    }

    /// <summary>
    ///     执行带参数脚本
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/v2/engine/action")]
    public async Task<ActionOutput> ActionScriptConValue(ActionScriptConValueInput input)
    {
        if (input.DeviceId > 0)
        {
            var device = await _device.AsQueryable().FirstAsync(f => f.Id == input.DeviceId);
            if (device != null && input.Content.Contains("${this.DeviceName}"))
                input.Content = input.Content.Replace("${this.DeviceName}", device.DeviceName);
        }

        foreach (var (key, val) in input.Value)
            try
            {
                if (val == null)
                    continue;
                _engine.Engine.SetValue(key, val.GetJsonElementValue());
            }
            catch (Exception)
            {
                // ignored
            }

        var completionValue = _engine.Engine.Evaluate(input.Content, new ScriptParsingOptions {Tolerant = true, AllowReturnOutsideFunction = true}).ToObject();
        if (completionValue is double && double.IsNaN(Convert.ToDouble(completionValue)))
            completionValue = 0;
        var result = new ActionOutput {Value = completionValue, Logs = _engine.Log.Logs};
        return result;
    }

    /// <summary>
    ///     转发Topic自定义解析测试
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/engine/dataRun")]
    public Task<dynamic> TransmitRun([FromBody] TransmitRunInput input)
    {
        var dataNull = input.Data == null;
        switch (input.Type)
        {
            case 1 or 2:
            {
                input.Data ??= new 
                {
                    ParentTime = DateTime.TimeStamp(),
                    DeviceName = "NS99",
                    Params = new Dictionary<string, dynamic>
                    {
                        {"edge/NS99/product", new  {Value = "8888", DataType = TransPondDataTypeEnum.Int, Time = DateTime.TimeStamp()}},
                        {"edge/NS99/length", new  {Value = "16.223", DataType = TransPondDataTypeEnum.Double, Time = DateTime.TimeStamp()}},
                        {"edge/NS99/message", new  {Value = "hello,Test", DataType = TransPondDataTypeEnum.String, Time = DateTime.TimeStamp()}},
                        {"edge/NS99/warning", new  {Value = "True", DataType = TransPondDataTypeEnum.Bool, Time = DateTime.TimeStamp()}}
                    },
                    DriverName = "ModbusTcp"
                };
                var jsonString = input.Data.ToString();
                var variable = dataNull ?  input.Data : jsonString?.ToObject<dynamic>();
                _engine.Engine.SetValue("payload", variable);
                _engine.Engine.SetValue("time", DateTime.ShangHai());
                var completionValue = _engine.Engine.Evaluate(input.Content, new ScriptParsingOptions {Tolerant = true, AllowReturnOutsideFunction = true}).ToObject();
                return Task.FromResult<dynamic>(completionValue);
            }
            case 3:
            {
                input.Data ??= new List<dynamic>
                {
                    new
                    {
                        Identifier = "edge/NS99/product",
                        TransitionType = TransPondDataTypeEnum.Int,
                        Name = "产量",
                        Unit = "个",
                        Description = "",
                        ProtectType = ProtectTypeEnum.ReadOnly
                    },
                    new
                    {
                        Identifier = "edge/NS99/status",
                        TransitionType = TransPondDataTypeEnum.Int,
                        Name = "状态",
                        Unit = "",
                        Description = "",
                        ProtectType = ProtectTypeEnum.ReadOnly
                    },
                    new
                    {
                        Identifier = "edge/NS99/temperature",
                        TransitionType = TransPondDataTypeEnum.Double,
                        Name = "温度",
                        Unit = "",
                        Description = "",
                        ProtectType = ProtectTypeEnum.ReadOnly
                    },
                    new
                    {
                        Identifier = "edge/NS99/IsAlarm",
                        TransitionType = TransPondDataTypeEnum.Bool,
                        Name = "是否报警",
                        Unit = "",
                        Description = "",
                        ProtectType = ProtectTypeEnum.ReadOnly
                    }
                };

                var jsonString = input.Data.ToString();
                var variable = dataNull ? (List<dynamic>) input.Data : jsonString?.ToObject<List<dynamic>>();
                _engine.Engine.SetValue("payload", variable);
                _engine.Engine.SetValue("guid", Guid.NewGuid());
                var completionValue = _engine.Engine.Evaluate(input.Content, new ScriptParsingOptions {Tolerant = true, AllowReturnOutsideFunction = true}).ToObject();
                return Task.FromResult<dynamic>(completionValue);
            }
            default:
            {
                _engine.Engine.SetValue("time", DateTime.ShangHai());
                var completionValue = _engine.Engine.Evaluate(input.Content, new ScriptParsingOptions {Tolerant = true, AllowReturnOutsideFunction = true}).ToObject();
                return Task.FromResult<dynamic>(completionValue);
            }
        }
    }
}