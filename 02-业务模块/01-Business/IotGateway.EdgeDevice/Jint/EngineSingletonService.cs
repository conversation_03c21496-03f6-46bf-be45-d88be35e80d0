using IotGateway.Application.Exec;
using IotGateway.EdgeDevice.Exec;
using IotGateway.Engine.Exec;

namespace IotGateway.Engine;

/// <summary>
///     执行脚本底层单例服务
/// </summary>
public class EngineSingletonService : ITransient
{
    public readonly DeviceExec Device;
    public readonly Share Share;
    public readonly Jint.Engine Engine;
    public readonly LogExtension Log;

    /// <summary>
    /// </summary>
    /// <param name="share">变量操作</param>
    /// <param name="device">采集设备动作</param>
    /// <param name="dateTimeEx">时间拓展类</param>
    /// <param name="systemEngine">系统方法</param>
    /// <param name="serialExec">串口相关函数</param>
    /// <param name="tcpExec">tcp连接</param>
    /// <param name="tDengIne">时序库查询</param>
    /// <param name="fileEx">文件操作</param>
    /// <param name="smbFileEx">smb文件操作</param>
    /// <param name="ftpFileEx">ftp</param>
    public EngineSingletonService(Share share, DeviceExec device, DateTimeEx dateTimeEx, SystemEngine systemEngine, SerialExec serialExec, TcpExec tcpExec,
        ExecuteService tDengIne, FileEx fileEx, SmbFileEx smbFileEx, FtpFileEx ftpFileEx)
    {
        Device = device;
        Share = share;
        Log = new LogExtension();
        Engine = new Jint.Engine();
        Engine.SetValue("share", share);
        Engine.SetValue("log", Log);
        Engine.SetValue("dateTime", dateTimeEx);
        Engine.SetValue("device", device);
        Engine.SetValue("http", new HttpClientExec());
        Engine.SetValue("system", systemEngine);
        Engine.SetValue("serial", serialExec);
        Engine.SetValue("tcp", tcpExec);
        Engine.SetValue("udp", new UdpExec());
        Engine.SetValue("data", tDengIne);
        Engine.SetValue("mqtt", new SendMsgEx());
        Engine.SetValue("file", fileEx);
        Engine.SetValue("smb", smbFileEx);
        Engine.SetValue("ftp", ftpFileEx);
    }
}