namespace IotGateway.Application.Exec;

public class SendMsgEx : IDisposable
{
    /// <summary>
    ///     mqtt对象
    /// </summary>
    public IMqttClient? Client { get; set; }

    public bool Connect(IMqttClient? client)
    {
        Client = client;
        return true;
    }

    public bool Connect(string ip, int port, string? clientId = null, string userName = "", string password = "")
    {
        Client = new MqttFactory().CreateMqttClient();
        var clientOptions = new MqttClientOptionsBuilder()
            .WithClientId(clientId ?? Guid.NewGuid().ToString("N"))
            .WithTcpServer(ip, port)
            .WithCredentials(userName, password)
            .WithTimeout(TimeSpan.FromSeconds(5))
            .WithKeepAlivePeriod(TimeSpan.FromSeconds(60))
            .WithCleanSession()
            .Build();
        try
        {
            Client.ConnectAsync(clientOptions, CancellationToken.None).GetAwaiter().GetResult();
        }
        catch (Exception e)
        {
            return false;
        }

        return Client.IsConnected;
    }

    /// <summary>
    ///     发送MQTT消息
    /// </summary>
    /// <param name="topic">消息主题</param>
    /// <param name="message">消息内容</param>
    /// <param name="level">消息质量</param>
    /// <param name="retain">保留消息</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public object Publish(string topic, string message, MqttQualityOfServiceLevel level = MqttQualityOfServiceLevel.AtLeastOnce, bool retain = false)
    {
        try
        {
            if (Client == null || Client.IsConnected == false)
                return new {success = false, message = "MQTT服务连接已断开!"};

            var resp = Client.PublishAsync(new MqttApplicationMessage
            {
                Topic = topic,
                PayloadSegment = Encoding.UTF8.GetBytes(message),
                QualityOfServiceLevel = level,
                Retain = retain
            }).Result;
            return resp.ReasonCode != MqttClientPublishReasonCode.Success
                ? new {success = false, message = $"上送请求Topic:{topic} 失败,返回结果:{JSON.Serialize(resp)}"}
                : new {success = true, message = $"上送Topic:{topic} 成功"};
        }
        catch (Exception e)
        {
            Log.Error($"【MQTT Publish】 Error:{e.Message}");
            return new {success = false, message = $"【MQTT Publish】 消息发送异常:{e.Message}"};
        }
    }

    public void DisConnect()
    {
        Client?.DisconnectAsync();
    }

    public void Dispose()
    {
        Client?.DisconnectAsync();
        Client?.Dispose();
    }
}