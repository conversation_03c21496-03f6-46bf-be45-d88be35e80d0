using Common.Extension;
using SMBLibrary.Client;
using System.Text.RegularExpressions;

namespace IotGateway.Engine.Exec;

/// <summary>
///     基于smb文件操作
/// </summary>
public class SmbFileEx : ITransient
{
    /// <summary>
    ///     登录
    /// </summary>
    /// <param name="ip"></param>
    /// <param name="path"></param>
    /// <param name="username"></param>
    /// <param name="password"></param>
    /// <param name="type"></param>
    /// <returns></returns>
    private SmbClient Login(string ip, string path, string username, string password, int type = 1)
    {
        try
        {
            // 尝试不同的认证方法
            for (int attempt = 1; attempt <= 3; attempt++)
            {
                try
                {
                    var authType = attempt == 1 ? type : (attempt == 2 ? 2 : 3); // 依次尝试用户指定的类型、NTLMv2和NTLMv1
                    Console.WriteLine($"尝试连接SMB服务器 {ip}:{path}，认证方法: {authType}，尝试次数: {attempt}/3");

                    var client = new SmbClient(ip, path)
                    {
                        User = username,
                        Domain = string.Empty,
                        Password = password,
                        NetBiosOverTCP = false,
                        Port = 445,
                        Type = authType == 1 ? AuthenticationMethod.NTLMv1ExtendedSessionSecurity :
                               authType == 2 ? AuthenticationMethod.NTLMv2 :
                               AuthenticationMethod.NTLMv1
                    };

                    // 尝试连接
                    var isConnected = client.Connect();
                    if (isConnected)
                    {
                        Console.WriteLine($"SMB连接成功: {ip}:{path}");
                        return client;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"SMB连接尝试 {attempt}/3 失败: {ex.Message}");
                    if (attempt == 3) throw; // 最后一次尝试失败，抛出异常
                }
            }

            // 如果所有尝试都失败，尝试使用NetBiosOverTCP和端口139
            Console.WriteLine("尝试使用NetBiosOverTCP和端口139连接");
            return new SmbClient(ip, path)
            {
                User = username,
                Domain = string.Empty,
                Password = password,
                NetBiosOverTCP = true,
                Port = 139,
                Type = type == 1 ? AuthenticationMethod.NTLMv1ExtendedSessionSecurity :
                       type == 2 ? AuthenticationMethod.NTLMv2 :
                       AuthenticationMethod.NTLMv1
            };
        }
        catch (Exception ex)
        {
            throw new Exception($"无法连接到SMB服务器: {ex.Message}", ex);
        }
    }

    /// <summary>
    ///     获取文件列表
    /// </summary>
    /// <param name="ip">Replace with your SMB server name or IP address</param>
    /// <param name="path">Replace with your share name</param>
    /// <param name="username">Windows用户名</param>
    /// <param name="password">Windows密码</param>
    /// <param name="subPath">子目录</param>
    /// <param name="type"></param>
    /// <returns></returns>
    public dynamic List(string ip, string path, string username, string password, string subPath = "", int type = 1)
    {
        var allItems = new List<dynamic>();
        try
        {
            Console.WriteLine($"开始列出文件: 共享: {path}，子路径: {subPath}");

            // 最多尝试3次
            for (int attempt = 1; attempt <= 3; attempt++)
            {
                try
                {
                    string fullPath = path;
                    if (subPath.IsNotNullOrWhiteSpace())
                        subPath = Path.Combine(path, subPath);

                    var client = Login(ip, fullPath, username, password, attempt); // 每次尝试使用不同的认证类型

                    // 开始连接
                    if (!client.IsConnected)
                    {
                        var isConnected = client.Connect();
                        if (!isConnected)
                        {
                            Console.WriteLine($"连接失败，尝试次数: {attempt}/3");
                            continue;
                        }
                    }

                    Console.WriteLine($"开始获取文件列表: {subPath ?? string.Empty}");
                    // 获取指定目录下的文件及子目录
                    var list = client.GetList(subPath ?? string.Empty);
                    foreach (var file in list)
                        allItems.Add(new
                        {
                            Type = file.IsDirectory ? "directory" : "file",
                            file.Name,
                            Size = file.IsDirectory ? 0 : file.Size / 1024, // 目录没有大小信息，可以留空或用0表示
                            CreateTime = file.CreationTime,
                            LastUpdateTime = file.LastModifiedTime
                        });

                    Console.WriteLine($"成功获取文件列表，共 {allItems.Count} 项");
                    break; // 成功获取列表，跳出循环
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"列表获取尝试 {attempt}/3 失败: {ex.Message}");
                    if (attempt == 3) throw; // 最后一次尝试失败，抛出异常
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取文件列表失败: {ex.Message}");
            // 返回空列表而不是抛出异常，以便调用者可以处理
        }

        return allItems;
    }

    /// <summary>
    ///     下载文件到本地
    /// </summary>
    /// <param name="ip"></param>
    /// <param name="shareName"></param>
    /// <param name="username"></param>
    /// <param name="password"></param>
    /// <param name="fileName"></param>
    /// <param name="localDirectory"></param>
    /// <param name="subPath"></param>
    /// <param name="ignoreFilesObj"></param>
    /// <returns></returns>
    public bool Download(string ip, string shareName, string username, string password, string fileName, string localDirectory, string subPath = "", string ignoreFilesObj = null)
    {
        try
        {
            Console.WriteLine($"开始下载: {fileName}，共享: {shareName}，子路径: {subPath}");

            List<string> ignoreFiles = null;
            if (!string.IsNullOrWhiteSpace(ignoreFilesObj))
            {
                ignoreFiles = ignoreFilesObj.Split(',').ToList();
            }

            // 最多尝试3次
            for (int attempt = 1; attempt <= 3; attempt++)
            {
                try
                {
                    var client = Login(ip, shareName, username, password, attempt); // 每次尝试使用不同的认证类型

                    // 开始连接
                    if (!client.IsConnected)
                    {
                        var isConnected = client.Connect();
                        if (!isConnected)
                        {
                            Console.WriteLine($"连接失败，尝试次数: {attempt}/3");
                            continue;
                        }
                    }

                    var newFileName = fileName;
                    if (subPath.IsNotNullOrWhiteSpace())
                        newFileName = Path.Combine(shareName, subPath, fileName);

                    Console.WriteLine($"检查是否为目录: {newFileName}");
                    // 检查是否为目录
                    var isDirectory = client.DirectoryIsExist(newFileName);
                    if (isDirectory)
                    {
                        Console.WriteLine($"{newFileName} 是一个目录，使用文件夹下载功能");

                        // 构建相对路径
                        string relativePath = fileName;
                        if (subPath.IsNotNullOrWhiteSpace())
                            relativePath = Path.Combine(subPath, fileName);

                        // 使用文件夹下载功能
                        var result = DownloadFolder(ip, shareName, username, password, relativePath, localDirectory, ignoreFilesObj);
                        return result.Success;
                    }

                    // 检查是否应该忽略该文件
                    if (ignoreFiles != null && ShouldIgnoreFile(fileName, ignoreFiles))
                    {
                        Console.WriteLine($"忽略文件: {fileName}");
                        return true; // 返回成功，因为忽略文件是预期行为
                    }

                    Console.WriteLine($"检查文件是否存在: {newFileName}");
                    // 文件是否存在
                    var fileExists = client.FileIsExist(newFileName);
                    if (!fileExists)
                    {
                        Console.WriteLine($"文件不存在: {newFileName}");
                        return false;
                    }

                    Console.WriteLine($"开始下载文件: {newFileName}");
                    // 下载文件
                    using var fs = new MemoryStream();
                    client.Download(newFileName, fs);

                    // 确保目标目录存在
                    if (!Directory.Exists(localDirectory))
                    {
                        Directory.CreateDirectory(localDirectory);
                    }

                    var localFilePath = Path.Combine(localDirectory, fileName);
                    using var outputStream = new FileStream(localFilePath, FileMode.Create);
                    fs.Position = 0; // 重置流位置
                    fs.CopyTo(outputStream);

                    Console.WriteLine($"文件下载成功: {localFilePath}");
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"下载尝试 {attempt}/3 失败: {ex.Message}");
                    if (attempt == 3) throw; // 最后一次尝试失败，抛出异常
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"下载失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    ///     删除文件目录
    /// </summary>
    /// <param name="ip"></param>
    /// <param name="shareName"></param>
    /// <param name="username"></param>
    /// <param name="password"></param>
    /// <param name="fileName"></param>
    /// <returns></returns>
    public bool Remove(string ip, string shareName, string username, string password, string fileName)
    {
        try
        {
            Console.WriteLine($"开始删除文件/目录: {fileName}，共享: {shareName}");

            // 最多尝试3次
            for (int attempt = 1; attempt <= 3; attempt++)
            {
                try
                {
                    var client = Login(ip, shareName, username, password, attempt); // 每次尝试使用不同的认证类型

                    // 开始连接
                    if (!client.IsConnected)
                    {
                        var isConnected = client.Connect();
                        if (!isConnected)
                        {
                            Console.WriteLine($"连接失败，尝试次数: {attempt}/3");
                            continue;
                        }
                    }

                    Console.WriteLine($"检查是否为目录: {fileName}");
                    // 检查是否为目录
                    var isDirectory = client.DirectoryIsExist(fileName);
                    if (!isDirectory)
                    {
                        Console.WriteLine($"检查是否为文件: {fileName}");
                        var isFile = client.FileIsExist(fileName);
                        if (!isFile)
                        {
                            Console.WriteLine($"文件/目录不存在: {fileName}");
                            return false;
                        }

                        Console.WriteLine($"删除文件: {fileName}");
                        // 删除文件
                        client.Delete(fileName);
                    }
                    else
                    {
                        Console.WriteLine($"删除目录: {fileName}");
                        client.RemoveDirectory(fileName);
                    }

                    Console.WriteLine($"成功删除文件/目录: {fileName}");
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"删除尝试 {attempt}/3 失败: {ex.Message}");
                    if (attempt == 3) throw; // 最后一次尝试失败，抛出异常
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"删除文件/目录失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    ///     下载文件夹到本地
    /// </summary>
    /// <param name="ip">服务器IP</param>
    /// <param name="shareName">共享名称</param>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="remoteFolderPath">远程文件夹路径（相对于共享根目录）</param>
    /// <param name="localDirectory">本地目录</param>
    /// <param name="ignoreFiles">忽略下载的文件名称列表，支持通配符*和?</param>
    /// <returns>下载结果，包含成功和失败的文件列表</returns>
    public dynamic DownloadFolder(string ip, string shareName, string username, string password, string remoteFolderPath, string localDirectory, string ignoreFilesObj = null)
    {
        try
        {
            Console.WriteLine($"开始下载文件夹: {remoteFolderPath} 到 {localDirectory}");

            List<string> ignoreFiles = null;
            if (!string.IsNullOrWhiteSpace(ignoreFilesObj))
            {
                ignoreFiles = ignoreFilesObj.Split(',').ToList();
            }
            if (ignoreFiles != null && ignoreFiles.Count > 0)
            {
                Console.WriteLine($"忽略文件列表: {string.Join(", ", ignoreFiles)}");
            }

            // 获取连接
            var client = Login(ip, shareName, username, password);

            // 确保连接成功
            if (!client.IsConnected)
            {
                var isConnected = client.Connect();
                if (!isConnected)
                {
                    return new
                    {
                        Success = false,
                        Message = "连接失败，无法下载文件夹",
                        SuccessFiles = new List<string>(),
                        FailedFiles = new List<dynamic>(),
                        IgnoredFiles = new List<string>()
                    };
                }
            }

            // 确保远程文件夹存在
            var fullRemotePath = remoteFolderPath;
            if (string.IsNullOrWhiteSpace(remoteFolderPath))
            {
                fullRemotePath = shareName;
            }
            else
            {
                fullRemotePath = Path.Combine(shareName, remoteFolderPath);
            }

            var folderExists = client.DirectoryIsExist(fullRemotePath);
            if (!folderExists)
            {
                return new
                {
                    Success = false,
                    Message = $"远程文件夹不存在: {remoteFolderPath}",
                    SuccessFiles = new List<string>(),
                    FailedFiles = new List<dynamic>(),
                    IgnoredFiles = new List<string>()
                };
            }

            // 创建本地目录
            var localFolderPath = localDirectory;
            if (!string.IsNullOrWhiteSpace(remoteFolderPath))
            {
                var folderName = Path.GetFileName(remoteFolderPath.TrimEnd('\\', '/'));
                localFolderPath = Path.Combine(localDirectory, folderName);
            }

            if (!Directory.Exists(localFolderPath))
            {
                Directory.CreateDirectory(localFolderPath);
            }

            // 递归下载文件夹
            var downloadResult = DownloadFolderRecursive(client, fullRemotePath, localFolderPath, "", ignoreFiles);

            return new
            {
                Success = downloadResult.FailedFiles.Count == 0,
                Message = downloadResult.FailedFiles.Count == 0 ? "文件夹下载成功" : "部分文件下载失败",
                SuccessFiles = downloadResult.SuccessFiles,
                FailedFiles = downloadResult.FailedFiles,
                IgnoredFiles = downloadResult.IgnoredFiles
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine($"下载文件夹失败: {ex.Message}");
            return new
            {
                Success = false,
                Message = $"下载文件夹失败: {ex.Message}",
                SuccessFiles = new List<string>(),
                FailedFiles = new List<dynamic>(),
                IgnoredFiles = new List<string>()
            };
        }
    }

    /// <summary>
    ///     递归下载文件夹
    /// </summary>
    private dynamic DownloadFolderRecursive(SmbClient client, string remotePath, string localPath, string relativePath, List<string> ignoreFiles = null)
    {
        var successFiles = new List<string>();
        var failedFiles = new List<dynamic>();
        var ignoredFiles = new List<string>();

        try
        {
            // 获取文件夹内容
            var items = client.GetList(remotePath);

            // 下载文件
            foreach (var item in items)
            {
                var itemName = item.Name;
                if (itemName == "." || itemName == "..") continue;

                var remoteItemPath = Path.Combine(remotePath, itemName);
                var localItemPath = Path.Combine(localPath, itemName);
                var relativeItemPath = string.IsNullOrEmpty(relativePath) ? itemName : Path.Combine(relativePath, itemName);

                // 检查是否在忽略列表中
                if (ignoreFiles != null && ShouldIgnoreFile(itemName, ignoreFiles))
                {
                    Console.WriteLine($"忽略文件: {relativeItemPath}");
                    ignoredFiles.Add(relativeItemPath);
                    continue;
                }

                if (item.IsDirectory)
                {
                    // 创建本地目录
                    if (!Directory.Exists(localItemPath))
                    {
                        Directory.CreateDirectory(localItemPath);
                    }

                    // 递归下载子文件夹
                    var subResult = DownloadFolderRecursive(client, remoteItemPath, localItemPath, relativeItemPath, ignoreFiles);
                    successFiles.AddRange(subResult.SuccessFiles);
                    failedFiles.AddRange(subResult.FailedFiles);
                    ignoredFiles.AddRange(subResult.IgnoredFiles);
                }
                else
                {
                    // 下载文件
                    try
                    {
                        using var fs = new MemoryStream();
                        client.Download(remoteItemPath, fs);

                        using var outputStream = new FileStream(localItemPath, FileMode.Create);
                        fs.Position = 0; // 重置流位置
                        fs.CopyTo(outputStream);

                        successFiles.Add(relativeItemPath);
                        Console.WriteLine($"文件下载成功: {relativeItemPath}");
                    }
                    catch (Exception ex)
                    {
                        failedFiles.Add(new
                        {
                            Path = relativeItemPath,
                            Error = ex.Message
                        });
                        Console.WriteLine($"文件下载失败: {relativeItemPath}, 错误: {ex.Message}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            failedFiles.Add(new
            {
                Path = relativePath,
                Error = ex.Message
            });
            Console.WriteLine($"获取文件夹内容失败: {relativePath}, 错误: {ex.Message}");
        }

        return new
        {
            SuccessFiles = successFiles,
            FailedFiles = failedFiles,
            IgnoredFiles = ignoredFiles
        };
    }

    /// <summary>
    ///     检查文件是否应该被忽略
    /// </summary>
    private bool ShouldIgnoreFile(string fileName, List<string> ignorePatterns)
    {
        if (ignorePatterns == null || ignorePatterns.Count == 0)
            return false;

        foreach (var pattern in ignorePatterns)
        {
            if (MatchesWildcard(fileName, pattern))
                return true;
        }

        return false;
    }

    /// <summary>
    ///     检查文件名是否匹配通配符模式
    /// </summary>
    private bool MatchesWildcard(string fileName, string pattern)
    {
        // 转换通配符为正则表达式
        string regexPattern = "^" + Regex.Escape(pattern)
            .Replace("\\*", ".*")
            .Replace("\\?", ".") + "$";

        return Regex.IsMatch(fileName, regexPattern, RegexOptions.IgnoreCase);
    }

    /// <summary>
    ///     批量下载文件夹
    /// </summary>
    /// <param name="ip">服务器IP</param>
    /// <param name="shareName">共享名称</param>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="folders">文件夹列表，格式为：[{folderPath, localSubPath}]</param>
    /// <param name="localDirectory">本地根目录</param>
    /// <param name="ignoreFiles">忽略下载的文件名称列表，支持通配符*和?</param>
    /// <returns>下载结果，格式为：[{folderPath, success, message, successFiles, failedFiles, ignoredFiles}]</returns>
    public List<dynamic> BatchDownloadFolders(string ip, string shareName, string username, string password, List<dynamic> folders, string localDirectory, List<string> ignoreFiles = null)
    {
        var results = new List<dynamic>();

        try
        {
            Console.WriteLine($"开始批量下载文件夹，共 {folders.Count} 个文件夹");
            if (ignoreFiles != null && ignoreFiles.Count > 0)
            {
                Console.WriteLine($"忽略文件列表: {string.Join(", ", ignoreFiles)}");
            }

            // 获取连接（只连接一次）
            var client = Login(ip, shareName, username, password);

            // 确保连接成功
            if (!client.IsConnected)
            {
                var isConnected = client.Connect();
                if (!isConnected)
                {
                    Console.WriteLine("连接失败，无法进行批量下载");
                    foreach (var folder in folders)
                    {
                        results.Add(new
                        {
                            FolderPath = folder.folderPath,
                            Success = false,
                            Message = "连接失败",
                            SuccessFiles = new List<string>(),
                            FailedFiles = new List<dynamic>(),
                            IgnoredFiles = new List<string>()
                        });
                    }
                    return results;
                }
            }

            // 确保本地根目录存在
            if (!Directory.Exists(localDirectory))
            {
                Directory.CreateDirectory(localDirectory);
            }

            // 批量下载文件夹
            foreach (var folder in folders)
            {
                try
                {
                    string folderPath = folder.folderPath;
                    string localSubPath = folder.localSubPath ?? string.Empty;

                    Console.WriteLine($"下载文件夹: {folderPath}，本地子路径: {localSubPath}");

                    // 构建完整的远程路径
                    var fullRemotePath = folderPath;
                    if (!string.IsNullOrWhiteSpace(folderPath))
                    {
                        fullRemotePath = Path.Combine(shareName, folderPath);
                    }
                    else
                    {
                        fullRemotePath = shareName;
                    }

                    // 检查远程文件夹是否存在
                    var folderExists = client.DirectoryIsExist(fullRemotePath);
                    if (!folderExists)
                    {
                        Console.WriteLine($"远程文件夹不存在: {folderPath}");
                        results.Add(new
                        {
                            FolderPath = folderPath,
                            Success = false,
                            Message = "远程文件夹不存在",
                            SuccessFiles = new List<string>(),
                            FailedFiles = new List<dynamic>(),
                            IgnoredFiles = new List<string>()
                        });
                        continue;
                    }

                    // 构建本地目录路径
                    var localFolderPath = localDirectory;
                    if (!string.IsNullOrWhiteSpace(localSubPath))
                    {
                        localFolderPath = Path.Combine(localDirectory, localSubPath);
                        if (!Directory.Exists(localFolderPath))
                        {
                            Directory.CreateDirectory(localFolderPath);
                        }
                    }

                    // 获取文件夹名称
                    var folderName = string.Empty;
                    if (!string.IsNullOrWhiteSpace(folderPath))
                    {
                        folderName = Path.GetFileName(folderPath.TrimEnd('\\', '/'));
                        localFolderPath = Path.Combine(localFolderPath, folderName);
                        if (!Directory.Exists(localFolderPath))
                        {
                            Directory.CreateDirectory(localFolderPath);
                        }
                    }

                    // 递归下载文件夹
                    var downloadResult = DownloadFolderRecursive(client, fullRemotePath, localFolderPath, "", ignoreFiles);

                    results.Add(new
                    {
                        FolderPath = folderPath,
                        Success = downloadResult.FailedFiles.Count == 0,
                        Message = downloadResult.FailedFiles.Count == 0 ? "文件夹下载成功" : "部分文件下载失败",
                        SuccessFiles = downloadResult.SuccessFiles,
                        FailedFiles = downloadResult.FailedFiles,
                        IgnoredFiles = downloadResult.IgnoredFiles
                    });
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"下载文件夹失败: {folder.folderPath}, 错误: {ex.Message}");
                    results.Add(new
                    {
                        FolderPath = folder.folderPath,
                        Success = false,
                        Message = $"下载失败: {ex.Message}",
                        SuccessFiles = new List<string>(),
                        FailedFiles = new List<dynamic>(),
                        IgnoredFiles = new List<string>()
                    });
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"批量下载文件夹失败: {ex.Message}");
            // 如果还没有添加结果，为所有文件夹添加失败结果
            if (results.Count == 0)
            {
                foreach (var folder in folders)
                {
                    results.Add(new
                    {
                        FolderPath = folder.folderPath,
                        Success = false,
                        Message = $"批量下载失败: {ex.Message}",
                        SuccessFiles = new List<string>(),
                        FailedFiles = new List<dynamic>(),
                        IgnoredFiles = new List<string>()
                    });
                }
            }
        }

        return results;
    }
}