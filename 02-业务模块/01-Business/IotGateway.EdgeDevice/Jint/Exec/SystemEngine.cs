using Common.Extension;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.EdgeDevice.Exec;

/// <summary>
///     系统方法
/// </summary>
public class SystemEngine : ISingleton
{
    public SystemEngine(IServiceProvider services)
    {
        Services = services;
    }

    private IServiceProvider Services { get; }

    /// <summary>
    ///     发送实时数据
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="objData"></param>
    /// <returns></returns>
    public dynamic OnLineCom(string deviceName, object[] objData)
    {
        var output = new List<string>();
        // 接收传入参数
        List<IDictionary<string, object>> dicList = null;
        if (objData != null)
            dicList = ConvertToObjectList(objData);
        if (dicList != null)
        {
            using var scope = Services.CreateScope();
            var deviceService = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<Device>>();
            // 设备
            var device = deviceService.AsQueryable().Where(w => w.DeviceName == deviceName).Includes(w => w.DeviceVariable)
                .First();
            if (device == null)
                throw Oops.Oh($"请先维护设备：{deviceName}");

            // 预先构建设备变量字典，避免循环中重复查询
            var deviceVariableDict = device.DeviceVariable.ToDictionary(v => v.Identifier);

            Parallel.ForEach(dicList, dic =>
            {
                try
                {
                    var time = dic.TryGetValue("Time", out var value) ? DateTime.ToLong(Convert.ToDateTime(value)) : DateTime.ToLong();
                    _ = MessageCenter.PublishAsync(EventConst.SendDeviceData, new PayLoad
                    {
                        Ts = time,
                        DeviceName = deviceName,
                        DeviceStatus = DeviceStatusTypeEnum.Good,
                        Values = dic.ToDictionary(s => s.Key, s =>
                        {
                            // 尝试获取设备变量
                            deviceVariableDict.TryGetValue(s.Key, out var deviceVar);
                            return new ParamValue
                            {
                                DataType = deviceVar?.ValueSource == ValueSourceEnum.Get ? deviceVar?.DeviceVariableEx.DataType ?? DataTypeEnum.String : DataTypeEnum.String,
                                ReadTime = time,
                                SendType = SendTypeEnum.Always,
                                TransitionType = deviceVar?.TransitionType ?? TransPondDataTypeEnum.String,
                                ValueSource = ValueSourceEnum.Calculate,
                                VariableStatus = VariableStatusTypeEnum.Good,
                                DeviceVariableName = s.Key,
                                Value = s.Value + "",
                            };
                        })
                    });
                }
                catch (Exception e)
                {
                    output.Add(e.Message);
                }
            });
        }


        return output;
    }

    /// <summary>
    ///     发送实时数据
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="objData"></param>
    /// <returns></returns>
    public dynamic OnLine(string deviceName, object[] objData)
    {
        var output = new List<string>();
        // 接收传入参数
        List<IDictionary<string, object>> dicList = null;
        if (objData != null)
            dicList = ConvertToObjectList(objData);
        if (dicList != null)
            _ = Parallel.ForEach(dicList, dic =>
            {
                try
                {
                    var time = dic.TryGetValue("Time", out var value) ? DateTime.ToLong(Convert.ToDateTime(value)) : DateTime.ToLong();
                    _ = MessageCenter.PublishAsync(EventConst.SendDeviceData, new PayLoad
                    {
                        Ts = time,
                        DeviceName = deviceName,
                        DeviceStatus = DeviceStatusTypeEnum.Good,
                        Values = dic.ToDictionary(s => s.Key, s => new ParamValue
                        {
                            Value = s.Value + "",
                            DataType = s.Key == "Time" ? DataTypeEnum.String : DataTypeEnum.Double,
                            ReadTime = time,
                            SendType = SendTypeEnum.Always,
                            TransitionType = s.Key == "Time" ? TransPondDataTypeEnum.String : TransPondDataTypeEnum.Double,
                            ValueSource = ValueSourceEnum.Calculate,
                            VariableStatus = VariableStatusTypeEnum.Good,
                            DeviceVariableName = s.Key
                        })
                    });
                }
                catch (Exception e)
                {
                    output.Add(e.Message);
                }
            });
        return output;
    }


    // 将 System.Object[] 转换为 List<IDictionary<string, object>>
    public static List<IDictionary<string, object>> ConvertToObjectList(object[] objectsArray)
    {
        var result = new List<IDictionary<string, object>>();

        foreach (var obj in objectsArray)
            // 尝试将每个元素转换为 Dictionary<string, object>
            if (obj is IDictionary<string, object> dictionary)
                result.Add(dictionary);
            else
                // 如果不是 Dictionary<string, object>，可以根据实际情况处理
                throw new ArgumentException("Object array contains non-dictionary elements.");

        return result;
    }

    /// <summary>
    ///     十进制转二进制
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    public string Binary(int value)
    {
        var binary = Convert.ToString(value, 2).PadLeft(8, '0');
        return binary;
    }

    /// <summary>
    ///     二进制转十进制
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    public int Decimal(string value)
    {
        var val = Convert.ToInt32(value, 2);
        return val;
    }

    /// <summary>
    ///     Md5加密
    /// </summary>
    /// <param name="text"></param>
    /// <param name="uppercase"></param>
    /// <param name="is16"></param>
    /// <returns></returns>
    public string Md5Encryption(string text, bool uppercase = false, bool is16 = false)
    {
        //  MD5 加密
        var md5Hash = MD5Encryption.Encrypt(text, uppercase, is16); // 加密
        return md5Hash;
    }

    /// <summary>
    ///     转换字节数组
    /// </summary>
    /// <param name="dataType">数据类型</param>
    /// <param name="value"></param>
    /// <returns></returns>
    public byte[] ToBytes(string dataType, string value)
    {
        var content = dataType.ToLower() switch
        {
            "bool" => BitConverter.GetBytes(bool.Parse(value)),
            "int16" => BitConverter.GetBytes(short.Parse(value)),
            "uint16" => BitConverter.GetBytes(ushort.Parse(value)),
            "uint32" => BitConverter.GetBytes(uint.Parse(value)),
            "int32" => BitConverter.GetBytes(int.Parse(value)),
            "uint64" => BitConverter.GetBytes(ulong.Parse(value)),
            "int64" => BitConverter.GetBytes(long.Parse(value)),
            "double" => BitConverter.GetBytes(double.Parse(value)),
            "float" => BitConverter.GetBytes(float.Parse(value)),
            "utf8" => Encoding.UTF8.GetBytes(value),
            "utf32" => Encoding.UTF32.GetBytes(value),
            "ascii" => Encoding.ASCII.GetBytes(value),
            "unicode" => Encoding.Unicode.GetBytes(value),
            "unicode-big" => Encoding.BigEndianUnicode.GetBytes(value),
            "gb2312" => Encoding.GetEncoding("GB2312").GetBytes(value),
            "base64" => Convert.FromBase64String(value),
            _ => new byte[] { }
        };
        return content;
    }

    /// <summary>
    ///     十六进制转字符串
    /// </summary>
    /// <param name="dataType">数据类型</param>
    /// <param name="value"></param>
    /// <returns></returns>
    public object BytesToString(string dataType, string value)
    {
        object content = null;
        if (value.IsNullOrEmpty())
            return null;
        switch (dataType.ToLower())
        {
            case "int16":
                content = BitConverter.ToInt16(SoftBasic.HexStringToBytes(value));
                break;
            case "uint16":
                content = BitConverter.ToUInt16(SoftBasic.HexStringToBytes(value));
                break;
            case "uint32":
                content = BitConverter.ToUInt32(SoftBasic.HexStringToBytes(value));
                break;
            case "int32":
                content = BitConverter.ToInt32(SoftBasic.HexStringToBytes(value));
                break;
            case "uint64":
            case "int64":
                content = BitConverter.ToInt64(SoftBasic.HexStringToBytes(value));
                break;
            case "double":
                content = BitConverter.ToDouble(SoftBasic.HexStringToBytes(value));
                break;
            case "float":
                content = BitConverter.ToSingle(SoftBasic.HexStringToBytes(value));
                break;
            case "utf8":
                content = Encoding.UTF8.GetString(SoftBasic.HexStringToBytes(value));
                break;
            case "utf32":
                content = Encoding.UTF32.GetString(SoftBasic.HexStringToBytes(value));
                break;
            case "ascii":
                content = Encoding.ASCII.GetString(SoftBasic.HexStringToBytes(value));
                break;
            case "unicode":
                content = Encoding.Unicode.GetString(SoftBasic.HexStringToBytes(value));
                break;
            case "unicode-big":
                content = Encoding.BigEndianUnicode.GetString(SoftBasic.HexStringToBytes(value));
                break;
            case "gb2312":
                content = Encoding.GetEncoding("gb2312").GetString(SoftBasic.HexStringToBytes(value));
                break;
            case "base64":
                content = Convert.ToBase64String(SoftBasic.HexStringToBytes(value));
                break;
        }

        return content;
    }

    /// <summary>
    ///     值转字符串
    /// </summary>
    /// <param name="dataType"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    public string HexToString(string dataType, string value)
    {
        var content = dataType.ToLower() switch
        {
            "bool" => BitConverter.GetBytes(bool.Parse(value)),
            "int16" => BitConverter.GetBytes(short.Parse(value)),
            "uint16" => BitConverter.GetBytes(ushort.Parse(value)),
            "uint32" => BitConverter.GetBytes(uint.Parse(value)),
            "int32" => BitConverter.GetBytes(int.Parse(value)),
            "uint64" => BitConverter.GetBytes(ulong.Parse(value)),
            "int64" => BitConverter.GetBytes(long.Parse(value)),
            "double" => BitConverter.GetBytes(double.Parse(value)),
            "float" => BitConverter.GetBytes(float.Parse(value)),
            "utf8" => Encoding.UTF8.GetBytes(value),
            "utf32" => Encoding.UTF32.GetBytes(value),
            "ascii" => Encoding.ASCII.GetBytes(value),
            "unicode" => Encoding.Unicode.GetBytes(value),
            "unicode-big" => Encoding.BigEndianUnicode.GetBytes(value),
            "gb2312" => Encoding.GetEncoding("GB2312").GetBytes(value),
            "base64" => Convert.FromBase64String(value),
            _ => new byte[] { }
        };

        var bytesReverseByWord = SoftBasic.BytesReverseByWord(content);
        return SoftBasic.ByteToHexString(bytesReverseByWord);
    }

    /// <summary>
    ///     字节数据转化成16进制表示的字符串
    /// </summary>
    /// <param name="value"></param>
    /// <param name="returnType"></param>
    /// <returns></returns>
    public object ByteToHexString(byte[] value, string returnType = "string")
    {
        var strValue = SoftBasic.ByteToHexString(value, ' ');
        if (returnType == "string")
            return strValue;
        return strValue.Split(' ');
    }

    /// <summary>
    ///     将16进制的字符串转化成Byte数据，将检测每2个字符转化，也就是说，中间可以是任意字符
    /// </summary>
    /// <param name="hex"></param>
    /// <returns></returns>
    public object HexStringToBytes(string hex)
    {
        return SoftBasic.HexStringToBytes(hex);
    }

    /// <summary>
    ///     将ASCII格式的字符串转为原始字节数组，如果遇到 \00 这种表示原始字节的内容，则直接进行转换操作，遇到 \r 直接转换 0x0D, \n 直接转换 0x0A
    /// </summary>
    /// <param name="render"></param>
    /// <returns></returns>
    public object AscStringToBytes(string render)
    {
        return SoftBasic.GetFromAsciiStringRender(render);
    }

    /// <summary>
    ///     休眠
    /// </summary>
    /// <param name="millisecondsTimeout"></param>
    /// <returns></returns>
    public void Sleep(int millisecondsTimeout)
    {
        Thread.Sleep(millisecondsTimeout);
    }

    /// <summary>
    ///     ping
    /// </summary>
    /// <param name="ip"></param>
    /// <param name="timeOut"></param>
    /// <returns></returns>
    public bool Ping(string ip, int timeOut = 1000)
    {
        try
        {
            using var pingSender = new Ping();
            var reply = pingSender.Send(ip, timeOut);
            return reply!.Status == IPStatus.Success;
        }
        catch (Exception e)
        {
            return false;
        }
    }

    /// <summary>
    ///     执行命令
    /// </summary>
    /// <param name="command">执行命令</param>
    /// <param name="fileName">执行文件路径</param>
    /// <returns></returns>
    public string Command(string command, string fileName = "")
    {
        try
        {
            return RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? ShellUtil.Cmd(fileName, command).Result : ShellUtil.Bash(command).Result;
        }
        catch (Exception e)
        {
            return e.Message;
        }
    }
}