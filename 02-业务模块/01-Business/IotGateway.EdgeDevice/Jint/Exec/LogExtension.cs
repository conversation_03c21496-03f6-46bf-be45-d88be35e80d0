using Common.Extension;

namespace IotGateway.Application.Exec;

/// <summary>
///     日志打印
/// </summary>
public class LogExtension
{
    /// <summary>
    ///     日志打印
    /// </summary>
    public List<object> Logs { get; set; } = new();

    /// <summary>
    ///     打印日志
    /// </summary>
    /// <param name="val"></param>
    /// <param name="save"></param>
    /// <exception cref="AppFriendlyException"></exception>
    public void Write(object? val, bool save = false)
    {
        try
        {
            if (val == null) return;

            if (save) Log.Information(JSON.Serialize(val));

            Logs.Add(val.GetJsonElementValue());
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }
}