using HslCommunication.Profinet.Freedom;
using HslCommunication.Serial;

namespace IotGateway.Application.Exec;

/// <summary>
///     Tcp连接
/// </summary>
public class TcpExec : ITransient, IDisposable
{
    /// <summary>
    ///     tcp网口
    /// </summary>
    private FreedomTcpNet _spReadData;

    /// <summary>
    /// </summary>
    private bool _isConnect;

    /// <summary>
    ///     连接Tcp
    /// </summary>
    /// <param name="ip">IP</param>
    /// <param name="port">端口</param>
    /// <returns></returns>
    public bool Connect(string ip, int port)
    {
        if (_spReadData != null && _isConnect) return true;

        _spReadData ??= new FreedomTcpNet();
        _spReadData.IpAddress = ip;
        _spReadData.Port = port;
        // _spReadData.ByteTransform.DataFormat = dataFormat;
        _spReadData.ConnectClose();
        _spReadData.ConnectTimeOut = 3000; // 连接3秒超时
        try
        {
            var connect = _spReadData.ConnectServer();
            _isConnect = connect.IsSuccess;
            return _isConnect;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    ///     发送消息
    /// </summary>
    /// <param name="sendData">数据byte[]</param>
    /// <param name="type">返回格式默认byte,支持【binary,ascii,byte】</param>
    /// <param name="sleepTime">等待时间,处理分包回复的场景</param>
    /// <param name="append">追加字符</param>
    /// <returns></returns>
    public object Send(byte[] sendData, string type = "byte", int sleepTime = 20, string append = "none")
    {
        try
        {
            if (sleepTime > 20)
                _spReadData.SleepTime = sleepTime;

            // 追加字符
            sendData = append switch
            {
                "\\r" => SoftBasic.SpliceArray(sendData, new byte[] {0x0D}),
                "\\r\\n" => SoftBasic.SpliceArray(sendData, new byte[] {0x0D, 0x0A}),
                "\\n" => SoftBasic.SpliceArray(sendData, new byte[] {0x0A}),
                "crc16" => SoftCRC16.CRC16(sendData),
                _ => sendData
            };

            var read = _spReadData.ReadFromCoreServer(sendData);
            if (!read.IsSuccess) return null;
            switch (type)
            {
                case "byte":
                    return read.Content;
                case "ascii":
                    return Encoding.ASCII.GetString(read.Content).TrimEnd('\0');
                case "binary":
                    return SoftBasic.ByteToHexString(read.Content, ' ');
            }

            return BitConverter.ToString(read.Content).Replace("-", " ");
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    ///     发送消息
    /// </summary>
    /// <param name="data">数据byte[]</param>
    /// <param name="encoding">字符编码</param>
    /// <param name="type">返回格式默认byte,支持【binary,ascii,byte】</param>
    /// <param name="sleepTime">等待时间,处理分包回复的场景</param>
    /// <param name="append"></param>
    /// <returns></returns>
    public object Send(string data, string encoding = "hex", string type = "byte", int sleepTime = 20, string append = "none")
    {
        try
        {
            if (sleepTime > 20)
                _spReadData.SleepTime = sleepTime;

            var sendData = Array.Empty<byte>();
            switch (encoding)
            {
                case "hex":
                    sendData = SoftBasic.HexStringToBytes(data);
                    break;
                case "ascii":
                    sendData = SoftBasic.GetFromAsciiStringRender(data);
                    break;
            }

            // 追加字符
            sendData = append switch
            {
                "\\r" => SoftBasic.SpliceArray(sendData, new byte[] {0x0D}),
                "\\r\\n" => SoftBasic.SpliceArray(sendData, new byte[] {0x0D, 0x0A}),
                "\\n" => SoftBasic.SpliceArray(sendData, new byte[] {0x0A}),
                "crc16" => SoftCRC16.CRC16(sendData),
                _ => sendData
            };

            var read = _spReadData.ReadFromCoreServer(sendData);
            if (!read.IsSuccess) return null;
            switch (type)
            {
                case "byte":
                    return read.Content;
                case "ascii":
                    return Encoding.ASCII.GetString(read.Content).TrimEnd('\0');
                case "binary":
                    return SoftBasic.ByteToHexString(read.Content, ' ');
            }

            return BitConverter.ToString(read.Content).Replace("-", " ");
            //return read.ToMessageShowString();
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    ///     关闭连接
    /// </summary>
    /// <returns></returns>
    public bool Close()
    {
        try
        {
            _isConnect = false;
            _spReadData?.ConnectClose();
            _spReadData?.Dispose();
            return true;
        }
        catch
        {
            return true;
        }
    }

    /// <summary>
    /// </summary>
    public void Dispose()
    {
        try
        {
            _isConnect = false;
            _spReadData?.ConnectClose();
            _spReadData?.Dispose();
        }
        catch
        {
        }
    }
}