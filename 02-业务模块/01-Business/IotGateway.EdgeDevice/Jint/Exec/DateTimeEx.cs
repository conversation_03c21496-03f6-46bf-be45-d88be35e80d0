using DateTime = System.DateTime;

namespace IotGateway.Engine.Exec;

/// <summary>
///     时间类扩展
/// </summary>
public class DateTimeEx : ISingleton
{
    /// <summary>
    ///     当前时间
    /// </summary>
    /// <returns></returns>
    public string Now(string convert = "utc")
    {
        if (convert != "cst") return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        try
        {
            // 设置时区为 Asia/Shanghai
            var timeZone = RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? TimeZoneInfo.FindSystemTimeZoneById("China Standard Time") : TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai");
            // 获取当前时间的 UTC 时间
            var utcTime = DateTime.UtcNow;
            // 将 UTC 时间转换为指定时区的本地时间
            return TimeZoneInfo.ConvertTimeFromUtc(utcTime, timeZone).ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch
        {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }

    /// <summary>
    ///     北京时间
    /// </summary>
    /// <returns></returns>
    public string ShangHai()
    {
        try
        {
            // 设置时区为 Asia/Shanghai
            var timeZone = RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? TimeZoneInfo.FindSystemTimeZoneById("China Standard Time") : TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai");
            // 获取当前时间的 UTC 时间
            var utcTime = DateTime.UtcNow;
            // 将 UTC 时间转换为指定时区的本地时间
            return TimeZoneInfo.ConvertTimeFromUtc(utcTime, timeZone).ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch
        {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }

    /// <summary>
    ///     格式化时间
    /// </summary>
    /// <returns></returns>
    public string Format(DateTime time, string str = "yyyy-MM-dd HH:mm:ss")
    {
        return time.ToString(str);
    }

    /// <summary>
    ///     格式化时间
    /// </summary>
    /// <returns></returns>
    public string Format(string time, string str = "yyyy-MM-dd HH:mm:ss")
    {
        return Convert.ToDateTime(time).ToString(str);
    }

    /// <summary>
    ///     时间差
    /// </summary>
    /// <returns></returns>
    public double DiffDate(DateTime startTime, DateTime endTime, int hour = 0)
    {
        if (hour > 0)
            endTime = endTime.AddHours(hour);
        var time = (endTime - startTime).TotalMilliseconds;
        return time;
    }

    /// <summary>
    ///     时间差,偏移量
    /// </summary>
    /// <returns></returns>
    public double DiffDate(string startTimeStr, string endTimeStr, int hour = 0)
    {
        var starTime = Convert.ToDateTime(startTimeStr);
        var endTime = Convert.ToDateTime(endTimeStr);
        if (hour > 0)
            endTime = endTime.AddHours(hour);
        return (endTime - starTime).TotalMilliseconds;
    }

    /// <summary>
    ///     增减时间
    /// </summary>
    /// <returns></returns>
    public string AddDate(DateTime nowTime, string type, int value)
    {
        return type switch
        {
            "year" => nowTime.AddYears(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "month" => nowTime.AddMonths(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "day" => nowTime.AddDays(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "hour" => nowTime.AddHours(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "min" => nowTime.AddMinutes(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "sec" => nowTime.AddSeconds(value).ToString("yyyy-MM-dd HH:mm:ss"),
            _ => throw new Exception("加减类型错误！")
        };
    }

    /// <summary>
    ///     增减时间
    /// </summary>
    /// <returns></returns>
    public string AddDate(string time, string type, int value)
    {
        var nowTime = Convert.ToDateTime(time);
        return type switch
        {
            "year" => nowTime.AddYears(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "month" => nowTime.AddMonths(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "day" => nowTime.AddDays(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "hour" => nowTime.AddHours(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "min" => nowTime.AddMinutes(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "sec" => nowTime.AddSeconds(value).ToString("yyyy-MM-dd HH:mm:ss"),
            _ => throw new Exception("加减类型错误！")
        };
    }

    /// <summary>
    ///     秒级时间戳
    /// </summary>
    /// <returns>Unix时间戳格式</returns>
    public static long ToSeconds()
    {
        var delta = Common.Extension.DateTime.Now() - new DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalSeconds);
    }

    /// <summary>
    ///     秒级时间戳
    /// </summary>
    /// <param name="time"> DateTime时间格式</param>
    /// <returns>Unix时间戳格式</returns>
    public static long ToSeconds(DateTime time)
    {
        var delta = time - new DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalSeconds);
    }

    /// <summary>
    ///     秒级时间戳
    /// </summary>
    /// <param name="time"> 时间</param>
    /// <returns>Unix时间戳格式</returns>
    public static long ToSeconds(string time)
    {
        var delta = Convert.ToDateTime(time) - new DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalSeconds);
    }

    /// <summary>
    ///     毫秒级时间戳
    /// </summary>
    /// <param name="time"></param>
    /// <returns></returns>
    public static string ToMilliseconds(string time)
    {
        var delta = Convert.ToDateTime(time) - new DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds).ToString();
    }

    /// <summary>
    ///     毫秒级时间戳返回int64类型
    /// </summary>
    /// <returns>Unix时间戳格式</returns>
    public static long ToMilliseconds(DateTime time)
    {
        var delta = time - new DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds);
    }

    /// <summary>
    ///     毫秒级时间戳返回int64类型
    /// </summary>
    /// <returns>Unix时间戳格式</returns>
    public static long ToMilliseconds()
    {
        var delta = Common.Extension.DateTime.Now() - new DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds);
    }

    /// <summary>
    ///     毫秒时间戳转为北京时间
    /// </summary>
    /// <returns>C#格式时间</returns>
    public static string ToLocalTime(long unixTimeStamp)
    {
        try
        {
            // 将毫秒时间戳转换为时间
            var dateTimeOffset = DateTimeOffset.FromUnixTimeMilliseconds(unixTimeStamp);
            // 将 DateTimeOffset 对象转换为本地时间
            var beijingOffset = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? TimeZoneInfo.ConvertTime(dateTimeOffset, TimeZoneInfo.FindSystemTimeZoneById("China Standard Time"))
                : TimeZoneInfo.ConvertTime(dateTimeOffset, TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai"));
            var localTime = beijingOffset.ToOffset(TimeSpan.FromHours(8));
            return localTime.ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch
        {
            var dtDateTime = new DateTime(1970, 1, 1);
            dtDateTime = dtDateTime.AddMilliseconds(Convert.ToInt64(unixTimeStamp)).ToLocalTime();
            return dtDateTime.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }

    /// <summary>
    ///     毫秒时间戳转为北京时间
    /// </summary>
    /// <returns>C#格式时间</returns>
    public static string ToLocalTime(string unixTimeStamp)
    {
        try
        {
            // 将毫秒时间戳转换为时间
            var dateTimeOffset = DateTimeOffset.FromUnixTimeMilliseconds(Convert.ToInt64(unixTimeStamp));
            // 将 DateTimeOffset 对象转换为本地时间
            var beijingOffset = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? TimeZoneInfo.ConvertTime(dateTimeOffset, TimeZoneInfo.FindSystemTimeZoneById("China Standard Time"))
                : TimeZoneInfo.ConvertTime(dateTimeOffset, TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai"));
            var localTime = beijingOffset.ToOffset(TimeSpan.FromHours(8));
            return localTime.ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch
        {
            var dtDateTime = new DateTime(1970, 1, 1);
            dtDateTime = dtDateTime.AddMilliseconds(Convert.ToInt64(unixTimeStamp)).ToLocalTime();
            return dtDateTime.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }
}