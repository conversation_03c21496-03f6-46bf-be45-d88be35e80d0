using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Profinet.Freedom;
using HslCommunication.Serial;

namespace IotGateway.Engine.Exec;

/// <summary>
///     udp连接
/// </summary>
public class UdpExec
{
    /// <summary>
    ///     tcp网口
    /// </summary>
    private FreedomUdpNet _spReadData;

    /// <summary>
    ///     连接Tcp
    /// </summary>
    /// <param name="ip">IP</param>
    /// <param name="port">端口</param>
    /// <param name="dataFormat">解析格式</param>
    /// <returns></returns>
    public bool Connect(string ip, int port, DataFormat dataFormat = DataFormat.ABCD)
    {
        _spReadData = new FreedomUdpNet
        {
            IpAddress = ip,
            Port = port,
            ByteTransform =
            {
                DataFormat = dataFormat
            }
        };
        return true;
    }

    /// <summary>
    ///     发送消息
    /// </summary>
    /// <param name="sendData">数据byte[]</param>
    /// <param name="type">返回格式默认byte,支持【binary,ascii,byte】</param>
    /// <param name="append">追加字符</param>
    /// <returns></returns>
    public object Send(byte[] sendData, string type = "byte", string append = "none")
    {
        try
        {
            // 追加字符
            sendData = append switch
            {
                "\\r" => SoftBasic.SpliceArray(sendData, new byte[] {0x0D}),
                "\\r\\n" => SoftBasic.SpliceArray(sendData, new byte[] {0x0D, 0x0A}),
                "\\n" => SoftBasic.SpliceArray(sendData, new byte[] {0x0A}),
                "crc16" => SoftCRC16.CRC16(sendData),
                _ => sendData
            };

            var read = _spReadData.ReadFromCoreServer(sendData);
            if (!read.IsSuccess) return null;
            switch (type)
            {
                case "byte":
                    return read.Content;
                case "ascii":
                    return Encoding.ASCII.GetString(read.Content).TrimEnd('\0');
                case "binary":
                    return SoftBasic.ByteToHexString(read.Content, ' ');
            }

            return BitConverter.ToString(read.Content).Replace("-", " ");
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    ///     发送消息
    /// </summary>
    /// <param name="data">数据byte[]</param>
    /// <param name="encoding">字符编码</param>
    /// <param name="type">返回格式默认byte,支持【binary,ascii,byte】</param>
    /// <param name="append"></param>
    /// <returns></returns>
    public object Send(string data, string encoding = "hex", string type = "byte", string append = "none")
    {
        try
        {
            var sendData = Array.Empty<byte>();
            switch (encoding)
            {
                case "hex":
                    sendData = SoftBasic.HexStringToBytes(data);
                    break;
                case "ascii":
                    sendData = SoftBasic.GetFromAsciiStringRender(data);
                    break;
            }

            // 追加字符
            sendData = append switch
            {
                "\\r" => SoftBasic.SpliceArray(sendData, new byte[] {0x0D}),
                "\\r\\n" => SoftBasic.SpliceArray(sendData, new byte[] {0x0D, 0x0A}),
                "\\n" => SoftBasic.SpliceArray(sendData, new byte[] {0x0A}),
                "crc16" => SoftCRC16.CRC16(sendData),
                _ => sendData
            };

            var read = _spReadData.ReadFromCoreServer(sendData);
            if (!read.IsSuccess) return null;
            switch (type)
            {
                case "byte":
                    return read.Content;
                case "ascii":
                    return Encoding.ASCII.GetString(read.Content).TrimEnd('\0');
                case "binary":
                    return SoftBasic.ByteToHexString(read.Content, ' ');
            }

            return BitConverter.ToString(read.Content).Replace("-", " ");
            //return read.ToMessageShowString();
        }
        catch
        {
            return null;
        }
    }
}