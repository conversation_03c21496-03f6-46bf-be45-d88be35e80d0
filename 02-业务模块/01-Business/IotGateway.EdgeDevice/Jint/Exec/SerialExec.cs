

using System.IO.Ports;
using HslCommunication.BasicFramework;
using HslCommunication.Profinet.Freedom;
using HslCommunication.Serial;

namespace IotGateway.Engine.Exec;

/// <summary>
///     串口连接
/// </summary>
public class SerialExec : ITransient, IDisposable
{
    /// <summary>
    ///     串口
    /// </summary>
    private FreedomSerial _spReadData;

    /// <summary>
    ///     打开串口
    /// </summary>
    /// <param name="serialNum">串口号</param>
    /// <param name="baudRate">波特率</param>
    /// <param name="dataBits">数据位</param>
    /// <param name="stop">停止位</param>
    /// <param name="checkout">校验位</param>
    /// <exception cref="AppFriendlyException"></exception>
    public bool OpenCom(string serialNum, int baudRate, int dataBits, int stop, int checkout)
    {
        if (_spReadData != null && _spReadData.IsOpen()) return true;

        _spReadData ??= new FreedomSerial();
        _spReadData.SerialPortInni(sp =>
        {
            sp.PortName = serialNum;
            sp.BaudRate = baudRate;
            sp.DataBits = dataBits;
            sp.StopBits = (StopBits) stop;
            sp.Parity = (Parity) checkout;
        });

        try
        {
            var open = _spReadData.Open();
            return open.IsSuccess;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    ///     发送消息
    /// </summary>
    /// <param name="sendData">数据byte[]</param>
    /// <param name="type">返回格式默认byte,支持【binary,ascii,byte】</param>
    /// <param name="sleepTime">等待时间,处理分包回复的场景</param>
    /// <param name="append">追加字符</param>
    /// <returns></returns>
    public object SendCom(byte[] sendData, string type = "byte", int sleepTime = 20, string append = "none")
    {
        try
        {
            if (sleepTime > 20)
                _spReadData.SleepTime = sleepTime;

            // 追加字符
            sendData = append switch
            {
                "\\r" => SoftBasic.SpliceArray(sendData, new byte[] {0x0D}),
                "\\r\\n" => SoftBasic.SpliceArray(sendData, new byte[] {0x0D, 0x0A}),
                "\\n" => SoftBasic.SpliceArray(sendData, new byte[] {0x0A}),
                "crc16" => SoftCRC16.CRC16(sendData),
                _ => sendData
            };

            var read = _spReadData.ReadFromCoreServer(sendData);
            if (!read.IsSuccess) return null;
            switch (type)
            {
                case "byte":
                    return read.Content;
                case "ascii":
                    return Encoding.ASCII.GetString(read.Content).TrimEnd('\0');
                case "binary":
                    return SoftBasic.ByteToHexString(read.Content, ' ');
            }

            return BitConverter.ToString(read.Content).Replace("-", " ");
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    ///     发送消息
    /// </summary>
    /// <param name="data">数据byte[]</param>
    /// <param name="encoding">字符编码</param>
    /// <param name="type">返回格式默认byte,支持【binary,ascii,byte】</param>
    /// <param name="sleepTime">等待时间,处理分包回复的场景</param>
    /// <param name="append">追加字符</param>
    /// <returns></returns>
    public object SendCom(string data = "", string encoding = "hex", string type = "byte", int sleepTime = 20, string append = "none")
    {
        try
        {
            if (sleepTime > 20)
                _spReadData.SleepTime = sleepTime;

            var sendData = Array.Empty<byte>();
            switch (encoding)
            {
                case "hex":
                    sendData = SoftBasic.HexStringToBytes(data);
                    break;
                case "ascii":
                    sendData = SoftBasic.GetFromAsciiStringRender(data);
                    break;
            }

            // 追加字符
            sendData = append switch
            {
                "\\r" => SoftBasic.SpliceArray(sendData, new byte[] {0x0D}),
                "\\r\\n" => SoftBasic.SpliceArray(sendData, new byte[] {0x0D, 0x0A}),
                "\\n" => SoftBasic.SpliceArray(sendData, new byte[] {0x0A}),
                "crc16" => SoftCRC16.CRC16(sendData),
                _ => sendData
            };

            var read = _spReadData.ReadFromCoreServer(sendData);
            if (!read.IsSuccess) return null;
            switch (type)
            {
                case "byte":
                    return read.Content;
                case "ascii":
                    return Encoding.ASCII.GetString(read.Content).TrimEnd('\0');
                case "binary":
                    return SoftBasic.ByteToHexString(read.Content, ' ');
            }

            return BitConverter.ToString(read.Content).Replace("-", " ");
            //return read.ToMessageShowString();
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    ///     关闭串口
    /// </summary>
    /// <returns></returns>
    public bool CloseCom()
    {
        try
        {
            _spReadData?.Close();
            _spReadData?.Dispose();
            return true;
        }
        catch
        {
            return true;
        }
    }

    /// <summary>
    /// </summary>
    public void Dispose()
    {
        try
        {
            _spReadData?.Close();
            _spReadData?.Dispose();
        }
        catch
        {
        }
    }
}