using System.Text.RegularExpressions;
using Common.Extension;
using Feng.IotGateway.Core.Service.Cache;
using NewLife.Caching;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Engine.Exec;

/// <summary>
///     脚本执行-操作变量
/// </summary>
public class Share : ISingleton
{
    private readonly CacheService _cacheService;
    public readonly ObservableDictionary<string, object> ShareDictionary = new();
    private static Timer _timer;

    public Share(CacheService cacheService)
    {
        _cacheService = cacheService;
        // 设置过期时间为5秒
        var ttl = 5000;
        // 启动定时器
        _timer = new Timer(OnTimedEvent, null, ttl, Timeout.Infinite);
    }

    #region 私有方法

    private void OnTimedEvent(object state)
    {
        // 遍历所有键值对，删除过期的数据
        var now = DateTime.ShangHai();
        foreach (var item in ShareDictionary)
            try
            {
                if (now >= ((dynamic) item.Value).Expires) ShareDictionary.TryRemove(item.Key, out _);
            }
            catch (Exception e)
            {
                Log.Error($"删除失败:{e.Message}");
            }

        // 重新启动定时器
        _timer.Change(5000, Timeout.Infinite);
    }

    /// <summary>
    ///     计算距离指定时间还有多长时间
    /// </summary>
    /// <param name="expire"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    private TimeSpan TimeToExecute(string expire)
    {
        // 获取当前时间和指定执行时间
        var timeString = DateTime.ShangHai().ToString("yyyy-MM-dd") + " " + expire; // 将 expire 转换为日期时间字符串
        if (!System.DateTime.TryParse(timeString, out var executeTime)) throw Oops.Oh("错误时间格式");
        var now = DateTime.ShangHai();
        // 如果当前时间已经超过了指定执行时间，则将执行时间加上一天
        if (now > executeTime)
            executeTime = executeTime.AddDays(1);
        // 计算距离指定时间还有多长时间
        var timeToExecute = executeTime - now;
        return timeToExecute;
    }

    #endregion

    #region 保存临时变量（持久化）

    /// <summary>
    ///     保存临时变量（持久化）
    /// </summary>
    /// <param name="identifier">标签</param>
    /// <param name="value">值</param>
    /// <param name="trigger">触发任务</param>
    /// <returns></returns>
    public bool Save(string identifier, object value,bool trigger =false)
    {
        if (value == null)
            return false;
        //校验是否存在nan的情况
        var isOk = SaveCheck(value);
        if (!isOk)
            return false;

        if (trigger)
            Publish(identifier);
        _cacheService.Set(identifier, System.DateTime.TryParseExact(value.ToString(), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out var time)
            ? time.ToString("yyyy-MM-dd HH:mm:ss")
            : value);
        return true;
    }

    /// <summary>
    ///     保存临时变量（持久化）
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="value"></param>
    /// <param name="expire">到期时间</param>
    /// <param name="trigger">触发任务</param>
    /// <returns></returns>
    public bool Save(string identifier, object value, int expire,bool trigger =false)
    {
        if (value == null)
            return false;
        //校验是否存在nan的情况
        var isOk = SaveCheck(value);
        if (!isOk)
            return false;
        if (trigger)
            Publish(identifier);
        return _cacheService.Set(identifier, System.DateTime.TryParseExact(value.ToString(), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out var time)
            ? time.ToString("yyyy-MM-dd HH:mm:ss")
            : value, TimeSpan.FromSeconds(expire));
    }

    /// <summary>
    ///     保存临时变量（持久化）
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="value"></param>
    /// <param name="expire">到期时间</param>
    /// <param name="trigger">触发任务</param>
    /// <returns></returns>
    public bool Save(string identifier, object value, string expire,bool trigger =false)
    {
        if (value == null)
            return false;
        //校验是否存在nan的情况
        var isOk = SaveCheck(value);
        if (!isOk)
            return false;
        if (trigger)
            Publish(identifier);
        var totalSeconds = TimeToExecute(expire).TotalSeconds;
        return _cacheService.Set(identifier, System.DateTime.TryParseExact(value.ToString(), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out var time)
            ? time.ToString("yyyy-MM-dd HH:mm:ss")
            : value, TimeSpan.FromSeconds(totalSeconds));
    }

    #endregion

    #region 保存临时变量

    /// <summary>
    ///     保存临时变量
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="value"></param>
    /// <param name="trigger">触发任务</param>
    /// <returns></returns>
    public bool Set(string identifier, object value,bool trigger =false)
    {
        if (value == null)
            return false;
        //校验是否存在nan的情况
        var isOk = SaveCheck(value);
        if (!isOk)
            return false;
        if (trigger)
            Publish(identifier);
        ShareDictionary.AddOrUpdate(identifier, new {Value = value, Expires = DateTime.ShangHai().AddDays(999)},
            (_, _) => new {Value = value, Expires = DateTime.ShangHai().AddDays(999)});
        return true;
    }

    /// <summary>
    ///     保存临时变量
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="value"></param>
    /// <param name="expire">到期时间</param>
    /// <param name="trigger">触发任务</param>
    /// <returns></returns>
    public bool Set(string identifier, object value, int expire,bool trigger =false)
    {
        if (value == null)
            return false;
        //校验是否存在nan的情况
        var isOk = SaveCheck(value);
        if (!isOk)
            return false;
        if (trigger)
            Publish(identifier);
        // 存储数据
        ShareDictionary.AddOrUpdate(identifier, new {Value = value, Expires = DateTime.ShangHai().AddSeconds(expire)},
            (_, _) => new {Value = value, Expires = DateTime.ShangHai().AddMilliseconds(expire * 1000)});
        return true;
    }

    /// <summary>
    ///     保存临时变量
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="value"></param>
    /// <param name="expire">到期时间</param>
    /// <param name="trigger">触发任务</param>
    /// <returns></returns>
    public bool Set(string identifier, object value, string expire,bool trigger =false)
    {
        if (value == null)
            return false;
        //校验是否存在nan的情况
        var isOk = SaveCheck(value);
        if (!isOk)
            return false;
        if (trigger)
            Publish(identifier);
        var timeSp = TimeToExecute(expire);
        // 存储数据
        ShareDictionary.AddOrUpdate(identifier, new {Value = value, Expires = DateTime.ShangHai().AddMilliseconds(timeSp.TotalMilliseconds)},
            (_, _) => new {Value = value, Expires = DateTime.ShangHai().AddMilliseconds(timeSp.TotalMilliseconds)});
        return true;
    }

    #endregion

    /// <summary>
    /// 触发消息事件
    /// </summary>
    /// <param name="identifier"></param>
    private void Publish(string identifier)
    {
        MessageCenter.PublishAsync(identifier, identifier);
    }

    /// <summary>
    /// 保存前检查
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    private bool SaveCheck(object value)
    {
        if (value == null)
            return false;
        value = value.GetJsonElementValue();
        if (value.GetJsonElementValue()?.GetType().ToString() == "SystemServices.Int32" || value.GetJsonElementValue()?.GetType().ToString() == "SystemServices.Double")
            if (double.IsNaN(Convert.ToDouble(value)))
                return false;

        return true;
    }
    
    private readonly Redis _rds =  new FullRedis(new RedisOptions
    {
        Configuration =  Regex.Replace(App.Configuration["Cache:RedisConnectionString"], @"(db=)\d+", "db=2"),
        Prefix = "IotGateway:"
    });
    
    /// <summary>
    ///     获取临时变量
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    public object Get(string identifier)
    {
        if (identifier == null)
            return false;
        if (App.Configuration["Cache:CacheType"] == "RedisCache")
        {
            var get = _rds.GetDictionary<string>($"IotGateway:{identifier}");
            if (get != null && get.Any())
            {
                if (get["data"].Trim().StartsWith("[") || get["data"].Trim().StartsWith("{"))
                {
                }

                var value = get["data"].TrimStart('\"').TrimEnd('\"');
                var set = Save(identifier, value);
                if (set)
                    _rds.Remove($"IotGateway:{identifier}");
                return value;
            }
        }

        object val = ShareDictionary.TryGetValue(identifier, out var value1)
            ? ((dynamic) value1).Value
            : _cacheService.Get<string>(identifier);
        return val;
    }
    
    /// <summary>
    ///     获取临时变量
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    public object Get<T>(string identifier)
    {
        if (identifier == null)
            return false;
        object val = ShareDictionary.TryGetValue(identifier, out var value)
            ? ((dynamic) value).Value
            : _cacheService.Get<T>(identifier);

        return val;
    }

    /// <summary>
    ///     删除临时变量
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public bool Remove(string identifier)
    {
        if (identifier == null)
            return false;
        ShareDictionary.TryRemove(identifier, out _);
        _cacheService.Remove(identifier);
        return true;
    }
}