namespace IotGateway.Engine.Exec;

/// <summary>
///     Ftp文件扩展
/// </summary>
public class FtpFileEx : ITransient
{
    /// <summary>
    ///     获取文件列表
    /// </summary>
    /// <param name="ftpUrl">远程路径</param>
    /// <param name="username">用户名称</param>
    /// <param name="password">密码</param>
    /// <returns></returns>
    public IEnumerable<FtpFile> List(string ftpUrl, string username, string password)
    {
        var fileList = new List<FtpFile>();

        try
        {
            var request = (FtpWebRequest)WebRequest.Create(ftpUrl);
            request.Method = WebRequestMethods.Ftp.ListDirectoryDetails;
            request.Credentials = new NetworkCredential(username, password);

            using var response = (FtpWebResponse)request.GetResponse();
            using var responseStream = response.GetResponseStream();
            using var reader = new StreamReader(responseStream);
            while (reader.ReadLine() is { } line)
            {
                FtpFile ftpFile = null;
                try
                {
                    ftpFile = ParseFtpFileLine(line) ?? ParseFtpFileLineWin(line);
                }
                catch (Exception ex)
                {
                    // 记录解析错误
                    Log.Error($"Failed to parse line '{line}': {ex.Message}");
                }

                if (ftpFile != null)
                    fileList.Add(ftpFile);
                else
                    // 可以记录未解析成功的行，便于调试
                    Log.Error($"Unable to parse line: {line}");
            }
        }
        catch (Exception ex)
        {
            // 根据业务需求决定是否抛出异常
            throw Oops.Oh($"读取失败：{ex.Message}");
        }

        return fileList;
    }

    /// <summary>
    ///     下载文件到本地
    /// </summary>
    /// <param name="remoteFilePath">远程路径</param>
    /// <param name="localFilePath">本地路径</param>
    /// <param name="username">用户名称</param>
    /// <param name="password">密码</param>
    /// <returns></returns>
    public bool Download(string remoteFilePath, string localFilePath, string username, string password)
    {
        try
        {
            var request = (FtpWebRequest)WebRequest.Create(remoteFilePath);
            request.Method = WebRequestMethods.Ftp.DownloadFile;
            request.Credentials = new NetworkCredential(username, password);
            using (var response = (FtpWebResponse)request.GetResponse())
            using (var responseStream = response.GetResponseStream())
            using (var fileStream = new FileStream(localFilePath, FileMode.Create))
            {
                responseStream.CopyTo(fileStream);
            }

            return true;
        }
        catch (Exception e)
        {
            throw Oops.Oh("文件下载失败：" + e.Message);
        }
    }

    /// <summary>
    ///     上传本地文件到Ftp服务器
    /// </summary>
    /// <param name="remoteFilePath"></param>
    /// <param name="localFilePath"></param>
    /// <param name="username"></param>
    /// <param name="password"></param>
    /// <returns></returns>
    public bool Upload(string remoteFilePath, string localFilePath, string username, string password)
    {
        try
        {
            // 创建一个 FtpWebRequest 对象
            var request = (FtpWebRequest)WebRequest.Create(remoteFilePath);
            request.Method = WebRequestMethods.Ftp.UploadFile;
            request.Credentials = new NetworkCredential(username, password);

            // 读取本地文件内容
            byte[] fileContents;
            using (var sourceStream = new StreamReader(localFilePath))
            {
                fileContents = Encoding.UTF8.GetBytes(sourceStream.ReadToEnd());
            }

            // 将文件内容上传到 FTP 服务器
            using (var requestStream = request.GetRequestStream())
            {
                requestStream.Write(fileContents, 0, fileContents.Length);
            }

            return true;
        }
        catch (Exception ex)
        {
            throw Oops.Oh("文件上传失败：" + ex.Message);
        }
    }

    private FtpFile ParseFtpFileLine(string line)
    {
        // FTP服务器返回的行格式通常为:
        // -rwxr-xr-x   1 <USER> <GROUP>  12345 Nov  1 12:34 filename.txt
        // drwxr-xr-x   2 <USER> <GROUP>  4096 Nov  1 12:34 foldername

        var regex = new Regex(
            @"(?<type>[dr])(?<permissions>.+?)\s+(?<numLinks>\d+)\s+(?<owner>\S+)\s+(?<group>\S+)\s+(?<size>\d+)\s+(?<month>\S+)\s+(?<day>\d+)\s+(?<timeOrYear>(\d{2}:\d{2}|\d{4}))\s+(?<name>.+)");
        var match = regex.Match(line);

        if (match.Success)
        {
            var fileType = match.Groups["type"].Value == "d" ? "Directory" : "Files";
            var fileName = match.Groups["name"].Value;
            var fileSize = long.Parse(match.Groups["size"].Value);
            var createdTime = DateTime.Parse($"{match.Groups["month"].Value} {match.Groups["day"].Value} {match.Groups["timeOrYear"].Value}");
            var modifiedTime = DateTime.Parse($"{match.Groups["month"].Value} {match.Groups["day"].Value} {match.Groups["timeOrYear"].Value}");

            return new FtpFile(fileType, fileName, fileSize, createdTime, modifiedTime);
        }

        return null;
    }

    private FtpFile ParseFtpFileLineWin(string line)
    {
        // Example line: "08-15-24  06:01PM                   27 line14.csv"
        var regex = new Regex(@"(?<date>\S+\s+\S+)\s+(?<size>\d+)\s+(?<name>.+)");
        var match = regex.Match(line);

        if (match.Success)
        {
            var fileName = match.Groups["name"].Value;
            var fileSize = long.Parse(match.Groups["size"].Value);

            // Convert the date and time string into DateTime
            var dateTimeString = match.Groups["date"].Value;
            DateTime modifiedTime;

            // Attempt to parse the date
            string[] formats = { "MM-dd-yy  hh:mmtt", "MM-dd-yy  hh:mm tt" }; // Multiple formats
            if (DateTime.TryParseExact(dateTimeString, formats,
                    CultureInfo.InvariantCulture,
                    DateTimeStyles.None,
                    out modifiedTime))
            {
                // For simplicity, assuming created time is the same as modified time
                var createdTime = modifiedTime;

                return new FtpFile("Files", fileName, fileSize, createdTime, modifiedTime);
            }

            Console.WriteLine($"Failed to parse date: {dateTimeString}");
        }

        return null;
    }
}

/// <summary>
/// </summary>
public class FtpFile
{
    /// <summary>
    /// </summary>
    /// <param name="fileType"></param>
    /// <param name="fileName"></param>
    /// <param name="size"></param>
    /// <param name="createdTime"></param>
    /// <param name="modifiedTime"></param>
    public FtpFile(string fileType, string fileName, long size, DateTime createdTime, DateTime modifiedTime)
    {
        FileType = fileType;
        FileName = fileName;
        Size = size;
        CreatedTime = createdTime;
        ModifiedTime = modifiedTime;
    }

    /// <summary>
    /// </summary>
    public string FileType { get; set; }

    /// <summary>
    /// </summary>
    public string FileName { get; set; }

    /// <summary>
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// </summary>
    public DateTime ModifiedTime { get; set; }
}