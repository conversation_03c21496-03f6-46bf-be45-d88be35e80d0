using Common.Extension;

namespace IotGateway.Engine.Exec;

/// <summary>
///     文件操作
/// </summary>
public class FileEx : ISingleton
{
    /// <summary>
    ///     获取文件列表
    /// </summary>
    /// <returns></returns>
    public dynamic List(string fileName, string path)
    {
        // 磁盘操作，根据传入路径和文件名称查找指定目录下的文件，返国值格式[{type:"dir/fi1e'。name:'文件名”。's5ze”:'文件尺寸’。createTim'创建时间”，1asturlteTime:"最后修改时间')]
        if (!Directory.Exists(path)) return "Directory not found";

        var directoryPath = Path.Combine(path, fileName);
        var isDirectory = Directory.Exists(directoryPath);
        if (isDirectory)
        {
            // 当前目录下的所有文件
            var files = Directory.GetFiles(directoryPath);
            // 当前目录下所有目录
            var dirs = Directory.GetDirectories(directoryPath);
            var allItems = new List<dynamic>();
            // 添加子目录信息
            foreach (var dir in dirs)
                allItems.Add(new
                {
                    Type = "directory",
                    Name = Path.GetFileName(dir),
                    Size = 0, // 目录没有大小信息，可以留空或用0表示
                    CreateTime = Directory.GetCreationTime(dir),
                    LastUpdateTime = Directory.GetLastWriteTime(dir)
                });
            // // 获取子目录中的所有文件
            // string[] files = Directory.GetFiles(dir);
            // foreach (string file in files)
            // {
            //     allItems.Add(new
            //     {
            //         Type = "file",
            //         Name = Path.GetFileName(file),
            //         Size = new FileInfo(file).Length,
            //         CreateTime = new FileInfo(file).CreationTime,
            //         LastUpdateTime = new FileInfo(file).LastWriteTime
            //     });
            // }
            // 返回包含文件信息的动态对象数组
            foreach (var file in files)
            {
                var fileInfo = new FileInfo(file);
                allItems.Add(new
                {
                    Type = "file",
                    Name = Path.GetFileName(file),
                    Size = fileInfo.Length,
                    CreateTime = fileInfo.CreationTime,
                    LastUpdateTime = fileInfo.LastWriteTime
                });
            }

            return allItems;
        }

        {
            var fileInfo = new FileInfo(Path.Combine(path, fileName));
            if (!fileInfo.Exists) return "Files not found";
            // 返回单个文件的信息
            return new
            {
                Type = "file",
                fileInfo.Name,
                Size = fileInfo.Length,
                CreateTime = fileInfo.CreationTime,
                LastUpdateTime = fileInfo.LastWriteTime
            };
        }
    }

    /// <summary>
    ///     移动文件目录
    /// </summary>
    /// <param name="fileName"></param>
    /// <param name="path"></param>
    /// <param name="newPath"></param>
    /// <returns></returns>
    public bool Move(string fileName, string path, string newPath)
    {
        if (string.IsNullOrEmpty(fileName) || string.IsNullOrEmpty(path) || string.IsNullOrEmpty(newPath))
            throw Oops.Oh("请按照说明完整填写内容！");
        // 检查源路径是否存在
        if (!Directory.Exists(path))
            throw Oops.Oh($"path:{path} not found");
        // 检查是否是目录
        if (Directory.Exists(Path.Combine(path, fileName)))
        {
            Directory.Move(Path.Combine(path, fileName), newPath);
            return true;
        }

        // 待移动的文件
        var fileInfo = new FileInfo(Path.Combine(path, fileName));
        if (!fileInfo.Exists)
            throw Oops.Oh($"Files:{fileInfo} not found");
        var newFileInfo = Path.Combine(newPath, fileName);
        File.Move(Path.Combine(path, fileName), newFileInfo);
        return true;
    }

    /// <summary>
    ///     删除文件目录
    /// </summary>
    /// <param name="fileName"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    public bool Remove(string fileName, string path)
    {
        // 检查源路径是否存在
        if (!Directory.Exists(path))
            return true;
        // 检查是否是目录
        if (Directory.Exists(Path.Combine(path, fileName)))
            Directory.Delete(Path.Combine(path, fileName));
        else if (File.Exists(Path.Combine(path, fileName))) File.Delete(Path.Combine(path, fileName));
        return true;
    }

    /// <summary>
    ///     创建文件夹
    /// </summary>
    /// <param name="dirName"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    public bool CreateDir(string dirName, string path)
    {
        if (string.IsNullOrEmpty(path) || string.IsNullOrEmpty(dirName))
            throw Oops.Oh("请按照说明完整填写内容！");
        // 检查路径是否存在
        if (!Directory.Exists(path)) Directory.CreateDirectory(path);
        Directory.CreateDirectory(Path.Combine(path, dirName));
        return true;
    }

    /// <summary>
    ///     创建文件
    /// </summary>
    /// <param name="fileName"></param>
    /// <param name="path"></param>
    /// <param name="content">内容</param>
    /// <param name="append">是否追加写入</param>
    /// <returns></returns>
    public bool CreateFile(string fileName, string path, string content, bool append = true)
    {
        if (string.IsNullOrEmpty(path) || string.IsNullOrEmpty(fileName))
            throw Oops.Oh("请按照说明完整填写内容！");
        // 检查路径是否存在
        if (!Directory.Exists(path)) Directory.CreateDirectory(path);
        if (append)
            File.AppendAllText(Path.Combine(path, fileName), content);
        else
            File.WriteAllText(Path.Combine(path, fileName), content);
        return true;
    }

    /// <summary>
    ///     读取文本内容
    /// </summary>
    /// <param name="fileName"></param>
    /// <param name="path"></param>
    /// <param name="enCoding">编码格式</param>
    /// <returns></returns>
    public dynamic ReadText(string fileName, string path, string enCoding = "utf-8")
    {
        if (string.IsNullOrEmpty(path) || string.IsNullOrEmpty(fileName))
            throw Oops.Oh("请按照说明完整填写内容！");
        // 检查是否是目录
        if (Directory.Exists(Path.Combine(path, fileName)))
            throw Oops.Oh($"{fileName} 是文件夹，不支持读取！");
        if (enCoding == "utf-8")
            return File.ReadAllText(Path.Combine(path, fileName));
        return File.ReadAllBytes(Path.Combine(path, fileName));
    }

    /// <summary>
    ///     读取文本内容，从指定行开始读取
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="startLine">开始行</param>
    /// <param name="numberOfLines">读取多少行</param>
    /// <param name="enCoding">编码格式</param>
    /// <returns></returns>
    public dynamic ReadTextLines(string filePath, int startLine, int numberOfLines, string enCoding = "utf-8")
    {
        var lines = new List<string>();
        // 使用 StreamReader 打开文件
        var encoding = Encoding.GetEncoding(enCoding);
        using var reader = new StreamReader(filePath, encoding);
        string line;
        var currentLine = 0;
        // 跳过前 startLine - 1 行
        while (currentLine < startLine - 1 && (line = reader.ReadLine()) != null) currentLine++;
        // 开始读取行
        if (numberOfLines == 0)
            // 读取所有剩余行
            while ((line = reader.ReadLine()) != null)
                lines.Add(line); // 将每一行添加到 List 中
        else
            // 读取 numberOfLines 行
            while (currentLine < startLine + numberOfLines - 1 && (line = reader.ReadLine()) != null)
            {
                lines.Add(line); // 将每一行添加到 List 中
                currentLine++;
            }

        return lines;
    }

    /// <summary>
    ///     下载文件
    /// </summary>
    /// <param name="fileName"></param>
    /// <param name="path"></param>
    /// <param name="url">地址</param>
    /// <returns></returns>
    public void Download(string fileName, string path, string url)
    {
        var sharedFilePath = url;
        var localFilePath = Path.Combine(path, fileName);
        try
        {
            File.Copy(sharedFilePath, localFilePath, true);
        }
        catch (Exception ex)
        {
            throw Oops.Oh("下载失败:" + ex.Message);
        }
    }

    /// <summary>
    ///     下载文件
    /// </summary>
    /// <param name="filePath"></param>
    /// <returns></returns>
    public int Length(string filePath)
    {
        try
        {
            // 读取文件所有行到一个字符串数组中
            var lines = File.ReadAllLines(filePath);
            // 统计行数
            var lineCount = lines.Length;
            return lineCount;
        }
        catch (IOException e)
        {
            throw Oops.Oh($"读取文件时出现错误: {e.Message}");
        }
    }
}