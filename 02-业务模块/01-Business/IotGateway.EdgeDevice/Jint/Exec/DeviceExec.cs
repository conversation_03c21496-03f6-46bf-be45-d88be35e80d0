using Furion.ClayObject.Extensions;
using IotGateway.EdgeDevice;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Engine.Exec;

/// <summary>
///     设备相关
/// </summary>
public class DeviceExec : ISingleton
{
    // key=DeviceName;  value.Key = tag;  value = 属性标识 用于根据标签获取所属属性
    public readonly ConcurrentDictionary<string, Dictionary<string, List<DeviceVariable>>> TagDeviceValues = new();

    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public DeviceExec(IServiceProvider services)
    {
        Services = services;
    }

    private IServiceProvider Services { get; }

    #region 设备开关机状态

    /// <summary>
    ///     全部设备连接状态
    /// </summary>
    /// <returns></returns>
    public object Status()
    {
        using var scope = Services.CreateScope();
        var deviceService = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<Device>>();
        //设备集合
        var deviceList = deviceService.AsQueryable().Where(w => w.Enable == true).ToList();
        //采集设备
        var initializeDeviceService = scope.ServiceProvider.GetRequiredService<DeviceHostedService>();
        var enumerable = deviceList.Select(s => new
        {
            s.DeviceName,
            OnLine = initializeDeviceService.DeviceConnectStatus(s.DeviceName)
        });
        return enumerable;
    }

    /// <summary>
    ///     设备连接状态
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="duration">有效时间范围(秒)</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public bool Status(string deviceName, int duration = 0)
    {
        using var scope = Services.CreateScope();
        var deviceService = scope.ServiceProvider.GetRequiredService<DeviceHostedService>();
        var status = deviceService.DeviceConnectStatus(deviceName);
        if (status || duration <= 0) return status;
        var deviceStatus = deviceService.DeviceThreads.Values.FirstOrDefault(x => x.Device.DeviceName == deviceName)?.DeviceStatus;
        if (deviceStatus == null)
            return false;
        var durationTime = Convert.ToInt64((DateTime.Now() - Convert.ToDateTime(deviceStatus.DeviceStatusChangeTime)).TotalMilliseconds);
        var close = durationTime <= duration * 1000;
        return close;
    }

    /// <summary>
    ///     设备连接状态
    /// </summary>
    /// <param name="deviceName">设备连接状态</param>
    /// <param name="otherName">别名</param>
    /// <param name="duration">有效时间范围(秒)</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public bool Status(string deviceName, string otherName, int duration = 0)
    {
        using var scope = Services.CreateScope();
        var deviceService = scope.ServiceProvider.GetRequiredService<DeviceHostedService>();
        var status = deviceService.DeviceConnectStatus(deviceName, otherName);
        if (status || duration <= 0) return status;
        var deviceStatus = deviceService.DeviceThreads.Values.FirstOrDefault(x => x.Device.DeviceName == deviceName && x.Device.OtherName == otherName)?.DeviceStatus;
        if (deviceStatus == null)
            return false;
        var durationTime = Convert.ToInt64((DateTime.Now() - Convert.ToDateTime(deviceStatus.DeviceStatusChangeTime)).TotalMilliseconds);
        var close = durationTime <= duration * 1000;
        return close;
    }

    #endregion

    #region 设备属性

    /// <summary>
    ///     初始化对象
    /// </summary>
    /// <param name="deviceName">设备名称,为保证全局唯一性</param>
    /// <param name="deviceValues">设备属性</param>
    public void Load(string deviceName, ConcurrentDictionary<string, DriverReturnValueModel> deviceValues)
    {
        foreach (var param in deviceValues)
            DataStorage.Instance.Set(deviceName + "." + param.Key, param.Value);
    }

    /// <summary>
    ///     初始化对象
    /// </summary>
    /// <param name="deviceName">设备名称,为保证全局唯一性</param>
    /// <param name="key">设备属性</param>
    /// <param name="driverReturnValueModel">值</param>
    public void Load(string deviceName, string key, DriverReturnValueModel driverReturnValueModel)
    {
        DataStorage.Instance.Set(deviceName + "." + key, driverReturnValueModel);
    }

    /// <summary>
    ///     获取上一次的值
    /// </summary>
    /// <param name="identifier">get</param>
    /// <returns></returns>
    public object Cookie(string identifier)
    {
        var property = DataStorage.Instance.Get(identifier);
        if (property == null)
            return null;
        var cookieValue = property.CookieValue;
        if (cookieValue == null)
            return null;
        try
        {
            switch (property.TransitionType)
            {
                case TransPondDataTypeEnum.Bool:
                    return Convert.ToBoolean(cookieValue);
                case TransPondDataTypeEnum.Int:
                    return Convert.ToInt64(cookieValue);
                case TransPondDataTypeEnum.Double:
                    return Convert.ToDouble(cookieValue);
                default:
                    return cookieValue;
            }
        }
        catch
        {
            return cookieValue;
        }
    }

    /// <summary>
    ///     获取属性值
    /// </summary>
    /// <param name="identifier">get</param>
    /// <returns></returns>
    public object Get(string identifier)
    {
        var property = DataStorage.Instance.Get(identifier);
        if (property == null)
            return null;
        var value = property.Value;
        if (value == null)
            return null;
        try
        {
            switch (property.TransitionType)
            {
                case TransPondDataTypeEnum.Bool:
                    return Convert.ToBoolean(value);
                case TransPondDataTypeEnum.Int:
                    return Convert.ToInt64(value);
                case TransPondDataTypeEnum.Double:
                    return Convert.ToDouble(value);
                default:
                    return value;
            }
        }
        catch
        {
            return value;
        }
    }

    /// <summary>
    ///     获取属性对象
    /// </summary>
    /// <param name="identifier">get</param>
    /// <returns></returns>
    public object GetData(string identifier)
    {
        var property = DataStorage.Instance.Get(identifier);
        if (property == null)
            return null;
        return property;
    }

    #endregion

    #region 设备标签

    /// <summary>
    ///     设备开始采集时将标签属性持久化到内存
    /// </summary>
    /// <param name="device"></param>
    public void LoadTag(Device device)
    {
        // 每次调用都应该清空历史的缓存
        if (TagDeviceValues.ContainsKey(device.DeviceName))
            TagDeviceValues.TryRemove(device.DeviceName, out _);

        // 含标签的数据
        foreach (var deviceVariable in device.DeviceVariable.Where(w => w.Tags != null && w.Tags.Count != 0))
            if (deviceVariable.Tags != null)
                foreach (var tag in deviceVariable.Tags)
                {
                    if (!TagDeviceValues.ContainsKey(device.DeviceName))
                        TagDeviceValues.TryAdd(device.DeviceName, new Dictionary<string, List<DeviceVariable>>());

                    var tagDic = TagDeviceValues[device.DeviceName];
                    if (tagDic.ContainsKey(tag))
                    {
                        var variable = tagDic[tag];
                        if (!variable.Contains(deviceVariable))
                            variable.Add(deviceVariable);
                        tagDic[tag] = variable;
                    }
                    else
                    {
                        tagDic.Add(tag, new List<DeviceVariable> {deviceVariable});
                    }
                }
    }

    /// <summary>
    ///     根据设备标签-返回属性标识符
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="tag">get</param>
    /// <returns></returns>
    public List<string> GetTag(string deviceName, string tag)
    {
        if (!TagDeviceValues.TryGetValue(deviceName, out var tagDic))
            return new List<string>();
        return !tagDic.TryGetValue(tag, out var value) ? new List<string>() : value.Select(s => s.Identifier).ToList();
    }

    /// <summary>
    ///     根据设备标签-返回属性对象
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="tag">get</param>
    /// <returns></returns>
    public List<DeviceVariable> GetDeviceVariableByTag(string deviceName, string tag)
    {
        if (!TagDeviceValues.TryGetValue(deviceName, out var tagDic))
            return new List<DeviceVariable>();
        return !tagDic.TryGetValue(tag, out var value) ? new List<DeviceVariable>() : value;
    }

    /// <summary>
    ///     根据设备标签获取属性
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="tag">get</param>
    /// <returns></returns>
    [Obsolete]
    public Dictionary<string, object> GetTagValue(string deviceName, string tag)
    {
        var output = new Dictionary<string, object>();
        if (!TagDeviceValues.TryGetValue(deviceName, out var tagDic))
            return output;
        if (!tagDic.TryGetValue(tag, out var value))
            return output;

        foreach (var variable in value)
        {
            var property = DataStorage.Instance.Get(deviceName + "." + variable.Identifier);
            output.Add(variable.Identifier, property?.Value);
        }

        return output;
    }

    /// <summary>
    ///     根据设备标签-返回属性标识符和值
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="tag">get</param>
    /// <returns></returns>
    public Dictionary<string, object> GetIdentifierAndValueByTag(string deviceName, string tag)
    {
        var output = new Dictionary<string, object>();
        if (!TagDeviceValues.TryGetValue(deviceName, out var tagDic))
            return output;
        if (!tagDic.TryGetValue(tag, out var value))
            return output;

        foreach (var variable in value)
        {
            var property = DataStorage.Instance.Get(deviceName + "." + variable.Identifier);
            output.Add(variable.Identifier, property?.Value);
        }

        return output;
    }

    /// <summary>
    ///     根据设备标签-返回属性和值
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="tag">get</param>
    /// <returns></returns>
    public dynamic GetDeviceVariableAndValueByTag(string deviceName, string tag)
    {
        var output = new Dictionary<string, object>();
        if (!TagDeviceValues.TryGetValue(deviceName, out var tagDic))
            return output;
        if (!tagDic.TryGetValue(tag, out var value))
            return output;

        foreach (var variable in value)
        {
            var property = DataStorage.Instance.Get(deviceName + "." + variable.Identifier);
            output.Add(variable.Identifier, new {property?.Value, DeviceVariable = variable});
        }

        return output;
    }

    #endregion

    #region 设备写入

    /// <summary>
    ///     写设备属性为【手动写值】属性值
    /// </summary>
    /// <param name="deviceName">设备名称</param>
    /// <param name="values">值</param>
    /// <param name="eval">是否支持表达式解析</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public dynamic SetVariable(string deviceName, object values, bool eval = false)
    {
        return WriteCommon(deviceName, values, eval);
    }

    /// <summary>
    ///     设备地址写入
    /// </summary>
    /// <param name="deviceName">设备名称</param>
    /// <param name="values">值</param>
    /// <param name="eval"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public dynamic WriteDataToDevice(string deviceName, object values, bool eval = false)
    {
        return WriteCommon(deviceName, values, eval, 2);
    }

    /// <summary>
    ///     静态点位写入
    /// </summary>
    /// <param name="deviceName">设备名称</param>
    /// <param name="values">值</param>
    /// <param name="eval"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public dynamic WriteDataToStatic(string deviceName, object values, bool eval = false)
    {
        return WriteCommon(deviceName, values, eval, 1);
    }

    /// <summary>
    /// 调用写入统一入口
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="values"></param>
    /// <param name="eval"></param>
    /// <param name="type"></param>
    /// <returns></returns>
    private dynamic WriteCommon(string deviceName, object values, bool eval = false,int type = 0)
    {
        try
        {
            // 接收传入参数
            IDictionary<string, object> dicValues = null;
            if (values != null)
                dicValues = DictionaryExtensions.ToDictionary(values);

            if (dicValues == null)
                return new {success = false, error = "传入参数是空", data = new List<dynamic>()};
            var initializeDevice = Services.GetService<DeviceHostedService>();

            // 采集线程
            var deviceThread = initializeDevice.DeviceThreads.Values.FirstOrDefault(x => x.Device.DeviceName == deviceName);
            if (deviceThread == null)
                return new {success = false, error = "设备已经禁用,暂不支持", data = new List<dynamic>()};
            if (deviceThread.Device == null)
                return new {success = false, error = "设备未启用，请重启设备", data = new List<dynamic>()};

            return deviceThread.DeviceWrite(new DeviceWriteRequest
            {
                DeviceName = deviceName,
                Params = dicValues.ToDictionary(k => k.Key, v => v.Value.ToString()),
                Eval = eval
            }, type).Result;
        }
        catch (Exception e)
        {
            return new {success = false, error = e.Message, data = new List<dynamic>()};
        }
    }

    #endregion
}