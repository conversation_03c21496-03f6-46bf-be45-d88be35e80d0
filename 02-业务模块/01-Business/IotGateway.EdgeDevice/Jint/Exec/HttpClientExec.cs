using Furion.ClayObject.Extensions;
using Furion.RemoteRequest.Extensions;

namespace IotGateway.Engine.Exec;

/// <summary>
///     http
/// </summary>
public class HttpClientExec
{
    /// <summary>
    ///     Get请求
    /// </summary>
    /// <returns></returns>
    public object Get(string url, object headers, object query, int timeout = 5, string contentType = "application/json")
    {
        var statusCode = HttpStatusCode.OK;
        try
        {
            IDictionary<string, object> dicHeaders = null;
            if (headers != null)
                dicHeaders = DictionaryExtensions.ToDictionary(headers);
            IDictionary<string, object> dicQuery = null;
            if (query != null)
                dicQuery = DictionaryExtensions.ToDictionary(query);
            var res = url
                .SetHttpMethod(HttpMethod.Get)
                .SetHeaders(dicHeaders)
                .SetQueries(dicQuery)
                .SetContentType(contentType)
                .SetClient(() => new HttpClient {Timeout = TimeSpan.FromSeconds(timeout)})
                .SetContentEncoding(Encoding.UTF8)
                .OnException((_, errors, errorMsg) =>
                {
                    if (errors != null)
                        statusCode = errors.StatusCode;
                    throw Oops.Oh(errors != null ? (int) errors.StatusCode + " " + errorMsg : errorMsg);
                })
                .GetAsStringAsync().GetAwaiter().GetResult();
            return new
            {
                statusCode,
                success = true,
                data = res
            };
        }
        catch (Exception e)
        {
            return new
            {
                statusCode,
                success = false,
                data = e.Message
            };
        }
    }

    /// <summary>
    ///     POST请求
    /// </summary>
    /// <returns></returns>
    public object Post(string url, object headers, object body, int timeout = 5, string contentType = "application/json")
    {
        var statusCode = HttpStatusCode.OK;
        try
        {
            IDictionary<string, object> dicHeaders = null;
            if (headers != null)
                dicHeaders = DictionaryExtensions.ToDictionary(headers);
            var res = url
                .SetHttpMethod(HttpMethod.Post)
                .SetHeaders(dicHeaders)
                .SetBody(body)
                .SetClient(() => new HttpClient {Timeout = TimeSpan.FromSeconds(timeout)})
                .SetContentType(contentType)
                .SetContentEncoding(Encoding.UTF8)
                .OnException((_, errors, errorMsg) =>
                {
                    if (errors != null)
                        statusCode = errors.StatusCode;
                    throw Oops.Oh(errors != null ? (int) errors.StatusCode + " " + errorMsg : errorMsg);
                })
                .PostAsStringAsync().GetAwaiter().GetResult();
            return new
            {
                statusCode,
                success = true,
                data = res
            };
        }
        catch (Exception e)
        {
            return new
            {
                statusCode,
                success = false,
                data = e.Message
            };
        }
    }
}