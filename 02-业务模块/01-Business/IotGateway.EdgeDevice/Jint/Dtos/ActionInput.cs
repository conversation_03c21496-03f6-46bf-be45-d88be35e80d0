namespace IotGateway.Engine;

/// <summary>
///     执行脚本请求参数
/// </summary>
public class DeviceActionInput
{
    /// <summary>
    ///     脚本内容
    /// </summary>
    [Required]
    public string Content { get; set; }

    /// <summary>
    ///     设备Id
    /// </summary>
    public long DeviceId { get; set; }
}

/// <summary>
///     执行脚本-请求参数
/// </summary>
public class ActionInput
{
    /// <summary>
    ///     脚本内容
    /// </summary>
    [Required]
    public string Content { get; set; }
}

/// <summary>
///     带参数执行脚本-请求参数
/// </summary>
public class ActionScriptConValueInput : DeviceActionInput
{
    /// <summary>
    ///     参数
    /// </summary>
    public Dictionary<string, object> Value { get; set; } = new();
}

/// <summary>
/// </summary>
public class TransmitRunInput
{
    /// <summary>
    ///     脚本内容
    /// </summary>
    [Required]
    public string Content { get; set; }

    /// <summary>
    ///     1实时数据；2离线数据； 3设备属性同步
    /// </summary>
    public int Type { get; set; }

    /// <summary>
    ///     源数据
    /// </summary>
    public object? Data { get; set; }
}