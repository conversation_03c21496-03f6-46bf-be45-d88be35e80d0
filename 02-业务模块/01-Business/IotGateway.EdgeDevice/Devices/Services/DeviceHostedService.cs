using NewLife.Serialization;

namespace IotGateway.EdgeDevice;

/// <summary>
/// </summary>
public class DeviceHostedService : IHostedService, IDisposable
{
    /// <summary>
    ///     采集协议底层单例服务
    /// </summary>
    private readonly DriverHostedService _driverService;

    /// <summary>
    ///     socket消息推送
    /// </summary>
    private readonly SendMessageService _socket;

    /// <summary>
    ///     全局采集线程
    /// </summary>
    public ConcurrentDictionary<long, DeviceThread> DeviceThreads = new();

    /// <summary>
    ///     全局设备事件线程
    /// </summary>
    public ConcurrentDictionary<long, DeviceEventThread> DeviceEventThreads = new();

    /// <summary>
    ///     Sqlsugar单例服务
    /// </summary>
    private readonly ISqlSugarClient _db;

    /// <summary>
    /// </summary>
    private readonly WriteService _writeService;

    /// <summary>
    /// </summary>
    /// <param name="driverService">驱动dll</param>
    /// <param name="services">作用域</param>
    /// <param name="socket">socket推送</param>
    /// <param name="db"></param>
    /// <param name="writeService">写入TDengine库</param>
    public DeviceHostedService(DriverHostedService driverService, IServiceProvider services, SendMessageService socket, ISqlSugarClient db, WriteService writeService)
    {
        _driverService = driverService;
        Services = services;
        _socket = socket;
        _db = db;
        _writeService = writeService;
    }

    /// <summary>
    ///     启动采集程序
    /// </summary>
    /// <param name="cancellationToken"></param>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        // 添加延迟等待驱动加载完成
        await Task.Delay(5000, cancellationToken);
        var deviceList = await _db.CopyNew().Queryable<Device>().Where(w => w.Enable == true).ToListAsync(cancellationToken);
        foreach (var device in deviceList)
            try
            {
                await CreateDeviceThread(device.Id);
            }
            catch (Exception ex)
            {
                Log.Error($"【采集服务初始化】 Error:{ex.Message}");
            }
    }

    /// <summary>
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        foreach (var (_, deviceThread) in DeviceThreads)
            deviceThread.StopThread();
        return Task.CompletedTask;
    }

    /// <summary>
    /// </summary>
    private IServiceProvider Services { get; }

    #region 设备配置

    /// <summary>
    ///     创建设备采集线程
    /// </summary>
    /// <param name="deviceId"></param>
    public async Task CreateDeviceThread(long deviceId)
    {
        // 校验授权
        await AuthorizationUtil.IsAuthorized();

        // 全部启动设备
        var deviceList = await _db.CopyNew().Queryable<Device>()
            .Where(w => w.Enable == true)
            .Includes(c => c.Driver)
            .Includes(c => c.DeviceConfigs)
            .Includes(c => c.DeviceEvent.Where(w => w.Status).ToList())
            .Includes(c => c.DeviceVariable.Where(w => w.Enable).ToList())
            .ToListAsync();

        // 当前设备
        var device = deviceList.FirstOrDefault(w => w.Id == deviceId);
        if (device == null)
            throw Oops.Oh("设备已经被删除！");

        // 驱动
        var driverInfo = _driverService.DriverInfos.FirstOrDefault(x => x.Type.FullName == device.Driver?.AssembleName);
        if (driverInfo == null)
            throw Oops.Oh($"未授权:【{device.Driver?.AssembleName}】驱动！");

        var types = new[] { typeof(DriverInfoDto) };
        var param = new object[] { new DriverInfoDto { Socket = _socket, DeviceId = device.Id, DeviceName = device.DeviceName, ConnectType = device.Driver!.ConnectType } };
        // 验证程序集是否正确
        var constructor = driverInfo.Type.GetConstructor(types);
        if (constructor == null)
            throw Oops.Oh("协议版本不适配,请联系管理员！");

        var deviceObj = constructor.Invoke(param) as IDriver;

        // 同步设备配置信息
        await SyncDeviceConfig(driverInfo, device, deviceObj);

        // 同步设备系统属性
        await SyncDeviceVariable(device);
        try
        {
            var deviceThread = new DeviceThread(device, deviceObj, _socket, Services, _writeService);
            DeviceThreads.TryAdd(device.Id, deviceThread);
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"创建设备采集线程Error:{ex.Message}");
        }

        foreach (var deviceEvent in device.DeviceEvent.Where(w => w.Status))
            try
            {
                if (DeviceEventThreads.TryGetValue(deviceEvent.Id, out var thread))
                {
                    thread.Dispose();
                    DeviceEventThreads.Remove(deviceEvent.Id);
                }

                var deviceEventThread = new DeviceEventThread(deviceEvent, Services, device);
                DeviceEventThreads.TryAdd(deviceEvent.Id, deviceEventThread);
            }
            catch (Exception e)
            {
                throw Oops.Oh($"创建设备事件线程Error:{e.Message}");
            }

        // 同步网关采集设备配置信息
        await MetaPush(deviceList);
    }

    /// <summary>
    ///     同步设备系统属性信息
    /// </summary>
    /// <param name="device"></param>
    private async Task SyncDeviceVariable(Device device)
    {
        var systemDeviceVariableList = device.DeviceVariable.Where(w => w.IsSystem).ToList();
        // 设备系统属性点
        var identifierList = systemDeviceVariableList.Select(s => s.Identifier).ToList();

        var deviceVariableList = await CreateSystemVariables(device);
        deviceVariableList = deviceVariableList.Where(w => !identifierList.Contains(w.Identifier)).ToList();
        if (deviceVariableList.Any())
        {
            device.DeviceVariable.AddRange(deviceVariableList);
            // 更新配置参数
            try
            {
                await _db.CopyNew().Storageable(device.DeviceVariable).ExecuteCommandAsync();
            }
            catch (Exception e)
            {
                Log.Error($"【更新设备属性】 Error:【{e.Message}】");
            }
        }
    }

    /// <summary>
    ///     同步设备配置信息
    /// </summary>
    /// <param name="driverInfo">驱动信息</param>
    /// <param name="device">设备信息</param>
    /// <param name="deviceObj">驱动接口</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    private async Task SyncDeviceConfig(DriverInfo driverInfo, Device device, IDriver deviceObj)
    {
        // 同步最新配置
        _driverService.CreateDeviceConfig(device, driverInfo, deviceObj);
        // 检查设备配置是否同步
        foreach (var property in driverInfo.Type.GetProperties())
        {
            var config = property.GetCustomAttribute<ConfigParameterAttribute>();
            // 该配置是否忽略
            if (config == null || (!config.Display && config.DisplayExpress.IsNullOrEmpty())) continue;
            // 是否设置该配置
            var deviceConfig = device.DeviceConfigs.FirstOrDefault(x => x.DeviceConfigName == property.Name);
            if (deviceConfig == null)
                continue;
            try
            {
                var value = await PropertyParse(property, deviceConfig.Value);
                property.SetValue(deviceObj, value);
            }
            catch (Exception ex)
            {
                var errorMsg = $"【初始化】,标识:{deviceConfig.DeviceConfigName},Desc:{deviceConfig.Description},value:{deviceConfig.Value},Error:{ex.Message}";
                throw Oops.Oh(errorMsg);
            }
        }

        // 更新配置参数
        try
        {
            await _db.CopyNew().Storageable(device.DeviceConfigs).ExecuteCommandAsync();
        }
        catch (Exception e)
        {
            Log.Error($"【更新设备配置】 Error:【{e.Message}】");
        }
    }

    /// <summary>
    ///     根据属性类型转换成对应的值
    /// </summary>
    /// <param name="property"></param>
    /// <param name="configValue"></param>
    /// <returns></returns>
    private Task<object> PropertyParse(PropertyInfo property, string configValue)
    {
        object value = configValue;
        if (property.PropertyType == typeof(bool))
            value = configValue.ToLower() != "false" && configValue != "0";
        else if (property.PropertyType == typeof(byte))
            value = byte.Parse(configValue);
        else if (property.PropertyType == typeof(sbyte))
            value = sbyte.Parse(configValue);
        else if (property.PropertyType == typeof(short))
            value = short.Parse(configValue);
        else if (property.PropertyType == typeof(ushort))
            value = ushort.Parse(configValue);
        else if (property.PropertyType == typeof(int))
            value = int.Parse(configValue);
        else if (property.PropertyType == typeof(uint))
            value = uint.Parse(configValue);
        else if (property.PropertyType == typeof(long))
            value = long.Parse(configValue);
        else if (property.PropertyType == typeof(ulong))
            value = ulong.Parse(configValue);
        else if (property.PropertyType == typeof(float))
            value = float.Parse(configValue);
        else if (property.PropertyType == typeof(double))
            value = double.Parse(configValue);
        else if (property.PropertyType == typeof(decimal))
            value = decimal.Parse(configValue);
        else if (property.PropertyType == typeof(Guid))
            value = Guid.Parse(configValue);
        else if (property.PropertyType == typeof(DateTime))
            value = DateTime.Parse(configValue);
        else if (property.PropertyType == typeof(string))
            value = configValue;
        else if (property.PropertyType == typeof(IPAddress))
            value = IPAddress.Parse(configValue);
        else if (property.PropertyType.BaseType == typeof(Enum))
            value = Enum.Parse(property.PropertyType, configValue);

        return Task.FromResult(value);
    }

    /// <summary>
    ///     增加系统标准属性
    /// </summary>
    /// <param name="device"></param>
    /// <returns></returns>
    public Task<List<DeviceVariable>> CreateSystemVariables(Device device)
    {
        var deviceVariableList = new List<DeviceVariable>
        {
            new()
            {
                Id = YitIdHelper.NextId(),
                SendType = SendTypeEnum.Always,
                Description = "true:在线;false:关机",
                Name = "在线状态",
                Enable = true,
                Identifier = "online",
                Tags = new List<string> {"系统属性"},
                DeviceId = device.Id,
                ValueSource = ValueSourceEnum.Calculate,
                Script = "return device.Status('${this.DeviceName}');",
                TransitionType = TransPondDataTypeEnum.Bool,
                IsSystem = true,
                Order = 1,
                Custom = new Dictionary<string, object> {{"True", "在线"}, {"False", "离线"}}.ToJson()
            }
        };

        return Task.FromResult(deviceVariableList);
    }

    /// <summary>
    ///     删除设备事件
    /// </summary>
    /// <param name="deviceEvent"></param>
    public Task RemoveDeviceEvent(DeviceEvent deviceEvent)
    {
        if (!DeviceEventThreads.ContainsKey(deviceEvent.Id)) return Task.CompletedTask;
        DeviceEventThreads[deviceEvent.Id].Dispose();
        DeviceEventThreads.Remove(deviceEvent.Id);
        if (!DeviceThreads.TryGetValue(deviceEvent.DeviceId, out var thread)) return Task.CompletedTask;
        // 移除旧的事件
        var oldEvent = thread.Device.DeviceEvent.FirstOrDefault(f => f.Id == deviceEvent.Id);
        if (oldEvent != null)
        {
            Console.WriteLine($"remove :{thread.Device.DeviceEvent.Count}");
            thread.Device.DeviceEvent.Remove(oldEvent);
            Console.WriteLine($"remove 后:{thread.Device.DeviceEvent.Count}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    ///     创建设备事件
    /// </summary>
    /// <param name="deviceEvent"></param>
    public async Task CreateDeviceEvent(DeviceEvent deviceEvent)
    {
        if (DeviceEventThreads.ContainsKey(deviceEvent.Id))
            await RemoveDeviceEvent(deviceEvent);
        var deviceEventThread = new DeviceEventThread(deviceEvent, Services, deviceEvent.Device);
        DeviceEventThreads.TryAdd(deviceEvent.Id, deviceEventThread);
        if (DeviceThreads.TryGetValue(deviceEvent.DeviceId, out var thread))
            thread.Device.DeviceEvent.Add(deviceEvent);
    }

    /// <summary>
    ///     设备采集信息
    /// </summary>
    /// <returns></returns>
    public async Task DeviceStatus()
    {
        foreach (var (_, deviceThread) in DeviceThreads)
        {
            deviceThread.DeviceStatus.DeviceId = deviceThread.Device.Id;
            await _socket.Send(JSON.Serialize(deviceThread.DeviceStatus), ConstMethod.DeviceStatus);
        }
    }

    /// <summary>
    ///     设备连接信息
    /// </summary>
    /// <returns></returns>
    public async Task DeviceConnectStatus()
    {
        foreach (var (_, deviceThread) in DeviceThreads)
        {
            deviceThread.DeviceStatus.DeviceId = deviceThread.Device.Id;
            await _socket.Send(JSON.Serialize(new
            {
                DeviceStatus = deviceThread.Driver.IsConnected,
                DeviceId = deviceThread.Device.Id
            }), ConstMethod.DeviceConnectStatus);
        }
    }

    /// <summary>
    ///     获取设备的连接状态
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="otherName"></param>
    /// <returns></returns>
    public bool DeviceConnectStatus(string deviceName, string otherName)
    {
        if (DeviceThreads == null)
            return false;
        var deviceThread = DeviceThreads.Values.FirstOrDefault(x => x.Device.DeviceName == deviceName && x.Device.OtherName == otherName);
        return deviceThread != null && deviceThread.Driver.IsConnected;
    }

    /// <summary>
    ///     获取设备的连接状态
    /// </summary>
    /// <param name="deviceName"></param>
    /// <returns></returns>
    public bool DeviceConnectStatus(string deviceName)
    {
        if (DeviceThreads == null)
            return false;
        var deviceThread = DeviceThreads.Values.FirstOrDefault(x => x.Device.DeviceName == deviceName);
        return deviceThread != null && deviceThread.Driver.IsConnected;
    }

    /// <summary>
    ///     获取设备的连接状态
    /// </summary>
    /// <param name="deviceId"></param>
    /// <returns></returns>
    public bool DeviceConnectStatus(long deviceId)
    {
        if (DeviceThreads == null)
            return false;
        var deviceThread = DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == deviceId);
        return deviceThread != null && deviceThread.Driver.IsConnected;
    }

    /// <summary>
    ///     获取采集中的设备属性
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="identifier"></param>
    /// <returns></returns>
    public Task<DriverReturnValueModel> GetVariable(string deviceName, string identifier)
    {
        if (DeviceThreads == null)
            return null;
        var deviceThread = DeviceThreads.Values.FirstOrDefault(x => x.Device.DeviceName == deviceName);
        return deviceThread == null ? null : Task.FromResult(deviceThread.DeviceVariables[identifier]);
    }

    /// <summary>
    ///     实时改变采集中设备的属性配置
    /// </summary>
    /// <param name="deviceId"></param>
    /// <param name="deviceVariable"></param>
    /// <returns></returns>
    public async Task SetVariable(long deviceId, DeviceVariable deviceVariable)
    {
        if (!deviceVariable.Enable)
            return;
        var deviceThread = DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == deviceId);
        if (deviceThread == null)
            return;
        //设备标识存在,直接修改值
        if (deviceThread.DeviceVariableSource.ContainsKey(deviceVariable.Identifier))
            deviceThread.DeviceVariableSource[deviceVariable.Identifier] = deviceVariable;
        else
            deviceThread.DeviceVariableSource.TryAdd(deviceVariable.Identifier, deviceVariable);
        deviceThread.Restart = true;

        //索引当前实体在对象中所在位置
        var index = deviceThread.Device.DeviceVariable.FindIndex(x => x.Id == deviceVariable.Id);
        if (index != -1)
            deviceThread.Device.DeviceVariable[index] = deviceVariable;
        else
            deviceThread.Device.DeviceVariable.Add(deviceVariable);

        //标签属性修改后需要再次同步
        if (deviceVariable.Tags.Any())
            deviceThread.Engine.Device.LoadTag(deviceThread.Device);

        //发送消息之前再次同步设备属性
        await MetaPush();
    }

    /// <summary>
    ///     设备发布
    /// </summary>
    /// <param name="device"></param>
    /// <returns></returns>
    public void SetVariable(Device device)
    {
        var deviceThread = DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == device.Id);
        if (deviceThread == null)
            return;
        deviceThread.Device.Release = device.Release;
        foreach (var deviceVariable in device.DeviceVariable.Where(w => w.Enable))
        {
            //索引当前实体在对象中所在位置
            var index = deviceThread.Device.DeviceVariable.FindIndex(x => x.Id == deviceVariable.Id);
            if (index != -1)
                deviceThread.Device.DeviceVariable[index] = deviceVariable;
            else
                deviceThread.Device.DeviceVariable.Add(deviceVariable);
            deviceThread.DeviceVariableSource[deviceVariable.Identifier] = deviceVariable;
        }

        deviceThread.Restart = true;
        // 标签属性修改后需要再次同步
        if (device.DeviceVariable.Any(a => a.Tags.Any()))
            deviceThread.Engine.Device.LoadTag(deviceThread.Device);
    }

    /// <summary>
    ///     实时改变采集中设备的属性配置
    /// </summary>
    /// <param name="deviceId"></param>
    /// <param name="deviceVariableList"></param>
    /// <returns></returns>
    public async Task SetVariable(long deviceId, List<DeviceVariable> deviceVariableList)
    {
        var deviceThread = DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == deviceId);
        if (deviceThread == null)
            return;

        foreach (var deviceVariable in deviceVariableList.Where(w => w.Enable))
        {
            //索引当前实体在对象中所在位置
            var index = deviceThread.Device.DeviceVariable.FindIndex(x => x.Id == deviceVariable.Id);
            if (index != -1)
                deviceThread.Device.DeviceVariable[index] = deviceVariable;
            else
                deviceThread.Device.DeviceVariable.Add(deviceVariable);
            deviceThread.DeviceVariableSource[deviceVariable.Identifier] = deviceVariable;
        }

        deviceThread.Restart = true;

        //标签属性修改后需要再次同步
        if (deviceVariableList.Any(a => a.Tags.Any()))
            deviceThread.Engine.Device.LoadTag(deviceThread.Device);

        //发送消息之前再次同步设备属性
        await MetaPush();
    }

    /// <summary>
    ///     实时移除采集中设备的属性配置
    /// </summary>
    /// <param name="deviceId"></param>
    /// <param name="deviceVariableList"></param>
    /// <returns></returns>
    public async Task RemoveVariable(long deviceId, List<DeviceVariable> deviceVariableList)
    {
        // 获取设备线程
        var deviceThread = DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == deviceId);
        if (deviceThread == null)
            return;
        // 删除设备变量
        foreach (var deviceVariable in deviceVariableList.Where(deviceVariable => deviceThread.DeviceVariableSource.ContainsKey(deviceVariable.Identifier)))
        {
            //索引当前实体在对象中所在位置
            var index = deviceThread.Device.DeviceVariable.FindIndex(x => x.Id == deviceVariable.Id);
            if (index != -1)
                deviceThread.Device.DeviceVariable.Remove(deviceThread.Device.DeviceVariable[index]);

            deviceThread.DeviceVariableSource.Remove(deviceVariable.Identifier, out _);
        }
        // 删除设备变量
        foreach (var deviceVariable in deviceVariableList.Where(deviceVariable => deviceThread.DeviceVariables.ContainsKey(deviceVariable.Identifier)))
            deviceThread.DeviceVariables.Remove(deviceVariable.Identifier, out _);

        deviceThread.Restart = true;

        // 标签属性移除后需要再次同步
        if (deviceVariableList.Any(a => a.Tags.Any()))
            deviceThread.Engine.Device.LoadTag(deviceThread.Device);

        Console.WriteLine($"删除元数据:【{deviceThread.Device.DeviceName}】");
        Console.WriteLine($"当前版本:【{JSON.Serialize(MachineUtil.ServerVersion)}】");
        // 如果当前版本大于等于1.1.0，则删除元数据
        if (MachineUtil.ServerVersion != null && MachineUtil.ServerVersion.Minor >= 1)
        {
            Console.WriteLine($"删除元数据:【{deviceThread.Device.DeviceName}】-v1.1.0");
            // 发送消息之前再次同步设备属性
            await MetaDelete(deviceThread.Device.DeviceName, deviceVariableList);
        }
        else
        {
            Console.WriteLine($"删除元数据:【{deviceThread.Device.DeviceName}】-v1.0.0");
            // 发送消息之前再次同步设备属性
            await MetaPush();
        }
    }

    /// <summary>
    ///     实时移除采集中设备的属性配置
    /// </summary>
    /// <param name="deviceId"></param>
    /// <param name="deviceVariable"></param>
    /// <returns></returns>
    public async Task RemoveVariable(long deviceId, DeviceVariable deviceVariable)
    {
        var deviceThread = DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == deviceId);
        if (deviceThread == null)
            return;
        if (deviceThread.DeviceVariableSource.ContainsKey(deviceVariable.Identifier))
            deviceThread.DeviceVariableSource.Remove(deviceVariable.Identifier, out _);
        deviceThread.Restart = true;

        //标签属性移除后需要再次同步
        if (deviceVariable.Tags.Any())
            deviceThread.Engine.Device.LoadTag(deviceThread.Device);
        //发送消息之前再次同步设备属性
        await MetaPush();
    }

    /// <summary>
    /// </summary>
    /// <param name="devices"></param>
    public async Task CreateDeviceThreads(List<Device> devices)
    {
        foreach (var device in devices)
            await CreateDeviceThread(device.Id);
    }

    /// <summary>
    ///     删除采集线程
    /// </summary>
    /// <param name="device"></param>
    public async Task RemoveDeviceThread(Device device)
    {
        var deviceThread = DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == device.Id);
        if (deviceThread == null)
            return;
        await deviceThread.StopThread();
        deviceThread.Dispose();
        DeviceThreads.Remove(device.Id, out _);
    }

    /// <summary>
    ///     删除采集线程
    /// </summary>
    /// <param name="deviceId"></param>
    public async Task RemoveDeviceThread(long deviceId)
    {
        var deviceThread = DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == deviceId);
        if (deviceThread == null)
            return;
        await deviceThread.StopThread();
        deviceThread.Dispose();
        DeviceThreads.Remove(deviceId, out _);
    }

    /// <summary>
    /// </summary>
    /// <param name="devices"></param>
    public async Task RemoveDeviceThreads(List<Device> devices)
    {
        foreach (var device in devices)
            await RemoveDeviceThread(device);
    }

    /// <summary>
    ///     停止全部的采集线程
    /// </summary>
    public async Task RemoveDeviceThreadAll()
    {
        foreach (var (_, deviceThread) in DeviceThreads)
        {
            await deviceThread.StopThread();
            deviceThread.Dispose();
        }

        DeviceThreads.Clear();
        DeviceThreads = new ConcurrentDictionary<long, DeviceThread>();
    }

    /// <summary>
    ///     同步元数据
    /// </summary>
    private async Task MetaPush(List<Device> deviceList = null)
    {
        deviceList ??= await _db.Queryable<Device>()
            .Where(w => w.Enable == true)
            .Includes(c => c.DeviceVariable.Where(w => w.Enable).ToList())
            .ToListAsync();
        // 发送消息再次同步设备属性
        var mqttClient = App.GetService<MasterClient>();
        _ = mqttClient.PublishMeta(deviceList);
    }

    /// <summary>
    /// 删除元数据
    /// </summary>
    /// <param name="deviceName">设备名称</param>
    /// <param name="deviceVariableList">设备变量列表</param>
    /// <returns></returns>
    private async Task MetaDelete(string deviceName, List<DeviceVariable> deviceVariableList)
    {
        var mqttClient = App.GetService<MasterClient>();
        _ = mqttClient.DeleteMeta(deviceName, deviceVariableList);
    }

    /// <summary>
    /// </summary>
    public void Dispose()
    {
        foreach (var (_, deviceThread) in DeviceThreads)
            deviceThread.Dispose();
    }

    #endregion
}