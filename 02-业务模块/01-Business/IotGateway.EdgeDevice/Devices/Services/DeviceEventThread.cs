using Common.Extension;
using DateTime = System.DateTime;

namespace IotGateway.EdgeDevice;

/// <summary>
///     设备事件
/// </summary>
public class DeviceEventThread : IDisposable
{
    /// <summary>
    ///     设备事件配置
    /// </summary>
    private readonly DeviceEvent _deviceEvent;

    /// <summary>
    ///     Token
    /// </summary>
    private readonly CancellationTokenSource _cancellationTokenSource = new();

    /// <summary>
    ///     定时器，定时触发不为空
    /// </summary>
    private TimerX _timer;

    /// <summary>
    ///     表达式执行器
    /// </summary>
    private readonly Interpreter _interpreter;

    /// <summary>
    ///     脚本
    ///     开放该字段，用于外部调用脚本
    /// </summary>
    private readonly EngineSingletonService _engine;

    /// <summary>
    /// </summary>
    private IServiceProvider Services { get; }

    /// <summary>
    ///     采集设备配置
    /// </summary>
    private readonly Device _device;

    public DeviceEventThread(DeviceEvent deviceEvent, IServiceProvider services, Device device)
    {
        _deviceEvent = deviceEvent;
        Services = services;
        _device = device;
        _engine = services.GetService<EngineSingletonService>();
        _interpreter = new Interpreter();
        // 设置上次执行结果默认值
        _interpreter.SetVariable(IsSuccessKey, false);
        Console.WriteLine($"设备：{_device.DeviceName},事件:{_deviceEvent.EventName} 创建成功！");
        _ = Subscribe();
    }

    /// <summary>
    /// </summary>
    private Task Subscribe()
    {
        var cancellationToken = _cancellationTokenSource.Token;
        if (_deviceEvent.TriggerEventType == TriggerEventTypeEnum.定时触发)
        {
            var eventTimedConfig = _deviceEvent.CustomEventConfig.EventTimedConfig;
            switch (eventTimedConfig.ExecutionType)
            {
                case ExecutionTypeEnum.周期执行:
                {
                    var period = eventTimedConfig.ExecutionTime.PeriodUnit == ExecutionStrategyByTimePeriodUnitEnum.秒 ? eventTimedConfig.ExecutionTime.Period * 1000
                        : eventTimedConfig.ExecutionTime.PeriodUnit == ExecutionStrategyByTimePeriodUnitEnum.分钟 ? eventTimedConfig.ExecutionTime.Period * 1000 * 60
                        : eventTimedConfig.ExecutionTime.Period * 1000 * 60 * 60;
                    _timer = new TimerX(ActionScript, _deviceEvent, 0, period, "DeviceEvent") {Async = true};
                    break;
                }
                case ExecutionTypeEnum.Cron:
                {
                    _timer = new TimerX(ActionScript, _deviceEvent, eventTimedConfig.ExecutionCron.Cron, "DeviceEvent") {Async = true};
                    break;
                }
            }
        }

        return MessageCenter.Subscribe(string.Format(ConstMethod.DeviceThread, _deviceEvent.Id), async context =>
        {
            if (cancellationToken.IsCancellationRequested)
                return;
            var payLoad = context.GetPayload<PayLoad>();
            // 设置变量
            foreach (var (identifier, variable) in payLoad.Values)
                try
                {
                    var cookieValue = variable.CookieValue?.GetJsonElementValue().ToString();
                    if (variable.ValueSource == ValueSourceEnum.Get)
                        switch (variable.DataType)
                        {
                            case DataTypeEnum.Bit:
                                _interpreter.SetVariable(identifier, new
                                {
                                    Value = Convert.ToInt32(variable.Value),
                                    CookieValue = Convert.ToInt32(cookieValue),
                                    ReadTime = Convert.ToInt64(variable.ReadTime),
                                    CookieTime = Convert.ToInt64(variable.CookieTime)
                                });
                                break;
                            case DataTypeEnum.Bool:
                                _interpreter.SetVariable(identifier, new
                                {
                                    Value = Convert.ToBoolean(variable.Value),
                                    CookieValue = cookieValue != null && !string.IsNullOrEmpty(cookieValue.ToString()) && Convert.ToBoolean(cookieValue),
                                    ReadTime = Convert.ToInt64(variable.ReadTime),
                                    CookieTime = Convert.ToInt64(variable.CookieTime)
                                });
                                break;
                            case DataTypeEnum.Uint16:
                            case DataTypeEnum.Int16:
                            case DataTypeEnum.Uint32:
                            case DataTypeEnum.Int32:
                            case DataTypeEnum.Float:
                            case DataTypeEnum.Uint64:
                            case DataTypeEnum.Int64:
                            case DataTypeEnum.Double:
                                if (double.IsNaN(Convert.ToDouble(variable.Value)))
                                    variable.Value = "0";
                                _interpreter.SetVariable(identifier, new
                                {
                                    Value = Convert.ToDouble(variable.Value),
                                    CookieValue = string.IsNullOrEmpty(cookieValue) ? 0 : Convert.ToDouble(cookieValue),
                                    ReadTime = Convert.ToInt64(variable.ReadTime),
                                    CookieTime = Convert.ToInt64(variable.CookieTime)
                                });
                                break;
                            case DataTypeEnum.String:
                            case DataTypeEnum.Bcd:
                            case DataTypeEnum.Byte:
                            default:
                                _interpreter.SetVariable(identifier, new
                                {
                                    variable.Value,
                                    CookieValue = cookieValue,
                                    ReadTime = Convert.ToInt64(variable.ReadTime),
                                    CookieTime = Convert.ToInt64(variable.CookieTime)
                                });
                                break;
                        }
                    else
                        switch (variable.TransitionType)
                        {
                            case TransPondDataTypeEnum.Bool:
                                _interpreter.SetVariable(identifier, new
                                {
                                    Value = Convert.ToBoolean(variable.Value),
                                    CookieValue = cookieValue != null && !string.IsNullOrEmpty(cookieValue) && Convert.ToBoolean(cookieValue),
                                    ReadTime = Convert.ToInt64(variable.ReadTime),
                                    CookieTime = Convert.ToInt64(variable.CookieTime)
                                });
                                break;
                            case TransPondDataTypeEnum.Int:
                            case TransPondDataTypeEnum.Double:
                                if (double.IsNaN(Convert.ToDouble(variable.Value)))
                                    variable.Value = "0";
                                _interpreter.SetVariable(identifier, new
                                {
                                    Value = Convert.ToDouble(variable.Value),
                                    CookieValue = string.IsNullOrEmpty(cookieValue) ? 0 : Convert.ToDouble(cookieValue),
                                    ReadTime = Convert.ToInt64(variable.ReadTime),
                                    CookieTime = Convert.ToInt64(variable.CookieTime)
                                });
                                break;
                            case TransPondDataTypeEnum.String:
                            case TransPondDataTypeEnum.Bytes:
                            default:
                                _interpreter.SetVariable(identifier, new
                                {
                                    variable.Value,
                                    CookieValue = cookieValue?.ToString(),
                                    ReadTime = Convert.ToInt64(variable.ReadTime),
                                    CookieTime = Convert.ToInt64(variable.CookieTime)
                                });
                                break;
                        }
                }
                catch (Exception e)
                {
                    Log.Error($"【设备事件】 设置变量：{identifier} Error:{e.Message}");
                    _interpreter.UnsetIdentifier(identifier);
                }

            if (_deviceEvent.TriggerEventType == TriggerEventTypeEnum.属性触发)
                ActionScript(null);
        }, cancellationToken: cancellationToken);
    }

    /// <summary>
    ///     记录防抖时间
    /// </summary>
    private readonly Dictionary<string, DateTime> _antiShake = new();

    /// <summary>
    ///     用于保存上一次动作执行结果的标记key
    /// </summary>
    private const string IsSuccessKey = "lastRunResult";

    /// <summary>
    ///     条件表达式
    /// </summary>
    /// <returns></returns>
    private CustomEventWhere CustomEventWhere()
    {
        try
        {
            foreach (var customEventWhere in _deviceEvent.CustomEventWhereList.OrderBy(o => o.Order))
            {
                try
                {
                    if (Convert.ToBoolean(_interpreter.Eval(customEventWhere.Expressions)))
                        return customEventWhere;
                }
                catch (Exception e)
                {
                    Log.Error($"【设备事件】 设备:{_device.DeviceName},事件:{_deviceEvent.EventName} 第{customEventWhere.Order}个条件表达式解析错误：{e.Message},表达式：[{customEventWhere.Expressions}]");
                }

                // 全部条件不符合，返回最后一组动作
                if (_deviceEvent.CustomEventWhereList.Any(a => a.Order > customEventWhere.Order) || !string.IsNullOrEmpty(customEventWhere.Expressions)) continue;
                return customEventWhere;
            }
        }
        catch (Exception e)
        {
            Log.Error($"【设备事件】 设备:{_device.DeviceName},事件:{_deviceEvent.EventName} Error:{e.Message}");
        }

        return null;
    }

    /// <summary>
    ///     执行设备事件动作
    /// </summary>
    /// <param name="state"></param>
    /// <exception cref="AppFriendlyException"></exception>
    private void ActionScript(object state)
    {
        // 当前事件执行耗时
        var nodeActionTimer = Stopwatch.StartNew();
        // 统计每个动作执行时间
        var nodeDataTimer = Stopwatch.StartNew();
        // 满足条件
        var customEventWhere = CustomEventWhere();
        if (customEventWhere == null)
            return;
        // 防抖
        if (customEventWhere.AntiShake)
        {
            if (_antiShake.ContainsKey(customEventWhere.Guid))
            {
                // 当前时间-执行时间 <= 设置时间
                if ((DateTime.Now - _antiShake[customEventWhere.Guid]).TotalSeconds <= customEventWhere.AntiShakeTime)
                    // Console.WriteLine($"【设备事件】 事件:{_deviceEvent.EventName} 抖动时间内，持续:{(DateTime.Now - _antiShake[customEventWhere.Guid]).TotalSeconds}秒,设置：{customEventWhere.AntiShakeTime}秒");
                    return;
                // 记录最新执行时间
                _antiShake[customEventWhere.Guid] = DateTime.Now;
            }
            else
            {
                _antiShake.Add(customEventWhere.Guid, DateTime.Now);
            }
        }

        var execResult = $"表达式：\n{customEventWhere.Expressions}\n执行动作：\n";
        var eventType = "info";
        // 根据排序执行脚本
        foreach (var customEventAction in customEventWhere.CustomEventActionList.OrderBy(w => w.Order))
            try
            {
                // 重置并且重新开始计时
                nodeDataTimer.Reset();
                nodeDataTimer.Start();

                // 校验动作执行条件是否满足
                if (!string.IsNullOrEmpty(customEventAction.Expressions))
                {
                    try
                    {
                        var eval = _interpreter.Eval(customEventAction.Expressions);
                        if (!Convert.ToBoolean(eval))
                        {
                            execResult += $"\t动作序号：{customEventAction.Order} 条件不成立：{customEventAction.Expressions} = {eval}，耗时：{nodeDataTimer.ElapsedMilliseconds}ms ";
                            continue;
                        }
                    }
                    catch (Exception e)
                    {
                        execResult += $"\t动作序号：{customEventAction.Order} 条件不成立：{customEventAction.Expressions} Error = {e.Message}，耗时：{nodeDataTimer.ElapsedMilliseconds}ms";
                        _interpreter.SetVariable(IsSuccessKey, false);
                        continue;
                    }
                }

                // 执行结果
                var success = false;
                // 执行动作返回结果
                var result = "";
                switch (customEventAction.EventActionType)
                {
                    case EventActionTypeEnum.属性下写:
                        if (customEventAction.DeviceVariableWrite == null)
                            throw Oops.Oh("脚本配置是空！");
                        // 采集线程
                        var deviceThread = Services.GetService<DeviceHostedService>().DeviceThreads.Values.FirstOrDefault(x => x.Device.DeviceName == _device.DeviceName);
                        if (deviceThread == null)
                        {
                            result = $"【事件动作】 写入失败，设备:{_device.DeviceName}未开启采集！";
                            eventType = "warning";
                        }
                        else
                        {
                            // 属性下写
                            result = JSON.Serialize(deviceThread.DeviceWrite(new DeviceWriteRequest
                            {
                                DeviceName = _device.DeviceName,
                                Params = customEventAction.DeviceVariableWrite.ToDictionary(k => k.Identifier, v => v.Value?.ToString())
                            }).Result);
                            success = true;
                        }

                        break;
                    case EventActionTypeEnum.消息通知:
                        break;
                    case EventActionTypeEnum.执行脚本:
                        if (customEventAction.ScriptConfig == null)
                            throw Oops.Oh("脚本配置是空！");
                        var content = customEventAction.ScriptConfig.Content;
                        if (content.Contains("${this.DeviceName}"))
                            content = content.Replace("${this.DeviceName}", _device.DeviceName);
                        foreach (var (key, param) in customEventAction.ScriptConfig.Params)
                            try
                            {
                                switch (param.DataType)
                                {
                                    case TransPondDataTypeEnum.Bool:
                                        _engine.Engine.SetValue(key, Convert.ToBoolean(param.Value));
                                        break;
                                    case TransPondDataTypeEnum.Int:
                                        _engine.Engine.SetValue(key, Convert.ToInt64(param.Value));
                                        break;
                                    case TransPondDataTypeEnum.Double:
                                        _engine.Engine.SetValue(key, Convert.ToDouble(param.Value));
                                        break;
                                    case TransPondDataTypeEnum.String:
                                    case TransPondDataTypeEnum.Bytes:
                                    default:
                                        _engine.Engine.SetValue(key, param.Value);
                                        break;
                                }
                            }
                            catch (Exception e)
                            {
                                Log.Error($"【事件动作】 设备:{_device.DeviceName},事件:{_deviceEvent.EventName},第{customEventAction.Order}个动作，设置变量失败:{e.Message}");
                            }

                        // 执行脚本
                        result = _engine.Engine.Evaluate(content, new ScriptParsingOptions {Tolerant = true, AllowReturnOutsideFunction = true}).ToObject()?.ToString();
                        success = true;
                        break;
                    case EventActionTypeEnum.延迟执行:
                    {
                        var sleepTime = customEventAction.SleepConfig!.Unit switch
                        {
                            ExecutionStrategyByTimePeriodUnitEnum.秒 => customEventAction.SleepConfig.Sleep * 1000,
                            ExecutionStrategyByTimePeriodUnitEnum.分钟 => customEventAction.SleepConfig.Sleep * 1000 * 60,
                            _ => customEventAction.SleepConfig.Sleep * 1000 * 60 * 60
                        };
                        Thread.Sleep((int) sleepTime);
                        result = "休眠完成";
                        success = true;
                    }
                        break;
                    case EventActionTypeEnum.设备重连:
                        _ = MessageCenter.PublishAsync(EventConst.UpdateDeviceThread, _device);
                        result = "设备主动发起重连";
                        success = true;
                        break;
                    default:
                        execResult += $"\t动作序号：{customEventAction.Order} Error = 暂不支持动作，耗时：{nodeDataTimer.ElapsedMilliseconds}ms";
                        Log.Error($"暂不支持事件动作:【{customEventAction.Order}】,事件:{_deviceEvent.EventName},设备:{_device.DeviceName}");
                        break;
                }

                // 记录本次执行结果
                _interpreter.SetVariable(IsSuccessKey, success);
                execResult += $"\t动作序号：{customEventAction.Order} result = {result}，耗时：{nodeDataTimer.ElapsedMilliseconds}ms";
            }
            catch (Exception e)
            {
                eventType = "warning";
                _interpreter.SetVariable(IsSuccessKey, false);
                execResult += $"\t动作序号：{customEventAction.Order} Error = {e.Message}，耗时：{nodeDataTimer.ElapsedMilliseconds}ms";
            }

        // 记录执行日志
        _ = MessageCenter.PublishAsync(EventConst.DeviceEvent, new DeviceEventLog
        {
            Id = YitIdHelper.NextId(),
            CreatedTime = Common.Extension.DateTime.Now(),
            EventType = eventType,
            DeviceId = _device.Id,
            Description = execResult,
            Title = _deviceEvent.EventName + $"({nodeActionTimer.ElapsedMilliseconds}ms)",
            DeviceEventId = _deviceEvent.Id
        });
    }

    public void Dispose()
    {
        MessageCenter.Unsubscribe(string.Format(ConstMethod.DeviceThread, _deviceEvent.Id));
        Console.WriteLine($"设备：{_device.DeviceName},事件:{_deviceEvent.EventName} 停止！");
        _timer?.Dispose();
    }
}