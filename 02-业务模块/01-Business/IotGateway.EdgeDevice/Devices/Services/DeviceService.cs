using Common.Extension;
using DateTime = System.DateTime;

namespace IotGateway.EdgeDevice;

/// <summary>
///     设备管理
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2022-10-01
/// </summary>
[ApiDescriptionSettings("设备管理")]
public class DeviceService : ITransient, IDynamicApiController
{
    private readonly ISqlSugarClient _db;
    private readonly SqlSugarRepository<Device> _device;
    private readonly DriverHostedService _driverService;
    private readonly IEventPublisher _eventPublisher;
    private readonly DeviceHostedService _deviceHostedService;
    private readonly SendMessageService _socket;
    private readonly ExecuteService _executeService;
    private readonly SysConfigService _sysConfigService;

    /// <summary>
    ///     忽略的方法
    /// </summary>
    private readonly List<string> _ignoreMethods = new() { "Parse", "BatchRead", "Init", "Debug", "FileWrite", "FileRead", "FileRouteInfo", "WriteAsync", "Clear", "FileDel", "SetCurrentProgram" };

    /// <summary>
    /// </summary>
    /// <param name="device"></param>
    /// <param name="driverService">驱动</param>
    /// <param name="db">数据库作用域</param>
    /// <param name="eventPublisher"></param>
    /// <param name="deviceHostedService">初始化内容</param>
    /// <param name="socket">socket推送</param>
    /// <param name="executeService"></param>
    /// <param name="sysConfigService"></param>
    public DeviceService(SqlSugarRepository<Device> device, DriverHostedService driverService, ISqlSugarClient db, IEventPublisher eventPublisher,
        DeviceHostedService deviceHostedService, SendMessageService socket, ExecuteService executeService, SysConfigService sysConfigService)
    {
        _device = device;
        _driverService = driverService;
        _db = db;
        _eventPublisher = eventPublisher;
        _deviceHostedService = deviceHostedService;
        _socket = socket;
        _executeService = executeService;
        _sysConfigService = sysConfigService;
    }

    #region Get

    /// <summary>
    ///     全部设备-根据协议分树
    /// </summary>
    /// <returns></returns>
    [HttpGet("/device/tree")]
    [Obsolete]
    public async Task<dynamic> Tree()
    {
        var devices = await _device.AsQueryable()
            // .Where(u => u.Enable == true)
            .OrderBy(u => u.Index)
            .Includes(w => w.Driver)
            .ToListAsync();

        var deviceGroupList = devices.GroupBy(w => w.DriverName).ToList();
        var output = new List<dynamic>();
        foreach (var deviceGroup in deviceGroupList)
            output.Add(new
            {
                deviceGroup.FirstOrDefault()?.DriverId,
                DriverName = deviceGroup.Key,
                Device = deviceGroup.Select(s => new
                {
                    s.DeviceName,
                    s.Id,
                    s.DriverId,
                    s.OtherName,
                    IsConnect = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == s.Id)?.Driver?.IsConnected ?? false
                })
            });

        return output;
    }

    /// <summary>
    ///     设备统计在线,离线,停用..数量
    /// </summary>
    /// <returns></returns>
    [HttpGet("/device/deviceStatus")]
    public async Task<dynamic> DeviceStatus()
    {
        // 全部在线设备
        var deviceThreads = _deviceHostedService.DeviceThreads.Values.Where(w => w.Driver.IsConnected).ToList();
        var devices = await _device.AsQueryable().ToListAsync();
        var all = devices.Count;
        var onLine = deviceThreads.Count(w => w.Driver.IsConnected);
        var close = devices.Count(w => w.Enable == false);
        return new
        {
            All = all,
            OnLine = onLine,
            Close = close,
            OffLine = all - close - onLine
        };
    }

    /// <summary>
    ///     单设备导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/device/export")]
    public async Task<dynamic> Export([FromQuery] BaseId input)
    {
        var device = await _device.AsQueryable().Includes(x => x.Driver)
            .Includes(x => x.DeviceConfigs)
            .Includes(x => x.DeviceVariable)
            .Includes(x => x.DeviceEvent)
            .FirstAsync(f => f.Id == input.Id);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);

        return new List<dynamic>
        {
            new
            {
                device.DeviceName,
                device.DriverName,
                device.OtherName,
                DeviceVariable = device.DeviceVariable.Select(s => new
                {
                    s.Identifier,
                    s.Name,
                    s.DefaultValue,
                    s.Length,
                    ValueSourceType = EnumUtil.GetEnumDesc(s.ValueSource),
                    ConvertDataType = EnumUtil.GetEnumDesc(s.TransitionType),
                    s.Expressions,
                    s.Script,
                    s.Description,
                    s.DeviceVariableEx,
                    s.SendType
                }),
                DeviceConfig = device.DeviceConfigs.ToDictionary(x => x.DeviceConfigName, x => (object) x.Value)
            }
        };
    }

    /// <summary>
    ///     全部设备-下拉
    /// </summary>
    /// <returns></returns>
    [HttpGet("/device/select")]
    public async Task<dynamic> Select([FromQuery] DeviceSelectInput input)
    {
        var devices = await _device.AsQueryable()
            .Where(u => u.Enable == true)
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue), w => w.DeviceName.Contains(input.SearchValue)
                                                                    || w.Description.Contains(input.SearchValue)
                                                                    || w.OtherName.Contains(input.SearchValue))
            .OrderBy(u => u.Index)
            .ToListAsync();

        return devices.Select(s => new
        {
            s.Id,
            s.DeviceName,
            s.OtherName,
            s.DriverId,
            IsConnect = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == s.Id)?.Driver.IsConnected ?? false
        });
    }

    /// <summary>
    ///     设备协议集合
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceDriver/list")]
    public async Task<dynamic> DeviceDriverList()
    {
        var devices = await _device.AsQueryable()
            .OrderBy(u => u.Index)
            .Includes(w => w.Driver)
            .ToListAsync();

        var deviceGroupList = devices.GroupBy(w => w.DriverName).ToList();
        var output = new List<dynamic>();
        foreach (var deviceGroup in deviceGroupList)
            output.Add(new
            {
                deviceGroup.FirstOrDefault()?.DriverId,
                DriverName = deviceGroup.Key
            });

        return output;
    }

    /// <summary>
    ///     设备-分组修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/group/update")]
    public async Task DeviceGroupSet(DeviceGroupSetInput input)
    {
        var deviceList = await _device.AsQueryable().Where(w => input.DeviceId.Contains(w.Id)).ToListAsync();
        if (!deviceList.Any())
            return;
        foreach (var device in deviceList) device.DeviceGroupId = input.DeviceGroupId;
        await _device.AsSugarClient().Updateable(deviceList).UpdateColumns(w => w.DeviceGroupId).ExecuteCommandAsync();
    }

    /// <summary>
    ///     设备列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/device/page")]
    public async Task<SqlSugarPagedList<DevicePageOutput>> Page([FromQuery] DevicePage input)
    {
        List<long> filterId = new();
        // 全部在线设备
        if (input.Status > 0)
            filterId.AddRange(_deviceHostedService.DeviceThreads.Values.Where(w => w.Driver.IsConnected).Select(s => s.Device.Id));

        var devices = await _device.AsQueryable()
            .WhereIF(input.Enable > 0, w => input.Enable == 1 ? w.Enable : !w.Enable)
            .WhereIF(input.DeviceGroupId > 0, w => w.DeviceGroupId == input.DeviceGroupId)
            .WhereIF(input.Status == 1, w => filterId.Contains(w.Id) && w.Enable == true)
            .WhereIF(input.Status == 2, w => !filterId.Contains(w.Id) || w.Enable == false)
            .WhereIF(input.DriverId > 0, w => w.DriverId == input.DriverId)
            .WhereIF(input.DeviceId > 0, w => w.Id == input.DeviceId)
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue), w => w.DeviceName.Contains(input.SearchValue)
                                                                    || w.Description.Contains(input.SearchValue)
                                                                    || w.OtherName.Contains(input.SearchValue))
            .WhereIF(!string.IsNullOrEmpty(input.SearchBeginTime?.Trim()), u => u.CreatedTime >= DateTime.Parse(input.SearchBeginTime.Trim()) &&
                                                                                u.CreatedTime <= DateTime.Parse(input.SearchEndTime.Trim()))
            .Includes(w => w.Driver)
            .Includes(w => w.DeviceConfigs)
            .Includes(w => w.DeviceVariable)
            .OrderBy(w => w.Index)
            .ToPagedListAsync(input.PageNo, input.PageSize);

        var deviceOutputs = devices.Adapt<SqlSugarPagedList<DevicePageOutput>>();
        foreach (var row in deviceOutputs.Rows)
            try
            {
                var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == row.Id);
                if (deviceThread != null)
                {
                    // 异常情况没有停止,就再次停止
                    row.IsConnect = deviceThread.Driver.IsConnected;
                    if (row is { IsConnect: true, Enable: false })
                    {
                        await _eventPublisher.PublishAsync(EventConst.StopDeviceThread, row);
                        row.IsConnect = false;
                    }
                }
                else
                {
                    row.NotRunning = true;
                }

                var device = devices.Rows.FirstOrDefault(w => w.Id == row.Id);
                if (device != null)
                    row.VariableCount = device.DeviceVariable.Count;

                // 特殊类协议(注塑机)支持多种连接方式
                if (row.DeviceConfigs.Select(s => s.DeviceConfigName).Contains("IpAddress") && row.DeviceConfigs.Select(s => s.DeviceConfigName).Contains("SerialNumber"))
                {
                    var ipConfig = row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "IpAddress");
                    if (ipConfig.DisplayExpress != null)
                    {
                        // 将JSON字符串转换为字典
                        var dict = JSON.Deserialize<Dictionary<string, object>>(ipConfig.DisplayExpress);
                        foreach (var (key, value) in dict)
                        {
                            var getKeyConfig = row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == key);
                            if (getKeyConfig == null) continue;
                            var objValue = value.GetJsonElementValue();
                            if (objValue.GetType() != typeof(object[])) continue;
                            if (((object[])objValue).Contains(getKeyConfig.Value))
                            {
                                row.IpAddress = row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "IpAddress")!.Value + ":" +
                                                row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "Port")!.Value;
                            }
                            else
                            {
                                if (Enum.TryParse(row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "SerialNumber")?.Value, true, out SerialNumberEnum enumValue))
                                    row.IpAddress = EnumUtil.GetEnumDesc(enumValue);
                                if (Enum.TryParse(row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "BaudRate")?.Value, true, out BaudRateEnum baudRateEnumValue))
                                    row.IpAddress = row.IpAddress + ":" + EnumUtil.GetEnumDesc(baudRateEnumValue);
                            }
                        }
                    }
                    else
                    {
                        if (Enum.TryParse(row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "SerialNumber")?.Value, true, out SerialNumberEnum enumValue))
                            row.IpAddress = EnumUtil.GetEnumDesc(enumValue);
                        if (Enum.TryParse(row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "BaudRate")?.Value, true, out BaudRateEnum baudRateEnumValue))
                            row.IpAddress = row.IpAddress + ":" + EnumUtil.GetEnumDesc(baudRateEnumValue);
                    }
                }
                // 网口设备
                else if (row.DeviceConfigs.Select(s => s.DeviceConfigName).Contains("IpAddress"))
                {
                    row.IpAddress = row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "IpAddress")!.Value + ":" + row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "Port")!.Value;
                }
                // url 设备
                else if (row.DeviceConfigs.Select(s => s.DeviceConfigName.ToLower()).Contains("url"))
                {
                    row.IpAddress = row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName.ToLower() == "url")!.Value;
                }
                // 串口设备
                else if (row.DeviceConfigs.Select(s => s.DeviceConfigName).Contains("SerialNumber"))
                {
                    if (Enum.TryParse(row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "SerialNumber")?.Value, true, out SerialNumberEnum enumValue))
                        row.IpAddress = EnumUtil.GetEnumDesc(enumValue);
                    if (Enum.TryParse(row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "BaudRate")?.Value, true, out BaudRateEnum baudRateEnumValue))
                        row.IpAddress = row.IpAddress + ":" + EnumUtil.GetEnumDesc(baudRateEnumValue);
                }

                // 设备是否支持Dnc
                if (row.DeviceConfigs.Any(a => a.DeviceConfigName == "Dnc"))
                    try
                    {
                        row.Dnc = Convert.ToInt32(row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "Dnc")?.Value) == 1;
                    }
                    catch (Exception)
                    {
                        // 兼容性配置
                        row.Dnc = row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "Dnc")?.Value.ToLower() == "true";
                    }

                row.MinPeriod = row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "MinPeriod")?.Value + "ms";
            }
            catch (Exception e)
            {
                Log.Error($"【采集设备】 配置不兼容,Error:{e.Message}");
            }

        // row.ReConnTime = row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "ReConnTime")!.Value + "s";
        return deviceOutputs;
    }

    /// <summary>
    ///     设备方法
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/device/methods")]
    public async Task<List<DriverSelectListItem>> DeviceMethods([FromQuery] BaseId input)
    {
        var methods = new List<MethodInfo>();
        Device device;
        List<DriverSelectListItem> driverFilesComboSelect = new();
        var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == input.Id);
        if (deviceThread == null)
        {
            device = await _device.AsQueryable()
                .Includes(c => c.Driver)
                .Where(w => w.Id == input.Id || w.Driver.Id == input.Id)
                .Includes(c => c.DeviceConfigs)
                .FirstAsync();
            if (device == null)
                throw Oops.Oh("设备不存在或者被删除！");
            // 驱动
            var driverInfo = _driverService.DriverInfos.FirstOrDefault(x => x.Type.FullName == device.Driver!.AssembleName);
            if (driverInfo == null)
                throw Oops.Oh("非法驱动！");

            var types = new[] { typeof(DriverInfoDto) };
            var param = new object[] { new DriverInfoDto { Socket = _socket, DeviceId = device.Id, DeviceName = device.DeviceName, ConnectType = device.Driver!.ConnectType } };
            // 验证程序集是否正确
            var constructor = driverInfo.Type.GetConstructor(types);
            if (constructor == null)
                throw Oops.Oh("程序集Error！");
            if (constructor.Invoke(param) is IDriver deviceObj)
                methods = deviceObj.GetType().GetMethods().Where(x => x.GetCustomAttribute(typeof(MethodAttribute)) != null).ToList();
        }
        else
        {
            methods = deviceThread.Methods;
            device = deviceThread.Device;
        }

        foreach (var method in methods)
        {
            var attribute = method.CustomAttributes.Count() == 1
                ? method.CustomAttributes.FirstOrDefault()?.ConstructorArguments
                : method.CustomAttributes.ToList()[method.CustomAttributes.Count() - 1].ConstructorArguments;
            var desc = (string)attribute![3].Value;
            // 方法标识
            var text = (string)attribute![4].Value;
            if (_ignoreMethods.Contains(text)) continue;
            // 是否过滤
            var value = attribute![5].Value;
            var filter = value != null && (bool)value;
            if (!filter)
            {
                driverFilesComboSelect.Add(new DriverSelectListItem { Text = text ?? method.Name, Value = method.Name, Desc = desc });
                continue;
            }

            // 根据字段过滤相应的方法
            var filterField = (string)attribute![6].Value;
            var filterValue = (string)attribute![7].Value;
            var config = device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == filterField);
            if (config == null) continue;
            if (filterValue != null && filterValue.Contains(config.Value))
                driverFilesComboSelect.Add(new DriverSelectListItem { Text = text ?? method.Name, Value = method.Name, Desc = desc });
        }

        return driverFilesComboSelect;
    }

    /// <summary>
    ///     设备状态监控列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/device/monitorPage")]
    public async Task<dynamic> MonitorPage()
    {
        var devicesList = await _device.AsQueryable().Where(w => w.Enable)
            .OrderBy(w => w.Index)
            .Select<dynamic>(s => new
            {
                s.DeviceName,
                s.OtherName,
                s.Id,
                s.Index
            })
            .ToListAsync();
        return devicesList;
    }

    /// <summary>
    ///     设备详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/device/detail")]
    public async Task<Device> Detail([FromQuery] BaseId input)
    {
        var device = await _device.AsQueryable()
            .Includes(x => x.Driver)
            .Includes(x => x.DeviceConfigs)
            .Includes(x => x.DeviceEvent)
            .FirstAsync(f => f.Id == input.Id);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        // 设备采集线程
        var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == device.Id);
        if (deviceThread != null)
            device.IsConnect = deviceThread.Driver.IsConnected;

        // 当前设备属性是否有未发布的点位
        var deviceVariableAny = await _device.AsSugarClient().Queryable<DeviceVariable>().AnyAsync(a => a.DeviceId == input.Id && a.Release == false);
        device.ReleaseAll = deviceVariableAny != true;
        // 网口设备
        if (device.DeviceConfigs.Select(s => s.DeviceConfigName).Contains("IpAddress"))
        {
            device.IpAddress = device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "IpAddress")!.Value + ":" +
                               device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "Port")!.Value;
        }
        // url 设备
        else if (device.DeviceConfigs.Select(s => s.DeviceConfigName).Contains("url"))
        {
            device.IpAddress = device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "url")!.Value;
        }
        // 串口设备
        else if (device.DeviceConfigs.Select(s => s.DeviceConfigName).Contains("SerialNumber"))
        {
            if (Enum.TryParse(device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "SerialNumber")?.Value, true, out SerialNumberEnum enumValue))
                device.IpAddress = EnumUtil.GetEnumDesc(enumValue);
            if (Enum.TryParse(device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "BaudRate")?.Value, true, out BaudRateEnum baudRateEnumValue))
                device.IpAddress = device.IpAddress + ":" + EnumUtil.GetEnumDesc(baudRateEnumValue);
        }

        var deviceConfigs = device.DeviceConfigs.OrderBy(o => o.Order).ToList();
        device.DeviceConfigs = deviceConfigs;
        return device;
    }

    /// <summary>
    ///     设备扩展信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/device/deviceEx")]
    public async Task<DeviceExOutput> DeviceEx([FromQuery] BaseId input)
    {
        var device = await _device.AsQueryable()
            .Includes(x => x.Driver)
            .Includes(x => x.DeviceConfigs)
            .Includes(x => x.DeviceVariable)
            .Includes(x => x.DeviceEvent)
            .FirstAsync(f => f.Id == input.Id);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        var deviceEx = device.Adapt<DeviceExOutput>();
        deviceEx.DriverName = device.Driver!.DriverName;
        return deviceEx;
    }

    /// <summary>
    ///     复制其他扩展信息列表
    /// </summary>
    /// <param name="driverId"></param>
    /// <returns></returns>
    [HttpGet("/device/deviceEx/select/{driverId:long}")]
    public async Task<List<DeviceExSelectOutput>> DeviceExSelect(long driverId)
    {
        var deviceList = await _device.AsQueryable()
            .Where(w => w.DriverId == driverId)
            .Includes(x => x.Driver)
            .Includes(x => x.DeviceConfigs)
            .Includes(x => x.DeviceVariable)
            .Includes(x => x.DeviceEvent)
            .ToListAsync();

        var deviceOutput = new List<DeviceExSelectOutput>();
        foreach (var device in deviceList)
        {
            var deviceExSelectOutput = new DeviceExSelectOutput
            {
                Id = device.Id,
                DeviceEx = device.Adapt<DeviceExOutput>(),
                DeviceName = device.DeviceName,
                DriverName = device.DriverName,
                OtherName = device.OtherName,
                UpdatedTime = device.UpdatedTime
            };
            var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == device.Id);
            if (deviceThread != null)
                deviceExSelectOutput.IsConnect = deviceThread.Driver.IsConnected;
            deviceOutput.Add(deviceExSelectOutput);
        }

        return deviceOutput;
    }

    #endregion Get

    #region Post

    /// <summary>
    ///     导出设备全部信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/exPort")]
    public async Task<Device> ExPort(BaseId input)
    {
        return await _device.AsQueryable().Where(f => f.Id == input.Id)
            .Includes(w => w.DeviceVariable)
            .Includes(w => w.DeviceConfigs)
            .Includes(w => w.DeviceEvent)
            .FirstAsync();
    }

    /// <summary>
    ///     导出设备全部信息
    /// </summary>
    /// <returns></returns>
    [HttpPost("/device/exPortAll")]
    public async Task<List<Device>> ExPortAll()
    {
        return await _device.AsQueryable()
            .Includes(w => w.DeviceVariable)
            .Includes(w => w.DeviceConfigs)
            .Includes(w => w.DeviceEvent)
            .ToListAsync();
    }

    /// <summary>
    ///     导入设备
    /// </summary>
    /// <returns></returns>
    [HttpPost("/api/device/inPortAll")]
    [HttpPost("/device/inPortAll")]
    public async Task InPortAll([FromForm] InPortAllInput input)
    {
        string content;
        using (var reader = new StreamReader(input.File.OpenReadStream()))
        {
            content = await reader.ReadToEndAsync();
        }

        var deviceList = new List<Device>();
        try
        {
            deviceList = JSON.Deserialize<List<Device>>(content);
        }
        catch
        {
            deviceList.Add(JSON.Deserialize<Device>(content));
        }

        if (await AuthorizationUtil.IsAuthorized())
        {
            // 最大添加设备点位数量
            var authorize = await AuthorizationUtil.GetAuthorization();
            if (authorize == null)
                throw Oops.Oh("未授权,请联系管理员！");
            if (await _device.CountAsync(w => w.Id > 0) + deviceList.Count > authorize.DeviceNumber)
                throw Oops.Oh($"超出网关允许最大设备数量:{authorize.DeviceNumber},创建失败！");

            var deviceVariableCount = await _device.AsSugarClient().Queryable<DeviceVariable>().CountAsync(w => w.Id > 0);
            if (deviceVariableCount + deviceList.Select(s => s.DeviceVariable).Count() >= authorize.VariableNumber)
                throw Oops.Oh($"超出网关允许最大设备点位采集数量:{authorize.VariableNumber},剩余可添加数量:{authorize.VariableNumber - deviceVariableCount}个,添加失败！");
        }

        await _device.AsSugarClient().InsertNav(deviceList)
            .Include(w => w.DeviceConfigs)
            .Include(w => w.DeviceVariable)
            .Include(w => w.DeviceEvent)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     设备扩展信息保存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/deviceExSave")]
    public async Task<long> DeviceExSave(DeviceExSaveInput input)
    {
        if (await AuthorizationUtil.IsAuthorized())
        {
            //最大添加设备点位数量
            var authorize = await AuthorizationUtil.GetAuthorization();
            if (authorize == null)
                throw Oops.Oh("未授权,请联系管理员！");
            var deviceVariableCount = await _device.AsSugarClient().Queryable<DeviceVariable>().CountAsync(w => w.Id > 0);
            if (deviceVariableCount + input.DeviceVariable.Count >= authorize.VariableNumber)
                throw Oops.Oh($"超出网关允许最大设备点位采集数量:{authorize.VariableNumber},剩余可添加数量:{authorize.VariableNumber - deviceVariableCount}个,添加失败！");
        }

        var device = await _device.AsQueryable().Where(f => f.Id == input.Id)
            .Includes(w => w.DeviceConfigs)
            .Includes(w => w.DeviceVariable)
            .FirstAsync();
        if (device == null)
            throw Oops.Oh("设备不存在,暂不支持保存!");

        device.DeviceName = input.DeviceName;
        device.OtherName = input.OtherName;
        device.Index = input.Index;
        device.Description = input.Description;
        device.Enable = input.Enable;
        device.Release = false;
        device.DeviceEvent = input.DeviceEvent.Any() ? input.DeviceEvent.Adapt<List<DeviceEvent>>() : new List<DeviceEvent>();
        foreach (var deviceConfig in device.DeviceConfigs)
        {
            var config = input.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == deviceConfig.DeviceConfigName);
            if (config != null)
                deviceConfig.Value = config.Value;
        }

        foreach (var deviceEvent in device.DeviceEvent)
        {
            deviceEvent.DeviceId = device.Id;
            deviceEvent.Id = YitIdHelper.NextId();
            foreach (var customEventWhere in deviceEvent.CustomEventWhereList)
            {
                // 处理触发条件
                foreach (var eventDeviceVariable in customEventWhere.TriggerConditionList.SelectMany(triggerCondition => triggerCondition.EventDeviceVariable))
                    eventDeviceVariable.Value = eventDeviceVariable.Value.GetJsonElementValue();
                // 处理动作
                foreach (var deviceVariableWrite in customEventWhere.CustomEventActionList.SelectMany(customEventAction => customEventAction.DeviceVariableWrite))
                    deviceVariableWrite.Value = deviceVariableWrite.Value.GetJsonElementValue();
            }
        }

        device.DeviceVariable.Clear();

        foreach (var variable in input.DeviceVariable.Select(deviceVariable => new DeviceVariable
        {
            Identifier = deviceVariable.Identifier,
            Name = deviceVariable.Name,
            DefaultValue = deviceVariable.DefaultValue,
            Length = deviceVariable.Length,
            TransitionType = deviceVariable.TransitionTypeName == "异常"
                         ? 0
                         : EnumUtil.GetEnum<TransPondDataTypeEnum>(deviceVariable.TransitionTypeName),
            ValueSource = deviceVariable.ValueSourceName == "异常"
                         ? 0
                         : EnumUtil.GetEnum<ValueSourceEnum>(deviceVariable.ValueSourceName),
            Order = deviceVariable.Order,
            Expressions = deviceVariable.Expressions,
            Script = deviceVariable.Script,
            SendType = deviceVariable.SendTypeName == "异常"
                         ? 0
                         : EnumUtil.GetEnum<SendTypeEnum>(deviceVariable.SendTypeName),
            Enable = deviceVariable.Enable,
            Tags = deviceVariable.Tags,
            Period = deviceVariable.Period,
            ArchiveTime = deviceVariable.ArchiveTime,
            Description = deviceVariable.Description,
            DeviceVariableFilter = new DeviceVariableFilter
            {
                Max = deviceVariable.DeviceVariableFilter.Max,
                Min = deviceVariable.DeviceVariableFilter.Min,
                Save = deviceVariable.DeviceVariableFilter.Save,
                SetMax = deviceVariable.DeviceVariableFilter.SetMax,
                SetMin = deviceVariable.DeviceVariableFilter.SetMin,
                MaxFilterType = deviceVariable.DeviceVariableFilter.MaxFilterTypeName == "异常"
                             ? 0
                             : EnumUtil.GetEnum<DeviceVariableFilterTypeEnum>(deviceVariable.DeviceVariableFilter.MaxFilterTypeName),
                MinFilterType = deviceVariable.DeviceVariableFilter.MinFilterTypeName == "异常"
                             ? 0
                             : EnumUtil.GetEnum<DeviceVariableFilterTypeEnum>(deviceVariable.DeviceVariableFilter.MinFilterTypeName)
            },
            DeviceVariableEx = new DeviceVariableEx
            {
                Method = deviceVariable.DeviceVariableEx.Method,
                RegisterAddress = deviceVariable.DeviceVariableEx.RegisterAddress,
                DataType = deviceVariable.DeviceVariableEx.DataTypeName == "异常"
                             ? 0
                             : EnumUtil.GetEnum<DataTypeEnum>(deviceVariable.DeviceVariableEx.DataTypeName),
                ProtectType = deviceVariable.DeviceVariableEx.ProtectTypeName == "异常"
                             ? 0
                             : EnumUtil.GetEnum<ProtectTypeEnum>(deviceVariable.DeviceVariableEx.ProtectTypeName)
            },
            Custom = deviceVariable.Custom,
            Unit = deviceVariable.Unit,
            IsSystem = deviceVariable.IsSystem,
            Release = false
        }))

            device.DeviceVariable.Add(variable);
        await _db.UpdateNav(device)
            .Include(ex => ex.DeviceConfigs)
            .Include(ex => ex.DeviceVariable)
            .Include(w => w.DeviceEvent)
            .ExecuteCommandAsync();

        await _eventPublisher.PublishAsync(EventConst.UpdateDeviceThread, device);
        return device.Id;
    }

    /// <summary>
    ///     创建设备
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/add")]
    public async Task<long> Add(DeviceAdd input)
    {
        // 验证是否需要开启授权
        if (await AuthorizationUtil.IsAuthorized())
        {
            // 最大添加设备数量
            var authorize = await AuthorizationUtil.GetAuthorization();
            if (authorize == null)
                throw Oops.Oh("未授权,请联系管理员！");
            if (await _device.CountAsync(w => w.Id > 0) >= authorize.DeviceNumber)
                throw Oops.Oh($"超出网关允许最大设备数量:{authorize.DeviceNumber},创建失败！");
        }

        // 验证协议是否存在
        var driver = await _device.CopyNew().AsSugarClient().Queryable<Feng.IotGateway.Core.Entity.Driver>().FirstAsync(f => f.Id == input.DriverId);
        if (driver == null)
            throw Oops.Oh(ErrorCode.Dri404);
        // 验证协议的可使用数
        if (driver.AuthorizesNum == 0)
            throw Oops.Oh(ErrorCode.Dri301);
        //
        var device = input.Adapt<Device>();
        device.Enable = true;
        device.Index = 99;
        // 添加设备增加默认点位值
        _driverService.AddConfigs(device, driver);
        device.DeviceVariable = new List<DeviceVariable>();

        // cnc设备添加标准属性
        if (driver.DriverType == DriverTypeEnum.Cnc || (driver.DriverType == DriverTypeEnum.Plc && _ignoreList.Contains(driver.FileName)))
            device.DeviceVariable.AddRange(await CreateCncVariables(device, driver));
        // 添加系统默认属性
        device.DeviceVariable.AddRange(await _deviceHostedService.CreateSystemVariables(device));
        // 新增设备
        var isAdd = await _db.CopyNew().InsertNav(device)
            .Include(con => con.DeviceConfigs)
            .Include(w => w.DeviceVariable)
            .ExecuteCommandAsync();
        // 添加成功后直接启动
        if (isAdd)
            await _eventPublisher.PublishAsync(EventConst.CreateDeviceThread, device.Id);
        return device.Id;
    }

    private readonly List<string> _ignoreList = new() { "PORCHESON_Ps660Bm.dll",
    "HongXunAkDouble.dll",
    "TechMationAk.dll", "PORCHESON_Ps660Am.dll",
    "KeQiang_T6H3.dll", "KeQiang_T6F3.dll",
    "WarpingMachine.dll", "TechMation5521.dll" };

    /// <summary>
    ///     修改设备
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/update")]
    public async Task<bool> Update(Device input)
    {
        if (await _device.IsAnyAsync(f => f.Id != input.Id && f.DeviceName == input.DeviceName))
            throw Oops.Oh("设备名称已存在！");
        // 设备名称
        var device = await _device.GetFirstAsync(f => f.Id == input.Id);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        if (device.DeviceName != input.DeviceName && device.Release)
            // 删除模型重新发布
            DeleteSTable(device.DeviceName);
        var update = await _db.CopyNew().UpdateNav(input, new UpdateNavRootOptions
        {
            IgnoreColumns = new[] { "Status" }
        })
            .Include(conf => conf.DeviceConfigs)
            .ExecuteCommandAsync();
        if (!update)
            throw Oops.Oh(ErrorCode.Com1001);

        //修改采集线程
        await _eventPublisher.PublishAsync(EventConst.UpdateDeviceThread, input);
        return true;
    }

    /// <summary>
    ///     设备启用/禁用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/enable")]
    public async Task<bool> Enable(EnableInput<long> input)
    {
        var device = await _device.GetFirstAsync(f => f.Id == input.Id);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        device.Enable = input.Enable;
        // 先修改
        await _device.CopyNew().AsUpdateable(device).UpdateColumns(w => w.Enable).ExecuteCommandAsync();
        // 后启动采集
        if (device.Enable) //启动采集线程
            await _eventPublisher.PublishAsync(EventConst.CreateDeviceThread, device.Id);
        else //停止采集线程
            await _eventPublisher.PublishAsync(EventConst.StopDeviceThread, device);
        return true;
    }

    /// <summary>
    ///     设备批量启用/禁用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/batchEnable")]
    public async Task<bool> BatchEnable(DeviceBatchEnableInput input)
    {
        var deviceList = await _device.AsQueryable().Where(f => input.IdList.Contains(f.Id)).ToListAsync();
        foreach (var device in deviceList.Where(device => device.Enable != input.Enable))
        {
            device.Enable = input.Enable;
            await _device.CopyNew().AsUpdateable(device).UpdateColumns(w => w.Enable).ExecuteCommandAsync();
            // 启动采集线程
            if (input.Enable)
                await _eventPublisher.PublishAsync(EventConst.CreateDeviceThread, device.Id);
            else
                // 停止采集线程
                await _eventPublisher.PublishAsync(EventConst.StopDeviceThread, device);
        }

        return true;
    }

    /// <summary>
    ///     删除设备
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/delete")]
    public async Task<bool> Delete(BaseId input)
    {
        var device = await _device.GetFirstAsync(f => f.Id == input.Id);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        // 停止采集线程
        await _eventPublisher.PublishAsync(EventConst.StopDeviceThread, device);
        await _db.CopyNew().DeleteNav(device)
            .Include(e => e.DeviceConfigs)
            .Include(e => e.DeviceEventLog)
            .Include(e => e.DeviceVariable)
            .ExecuteCommandAsync();

        if (device.Release)
            // 删除模型重新发布
            DeleteSTable(device.DeviceName);

        return true;
    }

    /// <summary>
    ///     批量删除设备
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/deleteBatch")]
    public async Task<bool> DeleteBatch(BaseId<List<long>> input)
    {
        var deviceList = await _device.AsQueryable().Where(w => input.Id.Contains(w.Id)).ToListAsync();
        foreach (var device in deviceList)
        {
            // 停止采集线程
            await _eventPublisher.PublishAsync(EventConst.StopDeviceThread, device);
            await _db.CopyNew().DeleteNav(device)
                .Include(e => e.DeviceConfigs)
                .Include(e => e.DeviceEventLog)
                .Include(e => e.DeviceVariable)
                .ExecuteCommandAsync();
            if (device.Release)
                // 删除模型
                DeleteSTable(device.DeviceName);
        }

        return true;
    }

    /// <summary>
    ///     设备复制
    /// </summary>
    /// <returns></returns>
    [HttpPost("/device/copy")]
    public async Task Copy(BaseId input)
    {
        if (await AuthorizationUtil.IsAuthorized())
        {
            // 最大添加设备数量
            var authorize = await AuthorizationUtil.GetAuthorization();
            if (authorize == null)
                throw Oops.Oh("未授权,请联系管理员！");
            if (await _device.CountAsync(w => w.Id > 0) >= authorize.DeviceNumber)
                throw Oops.Oh($"超出网关允许最大设备数量:{authorize.DeviceNumber},复制失败！");
        }

        var device = await _device.AsQueryable()
            .Includes(x => x.DeviceConfigs)
            .Includes(x => x.DeviceVariable)
            .Includes(w => w.DeviceEvent)
            .FirstAsync(f => f.Id == input.Id);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        device.Id = YitIdHelper.NextId();
        device.DeviceName += "_copy" + DateTime.Now.ToString("ssff");
        device.Release = false;
        foreach (var config in device.DeviceConfigs)
        {
            config.Id = YitIdHelper.NextId();
            config.DeviceId = device.Id;
        }

        foreach (var variable in device.DeviceVariable)
        {
            variable.Id = YitIdHelper.NextId();
            variable.DeviceId = device.Id;
        }

        foreach (var deviceEvent in device.DeviceEvent)
        {
            deviceEvent.Id = YitIdHelper.NextId();
            deviceEvent.DeviceId = device.Id;
        }

        device.Enable = false;
        await _db.CopyNew().InsertNav(device)
            .Include(ex => ex.DeviceConfigs)
            .Include(ex => ex.DeviceVariable)
            .Include(w => w.DeviceEvent)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     设备-发布
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/release")]
    public async Task DevicePublish(EnableInput<long> input)
    {
        if (MachineUtil.CurrentSystemEnvironment == CurrentSystemEnvironmentEnum.Px30)
            throw Oops.Oh("固件暂不支持！");
        var device = await _device.AsQueryable().Where(f => f.Id == input.Id).Includes(w => w.DeviceVariable).FirstAsync();
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        device.Release = input.Enable;
        // 删除模型重新发布
        DeleteSTable(device.DeviceName);
        // 生成Td超级表
        CreateSTable(device);
        await _device.CopyNew().AsUpdateable(device).UpdateColumns(w => new { w.Release }).ExecuteCommandAsync();
        await _device.CopyNew().AsSugarClient().Updateable(device.DeviceVariable).UpdateColumns(w => new { w.Release }).ExecuteCommandAsync();
        // 重新加载设备配置
        _deviceHostedService.SetVariable(device);
    }

    #endregion Post

    #region 采集启动/暂停

    /// <summary>
    ///     单设备启动采集
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/start")]
    public async Task Start(BaseId input)
    {
        var device = await _device.GetFirstAsync(f => f.Id == input.Id);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        if (!device.Enable)
            return;
        // 启动采集线程
        await _eventPublisher.PublishAsync(EventConst.UpdateDeviceThread, device);
    }

    /// <summary>
    ///     单设备停止采集
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/stop")]
    public async Task Stop(BaseId input)
    {
        var device = await _device.GetFirstAsync(f => f.Id == input.Id);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        // 停止采集线程
        await _eventPublisher.PublishAsync(EventConst.StopDeviceThread, device);
    }

    /// <summary>
    ///     单设备-采集重启
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/restart")]
    public async Task Restart(BaseId input)
    {
        var device = await _device.GetFirstAsync(f => f.Id == input.Id);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        if (!device.Enable)
            return;
        // 启动采集线程
        await _eventPublisher.PublishAsync(EventConst.UpdateDeviceThread, device);
    }

    /// <summary>
    ///     全部启动采集
    /// </summary>
    /// <returns></returns>
    [HttpPost("/device/startAll")]
    public async Task StartAll()
    {
        MachineUtil.ServiceOnlineTime = DateTime.UtcNow;
        var devices = await _device.AsQueryable().Where(x => x.Enable).ToListAsync();
        foreach (var device in devices)
            await _eventPublisher.PublishAsync(EventConst.CreateDeviceThread, device.Id);
    }

    /// <summary>
    ///     全部停止采集
    /// </summary>
    /// <returns></returns>
    [HttpPost("/device/stopAll")]
    public async Task StopAll()
    {
        // 停止采集线程
        await _eventPublisher.PublishAsync(EventConst.StopDeviceThreadAll, "");
    }

    #endregion

    #region 实时调试

    /// <summary>
    ///     设备实时调试-读取
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/debug")]
    public async Task<object> DeviceDebugRead(DeviceDebugInput input)
    {
        var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == input.Id);
        if (deviceThread == null)
            throw Oops.Oh("设备断开连接");

        var method = deviceThread.Methods.FirstOrDefault(x => x.Name == input.Method);
        if (method == null)
            throw Oops.Oh("非法读取");

        object ret;
        if (deviceThread.Device?.Driver?.DriverType == DriverTypeEnum.Plc)
        {
            ret = await ((Task<object>)method.Invoke(deviceThread.Driver, new object[]
            {
                input.Address, input.DataType, input.Lenth, input.Encoding
            }))!;
        }
        else
        {
            var readCnc = await ((Task<DriverReturnValueModel>)method.Invoke(deviceThread.Driver, new object[] { }))!;
            ret = readCnc.Value;
        }

        return ret;
    }

    /// <summary>
    ///     设备实时调试-写入
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/debugWrite")]
    public async Task<object> DeviceDebugWrite(DeviceDebugWriteInput input)
    {
        var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == input.Id);
        if (deviceThread == null)
            throw Oops.Oh("设备断开连接！");

        var method = deviceThread.Methods.FirstOrDefault(x => x.Name == "WriteAsync");
        if (method == null)
            throw Oops.Oh("暂不支持写入！");

        if (deviceThread.Device?.Driver?.DriverType == DriverTypeEnum.Cnc)
            throw Oops.Oh("CNC类型设备暂时不支持写入调试！");

        var req = new DriverAddressIoArgModel(0L, input.Address, input.Length, input.DataType, input.Value, encoding: input.Encoding);
        object ret = await ((Task<WriteResponse>)method.Invoke(deviceThread.Driver, new object[]
        {
            req
        }))!;

        return JSON.Serialize(ret);
    }

    #endregion

    #region Dnc管理

    /// <summary>
    ///     读取设备程序底层路径信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/file/routeInfo")]
    [AllowAnonymous]
    public async Task<dynamic> DeviceFileRouteInfo(DeviceFileRouteInfoInput input)
    {
        var deviceThread = await DncDeviceThread(input.DeviceName);
        var method = deviceThread.Methods.FirstOrDefault(x => x.Name == "FileRouteInfo");
        if (method == null)
            throw Oops.Oh("暂不支持方法！");

        var ret = await ((Task<object>)method.Invoke(deviceThread.Driver, new object[] { "" }))!;
        return ret;
    }

    /// <summary>
    ///     读取设备程序底层文件内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/file/read")]
    [AllowAnonymous]
    public async Task<string> DeviceFileRead(FileReadInput input)
    {
        var deviceThread = await DncDeviceThread(input.DeviceName);
        var method = deviceThread.Methods.FirstOrDefault(x => x.Name == "FileRead");
        if (method == null)
            throw Oops.Oh("暂不支持方法！");

        var ret = await ((Task<string>)method.Invoke(deviceThread.Driver, new object[] { input }))!;
        Log.Warning($"【Dnc读取】 参数:{input.ToJson()}");
        // 保存文件
        await SaveFile(ret, input.FileName, deviceThread.Device.Id);
        return ret;
    }

    /// <summary>
    ///     删除设备程序底层文件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/file/del")]
    [AllowAnonymous]
    public async Task<bool> DeviceFileDel(FileDelInput input)
    {
        var deviceThread = await DncDeviceThread(input.DeviceName);
        var method = deviceThread.Methods.FirstOrDefault(x => x.Name == "FileDel");
        if (method == null)
            throw Oops.Oh("暂不支持方法！");
        var ret = await ((Task<bool>)method.Invoke(deviceThread.Driver, new object[] { input }))!;
        return ret;
    }

    /// <summary>
    ///     写入设备程序底层文件内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/file/write")]
    [AllowAnonymous]
    public async Task<bool> DeviceFileWrite(FileWriteInput input)
    {
        var deviceThread = await DncDeviceThread(input.DeviceName);

        var method = deviceThread.Methods.FirstOrDefault(x => x.Name == "FileWrite");
        if (method == null)
            throw Oops.Oh("暂不支持方法！");

        var ret = await ((Task<bool>)method.Invoke(deviceThread.Driver, new object[] { input }))!;
        return ret;
    }

    /// <summary>
    ///     写入设备程序底层文件内容 -来源本地文件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [NonAction]
    public async Task<bool> DeviceFileWrite(FileWriteByFileInput input)
    {
        var deviceThread = await DncDeviceThread(input.DeviceName);

        var dncFile = await _db.Queryable<DncFile>().FirstAsync(f => f.FileName == input.FileName && f.DeviceId == deviceThread.Device.Id);
        if (dncFile == null)
            throw Oops.Oh($"网关暂未保存 【{input.FileName}】 文件");
        var content = await File.ReadAllTextAsync(Path.Combine(dncFile.FilePath, string.IsNullOrEmpty(dncFile.FileSuffix) ? dncFile.Id + ".txt" : dncFile.Id + dncFile.FileSuffix));

        var method = deviceThread.Methods.FirstOrDefault(x => x.Name == "FileWrite");
        if (method == null)
            throw Oops.Oh("暂不支持方法！");

        var ret = await ((Task<bool>)method.Invoke(deviceThread.Driver, new object[]
        {
            new FileWriteInput
            {
                Content = content,
                DeviceName = input.DeviceName,
                Path = input.Path
            }
        }))!;
        return ret;
    }

    /// <summary>
    ///     设置为主程序
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/file/setCurrentProgram")]
    [AllowAnonymous]
    public async Task<bool> DeviceFileSetCurrentProgram(SetCurrentProgramInput input)
    {
        var deviceThread = await DncDeviceThread(input.DeviceName);
        var method = deviceThread.Methods.FirstOrDefault(x => x.Name == "SetCurrentProgram");
        if (method == null)
            throw Oops.Oh("暂不支持方法！");
        var ret = await ((Task<bool>)method.Invoke(deviceThread.Driver, new object[] { input }))!;
        return ret;
    }

    #endregion

    #region 私有方法

    /// <summary>
    ///     生成TDengIne表
    /// </summary>
    /// <param name="device"></param>
    [NonAction]
    private void CreateSTable(Device device)
    {
        if (!device.DeviceVariable.Any())
            return;
        var sql = $"CREATE TABLE IF NOT EXISTS {device.DeviceName} (ts timestamp";
        foreach (var deviceVariable in device.DeviceVariable)
        {
            sql += ", `" + deviceVariable.Identifier + "` ";
            switch (deviceVariable.TransitionType)
            {
                case TransPondDataTypeEnum.Bool:
                    sql += "BOOL";
                    break;
                case TransPondDataTypeEnum.Int:
                    sql += "BigInt";
                    break;
                case TransPondDataTypeEnum.Double:
                    sql += "float";
                    break;
                case TransPondDataTypeEnum.String:
                case TransPondDataTypeEnum.Bytes:
                    sql += $"nchar({(deviceVariable.Length == 0 ? 64 : deviceVariable.Length)})";
                    break;
            }

            deviceVariable.Release = true;
        }

        sql += ") ;";

        _executeService.ExecuteCommand(sql);
    }

    /// <summary>
    ///     删除TDengIne表
    /// </summary>
    /// <param name="deviceName">设备名称</param>
    [NonAction]
    private void DeleteSTable(string deviceName)
    {
        var sql = $"DROP TABLE IF EXISTS  {deviceName};";
        _executeService.ExecuteCommand(sql);
    }

    /// <summary>
    ///     验证设备是否允许管理Dnc
    /// </summary>
    /// <param name="deviceName"></param>
    /// <returns></returns>
    private Task<DeviceThread> DncDeviceThread(string deviceName)
    {
        var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.DeviceName == deviceName);
        if (deviceThread == null)
            throw Oops.Oh("设备断开连接！");
        if (deviceThread.Device.DeviceConfigs.All(a => a.DeviceConfigName != "Dnc"))
            throw Oops.Oh("设备暂不支持Dnc！");

        var dnc = false;
        try
        {
            dnc = Convert.ToInt32(deviceThread.Device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "Dnc")?.Value) == 1;
        }
        catch
        {
            // 兼容性配置
            dnc = deviceThread.Device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "Dnc")?.Value.ToLower() == "true";
        }

        if (!dnc)
            throw Oops.Oh("设备限制不允许Dnc操作,请在网关中开启设备的Dnc管理！");
        return Task.FromResult(deviceThread);
    }

    /// <summary>
    ///     Dnc操作将文件保存在本地
    /// </summary>
    /// <param name="content">文件内容</param>
    /// <param name="fileName">文件名称</param>
    /// <param name="deviceId">关联设备Id</param>
    private async Task SaveFile(string content, string fileName, long deviceId)
    {
        try
        {
            //将内容转成文件返回
            var file = ObjectExtension.ConvertToIFormFile(content, fileName);
            var path = await _sysConfigService.GetConfigValue<string>(ConfigConst.DncFilePath);
            //相同文件存在
            var dncFile = await _db.Queryable<DncFile>().FirstAsync(f => f.FileName == fileName && f.DeviceId == deviceId);
            if (dncFile != null)
            {
                dncFile.Version += 1;
                dncFile.FileSize = (long)(file.Length / 1024.0) == 0 ? 1 : (long)(file.Length / 1024.0); // 文件大小KB
                dncFile.FileSuffix = Path.GetExtension(file.FileName).ToLower(); // 文件后缀
                await _db.Updateable(dncFile).ExecuteCommandAsync();
            }
            else
            {
                dncFile = new DncFile
                {
                    DeviceId = deviceId,
                    FileSize = (long)(file.Length / 1024.0) == 0 ? 1 : (long)(file.Length / 1024.0), // 文件大小KB
                    Version = 1,
                    FileName = fileName,
                    FileSuffix = Path.GetExtension(file.FileName).ToLower(),
                    FilePath = path,
                    Id = YitIdHelper.NextId()
                };
                await _db.Insertable(dncFile).ExecuteCommandAsync();
            }

            //文件本地保存
            await UploadFile(file, path, string.IsNullOrEmpty(dncFile.FileSuffix) ? dncFile.Id + ".txt" : dncFile.Id + dncFile.FileSuffix);
        }
        catch (Exception e)
        {
            Log.Error("【Dnc保存】 Error:" + e.Message);
        }
    }

    /// <summary>
    ///     上传文件
    /// </summary>
    /// <param name="file"></param>
    /// <param name="path"></param>
    /// <param name="fileGuid"></param>
    /// <returns></returns>
    private async Task UploadFile(IFormFile file, string path, string fileGuid)
    {
        //Dnc文件存放目录
        if (path == null)
            return;

        if (!Directory.Exists(path))
            Directory.CreateDirectory(path);
        await using var stream = File.Create(Path.Combine(path, fileGuid));
        await file.CopyToAsync(stream);
    }

    /// <summary>
    ///     增加Cnc类型标准属性
    /// </summary>
    private Task<List<DeviceVariable>> CreateCncVariables(Device device, Feng.IotGateway.Core.Entity.Driver driver)
    {
        //1.驱动
        var driverInfo = _driverService.DriverInfos.FirstOrDefault(x => x.Type.FullName == driver.AssembleName);
        if (driverInfo == null)
            throw Oops.Oh("非法驱动！");

        Type[] types = { typeof(DriverInfoDto) };
        object[] param =
        {
            new DriverInfoDto {Socket = _socket, DeviceId = device.Id, DeviceName = device.DeviceName, ConnectType = driver.ConnectType}
        };

        var constructor = driverInfo.Type.GetConstructor(types);
        if (constructor == null)
            throw Oops.Oh("暂不支持该驱动！");
        var deviceObj = constructor.Invoke(param) as IDriver;
        var methods = deviceObj!.GetType().GetMethods().Where(x => x.GetCustomAttribute(typeof(MethodAttribute)) != null).ToList();
        var deviceVariableList = (from method in methods
                                  let attribute = method.CustomAttributes.ToList().FirstOrDefault(w => w.ConstructorArguments.Count > 1)?.ConstructorArguments
                                  let transitionType = (TransPondDataTypeEnum)attribute![1].Value!
                                  where method.Name is not ("Debug" or "WriteAsync" or "FileWrite" or "FileRead" or "FileRouteInfo" or "Clear" or "R" or "FileDel" or "SetCurrentProgram" or "Macro")
                                  select new DeviceVariable
                                  {
                                      Id = YitIdHelper.NextId(),
                                      SendType = SendTypeEnum.Always,
                                      Description = (string)attribute![3].Value,
                                      Expressions = "",
                                      Identifier = (string)attribute![0].Value!,
                                      Length = transitionType == TransPondDataTypeEnum.Double ? (ushort)3 : transitionType == TransPondDataTypeEnum.String ? (ushort)50 : (ushort)0,
                                      Name = (string)attribute![4].Value!,
                                      Script = "",
                                      DeviceId = device.Id,
                                      ValueSource = ValueSourceEnum.Get,
                                      TransitionType = transitionType,
                                      DeviceVariableEx = new DeviceVariableEx
                                      {
                                          Method = method.Name,
                                          ProtectType = ProtectTypeEnum.ReadOnly,
                                          DataType = transitionType == TransPondDataTypeEnum.Double ? DataTypeEnum.Double :
                                            transitionType == TransPondDataTypeEnum.Int ? DataTypeEnum.Int32 :
                                            transitionType == TransPondDataTypeEnum.String ? DataTypeEnum.String :
                                            transitionType == TransPondDataTypeEnum.Bool ? DataTypeEnum.Bool : DataTypeEnum.Int32
                                      },
                                      Enable = true,
                                      DefaultValue = "",
                                      Tags = new List<string>(),
                                      DeviceVariableFilter = new DeviceVariableFilter(),
                                      Unit = "",
                                      Custom = ""
                                  }).ToList();

        return Task.FromResult(deviceVariableList);
    }

    #endregion
}