using Common.Extension;
using IotGateway.Application.Entity;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.EdgeDevice;

/// <summary>
///     采集线程统一入口
/// </summary>
public class DeviceThread : IDisposable
{
    private readonly WriteService _writeService;
    private readonly Interpreter _interpreter;
    private readonly CancellationTokenSource _tokenSource = new();
    private DeviceConnectDto _connect = new();
    public Device Device { get; set; }
    public IDriver Driver { get; set; }
    public List<MethodInfo> Methods { get; set; }

    #region 开放字段

    /// <summary>
    ///     读取完成存储字典数据
    /// </summary>
    public ObservableDictionary<string, DriverReturnValueModel> DeviceVariables { get; set; } = new();

    /// <summary>
    ///     属性源
    ///     key-标识符
    /// </summary>
    public ConcurrentDictionary<string, DeviceVariable> DeviceVariableSource { get; set; } = new();

    /// <summary>
    ///     采集设备状态监控
    /// </summary>
    public readonly DeviceStatusDto DeviceStatus = new();

    /// <summary>
    ///     脚本
    ///     开放该字段，用于外部调用脚本
    /// </summary>
    public EngineSingletonService Engine;

    /// <summary>
    ///     属性重置：公开该字段，属性在外部发生更改时需要重置该字段值
    /// </summary>
    public bool Restart = true;

    #endregion

    #region 私有字段

    /// <summary>
    ///     订阅模式-订阅属性
    /// </summary>
    private DeviceVariable _subDeviceVariable;

    /// <summary>
    /// </summary>
    private IServiceProvider Services { get; }

    /// <summary>
    /// </summary>
    private Task Task { get; set; }

    /// <summary>
    ///     socket
    /// </summary>
    private SendMessageService Socket { get; }

    /// <summary>
    ///     读取完毕需要清空的协议
    /// </summary>
    private readonly List<string> _driverClear = new() { "Fanuc", "Fanuc18i", "MazakSmooth", "Heidenhain", "FanucTtc", "Knd", "LncTcp", "MazakSmart", "Hnc", "Gsk25im" };

    /// <summary>
    ///     采集推送对象
    /// </summary>
    private PayLoad _payLoad;

    /// <summary>
    ///     计时器
    /// </summary>
    private readonly Stopwatch _stopwatch = new();

    /// <summary>
    ///     是否启用批量读取
    /// </summary>
    private bool _bulkReadEnabled;

    /// <summary>
    ///     批量采集是否初始化：控制避免重复初始化
    /// </summary>
    private bool _bulkReadInit;

    /// <summary>
    ///     缓存的InitGroup方法，避免重复LINQ查询
    /// </summary>
    private MethodInfo? _cachedInitMethod;

    /// <summary>
    ///     缓存的BatchRead方法，避免重复LINQ查询
    /// </summary>
    private MethodInfo? _cachedBatchReadMethod;

    #endregion

    /// <summary>
    /// </summary>
    /// <param name="device">采集设备</param>
    /// <param name="driver">采集协议</param>
    /// <param name="socket">socket推送</param>
    /// <param name="services">作用域</param>
    /// <param name="writeService">TDengine时序库</param>
    public DeviceThread(Device device, IDriver driver, SendMessageService socket, IServiceProvider services, WriteService writeService)
    {
        Services = services;
        _writeService = writeService;
        Device = device;
        Driver = driver;
        Socket = socket;
        _interpreter = new Interpreter();
        Engine = services.GetService<EngineSingletonService>();
        // 订阅属性写入事件
        _ = DeviceWriteSubscribe();
        // 订阅【云端要求刷新设备实时数据】  备注:目前仅SupOS平台有效
        _ = DeviceCloudRequestRefreshRealTimeDeviceData();
        // FLink订阅【云端要求刷新设备实时数据】
        _ = FLinkRequestRefreshRealTimeDeviceData();
        // 写入属性间隔时间
        _delayTime = Convert.ToInt32(device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "WriteInterval")?.Value ?? "100");
        Methods = Driver.GetType().GetMethods().Where(x => x.GetCustomAttribute(typeof(MethodAttribute)) != null).ToList();
        // 加载数据
        Load();
        // 启动采集
        StartDataCollectionTask();
    }

    /// <summary>
    ///     启动采集
    /// </summary>
    private void StartDataCollectionTask()
    {
        SendInfo($"设备【{Device.DeviceName}】开始启动采集任务...");
        Task = Task.Factory.StartNew(async () =>
        {
            try
            {
                // 初始化payload
                await InitializePayload(Device);

                // 静态属性
                List<DeviceVariable> staticDeviceVariable = new();
                // 读取属性
                List<DeviceVariable> readDeviceVariable = new();
                // 脚本属性
                List<DeviceVariable> calculateDeviceVariable = new();

                while (!_tokenSource.IsCancellationRequested)
                {
                    try
                    {
                        if (Restart)
                        {
                            // 静态变量
                            staticDeviceVariable = DeviceVariableSource.Values.Where(w => w.ValueSource == ValueSourceEnum.Static).ToList();

                            readDeviceVariable = DeviceVariableSource.Values
                                .Where(variable => variable.ValueSource == ValueSourceEnum.Get && variable.DeviceVariableEx?.ProtectType != ProtectTypeEnum.WriteOnly).ToList();

                            calculateDeviceVariable = DeviceVariableSource.Values.Where(w => w.ValueSource == ValueSourceEnum.Calculate && !string.IsNullOrWhiteSpace(w.Script?.Trim()))
                                .OrderBy(o => o.Order).ToList();

                            _bulkReadInit = false;
                            Restart = false;
                        }
                        _payLoad.Ts = DateTime.ToLong();
                        _payLoad.Values.Clear();
                        // 采集时间戳
                        SendInfo($"设备【{Device.DeviceName}】开始新一轮采集,时间戳:【{_payLoad.Ts}】");

                        // 读取静态属性
                        await ReadStatic(staticDeviceVariable);

                        // 检查设备是否连接成功
                        var isDeviceConnect = await DeviceConnect();
                        if (isDeviceConnect)
                        {
                            SendInfo($"设备【{Device.DeviceName}】连接成功");

                            // 订阅模式
                            if (Driver.ReadType == ReadTypeEnum.Subscription && _subDeviceVariable != null)
                            {
                                SendInfo($"设备【{Device.DeviceName}】使用订阅模式读取数据...");
                                var isSuccess = await SubscribeAndReadOnce();
                                if (!isSuccess)
                                {
                                    SendError($"设备【{Device.DeviceName}】订阅模式读取失败");
                                    continue;
                                }
                                SendInfo($"设备【{Device.DeviceName}】订阅模式读取成功");
                            }

                            // 增加下行读写次数
                            MachineUtil.ReadCount++;

                            // 记录整组数据采集花费时间
                            _stopwatch.Restart();
                            // 批量方式读取
                            if (_bulkReadEnabled)
                            {
                                SendInfo($"设备【{Device.DeviceName}】开始批量读取数据...");
                                await BatchRead(readDeviceVariable);
                                SendInfo($"设备【{Device.DeviceName}】批量读取数据完成");
                            }
                            // 单个轮询方式读取
                            else
                            {
                                SendInfo($"设备【{Device.DeviceName}】开始轮询读取数据...");
                                await ReadGet(readDeviceVariable);
                                SendInfo($"设备【{Device.DeviceName}】轮询读取数据完成");
                            }
                            _stopwatch.Stop();
                            SendMessage($"设备【{Device.DeviceName}】采集耗时:【{_stopwatch.ElapsedMilliseconds}ms】", "debug");

                            // 超过1s的记录到日志中
                            if (_stopwatch.ElapsedMilliseconds > 1000)
                            {
                                Log.Warning($"设备【{Device.DeviceName}】数据采集耗时过长:【{_stopwatch.ElapsedMilliseconds}ms】");
                            }
                        }
                        else
                        {
                            SendError($"设备【{Device.DeviceName}】连接失败");
                        }

                        // 计算采集花费时间
                        DeviceStatus.ReadMilliseconds = _stopwatch.ElapsedMilliseconds;

                        // 将数据添加到内存中
                        Engine.Device.Load(Device.DeviceName, DeviceVariables);

                        // 执行脚本
                        RunScript(calculateDeviceVariable);

                        // 发送事件
                        if (Device.DeviceEvent.Any())
                        {
                            SendInfo($"设备【{Device.DeviceName}】开始处理设备事件...");
                            foreach (var deviceEvent in Device.DeviceEvent.Where(w => w.Status))
                            {
                                // 属性触发 && 设备正常连接
                                if (deviceEvent.TriggerEventType == TriggerEventTypeEnum.属性触发 && isDeviceConnect)
                                    foreach (var customEventWhere in deviceEvent.CustomEventWhereList.OrderBy(o => o.Order))
                                    {
                                        try
                                        {
                                            if (Convert.ToBoolean(_interpreter.Eval(customEventWhere.Expressions)))
                                            {
                                                await MessageCenter.PublishAsync(string.Format(ConstMethod.DeviceThread, deviceEvent.Id), _payLoad);
                                                break;
                                            }
                                        }
                                        catch (Exception e)
                                        {
                                            Console.WriteLine($"【设备事件】 事件:{deviceEvent.EventName} 第{customEventWhere.Order}个条件表达式解析错误：{e.Message},表达式：[{customEventWhere.Expressions}]！");
                                        }

                                        // 全部条件不符合，返回最后一组动作
                                        if (deviceEvent.CustomEventWhereList.Any(a => a.Order > customEventWhere.Order) || !string.IsNullOrEmpty(customEventWhere.Expressions)) continue;
                                        break;
                                    }
                                else
                                    await MessageCenter.PublishAsync(string.Format(ConstMethod.DeviceThread, deviceEvent.Id), _payLoad);
                            }
                            SendInfo($"设备【{Device.DeviceName}】处理设备事件完成");
                        }

                        // 检查设备是否离线
                        await CheckDeviceStatusAsync();

                        try
                        {
                            // 处理设备状态数据
                            UpdateDeviceStatusAccordingToConnectionAndVariableStatus();

                            // 发送前过滤从不上报和变化上报的数据
                            var newPayLoad = Filter();

                            // 推送到实时数据
                            _ = Socket.Send(JSON.Serialize(_payLoad), string.Format(ConstMethod.DeviceThread, Device.Id));
                            _ = Socket.Send(JSON.Serialize(_payLoad), ConstMethod.OnLineData);

                            // 设备上报策略
                            await DataCollectionReportingRule(newPayLoad);
                        }
                        catch (Exception ex)
                        {
                            SendError($"设备:【{Device.DeviceName}】,推送数据异常:【{ex.Message}】,Error:【{ex.StackTrace}】");
                        }
                    }
                    catch (Exception ex)
                    {
                        SendError($"设备:【{Device.DeviceName}】,设备采集线程异常:【{ex.Message}】,堆栈:【{ex.StackTrace}】");
                    }

                    // 计算实际需要延迟的时间 = 配置的最小周期 - 本次采集耗时
                    var actualDelayTime = Driver.MinPeriod - (int)DeviceStatus.ReadMilliseconds;

                    // 如果实际需要延迟的时间大于0，则延迟相应时间，否则不延迟
                    if (actualDelayTime > 0)
                    {
                        SendInfo($"设备【{Device.DeviceName}】本次采集耗时【{DeviceStatus.ReadMilliseconds}ms】，休眠【{actualDelayTime}ms】");
                        await Task.Delay(actualDelayTime, _tokenSource.Token);
                    }
                    else
                    {
                        SendWarning($"设备【{Device.DeviceName}】本次采集耗时【{DeviceStatus.ReadMilliseconds}ms】已超过配置的最小周期【{Driver.MinPeriod}ms】，跳过休眠");
                    }
                }

                Log.Information($"设备【{Device.DeviceName}】停止采集");
            }
            catch (Exception ex)
            {
                SendError($"设备【{Device.DeviceName}】采集任务异常退出:【{ex.Message}】,堆栈:【{ex.StackTrace}】");
            }
        }, TaskCreationOptions.LongRunning);
    }

    /// <summary>
    ///     订阅模式
    /// </summary>
    private async Task<bool> SubscribeAndReadOnce()
    {
        // 订阅模式情况下-设备保持在线
        UpdateDeviceStatus(DeviceStatusTypeEnum.Good);
        // 订阅指定地址的值等于设置的值后在读取一次地址
        var wait = await Driver.WaitAsync(new WaitAsyncInput
        {
            DataType = _subDeviceVariable.DeviceVariableEx.DataType,
            Address = _subDeviceVariable.DeviceVariableEx.RegisterAddress
        });
        return wait.IsSuccess;
    }

    /// <summary>
    ///     初始化payload
    /// </summary>
    /// <param name="device"></param>
    private async Task InitializePayload(Device device)
    {
        // 未配置设备采集属性就循环持续等待
        while (!DeviceVariableSource.Any() && !_tokenSource.IsCancellationRequested)
            await Task.Delay(1000 * 3, _tokenSource.Token);
        // 初始化PayLoad
        _payLoad = new PayLoad { Values = new Dictionary<string, ParamValue>(), DeviceName = Device.DeviceName, DeviceId = device.Id, DriverName = device.Driver!.DriverName };
    }

    /// <summary>
    ///     加载数据
    /// </summary>
    private void Load()
    {
        // 将采集属性放到全局字典中，方便后续对数据做静默管理
        foreach (var deviceVariable in Device.DeviceVariable)
            try
            {
                DeviceVariableSource.TryAdd(deviceVariable.Identifier, deviceVariable);

                if (deviceVariable.ValueSource == ValueSourceEnum.Get)
                    DeviceVariables[deviceVariable.Identifier] = new DriverReturnValueModel
                    {
                        VariableStatus = VariableStatusTypeEnum.Bad,
                        Identifier = deviceVariable.Identifier,
                        DataType = deviceVariable.DeviceVariableEx.DataType,
                        Id = deviceVariable.Id
                    };

                #region 计算赋值-持久化

                // 加载持久化的值到内存中
                if (deviceVariable.ValueSource != ValueSourceEnum.Calculate || !deviceVariable.Persistence) continue;
                // 创建标准返回对象
                var ret = Create(deviceVariable, deviceVariable.DeviceVariableEx.DataType);
                var getObject = Engine.Share.Get<DriverReturnValueModel>("Persistence:" + Device.DeviceName + "." + deviceVariable.Identifier);
                if (getObject != null)
                    ret = (DriverReturnValueModel)getObject;
                // 更新设备变量的值
                DeviceVariables[deviceVariable.Identifier] = ret;
                // 脚本执行完成后放到底层存储
                Engine.Device.Load(Device.DeviceName, DeviceVariables);

                #endregion
            }
            catch (Exception e)
            {
                Log.Error("【静默管理】 " + e.Message);
            }

        Log.Information($"采集[{Device.DeviceName}]任务已启动！");

        // 初始化设备活动时间
        DeviceStatus.DeviceStatusChangeTime = DateTime.ShangHaiString();

        // 订阅模式
        if (Driver.ReadType == ReadTypeEnum.Subscription)
        {
            // 订阅属性对象
            var waitVariableIdentifier = Device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "VariableIdentifier")?.Value;
            if (waitVariableIdentifier != null && DeviceVariableSource.TryGetValue(waitVariableIdentifier, out var value))
                _subDeviceVariable = value;
        }

        // 检查是否使用批量读取
        ShouldUseBulkRead();
        // 将有标签的属性实例化下来
        Engine.Device.LoadTag(Device);
    }

    /// <summary>
    ///     是否使用批量读取
    /// </summary>
    /// <returns></returns>
    private void ShouldUseBulkRead()
    {
        var variables = DeviceVariableSource.Values;
        var isPlc = Device.Driver?.DriverType == DriverTypeEnum.Plc;
        var bulkRead = Device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "BulkRead")?.Value;
        bool bulkReadEnabled;
        try
        {
            bulkReadEnabled = (BoolEnum)Convert.ToInt32(bulkRead) == BoolEnum.True;
        }
        catch (Exception)
        {
            // try catch 为了兼容老版本项目，担心有遗漏
            bulkReadEnabled = bulkRead == BoolEnum.True.ToString();
        }

        _bulkReadEnabled = variables.Count(v => v.ValueSource == ValueSourceEnum.Get) > 1 && isPlc && bulkReadEnabled;
    }

    /// <summary>
    ///     发送采集数据
    /// </summary>
    private async Task SendDataToEventBusAsync(PayLoad payLoad)
    {
        if (payLoad.Values.Any())
            await MessageCenter.PublishAsync(EventConst.SendDeviceData, payLoad);
    }

    /// <summary>
    ///     udp协议设备是否是首次连接,避免首次等待
    /// </summary>
    private bool _firstConnect = true;

    /// <summary>
    ///     连接超时次数
    /// </summary>
    private short _timeOutCount;

    /// <summary>
    ///     设备连接
    /// </summary>
    /// <returns></returns>
    private async Task<bool> DeviceConnect()
    {
        try
        {
            if (Driver.IsConnected)
                return true;
            DeviceStatus.ReConnCount++;
            // 强制手动释放一次
            Driver.Close();
            // 串口设备和UDP设备不用考虑重新连接
            if (Device.Driver?.ConnectType is ConnectTypeEnum.SerialPort or ConnectTypeEnum.Udp && !_firstConnect)
                return true;

            // 重新连接设备
            _connect = Driver.Connect();
            _firstConnect = false;
            // 本次连接是否成功
            if (Driver.IsConnected)
            {
                // 连接成功重置超时次数
                _timeOutCount = 0;
                DeviceStatus.LastTime = DateTime.NowString();
                // 错误清空
                DeviceStatus.ErrorMessage = "";
                // 推给事件总线
                PublishDeviceEvent("info", _connect.Message, "连接成功");
                await Task.Delay(1000 * Driver.WaitTime, _tokenSource.Token);
            }
            else
            {
                // 超时次数累计
                _timeOutCount++;
                // 设备断开连接
                PublishDeviceEvent("error", _connect.Message, "断开连接");
                Log.Warning($"时间:【{DateTime.ShangHai()}】,设备:【{Device.DeviceName}】,未成功连接,【{Driver.ReConnTime}】/秒后重新尝试连接！");
                SendWarning($"设备:【{Device.DeviceName}】,未成功连接,【{Driver.ReConnTime}】/秒后重新尝试连接！");
                DeviceStatus.ErrorMessage = _connect?.Message ?? "";
                await Task.Delay(1000 * Driver.ReConnTime, _tokenSource.Token);
                // 设置过超时次数
                if (Driver.TimeOutCount > 0 && _timeOutCount >= Driver.TimeOutCount)
                {
                    PublishDeviceEvent("warning", $"设备连续超时:【{_timeOutCount}】次,进入退避时间", "退避时间");
                    // 进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询报文里
                    await Task.Delay(1000 * 60 * Driver.BackoffTime, _tokenSource.Token);
                    // 超时次数重新计数
                    _timeOutCount = 0;
                }

                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            SendError($"【设备连接】 Error:【{ex.Message}】");
            return false;
        }
    }

    /// <summary>
    ///     读取静态属性
    /// </summary>
    /// <returns></returns>
    private Task ReadStatic(List<DeviceVariable> staticDeviceVariable)
    {
        CloseDevice();
        foreach (var variable in staticDeviceVariable)
            try
            {
                var ret = new DriverReturnValueModel
                {
                    Id = variable.Id,
                    Identifier = variable.Identifier,
                    Value = variable.DefaultValue ?? "",
                    DataType = DataTypeEnum.String,
                    ReadTime = DateTime.TimeStamp(),
                    VariableStatus = VariableStatusTypeEnum.Good
                };

                // 处理属性
                _ = SetVariableValue(variable, ret);
            }
            catch (Exception e)
            {
                SendError($"【手动写值】 标识符:【{variable.Identifier}】,错误信息:【{e.Message}】");
            }

        return Task.CompletedTask;
    }

    /// <summary>
    ///     新增PayLoad
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="ret"></param>
    /// <param name="variable"></param>
    private void PayLoadMap(string identifier, DriverReturnValueModel ret, DeviceVariable variable)
    {
        var payload = ret.Adapt<ParamValue>();
        payload.TransitionType = variable.TransitionType;
        payload.SendType = variable.SendType;
        payload.DeviceVariableName = variable.Name;
        payload.Custom = variable.Custom;
        payload.ValueSource = variable.ValueSource;
        payload.Release = variable.Release;
        // 更新payload
        _payLoad.Values[identifier] = payload;
    }

    /// <summary>
    ///     单个读取属性
    /// </summary>
    /// <returns></returns>
    private async Task ReadGet(List<DeviceVariable> deviceVariableSource)
    {
        // 检查设备连接状态
        CloseDevice();
        foreach (var variable in deviceVariableSource)
            try
            {
                // 设备是非串口设备且未连接状态退出
                if (!Driver.IsConnected && Device.Driver.ConnectType != ConnectTypeEnum.SerialPort)
                    return;
                // 验证是否周期采集
                if (variable.Period > 0 && DeviceVariables.TryGetValue(variable.Identifier, out var deviceVariable))
                    // 属性采集周期 当前时间-上次读取时间 >= 配置周期时间  未达到时间就不采集当前属性
                    if (DateTime.TimeStamp() - deviceVariable.ReadTime < variable.Period)
                        return;
                // 报文间隔:发要查询报文后延时设定时间再发下一条报文（针对低速设备）
                if (Driver.MessageInterval > 0)
                    await Task.Delay(Driver.MessageInterval, _tokenSource.Token);
                var ret = new DriverReturnValueModel();
                // 定义请求方法参数对象
                var readVal = new DriverAddressIoArgModel(variable.Id, variable.DeviceVariableEx.RegisterAddress, variable.DeviceVariableEx.Length, variable.DeviceVariableEx.DataType,
                    encoding: variable.DeviceVariableEx.Encoding);
                // 反射调用方法名称
                var method = Methods.FirstOrDefault(x => x.Name == variable.DeviceVariableEx.Method);
                if (method == null)
                {
                    // 方法不存在
                    ret.VariableStatus = VariableStatusTypeEnum.MethodError;
                }
                else
                {
                    // CNC设备读取R变量
                    const string readRMethodName = "R";
                    // CNC设备读取宏变量
                    const string readMacroMethodName = "Macro";
                    // 注塑机协议无需参数
                    var igonreList = new List<string> { "PORCHESON_Ps660Bm.dll", "TechMationAk.dll",
                    "HongXunAkDouble.dll",
                     "PORCHESON_Ps660Am.dll", "KeQiang_T6H3.dll", "KeQiang_T6F3.dll",
                     "WarpingMachine.dll", "TechMation5521.dll" };
                    // 注：CNC类协议（cnc读R变量地址也需要参数）和注塑机协议底层采集点位都是固定的，所以无需参数，而普通的plc设备是通过地址读取，故需要传参
                    var parameters = Device.Driver.DriverType == DriverTypeEnum.Cnc
                        // cnc协议如果是读宏变量或者r变量等需要传参，普通参数则无
                        ? method.Name is readRMethodName or readMacroMethodName ? new object[] { readVal } : new object[] { }
                        // plc协议中是否是一些特殊的协议，如注塑机等无需地址
                        : igonreList.Contains(Device.Driver.FileName)
                            ? new object[] { }
                            : new object[] { readVal };
                    // 反射调用方法
                    ret = await ((Task<DriverReturnValueModel>)method.Invoke(Driver, parameters))!;
                }

                // 处理字符串保留长度
                ret = SetVariableValue(variable, ret);
                // 打印读取失败的日志
                if (ret.ErrorCode != 0 && ret.Message.IsNotNull())
                {
                    SendError(Console.Read(variable.Identifier, variable.DeviceVariableEx.RegisterAddress, VariableStatusTypeEnum.Bad, ret.ErrorCode, ret.Message, "读取失败"));
                }
                else
                {
                    var msg = $" 标识符:【{variable.Identifier}】,{(variable.DeviceVariableEx.RegisterAddress.IsNotNull() ? "地址【" + variable.DeviceVariableEx.RegisterAddress + "】," : "")}" +
                              $" 读取状态:【{EnumUtil.GetEnumDesc(ret.VariableStatus)}】,返回值:【{ret.Value}】";
                    if (ret.VariableStatus != VariableStatusTypeEnum.Good)
                        SendError(msg);
                    else
                        SendInfo(msg);
                }
            }
            catch (Exception ex)
            {
                // 推送错误日志到Socket
                SendError(Console.Read(variable.Identifier, variable.DeviceVariableEx.RegisterAddress, VariableStatusTypeEnum.Bad, 0, ex.Message));
                // 错误次数累加
                DeviceStatus.ErrorCount++;
            }

        // 部分协议读取完毕，清空本次采集内容
        if (_driverClear.Contains(Device.Driver.DriverName))
        {
            // 默认清除方法
            var method = Methods.FirstOrDefault(x => x.Name == "Clear");
            if (method == null)
                return;
            // 反射调用
            await ((Task)method.Invoke(Driver, new object[] { }))!;
        }
    }

    /// <summary>
    ///     批量读取属性
    /// </summary>
    /// <returns></returns>
    private async Task BatchRead(List<DeviceVariable> getDeviceVariables)
    {
        var batchReadStopwatch = Stopwatch.StartNew();
        try
        {
            CloseDevice();

            // 缓存方法查找结果，避免重复LINQ查询
            var initMethod = _cachedInitMethod ??= Methods.FirstOrDefault(x => x.Name == "InitGroup");
            var batchReadMethod = _cachedBatchReadMethod ??= Methods.FirstOrDefault(x => x.Name == "BatchRead");

            // 直接读取变量
            if (!_bulkReadInit || Restart)
            {
                var initStopwatch = Stopwatch.StartNew();
                // 批量读取地址,初始化地址分组,确保只初始化一次
                if (initMethod != null)
                {
                    // 优化：预分配容量，减少内存重新分配
                    var reqModels = new List<DriverAddressIoArgModel>(getDeviceVariables.Count);

                    // 使用for循环替代LINQ，提升性能
                    for (int i = 0; i < getDeviceVariables.Count; i++)
                    {
                        var variable = getDeviceVariables[i];
                        reqModels.Add(new DriverAddressIoArgModel(
                            variable.Id,
                            variable.DeviceVariableEx.RegisterAddress,
                            variable.Length,
                            variable.DeviceVariableEx.DataType,
                            method: variable.DeviceVariableEx.Method,
                            encoding: variable.DeviceVariableEx.Encoding));
                    }

                    // 将地址分组,并且调用初始化
                    var groupVariables = reqModels.GroupBy(model => model.Method).ToList();
                    _bulkReadInit = (bool)initMethod.Invoke(Driver, new object[] { groupVariables })!;
                }
                initStopwatch.Stop();
                SendInfo($"【批量读取】初始化耗时: {initStopwatch.ElapsedMilliseconds}ms");
            }

            if (batchReadMethod == null)
            {
                SendError("当前设备暂不支持批量读取！");
                return;
            }

            // 添加批量读取调用的时间测量
            var readStopwatch = Stopwatch.StartNew();
            var retList = await ((Task<List<DriverReturnValueModel>>)batchReadMethod.Invoke(Driver, new object[] { }))!;
            readStopwatch.Stop();
            SendInfo($"【批量读取】Driver.BatchRead调用耗时: {readStopwatch.ElapsedMilliseconds}ms，返回{retList.Count}个结果");

            // 优化：使用Dictionary提升查找性能
            var retDict = retList.ToDictionary(r => r.Id, r => r);
            var readBadDeviceVariableList = new List<DeviceVariable>();

            // 优化字符串构建，减少内存分配
            var stringBuilder = new StringBuilder(256);

            var processStopwatch = Stopwatch.StartNew();
            foreach (var variable in getDeviceVariables)
                try
                {
                    // 批量读取失败的地址,执行单次读取
                    if (!retDict.TryGetValue(variable.Id, out var ret))
                    {
                        readBadDeviceVariableList.Add(variable);
                        continue;
                    }

                    // 处理属性
                    ret = SetVariableValue(variable, ret);

                    // 优化字符串构建
                    stringBuilder.Clear();
                    stringBuilder.Append("【批量读取】 标识符:【").Append(variable.Identifier)
                                .Append("】,地址:【").Append(variable.DeviceVariableEx.RegisterAddress)
                                .Append("】,读取状态:【").Append(EnumUtil.GetEnumDesc(ret.VariableStatus))
                                .Append("】,返回值:【").Append(ret.Value).Append("】");

                    if (ret.VariableStatus != VariableStatusTypeEnum.Good)
                    {
                        stringBuilder.Append("错误码:【").Append(ret.ErrorCode)
                                    .Append("】,msg:【").Append(ret.Message).Append("】");
                        SendError(stringBuilder.ToString());
                    }
                    else
                    {
                        SendInfo(stringBuilder.ToString());
                    }
                }
                catch (Exception ex)
                {
                    SendError(Console.Read(variable.Identifier, variable.DeviceVariableEx.RegisterAddress, VariableStatusTypeEnum.Bad, 0, ex.Message));
                    DeviceStatus.ErrorCount++;
                }

            processStopwatch.Stop();
            SendInfo($"【批量读取】结果处理耗时: {processStopwatch.ElapsedMilliseconds}ms");

            if (readBadDeviceVariableList.Count > 0)
            {
                var fallbackStopwatch = Stopwatch.StartNew();
                await ReadGet(readBadDeviceVariableList);
                fallbackStopwatch.Stop();
                SendInfo($"【批量读取】失败地址单独读取耗时: {fallbackStopwatch.ElapsedMilliseconds}ms，处理{readBadDeviceVariableList.Count}个地址");
            }
        }
        catch (Exception ex)
        {
            SendError(Console.Read("", "", VariableStatusTypeEnum.Bad, 0, ex.Message, "批量读取"));
        }
        finally
        {
            batchReadStopwatch.Stop();
            SendInfo($"【批量读取】总耗时: {batchReadStopwatch.ElapsedMilliseconds}ms");
        }
    }

    /// <summary>
    ///     设备关机
    /// </summary>
    private void CloseDevice()
    {
        if (Driver.IsConnected) return;
        UpdateDeviceStatus(DeviceStatusTypeEnum.Bad);
        _payLoad.DeviceStatus = DeviceStatusTypeEnum.Bad;
    }

    /// <summary>
    ///     读取完毕后执行脚本
    /// </summary>
    private void RunScript(List<DeviceVariable> calculateDeviceVariable)
    {
        _stopwatch.Restart();
        // 过滤掉空脚本-按优先级执行
        foreach (var variable in calculateDeviceVariable)
            try
            {
                // 设置周期时间的情况在进行时间判断 => 防止时间被拉回过去时 脚本不执行问题  --------反馈人 yzj
                if (variable.Period > 0)
                    if (DeviceVariables.TryGetValue(variable.Identifier, out var deviceVariable))
                        // 属性采集周期 当前时间-上次读取时间 >= 配置周期时间  未达到时间就不采集当前属性
                        if (DateTime.TimeStamp() - deviceVariable.ReadTime < variable.Period)
                            continue;

                // 创建标准返回对象
                var ret = Create(variable, variable.DeviceVariableEx.DataType);
                try
                {
                    // 当前设备的名称
                    if (variable.Script != null && variable.Script.Contains("${this.DeviceName}"))
                        variable.Script = variable.Script.Replace("${this.DeviceName}", Device.DeviceName);

                    ret.Value = Engine.Engine.Evaluate(variable.Script ?? "", new ScriptParsingOptions { Tolerant = true, AllowReturnOutsideFunction = true }).ToObject();
                    // 处理属性
                    ret = SetVariableValue(variable, ret);
                    // 脚本执行完成后 -- 实时放到底层存储
                    Engine.Device.Load(Device.DeviceName, variable.Identifier, ret);
                    SendInfo($"【计算赋值】 标识符:【{variable.Identifier}】, 读取状态状态:【Good】,返回值:【{ret.Value}】");
                    // 持久化存储
                    if (variable.Persistence)
                        Engine.Share.Save("Persistence:" + Device.DeviceName + "." + variable.Identifier, ret);
                }
                catch (Exception ex)
                {
                    SendError($"【计算赋值】 标识符:【{variable.Identifier}】 Error:【{ex.Message}】");
                    ret.Message = ex.Message;
                    ret.VariableStatus = VariableStatusTypeEnum.ScriptError;
                }
            }
            catch (Exception ex)
            {
                SendError($"【计算赋值】 标识符:【{variable.Identifier}】 Error:【{ex.Message}】");
            }

        _stopwatch.Stop();
        // 脚本执行时间
        DeviceStatus.ScriptMilliseconds = _stopwatch.ElapsedMilliseconds;
        // 采集周期累计时间 = 采集时间+脚本执行时间
        DeviceStatus.ReadMilliseconds += DeviceStatus.ScriptMilliseconds;
    }

    /// <summary>
    ///     实时处理读取完毕的属性
    /// </summary>
    private DriverReturnValueModel SetVariableValue(DeviceVariable variable, DriverReturnValueModel ret)
    {
        // 上次数据
        if (DeviceVariables.ContainsKey(variable.Identifier))
        {
            // // 读取失败就将值改成上一次成功的值
            // if (ret.VariableStatus != VariableStatusTypeEnum.Good)
            //     ret.Value = DeviceVariables[variable.Identifier]?.Value;
            // 上一次的读取时间
            ret.CookieTime = DeviceVariables[variable.Identifier].ReadTime;
            // 上一次的读取值
            ret.CookieValue = DeviceVariables[variable.Identifier]?.Value;
        }
        ret.Id = variable.Id;
        // 设置标识符
        ret.Identifier = variable.Identifier;
        // 属性名称
        ret.Name = variable.Name;

        Add(variable, ret);
        return ret;
    }

    /// <summary>
    ///     数据添加到计算变量中
    /// </summary>
    /// <param name="ret"></param>
    /// <param name="variable"></param>
    private void SetVariable(DriverReturnValueModel ret, DeviceVariable variable)
    {
        try
        {
            var identifier = ret.Identifier;
            var cookieValue = ret.CookieValue.GetJsonElementValue()?.ToString();
            // 将变量更新到表达式变量中
            if (DeviceVariableSource[ret.Identifier]?.ValueSource == ValueSourceEnum.Get)
                switch (ret.DataType)
                {
                    case DataTypeEnum.Bit:
                        _interpreter.SetVariable(identifier, new
                        {
                            Value = Convert.ToInt32(ret.Value),
                            CookieValue = Convert.ToInt32(cookieValue),
                            ReadTime = Convert.ToInt64(ret.ReadTime),
                            CookieTime = Convert.ToInt64(ret.CookieTime)
                        });
                        break;
                    case DataTypeEnum.Bool:
                        _interpreter.SetVariable(identifier, new
                        {
                            Value = Convert.ToBoolean(ret.Value),
                            CookieValue = cookieValue != null && !string.IsNullOrEmpty(cookieValue) && Convert.ToBoolean(cookieValue),
                            ReadTime = Convert.ToInt64(ret.ReadTime),
                            CookieTime = Convert.ToInt64(ret.CookieTime)
                        });
                        break;
                    case DataTypeEnum.Uint16:
                    case DataTypeEnum.Int16:
                    case DataTypeEnum.Uint32:
                    case DataTypeEnum.Int32:
                    case DataTypeEnum.Float:
                    case DataTypeEnum.Uint64:
                    case DataTypeEnum.Int64:
                    case DataTypeEnum.Double:
                        if (double.IsNaN(Convert.ToDouble(ret.Value)))
                            ret.Value = "0";
                        _interpreter.SetVariable(identifier, new
                        {
                            Value = Convert.ToDouble(ret.Value),
                            CookieValue = Convert.ToDouble(cookieValue),
                            ReadTime = Convert.ToInt64(ret.ReadTime),
                            CookieTime = Convert.ToInt64(ret.CookieTime)
                        });
                        break;
                    case DataTypeEnum.String:
                    case DataTypeEnum.Bcd:
                    case DataTypeEnum.Byte:
                    default:
                        _interpreter.SetVariable(identifier, new
                        {
                            ret.Value,
                            CookieValue = cookieValue,
                            ReadTime = Convert.ToInt64(ret.ReadTime),
                            CookieTime = Convert.ToInt64(ret.CookieTime)
                        });
                        break;
                }
            else
                switch (variable.TransitionType)
                {
                    case TransPondDataTypeEnum.Bool:
                        _interpreter.SetVariable(identifier, new
                        {
                            Value = Convert.ToBoolean(ret.Value),
                            CookieValue = cookieValue != null && !string.IsNullOrEmpty(cookieValue.ToString()) && Convert.ToBoolean(cookieValue),
                            ReadTime = Convert.ToInt64(ret.ReadTime),
                            CookieTime = Convert.ToInt64(ret.CookieTime)
                        });
                        break;
                    case TransPondDataTypeEnum.Int:
                    case TransPondDataTypeEnum.Double:
                        ret.Value ??= 0;
                        if (double.IsNaN(Convert.ToDouble(ret.Value)))
                            ret.Value = "0";
                        _interpreter.SetVariable(identifier, new
                        {
                            Value = Convert.ToDouble(ret.Value),
                            CookieValue = ret.CookieValue != null ? Convert.ToDouble(cookieValue) : 0,
                            ReadTime = Convert.ToInt64(ret.ReadTime),
                            CookieTime = Convert.ToInt64(ret.CookieTime)
                        });
                        break;
                    case TransPondDataTypeEnum.String:
                    case TransPondDataTypeEnum.Bytes:
                    default:
                        _interpreter.SetVariable(identifier, new
                        {
                            Value = ret.Value?.ToString(),
                            CookieValue = ret.CookieValue != null ? cookieValue : "",
                            ReadTime = Convert.ToInt64(ret.ReadTime),
                            CookieTime = Convert.ToInt64(ret.CookieTime)
                        });
                        break;
                }
        }
        catch (Exception ex)
        {
            _interpreter.UnsetVariable(ret.Identifier);
            SendError($"设备:【{Device.DeviceName}】，【{ret.Identifier}】，【{ret.Value}】 添加到变量异常:【{ex.Message}】");
        }
    }

    /// <summary>
    ///     将数据实时更新
    /// </summary>
    /// <param name="variable"></param>
    /// <param name="ret"></param>
    private void Add(DeviceVariable variable, DriverReturnValueModel ret)
    {
        try
        {
            // 如果返回值不为空
            if (ret.Value != null)
            {
                // 首先处理数据类型是字符串，且包含无效字符的情况
                if (ret.DataType is DataTypeEnum.String && (ret.Value.ToString()!.Contains("\u0000") || ret.Value.ToString()!.Contains("\uFFFD")))
                    ret.Value = ret.Value.ToString()?.Replace("\u0000", "").Replace("\uFFFD", "") ?? "";

                // 处理double和float类型的nan情况
                if (ret.DataType is DataTypeEnum.Double && double.IsNaN(Convert.ToDouble(ret.Value)))
                    ret.Value = "0";
                if (ret.DataType is DataTypeEnum.Float && float.IsNaN(Convert.ToSingle(ret.Value)))
                    ret.Value = "0";

                // 先处理表达式计算
                if (DeviceVariableSource.Values.Any(f => f.Identifier == variable.Identifier && !string.IsNullOrWhiteSpace(f.Expressions?.Trim())) && ret.ErrorCode == 0)
                    try
                    {
                        // raw代表自己本身值
                        _interpreter.SetVariable("raw", ret.Value);
                        var original = ret.Value;
                        // 表达式计算后的值
                        ret.Value = _interpreter.Eval(variable.Expressions);

                        // 发送表达式计算结果
                        SendInfo($"【表达式计算】 标识符:【{variable.Identifier}】,原始值:【{original}】,计算值:【{ret.Value}】");
                    }
                    catch (Exception ex)
                    {
                        // 表达式计算出错，设置变量状态
                        ret.Message = ex.Message;
                        ret.VariableStatus = VariableStatusTypeEnum.ExpressionError;
                        // 发送表达式执行错误信息
                        SendError($"【表达式计算】 标识:【{variable.Identifier}】,表达式:【{variable.Expressions}】 Error:【{ex.Message}】");
                    }
                // 处理长度问题
                ret.Value = variable.TransitionType switch
                {
                    TransPondDataTypeEnum.Double when variable.Length > 0 =>
                        Math.Round(double.IsNaN(Convert.ToDouble(ret.Value))
                        ? Convert.ToDecimal(0)
                        : Convert.ToDecimal(ret.Value),
                        variable.Length),
                    TransPondDataTypeEnum.Int when double.IsNaN(Convert.ToDouble(ret.Value)) => 0,
                    TransPondDataTypeEnum.String when ret.Value != null && ret.Value.ToString()?.Length > variable.Length => ret.Value.ToString()?.Substring(0, variable.Length),
                    _ => ret.Value
                };

                // 最后设置变量
                SetVariable(ret, variable);
            }

            // 设置 Payload 的值
            PayLoadMap(variable.Identifier, ret, variable);

            // 更新设备变量的值
            DeviceVariables[variable.Identifier] = ret;
        }
        catch (Exception ex)
        {
            // 发送采集数据处理异常信息
            SendError($"设备:【{Device.DeviceName}】,采集数据处理异常:【{ex.Message}】");
        }
    }

    /// <summary>
    ///     设备属性最后一次发送时间
    /// </summary>
    private readonly Dictionary<string, long> _lastSendTime = new();

    /// <summary>
    ///     过滤设置了最大值最小值的属性
    /// </summary>
    private PayLoad Filter()
    {
        try
        {
            var payLoad = new PayLoad
            {
                DriverName = _payLoad.DriverName,
                DeviceId = _payLoad.DeviceId,
                DeviceName = _payLoad.DeviceName,
                DeviceStatus = _payLoad.DeviceStatus,
                Ts = _payLoad.Ts
            };
            var filterValues = new Dictionary<string, ParamValue>();
            foreach (var (key, variable) in _payLoad.Values)
                try
                {
                    if (variable.VariableStatus != VariableStatusTypeEnum.Good)
                        continue;
                    try
                    {
                        // int or bool类型允许设置自定义映射名称
                        if (variable.TransitionType is TransPondDataTypeEnum.Int or TransPondDataTypeEnum.Bool)
                            if (variable.Custom.IsNotNull())
                            {
                                var custom = variable.Custom.ToObject<Dictionary<string, object>>();
                                if (custom.TryGetValue(variable.Value.ToString(CultureInfo.InvariantCulture), out var value1))
                                    variable.MappingAlias = value1.ToString();
                            }

                        switch (variable.TransitionType)
                        {
                            case TransPondDataTypeEnum.Bool:
                                // 强制优化0-1转bool
                                if (variable.Value is "0" or "1")
                                    variable.Value = variable.Value == "0" ? false.ToString() : true.ToString();
                                else
                                    variable.Value = Convert.ToBoolean(variable.Value).ToString();

                                // 修改过值后需要对内存对象的值重新赋值
                                DeviceVariables[key].Value = Convert.ToBoolean(variable.Value);
                                break;
                            case TransPondDataTypeEnum.Int:
                                if (variable.Value != null)
                                    variable.Value = Convert.ToInt64(variable.Value).ToString();
                                break;
                            case TransPondDataTypeEnum.Double:
                                if (variable.Value != null)
                                    variable.Value = Convert.ToDouble(variable.Value).ToString(CultureInfo.InvariantCulture);
                                break;
                            case TransPondDataTypeEnum.Bytes:
                                variable.Value = Convert.ToByte(variable.Value).ToString();
                                break;
                            case TransPondDataTypeEnum.String:
                                variable.Value = variable.Value;
                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        variable.VariableStatus = VariableStatusTypeEnum.Convert;
                        SendError($"设备:【{Device.DeviceName}】,数据转换异常:【{ex.Message}】,原数据:【{variable.Value}】,转换类型:【{EnumUtil.GetEnumDesc(variable.TransitionType)}】");
                    }

                    if (variable.TransitionType is TransPondDataTypeEnum.Int or TransPondDataTypeEnum.Double)
                        if (DeviceVariableSource.TryGetValue(key, out var getVariable))
                            if (getVariable is { DeviceVariableFilter: not null })
                            {
                                // number类型的脚本返回如果返回null 就按照设置的逻辑处理
                                if (variable.Value == null || Convert.ToDouble(variable.Value) < getVariable.DeviceVariableFilter.Min)
                                {
                                    switch (getVariable.DeviceVariableFilter.MinFilterType)
                                    {
                                        case DeviceVariableFilterTypeEnum.Min:
                                            variable.Value = getVariable.DeviceVariableFilter.Min.ToString(CultureInfo.InvariantCulture);
                                            break;
                                        case DeviceVariableFilterTypeEnum.Set:
                                            variable.Value = getVariable.DeviceVariableFilter.SetMin.ToString(CultureInfo.InvariantCulture);
                                            break;
                                        case DeviceVariableFilterTypeEnum.Cookie:
                                            variable.Value = variable.CookieValue?.ToString() ?? null;
                                            break;
                                        case DeviceVariableFilterTypeEnum.Clear:
                                            continue;
                                    }

                                    if (DeviceVariables.TryGetValue(key, out var deviceVariable))
                                        if (deviceVariable != null)
                                            deviceVariable.Value = variable.Value;
                                }

                                // 超出最大值
                                if (getVariable.DeviceVariableFilter.Max != 0 && Convert.ToDouble(variable.Value) > getVariable.DeviceVariableFilter.Max)
                                {
                                    switch (getVariable.DeviceVariableFilter.MaxFilterType)
                                    {
                                        case DeviceVariableFilterTypeEnum.Max:
                                            variable.Value = getVariable.DeviceVariableFilter.Max.ToString(CultureInfo.InvariantCulture);
                                            break;
                                        case DeviceVariableFilterTypeEnum.Set:
                                            variable.Value = getVariable.DeviceVariableFilter.SetMax.ToString(CultureInfo.InvariantCulture);
                                            break;
                                        case DeviceVariableFilterTypeEnum.Cookie:
                                            variable.Value = variable.CookieValue.ToString();
                                            break;
                                        case DeviceVariableFilterTypeEnum.Clear:
                                            continue;
                                    }

                                    if (DeviceVariables.TryGetValue(key, out var deviceVariable))
                                        deviceVariable.Value = variable.Value;
                                }
                            }

                    // 根据实际设备属性配置
                    if (Device.DataCollectionReportingRule == DataCollectionReportingRuleEnum.ReportAccordingToActualAttributeConfiguration)
                        switch (variable.SendType)
                        {
                            case SendTypeEnum.Always:
                                filterValues.Add(key, variable);
                                break;
                            case SendTypeEnum.Never:
                                continue;
                            case SendTypeEnum.Changed:
                                {
                                    // 值是否改变,未改变根据归档周期判断是否强制归档
                                    if (variable.CookieValue?.ToString() == variable.Value)
                                        if (DeviceVariableSource.TryGetValue(key, out var getVariable))
                                        {
                                            if (getVariable == null)
                                                continue;
                                            // 设置归档时间
                                            if (getVariable.ArchiveTime > 0)
                                            {
                                                // 验证是否符合条件
                                                if (_lastSendTime.ContainsKey(getVariable.Identifier))
                                                {
                                                    var timestamp = DateTime.TimeStamp();
                                                    if (timestamp - _lastSendTime[getVariable.Identifier] >= getVariable.ArchiveTime)
                                                    {
                                                        _lastSendTime[getVariable.Identifier] = timestamp;
                                                        filterValues.Add(key, variable);
                                                    }

                                                    continue;
                                                }

                                                // 更新最后上报时间
                                                _lastSendTime[getVariable.Identifier] = DateTime.TimeStamp();
                                                filterValues.Add(key, variable);
                                            }
                                            else
                                            {
                                                continue;
                                            }
                                        }

                                    filterValues.Add(key, variable);
                                    break;
                                }
                        }
                    else
                        filterValues.Add(key, variable);
                }
                catch (Exception ex)
                {
                    SendError($"【属性过滤】 完整数据:{JSON.Serialize(variable)},Error:【{ex.Message}】");
                }

            payLoad.Values = filterValues;
            return payLoad;
        }
        catch (Exception ex)
        {
            SendError($"【属性过滤】 Error:【{ex.Message}】");
            return _payLoad;
        }
    }
    // 遗嘱消息
    private int testamentaryMessage = 0;
    /// <summary>
    ///     设备数据上报策略
    /// </summary>
    /// <param name="payLoad"></param>
    private async Task DataCollectionReportingRule(PayLoad payLoad)
    {
        // 写入时序库
        if ((MachineUtil.CurrentSystemEnvironment == CurrentSystemEnvironmentEnum.FengEdge2000
             || MachineUtil.CurrentSystemEnvironment == CurrentSystemEnvironmentEnum.Wr610
             || MachineUtil.CurrentSystemEnvironment == CurrentSystemEnvironmentEnum.TerUbuntu) && Device.Release)
            await _writeService.Write(payLoad);

        switch (Device.DataCollectionReportingRule)
        {
            case DataCollectionReportingRuleEnum.ReportAccordingToActualAttributeConfiguration:
                {
                    await SendDataToEventBusAsync(payLoad);
                    break;
                }
            case DataCollectionReportingRuleEnum.ReportAccordingToActualAttributeConfigurationTestamentaryMessage:
                {
                    // 设备在线
                    if (Driver.IsConnected)
                    {
                        await SendDataToEventBusAsync(payLoad);
                        testamentaryMessage = 0;
                    }
                    else
                    {
                        if (Device.Driver.ConnectType == ConnectTypeEnum.NetWork)
                        {
                            if (testamentaryMessage != 10)
                            {
                                await SendDataToEventBusAsync(payLoad);
                                testamentaryMessage = 10;
                            }
                        }
                        else
                        {
                            if (testamentaryMessage != 10 && testamentaryMessage != 5)
                            {
                                await SendDataToEventBusAsync(payLoad);
                                testamentaryMessage = 5;
                            }
                            else if (testamentaryMessage != 10)
                            {
                                await SendDataToEventBusAsync(payLoad);
                                testamentaryMessage = 10;
                            }
                        }
                    }
                    break;
                }
            case DataCollectionReportingRuleEnum.ReportWholeGroupDataWhenAnyAttributeChanges:
                {
                    // 是否有数据变化
                    if (payLoad.Values.Any(a => a.Value.CookieValue?.ToString() != a.Value.Value))
                    {
                        foreach (var (key, value) in payLoad.Values.Where(a => a.Value.CookieValue?.ToString() != a.Value.Value))
                            SendWarning($"【任一属性变化上报】 属性值发生改变,标识符:【{key}】,value:{value.Value},cookieValue:{value.CookieValue}");
                        await SendDataToEventBusAsync(payLoad);
                    }
                    else
                    {
                        // 在数据都没有变化时，检查是否有系统属性，系统属性都需要上报
                        // 查出系统属性
                        var systemDeviceVariableList = Device.DeviceVariable.Where(w => w.IsSystem).ToList();
                        if (systemDeviceVariableList.Any())
                        {
                            // 系统属性标识
                            var ids = systemDeviceVariableList.Select(s => s.Identifier).ToList();
                            // 使用LINQ过滤字典中匹配的数据
                            payLoad.Values = payLoad.Values.Where(w => ids.Contains(w.Key))
                                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                            // 发送匹配出来的系统属性
                            await SendDataToEventBusAsync(payLoad);
                        }
                    }

                    break;
                }
            case DataCollectionReportingRuleEnum.ReportOnlyChangedAttributes:
                {
                    // 定义一个匹配条件,默认以当前值 ！= 上一次的值
                    bool FilterCondition(KeyValuePair<string, ParamValue> kvp)
                    {
                        return kvp.Value.CookieValue?.ToString() != kvp.Value.Value;
                    }

                    // 过滤字典中匹配的数据
                    payLoad.Values = payLoad.Values.Where(FilterCondition).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                    await SendDataToEventBusAsync(payLoad);
                    break;
                }
            case DataCollectionReportingRuleEnum.DeviceDoesNotReportData:
                break;
        }
    }

    #region 设备是否离线

    private const int AddressErrorCode = 10000; // 地址错误码
    private const string DeviceEvent = EventConst.DeviceEvent; // 设备断开连接事件名
    private const DriverTypeEnum SerialPortDriverType = DriverTypeEnum.Plc;

    /// <summary>
    ///     修改设备状态
    /// </summary>
    /// <param name="status"></param>
    /// <param name="message"></param>
    private void UpdateDeviceStatus(DeviceStatusTypeEnum status, string message = null)
    {
        // 状态发生改变
        if (DeviceStatus.DeviceStatus != status)
        {
            // 成功 or 部分成功
            if (DeviceStatus.DeviceStatus is DeviceStatusTypeEnum.PartGood or DeviceStatusTypeEnum.Good)
            {
                // 原状态是失败
                if (status == DeviceStatusTypeEnum.Bad)
                    // 记录状态变化时间
                    DeviceStatus.DeviceStatusChangeTime = DateTime.ShangHaiString();
            }
            else
            {
                // 记录状态变化时间
                DeviceStatus.DeviceStatusChangeTime = DateTime.ShangHaiString();
            }

            // 改变状态
            DeviceStatus.DeviceStatus = status;
        }

        // 改变payload状态
        _payLoad.DeviceStatus = status;
        if (message == null) return;
        // 记录错误信息
        DeviceStatus.ErrorMessage = message;
        // 推送错误信息
        SendInfo(message);
    }

    /// <summary>
    ///     推送设备相关消息到站内信中
    /// </summary>
    /// <param name="eventType"></param>
    /// <param name="description"></param>
    /// <param name="title"></param>
    private void PublishDeviceEvent(string eventType, string description, string title)
    {
        _ = MessageCenter.PublishAsync(DeviceEvent, new DeviceEventLog
        {
            Id = YitIdHelper.NextId(),
            CreatedTime = DateTime.Now(),
            EventType = eventType,
            DeviceId = Device.Id,
            Description = description,
            Title = title
        }, _tokenSource.Token);
    }

    /// <summary>
    ///     发送错误信息
    /// </summary>
    /// <param name="message"></param>
    private void SendError(string message)
    {
        SendMessage(message, "error");
    }

    /// <summary>
    ///     发送普通信息
    /// </summary>
    /// <param name="message"></param>
    private void SendInfo(string message)
    {
        SendMessage(message, "info");
    }

    /// <summary>
    ///     发送警告信息
    /// </summary>
    /// <param name="message"></param>
    private void SendWarning(string message)
    {
        SendMessage(message, "warning");
    }

    /// <summary>
    ///     socket推送消息
    /// </summary>
    /// <param name="message"></param>
    /// <param name="info"></param>
    private void SendMessage(string message, string info)
    {
        if (info == "error")
            message = $"<span style=\"color:#ff4d4f;\">{message}</span>";
        else if (info == "warning")
            message = $"<span style=\"color:#F1C40F;\">{message}</span>";
        else if (info == "debug")
            message = $"<span style=\"color:#1890ff;\">{message}</span>";
        // 推送下行消息
        _ = Socket.DeviceConsole(message, Device.Id);
    }

    /// <summary>
    ///     设备是否离线
    /// </summary>
    private async Task CheckDeviceStatusAsync()
    {
        // CNC类设备根据实际连接状况决定在线状态
        if (Device.Driver!.ConnectType == ConnectTypeEnum.NetWork || Device.Driver.DriverType == DriverTypeEnum.Cnc)
            return;
        // 非直接读取的属性数量
        var availableVariablesCount = DeviceVariableSource.Values.Count(w => w.Enable && w.ValueSource != ValueSourceEnum.Get);
        // 特殊场景一: 地址全部配置错误,错误码10000的情况，plc设备目前是不知道是否正常连接
        var addressErrorCount = DeviceVariables.Count(w => w.Value.ErrorCode == AddressErrorCode);
        // 遇到地址全部错误 直接返回,不对状态做任何处理
        if (addressErrorCount == DeviceVariables.Count - availableVariablesCount) return;

        //  读取状态<0的属性数量 (errorCode < 0代表关机)
        var closeCount = DeviceVariables.Count(w => w.Value.ErrorCode < 0);
        //  读取的数量 = 配置数量 - （静态和脚本）数量 - 地址配置错误数量
        var readCount = DeviceVariables.Count - availableVariablesCount - addressErrorCount;
        // 非全部失败的情况处理
        if (closeCount != readCount)
        {
            // 如果不是plc类型设备的串口/udp协议,就直接返回,不需要在此处处理设备连接状态
            if (Device.Driver is { ConnectType: ConnectTypeEnum.NetWork, DriverType: SerialPortDriverType })
                return;
            // 重置超时次数
            _timeOutCount = 0;
            // 连接成功
            UpdateDeviceStatus(DeviceStatusTypeEnum.Good);
            Driver.IsConnected = true;
            return;
        }

        //  全部失败，表示关机
        Driver.IsConnected = false;
        // 设备关机
        UpdateDeviceStatus(DeviceStatusTypeEnum.Bad);
        if (Device.Driver.ConnectType is ConnectTypeEnum.SerialPort or ConnectTypeEnum.Udp && Device.Driver.DriverType == SerialPortDriverType)
        {
            // 超时次数累加
            _timeOutCount++;
            // 处理设备连接状态
            PublishDeviceEvent("error", Device.Driver.ConnectType is ConnectTypeEnum.SerialPort ? "连接失败" : _connect.Message, "设备断开连接");
            // 断开连接
            UpdateDeviceStatus(DeviceStatusTypeEnum.Bad, _connect.Message);
            // 推送断开连接日志
            SendWarning($"时间:【{DateTime.ShangHai()}】,设备:【{Device.DeviceName}】,未成功连接,【{Driver.ReConnTime}】/秒后重新尝试连接！");
            // 重连周期
            await Task.Delay(1000 * Driver.ReConnTime, _tokenSource.Token);
            // 超时次数 -退避逻辑
            if (Driver.TimeOutCount > 0 && _timeOutCount >= Driver.TimeOutCount)
            {
                // 输入退避日志
                PublishDeviceEvent("warning", $"设备连续超时:【{_timeOutCount}】次,进入退避时间", "退避时间");
                // 进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询报文里
                await Task.Delay(1000 * 60 * Driver.BackoffTime, _tokenSource.Token);
                // 超时次数重新计数
                _timeOutCount = 0;
            }
        }
    }

    #endregion

    #region 将推送的设备状态修改

    /// <summary>
    ///     设备状态修改
    /// </summary>
    private void UpdateDeviceStatusAccordingToConnectionAndVariableStatus()
    {
        // 设备未连接
        if (!Driver.IsConnected)
        {
            // 读取失败
            UpdateDeviceStatus(DeviceStatusTypeEnum.Bad);
            return;
        }

        // 检查是否有部分读取成功
        if (HasPartialGoodVariableStatus())
        {
            // 部分读取成功
            UpdateDeviceStatus(DeviceStatusTypeEnum.PartGood);
            return;
        }

        // 修改状态
        UpdateDeviceStatus(!ContainsGoodVariableStatus() ? DeviceStatusTypeEnum.Bad : DeviceStatusTypeEnum.Good);
    }

    /// <summary>
    ///     是否全部读取成功
    /// </summary>
    /// <returns></returns>
    private bool ContainsGoodVariableStatus()
    {
        return DeviceVariables.Any(x => x.Value.VariableStatus == VariableStatusTypeEnum.Good);
    }

    /// <summary>
    ///     状态是否是部分成功
    /// </summary>
    /// <returns></returns>
    private bool HasPartialGoodVariableStatus()
    {
        // 非直接读取的属性数量
        var availableVariablesCount = DeviceVariableSource.Values.Count(w =>
            w.Enable && w.ValueSource != ValueSourceEnum.Get || (w.ValueSource == ValueSourceEnum.Get && w.DeviceVariableEx?.ProtectType != ProtectTypeEnum.WriteOnly));
        // 标记成功的数量
        var goodCount = DeviceVariables.Count(x => x.Value.VariableStatus == VariableStatusTypeEnum.Good);
        // 成功数量 - 脚本/静态属性数量 > 0 代表有读取成功
        var isSuccess = goodCount > availableVariablesCount;
        return isSuccess && DeviceVariables.Any(x => x.Value.VariableStatus != VariableStatusTypeEnum.Good);
    }

    #endregion

    /// <summary>
    ///     生成标准返回实体
    /// </summary>
    /// <param name="deviceVariable"></param>
    /// <param name="dataType"></param>
    /// <returns></returns>
    private DriverReturnValueModel Create(DeviceVariable deviceVariable, DataTypeEnum dataType)
    {
        DeviceVariables.TryGetValue(deviceVariable.Identifier, out var variable);
        return new DriverReturnValueModel
        {
            Id = deviceVariable.Id,
            Identifier = deviceVariable.Identifier,
            ReadTime = DateTime.ToLong(DateTime.Now()),
            CookieValue = variable?.Value ?? "",
            CookieTime = variable?.ReadTime ?? 0,
            DataType = dataType,
            VariableStatus = VariableStatusTypeEnum.Good,
            TransitionType = deviceVariable.TransitionType
        };
    }

    /// <summary>
    ///     写入属性间隔时间
    /// </summary>
    private readonly int _delayTime;

    /// <summary>
    ///     设备属性操作下写订阅
    /// </summary>
    private async Task DeviceWriteSubscribe()
    {
        await MessageCenter.Subscribe(string.Format(ConstMethod.DeviceWrite, Device.DeviceName), async context =>
        {
            // 确认节点是否被标记取消
            if (_tokenSource.IsCancellationRequested)
                return;
            Log.Information($"准备执行属性下写:{context.Source.Payload.ToJson()},设备名称:{Device.DeviceName},延迟设置:{_delayTime}");
            // 默认间隔休眠
            await Task.Delay(_delayTime, _tokenSource.Token);
            var payLoad = context.GetPayload<DeviceWriteRequest>();
            var output = await DeviceWrite(payLoad);
            Log.Information($"准备执行属性下写完成  设备名称:{Device.DeviceName},下写结果:{JSON.Serialize(output)}");
            // 记录远程下写日志
            var writeLog = new DeviceWriteLog
            {
                Id = YitIdHelper.NextId(),
                DeviceId = Device.Id,
                StartTime = DateTime.ShangHai(),
                WriteSide = DeviceWriteSideEnum.ServerSide,
                Params = JSON.Serialize(payLoad.Params),
                IsSuccess = true,
                Description = JSON.Serialize(output),
                EndTime = DateTime.ShangHai()
            };

            // 将日志加入队列
            _writeLogQueue.Enqueue(writeLog);

            // 尝试批量写入日志
            await TryBatchWriteLogs();
        });
    }

    // 1. 使用队列缓存写入日志
    private static readonly ConcurrentQueue<DeviceWriteLog> _writeLogQueue = new();
    private static readonly SemaphoreSlim _writeSemaphore = new(1, 1);

    // 2. 批量写入日志的方法
    private async Task TryBatchWriteLogs()
    {
        // 使用信号量控制并发
        if (!await _writeSemaphore.WaitAsync(TimeSpan.FromMilliseconds(100)))
            return;

        try
        {
            var logs = new List<DeviceWriteLog>();

            // 从队列中取出最多100条日志
            while (logs.Count < 100 && _writeLogQueue.TryDequeue(out var log))
            {
                logs.Add(log);
            }

            if (!logs.Any())
                return;

            // 批量写入数据库
            using var scope = Services.CreateScope();
            var rep = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<DeviceWriteLog>>();
            await rep.CopyNew().InsertRangeAsync(logs);
        }
        catch (Exception ex)
        {
            Log.Error($"批量写入设备日志失败: {ex.Message}");
        }
        finally
        {
            _writeSemaphore.Release();
        }
    }

    /// <summary>
    ///     设备属性下写
    /// </summary>
    /// <param name="payLoad">写入内容</param>
    /// <param name="type">写入限制:0不限制；1仅静态变量；2仅设备属性</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public async Task<dynamic> DeviceWrite(DeviceWriteRequest payLoad, int type = 0)
    {
        List<dynamic> output = new();
        using var scope = Services.CreateScope();
        var rep = scope.ServiceProvider.GetRequiredService<SysConfigService>();
        // 全局限制写入
        var whetherToAllowTheDeviceToWrite = await rep.GetConfigValue<bool>(ConfigConst.WhetherToAllowTheDeviceToWrite);
        if (type == 2)
            if (!whetherToAllowTheDeviceToWrite)
                throw Oops.Oh("全局设置已经禁止设备写入");
        // 遍历写入
        foreach (var (identifier, value) in payLoad.Params)
            try
            {
                // 检查属性是否存在
                var variable = Device.DeviceVariable.FirstOrDefault(f => f.Identifier == identifier);
                if (variable == null)
                {
                    output.Add(new
                    {
                        identifier,
                        isSuccess = false,
                        desc = "标识属性未找到"
                    });
                    continue;
                }

                var execValue = value;
                try
                {
                    // 允许解析表达式
                    if (payLoad.Eval)
                        // 尝试解析，确认是否是表达式
                        execValue = _interpreter.Eval(value).ToString();
                }
                catch
                {
                    // ignored
                }

                switch (variable.ValueSource)
                {
                    // 值相同就不改变
                    case ValueSourceEnum.Static when variable.DefaultValue == value:
                        continue;
                    // 改变默认值
                    case ValueSourceEnum.Static:
                        {
                            // 检查是否受限
                            if (type != 0 && type != 1)
                            {
                                output.Add(new
                                {
                                    identifier,
                                    isSuccess = false,
                                    desc = "限制静态变量暂不允许写入"
                                });
                                continue;
                            }

                            variable.DefaultValue = execValue;
                            output.Add(new
                            {
                                identifier,
                                isSuccess = true
                            });
                            // 修改默认值
                            DeviceVariableSource[identifier].DefaultValue = execValue;
                            var deviceVariableRep = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<DeviceVariable>>();
                            await deviceVariableRep.AsSugarClient().Updateable(DeviceVariableSource[identifier]).UpdateColumns(w => w.DefaultValue).ExecuteCommandAsync();
                            break;
                        }
                    // 读取变量
                    case ValueSourceEnum.Get:
                        {
                            // 检查是否受限
                            if (type != 0 && type != 2)
                            {
                                output.Add(new
                                {
                                    identifier,
                                    isSuccess = false,
                                    desc = "限制设备地址暂不允许写入"
                                });
                                continue;
                            }

                            // 检查是否允许写入PLC
                            if (!whetherToAllowTheDeviceToWrite)
                            {
                                output.Add(new
                                {
                                    identifier,
                                    isSuccess = false,
                                    desc = "全局设置已经禁止设备写入"
                                });
                                continue;
                            }

                            // 慢设备频繁写入会导致timeout
                            Thread.Sleep(_delayTime);
                            // 确认是否允许写入
                            if (variable.DeviceVariableEx.ProtectType == ProtectTypeEnum.ReadOnly)
                            {
                                output.Add(new
                                {
                                    identifier,
                                    isSuccess = false,
                                    desc = "地址是只读属性,无法写入"
                                });
                                await MessageCenter.PublishAsync(EventConst.WriteVariable, ErrorTp(identifier, value, "地址是只读属性,无法写入"));
                                continue;
                            }

                            // 是否是CNC设备
                            if (Device.Driver.DriverType == DriverTypeEnum.Cnc)
                                return new { success = false, error = $"{variable.Identifier},CNC类型设备暂时不支持写入！", data = new List<dynamic>() };
                            // 地址
                            var addr = variable.DeviceVariableEx.RegisterAddress;
                            // 数据类型
                            var valueType = variable.DeviceVariableEx.DataType;
                            if ((valueType == DataTypeEnum.Double && double.IsNaN(Convert.ToDouble(execValue))) || (valueType == DataTypeEnum.Float && double.IsNaN(Convert.ToSingle(value))))
                            {
                                output.Add(new
                                {
                                    identifier,
                                    isSuccess = false,
                                    desc = "值是NAN,无法写入"
                                });
                                // 推送结果日志
                                await MessageCenter.PublishAsync(EventConst.WriteVariable, ErrorTp(identifier, execValue, "值是NAN,无法写入"));
                                break;
                            }

                            // 创建写入对象
                            var ioArgModel = new DriverAddressIoArgModel(variable.Id, addr, variable.DeviceVariableEx.Length, valueType, execValue, encoding: variable.DeviceVariableEx.Encoding);
                            // 写入设备
                            var ret = Driver.WriteAsync(ioArgModel).GetAwaiter().GetResult();
                            // 推送结果日志
                            await MessageCenter.PublishAsync(EventConst.WriteVariable, $"下写地址:【{addr}】,数据类型:【{valueType}】,值:【{execValue}】,结果：【{JSON.Serialize(ret)}】");
                            output.Add(new
                            {
                                identifier,
                                isSuccess = ret.IsSuccess,
                                desc = ret.Description
                            });
                            break;
                        }
                    default:
                        output.Add(new
                        {
                            identifier,
                            isSuccess = false,
                            desc = "暂不支持"
                        });
                        break;
                }
            }
            catch (Exception e)
            {
                output.Add(new
                {
                    identifier,
                    isSuccess = false,
                    desc = "出错!" + e.Message
                });
                var errorTp = ErrorTp(identifier, value, e.Message);
                await MessageCenter.PublishAsync(EventConst.WriteVariable, errorTp);
            }

        // 写入结果反馈
        // _myMqttClient.ResponseRpc(rpcResponse);
        return output;
    }

    /// <summary>
    ///     输出统一信息
    /// </summary>
    /// <returns></returns>
    private string ErrorTp(string key, string value, string message)
    {
        return TP.WrapperRectangle(new[]
        {
            "属性下写",
            $"时间:【{DateTime.NowString()}】",
            $"属性标识:【{key}】",
            $"值:【{value}】",
            $"错误信息:【{message}】"
        }, -1);
    }

    /// <summary>
    ///     云端要求刷新设备实时数据
    ///     备注:目前仅SupOS平台有效
    /// </summary>
    private async Task DeviceCloudRequestRefreshRealTimeDeviceData()
    {
        await MessageCenter.Subscribe(string.Format(ConstMethod.CloudRequestRefreshRealTimeDeviceData, Device.DeviceName), async _ =>
        {
            try
            {
                //确认节点是否被标记取消
                if (_tokenSource.IsCancellationRequested)
                    return;

                await Task.Factory.StartNew(async () =>
                {
                    var payLoad = CreatePayLoad();
                    MasterClient mqttClient = App.GetService<MasterClient>();
                    // 云端要求刷新设备实时数据回复
                    await mqttClient.PublishAsync(payLoad, TransPondTopicPurposeEnum.CloudRequestRefreshRealTimeDeviceDataReply);
                });
                await Task.CompletedTask;
            }
            catch (Exception e)
            {
                Log.Error($"【云端要求刷新设备实时数据】 订阅Error:{e.Message}");
            }
        });
    }

    /// <summary>
    ///     FLink要求刷新设备实时数据
    ///     备注:目前仅对FLink平台有效
    /// </summary>
    private async Task FLinkRequestRefreshRealTimeDeviceData()
    {
        await MessageCenter.Subscribe(string.Format(ConstMethod.FLinkRequestRefreshRealTimeDeviceData, Device.DeviceName), async _ =>
        {
            try
            {
                // 确认节点是否被标记取消
                if (_tokenSource.IsCancellationRequested)
                    return;
                await Task.Factory.StartNew(async () =>
                {
                    var payLoad = CreatePayLoad();
                    Console.WriteLine($"[FLink] 发布刷新事件: {JSON.Serialize(payLoad)}");
                    await MessageCenter.PublishAsync(EventConst.FLinkSendDeviceData, payLoad);
                });
                await Task.CompletedTask;
            }
            catch (Exception e)
            {
                Log.Error($"【FLink要求刷新设备实时数据】 订阅Error:{e.Message}");
            }
        });
    }

    /// <summary>
    /// 创建payload
    /// </summary>
    /// <returns></returns>
    private PayLoad CreatePayLoad()
    {
        var payLoad = new PayLoad
        {
            DriverName = _payLoad.DriverName,
            DeviceId = _payLoad.DeviceId,
            DeviceName = _payLoad.DeviceName,
            DeviceStatus = _payLoad.DeviceStatus,
            Ts = _payLoad.Ts,
            Values = new Dictionary<string, ParamValue>()
        };
        foreach (var (key, driverReturnValue) in DeviceVariables)
        {
            DeviceVariableSource.TryGetValue(key, out var deviceVariable);
            if (deviceVariable == null)
                continue;
            payLoad.Values.Add(key, new ParamValue
            {
                Id = driverReturnValue.Id,
                Message = driverReturnValue.Message,
                Value = driverReturnValue.Value?.ToString(),
                VariableStatus = driverReturnValue.VariableStatus,
                CookieTime = driverReturnValue.CookieTime,
                CookieValue = driverReturnValue.CookieValue,
                DataType = driverReturnValue.DataType,
                ReadTime = driverReturnValue.ReadTime,
                SendType = deviceVariable.SendType,
                TransitionType = deviceVariable.TransitionType,
                DeviceVariableName = deviceVariable.Name
            });
        }

        return payLoad;
    }

    /// <summary>
    ///     停止采集线程
    /// </summary>
    public Task StopThread()
    {
        PublishDeviceEvent("warning", "设备断开连接,停止采集", "停止采集");
        if (Task == null) return Task.CompletedTask;
        Driver.Close();
        _tokenSource.Cancel();
        return Task.CompletedTask;
    }

    /// <summary>
    ///     释放采集线程
    /// </summary>
    public void Dispose()
    {
        Driver.Close();
        _tokenSource.Cancel();
        Driver.Dispose();
        //取消订阅当前设备写入方法
        MessageCenter.Unsubscribe(string.Format(ConstMethod.DeviceWrite, Device.DeviceName));
        Log.Information($"{Device.DeviceName},停止采集");
    }
}