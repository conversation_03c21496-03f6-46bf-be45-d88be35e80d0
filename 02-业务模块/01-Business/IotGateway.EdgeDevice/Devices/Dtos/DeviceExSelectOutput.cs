using DateTime = System.DateTime;

namespace IotGateway.EdgeDevice;

/// <summary>
/// </summary>
public class DeviceExSelectOutput
{
    /// <summary>
    ///     设备Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    ///     设备别名
    /// </summary>
    public string OtherName { get; set; }

    /// <summary>
    ///     协议名称
    /// </summary>
    public string DriverName { get; set; }

    /// <summary>
    ///     是否连接
    /// </summary>
    public bool IsConnect { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    ///     设备扩展信息
    /// </summary>
    public DeviceExOutput DeviceEx { get; set; }
}