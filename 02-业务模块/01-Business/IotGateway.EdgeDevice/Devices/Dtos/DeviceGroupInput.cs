namespace IotGateway.EdgeDevice;

/// <summary>
///     设备分组-新增
/// </summary>
public class DeviceGroupAddInput
{
    /// <summary>
    ///     分组名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     父级
    /// </summary>
    [Required]
    public long ParentId { get; set; }
}

/// <summary>
///     设备分组
/// </summary>
public class DeviceGroupSetInput
{
    /// <summary>
    ///     设备分组Id
    /// </summary>
    [Required]
    public long DeviceGroupId { get; set; }

    /// <summary>
    ///     设备Id集合
    /// </summary>
    [Required]
    public List<long> DeviceId { get; set; }
}