using Common.Enums;

namespace IotGateway.EdgeDevice;

/// <summary>
///     设备列表请求参数
/// </summary>
public class DevicePage : BasePageInput
{
    /// <summary>
    ///     设备运行状态:0:全部；1:在线；2:离线
    /// </summary>
    public short Status { get; set; }

    /// <summary>
    ///     0全部,1启用,2禁用
    /// </summary>
    public short Enable { get; set; }

    /// <summary>
    ///     协议Id
    /// </summary>
    public long DriverId { get; set; }

    /// <summary>
    ///     设备Id
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    ///     设备分组Id
    /// </summary>
    public long DeviceGroupId { get; set; }
}

/// <summary>
///     设备下拉请求参数
/// </summary>
public class DeviceSelectInput
{
    /// <summary>
    ///     搜索值
    /// </summary>
    public string SearchValue { get; set; }
}

/// <summary>
///     设备新增-请求参数
/// </summary>
public class DeviceAdd
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [Required(ErrorMessage = "设备名称不能是空")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    ///     采集协议Id
    /// </summary>
    [Range(1, long.MaxValue, ErrorMessage = "请选择采集协议！")]
    public long DriverId { get; set; }
}

/// <summary>
///     导入设备-请求参数
/// </summary>
public class InPortAllInput
{
    /// <summary>
    ///     上传文件
    /// </summary>
    public IFormFile File { get; set; }
}

/// <summary>
///     设备批量启用/禁用
/// </summary>
public class DeviceBatchEnableInput
{
    /// <summary>
    ///     设备Id集合
    /// </summary>
    [Required]
    public List<long> IdList { get; set; }

    /// <summary>
    ///     true 启用/false 禁用
    /// </summary>
    public bool Enable { get; set; }
}

/// <summary>
///     设备实时调试-读取请求参数
/// </summary>
public class DeviceDebugInput
{
    /// <summary>
    ///     读取方法,默认Debug,CNC协议调试必填
    /// </summary>
    public string Method { get; set; } = "Debug";

    /// <summary>
    ///     字符串编码
    /// </summary>
    public StringEnum Encoding { get; set; } = StringEnum.Utf8;

    /// <summary>
    ///     设备Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     数据类型,PLC协议必填
    /// </summary>
    public DataTypeEnum DataType { get; set; }

    /// <summary>
    ///     读取长度:string 类型读取必填
    /// </summary>
    public byte Lenth { get; set; } = 10;

    /// <summary>
    ///     读取地址,PLC类型必填
    /// </summary>
    public string Address { get; set; }
}

/// <summary>
///     设备实时调试-写入请求参数
/// </summary>
public class DeviceDebugWriteInput
{
    /// <summary>
    ///     设备Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     数据类型,PLC协议必填
    /// </summary>
    public DataTypeEnum DataType { get; set; }

    /// <summary>
    ///     读取长度:string 类型读取必填
    /// </summary>
    public byte Length { get; set; } = 10;

    /// <summary>
    ///     字符串编码
    /// </summary>
    public StringEnum Encoding { get; set; } = StringEnum.Utf8;

    /// <summary>
    ///     读取地址
    /// </summary>
    [Required]
    public string Address { get; set; }

    /// <summary>
    ///     Value
    /// </summary>
    [Required]
    public string Value { get; set; }
}