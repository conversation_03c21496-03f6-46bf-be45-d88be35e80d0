using DateTime = Common.Extension.DateTime;

namespace IotGateway.EdgeDevice;

/// <summary>
///     设备列表返回
/// </summary>
public class DevicePageOutput : BasePageOutput
{
    /// <summary>
    ///     名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public uint Index { get; set; }

    /// <summary>
    ///     设备别名
    /// </summary>
    public string OtherName { get; set; }

    /// <summary>
    /// </summary>
    public Feng.IotGateway.Core.Entity.Driver Driver { get; set; }

    /// <summary>
    ///     采集间隔ms
    /// </summary>
    public string MinPeriod { get; set; }

    /// <summary>
    ///     连接地址
    /// </summary>
    public string IpAddress { get; set; } = "-";

    /// <summary>
    ///     配置
    /// </summary>
    [JsonIgnore]
    public List<DeviceConfig> DeviceConfigs { get; set; }

    /// <summary>
    /// </summary>
    [JsonIgnore]
    public long DriverId { get; set; }

    /// <summary>
    ///     是否启动
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    ///     是否连接
    /// </summary>
    public bool IsConnect { get; set; }

    /// <summary>
    ///     没有运行 == true 显示启动运行,
    ///     == false  显示停止运行
    /// </summary>
    public bool NotRunning { get; set; }

    /// <summary>
    ///     采集点数量
    /// </summary>
    public int VariableCount { get; set; }

    /// <summary>
    ///     Dnc支持
    /// </summary>
    public bool Dnc { get; set; }

    /// <summary>
    ///     设备分组Id
    /// </summary>
    public long DeviceGroupId { get; set; }
}

/// <summary>
///     推送设备状态
/// </summary>
public class DeviceStatusDto
{
    /// <summary>
    /// </summary>
    [JsonPropertyName("deviceId")]
    public long DeviceId { get; set; }

    /// <summary>
    ///     失败次数
    /// </summary>
    [JsonPropertyName("errorCount")]
    public int ErrorCount { get; set; }

    /// <summary>
    ///     错误信息
    /// </summary>
    [JsonPropertyName("errorMessage")]
    public string ErrorMessage { get; set; }

    /// <summary>
    ///     连接次数
    /// </summary>
    [JsonPropertyName("reConnCount")]
    public int ReConnCount { get; set; }

    /// <summary>
    ///     采集耗时
    /// </summary>
    [JsonPropertyName("readMilliseconds")]
    public long ReadMilliseconds { get; set; }

    /// <summary>
    ///     脚本执行耗时
    /// </summary>
    [JsonPropertyName("scriptMilliseconds")]
    public long ScriptMilliseconds { get; set; }

    /// <summary>
    ///     设备状态
    /// </summary>
    [JsonPropertyName("DeviceStatus")]
    public DeviceStatusTypeEnum DeviceStatus { get; set; } = DeviceStatusTypeEnum.Bad;

    /// <summary>
    ///     最后活动时间
    /// </summary>
    [JsonPropertyName("lastTime")]
    public string LastTime { get; set; }

    /// <summary>
    ///     设备连接状态变化时间
    /// </summary>
    [JsonPropertyName("deviceStatusChangeTime")]
    public string DeviceStatusChangeTime { get; set; }

    /// <summary>
    ///     状态持续时间
    /// </summary>
    [JsonPropertyName("durationTime")]
    public string DurationTime => DateTime.FormatTime(Convert.ToInt64((DateTime.ShangHai() - Convert.ToDateTime(DeviceStatusChangeTime)).TotalMilliseconds));
}

/// <summary>
///     设备扩展信息返回
/// </summary>
public class DeviceExOutput
{
    /// <summary>
    ///     名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    ///     别名
    /// </summary>
    public string? OtherName { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public uint Index { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    ///     驱动名称
    /// </summary>
    public string DriverName { get; set; }

    /// <summary>
    ///     是否启动
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    ///     设备基本信息
    /// </summary>
    [SugarColumn(ColumnDescription = "设备基本信息", IsJson = true ,IsNullable = true)]
    public Dictionary<string, string>? DeviceInfo { get; set; } = new();

    /// <summary>
    ///     设备基本扩展信息
    /// </summary>
    [SugarColumn(ColumnDescription = "设备基本扩展信息", IsJson = true,IsNullable = true)]
    public List<DeviceInfoExtension>? DeviceInfoExtensions { get; set; } = new();

    /// <summary>
    ///     设备配置
    /// </summary>
    public List<DeviceConfigExOutput> DeviceConfigs { get; set; }

    /// <summary>
    ///     属性配置
    /// </summary>
    public List<DeviceVariableExOutput> DeviceVariable { get; set; }

    /// <summary>
    ///     设备事件
    /// </summary>
    public List<DeviceEvent> DeviceEvent { get; set; }
}

/// <summary>
///     设备配置
/// </summary>
public class DeviceConfigExOutput
{
    /// <summary>
    ///     名称
    /// </summary>
    public string DeviceConfigName { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public string Value { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    public string? EnumInfo { get; set; }

    /// <summary>
    ///     是否必填
    /// </summary>
    public bool IsRequired { get; set; }
}

/// <summary>
///     属性配置
/// </summary>
public class DeviceVariableExOutput
{
    /// <summary>
    ///     标识符
    /// </summary>
    public string Identifier { get; set; }

    /// <summary>
    ///     变量名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     默认值
    /// </summary>
    public string? DefaultValue { get; set; }

    /// <summary>
    ///     长度(属性值小数点保留位数长度)
    /// </summary>
    public ushort Length { get; set; }

    /// <summary>
    ///     转换数据类型
    /// </summary>
    [JsonIgnore]
    public TransPondDataTypeEnum TransitionType { get; set; }

    /// <summary>
    ///     转换数据类型
    /// </summary>
    public string TransitionTypeName => EnumUtil.GetEnumDesc(TransitionType);

    /// <summary>
    ///     数据来源
    /// </summary>
    [JsonIgnore]
    public ValueSourceEnum ValueSource { get; set; }

    /// <summary>
    ///     数据来源
    /// </summary>
    public string ValueSourceName => EnumUtil.GetEnumDesc(ValueSource);

    /// <summary>
    ///     执行优先级:0最优
    /// </summary>
    public short Order { get; set; }

    /// <summary>
    ///     表达式
    /// </summary>
    public string? Expressions { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    public string? Script { get; set; }

    /// <summary>
    ///     上送方式
    /// </summary>
    [JsonIgnore]
    public SendTypeEnum SendType { get; set; }

    /// <summary>
    ///     上送方式
    /// </summary>
    public string SendTypeName => EnumUtil.GetEnumDesc(SendType);

    /// <summary>
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    [SugarColumn(IsJson = true)]
    public List<string> Tags { get; set; } = new();

    /// <summary>
    ///     采集周期
    /// </summary>
    public int Period { get; set; }

    /// <summary>
    ///     强制归档时间
    /// </summary>
    public int ArchiveTime { get; set; }

    /// <summary>
    ///     单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    ///     为属性值添加自定义
    /// </summary>
    public string? Custom { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    ///     是否是系统属性
    /// </summary>
    public bool IsSystem { get; set; }

    /// <summary>
    ///     计算复制-持久化
    /// </summary>
    public bool Persistence { get; set; }

    /// <summary>
    ///     取值范围配置
    /// </summary>
    [SugarColumn(IsJson = true)]
    public DeviceVariableFilterExOutput DeviceVariableFilter { get; set; }

    /// <summary>
    ///     属性拓展信息
    /// </summary>
    [SugarColumn(IsJson = true)]
    public DeviceVariableExExOutput DeviceVariableEx { get; set; }
}

/// <summary>
///     属性过滤
/// </summary>
public class DeviceVariableFilterExOutput
{
    /// <summary>
    ///     最小值
    /// </summary>
    public long Min { get; set; }

    /// <summary>
    ///     最大值
    /// </summary>
    public long Max { get; set; }

    /// <summary>
    ///     设备属性取值范围最大最小值类型枚举：1:原值；2：最小值；3:最大值；4：指定值；5：上一有效值 6：丢弃
    /// </summary>
    [JsonIgnore]
    public DeviceVariableFilterTypeEnum MinFilterType { get; set; }

    /// <summary>
    ///     设备属性取值范围最大最小值类型枚举：1:原值；2：最小值；3:最大值；4：指定值；5：上一有效值 6：丢弃
    /// </summary>
    public string MinFilterTypeName => EnumUtil.GetEnumDesc(MinFilterType);

    /// <summary>
    ///     最小取值范围指定值
    /// </summary>
    public long SetMin { get; set; }

    /// <summary>
    ///     最大取值范围指定值
    /// </summary>
    public long SetMax { get; set; }

    /// <summary>
    ///     设备属性取值范围最大最小值类型枚举：1:原值；2：最小值；3:最大值；4：指定值；5：上一有效值 6：丢弃
    /// </summary>
    [JsonIgnore]
    public DeviceVariableFilterTypeEnum MaxFilterType { get; set; } = DeviceVariableFilterTypeEnum.This;

    /// <summary>
    ///     设备属性取值范围最大最小值类型枚举：1:原值；2：最小值；3:最大值；4：指定值；5：上一有效值 6：丢弃
    /// </summary>
    public string MaxFilterTypeName => EnumUtil.GetEnumDesc(MaxFilterType);

    /// <summary>
    ///     是否保存
    /// </summary>
    public bool Save { get; set; }
}

/// <summary>
/// </summary>
public class DeviceVariableExExOutput
{
    /// <summary>
    ///     读写方式
    /// </summary>
    [JsonIgnore]
    public ProtectTypeEnum ProtectType { get; set; }

    /// <summary>
    ///     读写方式
    /// </summary>
    public string ProtectTypeName => ProtectType == 0 ? "只读" : EnumUtil.GetEnumDesc(ProtectType);

    /// <summary>
    ///     读取方法
    /// </summary>
    public string Method { get; set; }

    /// <summary>
    ///     读取地址
    /// </summary>
    public string RegisterAddress { get; set; }

    /// <summary>
    ///     读取数据类型
    /// </summary>
    [JsonIgnore]
    public DataTypeEnum DataType { get; set; }

    /// <summary>
    ///     读取数据类型
    /// </summary>
    public string DataTypeName => EnumUtil.GetEnumDesc(DataType);
}