namespace IotGateway.EdgeDevice;

/// <summary>
///     设备扩展属性保存
/// </summary>
public class DeviceExSaveInput
{
    /// <summary>
    ///     名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    ///     别名
    /// </summary>
    public string? OtherName { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public uint Index { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    ///     是否启动
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    ///     设备基本信息
    /// </summary>
    [SugarColumn(ColumnDescription = "设备基本信息", IsJson = true)]
    public Dictionary<string, string> DeviceInfo { get; set; } = new();

    /// <summary>
    ///     设备基本扩展信息
    /// </summary>
    [SugarColumn(ColumnDescription = "设备基本扩展信息", IsJson = true)]
    public List<DeviceInfoExtension> DeviceInfoExtensions { get; set; } = new();

    /// <summary>
    ///     设备配置
    /// </summary>
    public List<DeviceConfigExInput> DeviceConfigs { get; set; }

    /// <summary>
    ///     属性配置
    /// </summary>
    public List<DeviceVariableExInput> DeviceVariable { get; set; }

    /// <summary>
    ///     设备事件
    /// </summary>
    public List<DeviceEventExInput> DeviceEvent { get; set; } = new();

    /// <summary>
    ///     设备Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}

/// <summary>
///     设备配置
/// </summary>
public class DeviceConfigExInput
{
    /// <summary>
    ///     名称
    /// </summary>
    public string DeviceConfigName { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public string Value { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    public string? EnumInfo { get; set; }

    /// <summary>
    ///     是否必填
    /// </summary>
    public bool IsRequired { get; set; }
}

/// <summary>
///     属性配置
/// </summary>
public class DeviceVariableExInput : DeviceVariableExOutput
{
    /// <summary>
    ///     转换数据类型
    /// </summary>
    public new string TransitionTypeName { get; set; }

    /// <summary>
    ///     数据来源
    /// </summary>
    public new string ValueSourceName { get; set; }

    /// <summary>
    ///     上送方式
    /// </summary>
    public new string SendTypeName { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    [SugarColumn(IsJson = true)]
    public List<string> Tags { get; set; } = new();

    /// <summary>
    ///     取值范围配置
    /// </summary>
    [SugarColumn(IsJson = true)]
    public DeviceVariableFilterExInput DeviceVariableFilter { get; set; }

    /// <summary>
    ///     属性拓展信息
    /// </summary>
    [SugarColumn(IsJson = true)]
    public DeviceVariableExExInput DeviceVariableEx { get; set; }
}

/// <summary>
///     设备事件拓展
/// </summary>
public class DeviceEventExInput
{
    /// <summary>
    ///     触发方式：1 属性触发；2定时触发
    /// </summary>
    [SugarColumn(ColumnDescription = "触发方式：1属性触发；2定时触发；")]
    public TriggerEventTypeEnum TriggerEventType { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string EventName { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool Status { get; set; }

    /// <summary>
    ///     事件配置参数
    /// </summary>
    [SugarColumn(ColumnDescription = "事件配置参数", IsJson = true, ColumnDataType = StaticConfig.CodeFirst_BigString, IsNullable = true)]
    public CustomEventConfig CustomEventConfig { get; set; }

    /// <summary>
    ///     事件执行条件
    /// </summary>
    [SugarColumn(ColumnDescription = "事件执行条件", IsJson = true, ColumnDataType = StaticConfig.CodeFirst_BigString, IsNullable = true)]
    public List<CustomEventWhere> CustomEventWhereList { get; set; }
}

/// <summary>
///     属性过滤
/// </summary>
public class DeviceVariableFilterExInput : DeviceVariableFilterExOutput
{
    /// <summary>
    ///     设备属性取值范围最大最小值类型枚举：1:原值；2：最小值；3:最大值；4：指定值；5：上一有效值 6：丢弃
    /// </summary>
    public new string MinFilterTypeName { get; set; }

    /// <summary>
    ///     设备属性取值范围最大最小值类型枚举：1:原值；2：最小值；3:最大值；4：指定值；5：上一有效值 6：丢弃
    /// </summary>
    public new string MaxFilterTypeName { get; set; }
}

/// <summary>
///     设备属性导入
/// </summary>
public class DeviceVariableExExInput : DeviceVariableExExOutput
{
    /// <summary>
    ///     读写方式
    /// </summary>
    public new string ProtectTypeName { get; set; }

    /// <summary>
    ///     读取数据类型
    /// </summary>
    public new string DataTypeName { get; set; }
}