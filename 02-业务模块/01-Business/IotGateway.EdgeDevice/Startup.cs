using IotGateway.EdgeDevice.BackgroundServices;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;

namespace IotGateway.EdgeDevice;

[AppStartup(99)]
public class Startup : AppStartup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // 延迟加载
        services.AddSingleton<DriverHostedService>();
        // 注册采集程序
        services.AddSingleton<DeviceHostedService>();
        // 清理设备事件
        services.AddSingleton<DropDeviceEventTimer>();
        
        // // 注册协议 
        // services.AddSingleton<DriverHostedService>();
        // services.AddHostedService(provider => provider.GetRequiredService<DriverHostedService>());

        // // 注册采集程序 
        // services.AddSingleton<DeviceHostedService>();
        // services.AddHostedService(provider => provider.GetRequiredService<DeviceHostedService>());

        // // 清理设备事件
        // services.AddHostedService<DropDeviceEventTimer>();
    }

    /// <summary>
    /// 配置中间件
    /// </summary>
    /// <param name="app"></param>
    /// <param name="env"></param>
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        _serviceProvider = app.ApplicationServices;

        // 在最后添加延迟启动的代码
        Task.Run(async () =>
        {
            // 等待3秒再启动非关键服务
            await Task.Delay(10000);
            await StartBackgroundServices();
        });
    }

    /// <summary>
    /// 启动后台服务
    /// </summary>
    /// <returns></returns>
    private async Task StartBackgroundServices()
    {
        // 采集协议
        await StartHostedService<DriverHostedService>();
        // 采集设备
        await StartHostedService<DeviceHostedService>();
        // 清理设备事件
        await StartHostedService<DropDeviceEventTimer>();

    }

    private IServiceProvider _serviceProvider;

    /// <summary>
    ///     启动后台服务
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    private async Task StartHostedService<T>() where T : IHostedService
    {
        var service = _serviceProvider.GetService<T>();
        if (service != null)
        {
            await service.StartAsync(CancellationToken.None);
        }
    }
}