using Common.Extension;
using MiniExcelLibs;
using StackExchange.Profiling.Internal;
using DateTime = System.DateTime;

namespace IotGateway.EdgeDevice.DeviceVariables;

/// <summary>
///     设备属性
/// </summary>
[ApiDescriptionSettings("设备管理")]
public class DeviceVariableService : ITransient, IDynamicApiController
{
    private readonly ISqlSugarClient _db;
    private readonly SqlSugarRepository<Device> _device;
    private readonly SqlSugarRepository<DeviceVariable> _deviceVariable;
    private readonly IEventPublisher _eventPublisher;
    private readonly DeviceHostedService _deviceHostedService;

    /// <summary>
    /// </summary>
    /// <param name="deviceVariable">设备变量</param>
    /// <param name="device"></param>
    /// <param name="deviceHostedService"></param>
    /// <param name="db"></param>
    /// <param name="eventPublisher"></param>
    public DeviceVariableService(SqlSugarRepository<DeviceVariable> deviceVariable, SqlSugarRepository<Device> device, DeviceHostedService deviceHostedService, ISqlSugarClient db,
        IEventPublisher eventPublisher)
    {
        _deviceVariable = deviceVariable;
        _device = device;
        _deviceHostedService = deviceHostedService;
        _db = db;
        _eventPublisher = eventPublisher;
    }

    #region Get

    /// <summary>
    ///     获取网关已经使用授权数量
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/getAuthorizedQuantityInUse")]
    public async Task<dynamic> GetAuthorizedQuantityInUse()
    {
        var deviceCount = 0;
        var deviceVariableCount = 0;

        // 设备使用数量
        var useDeviceCount = await _db.CopyNew().Queryable<Device>().CountAsync();
        // 属性使用数量
        var useDeviceVariableCount = await _db.CopyNew().Queryable<DeviceVariable>().CountAsync();

        var authorized = await AuthorizationUtil.GetAuthorization();
        if (authorized != null)
        {
            deviceCount = authorized.DeviceNumber;
            deviceVariableCount = authorized.VariableNumber;
        }

        return new[]
        {
            new
            {
                Name = "设备限制",
                Count = deviceCount,
                UseCount = useDeviceCount
            },
            new
            {
                Name = "点数限制",
                Count = deviceVariableCount,
                UseCount = useDeviceVariableCount
            }
        };
    }

    /// <summary>
    ///     设备属性-标签下拉框
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceVariable/tag/select")]
    public async Task<List<string>> DeviceVariableTagSelect([FromQuery] BaseId input)
    {
        var tags = new List<string>();
        var deviceVariableList = await _deviceVariable.AsQueryable()
            .Where(w => w.DeviceId == input.Id)
            .ToListAsync();
        foreach (var tag in deviceVariableList.Where(deviceVariable => deviceVariable.Tags != null && deviceVariable.Tags.Any())
                     .SelectMany(deviceVariable => deviceVariable.Tags.Where(tag => !tags.Contains(tag)))) tags.Add(tag);
        return tags;
    }

    /// <summary>
    ///     设备属性列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceVariable/page")]
    public async Task<SqlSugarPagedList<DeviceVariablePageOutput>> Page([FromQuery] DeviceVariablePageInput input)
    {
        // 为了支持实时读取状态查询,需要从实时里面查询到当前的状态
        var deviceValues = new List<string>();
        if (input.VariableStatus > 0)
        {
            var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == input.DeviceId);
            if (deviceThread != null)
                // GOOD
                deviceValues.AddRange(input.VariableStatus == 1
                    ? deviceThread.DeviceVariables.Where(w => w.Value.VariableStatus == VariableStatusTypeEnum.Good).Select(s => s.Key)
                    // Bad
                    : deviceThread.DeviceVariables.Where(w => w.Value.VariableStatus != VariableStatusTypeEnum.Good).Select(s => s.Key));
        }

        if (input.VariableStatus > 0 && !deviceValues.Any())
            return new SqlSugarPagedList<DeviceVariablePageOutput>();

        var deviceVariableList = await _deviceVariable.AsQueryable()
            .WhereIF(input.Partition.HasValue() && input.Partition is "CoilStatus" or "InputStatus",w => w.TransitionType == TransPondDataTypeEnum.Bool)
            .WhereIF(input.Partition.HasValue() && input.Partition is "HoldingRegister" or "InputRegister",w => w.TransitionType != TransPondDataTypeEnum.Bool)
            .WhereIF(input.DeviceId > 0, w => w.DeviceId == input.DeviceId)
            .WhereIF(input.ValueSource.Any(), w => input.ValueSource.Contains((int) w.ValueSource))
            .WhereIF(input.TransitionType.Any(), w => input.TransitionType.Contains((int) w.TransitionType))
            .WhereIF(input.SendType.Any(), w => input.SendType.Contains((int) w.SendType))
            .WhereIF(input.VariableStatus > 0 && deviceValues.Any(), w => deviceValues.Contains(w.Identifier))
            .WhereIF(input.FilterType > 0, w => w.Enable == true)
            .WhereIF(input.Tags.Any(), w => input.Tags.Any(s => SqlFunc.JsonLike(w.Tags, s)))
            .WhereIF(input.DeviceVariableId.Any(), w => input.DeviceVariableId.Contains(w.Id))
            .WhereIF(!string.IsNullOrEmpty(input.Name), w => w.Name.Contains(input.Name))
            .WhereIF(!string.IsNullOrEmpty(input.Identifier), w => w.Identifier.Contains(input.Identifier))
            .WhereIF(!string.IsNullOrEmpty(input.Unit), w => w.Unit != null && w.Unit.Contains(input.Unit))
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue), w => 
                w.Unit != null && w.Unit.Contains(input.SearchValue) ||
                w.Name.Contains(input.SearchValue) || 
                w.Identifier.Contains(input.SearchValue) ||
                SqlFunc.JsonLike(w.DeviceVariableEx, input.SearchValue))
            .Includes(w => w.Device)
            .WhereIF(input.DriverId > 0, w => w.Device.DriverId == input.DriverId)
            .OrderBy(u => new {u.DeviceId, u.Identifier})
            .ToPagedListAsync(input.PageNo, input.PageSize);

        return deviceVariableList.Adapt<SqlSugarPagedList<DeviceVariablePageOutput>>();
    }

    /// <summary>
    ///     设备属性详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceVariable/detail")]
    public async Task<DeviceVariable> Detail([FromQuery] BaseId input)
    {
        var deviceVariable = await _deviceVariable.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (deviceVariable == null)
            throw Oops.Oh(ErrorCode.Dvr404);
        return deviceVariable;
    }

    /// <summary>
    ///     设备属性-读写属性下拉框
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceVariable/select/readAndWrite")]
    public async Task<dynamic> ReadWriteList([FromQuery] ReadWriteListInput input)
    {
        var deviceVariableList = await _deviceVariable.AsQueryable()
            .Where(w => w.DeviceId == input.Id)
            .ToListAsync();
        // 
        return deviceVariableList.Where(w => (input.Type == 1
                                                 ?
                                                 // 只读
                                                 w.DeviceVariableEx.ProtectType == ProtectTypeEnum.ReadOnly && w.ValueSource == ValueSourceEnum.Get
                                                 :
                                                 // 读写
                                                 w.DeviceVariableEx.ProtectType != ProtectTypeEnum.ReadOnly && w.ValueSource == ValueSourceEnum.Get)
                                             || w.ValueSource == ValueSourceEnum.Static).Select(
            s => new
            {
                s.Id,
                s.Identifier,
                s.Name,
                s.DeviceVariableEx,
                s.DeviceVariableEx.RegisterAddress,
                s.DeviceVariableEx.DataType,
                s.DeviceVariableEx.Encoding,
                DataTypeName = EnumUtil.GetEnumDesc(s.DeviceVariableEx.DataType),
                EncodingName = EnumUtil.GetEnumDesc(s.DeviceVariableEx.Encoding)
            });
    }

    /// <summary>
    ///     设备属性下拉
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceVariable/select")]
    public async Task<dynamic> Select([FromQuery] DeviceVariableSelectInput input)
    {
        var deviceVariableList = await _deviceVariable.AsQueryable()
            .WhereIF(input.DeviceId > 0, w => w.DeviceId == input.DeviceId)
            .WhereIF(input.Tags.Any(), w => input.Tags.Any(s => SqlFunc.JsonLike(w.Tags, s)))
            .WhereIF(input.ValueSource > 0, w => w.ValueSource == (ValueSourceEnum) input.ValueSource)
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue), w => w.Name.Contains(input.SearchValue)
                                                                    || w.Description.Contains(input.SearchValue)
                                                                    || w.Identifier.Contains(input.SearchValue))
            .OrderBy(u => u.Identifier)
            .ToListAsync();

        if (input.Type > 0)
            deviceVariableList = deviceVariableList.Where(w => (input.Type == 1
                                                                   ?
                                                                   // 只读
                                                                   w.DeviceVariableEx.ProtectType == ProtectTypeEnum.ReadOnly && w.ValueSource == ValueSourceEnum.Get
                                                                   :
                                                                   // 读写
                                                                   w.DeviceVariableEx.ProtectType != ProtectTypeEnum.ReadOnly && w.ValueSource == ValueSourceEnum.Get)
                                                               || w.ValueSource == ValueSourceEnum.Static).ToList();
        return deviceVariableList.Select(s => new
        {
            s.Identifier,
            s.Id,
            s.Name,
            s.Description,
            DataType = s.ValueSource == ValueSourceEnum.Get ? s.DeviceVariableEx.DataType
                : s.TransitionType == TransPondDataTypeEnum.String ? DataTypeEnum.String
                : s.TransitionType == TransPondDataTypeEnum.Bool ? DataTypeEnum.Bool : DataTypeEnum.Int32,
            DataTypeName = EnumUtil.GetEnumDesc(s.DeviceVariableEx.DataType)
        });
    }

    /// <summary>
    ///     全部启用的设备和设备属性
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceVariable/all")]
    public async Task<dynamic> All()
    {
        var devices = await _device.AsQueryable()
            .Where(u => u.Enable == true)
            .OrderBy(u => u.Index)
            .Includes(w => w.Driver)
            .Includes(w => w.DeviceVariable.Where(s => s.Enable).ToList())
            .ToListAsync();
        return devices.Select(s => new
        {
            s.Id,
            s.DeviceName,
            s.DriverName,
            DeviceVariable = s.DeviceVariable.Select(v => new
            {
                v.Identifier,
                v.Id,
                v.Name,
                TransitionType = v.ValueSource == ValueSourceEnum.Get && s.Driver?.DriverType != DriverTypeEnum.Cnc ? v.DeviceVariableEx?.DataType
                    : v.TransitionType == TransPondDataTypeEnum.Int ? DataTypeEnum.Int32
                    : v.TransitionType == TransPondDataTypeEnum.Bool ? DataTypeEnum.Bool
                    : v.TransitionType == TransPondDataTypeEnum.Double ? DataTypeEnum.Double
                    : DataTypeEnum.String,
                TransitionTypeName = EnumUtil.GetEnumDesc(v.ValueSource == ValueSourceEnum.Get && s.Driver?.DriverType != DriverTypeEnum.Cnc ? v.DeviceVariableEx?.DataType
                    : v.TransitionType == TransPondDataTypeEnum.Int ? DataTypeEnum.Int32
                    : v.TransitionType == TransPondDataTypeEnum.Bool ? DataTypeEnum.Bool
                    : v.TransitionType == TransPondDataTypeEnum.Double ? DataTypeEnum.Double
                    : DataTypeEnum.String)
            })
        });
    }

    #endregion Get

    #region Post

    /// <summary>
    ///     添加设备属性属性
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/deviceVariable/add")]
    public async Task Add(DeviceVariableAdd input)
    {
        if (await AuthorizationUtil.IsAuthorized())
        {
            //最大添加设备点位数量
            var authorize = await AuthorizationUtil.GetAuthorization();
            if (authorize == null)
                throw Oops.Oh("未授权,请联系管理员！");
            var deviceVariableCount = await _deviceVariable.CountAsync(w => w.Id > 0);
            if (deviceVariableCount + input.Count >= authorize.VariableNumber)
                throw Oops.Oh($"超出网关允许最大设备点位采集数量:{authorize.VariableNumber},剩余可添加数量:{authorize.VariableNumber - deviceVariableCount}个,添加失败！");
        }

        if (await _deviceVariable.IsAnyAsync(a => a.Identifier == input.Identifier && a.DeviceId == input.DeviceId))
            throw Oops.Oh($"标识符:{input.Identifier},已存在！");
        var device = await _device.GetFirstAsync(f => f.Id == input.DeviceId);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        var deviceVariable = input.Adapt<DeviceVariable>();
        deviceVariable.Id = YitIdHelper.NextId();
        deviceVariable.DeviceId = device.Id;
        deviceVariable.DeviceVariableFilter ??= new DeviceVariableFilter();
        deviceVariable.DeviceVariableEx ??= new DeviceVariableEx();
        List<DeviceVariable> deviceVariableEntity = new() {deviceVariable};
        for (var i = 1; i < input.Count; i++)
        {
            var copyDeviceVariable = input.Adapt<DeviceVariable>();
            copyDeviceVariable.Id = YitIdHelper.NextId();
            copyDeviceVariable.DeviceId = device.Id;
            copyDeviceVariable.Identifier += $"_{i}";
            copyDeviceVariable.Name += $"_{i}";
            copyDeviceVariable.DeviceVariableEx.RegisterAddress = IncrementString(copyDeviceVariable.DeviceVariableEx.RegisterAddress, i);
            deviceVariableEntity.Add(copyDeviceVariable);
        }

        var isAdd = await _db.CopyNew().Insertable(deviceVariableEntity).ExecuteCommandAsync();
        if (isAdd >= 0)
            await _deviceHostedService.SetVariable(device.Id, deviceVariableEntity);
    }

    /// <summary>
    ///     批量添加设备属性属性
    /// </summary>
    /// <param name="deviceVariableBatchAddInput"></param>
    /// <param name="deviceId">设备Id</param>
    /// <returns></returns>
    [HttpPost("/deviceVariable/batchAdd/{deviceId:long}")]
    public async Task BatchAdd(List<DeviceVariableBatchAddInput> deviceVariableBatchAddInput, long deviceId)
    {
        if (await AuthorizationUtil.IsAuthorized())
        {
            //最大添加设备点位数量
            var authorize = await AuthorizationUtil.GetAuthorization();
            if (authorize == null)
                throw Oops.Oh("未授权,请联系管理员！");
            var deviceVariableCount = await _deviceVariable.CountAsync(w => w.Id > 0);
            if (deviceVariableCount + deviceVariableBatchAddInput.Count >= authorize.VariableNumber)
                throw Oops.Oh($"超出网关允许最大设备点位采集数量:{authorize.VariableNumber},剩余可添加数量:{authorize.VariableNumber - deviceVariableCount}个,添加失败！");
        }

        var device = await _device.GetFirstAsync(f => f.Id == deviceId);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);

        List<DeviceVariable> deviceVariableEntity = new();
        //循环添加到集合
        foreach (var input in deviceVariableBatchAddInput)
        {
            if (await _deviceVariable.IsAnyAsync(a => a.Identifier == input.Identifier && a.DeviceId == deviceId))
                throw Oops.Oh($"标识符:{input.Identifier},已存在！");

            var deviceVariable = input.Adapt<DeviceVariable>();
            deviceVariable.Id = YitIdHelper.NextId();
            deviceVariable.DeviceId = device.Id;
            deviceVariable.Enable = true;
            deviceVariable.ValueSource = ValueSourceEnum.Get;
            deviceVariable.DeviceVariableEx ??= new DeviceVariableEx();
            deviceVariable.DeviceVariableFilter ??= new DeviceVariableFilter();
            deviceVariableEntity.Add(deviceVariable);
        }

        var isAdd = await _db.CopyNew().Insertable(deviceVariableEntity).ExecuteCommandAsync();
        if (isAdd >= 0)
            await _deviceHostedService.SetVariable(device.Id, deviceVariableEntity);
    }

    /// <summary>
    ///     批量修改设备属性属性
    /// </summary>
    /// <param name="deviceVariableBatchUpdateInput"></param>
    /// <param name="deviceId">设备Id</param>
    /// <returns></returns>
    [HttpPost("/deviceVariable/batchUpdate/{deviceId:long}")]
    public async Task BatchUpdate(List<DeviceVariableBatchUpdateInput> deviceVariableBatchUpdateInput, long deviceId)
    {
        var device = await _device.GetFirstAsync(f => f.Id == deviceId);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);

        // 编辑的属性
        var deviceVariableList = await _deviceVariable.AsQueryable()
            .Where(w => w.DeviceId == deviceId && deviceVariableBatchUpdateInput.Select(s => s.Id).Contains(w.Id))
            .ToListAsync();
        foreach (var deviceVariable in deviceVariableList)
        {
            var variable = deviceVariableBatchUpdateInput.FirstOrDefault(f => f.Id == deviceVariable.Id);
            if (variable == null)
                continue;
            if (await _deviceVariable.IsAnyAsync(a => a.Identifier == variable.Identifier && a.DeviceId == deviceId && a.Id != variable.Id))
                throw Oops.Oh($"标识符:{variable.Identifier},已存在！");
            deviceVariable.Identifier = variable.Identifier;
            deviceVariable.Name = variable.Name;
            if (deviceVariable.TransitionType != variable.TransitionType)
                deviceVariable.Release = false;
            deviceVariable.TransitionType = variable.TransitionType;
            deviceVariable.SendType = variable.SendType;
            deviceVariable.Identifier = variable.Identifier;
            deviceVariable.Tags = variable.Tags;
            deviceVariable.Period = variable.Period;
            deviceVariable.DeviceVariableFilter ??= new DeviceVariableFilter();
            deviceVariable.DeviceVariableEx = variable.DeviceVariableEx;
        }

        var isSet = await _db.CopyNew().Updateable(deviceVariableList).IgnoreColumns(w => new {w.IsSystem, w.DeviceId}).ExecuteCommandAsync();
        if (isSet >= 0)
            await _deviceHostedService.SetVariable(device.Id, deviceVariableList);
    }


    /// <summary>
    ///     批量修改设备属性属性
    /// </summary>
    /// <param name="input"></param>
    /// <param name="deviceId">设备Id</param>
    /// <returns></returns>
    [HttpPost("/deviceVariable/batchUpdate/field/{deviceId:long}")]
    public async Task BatchUpdateField(BatchUpdateFieldInput input, long deviceId)
    {
        var device = await _device.AsQueryable().Where(f => f.Id == deviceId)
            .Includes(w => w.DeviceVariable.Where(v => input.IdList.Contains(v.Id)).ToList())
            .FirstAsync();
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        if (device.DeviceVariable.Count == 0)
            return;
        // 缓存反射结果
        var propertyCache = device.DeviceVariable.FirstOrDefault()!.GetType().GetProperties()
            .ToDictionary(p => p.Name, p => p);
        
        foreach (var (key, value) in input.SetValue)
        foreach (var deviceVariable in device.DeviceVariable)
        {
            var objValue = value.GetJsonElementValue();
            if (objValue == null)
                continue;
            // 查找属性是否存在
            if (propertyCache.TryGetValue(key, out var property))
            {
                switch (property.Name)
                {
                    case "Tags":
                        property.SetValue(deviceVariable, objValue.Adapt<List<string>>());
                        break;
                    case "Length":
                        property.SetValue(deviceVariable, Convert.ToUInt16(objValue));
                        break;
                    default:
                        property.SetValue(deviceVariable, objValue);
                        break;
                }
            }
            else
                switch (key)
                {
                    case "ProtectType":
                        deviceVariable.DeviceVariableEx.ProtectType = (ProtectTypeEnum)objValue;
                        break;

                    case "Method":
                        deviceVariable.DeviceVariableEx.Method = objValue as string ?? throw new InvalidOperationException();
                        break;

                    case "RegisterAddress":
                        deviceVariable.DeviceVariableEx.RegisterAddress = objValue.ToString() ?? "";
                        break;

                    case "DataType":
                        deviceVariable.DeviceVariableEx.DataType = (DataTypeEnum)objValue;
                        break;
                    case "ReadLength":
                        deviceVariable.DeviceVariableEx.Length = Convert.ToUInt16(objValue);
                        break;
                }
        }

        var isSet = await _db.CopyNew().Updateable(device.DeviceVariable).IgnoreColumns(w => new { w.IsSystem, w.DeviceId }).ExecuteCommandAsync();
        if (isSet >= 0)
            await _deviceHostedService.SetVariable(device.Id, device.DeviceVariable);
    }

    /// <summary>
    ///     修改设备属性属性
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/deviceVariable/update")]
    public async Task Update(DeviceVariable input)
    {
        var device = await _device.GetFirstAsync(f => f.Id == input.DeviceId);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);

        var deviceVariable = await _deviceVariable.GetFirstAsync(f => f.Id == input.Id);
        if (deviceVariable == null)
            throw Oops.Oh(ErrorCode.Dvr404);
        input.DeviceVariableFilter ??= new DeviceVariableFilter();
        input.DeviceVariableEx ??= new DeviceVariableEx();
        // 如果数据类型发生改变，将变为未发布状态
        if (input.TransitionType != deviceVariable.TransitionType)
            input.Release = false;
        var isSet = await _db.CopyNew().Updateable(input).IgnoreColumns(w => w.IsSystem).ExecuteCommandAsync();
        if (isSet >= 0 && deviceVariable.Enable)
        {
            if (deviceVariable.Identifier != input.Identifier) await _deviceHostedService.RemoveVariable(device.Id, deviceVariable);
            await _deviceHostedService.SetVariable(device.Id, input);
        }
    }

    /// <summary>
    ///     删除子设备属性
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/deviceVariable/delete")]
    public async Task Delete(DeviceVariableDeleteInput input)
    {
        var device = await _device.GetFirstAsync(f => f.Id == input.DeviceId);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        var deviceVariableList = await _deviceVariable.AsQueryable().Where(f => input.Id.Contains(f.Id)).ToListAsync();
        if (!deviceVariableList.Any())
            return;

        var isDel = await _db.CopyNew().Deleteable(deviceVariableList).ExecuteCommandAsync();
        if (isDel > 0)
            await _deviceHostedService.RemoveVariable(device.Id, deviceVariableList);
    }

    /// <summary>
    ///     导入设备属性
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/deviceVariable/inPort")]
    [HttpPost("/api/deviceVariable/inPort")]
    public async Task<DataInPortOutput> InPort([FromForm] DeviceVariableInPort input)
    {
        var device = await _device.GetFirstAsync(f => f.Id == input.DeviceId);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        var stream = new MemoryStream();
        await input.File.CopyToAsync(stream);
        // 返回结果
        var result = new DataInPortOutput {ErrorColumn = new List<ErrorColumn>()};
        // 需要新增的属性
        var deviceVariableList = new List<DeviceVariable>();
        // 当前所在行
        var line = 2;
        foreach (var deviceVariableExPort in await stream.QueryAsync<DeviceVariableInPortDto>(startCell: "A2"))
            try
            {
                // 检查必填项
                var success = CheckInPortData(deviceVariableExPort, line, result);
                if (!success)
                    continue;
                var deviceVariable = deviceVariableExPort.Adapt<DeviceVariable>();
                deviceVariable.Id = YitIdHelper.NextId();
                deviceVariable.DeviceId = device.Id;
                deviceVariable.Custom = deviceVariableExPort.Custom.IsNull() || deviceVariableExPort.Custom == "\"\"" ? null : deviceVariableExPort.Custom;
                deviceVariable.Length = (ushort) (!string.IsNullOrEmpty(deviceVariableExPort.LengthEx) ? Convert.ToUInt32(deviceVariableExPort.LengthEx) : 0);
                deviceVariable.Period = (ushort) (!string.IsNullOrEmpty(deviceVariableExPort.PeriodEx) ? Convert.ToUInt32(deviceVariableExPort.PeriodEx) : 0);
                deviceVariable.ArchiveTime = (ushort) (!string.IsNullOrEmpty(deviceVariableExPort.ArchiveTimeEx) ? Convert.ToUInt32(deviceVariableExPort.ArchiveTimeEx) : 0);
                deviceVariable.Tags = !string.IsNullOrEmpty(deviceVariableExPort.Tag) ? JSON.Deserialize<List<string>>(deviceVariableExPort.Tag) : new List<string>();
                deviceVariable.DeviceVariableFilter = JSON.Deserialize<DeviceVariableFilter>(deviceVariableExPort.DeviceVariableFilterExtend);
                deviceVariable.DeviceVariableEx = JSON.Deserialize<DeviceVariableEx>(deviceVariableExPort.VariableExtend);
                deviceVariableList.Add(deviceVariable);
                line++;
            }
            catch (Exception e)
            {
                result.ErrorCount += 1;
                result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 Error:【{e.Message}】 "});
            }

        // 查找出来同设备相同的标识符
        var deviceVariableAny = await _deviceVariable.AsQueryable()
            .Where(w => deviceVariableList.Select(s => s.Identifier).Contains(w.Identifier) && w.DeviceId == input.DeviceId)
            .ToListAsync();

        foreach (var deviceVariable in deviceVariableAny)
        {
            var deviceVariableVal = deviceVariableList.FirstOrDefault(f => f.Identifier == deviceVariable.Identifier);
            if (deviceVariableVal == null)
                continue;
            if (input.InPortType == DeviceVariableInPortTypeEnum.OverLook)
            {
                deviceVariableList.Remove(deviceVariableVal);
            }
            else
            {
                // 覆盖原始数据-移除对象,修改Id后重新加入
                deviceVariableList.Remove(deviceVariableVal);
                deviceVariableVal.Id = deviceVariable.Id;
                deviceVariableList.Add(deviceVariableVal);
            }
        }

        if (deviceVariableList.Any() && await AuthorizationUtil.IsAuthorized())
        {
            // 最大添加设备点位数量
            var authorize = await AuthorizationUtil.GetAuthorization();
            if (authorize == null)
                throw Oops.Oh("未授权,请联系管理员！");
            if (await _deviceVariable.CountAsync(w => w.Id > 0) >= authorize.VariableNumber
                || await _deviceVariable.CountAsync(w => w.Id > 0) + deviceVariableList.Count >= authorize.VariableNumber)
                throw Oops.Oh($"超出网关允许最大设备点位采集数量:{authorize.VariableNumber},添加失败！");
        }

        if (deviceVariableList.Any())
            await _db.Storageable(deviceVariableList).ExecuteCommandAsync();
        await UpdateDeviceThread(device);
        result.SuccessCount = line - 1 - result.ErrorCount < 0 ? 0 : line - 2 - result.ErrorCount;
        return result;
    }

    /// <summary>
    ///     导出设备属性
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/deviceVariable/exPort")]
    public async Task<IActionResult> ExPort(DeviceVariableExPortInput input)
    {
        var device = await _device.AsQueryable().Where(f => f.Id == input.Id)
            .Includes(w => w.DeviceVariable.Where(v => v.IsSystem == false)
                .WhereIF(input.DeviceVariableIds.Any(), x => input.DeviceVariableIds.Contains(x.Id)).ToList())
            .FirstAsync();
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);

        var value = new
        {
            deviceVariable = device.DeviceVariable.Select(s => new
            {
                s.Name,
                s.Identifier,
                TransitionType = EnumUtil.GetEnumDesc(s.TransitionType),
                ValueSource = EnumUtil.GetEnumDesc(s.ValueSource),
                SendType = EnumUtil.GetEnumDesc(s.SendType),
                DeviceVariableEx = JSON.Serialize(s.DeviceVariableEx),
                DeviceVariableFilter = JSON.Serialize(s.DeviceVariableFilter),
                s.DefaultValue,
                Period = s.Period == 0 ? "" : s.Period.ToString(),
                Tag = s.Tags.Any() ? JSON.Serialize(s.Tags) : "",
                Length = s.Length == 0 ? "" : s.Length.ToString(),
                s.Unit,
                s.Expressions,
                s.Script,
                ArchiveTime = s.ArchiveTime == 0 ? "" : s.ArchiveTime.ToString(),
                Custom = s.Custom ?? "",
                s.Persistence,
                s.Description,
                s.Enable
            })
        };

        try
        {
            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsByTemplateAsync("Templates/DeviceVariableImport.xlsx", value);
            memoryStream.Seek(0, SeekOrigin.Begin);
            return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            {
                FileDownloadName = $"{DateTime.Now:yyyyMMddHHmmssfff}.xlsx"
            };
        }
        catch (Exception ex)
        {
            throw Oops.Bah($"导出失败：{ex.Message}");
        }
    }

    /// <summary>
    ///     导出设备属性-简单模板
    /// </summary>
    /// <returns></returns>
    [HttpPost("/deviceVariable/exPortTemplate")]
    public async Task<IActionResult> ExPortTemplate()
    {
        var value = new
        {
            managers = new[]
            {
                new {Ident = "product", Name = "产量", Addr = "D100", DataType = "int32"}
            }
        };

        try
        {
            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsByTemplateAsync("Templates/DeviceVariableSimpleImport.xlsx", value);
            // await memoryStream.SaveAsAsync(values );
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            {
                FileDownloadName = $"{DateTime.Now:yyyyMMddHHmmssfff}.xlsx"
            };
        }
        catch (Exception ex)
        {
            throw Oops.Bah($"导出失败：{ex.Message}");
        }
    }

    /// <summary>
    ///     导入设备属性-简单模板
    /// </summary>
    /// <returns></returns>
    [HttpPost("/deviceVariable/inPortTemplate")]
    [HttpPost("/api/deviceVariable/inPortTemplate")]
    public async Task<DataInPortOutput> InPortTemplate([FromForm] DeviceVariableInPort input)
    {
        var device = await _device.GetFirstAsync(f => f.Id == input.DeviceId);
        if (device == null)
            throw Oops.Oh(ErrorCode.Dev404);
        var stream = new MemoryStream();
        await input.File.CopyToAsync(stream);
        //返回结果
        var result = new DataInPortOutput {ErrorColumn = new List<ErrorColumn>()};
        //需要新增的属性
        var deviceVariableList = new List<DeviceVariable>();
        //当前所在行
        var line = 2;
        foreach (var deviceVariableExPort in await stream.QueryAsync<DeviceVariableExPortTemplateInput>(startCell: "A2"))
            try
            {
                //检查必填项
                var success = CheckInPortDataTem(deviceVariableExPort, line, result);
                if (!success)
                    continue;
                var deviceVariable = deviceVariableExPort.Adapt<DeviceVariable>();
                deviceVariable.Id = YitIdHelper.NextId();
                deviceVariable.DeviceId = device.Id;
                deviceVariable.Tags = new List<string>();
                deviceVariable.DeviceVariableFilter = new DeviceVariableFilter();
                deviceVariable.DeviceVariableEx = new DeviceVariableEx
                {
                    DataType = deviceVariableExPort.DataType.Adapt<DataTypeEnum>(),
                    RegisterAddress = deviceVariableExPort.RegisterAddress,
                    Encoding = StringEnum.Utf8,
                    Method = "Debug",
                    ProtectType = ProtectTypeEnum.ReadOnly
                };
                if (deviceVariable.DeviceVariableEx.DataType == DataTypeEnum.String)
                    deviceVariable.Length = 10;
                switch (deviceVariable.DeviceVariableEx.DataType)
                {
                    case DataTypeEnum.Bool:
                        deviceVariable.TransitionType = TransPondDataTypeEnum.Bool;
                        break;
                    case DataTypeEnum.Uint16:
                    case DataTypeEnum.Int16:
                    case DataTypeEnum.Uint32:
                    case DataTypeEnum.Int32:
                    case DataTypeEnum.Uint64:
                    case DataTypeEnum.Int64:
                    case DataTypeEnum.Bit:
                    case DataTypeEnum.Bcd:
                        deviceVariable.TransitionType = TransPondDataTypeEnum.Int;
                        break;
                    case DataTypeEnum.Float:
                    case DataTypeEnum.Double:
                        deviceVariable.TransitionType = TransPondDataTypeEnum.Double;
                        break;
                    case DataTypeEnum.String:
                    default:
                        deviceVariable.TransitionType = TransPondDataTypeEnum.String;
                        break;
                }

                deviceVariable.ValueSource = ValueSourceEnum.Get;
                deviceVariable.SendType = SendTypeEnum.Always;
                deviceVariable.Enable = true;
                deviceVariableList.Add(deviceVariable);
                line++;
            }
            catch (Exception e)
            {
                result.ErrorCount += 1;
                result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 Error:【{e.Message}】 "});
            }

        //查找出来同设备相同的标识符
        var deviceVariableAny = await _deviceVariable.CopyNew().AsQueryable()
            .Where(w => deviceVariableList.Select(s => s.Identifier).Contains(w.Identifier) && w.DeviceId == input.DeviceId)
            .ToListAsync();

        foreach (var deviceVariable in deviceVariableAny)
        {
            // 设备属性
            var deviceVariableVal = deviceVariableList.FirstOrDefault(f => f.Identifier == deviceVariable.Identifier);
            if (deviceVariableVal == null)
                continue;
            // 重复忽略
            if (input.InPortType == DeviceVariableInPortTypeEnum.OverLook)
            {
                deviceVariableList.Remove(deviceVariableVal);
            }
            else
            {
                // 移除原始对象
                deviceVariableList.Remove(deviceVariableVal);
                // 重新设置Id
                deviceVariableVal.Id = deviceVariable.Id;
                // 重新加入
                deviceVariableList.Add(deviceVariableVal);
            }
        }

        if (deviceVariableList.Any() && await AuthorizationUtil.IsAuthorized())
        {
            //最大添加设备点位数量
            var authorize = await AuthorizationUtil.GetAuthorization();
            if (authorize == null)
                throw Oops.Oh("未授权,请联系管理员！");
            if (await _deviceVariable.CountAsync(w => w.Id > 0) >= authorize.VariableNumber
                || await _deviceVariable.CountAsync(w => w.Id > 0) + deviceVariableList.Count >= authorize.VariableNumber)
                throw Oops.Oh($"超出网关允许最大设备点位采集数量:{authorize.VariableNumber},添加失败！");
        }

        if (deviceVariableList.Any())
            await _db.CopyNew().Storageable(deviceVariableList).ExecuteCommandAsync();
        await UpdateDeviceThread(device);
        result.SuccessCount = line - 1 - result.ErrorCount < 0 ? 0 : line - 2 - result.ErrorCount;
        return result;
    }

    /// <summary>
    ///     设备属性启用/禁用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/deviceVariable/enable")]
    public async Task<bool> Enable(EnableInput<long> input)
    {
        var deviceVariable = await _deviceVariable.AsQueryable().Where(w => w.Id == input.Id)
            .Includes(w => w.Device)
            .FirstAsync();
        if (deviceVariable == null)
            throw Oops.Oh(ErrorCode.D1002);
        deviceVariable.Enable = input.Enable;
        await _deviceVariable.CopyNew().AsUpdateable(deviceVariable).UpdateColumns(w => w.Enable).ExecuteCommandAsync();
        if (deviceVariable.Enable)
            await _deviceHostedService.SetVariable(deviceVariable.DeviceId, deviceVariable);
        else
            await _deviceHostedService.RemoveVariable(deviceVariable.DeviceId, deviceVariable);
        return true;
    }

    /// <summary>
    ///     设备属性批量启用/禁用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/deviceVariable/batchEnable")]
    public async Task<bool> BatchEnable(DeviceVariableBatchEnableInput input)
    {
        var deviceVariableList = await _deviceVariable.AsQueryable().Where(w => input.DeviceId == w.DeviceId && input.IdList.Contains(w.Id))
            .Includes(w => w.Device)
            .ToListAsync();
        foreach (var deviceVariable in deviceVariableList) deviceVariable.Enable = input.Enable;
        await _deviceVariable.CopyNew().AsUpdateable(deviceVariableList).UpdateColumns(w => w.Enable).ExecuteCommandAsync();
        if (input.Enable)
            await _deviceHostedService.SetVariable(input.DeviceId, deviceVariableList);
        else
            await _deviceHostedService.RemoveVariable(input.DeviceId, deviceVariableList);
        return true;
    }

    /// <summary>
    ///     表达式函数
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceVariable/function/meta")]
    public async Task<dynamic> FunctionMeta()
    {
        var functionInfos = new[]
        {
            new
            {
                DisplayName = "CEILING()",
                DisplayValue = "Math.Ceiling()",
                FunctionMetaDesc = new
                {
                    Desc = "CEILING()：返回大于或等于指定数字的最小整数值。",
                    Usage = "CEILING(数字)",
                    Example = "CEILING(3.1415926) 返回4"
                }
            },
            new
            {
                DisplayName = "FLOOR()",
                DisplayValue = "Math.Floor()",
                FunctionMetaDesc = new
                {
                    Desc = "FLOOR()：返回小于或等于指定数字的最大整数值。",
                    Usage = "FLOOR(数字)",
                    Example = "FLOOR(3.1415926)返回3"
                }
            },
            new
            {
                DisplayName = "ROUND()",
                DisplayValue = "Math.Round()",
                FunctionMetaDesc = new
                {
                    Desc = "ROUND()：通过使用舍入到最接近的约定，将数字舍入到指定的小数位数。",
                    Usage = "ROUND(数字,保留长度)",
                    Example = "ROUND(3.1415926, 3)返回3.142，因为3.1415926四舍五入保留3位小数是3.142。"
                }
            }
        };
        return functionInfos;
    }

    /// <summary>
    ///     设备属性-写入
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/deviceVariable/write")]
    public async Task<dynamic> DeviceVariableWrite(DeviceVariableWriteInput input)
    {
        var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == input.DeviceId);
        if (deviceThread == null)
            throw Oops.Oh("设备连接已断开,请连接后重试！");

        return await deviceThread.DeviceWrite(new DeviceWriteRequest
        {
            DeviceName = deviceThread.Device.DeviceName,
            Params = input.Params
        });
    }

    /// <summary>
    ///     复制设备属性到其他设备中
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/deviceVariable/copy/toOtherDevice")]
    public async Task CopyToOtherDevice(CopyToOtherDeviceInput input)
    {
        if (await AuthorizationUtil.IsAuthorized())
        {
            //最大添加设备点位数量
            var authorize = await AuthorizationUtil.GetAuthorization();
            if (authorize == null)
                throw Oops.Oh("未授权,请联系管理员！");
            var deviceVariableCount = await _deviceVariable.CountAsync(w => w.Id > 0);
            var count = input.Ids.Count * input.DeviceIds.Count;
            if (deviceVariableCount + count >= authorize.VariableNumber)
                throw Oops.Oh($"超出网关允许最大设备点位采集数量:{authorize.VariableNumber},剩余可添加数量:{authorize.VariableNumber - deviceVariableCount}个,添加失败！");
        }

        //要复制的设备属性
        var deviceVariableList = await _deviceVariable.AsQueryable().Where(w => input.Ids.Contains(w.Id)).ToListAsync();
        //需要添加的设备
        var deviceList = await _device.AsQueryable().Where(f => input.DeviceIds.Contains(f.Id))
            .Includes(w => w.DeviceVariable)
            .ToListAsync();

        // 新增或者修改的数据
        var setDeviceVariables = new List<DeviceVariable>();
        // 当前设备的采集点
        foreach (var device in deviceList)
        {
            // 当前设备的采集点
            var identifier = device.DeviceVariable.Select(s => s.Identifier).ToList();
            foreach (var deviceVariable in deviceVariableList)
            {
                var mapDeviceVariable = deviceVariable.Adapt<DeviceVariable>();
                mapDeviceVariable.DeviceId = device.Id;
                mapDeviceVariable.Device = device;
                mapDeviceVariable.Release = false;
                //如果属性标识已经存在
                if (identifier.Contains(mapDeviceVariable.Identifier))
                {
                    // 忽略复制
                    if (!input.OverlaySave) continue;
                    // 检查属性是否存在,存在就修改，反之新增
                    var index = device.DeviceVariable.FindIndex(w => w.Identifier == mapDeviceVariable.Identifier);
                    mapDeviceVariable.Id = index != -1 ? device.DeviceVariable[index].Id : YitIdHelper.NextId();
                }
                else
                {
                    //新增new
                    mapDeviceVariable.Id = YitIdHelper.NextId();
                }

                setDeviceVariables.Add(mapDeviceVariable);
            }
        }

        // 插入数据库
        await _deviceVariable.CopyNew().AsSugarClient().Storageable(setDeviceVariables).ExecuteCommandAsync();
        // 保存后重新对实时采集的点位进行更新
        foreach (var device in deviceList)
            await _deviceHostedService.SetVariable(device.Id, setDeviceVariables);
    }

    #endregion Post

    #region Private

    /// <summary>
    ///     地址是否是数字结尾，满足 地址值+1
    /// </summary>
    /// <param name="input"></param>
    /// <param name="addNumber">添加值</param>
    /// <returns></returns>
    private static string IncrementString(string input, int addNumber = 1)
    {
        var pattern = @"(\d+)$";
        var regex = new Regex(pattern);
        var match = regex.Match(input);

        if (match.Success)
        {
            var numberString = match.Groups[1].Value;
            if (numberString.StartsWith("0"))
            {
                var parts = SplitString(numberString);
                var zeroes = parts[0];
                numberString = parts[1];
                var number = int.Parse(numberString);
                var incrementedNumber = number + addNumber;

                var incrementedString = zeroes + incrementedNumber;
                return input.Substring(0, match.Index) + incrementedString;
            }
            else
            {
                var number = int.Parse(numberString);
                var incrementedNumber = number + addNumber;

                var incrementedString = incrementedNumber.ToString();
                return input.Substring(0, match.Index) + incrementedString;
            }
        }

        return input;
    }

    /// <summary>
    ///     字符串是否是0开头
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private static string[] SplitString(string input)
    {
        var pattern = @"^(0+)(.*)";
        var regex = new Regex(pattern);

        var match = regex.Match(input);
        var zeroes = match.Groups[1].Value;
        var remainder = match.Groups[2].Value;

        return new[] {zeroes, remainder};
    }

    /// <summary>
    ///     修改采集线程
    /// </summary>
    /// <param name="device"></param>
    private async Task UpdateDeviceThread(Device device)
    {
        await _eventPublisher.PublishAsync(EventConst.UpdateDeviceThread, device);
    }

    /// <summary>
    ///     插入设备属性
    /// </summary>
    /// <param name="deviceVariable"></param>
    /// <returns></returns>
    [NonAction]
    public async Task Insert(List<DeviceVariable> deviceVariable)
    {
        await _db.Insertable(deviceVariable).ExecuteCommandAsync();
    }

    /// <summary>
    ///     检查属性导入必填项
    /// </summary>
    private bool CheckInPortData(DeviceVariableInPortDto deviceVariableExPort, int line, DataInPortOutput result)
    {
        if (deviceVariableExPort.Identifier.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 【标识符】 不能是空！ ", Line = line});
            return false;
        }

        if (deviceVariableExPort.Name.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 【名称】 不能是空！ ", Line = line});
            return false;
        }

        if (deviceVariableExPort.TransitionType.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 【转换类型】 不能是空！ ", Line = line});
            return false;
        }

        if (deviceVariableExPort.ValueSource.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 【取值方式】 不能是空！", Line = line});
            return false;
        }

        if (deviceVariableExPort.SendType.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 【上报方式】 不能是空！ ", Line = line});
            return false;
        }

        return true;
    }

    /// <summary>
    ///     检查属性导入必填项-简单模板
    /// </summary>
    private bool CheckInPortDataTem(DeviceVariableExPortTemplateInput deviceVariableExPort, int line, DataInPortOutput result)
    {
        if (deviceVariableExPort.Identifier.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 【标识符】 不能是空！ ", Line = line});
            return false;
        }

        if (deviceVariableExPort.Name.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 【名称】 不能是空！ ", Line = line});
            return false;
        }

        if (deviceVariableExPort.DataType.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 【数据类型】 不能是空！ ", Line = line});
            return false;
        }

        var transitionTypeList = new List<string> {"bit", "bool", "int16", "uint16", "int32", "uint32", "int64", "uint64", "float", "double", "string", "bcd"};
        if (!transitionTypeList.Contains(deviceVariableExPort.DataType))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 【数据类型】 暂时还不支持，建议下拉选择", Line = line});
            return false;
        }

        if (deviceVariableExPort.RegisterAddress.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 【读取地址】 不能是空！", Line = line});
            return false;
        }

        return true;
    }

    #endregion Private
}