using Common.Extension;
using Google.Protobuf.WellKnownTypes;
using MiniExcelLibs.Attributes;

namespace IotGateway.EdgeDevice.DeviceVariables;

/// <summary>
///     设备属性-新增
/// </summary>
public class DeviceVariableAdd : IValidatableObject
{
    /// <summary>
    ///     标识符
    /// </summary>
    [Required(ErrorMessage = "标识符不能为空")]
    [MaxLength(64)]
    public string Identifier { get; set; }

    /// <summary>
    ///     属性名称
    /// </summary>
    [Required(ErrorMessage = "属性名称不能为空")]
    [MaxLength(64)]
    public string Name { get; set; }

    /// <summary>
    ///     长度
    /// </summary>
    public ushort Length { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    ///     优先级
    /// </summary>
    public short Order { get; set; }

    /// <summary>
    ///     为属性值添加自定义
    /// </summary>
    public string Custom { get; set; }

    /// <summary>
    ///     单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    ///     转换数据类型
    /// </summary>
    [Required(ErrorMessage = "请选择转换数据类型")]
    public TransPondDataTypeEnum TransitionType { get; set; }

    /// <summary>
    ///     数据来源
    /// </summary>
    [Required(ErrorMessage = "请选择数据来源")]
    public ValueSourceEnum ValueSource { get; set; }

    /// <summary>
    ///     表达式
    /// </summary>
    public string Expressions { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    public string Script { get; set; }

    /// <summary>
    ///     所属设备
    /// </summary>
    [Required]
    [Range(1, long.MaxValue, ErrorMessage = "设备Id不能是空")]
    public long DeviceId { get; set; }

    /// <summary>
    ///     上报方式
    /// </summary>
    public SendTypeEnum SendType { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    ///     拓展属性
    /// </summary>
    public DeviceVariableEx DeviceVariableEx { get; set; }

    /// <summary>
    ///     批量添加数量
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    ///     采集周期
    /// </summary>
    [Required]
    public int Period { get; set; } = 1000;

    /// <summary>
    ///     强制归档时间(0代表不强制归档)
    /// </summary>
    public int ArchiveTime { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    public List<string> Tags { get; set; }

    /// <summary>
    ///     取值范围配置
    /// </summary>
    public DeviceVariableFilter DeviceVariableFilter { get; set; }

    /// <summary>
    ///     默认值
    /// </summary>
    public string DefaultValue { get; set; }

    /// <summary>
    ///     计算复制-持久化
    /// </summary>
    public bool Persistence { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (ValueSource == ValueSourceEnum.Calculate && Script.IsNull())
            yield return new ValidationResult(
                "脚本内容不能是空！", new[] {nameof(Script)}
            );
    }
}

/// <summary>
///     批量添加设备属性
/// </summary>
public class DeviceVariableBatchAddInput
{
    /// <summary>
    ///     标识符
    /// </summary>
    [Required(ErrorMessage = "标识符不能为空")]
    public string Identifier { get; set; }

    /// <summary>
    ///     属性名称
    /// </summary>
    [Required(ErrorMessage = "属性名称不能为空")]
    public string Name { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    [Required(ErrorMessage = "请选择数据类型")]
    public TransPondDataTypeEnum TransitionType { get; set; }

    /// <summary>
    ///     采集周期
    /// </summary>
    public int Period { get; set; }

    /// <summary>
    ///     上报方式
    /// </summary>
    public SendTypeEnum SendType { get; set; }

    /// <summary>
    ///     拓展属性
    /// </summary>
    public DeviceVariableEx DeviceVariableEx { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    public List<string> Tags { get; set; }

    // /// <summary>
    // /// 取值范围配置
    // /// </summary>
    // public DeviceVariableFilter DeviceVariableFilter { get; set; }
}

/// <summary>
///     复制属性到其他设备
/// </summary>
public class CopyToOtherDeviceInput
{
    /// <summary>
    ///     覆盖保存/忽略保存
    /// </summary>
    public bool OverlaySave { get; set; }

    /// <summary>
    ///     设备属性Id
    /// </summary>
    [Required]
    public List<long> Ids { get; set; }

    /// <summary>
    ///     设备Id
    /// </summary>
    public List<long> DeviceIds { get; set; }
}

/// <summary>
///     设备属性导出-请求参数
/// </summary>
public class DeviceVariableExPortInput : BaseId
{
    /// <summary>
    ///     选中设备属性Id集合
    /// </summary>
    public List<long> DeviceVariableIds { get; set; } = new();
}

/// <summary>
///     批量修改设备属性
/// </summary>
public class DeviceVariableBatchUpdateInput : DeviceVariableBatchAddInput
{
    /// <summary>
    ///     属性Id
    /// </summary>
    [Required(ErrorMessage = "属性Id不能为空")]
    public long Id { get; set; }
}

public class BatchUpdateFieldInput
{
    /// <summary>
    /// 点位Id集合
    /// </summary>
    public List<long> IdList { get; set; }

    /// <summary>
    /// 修改值
    /// </summary>
    public Dictionary<string,object> SetValue { get; set; }
}

/// <summary>
///     删除设备属性请求参数
/// </summary>
public class DeviceVariableDeleteInput
{
    /// <summary>
    ///     设备Id
    /// </summary>
    [Range(1, long.MaxValue, ErrorMessage = "设备Id不能为空")]
    public long DeviceId { get; set; }

    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public List<long> Id { get; set; }
}

/// <summary>
///     设备属性导入-简单模板
/// </summary>
public class DeviceVariableExPortTemplateInput
{
    /// <summary>
    ///     标识符
    /// </summary>
    [ExcelColumnName("标识符")]
    public string Identifier { get; set; }

    /// <summary>
    ///     变量名
    /// </summary>
    [ExcelColumnName("名称")]
    public string Name { get; set; }

    /// <summary>
    ///     读取地址
    /// </summary>
    [ExcelColumnName("读取地址")]
    public string RegisterAddress { get; set; }

    /// <summary>
    ///     读取数据类型
    /// </summary>
    [ExcelColumnName("数据类型")]
    public string DataType { get; set; }
}

/// <summary>
///     设备属性导入
/// </summary>
public class DeviceVariableInPort
{
    /// <summary>
    ///     上传文件
    /// </summary>
    [Required]
    public IFormFile File { get; set; }

    /// <summary>
    ///     1:覆盖原数据,2:忽略
    /// </summary>
    [Required]
    public DeviceVariableInPortTypeEnum InPortType { get; set; }

    /// <summary>
    ///     设备Id
    /// </summary>
    [Required]
    public long DeviceId { get; set; }
}

/// <summary>
///     设备属性导入
/// </summary>
public class DeviceVariableInPortDto
{
    /// <summary>
    ///     标识符
    /// </summary>
    [ExcelColumnName("标识符")]
    public string Identifier { get; set; }

    /// <summary>
    ///     变量名
    /// </summary>
    [ExcelColumnName("名称")]
    public string Name { get; set; }

    /// <summary>
    ///     转换类型
    /// </summary>
    [ExcelColumnName("转换类型")]
    public string TransitionType { get; set; }

    /// <summary>
    ///     取值方式
    /// </summary>
    [ExcelColumnName("取值方式")]
    public string ValueSource { get; set; }

    /// <summary>
    ///     上报方式
    /// </summary>
    [ExcelColumnName("上报方式")]
    public string SendType { get; set; }

    /// <summary>
    ///     取值范围 选填
    /// </summary>
    [ExcelColumnName("取值范围 选填")]
    public string DeviceVariableFilterExtend { get; set; }

    /// <summary>
    ///     驱动配置拓展 选填
    /// </summary>
    [ExcelColumnName("驱动配置拓展 选填")]
    public string VariableExtend { get; set; }

    /// <summary>
    ///     默认值 选填
    /// </summary>
    [ExcelColumnName("默认值 选填")]
    public string DefaultValue { get; set; }

    /// <summary>
    ///     保留长度 选填
    /// </summary>
    [ExcelColumnName("保留长度 选填")]
    public string LengthEx { get; set; }

    /// <summary>
    ///     单位 选填
    /// </summary>
    [ExcelColumnName("单位 选填")]
    public string Unit { get; set; }

    /// <summary>
    ///     表达式 选填
    /// </summary>
    [ExcelColumnName("表达式 选填")]
    public string Expressions { get; set; }

    /// <summary>
    ///     计算赋值(脚本) 选填
    /// </summary>
    [ExcelColumnName("计算赋值(脚本) 选填")]
    public string Script { get; set; }

    /// <summary>
    ///     为属性添加自定义
    /// </summary>
    [ExcelColumnName("属性自定义映射 选填")]
    public string Custom { get; set; }

    /// <summary>
    ///     属性标签 选填
    /// </summary>
    [ExcelColumnName("属性标签 选填")]
    public string Tag { get; set; }

    /// <summary>
    ///     采集周期
    /// </summary>
    [ExcelColumnName("采集周期 选填")]
    public string PeriodEx { get; set; }

    /// <summary>
    ///     强制归档时间
    /// </summary>
    [ExcelColumnName("强制归档时间 选填")]
    public string ArchiveTimeEx { get; set; }

    /// <summary>
    ///     持久化存储(脚本) 选填
    /// </summary>
    [ExcelColumnName("持久化存储(脚本) 选填")]
    public bool Persistence { get; set; }

    /// <summary>
    /// </summary>
    [ExcelColumnName("是否启用")]
    public bool Enable { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [ExcelColumnName("描述 选填")]
    public string Description { get; set; }
}

/// <summary>
///     设备属性列表-请求参数
/// </summary>
public class DeviceVariablePageInput : BasePageInput
{
    /// <summary>
    ///     设备Id
    /// </summary>
    [Required]
    public long DeviceId { get; set; }

    /// <summary>
    ///     转换数据类型
    /// </summary>
    public List<int> TransitionType { get; set; } = new();

    /// <summary>
    ///     数据来源
    /// </summary>
    public List<int> ValueSource { get; set; } = new();

    /// <summary>
    ///     读取状态：1 GOOD ；其他 BAD
    /// </summary>
    public int VariableStatus { get; set; }

    /// <summary>
    ///     上报方式
    /// </summary>
    public List<int> SendType { get; set; } = new();

    /// <summary>
    ///     状态过滤:0全部;1:过滤掉停用的
    /// </summary>
    public int FilterType { get; set; }

    /// <summary>
    ///     协议Id
    /// </summary>
    public long DriverId { get; set; }

    /// <summary>
    ///     属性名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     属性标识
    /// </summary>
    public string Identifier { get; set; }

    /// <summary>
    ///     单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    ///     属性Id集合
    /// </summary>
    public List<long> DeviceVariableId { get; set; } = new();

    /// <summary>
    /// CoilStatus
    /// </summary>
    public string? Partition { get; set; }
}

/// <summary>
///     设备属性下拉
/// </summary>
public class DeviceVariableSelectInput
{
    /// <summary>
    ///     设备Id
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    ///     搜索值
    /// </summary>
    public string SearchValue { get; set; }

    /// <summary>
    ///     数据来源
    /// </summary>
    public int ValueSource { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 读写类型：1只读；2读写；3只写
    /// </summary>
    public int Type { get; set; }
}

/// <summary>
///     设备属性写入
/// </summary>
public class DeviceVariableWriteInput
{
    /// <summary>
    ///     设备Id
    /// </summary>
    [Required]
    public long DeviceId { get; set; }

    /// <summary>
    ///     写入属性值
    /// </summary>
    public Dictionary<string, string> Params { get; set; }
}

/// <summary>
///     设备属性批量启用/禁用
/// </summary>
public class DeviceVariableBatchEnableInput
{
    /// <summary>
    ///     设备Id
    /// </summary>
    [Required]
    public long DeviceId { get; set; }

    /// <summary>
    ///     设备属性Id
    /// </summary>
    [Required]
    public List<long> IdList { get; set; }

    /// <summary>
    ///     true是启用;false是停用
    /// </summary>
    [Required]
    public bool Enable { get; set; }
}

/// <summary>
/// 设备属性集合-
/// </summary>
public class ReadWriteListInput : BaseId
{
    /// <summary>
    /// type：1 只读；2：读写；3：只写
    /// </summary>
    public int Type { get; set; }
}