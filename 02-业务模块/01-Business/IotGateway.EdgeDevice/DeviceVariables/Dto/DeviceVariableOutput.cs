namespace IotGateway.EdgeDevice.DeviceVariables;

/// <summary>
///     设备属性列表
/// </summary>
public class DeviceVariablePageOutput
{
    /// <summary>
    ///     唯一主键
    /// </summary>
    [Description("唯一主键")]
    public long Id { get; set; }

    /// <summary>
    ///     标识符
    /// </summary>
    [Description("标识符")]
    public string Identifier { get; set; }

    /// <summary>
    ///     变量名
    /// </summary>
    [Description("变量名")]
    public string Name { get; set; }

    /// <summary>
    ///     读取数据类型
    /// </summary>
    [Description("读取数据类型")]
    public TransPondDataTypeEnum TransitionType { get; set; }

    /// <summary>
    ///     读取数据类型中文描述
    /// </summary>
    [Description("读取数据类型中文描述")]
    public string TransitionTypeName => EnumUtil.GetEnumDesc(TransitionType);

    /// <summary>
    ///     数据来源
    /// </summary>
    [Description("数据来源")]
    public ValueSourceEnum ValueSource { get; set; }

    /// <summary>
    ///     采集周期
    /// </summary>
    [Description("采集周期")]
    public int Period { get; set; }

    /// <summary>
    ///     数据来源
    /// </summary>
    [Description("数据来源中文描述")]
    public string ValueSourceName => EnumUtil.GetEnumDesc(ValueSource);

    /// <summary>
    ///     表达式
    /// </summary>
    [Description("表达式")]
    public string Expressions { get; set; }

    /// <summary>
    ///     所属设备Id
    /// </summary>
    [Description("所属设备Id")]
    public long DeviceId { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    [Description("是否启用")]
    public bool Enable { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    [SugarColumn(IsJson = true)]
    [Description("标签")]
    public List<string> Tags { get; set; }

    /// <summary>
    /// </summary>
    [SugarColumn(IsJson = true)]
    [Description("属性扩展信息")]
    public DeviceVariableEx DeviceVariableEx { get; set; }

    /// <summary>
    ///     上送方式
    /// </summary>
    [Description("上送方式")]
    public SendTypeEnum SendType { get; set; }

    /// <summary>
    ///     是否是系统属性
    /// </summary>
    [Description("是否是系统属性")]
    public bool IsSystem { get; set; }

    /// <summary>
    ///     设备名称
    /// </summary>
    [Description("设备名称")]
    public string DeviceName { get; set; }
    
    /// <summary>
    ///     描述
    /// </summary>
    [Description("描述")]
    public string? Description { get; set; }
}
