using Furion.ClayObject.Extensions;
using Furion.RemoteRequest.Extensions;
using IotGateway.Application.Entity;
using DateTime = System.DateTime;

namespace IotGateway.EdgeDevice.TransPonds.SendToHttp;

/// <summary>
///     Http实时发送数据
/// </summary>
public class SendToHttpClient : IDisposable
{
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private readonly SendMessageService _send;
    private readonly TransPond _transPond;
    private IServiceProvider Services { get; }

    /// <summary>
    ///     脚本执行
    /// </summary>
    private readonly EngineSingletonService _engine;

    /// <summary>
    ///     记录每台设备最后一次上报时间
    /// </summary>
    private readonly ConcurrentDictionary<long, DateTime> _lastSendTime = new();

    public SendToHttpClient(TransPond transPond, IServiceProvider services, SendMessageService send)
    {
        _send = send;
        _engine = services.GetService<EngineSingletonService>();
        _transPond = transPond;
        Services = services;
        _ = Subscribe();
    }

    /// <summary>
    ///     slave 订阅
    /// </summary>
    private async Task Subscribe()
    {
        var cancellationToken = _cancellationTokenSource.Token;
        await MessageCenter.Subscribe(EventConst.SendDeviceData, async context =>
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    return;

                // 发送消息
                var data = context.GetPayload<PayLoad>();
                await PushDataBase(data);
            }
            catch (Exception ex)
            {
                await _send.DeviceResp($"【Http实时数据】 Error:【{ex.Message}】", _transPond.Identifier);
            }

            await Task.CompletedTask;
        }, cancellationToken: cancellationToken);
    }

    /// <summary>
    ///     将消息发送到Http地址
    /// </summary>
    /// <returns></returns>
    private async Task PushDataBase(PayLoad payLoad)
    {
        if (_transPond.HttpConfModel == null) return;

        // 业务代码
        try
        {
            // 根据配置策略过滤数据
            var send = await Filter(payLoad);
            if (!send)
                return;
            // 数据组成统一格式
            var variableSendToPlatform = CreateVariableSendToPlatform(payLoad);
            //发送的数据
            object sendData = variableSendToPlatform;

            //使用脚本解析方式自行处理数据
            if (_transPond.HttpConfModel.DataParsingRuleType == DataParsingRuleTypeEnum.Script) sendData = ParseDataWithScript(variableSendToPlatform);
            // 生成请求头
            var dicHeaders = GetHeaders(sendData);
            // 设置超时时间
            var httpClient = CreateHttpClient();
            // 设置请求方式
            var httpMethod = _transPond.HttpConfModel.HttpType == HttpTypeEnum.Get ? HttpMethod.Get : HttpMethod.Post;
            // 设置请求参数
            var res = await SendHttpRequest(dicHeaders, sendData, httpClient, httpMethod);

            await _send.DeviceResp($"设备:【{payLoad.DeviceName}】,【Http】请求成功,返回内容:【{res}】", _transPond.Identifier);
        }
        catch (Exception ex)
        {
            await _send.DeviceResp($"【Http实时数据】 Error:【{ex.Message}】", _transPond.Identifier);
        }

        await Task.CompletedTask;
    }

    /// <summary>
    ///  组装成统一的格式
    /// </summary>
    /// <param name="payLoad"></param>
    /// <returns></returns>
    private VariableSendToPlatform CreateVariableSendToPlatform(PayLoad payLoad)
    {
        var variableSendToPlatform = new VariableSendToPlatform
        {
            ParentTime = payLoad.Ts, DeviceName = payLoad.DeviceName, DriverName = payLoad.DriverName,
            Params = payLoad.Values.ToDictionary(kv => kv.Key, kv =>
                new TransPondParamValue {Value = kv.Value.Value, Time = kv.Value.ReadTime, DataType = kv.Value.TransitionType})
        };

        return variableSendToPlatform;
    }

    /// <summary>
    /// 执行脚本
    /// </summary>
    /// <param name="variableSendToPlatform"></param>
    /// <returns></returns>
    private object ParseDataWithScript(VariableSendToPlatform variableSendToPlatform)
    {
        lock (this)
        {
            _engine.Engine.SetValue("payload", variableSendToPlatform);
            var getValue = _engine.Engine.Evaluate(_transPond.HttpConfModel.Content ?? "", new ScriptParsingOptions {Tolerant = true,AllowReturnOutsideFunction = true}).ToObject();
            return getValue ?? variableSendToPlatform;
        }
    }

    /// <summary>
    /// 参数请求头
    /// </summary>
    /// <param name="sendData"></param>
    /// <returns></returns>
    private IDictionary<string, object> GetHeaders(object sendData)
    {
        IDictionary<string, object> dicHeaders;

        switch (_transPond.HttpConfModel?.HeaderType)
        {
            case HeaderTypeEnum.Static:
                dicHeaders = DictionaryExtensions.ToDictionary(_transPond.HttpConfModel.Headers);
                break;
            case HeaderTypeEnum.Script:
                lock (this)
                {
                    _engine.Engine.SetValue("payload", sendData);
                    var getValue = _engine.Engine.Evaluate(_transPond.HttpConfModel.Headers.ToString() ?? string.Empty, new ScriptParsingOptions {Tolerant = true, AllowReturnOutsideFunction = true}).ToObject();
                    dicHeaders = getValue?.ToString()?.ToObject<Dictionary<string, object>>() ?? new Dictionary<string, object>();
                }

                break;
            default:
                dicHeaders = new Dictionary<string, object>();
                break;
        }

        return dicHeaders;
    }

 
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    private HttpClient CreateHttpClient()
    {
        if (_transPond.HttpConfModel == null)
            throw Oops.Oh("配置异常！");
        return new HttpClient {Timeout = TimeSpan.FromSeconds(_transPond.HttpConfModel.TimeOut)};
    }

    /// <summary>
    /// 发送Http请求
    /// </summary>
    /// <param name="dicHeaders"></param>
    /// <param name="sendData"></param>
    /// <param name="httpClient"></param>
    /// <param name="httpMethod"></param>
    /// <returns></returns>
    private async Task<string> SendHttpRequest(IDictionary<string, object> dicHeaders, object sendData, HttpClient httpClient, HttpMethod httpMethod)
    {
        if (_transPond.HttpConfModel == null)
            throw Oops.Oh("配置异常！");
        var request = _transPond.HttpConfModel.Url.SetHttpMethod(httpMethod).SetHeaders(dicHeaders).SetClient(() => httpClient).SetContentEncoding(Encoding.UTF8).SetRequestScoped(Services);

        if (httpMethod == HttpMethod.Get)
            request = request.SetQueries(sendData)
                .SetContentType("application/json");
        else
            request = request.SetBody(sendData)
                .SetContentType("application/json");

        var res = await request.SendAsStringAsync();
        return res;
    }
    
    /// <summary>
    ///     根据规则过滤条件
    /// </summary>
    /// <param name="payLoad"></param>
    private async Task<bool> Filter(PayLoad payLoad)
    {
        switch (_transPond.HttpConfModel?.SendType)
        {
            case TransPondSendTypeEnum.Always:
                payLoad.Values = FilterByAlways(payLoad.Values);
                break;
            case TransPondSendTypeEnum.PubPeriod:
                if (!IsReachedPubPeriod(payLoad)) return false;
                payLoad.Values = FilterByAlways(payLoad.Values);
                break;
            case TransPondSendTypeEnum.Changed:
                payLoad.Values = FilterByChanged(payLoad.Values);
                break;
        }

        return true;
    }

    /// <summary>
    /// 实时上报
    /// </summary>
    /// <param name="values"></param>
    /// <returns></returns>
    private Dictionary<string, ParamValue> FilterByAlways(Dictionary<string, ParamValue> values)
    {
        return values.Where(i => i.Value.Value != null).ToDictionary(i => i.Key, i => i.Value);
    }

    /// <summary>
    /// 周期上报
    /// </summary>
    /// <param name="payLoad"></param>
    /// <returns></returns>
    private bool IsReachedPubPeriod(PayLoad payLoad)
    {
        var nowTime = DateTime.Now;
        if (_lastSendTime.ContainsKey(payLoad.DeviceId))
        {
            var pubPeriod = _transPond.HttpConfModel?.PubPeriodUnit switch
            {
                "毫秒" => _transPond.HttpConfModel.PubPeriod,
                "秒" => _transPond.HttpConfModel.PubPeriod * 1000,
                "分钟" => _transPond.HttpConfModel.PubPeriod * 1000 * 60,
                _ => _transPond.HttpConfModel?.PubPeriod * 1000 * 60 * 60
            };
            
            if ((nowTime - _lastSendTime[payLoad.DeviceId]).TotalMilliseconds < pubPeriod) return false;
            _lastSendTime[payLoad.DeviceId] = nowTime;
        }
        else
        {
            _lastSendTime.TryAdd(payLoad.DeviceId, DateTime.Now);
        }

        return true;
    }

    /// <summary>
    /// 变化上报
    /// </summary>
    /// <param name="values"></param>
    /// <returns></returns>
    private Dictionary<string, ParamValue> FilterByChanged(Dictionary<string, ParamValue> values)
    {
        return values.Where(i => (i.Value.Value != null && i.Value.CookieValue?.ToString() != i.Value.Value) || (i.Value.CookieValue == null && i.Value.Value != null))
            .ToDictionary(i => i.Key, i => i.Value);
    }
    
    public void Dispose()
    {
        _cancellationTokenSource.Cancel();
        _cancellationTokenSource.Dispose();
    }
}