using IotGateway.Application.Entity;

namespace IotGateway.EdgeDevice.TransPonds;

/// <summary>
/// </summary>
public class TransPondHostedService : IHostedService, IDisposable
{
    /// <summary>
    ///     Sqlsugar单例服务
    /// </summary>
    private readonly ISqlSugarClient _db;

    private readonly TransPondEventSubscriber _eventSubscriber;

    public TransPondHostedService(ISqlSugarClient db, TransPondEventSubscriber eventSubscriber)
    {
        _db = db;
        _eventSubscriber = eventSubscriber;
    }

    public void Dispose()
    {
        _db?.Dispose();
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        await TaskQueued.EnqueueAsync(async (provider, token) =>
        {
            try
            {
                var transPonds = await _db.Queryable<TransPond>().Where(x => x.Enable)
                    .Includes(w => w.TransPondTopic)
                    .ToListAsync(cancellationToken);

                foreach (var transPond in transPonds)
                    try
                    {
                        await _eventSubscriber.Create(transPond);
                    }
                    catch (Exception e)
                    {
                        Log.Error($"转发标识[{transPond.Identifier}] 异常：" + e.Message);
                    }
            }
            catch (Exception ex)
            {
                Log.Error($"转发配置异常:{ex.Message}");
            }
        });
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }
}