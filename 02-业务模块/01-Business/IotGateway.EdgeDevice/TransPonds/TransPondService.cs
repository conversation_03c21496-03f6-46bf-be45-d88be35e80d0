using Feng.IotGateway.Application.TransPonds.Dto;
using IotGateway.Application.Entity;
using IotGateway.Application.TransPonds.Dto;

namespace IotGateway.EdgeDevice.TransPonds;

/// <summary>
///     转发配置
/// </summary>
[ApiDescriptionSettings("转发配置")]
public class TransPondService : ITransient, IDynamicApiController
{
    private readonly MasterClient _master;
    private readonly SqlSugarRepository<TransPond> _transPond;
    private readonly TransPondEventSubscriber _eventSubscriber;

    public TransPondService(SqlSugarRepository<TransPond> transPond, MasterClient master, TransPondEventSubscriber eventSubscriber)
    {
        _transPond = transPond;
        _master = master;
        _eventSubscriber = eventSubscriber;
    }

    #region Get

    /// <summary>
    ///     转发最近操作记录
    /// </summary>
    /// <returns></returns>
    [HttpGet("/transPond/runStatusLine")]
    public async Task<List<RunRecordLine>> RunStatusLine([FromQuery] BaseId input)
    {
        var transPond = await _transPond.GetSingleAsync(f => f.Id == input.Id);
        if (transPond == null)
            throw Oops.Oh(ErrorCode.Tra404);
        if (transPond.TransPondType != TransPondTypeEnum.Mqtt) return new List<RunRecordLine>();
        if (transPond.Master)
            return _master.Timelines != null ? _master.Timelines.OrderByDescending(u => u.Time).ToList() : new List<RunRecordLine>();
        if (!_eventSubscriber.SlaveClients.ContainsKey(transPond.Id)) return new List<RunRecordLine>();
        var slave = _eventSubscriber.SlaveClients[transPond.Id];
        return slave.Timelines.OrderByDescending(u => u.Time).ToList();
    }

    /// <summary>
    ///     转发MQTT日志记录
    /// </summary>
    /// <returns></returns>
    [HttpGet("/transPond/recordLine")]
    public async Task<List<RunRecordLine>> RecordLine([FromQuery] BaseId input)
    {
        var transPond = await _transPond.GetSingleAsync(f => f.Id == input.Id);
        if (transPond == null)
            throw Oops.Oh(ErrorCode.Tra404);
        if (transPond.TransPondType != TransPondTypeEnum.Mqtt) return new List<RunRecordLine>();
        return transPond.Master
            ? _master.RecordLines.OrderByDescending(u => u.Time).ToList()
            : new List<RunRecordLine>();
    }

    /// <summary>
    ///     全部转发配置（不分页）
    /// </summary>
    /// <returns></returns>
    [HttpGet("/transPond/list")]
    public async Task<List<TransPond>> List()
    {
        var transPondList = await _transPond.AsQueryable().ToListAsync();
        foreach (var transPond in transPondList)
        {
            if (!transPond.Enable)
            {
                transPond.IsConnected = false;
                continue;
            }

            switch (transPond.TransPondType)
            {
                case TransPondTypeEnum.Mqtt:
                    if (transPond.Master)
                    {
                        transPond.IsConnected = _master?.IsConnected ?? false;
                    }
                    else
                    {
                        if (_eventSubscriber.SlaveClients.TryGetValue(transPond.Id, out var slaveClient))
                            transPond.IsConnected = slaveClient.IsConnected;
                    }

                    break;
                case TransPondTypeEnum.Sql:
                case TransPondTypeEnum.Http:
                    transPond.IsConnected = transPond.Enable;
                    break;
                case TransPondTypeEnum.OpcUa:
                    if (_eventSubscriber.OpcUaClients.TryGetValue(transPond.Id, out var client))
                        transPond.IsConnected = client.IsConnected;
                    break;
            }
        }

        return transPondList;
    }

    /// <summary>
    ///     转发配置属性详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/transPond/detail")]
    public async Task<TransPond> Detail([FromQuery] BaseId input)
    {
        var transPond = await _transPond.GetSingleAsync(f => f.Id == input.Id);
        if (transPond == null)
            throw Oops.Oh(ErrorCode.Tra404);
        if (!transPond.Enable)
            transPond.IsConnected = false;

        switch (transPond.TransPondType)
        {
            case TransPondTypeEnum.Mqtt:
                if (transPond.Master)
                {
                    transPond.IsConnected = _master?.IsConnected ?? false;
                }
                else
                {
                    if (_eventSubscriber.SlaveClients.TryGetValue(transPond.Id, out var slaveClient))
                        transPond.IsConnected = slaveClient.IsConnected;
                }

                break;
            case TransPondTypeEnum.Sql:
            case TransPondTypeEnum.Http:
                transPond.IsConnected = transPond.Enable;
                break;
            case TransPondTypeEnum.OpcUa:
                if (_eventSubscriber.OpcUaClients.TryGetValue(transPond.Id, out var client))
                    transPond.IsConnected = client.IsConnected;
                break;
        }

        return transPond;
    }

    /// <summary>
    ///     转发Topic分类（不分页）
    /// </summary>
    /// <returns></returns>
    [HttpGet("/transPond/Topic/list")]
    public async Task<List<TransPondTopic>> TopicList([FromQuery] TransPondTopicListInput input)
    {
        var transPond = await _transPond.AsQueryable().FirstAsync(f => f.Id == input.TransPondId);
        if (transPond == null)
            throw Oops.Oh("转发配置已被删除,刷新后重试！");
        var transPondTopicList = await _transPond.AsSugarClient().Queryable<TransPondTopic>()
            .Where(w => w.TransPondId == input.TransPondId)
            .Where(w => w.TransPondTopicType == input.TransPondTopicType)
            .ToListAsync();
        return transPondTopicList;
    }

    /// <summary>
    ///     转发topic类型分页
    /// </summary>
    /// <returns></returns>
    [HttpGet("/transPond/Topic/groupType/list")]
    public async Task<Dictionary<int, string>> TopicGroupTypeList([FromQuery] TopicGroupTypeListInput input)
    {
        Dictionary<int, string> output = new();
        if (input.TransPondTopicType == TransPondTopicTypeEnum.Pub)
        {
            output.Add((int)TransPondTopicPurposeEnum.OnLine, "实时数据上报");
            output.Add((int)TransPondTopicPurposeEnum.OffLine, "离线数据上报");
            output.Add((int)TransPondTopicPurposeEnum.Meta, "设备配置上报");
            output.Add((int)TransPondTopicPurposeEnum.SysTime, "发起时间同步");
            output.Add((int)TransPondTopicPurposeEnum.Heartbeat, "推送心跳包");
        }
        else
        {
            output.Add((int)TransPondTopicPurposeEnum.Variable, "属性下写");
            output.Add((int)TransPondTopicPurposeEnum.SysTimeSub, "时钟同步响应");
            output.Add((int)TransPondTopicPurposeEnum.Cmd, "指令下发");
        }

        return output;
    }

    #endregion Get

    #region POST

    /// <summary>
    ///     创建转发配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/transPond/add")]
    public async Task<long> Add(TransPondAdd input)
    {
        //
        if (await _transPond.IsAnyAsync(a => a.Identifier == input.Identifier)) throw Oops.Oh(ErrorCode.Tra402);
        var transPond = input.Adapt<TransPond>();
        transPond.Id = YitIdHelper.NextId();
        //默认第一个是master节点
        if (!await _transPond.IsAnyAsync(a => a.Master) && input.TransPondType == TransPondTypeEnum.Mqtt)
            transPond.Master = true;

        switch (transPond.TransPondType)
        {
            case TransPondTypeEnum.Mqtt:
                {
                    transPond.Config = JSON.Serialize(input.MqttConfModel);
                    transPond.TransPondTopic ??= new List<TransPondTopic>();
                    transPond.TransPondTopic = InitTransPondTopic(transPond, input.MqttConfModel);
                }
                break;
            case TransPondTypeEnum.Http:
                transPond.Config = JSON.Serialize(input.HttpConfModel);
                break;
            case TransPondTypeEnum.Sql:
                transPond.Config = JSON.Serialize(input.SqlConfModel);
                break;
            case TransPondTypeEnum.OpcUa:
                transPond.Config = JSON.Serialize(input.OpcUaConfModel);
                break;
        }

        await _transPond.AsSugarClient().InsertNav(transPond).Include(w => w.TransPondTopic).ExecuteCommandAsync();

        if (!transPond.Enable) return transPond.Id; //动态创建一个订阅

        if (transPond.TransPondType == TransPondTypeEnum.Mqtt && transPond.Master)
            _master.ReConnect(); //重置master节点

        await _eventSubscriber.Create(transPond);
        return transPond.Id;
    }

    /// <summary>
    ///     修改转发配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/transPond/update")]
    public async Task Update(TransPondUpdate input)
    {
        //
        if (await _transPond.IsAnyAsync(a => a.Identifier == input.Identifier && a.Id != input.Id))
            throw Oops.Oh(ErrorCode.Tra402);
        var transPond = await _transPond.AsQueryable().Where(f => f.Id == input.Id)
            .Includes(w => w.TransPondTopic)
            .FirstAsync();
        if (transPond == null)
            throw Oops.Oh(ErrorCode.Tra404);
        switch (transPond.TransPondType)
        {
            case TransPondTypeEnum.Mqtt:
                {
                    transPond.Config = JSON.Serialize(input.MqttConfModel);
                    // Topic，保证配置都是最新的
                    transPond.TransPondTopic.AddRange(InitTransPondTopic(transPond, input.MqttConfModel));
                    break;
                }
            case TransPondTypeEnum.Http:
                transPond.Config = JSON.Serialize(input.HttpConfModel);
                break;
            case TransPondTypeEnum.Sql:
                transPond.Config = JSON.Serialize(input.SqlConfModel);
                break;
            case TransPondTypeEnum.OpcUa:
                transPond.Config = JSON.Serialize(input.OpcUaConfModel);
                break;
        }

        transPond.Enable = input.Enable;
        await _transPond.AsSugarClient().UpdateNav(transPond).Include(w => w.TransPondTopic).ExecuteCommandAsync();

        // 停止服务
        await _eventSubscriber.Stop(transPond);

        if (transPond.Enable) // 动态创建一个订阅
            switch (transPond.TransPondType)
            {
                case TransPondTypeEnum.Mqtt:
                    if (transPond.Master)
                        _master.ReConnect(); // 重置master节点
                    await _eventSubscriber.Create(transPond);
                    break;
                case TransPondTypeEnum.Http:
                case TransPondTypeEnum.Sql:
                case TransPondTypeEnum.OpcUa:
                    await _eventSubscriber.Create(transPond);
                    break;
            }
        else
            switch (transPond.TransPondType)
            {
                case TransPondTypeEnum.Mqtt:
                    if (transPond.Master)
                        _master.StopMaster(); // 停止master节点
                    break;
            }
    }

    /// <summary>
    ///     将Slave的MQTT设置成Master
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/transPond/setMaster")]
    public async Task SetMaster(BaseId input)
    {
        if (await _transPond.IsAnyAsync(a => a.Master == true && a.Id != input.Id))
            throw Oops.Oh("Master已存在！");
        var transPond = await _transPond.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (transPond == null)
            throw Oops.Oh(ErrorCode.Tra404);
        if (transPond.TransPondType != TransPondTypeEnum.Mqtt)
            throw Oops.Oh("暂不支持!");

        if (transPond.Master == false)
        {
            // 停止服务
            await _eventSubscriber.Stop(transPond);
            transPond.Master = true;
            await _transPond.UpdateAsync(transPond);
            // 动态创建一个订阅
            if (transPond.Enable)
                await _eventSubscriber.Create(transPond);
        }
    }

    /// <summary>
    ///     转发配置启用/禁用
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("/transPond/enable")]
    public async Task Enable(EnableInput<long> input)
    {
        var transPond = await _transPond.AsQueryable().Where(f => f.Id == input.Id)
            .Includes(w => w.TransPondTopic)
            .FirstAsync();
        if (transPond == null)
            throw Oops.Oh(ErrorCode.Tra404);

        switch (transPond.TransPondType)
        {
            case TransPondTypeEnum.Mqtt:
                {
                    var mqttConfModel = JSON.Deserialize<MqttConfModel>(transPond.Config);
                    // 初始化Topic，保证配置都是最新的
                    transPond.TransPondTopic.AddRange(InitTransPondTopic(transPond, mqttConfModel));
                    break;
                }
        }

        transPond.Enable = input.Enable;
        await _transPond.AsSugarClient().UpdateNav(transPond).Include(w => w.TransPondTopic).ExecuteCommandAsync();
        // 停止服务
        await _eventSubscriber.Stop(transPond);

        if (transPond.Enable) // 动态创建一个订阅
            switch (transPond.TransPondType)
            {
                case TransPondTypeEnum.Mqtt:
                    if (transPond.Master)
                        _master.ReConnect(); // 重置master节点
                    await _eventSubscriber.Create(transPond);
                    break;
                case TransPondTypeEnum.Http:
                case TransPondTypeEnum.Sql:
                case TransPondTypeEnum.OpcUa:
                    await _eventSubscriber.Create(transPond);
                    break;
            }
        else
            switch (transPond.TransPondType)
            {
                case TransPondTypeEnum.Mqtt:
                    if (transPond.Master)
                        _master.StopMaster(); //停止master节点
                    break;
            }
    }

    /// <summary>
    ///     转发配置删除
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("/transPond/delete")]
    public async Task Delete(BaseId input)
    {
        var transPond = await _transPond.AsQueryable()
            .Where(w => w.Id == input.Id)
            .Includes(w => w.TransPondTopic)
            .FirstAsync();

        if (transPond == null)
            throw Oops.Oh(ErrorCode.Tra404);
        await _transPond.AsSugarClient().DeleteNav(transPond)
            .Include(w => w.TransPondTopic)
            .ExecuteCommandAsync();

        if (transPond.Master)
            _master.StopMaster(); // 停用master节点
        // 停止服务
        await _eventSubscriber.Stop(transPond);
    }

    /// <summary>
    ///     新增转发Topic配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/transPond/Topic/add")]
    public async Task TopicAdd(TransPondTopicAddInput input)
    {
        var transPond = await _transPond.GetSingleAsync(f => f.Id == input.TransPondId);
        if (transPond == null)
            throw Oops.Oh(ErrorCode.Tra404);
        if (await _transPond.AsSugarClient().Queryable<TransPondTopic>().AnyAsync(a =>
                a.TransPondId == input.TransPondId && a.TransPondTopicPurpose == input.TransPondTopicPurpose))
            throw Oops.Oh("不能同时添加多个相同用途的Topic！");
        var transPondTopic = input.Adapt<TransPondTopic>();
        transPondTopic.Config ??= "";
        transPondTopic.Description ??= "";
        await _transPond.AsSugarClient().Insertable(transPondTopic).ExecuteCommandAsync();

        if (transPond.Enable) // 动态创建一个订阅
            switch (transPond.TransPondType)
            {
                case TransPondTypeEnum.Mqtt:
                    if (transPond.Master)
                        await _master.ResetTopic(); // 重置master的topic
                    break;
            }
    }

    /// <summary>
    ///     修改转发Topic配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/transPond/Topic/update")]
    public async Task TopicUpdate(TransPondTopicUpdateInput input)
    {
        var transPond = await _transPond.GetSingleAsync(f => f.Id == input.TransPondId);
        if (transPond == null)
            throw Oops.Oh(ErrorCode.Tra404);

        var transPondTopic =
            await _transPond.AsSugarClient().Queryable<TransPondTopic>().FirstAsync(f => f.Id == input.Id);
        if (transPondTopic == null)
            throw Oops.Oh("修改Topic不存在,请刷新重试！");

        transPondTopic.Topic = input.Topic;
        transPondTopic.Description = input.Description;
        transPondTopic.Rule = input.Rule;
        transPondTopic.Qos = input.Qos;
        transPondTopic.TimeOut = input.TimeOut;
        await _transPond.AsSugarClient().Updateable(transPondTopic).UpdateColumns(w =>
            new { w.Topic, w.Description, w.Rule, w.Qos, w.TimeOut }).ExecuteCommandAsync();

        if (transPond.Enable) // 动态创建一个订阅
            switch (transPond.TransPondType)
            {
                case TransPondTypeEnum.Mqtt:
                    if (transPond.Master)
                        await _master.ResetTopic(); // 重置master的topic
                    break;
            }
    }

    /// <summary>
    ///     删除转发Topic配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/transPond/Topic/delete")]
    public async Task TopicDelete(TopicDeleteInput input)
    {
        var transPond = await _transPond.GetSingleAsync(f => f.Id == input.TransPondId);
        if (transPond == null)
            throw Oops.Oh(ErrorCode.Tra404);

        var transPondTopic = await _transPond.AsSugarClient().Queryable<TransPondTopic>().FirstAsync(f => f.Id == input.Id);
        if (transPondTopic == null)
            throw Oops.Oh("修改Topic不存在,请刷新重试！");

        await _transPond.AsSugarClient().Deleteable(transPondTopic).ExecuteCommandAsync();

        if (transPond.Enable) // 动态创建一个订阅
            switch (transPond.TransPondType)
            {
                case TransPondTypeEnum.Mqtt:
                    if (transPond.Master)
                        await _master.ResetTopic(); // 重置master的topic
                    break;
            }
    }

    /// <summary>
    ///     修改Topic的解析配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/transPond/Topic/setConfig")]
    public async Task SetTopicConfig(SetTopicConfigInput input)
    {
        var transPond = await _transPond.GetSingleAsync(f => f.Id == input.TransPondId);
        if (transPond == null)
            throw Oops.Oh(ErrorCode.Tra404);

        var transPondTopic =
            await _transPond.AsSugarClient().Queryable<TransPondTopic>().FirstAsync(f => f.Id == input.Id);
        if (transPondTopic == null)
            throw Oops.Oh("修改Topic不存在,请刷新重试！");

        transPondTopic.Config = input.Config;
        await _transPond.AsSugarClient().Updateable(transPondTopic).UpdateColumns(w => w.Config).ExecuteCommandAsync();

        if (transPond.Enable) // 动态创建一个订阅
            switch (transPond.TransPondType)
            {
                case TransPondTypeEnum.Mqtt:
                    if (transPond.Master)
                        await _master.ResetTopic(); // 重置master的topic
                    break;
            }
    }

    #endregion

    /// <summary>
    ///     转发扩展方法-预览Sql
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/transPond/Extension/preview/sql")]
    public async Task<string> PreviewSql(PreviewSqlInput input)
    {
        if (string.IsNullOrEmpty(input.Table))
            throw Oops.Oh("请输入数据库表名称!");

        if (!input.Params.Any())
            throw Oops.Oh("请填写数据库字段!");

        var sql = $"INSERT INTO\n `{input.Table}` (";
        var values = "\nVALUES\n (";
        foreach (var param in input.Params)
        {
            sql += param.FiledName + ",";
            switch (param.DataType)
            {
                case "int":
                case "bool":
                    values += param.Value + ",";
                    break;
                case "string":
                    values += "'" + param.Value + "'" + ",";
                    break;
                default:
                    throw Oops.Oh("暂不支持数据类型!");
            }
        }

        values = values.TrimEnd(',') + ");";
        sql = sql.TrimEnd(',') + ") " + values;
        return sql;
    }

    #region 私有方法

    /// <summary>
    ///     初始化各平台Topic
    /// </summary>
    /// <param name="transPond"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    private List<TransPondTopic> InitTransPondTopic(TransPond transPond, MqttConfModel input)
    {
        switch (input.IoTPlatformType)
        {
            case IoTPlatformType.ELink:
                {
                    return CreateELink(transPond);
                }
            case IoTPlatformType.SupOS:
                {
                    var authToken = input.MqttConfigExtend.FirstOrDefault(f => f.Name == "AuthToken")?.Value;
                    return CreateSupOs(transPond, authToken);
                }
            case IoTPlatformType.RootCloud:
                {
                    return CreateRootCloud(transPond);
                }
            case IoTPlatformType.IotSuite:
                {
                    return CreateIotSuite(transPond);
                }
            default:
                return new List<TransPondTopic>();
        }
    }

    /// <summary>
    ///     ELink平台Topic
    /// </summary>
    /// <param name="transPond"></param>
    /// <returns></returns>
    private List<TransPondTopic> CreateELink(TransPond transPond)
    {
        var topicList = new List<TransPondTopic>();
        // 已经配置过的Topic
        var transPondTopicList = transPond.TransPondTopic.Select(s => s.TransPondTopicPurpose).ToList();

        // // 已过时
        // if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.Meta))
        //     topicList.Add(new TransPondTopic
        //     {
        //         Topic = $"{transPond.Identifier}/meta/pub",
        //         Description = "设备属性上报",
        //         TransPond = transPond,
        //         TransPondTopicType = TransPondTopicTypeEnum.Pub,
        //         TransPondTopicPurpose = TransPondTopicPurposeEnum.Meta,
        //         Config = ""
        //     });

        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.OffLine))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/offline/data/pub",
                Description = "离线数据上报",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.OffLine,
                Config =
                    "/**\r\n* 将设备自定义topic数据转换为json格式数据, 设备上报数据到物联网平台时调用\r\n* 入参：rawData 发送源数据对象 不能为空\r\n* 出参：jsonObj JSON 对象 不能为空\r\n*/\r\nfunction transformPayload(rawData) {\r\n    var jsonObj = JSON.stringify(rawData) ;\r\n    return jsonObj;\r\n}\r\n\r\nvar data = payload;\r\ntransformPayload(data);"
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.OnLine))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/online/data/pub",
                Description = "实时数据上报",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.OnLine,
                Config =
                    "/**\r\n* 将设备自定义topic数据转换为json格式数据, 设备上报数据到物联网平台时调用\r\n* 入参：rawData 发送源数据对象 不能为空\r\n* 出参：jsonObj JSON 对象 不能为空\r\n*/\r\nfunction transformPayload(rawData) {\r\n    var jsonObj = JSON.stringify(rawData) ;\r\n    return jsonObj;\r\n}\r\n\r\nvar data = payload;\r\ntransformPayload(data);"
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.SysTime))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/SystemServices/broker/uptime",
                Description = "时钟同步请求",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.SysTime,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.PubOta))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/resp/post/gateway/ota/cmd",
                Description = "Ota响应",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.PubOta,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.SysTimeSub))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/SystemServices/broker/uptime_reply",
                Description = "时钟同步响应",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.SysTimeSub,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.Variable))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/v1/p/write",
                Description = "云端下写属性",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.Variable,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.Share))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/v1/p/share",
                Description = "云端下写标签",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.Share,
                Config = ""
            });

        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.SubOta))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/post/gateway/ota/cmd",
                Description = "ota升级",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.SubOta,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.Cmd))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/v1/p/cmd",
                Description = "指令下发",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.Cmd,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.DeviceCmd))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/v1/p/device/cmd",
                Description = "设备指令下发",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.DeviceCmd,
                Config = ""
            });

        return topicList;
    }

    /// <summary>
    ///     SupOS平台Topic
    /// </summary>
    /// <param name="transPond">转发配置</param>
    /// <param name="authToken">token</param>
    /// <returns></returns>
    private List<TransPondTopic> CreateSupOs(TransPond transPond, string authToken)
    {
        var topicList = new List<TransPondTopic>();
        // 已经配置过的Topic
        var transPondTopicList = transPond.TransPondTopic.Select(s => s.TransPondTopicPurpose).ToList();
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.Meta))
        {
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/metatag/retain",
                Description = "设备属性上报",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.Meta,
                Config = ""
            });
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/metatag/report",
                Description = "网关主动刷新位号元数据（V1.1及以上）",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.Meta,
                Config = ""
            });
        }

        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.OnLine))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/rtdvalue/report",
                Description = "实时数据上报",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.OnLine,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.SysTime))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/rtdevent/get",
                Description = "时钟同步请求",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.SysTime,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.OffLine))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/cachevalue/report",
                Description = "离线数据上报",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.OffLine,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.MetaPush))
        {
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/metatag/push",
                Description = "云端要求同步设备属性",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.MetaPush,
                Config = ""
            });
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/metanotify/push",
                Description = "服务端要求刷新网关位号数据(v1.1及以上支持)",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.MetaPush,
                Config = ""
            });
        }
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.SysTimeSub))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/rtdevent/get_reply",
                Description = "时钟同步响应",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.SysTimeSub,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.Variable))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/rtdnotify/push",
                Description = "属性下写",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.Variable,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.MetaPushReply))
        {
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/metatag/push_reply",
                Description = "云端要求同步设备属性回复",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.MetaPushReply,
                Config = ""
            });
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/metanotify/push_reply",
                Description = "服务端要求刷新网关位号数据响应服务端(v1.1及以上支持)",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.MetaPushReply,
                Config = ""
            });
        }
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.CloudRequestRefreshRealTimeDeviceData))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/rtdvalue/push",
                Description = "云端要求刷新设备实时数据",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.CloudRequestRefreshRealTimeDeviceData,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.CloudRequestRefreshRealTimeDeviceDataReply))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/rtdvalue/push_reply",
                Description = "云端要求刷新设备实时数据回复",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.CloudRequestRefreshRealTimeDeviceDataReply,
                Config = ""
            });
        return topicList;
    }

    /// <summary>
    ///     RootCloud平台Topic
    /// </summary>
    /// <param name="transPond"></param>
    /// <returns></returns>
    private List<TransPondTopic> CreateRootCloud(TransPond transPond)
    {
        var topicList = new List<TransPondTopic>();
        // 已经配置过的Topic
        var transPondTopicList = transPond.TransPondTopic.Select(s => s.TransPondTopicPurpose).ToList();
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.OnLine))
            topicList.Add(new TransPondTopic
            {
                Topic = "v4/p/post/thing/live/json/1.1",
                Description = "实时数据上报",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.OnLine,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.OffLine))
            topicList.Add(new TransPondTopic
            {
                Topic = "v4/p/post/thing/history/json/1.1",
                Description = "离线数据上报",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.OffLine,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.SysTime))
            topicList.Add(new TransPondTopic
            {
                Topic = "v4/p/get/cloud/time/json/1.0",
                Description = "时钟同步请求",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.SysTime,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.SysTimeSub))
            topicList.Add(new TransPondTopic
            {
                Topic = "v4/s/get/cloud/time/json/1.0",
                Description = "时钟同步响应",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.SysTimeSub,
                Config = ""
            });
        return topicList;
    }

    /// <summary>
    ///     IotSuite平台Topic
    /// </summary>
    /// <param name="transPond"></param>
    /// <returns></returns>
    private List<TransPondTopic> CreateIotSuite(TransPond transPond)
    {
        var topicList = new List<TransPondTopic>();
        // 已经配置过的Topic
        var transPondTopicList = transPond.TransPondTopic.Select(s => s.TransPondTopicPurpose).ToList();
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.Meta))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/wisepaas/scada/{transPond.MqttConfModel?.ClientId}/cfg",
                Description = "设备配置上报",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.Meta,
                Config =
                    "// 入参默认数据\nvar rawData = payload;\n//根据网关侧数据类型转换成研华平台数据类型\nfunction getType(transitionType) {\n  var val = 3;\n  switch (transitionType) {\n    //数字\n    case 2:\n    case 3:\n      val = 2;\n      break;\n  }\n  return val;\n}\n\n//外层配置\nvar cfgDic = {\n  'Action': 4 //1.Add；2.Update，3.delete，4.覆盖;\n};\n//scada层配置\nvar scada = {\n  Hbt: 90,\n  Type: 2 //固定参数\n};\n//设备层配置\nvar deviceDic = {};\n\n//循环遍历组装数据\nvar device = {\n  Name: '',\n  Type: 'Modicon', //固定参数\n};\n//设备属性\nvar tag = {};\n\nfor (var i = 0; i < rawData.length; i++) {\n  var deviceVariable = rawData[i];\n  // 使用split方法按'/'切割字符串\n  var parts = deviceVariable.Identifier.split('/');\n  device.Name = parts[1];\n  var desc = deviceVariable.Name + ' ' + deviceVariable.Description;\n  //组装属性配置\n  tag[parts[2]] = {\n    Type: getType(deviceVariable.TransitionType), //数据类型\n    Desc: desc, //描述\n    RO: deviceVariable.ProtectType == 1 ? 1 : 0 //只读\n  }\n\n  device['Tag'] = tag;\n  deviceDic[device.Name] = device;\n}\n\n//网关标识\nvar scadaDic = {};\nscada['Device'] = deviceDic;\nscadaDic['f8e6356c23cd4ea99a662c9a5ba4e3ea'] = scada; // 实际上平台对应的值，通常会用网关中设置的clientId\ncfgDic['Scada'] = scadaDic;\n\nreturn JSON.stringify({\n  'd': cfgDic,\n  'ts': dateTime.Now(), //当前时间\n});"
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.OnLine))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/wisepaas/scada/{transPond.MqttConfModel?.ClientId}/data",
                Description = "实时数据上报",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.OnLine,
                Config =
                    "// 数据源示例数据 \r\n//{\r\n// \t\"DeviceName\": \"NC-V80S\",\r\n// \t\"Params\": {\r\n// \t\t\"d1\": {\r\n// \t\t\t\"Value\": \"11478\",\r\n// \t\t\t\"DataType\": 2,\r\n// \t\t\t\"ValueType\": \"Int\",\r\n// \t\t\t\"Time\": 1673089860171\r\n// \t\t}\r\n// \t}\r\n// }\r\nvar data = payload;\r\n//根据网关侧数据类型转换成研华平台需要的数据值\r\nfunction getValue(object) {\r\n  switch (object.ValueType) {\r\n    case 'Int':\r\n      return parseInt(object.Value);\r\n    case 'Double':\r\n      return parseFloat(object.Value);\r\n    default:\r\n      return object.Value;\r\n  }\r\n}\r\n//外层配置\r\nvar cfgDic = {};\r\n//属性配置\r\nvar tagValue = {};\r\n\r\nfor (var key in data.Params) {\r\n  //循环转换数据值\r\n  tagValue[key] = getValue(data.Params[key]);\r\n}\r\n\r\ncfgDic[data.DeviceName] = tagValue;\r\n//返回结果格式\r\nreturn JSON.stringify({\r\n  \"d\": cfgDic,\r\n  \"ts\": time, //当前时间\r\n});"
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.Heartbeat))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/wisepaas/scada/{transPond.MqttConfModel?.ClientId}/conn",
                Description = "心跳上报",
                TransPond = transPond,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.Heartbeat,
                Config =
                    "//返回结果格式\r\nreturn JSON.stringify({\r\n  \"d\": {\r\n    Hbt: 1\r\n  },\r\n  \"ts\": time, //当前时间\r\n});"
            });
        return topicList;
    }

    #endregion
}