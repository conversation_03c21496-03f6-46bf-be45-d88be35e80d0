
using IotGateway.Application.Entity;

namespace IotGateway.Application.TransPonds.Dto;

/// <summary>
/// 转发Topic修改
/// </summary>
public class TransPondTopicUpdateInput 
{
    /// <summary>
    /// TopicId
    /// </summary>
    [Required]
    public long Id { get; set; }
    
    /// <summary>
    ///     Topic
    /// </summary>
    [Required]
    public string Topic { get; set; }

    /// <summary>
    ///     转发Topic规则：1静态；2动态
    /// </summary>
    public TransPondTopicRuleEnum Rule { get; set; }
    
    /// <summary>
    ///     上行转发配置Id
    /// </summary>
    [Required]
    public long TransPondId { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Description { get; set; }
    
    /// <summary>
    ///     qms等级
    /// </summary>
    [Required]
    public MqttQualityOfServiceLevel Qos { get; set; } = MqttQualityOfServiceLevel.AtLeastOnce;

    /// <summary>
    ///     消息发送超时时间(s)
    /// </summary>
    [Required]
    public int TimeOut { get; set; } = 1;
}

/// <summary>
///     删除topic
/// </summary>
public class TopicDeleteInput
{
    /// <summary>
    ///     TopicId
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     上行转发配置Id
    /// </summary>
    [Required]
    public long TransPondId { get; set; }
}