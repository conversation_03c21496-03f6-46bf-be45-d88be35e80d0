using IotGateway.Application.Entity;

namespace IotGateway.Application.TransPonds.Dto;

/// <summary>
///     转发Topic列表
/// </summary>
public class TransPondTopicListInput
{
    /// <summary>
    ///     转发Id
    /// </summary>
    [Required]
    public long TransPondId { get; set; }

    /// <summary>
    ///     转发Topic类型：1:订阅；2：发布
    /// </summary>
    [Required]
    public TransPondTopicTypeEnum TransPondTopicType { get; set; }
}

/// <summary>
/// 转发Topic
/// </summary>
public class TopicGroupTypeListInput
{
    /// <summary>
    ///     转发Topic类型：1:订阅；2：发布
    /// </summary>
    [Required]
    public TransPondTopicTypeEnum TransPondTopicType { get; set; }
}