namespace IotGateway.Application.TransPonds.Dto;

/// <summary>
/// 修改转发Topic 的解析配置请求参数
/// </summary>
public class SetTopicConfigInput
{
    /// <summary>
    /// TopicId
    /// </summary>
    [Required]
    public long Id { get; set; }
    
    /// <summary>
    ///     解析内容
    /// </summary>
    public string Config { get; set; }
    
    /// <summary>
    ///     上行转发配置Id
    /// </summary>
    [Required]
    public long TransPondId { get; set; }
}

