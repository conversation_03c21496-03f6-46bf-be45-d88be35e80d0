
using IotGateway.Application.Entity;

namespace IotGateway.Application.TransPonds.Dto;

/// <summary>
///     新增转发Topic配置
/// </summary>
public class TransPondTopicAddInput
{
    /// <summary>
    ///     Topic
    /// </summary>
    [Required]
    public string Topic { get; set; }

    /// <summary>
    ///     发布Topic作用：1:实时数据;2:离线数据;3:设备属性同步;4:时间同步
    /// </summary>
    public TransPondTopicPurposeEnum TransPondTopicPurpose { get; set; }

    /// <summary>
    ///     转发Topic规则：1静态；2动态
    /// </summary>
    public TransPondTopicRuleEnum Rule { get; set; }

    /// <summary>
    ///     上行转发配置Id
    /// </summary>
    [Required]
    public long TransPondId { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    ///     qms等级
    /// </summary>
    [Required]
    public MqttQualityOfServiceLevel Qps { get; set; } = MqttQualityOfServiceLevel.AtLeastOnce;

    /// <summary>
    ///     消息发送超时时间(s)
    /// </summary>
    [Required]
    public int TimeOut { get; set; } = 1;

    /// <summary>
    /// 类型
    /// </summary>
    public TransPondTopicTypeEnum TransPondTopicType { get; set; }
}