using Common.Extension;
using IotGateway.Application.Entity;

namespace IotGateway.Application.TransPonds.Dto;

/// <summary>
///     转发配置
/// </summary>
public class TransPondAdd : IValidatableObject
{
    /// <summary>
    ///     标识符
    /// </summary>
    [Required(ErrorMessage = "标识符不能是空!")]
    public string Identifier { get; set; }

    /// <summary>
    ///     转发类型;1:MQTT;2:HTTP;3:SQL;
    /// </summary>
    public TransPondTypeEnum TransPondType { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    ///     Mqtt配置实体
    /// </summary>
    public MqttConfModel MqttConfModel { get; set; }

    /// <summary>
    ///     Sql配置实体
    /// </summary>
    public SqlConfModel SqlConfModel { get; set; }

    /// <summary>
    ///     Http转发配置实体
    /// </summary>
    public HttpConfModel HttpConfModel { get; set; }

    /// <summary>
    ///     OpcUa配置实体
    /// </summary>
    public OpcUaConfModel OpcUaConfModel { get; set; }

    /// <summary>
    ///     加强校验
    /// </summary>
    /// <param name="validationContext"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        switch (TransPondType)
        {
            case TransPondTypeEnum.Mqtt:
                if (MqttConfModel == null)
                    yield return new ValidationResult("Mqtt配置不能是空！", new[] {nameof(MqttConfModel)});
                else
                    switch (MqttConfModel.IoTPlatformType)
                    {
                        case IoTPlatformType.ELink:
                            if (MqttConfModel.UserName.IsNull())
                                yield return new ValidationResult(
                                    "MQTT连接用户名不能是空！"
                                    , new[] {nameof(MqttConfModel.UserName)}
                                );
                            if (MqttConfModel.Password.IsNull())
                                yield return new ValidationResult(
                                    "MQTT连接密码不能是空！"
                                    , new[] {nameof(MqttConfModel.Password)}
                                );
                            break;
                        case IoTPlatformType.SupOS:
                            if (!MqttConfModel.MqttConfigExtend.Any())
                                yield return new ValidationResult(
                                    "SupOs转发配置参数不能是空！"
                                    , new[] {nameof(MqttConfModel.MqttConfigExtend)}
                                );
                            if (MqttConfModel.MqttConfigExtend.All(a => a.Name != "AuthToken"))
                                yield return new ValidationResult(
                                    "AuthToken是必填参数！"
                                    , new[] {nameof(MqttConfModel.MqttConfigExtend)}
                                );
                            break;
                        case IoTPlatformType.RootCloud:
                            if (MqttConfModel.ClientId.IsNull())
                                yield return new ValidationResult(
                                    "ClientId不能是空！"
                                    , new[] {nameof(MqttConfModel.ClientId)}
                                );
                            if (MqttConfModel.UserName.IsNull())
                                yield return new ValidationResult(
                                    "MQTT连接用户名不能是空！"
                                    , new[] {nameof(MqttConfModel.UserName)}
                                );
                            if (MqttConfModel.Password.IsNull())
                                yield return new ValidationResult(
                                    "MQTT连接密码不能是空！"
                                    , new[] {nameof(MqttConfModel.Password)}
                                );
                            break;
                        case IoTPlatformType.MQTT:
                            break;
                    }

                break;
            case TransPondTypeEnum.Http:
                if (HttpConfModel == null)
                {
                    yield return new ValidationResult("HTTP配置不能是空！", new[] {nameof(HttpConfModel)});
                }
                else
                {
                    if (HttpConfModel.Url.IsNull())
                        yield return new ValidationResult(
                            "Http请求地址不能是空！"
                            , new[] {nameof(HttpConfModel.Url)}
                        );
                    if (!Enum.IsDefined(typeof(HttpTypeEnum), HttpConfModel.HttpType))
                        yield return new ValidationResult(
                            "Http请求方式错误！"
                            , new[] {nameof(HttpConfModel.HttpType)}
                        );
                    
                    if (!Enum.IsDefined(typeof(DataParsingRuleTypeEnum), HttpConfModel.DataParsingRuleType))
                        yield return new ValidationResult(
                            "Http数据解析规则方式错误！"
                            , new[] {nameof(HttpConfModel.DataParsingRuleType)}
                        );
                    if (HttpConfModel.DataParsingRuleType == DataParsingRuleTypeEnum.Script)
                    {
                        if (HttpConfModel.Content.IsNull())
                            yield return new ValidationResult(
                                "Http脚本解析内容不能是空！"
                                , new[] {nameof(HttpConfModel.Content)}
                            );
                    }
                }

                break;
            case TransPondTypeEnum.Sql:
                if (SqlConfModel == null)
                {
                    yield return new ValidationResult("Sql配置不能是空！", new[] {nameof(SqlConfModel)});
                }
                else
                {
                    if (SqlConfModel.Content.IsNull())
                        yield return new ValidationResult(
                            "SQL脚本内容不能是空！"
                            , new[] {nameof(SqlConfModel.Content)}
                        );
                    if (SqlConfModel.Config == null)
                        yield return new ValidationResult(
                            "SQL数据源配置不能是空！"
                            , new[] {nameof(SqlConfModel.Config)}
                        );
                }

                break;
            case TransPondTypeEnum.OpcUa:
                if (OpcUaConfModel == null)
                {
                    yield return new ValidationResult("OpcUa配置不能是空！", new[] {nameof(OpcUaConfModel)});
                }
                else
                {
                    //地址映射
                    if (OpcUaConfModel.OpcUaSendType == 1)
                    {
                        if (OpcUaConfModel.Config == null)
                            yield return new ValidationResult(
                                "OpcUa配置不能是空！"
                                , new[] {nameof(OpcUaConfModel.Config)}
                            );
                    }
                    else
                    {
                        if (OpcUaConfModel.Content.IsNull())
                            yield return new ValidationResult(
                                "OpcUa脚本内容不能是空！"
                                , new[] {nameof(OpcUaConfModel.Content)}
                            );
                    }
                }

                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
    }
}