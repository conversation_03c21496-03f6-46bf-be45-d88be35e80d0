using IotGateway.Application.Entity;
using IotGateway.EdgeDevice.TransPonds.SendToHttp;
using IotGateway.EdgeDevice.TransPonds.SendToOpcUa;
using IotGateway.EdgeDevice.TransPonds.SendToSql;

#pragma warning disable CS1998

namespace IotGateway.EdgeDevice.TransPonds;

/// <summary>
///     数据转发动态订阅类
/// </summary>
public class TransPondEventSubscriber
{
    // master节点
    private readonly MasterClient _masterClient;
    private readonly SendMessageService _send;
    private IServiceProvider Services { get; }
    public Dictionary<long, SlaveClient> SlaveClients = new();

    /// <summary>
    ///     sql转发写入
    /// </summary>
    private readonly Dictionary<long, SqlClient> _sqlClients = new();

    /// <summary>
    ///     Http转发写入
    /// </summary>
    private readonly Dictionary<long, SendToHttpClient> _httpClients = new();

    /// <summary>
    ///     转发OpcUa写入
    /// </summary>
    public Dictionary<long, OpcUaClient> OpcUaClients = new();

    private readonly Dictionary<long, MqttSubscribe> _mqttSubscribe = new();

    private readonly SuperOsApplication _superOsApplication;
    private readonly ELinkApplication _eLinkApplication;
    private readonly RootCloudApplicationMessageReceived _rootCloudApplication;
    private readonly DeviceHostedService _deviceHostedService;

    public TransPondEventSubscriber(IServiceProvider services, MasterClient masterClient, SendMessageService send, SuperOsApplication superOsApplication,
        ELinkApplication eLinkApplication, RootCloudApplicationMessageReceived rootCloudApplication, DeviceHostedService deviceHostedService)
    {
        Services = services;
        _masterClient = masterClient;
        _send = send;
        _superOsApplication = superOsApplication;
        _eLinkApplication = eLinkApplication;
        _rootCloudApplication = rootCloudApplication;
        _deviceHostedService = deviceHostedService;
    }

    /// <summary>
    /// </summary>
    /// <param name="transPond"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public async Task Create(TransPond transPond)
    {
        switch (transPond.TransPondType)
        {
            case TransPondTypeEnum.Mqtt:
            {
                if (!transPond.Master)
                {
                    if (SlaveClients.TryGetValue(transPond.Id, out var client))
                        client.Dispose();

                    var slaveClient = new SlaveClient(Services, transPond, _send, _superOsApplication, _eLinkApplication, _rootCloudApplication);
                    SlaveClients.Add(transPond.Id, slaveClient);
                }
                else
                {
                    if (_mqttSubscribe.TryGetValue(transPond.Id, out var value))
                        value.Dispose();

                    var mqttSubscribe = new MqttSubscribe(_masterClient, _send);
                    _mqttSubscribe.Add(transPond.Id, mqttSubscribe);
                }
            }
                break;
            case TransPondTypeEnum.Http:
            {
                if (_httpClients.TryGetValue(transPond.Id, out var client1))
                    client1.Dispose();
                var httpClient = new SendToHttpClient(transPond, Services, _send);
                _httpClients.Add(transPond.Id, httpClient);
                break;
            }
            case TransPondTypeEnum.Sql:
            {
                if (_sqlClients.TryGetValue(transPond.Id, out var client1))
                    client1.Dispose();
                var sqlClient = new SqlClient(transPond, Services, _send, _deviceHostedService);
                _sqlClients.Add(transPond.Id, sqlClient);
                break;
            }
            case TransPondTypeEnum.OpcUa:
            {
                if (OpcUaClients.TryGetValue(transPond.Id, out var client2))
                    client2.Dispose();
                var opcUaClient = new OpcUaClient(transPond, Services, _send, _deviceHostedService);
                OpcUaClients.Add(transPond.Id, opcUaClient);
                break;
            }
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="transPond"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public async Task Stop(TransPond transPond)
    {
        switch (transPond.TransPondType)
        {
            case TransPondTypeEnum.Mqtt:
            {
                if (!transPond.Master)
                {
                    if (SlaveClients.ContainsKey(transPond.Id))
                    {
                        SlaveClients[transPond.Id].Dispose();
                        SlaveClients.Remove(transPond.Id);
                    }
                }
                else
                {
                    if (_mqttSubscribe.ContainsKey(transPond.Id))
                    {
                        _mqttSubscribe[transPond.Id].Dispose();
                        _mqttSubscribe.Remove(transPond.Id);
                    }
                }
            }
                break;
            case TransPondTypeEnum.Http:
            {
                if (_httpClients.ContainsKey(transPond.Id))
                {
                    _httpClients[transPond.Id].Dispose();
                    _httpClients.Remove(transPond.Id);
                }

                break;
            }
            case TransPondTypeEnum.Sql:
            {
                if (_sqlClients.ContainsKey(transPond.Id))
                {
                    _sqlClients[transPond.Id].Dispose();
                    _sqlClients.Remove(transPond.Id);
                }

                break;
            }
            case TransPondTypeEnum.OpcUa:
            {
                if (OpcUaClients.ContainsKey(transPond.Id))
                {
                    OpcUaClients[transPond.Id].Dispose();
                    OpcUaClients.Remove(transPond.Id);
                }

                break;
            }
        }
    }
}