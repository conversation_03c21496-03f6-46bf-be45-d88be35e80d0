using DateTime = Common.Extension.DateTime;

namespace IotGateway.EdgeDevice.TransPonds;

/// <summary>
///     MQTT订阅
/// </summary>
public class MqttSubscribe : IDisposable
{
    private readonly CancellationTokenSource _tokenSource = new();
    private readonly SendMessageService _send;
    private readonly MasterClient _masterClient;

    public MqttSubscribe(MasterClient masterClient, SendMessageService send)
    {
        _masterClient = masterClient;
        _send = send;
        _ = Subscribe();
    }

    /// <summary>
    ///     Master 订阅
    /// </summary>
    /// <returns></returns>
    private async Task Subscribe()
    {
        var cancellationToken = _tokenSource.Token;
        await MessageCenter.Subscribe(EventConst.SendDeviceData, async context =>
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    return;

                // 并行发送消息
                var data =   context.GetPayload<PayLoad>();
                //当前时间 一直小于发起同步的时间，中间全部数据直接丢掉
                var time = DateTime.Now();
                if (time < _masterClient.DeviceSendTime)
                    await _send.DeviceResp($"【实时数据】同步前本地时间:【{_masterClient.DeviceSendTime}】,当前时间:【{time}】...采集时间戳:【{data.Ts}】,此部分数据丢弃!", _masterClient.TransPond?.Identifier, true);
                else
                    await _masterClient.PublishAsync(data);
            }
            catch (Exception ex)
            {
                await _send.DeviceResp($"【MQTT实时数据】 Error:【{ex.Message}】", _masterClient.TransPond?.Identifier);
            }

            await Task.CompletedTask;
        }, cancellationToken: cancellationToken);
    }

    /// <summary>
    /// </summary>
    public void Dispose()
    {
        _tokenSource.Cancel();
        _tokenSource?.Dispose();
    }
}