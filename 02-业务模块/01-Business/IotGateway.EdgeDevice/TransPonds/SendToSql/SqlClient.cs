using IotGateway.Application.Entity;
using DateTime = System.DateTime;
using DbType = SqlSugar.DbType;

namespace IotGateway.EdgeDevice.TransPonds.SendToSql;

/// <summary>
///     SQL实时发送数据
/// </summary>
public class SqlClient : IDisposable
{
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private readonly SendMessageService _send;
    private readonly TransPond _transPond;
    private IServiceProvider Services { get; }

    private readonly DeviceHostedService _deviceHostedService;
    private SqlSugarScope _db;

    /// <summary>
    ///     最后一次上报时间
    /// </summary>
    private DateTime _lastSendTime = DateTime.MinValue;

    // public bool IsConnected => Client?.IsConnected ?? false;
    public SqlClient(TransPond transPond, IServiceProvider services, SendMessageService send, DeviceHostedService deviceHostedService)
    {
        _send = send;
        _deviceHostedService = deviceHostedService;
        _transPond = transPond;
        Services = services;
        _ = Subscribe();
    }

    /// <summary>
    ///     slave 订阅
    /// </summary>
    private async Task Subscribe()
    {
        if (_transPond.SqlConfModel == null)
            return;
        var config = _transPond.SqlConfModel.Config;
        var conStr = Connectoin(config.DbType, config.Ip, config.Port, config.Database, config.Password, config.Uid);
        _db = new SqlSugarScope(new ConnectionConfig
        {
            ConnectionString = conStr,
            DbType = config.DbType == 2 ? DbType.SqlServer : DbType.MySql,
            IsAutoCloseConnection = true //自动释放
        });
        var cancellationToken = _cancellationTokenSource.Token;
        await MessageCenter.Subscribe(EventConst.SendDeviceData, async context =>
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    return;

                // 发送消息
                var data = context.GetPayload<PayLoad>();
                await PushDataBase(data);
            }
            catch (Exception ex)
            {
                await _send.DeviceResp($"【SQL实时数据】 Error:【{ex.Message}】", _transPond.Identifier);
            }

            await Task.CompletedTask;
        }, cancellationToken: cancellationToken);
    }

    /// <summary>
    ///     将消息发送到DataBase
    /// </summary>
    /// <returns></returns>
    private async Task PushDataBase(PayLoad payLoad)
    {
        // 业务代码
        try
        {
            var conf = _transPond.SqlConfModel?.Content;
            if (string.IsNullOrEmpty(conf))
                return;
            // 配置策略过滤数据
            var send = await Filter(payLoad);
            if (!send)
                return;

            // 窄表写入-批量插入
            if (_transPond.SqlConfModel is {Batch: true})
            {
                var insertClause = GetInsertClause(conf);
                var valuesClause = await GenerateValuesClause(payLoad, conf);
                var completeSql = $"{insertClause} {valuesClause};";
                try
                {
                    var result = await ExecuteSqlAsync(completeSql);
                    await SendResultMessage(result, payLoad.DeviceName, completeSql);
                }
                catch (Exception e)
                {
                    await HandleSqlExecutionException(e, payLoad.DeviceName, completeSql);
                }
            }
            else
            {
                foreach (var (key, value) in payLoad.Values)
                {
                    var sqlContent = _transPond.SqlConfModel?.Content;
                    sqlContent = await ReplaceValuesClause(sqlContent, key, value, payLoad.DeviceName);
                    try
                    {
                        // 执行单条SQL
                        var result = await ExecuteSqlAsync(sqlContent);
                        await SendResultMessage(result, payLoad.DeviceName, sqlContent);
                    }
                    catch (Exception e)
                    {
                        await HandleSqlExecutionException(e, payLoad.DeviceName, sqlContent);
                    }
                }
            }

            // else
            // {
            //     // todo 方案不成熟，原因：多设备每个设备属性名称均不同，无法通用
            //    // 宽表写入 
            //    // 替换系统支持函数
            //    conf = await ReplaceValuesClause(conf,payLoad);
            //    // 替换属性标识
            //    if (conf.Contains("${"))
            //        conf = payLoad.Values.Aggregate(conf, (current, kvp) => current.Replace($"${{{kvp.Key}}}", kvp.Value.Value));
            //    try
            //    {
            //        var result = await ExecuteSqlAsync(conf);
            //        await SendResultMessage(result, payLoad.DeviceName, conf);
            //    }
            //    catch (Exception e)
            //    {
            //        await HandleSqlExecutionException(e, payLoad.DeviceName, conf);
            //    }
            // }
        }
        catch (Exception ex)
        {
            await _send.DeviceResp($"【SQL实时数据】 Error:【{ex.Message}】", _transPond.Identifier);
        }

        await Task.CompletedTask;
    }

    /// <summary>
    ///     截取insert语句
    /// </summary>
    /// <param name="conf"></param>
    /// <returns></returns>
    private string GetInsertClause(string conf)
    {
        var valuesIndex = conf!.ToLower().IndexOf("values", StringComparison.Ordinal);
        return conf.ToLower().Substring(0, valuesIndex + "values".Length);
    }

    /// <summary>
    ///     生成Values语句
    /// </summary>
    /// <param name="payLoad"></param>
    /// <param name="conf"></param>
    /// <returns></returns>
    private async Task<string> GenerateValuesClause(PayLoad payLoad, string conf)
    {
        var valuesIndex = conf!.ToLower().IndexOf("values", StringComparison.Ordinal);
        var valuesClause = conf.ToLower().Substring(valuesIndex + "values".Length);
        var output = "";
        foreach (var (key, value) in payLoad.Values)
        {
            var valuesClauseSource = valuesClause.TrimEnd(';');
            output += await ReplaceValuesClause(valuesClauseSource, key, value, payLoad.DeviceName) + ",";
        }

        return output.TrimEnd(',');
    }

    /// <summary>
    ///     替换系统支持函数
    /// </summary>
    /// <param name="valuesClause"></param>
    /// <param name="key"></param>
    /// <param name="value"></param>
    /// <param name="deviceName"></param>
    /// <returns></returns>
    private async Task<string> ReplaceValuesClause(string valuesClause, string key, ParamValue value, string deviceName)
    {
        valuesClause = valuesClause
            .Replace("$guid", Guid.NewGuid().ToString())
            .Replace("$key", key)
            .Replace("$value", value.Value)
            .Replace("$deviceName", deviceName).Replace("$devicename", deviceName)
            .Replace("$timestamp", value.ReadTime.ToString())
            .Replace("$yyyymm", Common.Extension.DateTime.Now().ToString("yyyyMM"))
            .Replace("$yymm", Common.Extension.DateTime.Now().ToString("yyMM"))
            .Replace("$deviceVariableName", value.DeviceVariableName).Replace("$devicevariablename", value.DeviceVariableName)
            .Replace("$time", Common.Extension.DateTime.ToTime(value.ReadTime).ToString("yyyy-MM-dd HH:mm:ss"));

        var deviceStatus = _deviceHostedService.DeviceConnectStatus(deviceName);
        valuesClause = valuesClause
            .Replace("$deviceStatusName", deviceStatus ? "在线" : "离线").Replace("$devicestatusname", deviceStatus ? "在线" : "离线")
            .Replace("$deviceStatus", deviceStatus.ToString()).Replace("$devicestatus", deviceStatus.ToString());

        return valuesClause;
    }

    // /// <summary>
    // ///     替换系统支持函数
    // /// </summary>
    // /// <param name="valuesClause"></param>
    // /// <param name="payLoad"></param>
    // /// <returns></returns>
    // private async Task<string> ReplaceValuesClause(string valuesClause,PayLoad payLoad)
    // {
    //     valuesClause = valuesClause
    //         .Replace("$guid", Guid.NewGuid().ToString())
    //         .Replace("$timestamp", payLoad.Ts.ToString())
    //         .Replace("$yyyymm", Common.Extension.DateTime.Now().ToString("yyyyMM"))
    //         .Replace("$yymm", Common.Extension.DateTime.Now().ToString("yyMM"))
    //         .Replace("$time", Common.Extension.DateTime.ToTime(payLoad.Ts).ToString("yyyy-MM-dd HH:mm:ss"));
    //     // 设备连接状态
    //     var deviceStatus = _deviceHostedService.DeviceConnectStatus(payLoad.DeviceName);
    //     valuesClause = valuesClause
    //         .Replace("$deviceStatusName", deviceStatus ? "在线" : "离线").Replace("$devicestatusname", deviceStatus ? "在线" : "离线")
    //         .Replace("$deviceStatus", deviceStatus.ToString()).Replace("$devicestatus", deviceStatus.ToString());
    //
    //     return valuesClause;
    // }

    /// <summary>
    ///     执行Sql
    /// </summary>
    /// <param name="sql"></param>
    /// <returns></returns>
    private async Task<int> ExecuteSqlAsync(string sql)
    {
        return await _db.Ado.ExecuteCommandAsync(sql);
    }

    /// <summary>
    ///     打印执行结果
    /// </summary>
    /// <param name="result"></param>
    /// <param name="deviceName"></param>
    /// <param name="sql"></param>
    private async Task SendResultMessage(int result, string deviceName, string sql)
    {
        if (result > 0)
            await _send.DeviceResp($"设备:【{deviceName}】,SQL执行成功", _transPond.Identifier);
        else
            await _send.DeviceResp($"设备:【{deviceName}】,SQL执行失败:【{sql}】", _transPond.Identifier);
    }

    /// <summary>
    ///     执行报错信息
    /// </summary>
    /// <param name="e"></param>
    /// <param name="deviceName"></param>
    /// <param name="sql"></param>
    private async Task HandleSqlExecutionException(Exception e, string deviceName, string sql)
    {
        await _send.DeviceResp($"设备:【{deviceName}】,SQL执行失败,失败原因:【{e.Message}】,SQL:【{sql}】", _transPond.Identifier);
    }

    /// <summary>
    ///     根据规则过滤条件
    /// </summary>
    /// <param name="payLoad"></param>
    private Task<bool> Filter(PayLoad payLoad)
    {
        switch (_transPond.SqlConfModel?.SendType)
        {
            case TransPondSendTypeEnum.Always:
            {
                payLoad.Values = payLoad.Values.Where(i => i.Value.Value != null).ToDictionary(i => i.Key, i => i.Value);
                break;
            }
            case TransPondSendTypeEnum.PubPeriod:
            {
                var pubPeriod = _transPond.SqlConfModel.PubPeriodUnit == "毫秒" ? _transPond.SqlConfModel.PubPeriod
                    : _transPond.SqlConfModel.PubPeriodUnit == "秒" ? _transPond.SqlConfModel.PubPeriod * 1000
                    : _transPond.SqlConfModel.PubPeriodUnit == "分钟" ? _transPond.SqlConfModel.PubPeriod * 1000 * 60
                    : _transPond.SqlConfModel.PubPeriod * 1000 * 60 * 60;
                // 周期上报是否达到要求时间,未达到就不上报数据
                var nowTime = Common.Extension.DateTime.Now();
                if ((nowTime - _lastSendTime).TotalMilliseconds < pubPeriod)
                    return Task.FromResult(false);
                _lastSendTime = nowTime;

                payLoad.Values = payLoad.Values.Where(i => i.Value.Value != null).ToDictionary(i => i.Key, i => i.Value);
                break;
            }
            case TransPondSendTypeEnum.Changed:
            {
                payLoad.Values = payLoad.Values.Where(i => (i.Value.Value != null && i.Value.CookieValue?.ToString() != i.Value.Value) || (i.Value.CookieValue == null && i.Value.Value != null))
                    .ToDictionary(i => i.Key, i => i.Value);
                break;
            }
        }

        return Task.FromResult(true);
    }

    /// <summary>
    ///     连接数据库
    /// </summary>
    /// <param name="dbType"></param>
    /// <param name="ip"></param>
    /// <param name="port"></param>
    /// <param name="database"></param>
    /// <param name="password"></param>
    /// <param name="uid"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private static string Connectoin(int dbType, string ip, short port, string database, string password, string uid)
    {
        string conStr = null;
        switch (dbType)
        {
            case 1: // mysql
                conStr = "Data Source=" + ip + ";" + "port=" + port + ";" + "Database=" + database + ";" + "Password=" + password + ";" + "User ID=" + uid + ";" +
                         "pooling=true;sslmode=none;CharSet=utf8;";
                break;
            case 2: // sqlserver
                conStr = "Data Source=" + ip + "," + port + ";" + "Database=" + database + ";" + "Password=" + password + ";" + "User ID=" + uid + ";" + "Trust Server Certificate = true;";
                break;
        }

        return conStr;
    }

    public void Dispose()
    {
        _cancellationTokenSource.Cancel();
        _cancellationTokenSource.Dispose();
        _db?.Close();
        _db?.Dispose();
    }
}