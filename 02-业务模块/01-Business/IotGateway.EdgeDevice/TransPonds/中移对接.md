# feng-中移平台

## V1.0.0

`2024.03.13`

- 🎯 注意：`该平台mqtt clientId未断开不能重新连接`
-
-
- 🌟 步骤一(设备注册)，通过http请求获取mqtt连接参数

```javascript
// 公网环境网关从DMP获取信息地址：http://dmp.cmsrict.com:39997/connection/info，userName:dmpHarborCon、password:dmp@123
var url = 'http://dmp.cmsrict.com:39997/connection/info';
//示例 请求头
var headers = {
  'Accept': 'application/json',
  'userName':'dmpHarborCon',
  'password':'dmp@123'
}
//示例 请求参数
var body = {
  'deviceId':'5130385898960601088',
  'password':'QV<Hb1ef'
}
var resultObj = http.Post(url, headers, body);
return  resultObj;
```

- 🌟 步骤二(设备上电)

```javascript
var topic = '/v1/5127899053907525632/p/j';
var payload = {
    'et': dateTime.Now('cst'),
    'ip': '***********'
}
//mqtt.connect('fengedge.cn',1883,'5130385898960601088','fXku87ZZw7','TCl8mcNcdBjEleHE');
// mqtt 服务端配置
mqtt.connect('***************',39999,'5127899053907525632','Ax4zYkzLJR','yCCiOLJdOcB70Jyx');
var pubRest = mqtt.Publish(topic, JSON.stringify(payload), 1, false)
// 手动断开连接
mqtt.DisConnect();
return pubRest
```

- 🌟 步骤三(实时上报心跳)

```javascript
var deviceConnectList = device.Status();
var daList = [];
for (var deviceConnect of deviceConnectList) {
    var da = {};
    da['id'] = deviceConnect.DeviceName;
    da['ds'] = deviceConnect.OnLine == true ? 0 : 1;
    daList.push(da);
}

var payload = {
    'ip': '***********',
    'et': dateTime.Now('cst'),
    'da': daList
}
return JSON.stringify(payload);
```

- 🌟 步骤四(实时数据上报)

```javascript
var data = payload;

//根据网关侧数据类型转换成研华平台需要的数据值
function getValue(object) {
    switch (object.ValueType) {
        case 'Int':
            return parseInt(object.Value);
        case 'Double':
            return parseFloat(object.Value);
        default:
            return object.Value;
    }
}

//属性配置
var tagValue = {};

for (var key in data.Params) {
    //循环转换数据值
    tagValue[key] = getValue(data.Params[key]);
}

var pubData = {
    "et": dateTime.Now('cst'),
    "da": [{
        'id': data.DeviceName,
        'da': tagValue
    }]
}

return JSON.stringify(pubData);
```