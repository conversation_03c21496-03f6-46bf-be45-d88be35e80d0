namespace IotGateway.EdgeDevice.TransPonds.SendToOpcUa.Dto;

/// <summary>
/// OpcUa连接
/// </summary>
public class NodeSelectInput
{
    /// <summary>
    ///     连接地址
    /// </summary>
    [Required]
    public string Url { get; set; }

    /// <summary>
    /// 上级节点 
    /// </summary>
    public string NodeId { get; set; }
}

/// <summary>
/// OpcUa连接
/// </summary>
public class NodeSelectByDeviceInput
{
    /// <summary>
    ///     设备Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    /// 上级节点 
    /// </summary>
    public string NodeId { get; set; }
}