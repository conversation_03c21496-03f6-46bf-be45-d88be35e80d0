using Opc.Ua;

namespace IotGateway.EdgeDevice.TransPonds.SendToOpcUa.Dto;

/// <summary>
///     OpcUa扫描节点树
/// </summary>
public class OpcUaNodeTree
{
    /// <summary>
    /// 名称
    /// </summary>
    public string BrowseName { get; set; }
    /// <summary>
    /// 节点Id
    /// </summary>
    public string NodeId { get; set; }
    /// <summary>
    /// 类型
    /// </summary>
    public NodeClass NodeClass { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public List<OpcUaNodeTree> Children { get; set; }

    /// <summary>
    /// 只有变量才有该字段
    /// </summary>
    public string DataType { get; set; }
}

/// <summary>
///     标准规范
/// </summary>
public class OpcUaScriptValue
{
    /// <summary>
    ///     数据类型
    /// </summary>
    public string DataType { get; set; }

    /// <summary>
    ///     数据值
    /// </summary>
    public object Value { get; set; }
}