using Common.Extension;
using Feng.Common.OpcUaHelper;
using IotGateway.Application.Entity;
using IotGateway.EdgeDevice.TransPonds.SendToOpcUa.Dto;
using DateTime = System.DateTime;

namespace IotGateway.EdgeDevice.TransPonds.SendToOpcUa;

/// <summary>
/// </summary>
public class OpcUaClient : IDisposable
{
    /// <summary>
    /// </summary>
    private readonly CancellationTokenSource _cancellationTokenSource = new();

    /// <summary>
    ///     Socket推送服务
    /// </summary>
    private readonly SendMessageService _send;

    /// <summary>
    ///     转发配置文件
    /// </summary>
    private readonly TransPond _transPond;

    /// <summary>
    /// </summary>
    private IServiceProvider Services { get; set; }

    /// <summary>
    /// </summary>
    private readonly DeviceHostedService _deviceHostedService;

    /// <summary>
    /// </summary>
    private OpcUaClientHelper _opcUa;

    /// <summary>
    ///     脚本执行
    /// </summary>
    private readonly Jint.Engine _engine;

    /// <summary>
    ///     最后一次上报时间
    /// </summary>
    private DateTime _lastSendTime = DateTime.MinValue;

    /// <summary>
    /// </summary>
    public bool IsConnected { get; set; }

    public OpcUaClient(TransPond transPond, IServiceProvider services, SendMessageService send, DeviceHostedService deviceHostedService)
    {
        _send = send;
        _deviceHostedService = deviceHostedService;
        _transPond = transPond;
        Services = services;
        _engine = new Jint.Engine();
        _ = Subscribe();
    }

    /// <summary>
    ///     数据订阅
    /// </summary>
    private async Task Subscribe()
    {
        var config = _transPond.OpcUaConfModel?.Config;
        if (config == null)
            return;
        _opcUa = new OpcUaClientHelper();
        try
        {
            _opcUa.ReconnectComplete += OnOpcUaOnReconnectComplete;
            await _opcUa.ConnectServer(config.Url);
        }
        catch (Exception ex)
        {
            await _send.DeviceResp($"【OpcUa】 无法连接 Error:【{ex.Message}】", _transPond.Identifier);
        }

        IsConnected = _opcUa.Connected;
        var cancellationToken = _cancellationTokenSource.Token;
        await MessageCenter.Subscribe(EventConst.SendDeviceData, async context =>
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                    return;

                // 发送消息
                var data = context.GetPayload<PayLoad>();
                await PushDataBase(data);
            }
            catch (Exception ex)
            {
                await _send.DeviceResp($"【OpcUa】 Error:【{ex.Message}】", _transPond.Identifier);
            }

            await Task.CompletedTask;
        }, cancellationToken: cancellationToken);
    }

    /// <summary>
    ///     重新连接事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="args"></param>
    private void OnOpcUaOnReconnectComplete(object sender, EventArgs args)
    {
        _ = _send.DeviceResp("【OpcUa】 重新连接", _transPond.Identifier);
    }

    /// <summary>
    ///     将消息发送到OpcUa
    /// </summary>
    /// <returns></returns>
    private async Task PushDataBase(PayLoad payLoad)
    {
        // 业务代码
        try
        {
            if (!IsConnected || _transPond.OpcUaConfModel == null)
            {
                if (IsConnected) return;
                try
                {
                    await _opcUa.ConnectServer(_transPond.OpcUaConfModel.Config.Url);
                    IsConnected = _opcUa.Connected;
                }
                catch (Exception ex)
                {
                    _ = _send.DeviceResp($"【OpcUa】 已断开连接 【{ex.Message}】", _transPond.Identifier);
                }

                return;
            }

            //根据配置策略过滤数据
            var send = await Filter(payLoad);
            if (!send)
                return;
            //地址映射
            if (_transPond.OpcUaConfModel.OpcUaSendType == 1)
                await Mapping(payLoad);
            //脚本解析
            else
                lock (this)
                {
                    _ = Script(payLoad);
                }
        }
        catch (Exception ex)
        {
            await _send.DeviceResp($"【OpcUa】 Error:【{ex.Message}】", _transPond.Identifier);
        }

        await Task.CompletedTask;
    }

    /// <summary>
    ///     根据规则过滤条件
    /// </summary>
    /// <param name="payLoad"></param>
    private Task<bool> Filter(PayLoad payLoad)
    {
        switch (_transPond.SqlConfModel?.SendType)
        {
            case TransPondSendTypeEnum.Always:
            {
                payLoad.Values = payLoad.Values.Where(i => i.Value.Value != null)
                    .ToDictionary(i => i.Key, i => i.Value);
                break;
            }
            case TransPondSendTypeEnum.PubPeriod:
            {
                var pubPeriod = _transPond.OpcUaConfModel.PubPeriodUnit == "毫秒" ? _transPond.OpcUaConfModel.PubPeriod
                    : _transPond.OpcUaConfModel.PubPeriodUnit == "秒" ? _transPond.OpcUaConfModel.PubPeriod * 1000
                    : _transPond.OpcUaConfModel.PubPeriodUnit == "分钟" ? _transPond.OpcUaConfModel.PubPeriod * 1000 * 60
                    : _transPond.OpcUaConfModel.PubPeriod * 1000 * 60 * 60;
                //判断周期上报是否达到要求时间,未达到就不上报数据
                var nowTime = Common.Extension.DateTime.Now();
                if ((nowTime - _lastSendTime).TotalMilliseconds < pubPeriod)
                    return Task.FromResult(false);
                _lastSendTime = nowTime;

                payLoad.Values = payLoad.Values.Where(i => i.Value.Value != null)
                    .ToDictionary(i => i.Key, i => i.Value);
                break;
            }
            case TransPondSendTypeEnum.Changed:
            {
                payLoad.Values = payLoad.Values.Where(i =>
                        (i.Value.Value != null && i.Value.CookieValue?.ToString() != i.Value.Value) ||
                        (i.Value.CookieValue == null && i.Value.Value != null))
                    .ToDictionary(i => i.Key, i => i.Value);
                break;
            }
        }

        return Task.FromResult(true);
    }

    /// <summary>
    ///     地址映射发送
    /// </summary>
    /// <param name="payLoad"></param>
    private async Task Mapping(PayLoad payLoad)
    {
        var opcUaMappings = _transPond.OpcUaConfModel?.OpcUaMappings;
        //拿到当前设备的采集信息
        var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(f => f.Device.DeviceName == payLoad.DeviceName);
        if (deviceThread == null)
            return;
        //过滤当前设备的点位
        opcUaMappings = opcUaMappings.Where(w => w.DeviceId == deviceThread.Device.Id).ToList();
        if (!opcUaMappings.Any())
            return;
        //地址映射
        var address = "";
        object value = null;
        foreach (var (key, paramValue) in payLoad.Values)
            try
            {
                //从内存中获取已经配置好的数据
                var deviceVariable = deviceThread.DeviceVariableSource.FirstOrDefault(f => f.Key == key).Value;
                if (deviceVariable == null)
                    continue;
                //查看当前属性是否有配置过
                var opcUaMapping = opcUaMappings.FirstOrDefault(f => f.DeviceVariableId == deviceVariable.Id);
                if (opcUaMapping == null)
                    continue;

                address = opcUaMapping.Address;
                value = paramValue.Value;
                bool write;
                switch (opcUaMapping.OpcUaDataType)
                {
                    case "Int32":
                        write = await _opcUa.WriteNodeAsync(address, Convert.ToInt32(value));
                        break;
                    case "Int16":
                        write = await _opcUa.WriteNodeAsync(address, Convert.ToInt16(value));
                        break;
                    case "Int64":
                        write = await _opcUa.WriteNodeAsync(address, Convert.ToInt64(value));
                        break;
                    case "DateTime":
                        write = await _opcUa.WriteNodeAsync(address, Convert.ToDateTime(value));
                        break;
                    case "Float":
                        write = await _opcUa.WriteNodeAsync(address, Convert.ToSingle(value));
                        break;
                    case "Double":
                        write = await _opcUa.WriteNodeAsync(address, Convert.ToDouble(value));
                        break;
                    case "Boolean":
                        write = await _opcUa.WriteNodeAsync(address, Convert.ToBoolean(value));
                        break;
                    default:
                        write = await _opcUa.WriteNodeAsync(address, value);
                        break;
                }

                if (write)
                    await _send.DeviceResp(
                        $"【OpcUa】 设备:【{payLoad.DeviceName}】, 属性:【{key}】,地址:【{address}】,值:【{value}】 写入成功",
                        _transPond.Identifier);
                else
                    await _send.DeviceResp(
                        $"【OpcUa】 设备:【{payLoad.DeviceName}】, 属性:【{key}】,地址:【{address}】,值:【{value}】 写入失败",
                        _transPond.Identifier);
            }
            catch (Exception e)
            {
                if (e.Message == "BadConnectionClosed")
                    IsConnected = false;
                await _send.DeviceResp(
                    $"【OpcUa】 设备:【{payLoad.DeviceName}】,属性:【{key}】,地址:【{address}】,值:【{value}】 错误原因:【{e.Message}】",
                    _transPond.Identifier);
            }
    }

    /// <summary>
    ///     脚本解析
    /// </summary>
    /// <param name="payLoad"></param>
    private async Task Script(PayLoad payLoad)
    {
        var variableSendToPlatform = new VariableSendToPlatform
        {
            ParentTime = payLoad.Ts,
            DeviceName = payLoad.DeviceName,
            DriverName = payLoad.DriverName,
            Params = new Dictionary<string, TransPondParamValue>()
        };
        foreach (var (key, val) in payLoad.Values)
            variableSendToPlatform.Params.Add(key, new TransPondParamValue
            {
                Value = val.Value,
                Time = val.ReadTime,
                DataType = val.TransitionType
            });
        _engine.SetValue("payload", variableSendToPlatform);
        var getValue = _engine.Evaluate(_transPond.OpcUaConfModel.Content ?? "", new ScriptParsingOptions {Tolerant = true, AllowReturnOutsideFunction = true}).ToObject();
        if (getValue != null)
        {
            var paramDic = JSON.Deserialize<Dictionary<string, OpcUaScriptValue>>(getValue.ToString());
            var address = "";
            object value = null;
            foreach (var (key, opcUaScriptValue) in paramDic)
                try
                {
                    address = key;
                    value = opcUaScriptValue.Value.GetJsonElementValue();
                    bool write;
                    switch (opcUaScriptValue.DataType)
                    {
                        case "Int32":
                            write = await _opcUa.WriteNodeAsync(address, Convert.ToInt32(value));
                            break;
                        case "Int16":
                            write = await _opcUa.WriteNodeAsync(address, Convert.ToInt16(value));
                            break;
                        case "Int64":
                            write = await _opcUa.WriteNodeAsync(address, Convert.ToInt64(value));
                            break;
                        case "DateTime":
                            write = await _opcUa.WriteNodeAsync(address, Convert.ToDateTime(value));
                            break;
                        case "Float":
                            write = await _opcUa.WriteNodeAsync(address, Convert.ToSingle(value));
                            break;
                        case "Double":
                            write = await _opcUa.WriteNodeAsync(address, Convert.ToDouble(value));
                            break;
                        case "Boolean":
                            write = await _opcUa.WriteNodeAsync(address, Convert.ToBoolean(value));
                            break;
                        default:
                            write = await _opcUa.WriteNodeAsync(address, value);
                            break;
                    }

                    if (write)
                        await _send.DeviceResp(
                            $"【OpcUa】 设备:【{variableSendToPlatform.DeviceName}】, 地址:【{address}】,值:【{value}】 写入成功",
                            _transPond.Identifier);
                    else
                        await _send.DeviceResp(
                            $"【OpcUa】 设备:【{variableSendToPlatform.DeviceName}】, 地址:【{address}】,值:【{value}】 写入失败",
                            _transPond.Identifier);
                }
                catch (Exception e)
                {
                    if (e.Message == "BadConnectionClosed")
                        IsConnected = false;
                    await _send.DeviceResp($"【OpcUa】 设备:【{variableSendToPlatform.DeviceName}】,地址:【{address}】,值:【{value}】 错误原因:【{e.Message}】", _transPond.Identifier);
                }
        }
    }

    /// <summary>
    /// </summary>
    public void Dispose()
    {
        _cancellationTokenSource.Cancel();
        _cancellationTokenSource.Dispose();
        _opcUa?.Disconnect();
        _engine.Dispose();
    }
}