using Feng.Common.OpcUaHelper;
using Feng.IotGateway.Core.Service.Cache;
using IotGateway.EdgeDevice.TransPonds.SendToOpcUa.Dto;
using Opc.Ua;

namespace IotGateway.EdgeDevice.TransPonds.SendToOpcUa;

/// <summary>
///     转发配置-OpcUa
///     版 本:V3.0.7.2
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-05-15
/// </summary>
[ApiDescriptionSettings("转发配置")]
public class OpcUaClientService : ITransient, IDynamicApiController
{
    private readonly CacheService _cacheService;
    private readonly SqlSugarRepository<Feng.IotGateway.Core.Entity.Driver> _driver;

    public OpcUaClientService(CacheService cacheService, SqlSugarRepository<Feng.IotGateway.Core.Entity.Driver> driver)
    {
        _cacheService = cacheService;
        _driver = driver;
    }

    /// <summary>
    ///     opcDa节点列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/opcDa/nodeSelect")]
    [HttpGet("/api/opcDa/nodeSelect")]
    public async Task<dynamic> NodeSelect([FromQuery] NodeSelectInput input)
    {
        try
        {
            var opcDa = new OpcUaClientHelper();
            await opcDa.ConnectServer(input.Url);
            if (!opcDa.Connected)
                return _cacheService.Get<List<OpcUaNodeTree>>("OpcDa:" + input.Url);

            if (input.NodeId.IsNullOrEmpty()) input.NodeId = ObjectIds.ObjectsFolder.ToString();
            var readNode = opcDa.BrowseNodeReference(input.NodeId).Select(
                s => new OpcUaNodeTree
                {
                    NodeId = s.NodeId.ToString(),
                    NodeClass = s.NodeClass,
                    BrowseName = s.BrowseName.Name,
                    DataType = s.NodeClass == NodeClass.Variable
                        ? opcDa.ReadNode(s.NodeId.ToString()).WrappedValue.TypeInfo.BuiltInType.ToString() ?? null
                        : null
                });
            var result = readNode.Where(w => w.BrowseName != "Server").Select(treeNode => ChildNodes(opcDa, treeNode))
                .Select(dummy => (OpcUaNodeTree) dummy).ToList();
            _cacheService.Set("OpcDa:" + input.Url, result);
            opcDa.Disconnect();
            return result;
        }
        catch
        {
            return _cacheService.Get<List<OpcUaNodeTree>>("OpcDa:" + input.Url);
        }
    }

    /// <summary>
    ///     opcDa测试连接
    /// </summary>
    /// <returns></returns>
    [HttpPost("/transPond/opcDa/connect")]
    [HttpPost("/api/transPond/opcDa/connect")]
    public async Task<bool> Connect(NodeSelectInput input)
    {
        // 释放连接，不能直接使用连接状态判断
        var connect = false;
        var opcDa = new OpcUaClientHelper();
        await opcDa.ConnectServer(input.Url);
        if (opcDa.Connected)
            connect = true;
        opcDa.Disconnect();
        return connect;
    }

    /// <summary>
    ///     根据当前节点，加载子节点
    /// </summary>
    /// <param name="opcDa"></param>
    /// <param name="currTreeEntity">当前节点</param>
    private dynamic ChildNodes(OpcUaClientHelper opcDa, OpcUaNodeTree currTreeEntity)
    {
        // 节点是否还有下级
        var childNodes = opcDa.BrowseNodeReference(currTreeEntity.NodeId);
        if (childNodes.Count <= 0) return currTreeEntity;
        currTreeEntity.Children ??= new List<OpcUaNodeTree>();
        currTreeEntity.Children.AddRange(childNodes.Select(
            s => new OpcUaNodeTree
            {
                NodeId = s.NodeId.ToString(),
                NodeClass = s.NodeClass,
                BrowseName = s.BrowseName.Name,
                DataType = s.NodeClass == NodeClass.Variable
                    ? opcDa.ReadNode(s.NodeId.ToString()).WrappedValue.TypeInfo?.BuiltInType.ToString() ?? null
                    : null
            }));
        foreach (var treeEntity in currTreeEntity.Children) ChildNodes(opcDa, treeEntity);
        return currTreeEntity;
    }

    #region Opc-Ua

    /// <summary>
    ///     扫描opcDa节点列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceVariable/opcDa/nodeSelect")]
    [HttpGet("/api/deviceVariable/opcDa/nodeSelect")]
    public async Task<dynamic> NodeSelect([FromQuery] NodeSelectByDeviceInput input)
    {
        // 网关设备
        var device = await _driver.AsSugarClient().Queryable<Device>()
            .Where(w => w.Id == input.Id)
            .Includes(w => w.DeviceConfigs)
            .FirstAsync();
        var url = device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName.ToLower() == "url")!.Value;
        return await NodeSelect(new NodeSelectInput {NodeId = input.NodeId, Url = url});
    }

    #endregion
}