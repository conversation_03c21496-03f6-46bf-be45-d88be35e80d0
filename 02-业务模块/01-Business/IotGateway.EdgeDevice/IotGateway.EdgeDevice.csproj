<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DocumentationFile>bin\Release\IotGateway.EdgeDevice.xml</DocumentationFile>
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\03-数据模块\IotGateway.TDengIne\IotGateway.TDengIne.csproj" />
        <ProjectReference Include="..\..\..\04-架构核心\IotGateway.Core\IotGateway.Core.csproj"/>
        <ProjectReference Include="..\..\..\04-架构核心\IotGateway.WebSocket\IotGateway.WebSocket.csproj"/>
        <ProjectReference Include="..\..\..\05-协议模块\DriversInterface\DriversInterface.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <None Remove="IotGateway.EdgeDevice.csproj.DotSettings"/>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="SMBLibrary" Version="1.5.3.4" />
    </ItemGroup>

</Project>
