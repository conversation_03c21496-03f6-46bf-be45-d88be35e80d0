using IotGateway.EdgeDevice.DeviceVariables;

namespace IotGateway.EdgeDevice;

public class Mapper : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        config.ForType<DeviceVariableInPortDto, DeviceVariable>()
            .Map(dest => dest.TransitionType, src => (TransPondDataTypeEnum) Enum.Parse(typeof(TransPondDataTypeEnum), src.TransitionType))
            .Map(dest => dest.ValueSource,
                src => src.ValueSource == "读取" ? ValueSourceEnum.Get : src.ValueSource == "计算赋值" ? ValueSourceEnum.Calculate : src.ValueSource == "手动写值" ? ValueSourceEnum.Static : ValueSourceEnum.Get)
            .Map(dest => dest.SendType,
                src => src.SendType == "总是上报" ? SendTypeEnum.Always : src.SendType == "从不上报" ? SendTypeEnum.Never : src.SendType == "变化上报" ? SendTypeEnum.Changed : SendTypeEnum.Always);
    }
}