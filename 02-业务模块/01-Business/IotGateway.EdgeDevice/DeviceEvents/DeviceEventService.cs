using Common.Extension;

namespace IotGateway.EdgeDevice.DeviceEvents;

/// <summary>
///     设备事件
/// </summary>
[ApiDescriptionSettings("设备管理")]
public class DeviceEventService : ITransient, IDynamicApiController
{
    private readonly SqlSugarRepository<DeviceEvent> _deviceEvent;
    private readonly DeviceHostedService _deviceHostedService;

    public DeviceEventService(SqlSugarRepository<DeviceEvent> deviceEvent, DeviceHostedService deviceHostedService)
    {
        _deviceEvent = deviceEvent;
        _deviceHostedService = deviceHostedService;
    }

    /// <summary>
    ///     设备事件列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceEvent/page")]
    public async Task<SqlSugarPagedList<DeviceEvent>> Page([FromQuery] DeviceEventPageInput input)
    {
        var deviceEvents = await _deviceEvent.AsQueryable()
            .Where(w => w.DeviceId == input.DeviceId)
            .WhereIF(input.TriggerEventType > 0, w => w.TriggerEventType == (TriggerEventTypeEnum) input.TriggerEventType)
            .WhereIF(!string.IsNullOrEmpty(input.EventName), u => u.EventName.Contains(input.EventName))
            .WhereIF(input.Status != null, w => w.Status == input.Status)
            .OrderByDescending(u => u.Id)
            .Includes(w => w.Device)
            .ToPagedListAsync(input.PageNo, input.PageSize);

        foreach (var deviceEvent in deviceEvents.Rows)
        {
            var deviceEventLogs = await _deviceEvent.AsSugarClient().Queryable<DeviceEventLog>().Where(w => deviceEvent.Id == w.DeviceEventId)
                .OrderByDescending(w => w.CreatedTime)
                .FirstAsync();
            if (deviceEventLogs != null)
                deviceEvent.LastTime = deviceEventLogs.CreatedTime;
        }

        return deviceEvents;
    }

    /// <summary>
    ///     设备事件新增
    /// </summary>
    /// <returns></returns>
    [HttpPost("/deviceEvent/add")]
    public async Task DeviceEventAdd([FromBody] DeviceEventAddInput input)
    {
        if (await _deviceEvent.IsAnyAsync(a => a.EventName == input.EventName && a.DeviceId == input.DeviceId))
            throw Oops.Oh("事件名称已经存在！");
        var device = await _deviceEvent.AsSugarClient().Queryable<Device>().FirstAsync(f => f.Id == input.DeviceId);
        if (device == null)
            throw Oops.Oh("设备已经被删除！");
        var deviceEvent = input.Adapt<DeviceEvent>();
        deviceEvent.Device = device;
        foreach (var customEventWhere in deviceEvent.CustomEventWhereList)
        {
            // 生成条件表达式
            customEventWhere.Expressions = EventDeviceVariable(customEventWhere.TriggerConditionList);

            // 生成动作条件表达式
            foreach (var customEventAction in customEventWhere.CustomEventActionList)
            {
                // 处理触发条件
                foreach (var eventDeviceVariable in customEventWhere.TriggerConditionList.SelectMany(triggerCondition => triggerCondition.EventDeviceVariable))
                    eventDeviceVariable.Value = eventDeviceVariable.Value.GetJsonElementValue();

                if (customEventAction.DeviceVariableWrite.Any())
                    // 处理设置的值
                    foreach (var deviceVariableWrite in customEventAction.DeviceVariableWrite)
                        deviceVariableWrite.Value = deviceVariableWrite.Value.GetJsonElementValue();

                if (customEventAction.TriggerConditionList.Any())
                    customEventAction.Expressions = EventDeviceVariable(customEventAction.TriggerConditionList);
            }
        }

        await _deviceEvent.CopyNew().InsertAsync(deviceEvent);
        // 创建事件
        await _deviceHostedService.CreateDeviceEvent(deviceEvent);
    }

    /// <summary>
    ///     设备事件修改
    /// </summary>
    /// <returns></returns>
    [HttpPost("/deviceEvent/update")]
    public async Task DeviceEventUpdate([FromBody] DeviceEvent input)
    {
        if (await _deviceEvent.IsAnyAsync(a => a.EventName == input.EventName && a.DeviceId == input.DeviceId && a.Id != input.Id))
            throw Oops.Oh("事件名称已经存在！");
        var deviceEvent = await _deviceEvent.AsQueryable().Where(f => f.Id == input.Id).Includes(w => w.Device).FirstAsync();
        if (deviceEvent == null)
            throw Oops.Oh("数据已经被删除！");
        // 停止事件
        await _deviceHostedService.RemoveDeviceEvent(deviceEvent);

        var deviceEventMap = input.Adapt<DeviceEvent>();
        deviceEventMap.Device = deviceEvent.Device;
        foreach (var customEventWhere in deviceEventMap.CustomEventWhereList)
        {
            // 生成条件表达式
            customEventWhere.Expressions = EventDeviceVariable(customEventWhere.TriggerConditionList);
            // 生成动作条件表达式
            foreach (var customEventAction in customEventWhere.CustomEventActionList)
            {
                // 处理触发条件
                foreach (var eventDeviceVariable in customEventWhere.TriggerConditionList.SelectMany(triggerCondition => triggerCondition.EventDeviceVariable))
                    eventDeviceVariable.Value = eventDeviceVariable.Value.GetJsonElementValue();

                if (customEventAction.DeviceVariableWrite.Any())
                    // 处理设置的值
                    foreach (var deviceVariableWrite in customEventAction.DeviceVariableWrite)
                        deviceVariableWrite.Value = deviceVariableWrite.Value.GetJsonElementValue();

                if (customEventAction.TriggerConditionList.Any())
                    customEventAction.Expressions = EventDeviceVariable(customEventAction.TriggerConditionList);
            }
        }

        // 创建事件
        await _deviceHostedService.CreateDeviceEvent(deviceEventMap);
        await _deviceEvent.CopyNew().UpdateAsync(deviceEventMap);
    }

    /// <summary>
    ///     设备事件启用/禁用
    /// </summary>
    /// <returns></returns>
    [HttpPost("/deviceEvent/enable")]
    public async Task DeviceEventEnable([FromBody] EnableInput<List<long>> input)
    {
        var deviceEventList = await _deviceEvent.AsQueryable()
            .Where(f => input.Id.Contains(f.Id))
            .Includes(w => w.Device)
            .ToListAsync();
        foreach (var deviceEvent in deviceEventList.Where(deviceEvent => deviceEvent.Status != input.Enable))
        {
            if (deviceEvent.Status)
                // 停止事件
                await _deviceHostedService.RemoveDeviceEvent(deviceEvent);
            else
                // 创建事件
                await _deviceHostedService.CreateDeviceEvent(deviceEvent);

            deviceEvent.Status = input.Enable;
        }

        await _deviceEvent.CopyNew().UpdateRangeAsync(deviceEventList);
    }

    /// <summary>
    ///     设备事件删除
    /// </summary>
    /// <returns></returns>
    [HttpPost("/deviceEvent/delete")]
    public async Task DeviceEventDelete([FromBody] BaseId<List<long>> input)
    {
        var deviceEventList = await _deviceEvent.AsQueryable()
            .Where(f => input.Id.Contains(f.Id))
            .ToListAsync();
        foreach (var deviceEvent in deviceEventList)
            // 停止事件
            await _deviceHostedService.RemoveDeviceEvent(deviceEvent);
        await _deviceEvent.CopyNew().DeleteAsync(deviceEventList);
    }

    /// <summary>
    ///     生成条件表达式
    /// </summary>
    /// <param name="triggerConditionList"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    private string EventDeviceVariable(List<TriggerConditionList> triggerConditionList)
    {
        var expressions = "";
        foreach (var eventDeviceVariables in triggerConditionList)
        {
            // 分组条件
            var expression = !string.IsNullOrWhiteSpace(eventDeviceVariables.Type) ? eventDeviceVariables.Type == "and" ? " && (" : " || (" : "(";

            foreach (var eventDeviceVariable in eventDeviceVariables.EventDeviceVariable)
            {
                if (!string.IsNullOrWhiteSpace(eventDeviceVariable.Type))
                    expression += eventDeviceVariable.Type == "and" ? " && " : " || ";
                // 真实值
                eventDeviceVariable.Value = eventDeviceVariable.Value.GetJsonElementValue();
                var value = eventDeviceVariable.Value;
                var identifier = eventDeviceVariable.Identifier;
                // 上一次执行结果
                if (identifier == "${this.isSuccess}")
                {
                    expression += "lastRunResult" + " == " + value?.ToString()?.ToLower();
                    continue;
                }

                if (eventDeviceVariable.DataType is DataTypeEnum.String or DataTypeEnum.Byte)
                    value = "\"" + eventDeviceVariable.Value + "\"";
                switch (eventDeviceVariable.Compare)
                {
                    case CompareEnum.值变化时执行:
                        expression += identifier + ".Value != " + identifier + ".CookieValue";
                        break;
                    case CompareEnum.时间变化时执行:
                        expression += identifier + ".ReadTime != " + identifier + ".CookieTime";
                        break;
                    case CompareEnum.等于:
                        expression += identifier + ".Value == " + value;
                        break;
                    case CompareEnum.大于:
                        expression += identifier + ".Value >= " + value;
                        break;
                    case CompareEnum.小于:
                        expression += identifier + ".Value <= " + value;
                        break;
                    case CompareEnum.不等于:
                        expression += identifier + ".Value != " + value;
                        break;
                    default:
                        throw Oops.Oh("操作符暂不支持！");
                }
            }

            expression += ")";
            expressions += expression;
        }

        return expressions;
    }
}