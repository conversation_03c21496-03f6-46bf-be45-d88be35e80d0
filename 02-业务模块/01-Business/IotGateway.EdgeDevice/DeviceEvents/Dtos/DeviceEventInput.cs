namespace IotGateway.EdgeDevice.DeviceEvents;

/// <summary>
/// 设备事件列表
/// </summary>
public class DeviceEventPageInput : BasePageInput
{
    /// <summary>
    /// 设备Id
    /// </summary>
    [Required]
    public long DeviceId{ get; set; }
    
    /// <summary>
    ///     触发方式：1属性触发；2定时触发；3连接成功触发；4连接断开触发
    /// </summary>
    public int TriggerEventType { get; set; }

    /// <summary>
    ///     事件名称
    /// </summary>
    public string EventName { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    public bool? Status { get; set; }
}

/// <summary>
/// 设备事件新增 
/// </summary>
public class DeviceEventAddInput
{
    /// <summary>
    ///     设备Id
    /// </summary>
    [Required]
    public long DeviceId { get; set; }

    /// <summary>
    ///     触发方式：1属性触发；2定时触发；3设备触发
    /// </summary>
    [Required]
    public TriggerEventTypeEnum TriggerEventType { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [Required]
    public string EventName { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    [Required]
    public bool Status { get; set; }

    /// <summary>
    ///     事件配置参数
    /// </summary>
    [SugarColumn(ColumnDescription = "事件配置参数", IsJson = true, ColumnDataType = StaticConfig.CodeFirst_BigString, IsNullable = true)]
    public CustomEventConfig CustomEventConfig { get; set; }

    /// <summary>
    ///     事件执行条件
    /// </summary>
    [SugarColumn(ColumnDescription = "事件执行条件", IsJson = true, ColumnDataType = StaticConfig.CodeFirst_BigString, IsNullable = true)]
    public List<CustomEventWhere> CustomEventWhereList { get; set; }
}
