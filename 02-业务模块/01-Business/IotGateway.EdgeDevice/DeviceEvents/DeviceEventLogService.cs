using Microsoft.Extensions.Logging;
using DateTime = System.DateTime;

namespace IotGateway.EdgeDevice.DeviceEvents;

/// <summary>
///     设备事件日志
/// </summary>
[ApiDescriptionSettings("设备管理")]
public class DeviceEventLogService : ITransient, IDynamicApiController
{
    private readonly SqlSugarRepository<DeviceEventLog> _deviceEventLog;
    private readonly ILogger<DeviceEventLogService> _logger;

    /// <summary>
    /// </summary>
    /// <param name="deviceEvent"></param>
    /// <param name="logger"></param>
    public DeviceEventLogService(SqlSugarRepository<DeviceEventLog> deviceEvent, ILogger<DeviceEventLogService> logger)
    {
        _deviceEventLog = deviceEvent;
        _logger = logger;
    }

    /// <summary>
    ///     设备事件日志列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceEventLog/page")]
    public async Task<SqlSugarPagedList<DeviceEventLog>> Page([FromQuery] DeviceEventLogPageInput input)
    {
        var deviceEvents = await _deviceEventLog.AsQueryable()
            .Where(w => w.DeviceId == input.DeviceId)
            .WhereIF(input.DeviceEventId > 0, w => w.DeviceEventId == input.DeviceEventId)
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue), w => w.EventType.Contains(input.SearchValue)
                                                                    || w.Description.Contains(input.SearchValue))
            .WhereIF(!string.IsNullOrEmpty(input.SearchBeginTime?.Trim()), u => u.CreatedTime >= DateTime.Parse(input.SearchBeginTime.Trim()) &&
                                                                                u.CreatedTime <= DateTime.Parse(input.SearchEndTime.Trim()))
            .OrderByDescending(u => u.CreatedTime)
            .Includes(w => w.Device)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return deviceEvents;
    }

    /// <summary>
    ///     清空设备事件日志
    /// </summary>
    /// <returns></returns>
    [HttpPost("/deviceEventLog/clear")]
    public async Task Clear(BaseId input)
    {
        var delete = await _deviceEventLog.CopyNew().DeleteAsync(w => w.DeviceId == input.Id);
        if (!delete)
            throw Oops.Oh(ErrorCode.Com1002);
    }

    /// <summary>
    ///     添加设备事件日志
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task Add(DeviceEventLog input)
    {
        await _deviceEventLog.CopyNew().InsertAsync(input);
    }

    /// <summary>
    ///     删除设备事件
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task Delete()
    {
        var deleteNumber = await _deviceEventLog.CopyNew().AsDeleteable()
            .Where(it => it.CreatedTime.Date < Common.Extension.DateTime.Now().AddDays(-1).Date)
            .ExecuteCommandAsync();
        _logger.LogWarning($"【设备事件日志】 定时清理消息【{deleteNumber}】条");
    }
}