using Feng.IotGateway.Core.Service.Cache;
using Furion.TimeCrontab;
using IotGateway.EdgeDevice.DeviceEvents;
using Microsoft.Extensions.Logging;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.EdgeDevice.BackgroundServices;

/// <summary>
///     定时清理设备事件消息，授权信息检查,清理站内信消息
/// </summary>
public class DropDeviceEventTimer : BackgroundService
{
    private readonly ILogger<DropDeviceEventTimer> _logger;
    private readonly Crontab _crontab;
    private readonly IServiceScopeFactory _scopeFactory;

    public DropDeviceEventTimer(ILogger<DropDeviceEventTimer> logger, IServiceScopeFactory scopeFactory)
    {
        _logger = logger;
        _scopeFactory = scopeFactory;
        _crontab = Crontab.Parse("00 00 0/12 ? * *", CronStringFormat.WithSeconds);
    }

    /// <summary>
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var taskFactory = new TaskFactory(TaskScheduler.Current);
            await taskFactory.StartNew(async () =>
            {
                using var scope = _scopeFactory.CreateScope();
                var services = scope.ServiceProvider;
                try
                {
                    await services.GetService<DeviceEventLogService>()?.Delete()!;
                }
                catch (Exception ex)
                {
                    _logger.LogError($"删除设备事件消息 Error:【{ex.Message}】");
                }

                try
                {
                    var auth = GlobalConfigManager.GetConfigValue<bool>(ConfigConst.Authorization);
                    // 检查授权是否过期
                    var authorized = await AuthorizationUtil.GetAuthorization();
                    if (authorized == null)
                    {
                        if (auth)
                            await services.GetService<DeviceHostedService>()?.RemoveDeviceThreadAll()!;
                    }
                    else
                    {
                        // 开启校验-并且已经过期
                        if (DateTime.Now() > Convert.ToDateTime(authorized.EndTime) && auth)
                        {
                            _logger.LogWarning($"【授权过期】 授权有效期:{authorized.EndTime},已过期，停止采集！");
                            await services.GetService<DeviceHostedService>()?.RemoveDeviceThreadAll()!;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"【授权检查】 Error:【{ex.Message}】");
                }
            }, stoppingToken);
            await Task.Delay((int) _crontab.GetSleepMilliseconds(DateTime.Now()), stoppingToken);
        }
    }
}