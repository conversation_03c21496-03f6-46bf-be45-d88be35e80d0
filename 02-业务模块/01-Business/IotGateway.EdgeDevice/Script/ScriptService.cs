using IotGateway.Application.Script.Dto;

namespace IotGateway.EdgeDevice.Script;

/// <summary>
///     系统脚本
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2022-10-01
/// </summary>
[ApiDescriptionSettings("边缘计算")]
public class ScriptService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<SysScript> _script;

    public ScriptService(SqlSugarRepository<SysScript> script)
    {
        _script = script;
    }

    /// <summary>
    ///     系统脚本集合
    /// </summary>
    /// <returns></returns>
    [HttpGet("/script/list")]
    public async Task<List<SysScript>> ScriptList([FromQuery] SysScriptListInput input)
    {
        return await _script.AsQueryable()
            .WhereIF(input != null && input.ScriptType.Any(), w => input.ScriptType.Contains((int) w.ScriptType))
            .WhereIF(input != null && (int) input.Method != 0, w => w.Method == input.Method)
            .OrderBy(w => w.ScriptType)
            .ToListAsync();
    }
}