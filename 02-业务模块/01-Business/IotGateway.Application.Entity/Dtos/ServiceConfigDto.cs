using Feng.IotGateway.Core.Base;

namespace IotGateway.Application.ServiceConfig.Dtos;

/// <summary>
/// 服务配置详情输出
/// </summary>
public class ServiceConfigDetailOutput
{
  /// <summary>
  /// 服务类型
  /// </summary>
  public string ServiceType { get; set; }

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool Enabled { get; set; }

  /// <summary>
  /// 最大连接数
  /// </summary>
  public int MaxConnections { get; set; }

  /// <summary>
  /// 超时时间(ms)
  /// </summary>
  public int Timeout { get; set; }

  /// <summary>
  /// 扩展配置(JSON格式)
  /// </summary>
  public ExtConfig ExtConfig { get; set; }
}

/// <summary>
/// 
/// </summary>
public class ServiceConfigDetail : ServiceConfigDetailOutput
{
  /// <summary>
  /// 映射配置
  /// </summary>
  public object MappingConfig { get; set; }
}

/// <summary>
/// 扩展配置
/// </summary>
public class ExtConfig
{
  /// <summary>
  /// 配置模式
  /// </summary>
  public ExtConfigSchema Schema { get; set; }

  /// <summary>
  /// 配置内容
  /// </summary>
  public object Config { get; set; }
}

/// <summary>
/// 扩展配置模式
/// </summary>
public class ExtConfigSchema
{
  /// <summary>
  /// 属性键值对
  /// </summary>
  public Dictionary<string, object> Properties { get; set; }

  /// <summary>
  /// 必填属性
  /// </summary>
  public string[] Required { get; set; }
}

/// <summary>
/// 服务配置更新输入
/// </summary>
public class ServiceConfigUpdateInput
{
  /// <summary>
  /// 服务类型(必填)
  /// </summary>
  [Required(ErrorMessage = "服务类型不能为空")]
  public string ServiceType { get; set; }

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool Enabled { get; set; }

  /// <summary>
  /// 最大连接数(必填)
  /// </summary>
  [Range(1, 10000, ErrorMessage = "最大连接数必须在1-10000之间")]
  public int MaxConnections { get; set; }

  /// <summary>
  /// 超时时间(必填)
  /// </summary>
  [Range(100, 300000, ErrorMessage = "超时时间必须在100-300000ms之间")]
  public int Timeout { get; set; }

  /// <summary>
  /// 配置信息
  /// </summary>
  public object Config { get; set; }
}

/// <summary>
/// 服务配置更新事件
/// </summary>
public class ServiceConfigUpdateEvent
{
  /// <summary>
  /// 服务类型
  /// </summary>
  [Required]
  public string ServiceType { get; set; }

  /// <summary>
  /// 配置信息
  /// </summary>
  public ServiceConfigUpdateInput Config { get; set; }
}

/// <summary>
/// 服务配置启用/禁用输入
/// </summary>
public class EnableServiceConfigInput
{
  /// <summary>
  /// 服务类型
  /// </summary>
  [Required]
  public string ServiceType { get; set; }
  /// <summary>
  /// 禁用/启用
  /// </summary>
  public bool Enable { get; set; }
}

/// <summary>
/// 获取映射配置输入
/// </summary>
public class GetMappingConfigInput : BasePageInput
{
  /// <summary>
  /// 服务类型不能为空
  /// </summary>
  [Required(ErrorMessage = "服务类型不能为空")]
  public string ServiceType { get; set; }

  /// <summary>
  /// 分区
  /// </summary>
  public string Partition { get; set; }

  /// <summary>
  /// 数据类型过滤
  /// </summary>
  public string DataType { get; set; }
}

/// <summary>
/// 更新映射配置输入
/// </summary>
public class UpdateMappingConfigInput
{
  /// <summary>
  /// 服务类型不能为空
  /// </summary>
  [Required(ErrorMessage = "服务类型不能为空")]
  public string ServiceType { get; set; }

  /// <summary>
  /// modbus映射配置
  /// </summary>
  public Dictionary<string, List<ModbusMappingConfig>>? ModbusMappingConfig { get; set; }

  /// <summary>
  /// opcua映射配置
  /// </summary>
  public Dictionary<string, List<OpcuaMappingConfig>>? OpcuaMappingConfig { get; set; }
}
/// <summary>
/// modbus映射配置
/// </summary>
public class ModbusMappingConfig
{
  /// <summary>
  /// 设备
  /// </summary>
  public string DeviceName { get; set; }

  /// <summary>
  /// 设备变量
  /// </summary>
  public string VariableName { get; set; }

  /// <summary>
  /// Modbus地址
  /// </summary>
  public string Address { get; set; }

  /// <summary>
  /// 数据类型
  /// </summary>
  public string DataType { get; set; }
}

/// <summary>
/// opcua映射配置
/// </summary>
public class OpcuaMappingConfig
{
  /// <summary>
  /// 设备
  /// </summary>
  public string DeviceName { get; set; }

  /// <summary>
  /// 设备变量  
  /// </summary>
  public string VariableName { get; set; }

  /// <summary>
  /// 变量中文
  /// </summary>
  public string DisplayName { get; set; }

  /// <summary>
  /// 描述
  /// </summary>
  public string Description { get; set; }

  /// <summary>
  /// 节点id
  /// </summary>
  public string NodeId { get; set; }

  /// <summary>
  /// 数据类型
  /// </summary>
  public string DataType { get; set; }
}



/// <summary>
/// 批量删除映射配置输入参数
/// </summary>
public class DeleteMappingConfigInput
{
  /// <summary>
  /// 服务类型
  /// </summary>
  [Required(ErrorMessage = "服务类型不能为空")]
  public string ServiceType { get; set; }

  /// <summary>
  /// 分区名称
  /// </summary>
  [Required(ErrorMessage = "分区名称不能为空")]
  public string Partition { get; set; }

  /// <summary>
  /// 要删除的映射项列表，包含设备名称和变量名称
  /// </summary>
  public List<MappingItem> Items { get; set; } = new List<MappingItem>();
}

/// <summary>
/// 映射项（用于批量删除）
/// </summary>
public class MappingItem
{
  /// <summary>
  /// 设备名称
  /// </summary>
  [Required(ErrorMessage = "设备名称不能为空")]
  public string DeviceName { get; set; }

  /// <summary>
  /// 变量名称
  /// </summary>
  [Required(ErrorMessage = "变量名称不能为空")]
  public string VariableName { get; set; }
}

/// <summary>
///     分区操作基础输入
/// </summary>
public class PartitionBaseInput
{
  /// <summary>
  ///     服务类型
  /// </summary>
  [Required(ErrorMessage = "服务类型不能为空")]
  public string ServiceType { get; set; }
}

/// <summary>
///     创建分区输入
/// </summary>
public class CreatePartitionInput : PartitionBaseInput
{
  /// <summary>
  ///     分区名称
  /// </summary>
  [Required(ErrorMessage = "分区名称不能为空")]
  public string PartitionName { get; set; }
}

/// <summary>
///     重命名分区输入
/// </summary>
public class RenamePartitionInput : PartitionBaseInput
{
  /// <summary>
  ///     原分区名称
  /// </summary>
  [Required(ErrorMessage = "原分区名称不能为空")]
  public string OldPartitionName { get; set; }

  /// <summary>
  ///     新分区名称
  /// </summary>
  [Required(ErrorMessage = "新分区名称不能为空")]
  public string NewPartitionName { get; set; }
}

/// <summary>
///     删除分区输入
/// </summary>
public class DeletePartitionInput : PartitionBaseInput
{
  /// <summary>
  ///     分区名称
  /// </summary>
  [Required(ErrorMessage = "分区名称不能为空")]
  public string PartitionName { get; set; }
}

/// <summary>
///     获取分区列表输出
/// </summary>
public class PartitionListOutput
{
  /// <summary>
  ///     分区名称列表
  /// </summary>
  public List<string> Partitions { get; set; } = new List<string>();

  /// <summary>
  ///     每个分区的映射项数量
  /// </summary>
  public Dictionary<string, int> ItemCounts { get; set; } = new Dictionary<string, int>();
}
