using Feng.Common.Util;
using Feng.IotGateway.Core.Enums;
using Feng.IotGateway.Core.Extension;

namespace IotGateway.Application.Entity;

/// <summary>
///     脚本执行策略
/// </summary>
[SugarTable("script_ExecutionStrategy", "脚本执行策略")]
public class ScriptExecutionStrategy : EntityBaseId
{
    /// <summary>
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(EdgeComputingScriptId))]
    public EdgeComputingScript EdgeComputingScript { get; set; }

    /// <summary>
    ///     边缘计算脚本Id
    /// </summary>
    [SugarColumn(ColumnDescription = "边缘计算脚本Id")]
    public long EdgeComputingScriptId { get; set; }

    /// <summary>
    ///     执行模式:1网关启动；2定时；3属性；4变量
    /// </summary>
    public ExecutionStrategyTypeEnum ExecutionStrategyType { get; set; }

    /// <summary>
    ///     执行模式配置
    /// </summary>
    public string? Config { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述")]
    public string Describe { get; set; }

    /// <summary>
    ///     启用/禁用
    /// </summary>
    [SugarColumn(ColumnDescription = "启用/禁用")]
    public bool Enable { get; set; } = true;

    /// <summary>
    ///     周期执行的配置实体
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public ExecutionTime? ExecutionTime => ExecutionStrategyType == ExecutionStrategyTypeEnum.定时触发 && Config != null ? Config.ToObject<ExecutionTime>() : null;

    /// <summary>
    ///     属性触发任务的配置实体
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public PropertyModel? Propertys => ExecutionStrategyType == ExecutionStrategyTypeEnum.属性触发 && Config != null ? Config.ToObject<PropertyModel>() : null;

    /// <summary>
    ///     变量触发的配置实体
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public VariableModel? Variable => ExecutionStrategyType == ExecutionStrategyTypeEnum.变量触发 && Config != null ? Config.ToObject<VariableModel>() : null;

    #region 忽略字段

    /// <summary>
    ///     执行模式:1网关启动；2定时；3属性；4变量；5消息；6Http
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string ExecutionStrategyTypeName => EnumUtil.GetEnumDesc(ExecutionStrategyType);

    #endregion
}

/// <summary>
///     属性触发任务配置
/// </summary>
public class PropertyModel
{
    /// <summary>
    ///     属性名称
    /// </summary>
    public List<string> Name { get; set; }

    /// <summary>
    ///     1.值变化时执行;2:时间触发时执行;3:等于;4：大于；5：小于；6：不等于
    /// </summary>
    public CompareEnum Compare { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public string Value { get; set; }

    /// <summary>
    ///     首次是否触发
    /// </summary>
    public bool FirstEven { get; set; }
}

/// <summary>
///     变量触发任务
/// </summary>
public class VariableModel
{
    /// <summary>
    ///     变量Key
    /// </summary>
    public string Key { get; set; }
}