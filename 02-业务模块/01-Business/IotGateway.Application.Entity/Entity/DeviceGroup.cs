namespace IotGateway.Application.Entity;

/// <summary>
/// </summary>
[SugarTable("deviceGroup", "设备分组")]
public class DeviceGroup : EntityBaseId
{
    /// <summary>
    /// </summary>
    [SugarColumn(ColumnDescription = "编码", Length = 50)]
    public string Name { get; set; }

    /// <summary>
    ///     父级
    /// </summary>
    [SugarColumn(ColumnDescription = "父级", Length = 50)]
    public long ParentId { get; set; }

    /// <summary>
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ParentId))] //设置导航 一对一
    [SugarColumn(IsIgnore = true)]
    public DeviceGroup Parent { get; set; }

    /// <summary>
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<DeviceGroup> Children { get; set; }
}