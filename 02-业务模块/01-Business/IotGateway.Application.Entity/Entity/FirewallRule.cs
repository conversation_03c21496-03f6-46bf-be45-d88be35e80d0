namespace IotGateway.Application.Entity;

/// <summary>
///     防火墙规则表
/// </summary>
[SugarTable("firewall_rule", "防火墙规则表")]
public class FirewallRule : EntityBaseId
{
  /// <summary>
  ///     IP地址或网段
  /// </summary>
  [SugarColumn(ColumnDescription = "IP地址或网段", Length = 64)]
  public string IpAddress { get; set; }

  /// <summary>
  ///     端口范围，如：22或22-80
  /// </summary>
  [SugarColumn(ColumnDescription = "端口范围", Length = 32, IsNullable = true)]
  public string PortRange { get; set; }

  /// <summary>
  ///     协议类型：tcp, udp, icmp, all
  /// </summary>
  [SugarColumn(ColumnDescription = "协议类型", Length = 16)]
  public string Protocol { get; set; }

  /// <summary>
  ///     规则类型：0-拒绝(黑名单)，1-允许(白名单)
  /// </summary>
  [SugarColumn(ColumnDescription = "规则类型：0-拒绝，1-允许")]
  public int RuleType { get; set; }

  /// <summary>
  ///     描述
  /// </summary>
  [SugarColumn(ColumnDescription = "描述", Length = 255, IsNullable = true)]
  public string Description { get; set; }

  /// <summary>
  ///     生成的命令
  /// </summary>
  [SugarColumn(ColumnDescription = "生成的命令", Length = 512)]
  public string Command { get; set; }

  /// <summary>
  ///     是否启用
  /// </summary>
  [SugarColumn(ColumnDescription = "是否启用")]
  public bool Enable { get; set; } = true;
  
}