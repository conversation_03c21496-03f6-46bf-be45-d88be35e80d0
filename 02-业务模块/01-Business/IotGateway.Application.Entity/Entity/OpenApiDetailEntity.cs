using Feng.IotGateway.Core.Enums;

namespace IotGateway.Application.Entity;

/// <summary>
///     开放Api-明细
/// </summary>
[SugarTable("openApiDetail", " 开放Api-明细")]
public class OpenApiDetailEntity : EntityBaseId
{
    /// <summary>
    ///     开放Api-Id
    /// </summary>
    [SugarColumn(ColumnDescription = "开放Api-Id")]
    public long OpenApiEntityId { get; set; }

    /// <summary>
    ///     开放Api
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(OpenApiEntityId))]
    public OpenApiEntity OpenApiEntity { get; set; }

    /// <summary>
    ///     所属模块编码
    /// </summary>
    [SugarColumn(ColumnDescription = "所属模块编码", Length = 128)]
    public string ModuleCore { get; set; }

    /// <summary>
    ///     所属模块
    /// </summary>
    [SugarColumn(ColumnDescription = "所属模块", Length = 128)]
    public string Module { get; set; }

    /// <summary>
    ///     Api名称
    /// </summary>
    [SugarColumn(ColumnDescription = "Api名称", Length = 128)]
    public string Name { get; set; }

    /// <summary>
    ///     Path
    /// </summary>
    [SugarColumn(ColumnDescription = "Path", Length = 256)]
    public string Path { get; set; }

    /// <summary>
    ///     参数
    /// </summary>
    [SugarColumn(IsJson = true)]
    public List<RequestParams> Params { get; set; }

    /// <summary>
    ///     Http请求方式:1:GET;2POST
    /// </summary>
    public HttpRequestTypeEnum HttpRequestType { get; set; }

    /// <summary>
    ///     返回结果
    /// </summary>
    [SugarColumn(IsJson = true)]
    public List<ResultParams> Result { get; set; }
}

/// <summary>
/// </summary>
public class RequestParams
{
    /// <summary>
    ///     属性名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     描述信息
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    ///     字段类型 String
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    ///     是否必填
    /// </summary>
    public bool IsRequired { get; set; }
}

public class ResultParams
{
    /// <summary>
    ///     属性名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     描述信息
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    ///     字段类型 String
    /// </summary>
    public string Type { get; set; }
}