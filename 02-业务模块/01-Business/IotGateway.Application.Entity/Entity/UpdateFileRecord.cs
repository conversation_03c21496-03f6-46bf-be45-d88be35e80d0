namespace IotGateway.Application.Entity;

/// <summary>
///     远程更新记录
/// </summary>
[SugarTable("updateFile_record", "远程更新记录")]
public class UpdateFileRecord : EntityBaseId
{
    /// <summary>
    ///     固件更新包
    /// </summary>
    [SugarColumn(ColumnDescription = " 名称", Length = 128)]
    public string Name { get; set; }

    /// <summary>
    ///     版本
    /// </summary>
    [SugarColumn(ColumnDescription = "版本")]
    public string Version { get; set; }

    /// <summary>
    ///     是否下载
    /// </summary>
    [SugarColumn(ColumnDescription = "是否下载")]
    public bool IsDown { get; set; }

    /// <summary>
    ///     是否升级完成
    /// </summary>
    [SugarColumn(ColumnDescription = "是否升级完成")]
    public bool IsUpdate { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间")]
    public DateTime CreateTime { get; set; }

    /// <summary>
    ///     id
    /// </summary>
    [SugarColumn(ColumnDescription = "id")]
    public long RecordId { get; set; }

    /// <summary>
    ///     文件名称
    /// </summary>
    [SugarColumn(ColumnDescription = "文件名称",IsNullable = true)]
    public string? FileName { get; set; }
}