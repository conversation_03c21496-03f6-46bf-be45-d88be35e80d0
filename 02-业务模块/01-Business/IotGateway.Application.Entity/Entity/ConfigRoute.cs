namespace IotGateway.Application.Entity;

/// <summary>
///     路由配置
/// </summary>
[SugarTable("configRoute", "路由配置")]
public class ConfigRoute : EntityBaseId
{
    /// <summary>
    ///     目的地址
    /// </summary>
    [SugarColumn(ColumnDescription = "目的地址", Length = 64)]
    public string InsideAddress { get; set; }

    /// <summary>
    ///     子网掩码
    /// </summary>
    [SugarColumn(ColumnDescription = "子网掩码")]
    public short Genmask { get; set; }

    /// <summary>
    ///     出接口
    /// </summary>
    [SugarColumn(ColumnDescription = "出接口")]
    public string OutInterface { get; set; }

    /// <summary>
    ///     下一跳
    /// </summary>
    [SugarColumn(ColumnDescription = "下一跳", IsNullable = true)]
    public string Via { get; set; }

    /// <summary>
    ///     优先级，值越小优先级越高
    /// </summary>
    [SugarColumn(ColumnDescription = "优先级，值越小优先级越高")]
    public short Metric { get; set; }

    /// <summary>
    /// 生成的命令
    /// </summary>
    [SugarColumn(ColumnDescription = "生成的命令")]
    public string Command { get; set; }
}