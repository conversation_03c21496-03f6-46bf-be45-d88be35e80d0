namespace IotGateway.Application.Entity;

/// <summary>
///     定义备份记录的数据模型
/// </summary>
[SugarTable("backup")]
public class BackupRecord : EntityBaseId
{
    /// <summary>
    ///     备份时间
    /// </summary>
    public DateTime BackupTime { get; set; }

    /// <summary>
    ///     备份数据库名称
    /// </summary>
    public string BackupFileName { get; set; }

    /// <summary>
    ///     备份路径
    /// </summary>
    public string BackupFilePath { get; set; }

    /// <summary>
    ///     是否压缩
    /// </summary>
    public bool Compress { get; set; }

    /// <summary>
    ///     下载次数
    /// </summary>
    public short Download { get; set; }

    /// <summary>
    ///     是否已经删除
    /// </summary>
    public bool IsDelete { get; set; }
}