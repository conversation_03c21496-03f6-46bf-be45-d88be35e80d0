using System.ComponentModel;
using Feng.Common.Util;

namespace IotGateway.Application.Entity;

/// <summary>
///     数据连接
/// </summary>
[SugarTable("DbLink")]
public class DbLinkEntity : EntityBase
{
    /// <summary>
    ///     连接名称
    /// </summary>
    [SugarColumn(ColumnDescription = "连接名称")]
    public string FullName { get; set; }

    /// <summary>
    ///     连接驱动
    /// </summary>
    [SugarColumn(ColumnDescription = "连接驱动")]
    public DbTypeEnum DbType { get; set; }

    /// <summary>
    ///     主机名称
    /// </summary>
    [SugarColumn(ColumnDescription = "主机名称")]
    public string Host { get; set; }

    /// <summary>
    ///     端口
    /// </summary>
    [SugarColumn(ColumnDescription = "端口")]
    public int Port { get; set; }

    /// <summary>
    ///     用户
    /// </summary>
    [SugarColumn(ColumnDescription = "用户")]
    public string UserName { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    [SugarColumn(ColumnDescription = "密码")]
    public string Password { get; set; }

    /// <summary>
    ///     服务名称
    /// </summary>
    [SugarColumn(ColumnDescription = "服务名称")]
    public string ServiceName { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述",IsNullable = true)]
    public string? Description { get; set; }

    /// <summary>
    ///     排序码
    /// </summary>
    [SugarColumn(ColumnDescription = "排序码")]
    public long? SortCode { get; set; }

    /// <summary>
    ///     数据库类型
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string DbTypeName => EnumUtil.GetEnumDesc(DbType);
}

/// <summary>
///     数据源类型:1:mysql；2:sqlserver；
/// </summary>
public enum DbTypeEnum
{
    /// <summary>
    ///     MySQL
    /// </summary>
    [Description("MySQL")] MySql = 1,

    /// <summary>
    ///     SQLServer
    /// </summary>
    [Description("SQLServer")] SqlServer = 2
}