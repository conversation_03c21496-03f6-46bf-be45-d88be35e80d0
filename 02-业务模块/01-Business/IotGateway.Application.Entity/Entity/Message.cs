namespace IotGateway.Application.Entity;

/// <summary>
///     站内信表
/// </summary>
[SugarTable("message", "站内信表")]
public class Message : EntityBaseId
{
    /// <summary>
    ///     标题
    /// </summary>
    [SugarColumn(ColumnDescription = "标题")]
    public string Title { get; set; }

    /// <summary>
    ///     内容
    /// </summary>
    [SugarColumn(ColumnDescription = "内容")]
    public string Content { get; set; }

    /// <summary>
    ///     消息类型:info,warning,error
    /// </summary>
    [SugarColumn(ColumnDescription = "消息类型:info,warning,error")]
    public string Level { get; set; }

    /// <summary>
    ///     已读
    /// </summary>
    [SugarColumn(ColumnDescription = "已读")]
    public bool Read { get; set; }
    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间")]
    public DateTime? CreatedTime { get; set; }
}