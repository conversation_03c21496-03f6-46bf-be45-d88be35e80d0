namespace IotGateway.Application.Entity;

/// <summary>
///     边缘计算脚本
/// </summary>
[SugarTable("edge_ComputingScript", "边缘计算脚本")]
public class EdgeComputingScript : EntityBaseId
{
    /// <summary>
    ///     脚本名称
    /// </summary>
    [SugarColumn(ColumnDescription = "脚本名称", Length = 64)]
    public string ScriptName { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    [SugarColumn(ColumnDescription = "脚本内容", ColumnDataType = "longtext,text,clob")]
    public string Content { get; set; }

    /// <summary>
    ///     启用/禁用
    /// </summary>
    [SugarColumn(ColumnDescription = "启用/禁用")]
    public bool Enable { get; set; } = true;

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述")]
    public string Describe { get; set; }

    /// <summary>
    ///     边缘计算脚本执行策略
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Entity.ScriptExecutionStrategy.EdgeComputingScriptId))]
    [SugarColumn(IsIgnore = true)]
    public List<ScriptExecutionStrategy> ScriptExecutionStrategy { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间")]
    public DateTime CreatedTime { get; set; }
}