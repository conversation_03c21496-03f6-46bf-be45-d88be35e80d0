namespace IotGateway.Application.Entity;

/// <summary>
///     端口转发表
/// </summary>
[SugarTable("portForwarding", "端口转发表")]
public class PortForwarding : EntityBaseId
{
    /// <summary>
    ///     动作: 1SNAT；2DNAT；3MASQUERADE
    /// </summary>
    [SugarColumn(ColumnDescription = "动作: 1SNAT；2DNAT；3MASQUERADE")]
    [Description("动作:1SNAT；2DNAT；3MASQUERADE")]
    public PortForwardingActionEnum Action { get; set; }

    /// <summary>
    ///     协议:1Any;2TCP;3UDP;4ICMP
    /// </summary>
    [SugarColumn(ColumnDescription = "协议:1Any;2TCP;3UDP;4ICMP")]
    [Description("协议:1Any;2TCP;3UDP;4ICMP")]
    public PortForwardingProtocolEnum Protocol { get; set; }

    /// <summary>
    ///     内部端口范围:1包含；2不包含;
    /// </summary>
    [SugarColumn(ColumnDescription = "内部端口范围:1包含；2不包含;", IsNullable = true)]
    [Description("内部端口范围:1包含；2不包含;")]
    public PortForwardingExpressionEnum InsideExpression { get; set; }

    /// <summary>
    ///     内部Ip
    /// </summary>
    [SugarColumn(ColumnDescription = "内部Ip", IsNullable = true)]
    [Description("内部Ip")]
    public string InsideIp { get; set; }

    /// <summary>
    ///     内部起始端口
    /// </summary>
    [SugarColumn(ColumnDescription = "内部起始端口", IsNullable = true)]
    [Description("内部起始端口")]
    public short InsidePortStart { get; set; }

    /// <summary>
    ///     内部截止端口
    /// </summary>
    [SugarColumn(ColumnDescription = "内部截止端口", IsNullable = true)]
    [Description("内部截止端口")]
    public short InsidePortEnd { get; set; }

    /// <summary>
    ///     外部端口范围:1包含；2不包含;
    /// </summary>
    [SugarColumn(ColumnDescription = "外部端口范围:1包含；2不包含;", IsNullable = true)]
    [Description("外部端口范围:1包含；2不包含;")]
    public PortForwardingExpressionEnum OutsideExpression { get; set; }

    /// <summary>
    ///     外部Ip
    /// </summary>
    [SugarColumn(ColumnDescription = "外部Ip", IsNullable = true)]
    [Description("外部Ip")]
    public string OutsideIp { get; set; }

    /// <summary>
    ///     外部起始端口
    /// </summary>
    [SugarColumn(ColumnDescription = "外部起始端口", IsNullable = true)]
    [Description("外部起始端口")]
    public short OutsidePortStart { get; set; }

    /// <summary>
    ///     外部截止端口
    /// </summary>
    [SugarColumn(ColumnDescription = "外部截止端口", IsNullable = true)]
    [Description("外部截止端口")]
    public short OutsidePortEnd { get; set; }

    /// <summary>
    ///     出接口
    /// </summary>
    [SugarColumn(ColumnDescription = "出接口", IsNullable = true)]
    [Description("出接口")]
    public string OutInterface { get; set; }

    /// <summary>
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", ColumnDataType = "longtext,text,clob", IsNullable = true)]
    public string Description { get; set; }
}

/// <summary>
///     内部端口范围:1包含；2不包含;
/// </summary>
public enum PortForwardingExpressionEnum
{
    /// <summary>
    ///     包含
    /// </summary>
    [Description("包含")] Any = 1,

    /// <summary>
    ///     不包含
    /// </summary>
    [Description("不包含")] NoAny = 2
}

/// <summary>
///     动作:1SNAT;2DNAT;3MASQUERADE
/// </summary>
public enum PortForwardingActionEnum
{
    /// <summary>
    ///     SNAT
    /// </summary>
    [Description("SNAT")] Snat = 1,

    /// <summary>
    ///     DNAT
    /// </summary>
    [Description("DNAT")] Dnat = 2,

    /// <summary>
    ///     MASQUERADE
    /// </summary>
    [Description("MASQUERADE")] Masquerade = 3
}

/// <summary>
///     协议:1Any;2TCP;3UDP;4ICMP
/// </summary>
public enum PortForwardingProtocolEnum
{
    /// <summary>
    ///     Any
    /// </summary>
    [Description("Any")] Any = 1,

    /// <summary>
    ///     TCP
    /// </summary>
    [Description("TCP")] Tcp = 2,

    /// <summary>
    ///     UDP
    /// </summary>
    [Description("UDP")] Udp = 3,

    /// <summary>
    ///     ICMP
    /// </summary>
    [Description("ICMP")] Icmp = 4
}