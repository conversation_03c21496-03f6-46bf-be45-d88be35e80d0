namespace IotGateway.Application.Entity;

/// <summary>
///     趋势分析表
/// </summary>
[SugarTable("trend_analysis", "趋势分析表")]
public class TrendAnalysis : EntityBase
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     设备属性配置
    /// </summary>
    [SugarColumn(ColumnDescription = "设备属性配置", ColumnDataType = "longtext,text,clob")]
    public string Metadata { get; set; }
}