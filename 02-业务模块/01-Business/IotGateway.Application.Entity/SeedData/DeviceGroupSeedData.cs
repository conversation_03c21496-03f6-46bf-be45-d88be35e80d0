using Feng.IotGateway.Core.SqlSugar;

namespace IotGateway.Application.Entity.SeedData;

/// <summary>
/// </summary>
public class DeviceGroupSeedData : ISqlSugarEntitySeedData<DeviceGroup>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<DeviceGroup> HasData()
    {
        yield return new DeviceGroup {Id = 390350585901253, ParentId = 0, Children = new List<DeviceGroup>(), Name = "全部设备", Parent = null};
        yield return new DeviceGroup {Id = 390350585901254, ParentId = 390350585901253, Children = new List<DeviceGroup>(), Name = "未分组", Parent = null};
    }
}