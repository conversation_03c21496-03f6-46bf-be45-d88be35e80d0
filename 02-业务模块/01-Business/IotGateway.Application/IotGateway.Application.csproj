<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <NoWarn>1701;1702;1591</NoWarn>
        <DocumentationFile>IotGateway.Application.xml</DocumentationFile>
        <ImplicitUsings>enable</ImplicitUsings>
        <RootNamespace>IotGateway.Application</RootNamespace>
    </PropertyGroup>


    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
        <DocumentationFile>bin\Release\IotGateway.Application.xml</DocumentationFile>
    </PropertyGroup>


    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
        <DocumentationFile>bin\Debug\IotGateway.Application.xml</DocumentationFile>
    </PropertyGroup>

    <ItemGroup>
        <None Remove="IotGateway.Application.xml"/>
        <None Remove="IotGateway.Application.csproj.DotSettings"/>
        <None Update="TransPonds\中移对接.md">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\03-数据模块\IotGateway.TDengIne\IotGateway.TDengIne.csproj"/>
        <ProjectReference Include="..\..\..\05-协议模块\DriversInterface\DriversInterface.csproj"/>
        <ProjectReference Include="..\..\03-Plugins\IotGateway.ModbusServer\IotGateway.ModbusServer.csproj" />
        <ProjectReference Include="..\..\03-Plugins\IotGateway.OpcUaServer\IotGateway.OpcUaServer.csproj" />
        <ProjectReference Include="..\IotGateway.Application.Entity\IotGateway.Application.Entity.csproj" />
        <ProjectReference Include="..\IotGateway.EdgeDevice\IotGateway.EdgeDevice.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="SharpCompress" Version="0.37.2"/>
    </ItemGroup>


</Project>
