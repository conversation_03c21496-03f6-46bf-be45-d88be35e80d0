global using Furion;
global using Furion.DataEncryption;
global using Furion.DependencyInjection;
global using Furion.DynamicApiController;
global using Furion.FriendlyException;
global using Mapster;
global using Microsoft.AspNetCore.Http;
global using Microsoft.AspNetCore.Mvc;
global using SqlSugar;
global using System.ComponentModel.DataAnnotations;
global using Feng.IotGateway.Core.Base;
global using System.Text.Json.Serialization;
global using Feng.IotGateway.Core.Enums;
global using Feng.IotGateway.Core.Util;
global using Feng.IotGateway.Core.Entity;
global using System.Reflection;
global using Feng.IotGateway.Core.SqlSugar;
global using Yitter.IdGenerator;
global using Furion.TimeCrontab;
global using Microsoft.Extensions.Hosting;
global using Microsoft.Extensions.Logging;
global using System.Net;
global using Feng.IotGateway.WebSocket;
global using Microsoft.Extensions.DependencyInjection;
global using Furion.JsonSerialization;
global using System.Text;
global using Furion.RemoteRequest.Extensions;
global using System.Diagnostics;
global using Furion.LinqBuilder;
global using Feng.IotGateway.Core.Const;
global using Feng.IotGateway.WebSocket.Const;
global using Feng.Common.Util;
global using Furion.EventBus;
global using MiniExcelLibs;
global using Feng.IotGateway.Core.Option;
global using Furion.Logging;
global using Driver.Core.Models;
global using System.Globalization;
global using Driver.Core.Write.Dto;
global using System.Collections.Concurrent;
global using Jint;
global using Feng.IotGateway.Application.TrendAnalysisServer.Dtos;
global using System.Data;
global using System.Dynamic;
global using Feng.IotGateway.Core.Extension;
global using Feng.IotGateway.Application.MessageServer.Dtos;
global using Feng.IotGateway.Core.Models;
global using Furion.Extensions;
global using Furion.RemoteRequest;
global using Furion.TaskQueue;
global using Furion.UnifyResult;
global using Microsoft.AspNetCore.Authorization;
global using SharpCompress.Common;
global using SharpCompress.Readers;
global using System.Collections;
global using System.ComponentModel;
global using System.Runtime.CompilerServices;
global using Feng.IotGateway.Application.OpenApiServer.Dto;
global using Feng.IotGateway.Core.Attribute;
global using MQTTnet;
global using MQTTnet.Client;
global using MQTTnet.Protocol;
global using TDengIne;
global using Common.Enums;
global using IotGateway.Application.OpenApiServer.Dto;
global using IotGateway.EdgeDevice;
global using IotGateway.EdgeDevice.DeviceVariables;
global using IotGateway.Engine.Exec;
global using IotGateway.TDengIne.Dto;