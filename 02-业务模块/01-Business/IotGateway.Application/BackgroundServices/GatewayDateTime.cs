using DateTime = Common.Extension.DateTime;

namespace IotGateway.Application.BackgroundServices;

/// <summary>
///     推送系统时间
/// </summary>
public class GatewayDateTime : BackgroundService
{
    private readonly ILogger<GatewayDateTime> _logger;
    private readonly Crontab _crontab;
    private readonly SendMessageService _socket;

    public GatewayDateTime(ILogger<GatewayDateTime> logger, SendMessageService socket)
    {
        _logger = logger;
        _socket = socket;
        _crontab = Crontab.Parse("0/1 * * ? * *", CronStringFormat.WithSeconds);
    }

    /// <summary>
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var taskFactory = new TaskFactory(TaskScheduler.Current);
            await taskFactory.StartNew(async () =>
            {
                try
                {
                    var dateTime = DateTime.Now();
                    var sysRunTime = DateTime.FormatTime((long)(dateTime - MachineUtil.SystemRunTime).TotalMilliseconds);
                    var runTime = DateTime.FormatTime((long)(dateTime - MachineUtil.ServiceOnlineTime).TotalMilliseconds);
                    dynamic dyc = new
                    {
                        DateTime = dateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        SystemRunTime = sysRunTime,
                        RunTime = runTime
                    };

                    await _socket.Send(JSON.Serialize(dyc), ConstMethod.DateTime);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"推送系统时间 Error:【{ex.Message}】");
                }
            }, stoppingToken);
            await Task.Delay((int)_crontab.GetSleepMilliseconds(DateTime.Now()), stoppingToken);
        }
    }
}