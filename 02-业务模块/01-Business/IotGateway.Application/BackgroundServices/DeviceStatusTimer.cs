using DateTime = Common.Extension.DateTime;

namespace IotGateway.Application.BackgroundServices;

/// <summary>
///     定时设备状态,设备连接状态
/// </summary>
public class DeviceStatusTimer : BackgroundService
{
    private readonly ILogger<DeviceStatusTimer> _logger;
    private readonly Crontab _crontab;
    private readonly IServiceScope _serviceScope;

    public DeviceStatusTimer(ILogger<DeviceStatusTimer> logger, IServiceScopeFactory scopeFactory)
    {
        _logger = logger;
        _serviceScope = scopeFactory.CreateScope();
        _crontab = Crontab.Parse("0/1 * * ? * *", CronStringFormat.WithSeconds);
    }

    /// <summary>
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var taskFactory = new TaskFactory(TaskScheduler.Current);
            await taskFactory.StartNew(async () =>
            {
                try
                {
                    // 推送设备采集信息
                    await _serviceScope.ServiceProvider.GetService<DeviceHostedService>()!.DeviceStatus();
                }
                catch (Exception ex)
                {
                    _logger.LogError($"定时推送设备状态 Error:【{ex.Message}】");
                }

                try
                {
                    // 推送设备连接信息
                    await _serviceScope.ServiceProvider.GetService<DeviceHostedService>()!.DeviceConnectStatus();
                }
                catch (Exception ex)
                {
                    _logger.LogError($"【设备连接状态】 Error:【{ex.Message}】");
                }
            }, stoppingToken);
            await Task.Delay((int) _crontab.GetSleepMilliseconds(DateTime.Now()), stoppingToken);
        }
    }
}