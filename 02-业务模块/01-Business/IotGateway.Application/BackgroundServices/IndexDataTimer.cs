using DateTime = Common.Extension.DateTime;

namespace IotGateway.Application.BackgroundServices;

/// <summary>
///     推送首页部分数据
/// </summary>
public class IndexDataTimer : BackgroundService
{
    private readonly ILogger<IndexDataTimer> _logger;
    private readonly Crontab _crontab;
    private readonly SendMessageService _socket;

    public IndexDataTimer(ILogger<IndexDataTimer> logger, SendMessageService socket)
    {
        _logger = logger;
        _socket = socket;
        _crontab = Crontab.Parse("0/5 * * ? * *", CronStringFormat.WithSeconds);
    }

    /// <summary>
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await Task.Delay((int)_crontab.GetSleepMilliseconds(DateTime.Now()), stoppingToken);
            var taskFactory = new TaskFactory(TaskScheduler.Current);
            await taskFactory.StartNew(async () =>
            {
                try
                {
                    dynamic data = new
                    {
                        Send = MachineUtil.SendCount,
                        Read = MachineUtil.ReadCount,
                        Cache = MachineUtil.OffLineCount < 0 ? 0 : MachineUtil.OffLineCount,
                        SaveCache = GlobalConfigManager.GetConfigValue<int>(ConfigConst.MaxOffLine)
                    };
                    await _socket.Send(JSON.Serialize(data), ConstMethod.IndexData);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"推送部分首页数据 Error:【{ex.Message}】");
                }
            }, stoppingToken);
        }
    }
}