using DateTime = Common.Extension.DateTime;

namespace IotGateway.Application.BackgroundServices;

/// <summary>
///     定时清理设备保存的数据
/// </summary>
public class DropReportDataTimer : BackgroundService
{
    private readonly ILogger<DropReportDataTimer> _logger;
    private readonly Crontab _crontab;
    private readonly IServiceScopeFactory _scopeFactory;

    public DropReportDataTimer(ILogger<DropReportDataTimer> logger, IServiceScopeFactory scopeFactory)
    {
        _logger = logger;
        _scopeFactory = scopeFactory;
        _crontab = Crontab.Parse("00 0/30 * ? * *", CronStringFormat.WithSeconds);
    }

    private async Task ExecuteWithRetry(Func<Task> action, int maxRetries = 3)
    {
        for (int i = 0; i < maxRetries; i++)
        {
            try
            {
                await action();
                return;
            }
            catch (Exception ex) when (i < maxRetries - 1)
            {
                _logger.LogWarning($"操作失败,准备重试 ({i + 1}/{maxRetries}): {ex.Message}");
                await Task.Delay(1000 * (i + 1)); // 递增延迟
            }
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            // 清理网关发送成功数据表
            await ExecuteWithRetry(async () =>
            {
                // 使用独立的数据库连接
                using var scope = _scopeFactory.CreateScope();
                // 获取网关数据表
                using var db = scope.ServiceProvider.GetRequiredService<ISqlSugarClient>().CopyNew();
                // 获取网关数据表
                var tableList = db.DbMaintenance.GetTableInfoList(false);
                // 获取网关数据表
                tableList = tableList.Where(w => w.Name.Contains("reportData_")).ToList();
                // 获取网关数据表
                var igorTableList = new List<string>();
                for (var i = 0; i < 48; i++) igorTableList.Add("reportData_" + DateTime.Now().AddHours(-i).ToString("MM-dd-HH"));
                // 获取网关数据表
                foreach (var sql in from table in tableList where !igorTableList.Contains(table.Name) select $"DROP TABLE IF EXISTS '{table.Name}'")
                    try
                    {
                        // 获取网关数据表
                        await db.CopyNew().Ado.ExecuteCommandAsync(sql);
                    }
                    catch (Exception e)
                    {
                        // 获取网关数据表
                        Log.Error($"【删除网关发送成功数据表失败】,SQL:【{sql}】,Error:【{e.Message}】");
                    }
            });

            await Task.Delay((int)_crontab.GetSleepMilliseconds(DateTime.Now()), stoppingToken);
        }
    }
}