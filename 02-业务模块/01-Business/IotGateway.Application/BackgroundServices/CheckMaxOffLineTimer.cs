using DateTime = Common.Extension.DateTime;

namespace IotGateway.Application.BackgroundServices;

/// <summary>
///     检查缓存数据是否超过预警配置
/// </summary>
public class CheckMaxOffLineTimer : BackgroundService
{
    private readonly ILogger<CheckMaxOffLineTimer> _logger;
    private readonly Crontab _crontab;
    private readonly IServiceScopeFactory _scopeFactory;

    public CheckMaxOffLineTimer(ILogger<CheckMaxOffLineTimer> logger, IServiceScopeFactory scopeFactory)
    {
        _logger = logger;
        _scopeFactory = scopeFactory;
        _crontab = Crontab.Parse("0/30 * * ? * *", CronStringFormat.WithSeconds);
    }

    /// <summary>
    ///     检查缓存数据是否超过预警配置
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var taskFactory = new TaskFactory(TaskScheduler.Current);
            await taskFactory.StartNew(async () =>
            {
                try
                {
                    using var scope = _scopeFactory.CreateScope();
                    // 最大离线存储数据
                    var maxOffLine = GlobalConfigManager.GetConfigValue<int>(ConfigConst.MaxOffLine);
                    if (MachineUtil.OffLineCount > maxOffLine + 1000)
                    {
                        var offLineDbRep = scope.ServiceProvider.GetRequiredService<ISqlSugarClient>();
                        var delNum = await offLineDbRep.CopyNew().AsTenant().GetConnection(SqlSugarConst.EdgeData).Ado
                            .ExecuteCommandAsync("DELETE FROM `offLine` WHERE Id IN (SELECT Id FROM `offLine` ORDER BY Id LIMIT 1000);");
                        MachineUtil.OffLineCount -= delNum;
                        // 超过90%删除数据
                        _logger.LogWarning($"当前时间:【{DateTime.ShangHai()}】,离线存储数据超出最大存储数量:【{maxOffLine}】条,删除前:【{delNum}】条数据");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"检查缓存数据是否超过预警配置 Error:【{ex.Message}】");
                }
            }, stoppingToken);
            await Task.Delay((int)_crontab.GetSleepMilliseconds(DateTime.Now()), stoppingToken);
        }
    }
}