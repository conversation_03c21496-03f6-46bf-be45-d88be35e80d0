using IotGateway.Application.MessageServer;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Application.BackgroundServices;

/// <summary>
///     定时清理站内信消息
/// </summary>
public class DropMessageTimer : BackgroundService
{
    private readonly ILogger<DropMessageTimer> _logger;
    private readonly Crontab _crontab;
    private readonly IServiceScopeFactory _scopeFactory;

    public DropMessageTimer(ILogger<DropMessageTimer> logger, IServiceScopeFactory scopeFactory)
    {
        _logger = logger;
        _scopeFactory = scopeFactory;
        _crontab = Crontab.Parse("00 00 0/12 ? * *", CronStringFormat.WithSeconds);
    }

    /// <summary>
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var taskFactory = new TaskFactory(TaskScheduler.Current);
            await taskFactory.StartNew(async () =>
            {
                using var scope = _scopeFactory.CreateScope();
                var services = scope.ServiceProvider;
                try
                {
                    await services.GetService<MessageService>()?.Delete()!;
                }
                catch (Exception ex)
                {
                    _logger.LogError($"删除站内信 Error:【{ex.Message}】");
                }
            }, stoppingToken);
            await Task.Delay((int) _crontab.GetSleepMilliseconds(DateTime.Now()), stoppingToken);
        }
    }
}