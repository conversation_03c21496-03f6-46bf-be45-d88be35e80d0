using IotGateway.Application.Entity;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;

namespace IotGateway.Application.Firewall;

/// <summary>
///     防火墙服务 - 系统启动时应用防火墙规则
/// </summary>
public class FirewallHostedService : IHostedService
{
  private readonly IServiceScopeFactory _serviceScopeFactory;
  private readonly ILogger<FirewallHostedService> _logger;

  /// <summary>
  ///     构造函数
  /// </summary>
  /// <param name="serviceScopeFactory">服务作用域工厂</param>
  /// <param name="logger">日志服务</param>
  public FirewallHostedService(
      IServiceScopeFactory serviceScopeFactory,
      ILogger<FirewallHostedService> logger)
  {
    _serviceScopeFactory = serviceScopeFactory;
    _logger = logger;
  }

  /// <summary>
  ///     启动服务
  /// </summary>
  /// <param name="cancellationToken">取消令牌</param>
  /// <returns>Task</returns>
  public async Task StartAsync(CancellationToken cancellationToken)
  {
    _logger.LogInformation("【防火墙服务】开始初始化防火墙规则...");

    try
    {
      // 使用服务作用域来获取作用域服务
      using var scope = _serviceScopeFactory.CreateScope();
      var firewallService = scope.ServiceProvider.GetRequiredService<FirewallService>();

      await firewallService.ApplyAllRules();
      _logger.LogInformation("【防火墙服务】防火墙规则初始化完成");
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "【防火墙服务】初始化防火墙规则时发生错误");
    }
  }

  /// <summary>
  ///     停止服务
  /// </summary>
  /// <param name="cancellationToken">取消令牌</param>
  /// <returns>Task</returns>
  public Task StopAsync(CancellationToken cancellationToken)
  {
    _logger.LogInformation("【防火墙服务】停止服务");
    return Task.CompletedTask;
  }
}