using IotGateway.Application.Entity;
using IotGateway.Application.Firewall.Dtos;

namespace IotGateway.Application.Firewall;

/// <summary>
///     防火墙管理
/// </summary>
[ApiDescriptionSettings("网络配置")]
public class FirewallService : ITransient, IDynamicApiController
{
    /// <summary>
    ///     防火墙规则
    /// </summary>
    private readonly SqlSugarRepository<FirewallRule> _firewallRule;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="firewallRule"></param>
    public FirewallService(SqlSugarRepository<FirewallRule> firewallRule)
    {
        _firewallRule = firewallRule;
    }

    /// <summary>
    ///     防火墙规则-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/firewall/page")]
    public async Task<SqlSugarPagedList<FirewallRule>> Page([FromQuery] FirewallRuleBaseInput input)
    {
        var rules = await _firewallRule.AsQueryable()
            .Where(w => w.RuleType == input.RuleType)
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue), w => w.IpAddress.Contains(input.SearchValue)
                                                                    || w.Description.Contains(input.SearchValue)
                                                                    || w.PortRange.Contains(input.SearchValue))
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return rules;
    }

    /// <summary>
    ///     防火墙规则-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/firewall/detail")]
    public async Task<FirewallRule> Detail([FromQuery] BaseId input)
    {
        var rule = await _firewallRule.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (rule == null)
            throw Oops.Oh(ErrorCode.D1002);
        return rule;
    }

    /// <summary>
    ///     防火墙规则-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/firewall/add")]
    public async Task Add(FirewallRuleAdd input)
    {
        var rule = input.Adapt<FirewallRule>();

        // 生成iptables命令
        var command = BuildIptablesCommand(rule, "add");
        rule.Command = command;

        // 执行命令
        await ShellUtil.Bash(command);
        Log.Information("【添加防火墙规则】 {command}", command);

        // 保存到数据库
        await _firewallRule.InsertAsync(rule);
    }

    /// <summary>
    ///     防火墙规则-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/firewall/update")]
    public async Task Update(FirewallRuleUpdate input)
    {
        var rule = await _firewallRule.GetFirstAsync(f => f.Id == input.Id);
        if (rule == null)
            throw Oops.Oh(ErrorCode.D1002);

        // 删除旧规则
        var delCommand = rule.Command.Replace("-A", "-D");
        await ShellUtil.Bash(delCommand);
        Log.Information("【删除防火墙规则】 {delCommand}", delCommand);

        // 更新规则属性
        rule.IpAddress = input.IpAddress;
        rule.PortRange = input.PortRange;
        rule.Protocol = input.Protocol;
        rule.RuleType = input.RuleType;
        rule.Description = input.Description;
        rule.Enable = input.Enable;

        // 生成新命令
        var command = BuildIptablesCommand(rule, "add");
        rule.Command = command;

        // 如果规则启用，则执行命令
        if (rule.Enable)
        {
            await ShellUtil.Bash(command);
            Log.Information("【添加防火墙规则】 {command}", command);
        }

        // 更新数据库
        await _firewallRule.UpdateAsync(rule);
    }

    /// <summary>
    ///     防火墙规则-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/firewall/delete")]
    public async Task Delete(BaseId input)
    {
        var rule = await _firewallRule.GetFirstAsync(f => f.Id == input.Id);
        if (rule == null)
            throw Oops.Oh(ErrorCode.D1002);

        // 删除规则
        var delCommand = rule.Command.Replace("-A", "-D");
        await ShellUtil.Bash(delCommand);
        Log.Information("【删除防火墙规则】 {delCommand}", delCommand);

        // 从数据库删除
        await _firewallRule.DeleteAsync(rule);
    }

    /// <summary>
    ///     应用所有防火墙规则
    /// </summary>
    /// <returns></returns>
    public async Task ApplyAllRules()
    {
        try
        {
            // 先清空所有规则
            await ResetIptablesRules();
            Log.Information("【防火墙服务】已清空所有防火墙规则");

            // 从数据库获取所有启用的规则
            var rules = await _firewallRule.GetListAsync(r => r.Enable);
            Log.Information("【防火墙服务】获取到 {count} 条启用的防火墙规则", rules.Count);

            // 应用所有启用的规则
            foreach (var rule in rules)
            {
                try
                {
                    await ShellUtil.Bash(rule.Command);
                    Log.Information("【防火墙服务】应用规则: {command}", rule.Command);
                }
                catch (Exception ex)
                {
                    Log.Error("【防火墙服务】应用规则失败: {command}", ex, rule.Command);
                }
            }

            Log.Information("【防火墙服务】所有防火墙规则已应用");
        }
        catch (Exception ex)
        {
            Log.Error("【防火墙服务】应用防火墙规则时发生错误", ex);
            throw;
        }
    }

    #region 私有方法

    /// <summary>
    ///     构建iptables命令
    /// </summary>
    /// <param name="rule">防火墙规则</param>
    /// <param name="action">操作类型：add或delete</param>
    /// <returns></returns>
    private string BuildIptablesCommand(FirewallRule rule, string action)
    {
        var actionFlag = action.ToLower() == "add" ? "-A" : "-D";
        var targetAction = rule.RuleType == 1 ? "ACCEPT" : "DROP";
        var protocol = rule.Protocol.ToLower();

        var command = $"iptables {actionFlag} INPUT -s {rule.IpAddress}";

        // 添加协议
        if (protocol != "all")
        {
            command += $" -p {protocol}";

            // 添加端口（仅TCP和UDP有端口）
            if ((protocol == "tcp" || protocol == "udp") && !string.IsNullOrEmpty(rule.PortRange)) command += $" --dport {rule.PortRange}";
        }

        // 添加动作
        command += $" -j {targetAction}";

        return command;
    }

    /// <summary>
    ///     重置iptables规则
    /// </summary>
    private async Task ResetIptablesRules()
    {
        // 清空所有规则
        await ShellUtil.Bash("iptables -F");
        await ShellUtil.Bash("iptables -X");
        _ = MessageCenter.PublishAsync(EventConst.SysLog, "【清空防火墙规则】");
    }

    #endregion
}