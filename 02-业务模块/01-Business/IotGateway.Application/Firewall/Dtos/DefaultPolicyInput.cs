namespace IotGateway.Application.Firewall.Dtos;

/// <summary>
/// Represents the base input for firewall rule-related operations, extending global pagination query parameters.
/// This class serves as a foundation for defining input parameters specific to firewall rule queries or operations.
/// Inherits properties such as pagination, sorting, and search criteria from the BasePageInput class.
/// </summary>
public class FirewallRuleBaseInput : BasePageInput
{
    /// <summary>
    ///     规则类型：0-拒绝(黑名单)，1-允许(白名单)
    /// </summary>
    public int RuleType { get; set; }
}