using System.ComponentModel.DataAnnotations;

namespace IotGateway.Application.Firewall.Dtos;

/// <summary>
///     防火墙规则-新增 请求参数
/// </summary>
public class FirewallRuleAdd
{
  /// <summary>
  ///     IP地址或网段
  /// </summary>
  [Required(ErrorMessage = "IP地址或网段不能为空")]
  public string IpAddress { get; set; }

  /// <summary>
  ///     端口范围，如：22或22-80
  /// </summary>
  public string PortRange { get; set; }

  /// <summary>
  ///     协议类型：tcp, udp, icmp, all
  /// </summary>
  [Required(ErrorMessage = "协议类型不能为空")]
  public string Protocol { get; set; } = "all";

  /// <summary>
  ///     规则类型：0-拒绝(黑名单)，1-允许(白名单)
  /// </summary>
  [Required(ErrorMessage = "规则类型不能为空")]
  public int RuleType { get; set; }

  /// <summary>
  ///     描述
  /// </summary>
  public string Description { get; set; }

  /// <summary>
  ///     是否启用
  /// </summary>
  public bool Enable { get; set; } = true;
}

/// <summary>
///     防火墙规则-修改 请求参数
/// </summary>
public class FirewallRuleUpdate : FirewallRuleAdd
{
  /// <summary>
  ///     Id
  /// </summary>
  [Required(ErrorMessage = "Id不能为空")]
  public long Id { get; set; }
}

/// <summary>
///     防火墙模式切换 请求参数
/// </summary>
public class FirewallModeInput
{
  /// <summary>
  ///     防火墙模式：0-黑名单模式，1-白名单模式
  /// </summary>
  [Required(ErrorMessage = "防火墙模式不能为空")]
  public int Mode { get; set; }
}