using System.Net.Http.Headers;
using Feng.IotGateway.Core.Service.Config;
using IotGateway.Application.Entity;
using IotGateway.Application.UpdateGatewayServer.Dto;
using Newtonsoft.Json;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Application.UpdateGatewayServer;

/// <summary>
///     网关更新
///     版 本:V3.0.6.4
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-05-05
/// </summary>
[ApiDescriptionSettings("系统配置")]
public class UpdateGatewayService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     文件加解密 密码
    /// </summary>
    private const string SKey = "201001";
    private readonly SqlSugarRepository<UpdateFileRecord> _updateFileRecord;

    public UpdateGatewayService(SqlSugarRepository<UpdateFileRecord> updateFileRecord)
    {
        _updateFileRecord = updateFileRecord;
    }

    #region 远程下载

    /// <summary>
    ///     更新记录
    /// </summary>
    /// <returns></returns>
    [HttpGet("/update/updateFileRecord/list")]
    public async Task<dynamic> GetUpdateFileRecordList()
    {
        return await _updateFileRecord.AsQueryable().ToListAsync();
    }

    /// <summary>
    /// 更新包下载
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("/update/updateFileRecord/down")]
    public async Task OtaTask(BaseId input)
    {
        var updateFileRecord = await _updateFileRecord.AsQueryable().FirstAsync(x => x.Id == input.Id);
        var ip = await App.GetService<SysConfigService>().GetConfigValue<string>(ConfigConst.SysFLinkIp);
        Log.Information($"[OTA更新] 开始从服务器 {ip} 下载更新包");

        var tempFilePath = Path.Combine(Path.GetTempPath(), "");

        try
        {
            using var client = new HttpClient { Timeout = TimeSpan.FromSeconds(60 * 10) };
            var url = $"http://{ip}:9081/firmwareManage/upload/download?RecordId={updateFileRecord.RecordId}";

            Log.Information($"[OTA更新] 请求URL: {url}");

            // 使用流式下载以减少内存占用
            using var response = await client.GetAsync(url, HttpCompletionOption.ResponseHeadersRead);

            // 输出详细的错误信息
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Log.Error($"[OTA更新] HTTP状态码: {(int)response.StatusCode} ({response.StatusCode})");
                Log.Error($"[OTA更新] 错误响应内容: {errorContent}");
                Log.Error($"[OTA更新] RecordId: {updateFileRecord.RecordId}");

                // 输出请求头信息
                Log.Error("[OTA更新] 请求头信息:");
                foreach (var header in response.RequestMessage?.Headers)
                {
                    Log.Error($"[OTA更新] {header.Key}: {string.Join(", ", header.Value)}");
                }

                response.EnsureSuccessStatusCode(); // 这会抛出异常
            }

            // 输出响应头信息
            Log.Information("[OTA更新] 响应头信息:");
            foreach (var header in response.Headers)
            {
                Log.Information($"[OTA更新] {header.Key}: {string.Join(", ", header.Value)}");
            }
            foreach (var header in response.Content.Headers)
            {
                Log.Information($"[OTA更新] {header.Key}: {string.Join(", ", header.Value)}");
            }
            // 获取文件大小
            var totalBytes = response.Content.Headers.ContentLength ?? -1L;
            // 打印文件大小
            var totalMB = Math.Round(totalBytes / (1024.0 * 1024.0), 2);
            Log.Information($"[OTA更新] 文件大小: {totalMB}MB");
            // 打印文件名称
            var fileName = response.Content.Headers.ContentDisposition?.FileName;
            Log.Information($"[OTA更新] 文件名称: {fileName}");
            // 使用返回的真实文件名称
            tempFilePath = Path.Combine(Path.GetTempPath(), fileName);
            using var stream = await response.Content.ReadAsStreamAsync();
            using var fileStream = new FileStream(tempFilePath, FileMode.Create, FileAccess.Write, FileShare.None, 8192);

            var buffer = new byte[81920];
            var totalBytesRead = 0L;
            var lastProgressReport = 0;

            while (true)
            {
                var bytesRead = await stream.ReadAsync(buffer);
                if (bytesRead == 0) break;

                await fileStream.WriteAsync(buffer.AsMemory(0, bytesRead));
                totalBytesRead += bytesRead;

                // 计算并显示进度
                if (totalBytes != -1)
                {
                    var progressPercentage = (int)((totalBytesRead * 100) / totalBytes);
                    if (progressPercentage > lastProgressReport + 4) // 每增加5%显示一次
                    {
                        var downloadedMB = Math.Round(totalBytesRead / (1024.0 * 1024.0), 2);
                        Log.Information($"[OTA更新] 下载进度: {progressPercentage}% ({downloadedMB}MB/{totalMB}MB)");
                        lastProgressReport = progressPercentage;
                    }
                }
            }

            Log.Information($"[OTA更新] 创建临时文件: {tempFilePath}");
            await ProcessDownloadedFile(updateFileRecord, fileName);
            Log.Information("[OTA更新] 全部流程完成");
        }
        catch (HttpRequestException ex)
        {
            Log.Error($"[OTA更新] HTTP请求错误: {ex.Message}");
            Log.Error($"[OTA更新] 内部异常: {ex.InnerException?.Message}");
            if (File.Exists(tempFilePath))
            {
                File.Delete(tempFilePath);
            }
            throw;
        }
        catch (Exception ex)
        {
            Log.Error($"[OTA更新] 下载文件时出错: {ex.Message}");
            Log.Error($"[OTA更新] 异常堆栈: {ex.StackTrace}");
            if (File.Exists(tempFilePath))
            {
                File.Delete(tempFilePath);
            }
            throw;
        }
    }


    /// <summary>
    /// 处理下载的文件
    /// </summary>
    /// <param name="updateFileRecord"></param>
    /// <param name="fileName"></param>
    private async Task ProcessDownloadedFile(UpdateFileRecord updateFileRecord, string fileName = null)
    {
        try
        {
            // 更新记录
            updateFileRecord.IsDown = true;
            updateFileRecord.FileName = fileName;
            await _updateFileRecord.UpdateAsync(updateFileRecord);
            Log.Information("[OTA更新] 更新记录已保存");
        }
        catch (Exception ex)
        {
            Log.Error($"[OTA更新] 处理文件时出错: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 更新包-安装
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/update/updateFileRecord/install")]
    [HttpPost("/api/update/updateFileRecord/install")]
    public async Task PackageInstall(BaseId input)
    {
        // 获取更新记录
        var updateFileRecord = await _updateFileRecord.AsQueryable().FirstAsync(x => x.Id == input.Id);
        if (!updateFileRecord.IsDown)
            throw Oops.Oh("更新包还未下载！");
        // 已经完成升级
        if (updateFileRecord.IsUpdate)
            throw Oops.Oh("已经完成升级！");

        var fileName = Path.Combine(Path.GetTempPath(), updateFileRecord.FileName); ;
        var backupTimestamp = System.DateTime.Now.ToString("yyyyMMddHHmmss");
        var backupPath = $"/usr/local/src/backup_{backupTimestamp}";
        // 读取文件
        var fileByte = await File.ReadAllBytesAsync(fileName);
        // 打印文件大小
        Log.Information($"[安装]--文件大小: {fileByte.Length} bytes");
        // 解密
        var decryptFileByte = AESEncryption.Decrypt(fileByte, SKey);
        // 打印解密后文件大小
        Log.Information($"[安装]--解密后文件大小: {decryptFileByte.Length} bytes");
        // 解压文件
        DeCompressionFile(decryptFileByte, fileName);

        try
        {
            // 创建备份目录
            await ShellUtil.Bash($"mkdir -p {backupPath}");

            // 备份现有文件
            await ShellUtil.Bash($"cp -r /usr/local/src/iotgatewayhtml {backupPath}/");
            await ShellUtil.Bash($"cp -r /usr/local/src/iotgateway {backupPath}/");
            Log.Warning("[远程更新]--创建备份完成");

            // 更新前端文件
            await ShellUtil.Bash($"cp -r /usr/local/src/{fileName}/iotgatewayhtml/ /usr/local/src/");
            Log.Warning("[远程更新]--前端文件更新完成");

            // 更新后端文件
            await ShellUtil.Bash("rm -rf /usr/local/src/iotgateway/*");
            await ShellUtil.Bash($"cp -r /usr/local/src/{fileName}/iotgateway/ /usr/local/src/");
            Log.Warning("[远程更新]--后端文件更新完成");

            // 删除更新包
            await ShellUtil.Bash($"rm -rf /usr/local/src/{fileName}");
            Log.Warning("[远程更新]--删除更新包");

            updateFileRecord.IsUpdate = true;
            await _updateFileRecord.UpdateAsync(updateFileRecord);

            // 更新成功后删除备份
            await ShellUtil.Bash($"rm -rf {backupPath}");
            Log.Warning("[远程更新]--删除备份文件");

            // 延迟5秒重启
            await TaskQueued.EnqueueAsync(async (_, _) =>
            {
                await ShellUtil.Bash("reboot");
                Log.Warning("[远程更新]--网关重启");
            }, 5000);
        }
        catch (Exception ex)
        {
            Log.Error($"[远程更新]--更新失败: {ex.Message}");

            try
            {
                // 回滚到备份
                await ShellUtil.Bash($"rm -rf /usr/local/src/iotgatewayhtml/*");
                await ShellUtil.Bash($"rm -rf /usr/local/src/iotgateway/*");
                await ShellUtil.Bash($"cp -r {backupPath}/iotgatewayhtml/* /usr/local/src/iotgatewayhtml/");
                await ShellUtil.Bash($"cp -r {backupPath}/iotgateway/* /usr/local/src/iotgateway/");
                Log.Warning("[远程更新]--回滚完成");

                // 清理备份
                await ShellUtil.Bash($"rm -rf {backupPath}");
            }
            catch (Exception rollbackEx)
            {
                Log.Error($"[远程更新]--回滚失败: {rollbackEx.Message}");
            }

            throw Oops.Oh($"更新失败: {ex.Message}");
        }
    }

    // /// <summary>
    // ///     获取最新版本
    // /// </summary>
    // /// <returns></returns>
    // [HttpGet("/update/latestVersion")]
    // public async Task<bool> GetLatestVersion()
    // {
    //     await ShellUtil.Bash("rm -rf /root/version.txt");
    //     await ShellUtil.Bash("wget http://fengedge.cn/gateway/version.txt -P /root/");
    //     var latestVersion = await ShellUtil.Bash("cat /root/version.txt");
    //     Log.Information($"服务器最新版本:【{latestVersion}】");
    //     return latestVersion != null && string.Compare(latestVersion, MachineUtil.Version) > 0;
    // }
    //
    // /// <summary>
    // ///     最新版本下载
    // /// </summary>
    // /// <returns></returns>
    // [HttpGet("/update/latestVersion/downLoad")]
    // public async Task<bool> LatestVersionDownLoad()
    // {
    //     await ShellUtil.Bash("rm -rf /root/gateway_v*");
    //     var latestVersion = await ShellUtil.Bash("cat /root/version.txt");
    //     await ShellUtil.Bash($"wget http://fengedge.cn/gateway/gateway_v{latestVersion}.zip -P /root/");
    //     Log.Information($"服务器最新版本:【{latestVersion}】 下载成功:【gateway_v{latestVersion}】");
    //     await ShellUtil.Bash($"unzip /root/gateway_v{latestVersion}.zip -d /usr/local/src/iotgateway/");
    //     Log.Information($"服务器最新版本:【{latestVersion}】 解压完成");
    //
    //     // //延迟3秒后重启硬件
    //     // await TaskQueued.EnqueueAsync(async (_, _) =>
    //     // {
    //     //     await ShellUtil.Bash("reboot");
    //     // },3000);
    //     return true;
    // }

    #endregion

    #region 局域网同步网关版本

    /// <summary>
    ///     局域网内网关
    /// </summary>
    /// <returns></returns>
    [HttpGet("/update/network/gatewayList")]
    public Task<Dictionary<string, object>> LanGatewayDevice()
    {
        var output = new Dictionary<string, object>();
        foreach (var (key, value) in MachineUtil.LanGatewayDevice)
            output.Add(key, new
            {
                value.Ip,
                value.Version,
                LastTime = value.PublishTime,
                OnLine = (DateTime.ShangHai() - value.PublishTime).TotalMinutes <= 5
            });
        return Task.FromResult(output);
    }

    /// <summary>
    ///     局域网同步更新网关
    /// </summary>
    /// <returns></returns>
    [HttpPost("/update/network/sysn")]
    [DisableRequestSizeLimit]
    public async Task<List<SynchronousUpdateOutput>> SynchronousUpdate([FromForm] SynchronousUpdateInput input)
    {
        var output = new List<SynchronousUpdateOutput>();
        // 并行请求
        await Parallel.ForEachAsync(input.Ip, async (ip, token) =>
        {
            try
            {
                var res = await ("http://" + ip + ":8093/update/upload/file").SetContentType("multipart/form-data")
                    .SetFiles(HttpFile.Create("Files", input.File.ToByteArray(), input.File.FileName))
                    .OnClientCreating(client => { client.Timeout = TimeSpan.FromSeconds(120); })
                    .PostAsAsync<RESTfulResult<bool?>>(token);
                var a = new SynchronousUpdateOutput
                {
                    Ip = ip,
                    Success = res.Data ?? false,
                    ErrorMsg = res.Errors == null ? "" : res.Errors.ToString()
                };
                output.Add(a);
            }
            catch (Exception e)
            {
                output.Add(new SynchronousUpdateOutput
                {
                    Ip = ip,
                    Success = false,
                    ErrorMsg = e.Message
                });
            }
        });
        return output;
    }

    #endregion

    #region 手动更新当前网关

    /// <summary>
    ///     模拟文件加密
    /// </summary>
    /// <returns></returns>
    [HttpPost("/update/upload/file/encrypt")]
    [AllowAnonymous]
    [DisableRequestSizeLimit]
    public Task<IActionResult> UploadFileEncrypt([FromForm] GatewayUploadInput input)
    {
        // 存储到相应地址中
        var fileName = input.File.FileName.Replace(".rar", "");
        // 加密
        var memoryStream = new MemoryStream(AESEncryption.Encrypt(input.File.ToByteArray(), SKey));
        return Task.FromResult<IActionResult>(new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = fileName + "-encrypt.rar"
        });
    }

    /// <summary>
    ///     读取本地文件并模拟加密
    /// </summary>
    /// <returns></returns>
    [HttpGet("/encrypt/local/file")]
    [AllowAnonymous]
    public async Task<IActionResult> EncryptLocalFile(string filePath)
    {
        // 检查并获取文件路径
        if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
        {
            throw Oops.Oh("123");
        }

        // 读取本地文件内容
        byte[] fileBytes;
        try
        {
            fileBytes = await File.ReadAllBytesAsync(filePath);
        }
        catch (IOException ex)
        {
            throw Oops.Oh(StatusCodes.Status500InternalServerError, $"Failed to read the file: {ex.Message}");
        }

        // 加密文件内容
        byte[] encryptedBytes;
        try
        {
            encryptedBytes = AESEncryption.Encrypt(fileBytes, SKey);
        }
        catch (Exception ex)
        {
            throw Oops.Oh(StatusCodes.Status500InternalServerError, $"Encryption failed: {ex.Message}");
        }

        // 创建内存流并写入加密数据
        var memoryStream = new MemoryStream(encryptedBytes);

        // 提取文件名
        string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(filePath);
        string encryptedFileName = $"{fileNameWithoutExtension}-encrypt.rar";

        // 返回加密后的文件结果
        return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = encryptedFileName
        };
    }

    /// <summary>
    ///     手动上传程序更新
    /// </summary>
    /// <returns></returns>
    [HttpPost("/update/upload/file")]
    [AllowAnonymous]
    [DisableRequestSizeLimit]
    public async Task<bool> UploadFile([FromForm] GatewayUploadInput input)
    {
        // 存储到相应地址中
        var fileName = input.File.FileName;
        // 解密
        var fileByte = AESEncryption.Decrypt(input.File.ToByteArray(), SKey);
        // 成功后删除压缩包
        DeCompressionFile(fileByte, fileName);
        await TaskQueued.EnqueueAsync(async (_, _) =>
        {
            // 更新前端文件
            await ShellUtil.Bash("rm -rf /usr/local/src/iotgatewayhtml/*");
            await ShellUtil.Bash($"cp -r /usr/local/src/{input.File.FileName}/iotgatewayhtml/ /usr/local/src/");
            Log.Warning("【手动上传程序更新】 前端文件更新完成");
            // 更新后端文件
            await ShellUtil.Bash("rm -rf /usr/local/src/iotgateway/*");
            await ShellUtil.Bash($"cp -r /usr/local/src/{input.File.FileName}/iotgateway/ /usr/local/src/");
            Log.Warning("【手动上传程序更新】 后端文件更新完成");
        }, 5000);

        return true;
    }

    /// <summary>
    ///     解压文件
    /// </summary>
    /// <param name="fileByte"></param>
    /// <param name="fileName">文件名称</param>
    private static void DeCompressionFile(byte[] fileByte, string fileName)
    {
        var dirPath = $"/usr/local/src/{fileName}/";
        Directory.CreateDirectory(dirPath);
        try
        {
            var option = new ReaderOptions
            {
                ArchiveEncoding = new ArchiveEncoding { Default = Encoding.UTF8 },
                LeaveStreamOpen = true
            };

            var reader = ReaderFactory.Open(new MemoryStream(fileByte), option);
            while (reader.MoveToNextEntry())
                if (reader.Entry.IsDirectory)
                {
                    Directory.CreateDirectory(Path.Combine(dirPath, reader.Entry.Key));
                }
                else
                {
                    // 创建父级目录，防止Entry文件,解压时由于目录不存在报异常
                    var file = Path.Combine(dirPath, reader.Entry.Key);
                    Directory.CreateDirectory(Path.GetDirectoryName(file) ?? string.Empty);
                    reader.WriteEntryToFile(file);
                }
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    #endregion
}
