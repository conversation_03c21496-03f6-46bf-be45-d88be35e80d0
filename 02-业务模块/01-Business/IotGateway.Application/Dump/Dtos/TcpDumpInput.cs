namespace IotGateway.Application.Dump.Dtos;

/// <summary>
///     网络抓包请求参数
/// </summary>
public class TcpDumpInput
{
    /// <summary>
    ///     接口
    /// </summary>
    [Required]
    public string Interface { get; set; }

    /// <summary>
    ///     抓包个数
    /// </summary>
    [Required]
    public short Number { get; set; }

    /// <summary>
    ///     源Ip地址
    /// </summary>
    public string SourceIp { get; set; }

    /// <summary>
    ///     目的Ip地址
    /// </summary>
    public string GoalIp { get; set; }

    /// <summary>
    ///     协议:1Any;2TCP;3ICMP;4UDP;5ARP
    /// </summary>
    public TcpDumpProtocolEnum TcpDumpProtocol { get; set; }
}

/// <summary>
///     协议:1Any;2TCP;3ICMP;4UDP;5ARP
/// </summary>
public enum TcpDumpProtocolEnum
{
    /// <summary>
    ///     Any
    /// </summary>
    [Description("Any")] Any = 1,

    /// <summary>
    ///     TCP
    /// </summary>
    [Description("TCP")] Tcp = 2,

    /// <summary>
    ///     ICMP
    /// </summary>
    [Description("ICMP")] Icmp = 3,

    /// <summary>
    ///     UDP
    /// </summary>
    [Description("UDP")] Udp = 4,

    /// <summary>
    ///     ARP
    /// </summary>
    [Description("ARP")] Arp = 5
}