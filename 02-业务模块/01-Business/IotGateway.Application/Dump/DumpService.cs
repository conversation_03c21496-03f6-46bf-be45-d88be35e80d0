using Common.Extension;
using IotGateway.Application.Dump.Dtos;

namespace IotGateway.Application.Dump;

/// <summary>
///     网络抓包
///     版 本:V3.1.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-06-13
/// </summary>
[ApiDescriptionSettings("网关调试")]
public class DumpService : ISingleton, IDynamicApiController
{
    private static Process _process;

    /// <summary>
    ///     网络抓包
    /// </summary>
    /// <returns></returns>
    [HttpPost("/shell/dump/start")]
    public async Task<string> TcpDump(TcpDumpInput input)
    {
        //每次抓包前 删除历史抓包文件
        await ShellUtil.Bash("rm -rf /Edge/Data/sniff.cbap");

        var command = $"tcpdump -i {input.Interface} -c {input.Number}";
        var writeCommand = " -w /Edge/Data/sniff.cbap";

        switch (input.TcpDumpProtocol)
        {
            case TcpDumpProtocolEnum.Any:
                command += " -vnn ";
                break;
            case TcpDumpProtocolEnum.Tcp:
                command += " -vnn tcp";
                break;
            case TcpDumpProtocolEnum.Icmp:
                command += " -vnn icmp";
                break;
            case TcpDumpProtocolEnum.Udp:
                command += " -vnn udp";
                break;
            case TcpDumpProtocolEnum.Arp:
                command += " -vnn arp";
                break;
        }

        if (input.SourceIp.IsNotNull())
        {
            if (input.TcpDumpProtocol == TcpDumpProtocolEnum.Any)
                command += $" src {input.SourceIp}";
            else
                command += $" and src {input.SourceIp}";
        }

        if (input.GoalIp.IsNotNull())
        {
            if (input.SourceIp.IsNotNull())
            {
                command += $" and dst {input.GoalIp}";
            }
            else
            {
                if (input.TcpDumpProtocol == TcpDumpProtocolEnum.Any)
                    command += $" dst {input.GoalIp}";
                else
                    command += $" and dst {input.GoalIp}";
            }
        }

        //重定向输出结果
        command += writeCommand;
        // Log.Information($"【抓包命令】:{command}");
        var escapedArgs = command.Replace("\"", "\\\"");
        _process = new Process
        {
            StartInfo = new ProcessStartInfo
            {
                FileName = "/bin/bash",
                Arguments = $"-c \"{escapedArgs}\"",
                RedirectStandardOutput = true,
                UseShellExecute = false,
                CreateNoWindow = true
            }
        };
        _process.Start();
        var result = await _process.StandardOutput.ReadToEndAsync();
        await _process.WaitForExitAsync();
        _process.Dispose();
        _process = null;
        return result;
    }

    /// <summary>
    ///     网络抓包-停止抓包
    /// </summary>
    /// <returns></returns>
    [HttpPost("/shell/dump/stop")]
    public Task TcpDumpClose()
    {
        // 如果进程未启动或已经结束，返回错误信息
        if (_process == null || _process.HasExited)
            return Task.CompletedTask;
        // 强制终止进程
        _process.Kill();
        return Task.CompletedTask;
    }

    /// <summary>
    ///     网络抓包-下载抓包文件
    /// </summary>
    /// <returns></returns>
    [HttpPost("/shell/dump/down")]
    public async Task<IActionResult> TcpDumpDown()
    {
        var outFile = await File.ReadAllBytesAsync("/Edge/Data/sniff.cbap");
        if (outFile.Length <= 0) throw Oops.Oh("暂无抓包文件可下载");
        var memoryStream = new MemoryStream(outFile);
        return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = "sniff.cbap"
        };
    }
}