using Common.Extension;
using IotGateway.Application.Entity;
using IotGateway.TDengIne;
using IotGateway.TDengIne.Dto;
using MiniExcelLibs.Attributes;
using MiniExcelLibs.OpenXml;
using TDengIne.Dto;

namespace IotGateway.Application.TrendAnalysisServer;

/// <summary>
///     趋势分析表
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2022-11-23
/// </summary>
[ApiDescriptionSettings("系统配置")]
public class TrendAnalysisService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     趋势分析表
    /// </summary>
    private readonly SqlSugarRepository<TrendAnalysis> _trend;

    /// <summary>
    /// 时序库读取
    /// </summary>
    private readonly ReadService _readService;

    /// <summary>
    /// </summary>
    /// <param name="trend"></param>
    /// <param name="readService"></param>
    public TrendAnalysisService(SqlSugarRepository<TrendAnalysis> trend, ReadService readService)
    {
        _trend = trend;
        _readService = readService;
    }

    /// <summary>
    ///     趋势分析列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/trendAnalysis/page")]
    public async Task<SqlSugarPagedList<TrendAnalysisPageOutput>> Page([FromQuery] TrendAnalysisPageInput input)
    {
        var trendAnalysis = await _trend.AsQueryable()
            .WhereIF(input.SearchValue.IsNotNull(), w => w.Name.Contains(input.SearchValue))
            .OrderByDescending(o => o.CreatedTime)
            .Select<TrendAnalysisPageOutput>()
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return trendAnalysis;
    }

    /// <summary>
    ///     趋势分析详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/trendAnalysis/detail")]
    [SuppressMonitor]
    public async Task<TrendAnalysis> Detail([FromQuery] BaseId input)
    {
        var trendAnalysis = await _trend.GetSingleAsync(f => f.Id == input.Id);
        if (trendAnalysis == null)
            throw Oops.Oh("数据不存在！");
        return trendAnalysis;
    }

    /// <summary>
    ///     趋势分析保存
    /// </summary>
    /// <returns></returns>
    [HttpPost("/trendAnalysis/save")]
    [SuppressMonitor]
    public async Task Save(TrendAnalysis input)
    {
        if (await _trend.IsAnyAsync(a => a.Name == input.Name && input.Id != a.Id))
            throw Oops.Oh("名称已被使用！");
        var trendAnalysis = await _trend.GetSingleAsync(f => f.Id == input.Id);
        if (trendAnalysis == null)
            await _trend.InsertAsync(input);
        else
            await _trend.AsUpdateable(input).IgnoreColumns(w => new {w.CreatedTime, w.CreatedUserId, w.CreatedUserName}).ExecuteCommandAsync();
    }

    /// <summary>
    ///     趋势分析删除
    /// </summary>
    /// <returns></returns>
    [HttpPost("/trendAnalysis/delete")]
    public async Task Delete(BaseId input)
    {
        var trendAnalysis = await _trend.GetSingleAsync(f => f.Id == input.Id);
        if (trendAnalysis != null)
            await _trend.DeleteAsync(trendAnalysis);
    }
    
     /// <summary>
    ///     趋势分析器-趋势图数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/trendAnalysis/readData")]
    [SuppressMonitor]
    public ReadDataListOutput ReadDataList([FromQuery] ReadDataInput input)
    {
        return _readService.ReadDataList(input);
    }

    /// <summary>
    ///     趋势分析器-查询时间段数据总数
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/trendAnalysis/count")]
    [SuppressMonitor]
    public int ReadDataCount([FromQuery] ReadDataCountInput input)
    {
        return _readService.ReadDataCount(input);
    }

    /// <summary>
    ///     读取历史数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/trendAnalysis/historian")]
    [SuppressMonitor]
    public async Task<dynamic> Historian([FromQuery] GetHistoricalDataOfInstanceInput input)
    {
        return await _readService.GetHistoricalDataOfInstance(input);
    }

    /// <summary>
    ///     历史数据导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/trendAnalysis/historian_export")]
    [SuppressMonitor]
    public async Task<IActionResult> HistorianExport(HistorianExportInput input)
    {
        // 设备信息
        var device = await _trend.AsSugarClient().Queryable<Device>()
            .Where(w => w.Id == input.Id)
            .Includes(w => w.DeviceVariable.Where(x => input.Properties.Contains(x.Identifier)).ToList())
            .FirstAsync();
        if (device == null)
            throw Oops.Oh(ErrorCode.D1002);
        List<DynamicExcelColumn> dynamicExcelColumns = new();

        var executeData = await _readService.HistorianExport(input, device.DeviceName);
        var resultData = new List<Dictionary<string, object?>>();
        for (var i = 0; i < executeData.Columns.Length; i++)
        {
            if (executeData.Columns[i] == "ts")
            {
                executeData.Columns[i] = "基准时间" + "（ts）";
            }
            else
            {
                // 拼接属性中文名称
                var deviceVariable = device.DeviceVariable.FirstOrDefault(deviceVariable => executeData.Columns[i] == deviceVariable.Identifier.ToLower());
                if (deviceVariable != null)
                    executeData.Columns[i] = deviceVariable.Name + $"（{deviceVariable.Identifier}）";
            }

            dynamicExcelColumns.Add(new DynamicExcelColumn(executeData.Columns[i])
            {
                Index = i,
                Width = 30
            });
        }

        for (var j = 0; j < executeData.Rows.Count; j++)
        {
            var dicValue = new Dictionary<string, object?>(StringComparer.OrdinalIgnoreCase);
            for (var i = 0; i < executeData.Columns.Length; i++) dicValue.Add(executeData.Columns[i], executeData.Rows[j][i] == null ? null : executeData.Rows[j][i]);
            resultData.Add(dicValue);
        }

        var config = new OpenXmlConfiguration
        {
            TableStyles = TableStyles.Default,
            DynamicColumns = dynamicExcelColumns.ToArray()
        };
        var memoryStream = new MemoryStream();
        await memoryStream.SaveAsAsync(resultData, configuration: config);

        memoryStream.Seek(0, SeekOrigin.Begin);

        return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = $"{device.DeviceName}历史工况.xlsx"
        };
    }
}