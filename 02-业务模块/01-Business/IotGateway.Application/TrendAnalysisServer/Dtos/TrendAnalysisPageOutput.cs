using DateTime = System.DateTime;

namespace Feng.IotGateway.Application.TrendAnalysisServer.Dtos;

/// <summary>
///     趋势分析列表返回
/// </summary>
public class TrendAnalysisPageOutput
{
    /// <summary>
    ///     雪花Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    ///     创建者Id
    /// </summary>
    public long? CreatedUserId { get; set; }

    /// <summary>
    ///     修改者Id
    /// </summary>
    public long? UpdatedUserId { get; set; }

    /// <summary>
    ///     创建者名称
    /// </summary>
    public string CreatedUserName { get; set; }

    /// <summary>
    ///     修改者名称
    /// </summary>
    public string UpdatedUserName { get; set; }
}