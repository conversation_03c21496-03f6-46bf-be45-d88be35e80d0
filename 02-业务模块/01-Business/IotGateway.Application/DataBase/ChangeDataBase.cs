using Feng.IotGateway.Core.Service.User;
using IotGateway.Application.DataBase.Dto;
using IotGateway.Application.Entity;
using DbType = SqlSugar.DbType;

namespace IotGateway.Application.DataBase;

/// <summary>
///     实现切换数据库后操作
/// </summary>
public class ChangeDataBase : ITransient
{
    public readonly ISqlSugarClient Db;
    private readonly SingletonService _singleton;
    private readonly UserManager _userManager;

    public ChangeDataBase(ISqlSugarClient db, SingletonService singleton, UserManager userManager)
    {
        Db = db;
        _singleton = singleton;
        _userManager = userManager;
    }

    /// <summary>
    ///     获取表字段列表
    /// </summary>
    /// <param name="dataBase">数据连接</param>
    /// <param name="tableName">表名</param>
    /// <returns></returns>
    public List<DbTableFieldModel> GetFieldList(string dataBase, string tableName)
    {
        Db.AsTenant().ChangeDatabase(dataBase);
        var list = Db.DbMaintenance.GetColumnInfosByTableName(tableName);
        return list.Adapt<List<DbTableFieldModel>>();
    }

    /// <summary>
    ///     获取数据库表信息
    /// </summary>
    /// <returns></returns>
    public Dictionary<string, List<DbTableInfo>> GetTableInfos()
    {
        var dicRest = new Dictionary<string, List<DbTableInfo>>();

        var dbOptions = App.GetOptions<DbConnectionOptions>();
        if (_userManager.SuperAdmin)
            foreach (var connectionConfig in dbOptions.ConnectionConfigs)
            {
                Db.AsTenant().ChangeDatabase(connectionConfig.ConfigId);
                var table = Db.DbMaintenance.GetTableInfoList(false);
                dicRest.Add(connectionConfig.ConfigId.ToString(), table);
            }
        else
            //非超级管理员 某些数据库不允许查看
            foreach (var connectionConfig in dbOptions.ConnectionConfigs.Where(w => w.ConfigId != "old"))
            {
                Db.AsTenant().ChangeDatabase(connectionConfig.ConfigId);
                var table = Db.DbMaintenance.GetTableInfoList(false);
                dicRest.Add(connectionConfig.ConfigId.ToString(), table);
            }

        return dicRest;
    }

    /// <summary>
    ///     获取数据表分页(SQL语句)
    /// </summary>
    /// <param name="configId">数据连接</param>
    /// <param name="dbSql">数据SQL</param>
    /// <param name="pageIndex">页数</param>
    /// <param name="pageSize">条数</param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<dynamic>> GetDataTablePage(string configId, string dbSql, int pageIndex, int pageSize)
    {
        Db.AsTenant().ChangeDatabase(configId);
        RefAsync<int> totalNumber = 0;
        var list = await Db.SqlQueryable<object>(dbSql).ToDataTablePageAsync(pageIndex, pageSize, totalNumber);

        var totalPage = (int)Math.Ceiling(totalNumber / (double)pageSize);
        var pageList = new SqlSugarPagedList<dynamic>
        {
            Rows = ToDynamicList(list),
            PageNo = pageIndex,
            PageSize = pageSize,
            TotalPage = totalPage,
            TotalRows = totalNumber,
            HasNextPage = pageIndex < totalPage,
            HasPrevPage = pageIndex - 1 > 0
        };

        return pageList;
    }

    /// <summary>
    ///     测试数据库连接
    /// </summary>
    /// <param name="link">数据连接</param>
    /// <returns></returns>
    public bool IsConnection(DbLinkEntity link)
    {
        try
        {
            if (link != null)
            {
                var conn = new ConnectionConfig
                {
                    ConfigId = link.Id.ToString(),
                    DbType = ToDbType(link.DbType),
                    ConnectionString = ToConnectionString(link),
                    InitKeyType = InitKeyType.Attribute,
                    IsAutoCloseConnection = true
                };

                Db.AsTenant().AddConnection(conn);
                Db.AsTenant().ChangeDatabase(link.Id.ToString());
            }

            Db.Open();
            Db.Close();
            return true;
        }
        catch (Exception e)
        {
            Log.Error(e.Message);
            return false;
        }
    }

    /// <summary>
    ///     动态执行SQL(连接外部数据库)
    /// </summary>
    /// <param name="link">数据连接</param>
    /// <param name="sql"></param>
    /// <returns></returns>
    public async Task<dynamic> DynamicQuery(DbLinkEntity link, string sql)
    {
        var conn = new ConnectionConfig
        {
            ConfigId = link.Id.ToString(),
            DbType = ToDbType(link.DbType),
            ConnectionString = ToConnectionString(link),
            InitKeyType = InitKeyType.Attribute,
            IsAutoCloseConnection = true
        };

        Db.AsTenant().AddConnection(conn);
        Db.AsTenant().ChangeDatabase(link.Id.ToString());

        var table = await Db.Ado.GetDataTableAsync(sql);
        return table;
    }

    /// <summary>
    ///     动态执行SQL(仅支持查询)
    /// </summary>
    /// <param name="configId">数据连接</param>
    /// <param name="sql"></param>
    /// <returns></returns>
    public async Task<dynamic> DynamicQuery(string configId, string sql)
    {
        Db.AsTenant().ChangeDatabase(configId);
        if (!sql.ToLower().StartsWith("select"))
            throw Oops.Oh("暂不支持语句！");
        _singleton.AddToSqlActionRecord(sql);
        var table = await Db.SqlQueryable<object>(sql).ToListAsync();
        return table;
    }

    /// <summary>
    ///     动态执行SQL(不支持查询)
    /// </summary>
    /// <param name="configId">数据连接</param>
    /// <param name="sql"></param>
    /// <returns></returns>
    public async Task<int> ExecuteCommandAsync(string configId, string sql)
    {
        Db.AsTenant().ChangeDatabase(configId);
        if (sql.ToLower().StartsWith("select"))
            throw Oops.Oh("暂不支持查询语句！");
        _singleton.AddToSqlActionRecord(sql);
        var table = await Db.Ado.ExecuteCommandAsync(sql);
        return table;
    }

    /// <summary>
    ///     转换数据库类型
    /// </summary>
    /// <param name="dbType"></param>
    /// <returns></returns>
    private DbType ToDbType(DbTypeEnum dbType)
    {
        return dbType switch
        {
            DbTypeEnum.MySql => DbType.MySql,
            DbTypeEnum.SqlServer => DbType.SqlServer,
            _ => throw Oops.Oh(ErrorCode.D1506)
        };
    }

    /// <summary>
    ///     转换连接字符串
    /// </summary>
    /// <param name="dbLinkEntity"></param>
    /// <returns></returns>
    public string ToConnectionString(DbLinkEntity dbLinkEntity)
    {
        switch (dbLinkEntity.DbType)
        {
            // case "oracle":
            //     return string.Format("Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST={0})(PORT={1}))(CONNECT_DATA=(SERVER = DEDICATED)(SERVICE_NAME={2})));User Id={3};Password=***", dbLinkEntity.Host, dbLinkEntity.Port.ToString(),
            //         dbLinkEntity.TableSpace, dbLinkEntity.UserName, dbLinkEntity.Password);
            // case "dm8":
            //     return string.Format("server={0};port={1};database={2};User Id={3};PWD=***", dbLinkEntity.Host, dbLinkEntity.Port.ToString(), dbLinkEntity.ServiceName, dbLinkEntity.UserName, dbLinkEntity.Password);
            // case "dm":
            //     return string.Format("server={0};port={1};database={2};User Id={3};PWD=***", dbLinkEntity.Host, dbLinkEntity.Port.ToString(), dbLinkEntity.ServiceName, dbLinkEntity.UserName, dbLinkEntity.Password);
            // case "kdbndp":
            //     return string.Format("server={0};port={1};database={2};UID={3};PWD=***", dbLinkEntity.Host, dbLinkEntity.Port.ToString(), dbLinkEntity.ServiceName, dbLinkEntity.UserName, dbLinkEntity.Password);
            // case "kingbasees":
            //     return string.Format("server={0};port={1};database={2};UID={3};PWD=***", dbLinkEntity.Host, dbLinkEntity.Port.ToString(), dbLinkEntity.ServiceName, dbLinkEntity.UserName, dbLinkEntity.Password);
            // case "postgresql":
            //     return string.Format("server={0};port={1};Database={2};User Id={3};Password=***", dbLinkEntity.Host, dbLinkEntity.Port.ToString(), dbLinkEntity.ServiceName, dbLinkEntity.UserName, dbLinkEntity.Password);
            case DbTypeEnum.MySql:
                return string.Format("server={0};port={1};database={2};user={3};password=***;AllowLoadLocalInfile=true;", dbLinkEntity.Host, dbLinkEntity.Port.ToString(), dbLinkEntity.ServiceName,
                    dbLinkEntity.UserName,
                    dbLinkEntity.Password);
            case DbTypeEnum.SqlServer:
                return string.Format("Data Source={0},***;Initial Catalog={1};User ID={2};Password={3};MultipleActiveResultSets=true", dbLinkEntity.Host, dbLinkEntity.ServiceName,
                    dbLinkEntity.UserName, dbLinkEntity.Password,
                    dbLinkEntity.Port);
            default:
                throw Oops.Oh(ErrorCode.D1506);
        }
    }

    /// <summary>
    ///     将DataTable 转换成 List
    /// </summary>
    /// <param name="table"></param>
    /// <param name="reverse">反转:控制返回结果中是只存在 FilterField 指定的字段,还是排除.[flase 返回FilterField 指定的字段]|[true 返回结果剔除 FilterField 指定的字段]</param>
    /// <param name="filterField">字段过滤,FilterField 为空 忽略 reverse 参数；返回DataTable中的全部数</param>
    /// <returns></returns>
    public static List<dynamic> ToDynamicList(DataTable table, bool reverse = true, params string[] filterField)
    {
        var modelList = new List<dynamic>();
        foreach (DataRow row in table.Rows)
        {
            dynamic model = new ExpandoObject();
            var dict = (IDictionary<string, object>)model;
            foreach (DataColumn column in table.Columns)
                if (filterField.Length != 0)
                {
                    if (reverse)
                    {
                        if (!filterField.Contains(column.ColumnName)) dict[column.ColumnName] = row[column];
                    }
                    else
                    {
                        if (filterField.Contains(column.ColumnName)) dict[column.ColumnName] = row[column];
                    }
                }
                else
                {
                    dict[column.ColumnName] = row[column];
                }

            modelList.Add(model);
        }

        return modelList;
    }
}