using IotGateway.Application.DataBase.Dto;
using IotGateway.Application.Entity;

namespace IotGateway.Application.DataBase;

/// <summary>
///     数据连接
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2022-10-01
/// </summary>
[ApiDescriptionSettings("转发配置")]
public class DbLinkService : IDynamicApiController, ITransient
{
    private readonly ChangeDataBase _changeDataBase;
    private readonly SqlSugarRepository<DbLinkEntity> _dbLinkRepository;

    /// <summary>
    /// </summary>
    public DbLinkService(SqlSugarRepository<DbLinkEntity> dbLinkRepository, ChangeDataBase changeDataBase)
    {
        _dbLinkRepository = dbLinkRepository;
        _changeDataBase = changeDataBase;
    }

    /// <summary>
    ///     信息.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [NonAction]
    public async Task<DbLinkEntity> GetInfo(long id)
    {
        return await _dbLinkRepository.GetSingleAsync(m => m.Id == id);
    }

    #region GET

    /// <summary>
    ///     下拉框列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/dbLink/select")]
    public async Task<List<DbLinkEntity>> Select()
    {
        var list = await _dbLinkRepository.AsQueryable()
            .OrderBy(o => o.SortCode).OrderByDescending(o => o.CreatedTime)
            .Select<DbLinkEntity>()
            .ToListAsync();
        return list;
    }

    #endregion

    #region POST

    /// <summary>
    ///     删除
    /// </summary>
    /// <param name="input">主键值</param>
    /// <returns></returns>
    [HttpPost("/dbLink/delete")]
    public async Task Delete(BaseId input)
    {
        var data = await _dbLinkRepository.GetFirstAsync(m => m.Id == input.Id);
        if (data == null)
            throw Oops.Oh(ErrorCode.D1002);
        await _dbLinkRepository.DeleteAsync(data);
    }

    /// <summary>
    ///     创建
    /// </summary>
    /// <param name="input">实体对象</param>
    /// <returns></returns>
    [HttpPost("/dbLink/add")]
    public async Task Create(DbLinkCrInput input)
    {
        if (await _dbLinkRepository.IsAnyAsync(m => m.FullName == input.FullName)) throw Oops.Oh(ErrorCode.Com1004);
        var entity = input.Adapt<DbLinkEntity>();
        await _dbLinkRepository.InsertAsync(entity);
    }

    /// <summary>
    ///     编辑
    /// </summary>
    /// <param name="input">实体对象</param>
    /// <returns></returns>
    [HttpPost("/dbLink/update")]
    public async Task Update(DbLinkEntity input)
    {
        if (await _dbLinkRepository.IsAnyAsync(x => x.Id != input.Id && x.FullName == input.FullName))
            throw Oops.Oh(ErrorCode.Com1004);
        await _dbLinkRepository.UpdateAsync(input);
    }

    /// <summary>
    ///     测试连接
    /// </summary>
    /// <param name="input">实体对象</param>
    /// <returns></returns>
    [HttpPost("/dbLink/actions/test")]
    public void TestDbConnection(DbLinkActionsTestInput input)
    {
        var entity = input.Adapt<DbLinkEntity>();
        entity.Id = entity.Id == 0L ? YitIdHelper.NextId() : input.Id;
        var flag = _changeDataBase.IsConnection(entity);
        if (!flag)
            throw Oops.Oh(ErrorCode.D1507);
    }

    /// <summary>
    ///     动态执行SQL
    /// </summary>
    /// <returns></returns>
    [HttpPost("/dbLink/dynamic-query")]
    public async Task<dynamic> Query(ScreenDynamicQueryInput input)
    {
        if (input.Id <= 0) return Task.FromResult(true);
        var entity = await _dbLinkRepository.GetSingleAsync(v => v.Id == input.Id);
        if (entity == null)
            throw Oops.Oh("数据源不存在！");
        return await _changeDataBase.DynamicQuery(entity, input.Sql);
    }

    #endregion
}