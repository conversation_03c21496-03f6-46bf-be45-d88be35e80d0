namespace IotGateway.Application.DataBase.Dto;

/// <summary>
///     数据库表列表输出.
/// </summary>
[SuppressSniffer]
public class DatabaseTableListOutput
{
    /// <summary>
    ///     表名.
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     说明.
    /// </summary>
    public string Description { get; set; }
    
    /// <summary>
    ///     表记录数.
    /// </summary>
    public int Sum { get; set; }
}

/// <summary>
/// 
/// </summary>
[SuppressSniffer]
public class TableGetListOutput
{
    /// <summary>
    /// 数据库名称
    /// </summary>
    public string DataBase { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<DatabaseTableListOutput> DatabaseTable { get; set; }
}

/// <summary>
/// </summary>
[SuppressSniffer]
public class DbTableFieldModel
{
    /// <summary>
    ///     字段名
    /// </summary>
    public string DbColumnName { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    public string DataType { get; set; }

    /// <summary>
    ///     数据长度
    /// </summary>
    public int Length { get; set; }

    /// <summary>
    ///     自增
    /// </summary>
    public bool IsIdentity { get; set; }

    /// <summary>
    ///     主键.
    /// </summary>
    public bool IsPrimarykey { get; set; }

    /// <summary>
    ///     允许null值
    /// </summary>
    public bool IsNullable { get; set; }

    /// <summary>
    ///     默认值
    /// </summary>
    public string DefaultValue { get; set; }

    /// <summary>
    ///     说明
    /// </summary>
    public string ColumnDescription { get; set; }
}