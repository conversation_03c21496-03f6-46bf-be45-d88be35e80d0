
using IotGateway.Application.Entity;

namespace IotGateway.Application.DataBase.Dto;

/// <summary>
/// </summary>
[SuppressSniffer]
public class DbLinkCrInput
{
    /// <summary>
    ///     数据库名
    /// </summary>
    public string ServiceName { get; set; }

    /// <summary>
    ///     用户名
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    ///     端口
    /// </summary>
    public short Port { get; set; }

    /// <summary>
    ///     数据库驱动类型
    /// </summary>
    public DbTypeEnum DbType { get; set; }

    /// <summary>
    ///     连接名称
    /// </summary>
    public string FullName { get; set; }

    /// <summary>
    ///     主机地址
    /// </summary>
    public string Host { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public long? SortCode { get; set; }

    /// <summary>
    ///     模式
    /// </summary>
    public string DbSchema { get; set; }

    /// <summary>
    ///     表空间
    /// </summary>
    public string TableSpace { get; set; }
}


/// <summary>
/// 动态查询sql
/// </summary>
public class DynamicQueryInput
{
    /// <summary>
    /// 执行语句
    /// </summary>
    [Required]
    public string Sql { get; set; }
}

/// <summary>
///     数据源动态查询
/// </summary>
public class ScreenDynamicQueryInput
{
    /// <summary>
    ///     数据连接Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     执行SQL
    /// </summary>
    [Required]
    public string Sql { get; set; }
}

/// <summary>
///     数据库表预览查询.
/// </summary>
[SuppressSniffer]
public class DatabaseTablePreviewQuery : BasePageInput
{
    /// <summary>
    ///     字段.
    /// </summary>
    public string Field { get; set; }
}

/// <summary>
/// </summary>
[SuppressSniffer]
public class DbLinkActionsTestInput
{
    /// <summary>
    ///     连接类型
    /// </summary>
    public DbTypeEnum DbType { get; set; }

    /// <summary>
    ///     主机
    /// </summary>
    public string Host { get; set; }

    /// <summary>
    ///     端口
    /// </summary>
    public short Port { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    ///     用户名
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    ///     库名
    /// </summary>
    public string ServiceName { get; set; }

    /// <summary>
    ///     模式
    /// </summary>
    public string DbSchema { get; set; }

    /// <summary>
    ///     表空间
    /// </summary>
    public string TableSpace { get; set; }

    /// <summary>
    ///     id
    /// </summary>
    public long Id { get; set; }
}