using Feng.IotGateway.Core.Service.User;
using IotGateway.Application.DataBase.Dto;

namespace IotGateway.Application.DataBase;

/// <summary>
///     数据管理
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2022-10-01
/// </summary>
[ApiDescriptionSettings("转发配置")]
public class DataBaseService : IDynamicApiController, ITransient
{
    private readonly UserManager _userManager;

    /// <summary>
    ///     数据库管理.
    /// </summary>
    private readonly ChangeDataBase _dataBaseManager;

    private readonly SingletonService _singleton;

    /// <summary>
    /// </summary>
    /// <param name="dataBaseManager"></param>
    /// <param name="userManager">用户信息</param>
    /// <param name="singleton">全局公共单例服务</param>
    /// <exception cref="AppFriendlyException"></exception>
    public DataBaseService(ChangeDataBase dataBaseManager, UserManager userManager, SingletonService singleton)
    {
        _dataBaseManager = dataBaseManager;
        _userManager = userManager;
        _singleton = singleton;
    }

    #region GET

    /// <summary>
    ///     Sql最近执行记录
    /// </summary>
    /// <returns></returns>
    [HttpGet("/table/sqlRecord")]
    public Task<List<dynamic>> SqlRecord()
    {
        return Task.FromResult(_singleton.SqlActionRecord.OrderByDescending(u => u.Time).ToList());
    }

    /// <summary>
    ///     表名列表.
    /// </summary>
    /// <returns></returns>
    [HttpGet("/table/tree")]
    public Task<List<TableGetListOutput>> GetList()
    {
        try
        {
            //返回全部数据得表结构
            var tables = _dataBaseManager.GetTableInfos();
            var resultData = new List<TableGetListOutput>();
            var superAdmin = _userManager.SuperAdmin;
            foreach (var (key, value) in tables)
            {
                var database = value;
                //非超级管理员过滤掉不能看的表
                if (!superAdmin && key == "业务数据(配置中心)")
                    continue;

                if (!database.Any())
                    continue;
                var tableGetList = new TableGetListOutput
                {
                    DataBase = key,
                    DatabaseTable = database.Adapt<List<DatabaseTableListOutput>>()
                };
                //查询数据总数
                GetTableCount(key, tableGetList.DatabaseTable);
                resultData.Add(tableGetList);
            }

            return Task.FromResult(resultData);
        }
        catch (Exception)
        {
            return Task.FromResult(new List<TableGetListOutput>());
        }
    }

    /// <summary>
    ///     字段列表.
    /// </summary>
    /// <param name="dataBase">连接数据库.</param>
    /// <param name="tableName">表名.</param>
    /// <returns></returns>
    [HttpGet("/table/{dataBase}/{tableName}/fields")]
    public Task<List<DbTableFieldModel>> GetFieldList(string dataBase, string tableName)
    {
        if (string.IsNullOrEmpty(tableName))
            return Task.FromResult(new List<DbTableFieldModel>());
        var data = _dataBaseManager.GetFieldList(dataBase, tableName).Adapt<List<DbTableFieldModel>>();
        return Task.FromResult(data);
    }

    /// <summary>
    ///     预览数据.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <param name="dataBase">连接数据库.</param>
    /// <param name="tableName">表名.</param>
    /// <returns></returns>
    [HttpGet("/table/{dataBase}/{tableName}/preview")]
    public async Task<SqlSugarPagedList<dynamic>> GetData([FromQuery] DatabaseTablePreviewQuery input, string dataBase, string tableName)
    {
        if (string.IsNullOrEmpty(tableName))
            return new SqlSugarPagedList<dynamic>();
        var dbSql = new StringBuilder();
        dbSql.AppendFormat("SELECT * FROM '{0}' WHERE 1=1", tableName);
        if (!string.IsNullOrEmpty(input.Field) && !string.IsNullOrEmpty(input.SearchValue))
            dbSql.AppendFormat(" AND {0} like '%{1}%'", input.Field, input.SearchValue);

        return await _dataBaseManager.GetDataTablePage(dataBase, dbSql.ToString(), input.PageNo, input.PageSize);
    }

    /// <summary>
    ///     动态执行SQL(仅支持查询)
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <param name="dataBase">连接数据库.</param>
    /// <returns></returns>
    [HttpGet("/table/{dataBase}/dynamicQuery")]
    public async Task<dynamic> DynamicQuery([FromQuery] DynamicQueryInput input, string dataBase)
    {
        if (string.IsNullOrEmpty(dataBase))
            throw Oops.Oh("请指定连接数据库！");

        var data = await _dataBaseManager.DynamicQuery(dataBase, input.Sql);
        return data;
    }

    /// <summary>
    ///     动态执行SQL(不支持查询)
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <param name="dataBase">连接数据库.</param>
    /// <returns></returns>
    [HttpGet("/table/{dataBase}/executeCommand")]
    public async Task<dynamic> ExecuteCommand([FromQuery] DynamicQueryInput input, string dataBase)
    {
        if (string.IsNullOrEmpty(dataBase))
            throw Oops.Oh("请指定连接数据库！");
        var superAdmin = _userManager.SuperAdmin;
        if (!superAdmin)
            throw Oops.Oh("暂无权限");
        var data = await _dataBaseManager.ExecuteCommandAsync(dataBase, input.Sql);
        return data;
    }

    #endregion

    #region Private

    /// <summary>
    ///     获取表条数.
    /// </summary>
    /// <param name="dataBase"></param>
    /// <param name="tableList"></param>
    private void GetTableCount(string dataBase, List<DatabaseTableListOutput> tableList)
    {
        foreach (var item in tableList)
            try
            {
                item.Sum = _dataBaseManager.Db.AsTenant().GetConnection(dataBase).Queryable<dynamic>().AS(item.Name).Count();
            }
            catch (Exception)
            {
                item.Sum = 0;
            }
    }

    #endregion
}