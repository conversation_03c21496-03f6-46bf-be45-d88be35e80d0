using IotGateway.Application.Entity;
using IotGateway.Application.PortForwardingServer.Dtos;

namespace IotGateway.Application.PortForwardingServer;

/// <summary>
///     端口转发
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-06-05
/// </summary>
[ApiDescriptionSettings("网络配置")]
public class PortForwardingService : ITransient, IDynamicApiController
{
    /// <summary>
    ///     数据源表
    /// </summary>
    private readonly SqlSugarRepository<PortForwarding> _portForWarding;

    /// <summary>
    /// </summary>
    /// <param name="portForWarding"></param>
    public PortForwardingService(SqlSugarRepository<PortForwarding> portForWarding)
    {
        _portForWarding = portForWarding;
    }

    /// <summary>
    ///     端口转发-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/portForwarding/page")]
    public async Task<dynamic> Page([FromQuery] BasePageInput input)
    {
        var page = await _portForWarding.AsQueryable()
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue), w => w.InsideIp.Contains(input.SearchValue)
                                                                    || w.Description.Contains(input.SearchValue)
                                                                    || w.OutsideIp.Contains(input.SearchValue)
                                                                    || w.OutInterface.Contains(input.SearchValue))
            .OrderByDescending(u => u.Id)
            .ToPagedListAsync(input.PageNo, input.PageSize);

        return page;
    }

    /// <summary>
    ///     端口转发-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/portForwarding/detail")]
    public async Task<dynamic> Detail([FromQuery] BaseId input)
    {
        return await _portForWarding.AsQueryable().FirstAsync(f => f.Id == input.Id);
    }

    /// <summary>
    ///     端口转发-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/portForwarding/add")]
    public async Task Add(PortForwardingAddInput input)
    {
        var portForwarding = input.Adapt<PortForwarding>();
        await _portForWarding.InsertAsync(portForwarding);

        switch (portForwarding.Action)
        {
            // 源地址转换，可以将源 IP 地址替换为指定的 IP 地址，一般用于实现出口流量负载均衡、主备切换等场景。
            case PortForwardingActionEnum.Snat:
            {
                //iptables -t nat -A PREROUTING -p tcp --dport [本地端口] -j DNAT --to-destination [目标IP:目标端口]
                break;
            }
            // 目标地址转换，可以将目标 IP 地址替换为指定的 IP 地址，一般用于实现端口映射、反向代理等场景。
            case PortForwardingActionEnum.Dnat:
            {
                
                break;
            }
            // MASQUERADE：与 SNAT 类似，但是它不需要指定具体的 IP 地址，而是动态地将源 IP 地址替换为出口网络接口的 IP 地址，常用于 Internet 访问场景。
            case PortForwardingActionEnum.Masquerade:
            {
                
                break;
            }
        }
        
        switch (portForwarding.Protocol)
        {
            case PortForwardingProtocolEnum.Any:
                //iptables -A INPUT -p any -s ***********/24 -j ACCEPT
                break;
            case PortForwardingProtocolEnum.Tcp:
                break;
            case PortForwardingProtocolEnum.Udp:
                break;
            case PortForwardingProtocolEnum.Icmp:
                break;
        }
        //iptables -t nat -A PREROUTING -p tcp --dport [本地端口] -j DNAT --to-destination [目标IP:目标端口]
        //iptables -t nat -A PREROUTING -p tcp --dport 9011 -j DNAT --to-destination *************:9004
        //iptables -t nat -A POSTROUTING -p tcp -d ************* --dport 9004 -j SNAT --to *************
        //iptables -t nat -A PREROUTING -p tcp --dport 80 -j DNAT --to-destination 远程服务器IP:8080

    }

    /// <summary>
    ///     端口转发-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/portForwarding/update")]
    public async Task Update(PortForwardingUpdateInput input)
    {
        var portForwarding = await _portForWarding.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (portForwarding == null)
            throw Oops.Oh(ErrorCode.D1002);
        portForwarding = input.Adapt<PortForwarding>();
        await _portForWarding.UpdateAsync(portForwarding);
        
        //删除
        //iptables -t nat -D PREROUTING -p tcp --dport 9011 -j DNAT --to-destination *************:9004
    }

    /// <summary>
    ///     端口转发-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/portForwarding/delete")]
    public async Task Delete(BaseId input)
    {
        var portForwarding = await _portForWarding.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (portForwarding == null)
            return;
        await _portForWarding.DeleteAsync(portForwarding);
    }
}