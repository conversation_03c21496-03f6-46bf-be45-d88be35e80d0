using IotGateway.Application.Entity;

namespace IotGateway.Application.PortForwardingServer.Dtos;

/// <summary>
/// 端口转发-新增输入参数
/// </summary>
public class PortForwardingAddInput 
{
    /// <summary>
    ///     动作: 1SNAT；2DNAT；3MASQUERADE
    /// </summary>
    [Required]
    public PortForwardingActionEnum Action { get; set; }

    /// <summary>
    ///     协议:1Any;2TCP;3UDP;4ICMP
    /// </summary>
    [Required]
    public PortForwardingProtocolEnum Protocol { get; set; }

    /// <summary>
    ///     内部端口范围:1包含；2不包含;
    /// </summary>
    public PortForwardingExpressionEnum InsideExpression { get; set; }

    /// <summary>
    ///     内部Ip
    /// </summary>
    public string InsideIp { get; set; }

    /// <summary>
    ///     内部起始端口
    /// </summary>
    public short InsidePortStart { get; set; }

    /// <summary>
    ///     内部截止端口
    /// </summary>
    public short InsidePortEnd { get; set; }

    /// <summary>
    ///     外部端口范围:1包含；2不包含;
    /// </summary>
    public PortForwardingExpressionEnum OutsideExpression { get; set; }

    /// <summary>
    ///     外部Ip
    /// </summary>
    public string OutsideIp { get; set; }

    /// <summary>
    ///     外部起始端口
    /// </summary>
    public short OutsidePortStart { get; set; }

    /// <summary>
    ///     外部截止端口
    /// </summary>
    public short OutsidePortEnd { get; set; }

    /// <summary>
    ///     出接口
    /// </summary>
    public string OutInterface { get; set; }

    /// <summary>
    /// </summary>
    public string Description { get; set; }
}

/// <summary>
/// 端口转发-修改输入参数
/// </summary>
public class PortForwardingUpdateInput : PortForwardingAddInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}