using Feng.IotGateway.Core.Service.Config;
using IotGateway.Application.Entity;

namespace IotGateway.EdgeDevice.Services;

/// <summary>
///     设备Dnc管理
///     版 本:V3.1.1.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-06-21
/// </summary>
[ApiDescriptionSettings("设备管理")]
public class DncFileService : ITransient, IDynamicApiController
{
    private readonly SqlSugarRepository<DncFile> _dncFile;
    private readonly SysConfigService _sysConfigService;

    public DncFileService(SqlSugarRepository<DncFile> dncFile, SysConfigService sysConfigService)
    {
        _dncFile = dncFile;
        _sysConfigService = sysConfigService;
    }

    /// <summary>
    ///     Dnc程序列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/device/dnc/list")]
    public async Task<List<DncFile>> DeviceDncList([FromQuery] BaseId input)
    {
        return await _dncFile.AsQueryable().Where(w => w.DeviceId == input.Id).ToListAsync();
    }

    /// <summary>
    ///     文件重命名
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/dnc/reName")]
    public async Task FileReName(FileReNameInput input)
    {
        var dncFile = await _dncFile.AsQueryable().FirstAsync(f => f.Id == input.Id);
        var filePath4 = Path.Combine(dncFile.FilePath, string.IsNullOrEmpty(dncFile.FileSuffix) ? dncFile.Id + ".txt" : dncFile.Id + dncFile.FileSuffix);

        // 检查源文件是否存在
        if (File.Exists(filePath4))
        {
            dncFile.FileName = input.NewName;
            // // 使用Move方法将源文件重命名为目标文件
            // Files.Move(filePath4, Path.Combine(file.FilePath, input.NewName));
            await _dncFile.UpdateAsync(dncFile);
        }
        else
        {
            throw Oops.Oh("源文件不存在，请检查路径是否正确");
        }
    }

    /// <summary>
    ///     预览文件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/device/dnc/preview")]
    public async Task<string> PreviewFileInfo([FromQuery] BaseId input)
    {
        var dncFile = await _dncFile.AsQueryable().FirstAsync(f => f.Id == input.Id);
        var content = await File.ReadAllTextAsync(Path.Combine(dncFile.FilePath, string.IsNullOrEmpty(dncFile.FileSuffix) ? dncFile.Id + ".txt" : dncFile.Id + dncFile.FileSuffix));
        return content;
    }

    /// <summary>
    ///     上传Dnc文件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/dnc/upload")]
    public async Task UploadFileInfo([FromForm] UploadFileInfoInput input)
    {
        var device = await _dncFile.AsSugarClient().Queryable<Device>().AnyAsync(a => a.Id == input.DeviceId);
        if (!device)
            throw Oops.Oh("设备已被删除,请刷新后重试!");
        string path;
        //是否存在相同文件
        var dncFile = await _dncFile.AsQueryable().FirstAsync(f => f.DeviceId == input.DeviceId && f.FileName == input.File.FileName);
        if (dncFile != null)
        {
            path = dncFile.FilePath;
            var fileSizeKb = (long) (input.File.Length / 1024.0) == 0 ? 1 : (long) (input.File.Length / 1024.0); // 文件大小KB
            dncFile.FileSize = fileSizeKb;
            dncFile.Version += 1;
            await _dncFile.UpdateAsync(dncFile);
        }
        else
        {
            path = await _sysConfigService.GetConfigValue<string>(ConfigConst.DncFilePath);
            dncFile = new DncFile
            {
                DeviceId = input.DeviceId,
                FileSize = (long) (input.File.Length / 1024.0) == 0 ? 1 : (long) (input.File.Length / 1024.0), // 文件大小KB
                Version = 1,
                FileName = input.File.FileName,
                FileSuffix = Path.GetExtension(input.File.FileName).ToLower(),
                FilePath = path
            };
            await _dncFile.InsertAsync(dncFile);
        }

        if (!Directory.Exists(path))
            Directory.CreateDirectory(path!);
        await using var stream = File.Create(Path.Combine(path, string.IsNullOrEmpty(dncFile.FileSuffix) ? dncFile.Id + ".txt" : dncFile.Id + dncFile.FileSuffix));
        await input.File.CopyToAsync(stream);
    }

    /// <summary>
    ///     删除文件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/dnc/delete")]
    public async Task DeleteFileInfo(BaseId input)
    {
        var dncFile = await _dncFile.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (dncFile != null)
        {
            await _dncFile.DeleteAsync(dncFile);
            var filePath = Path.Combine(dncFile.FilePath, string.IsNullOrEmpty(dncFile.FileSuffix) ? dncFile.Id + ".txt" : dncFile.Id + dncFile.FileSuffix);
            if (File.Exists(filePath))
                File.Delete(filePath);
        }
    }

    /// <summary>
    ///     编辑文件内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/device/dnc/update")]
    public async Task PreviewFileEdit(PreviewFileEditInput input)
    {
        var dncFile = await _dncFile.AsQueryable().FirstAsync(f => f.Id == input.Id);
        var fileSizeKb = (long) (input.Content.Length / 1024.0) == 0 ? 1 : (long) (input.Content.Length / 1024.0); // 文件大小KB
        dncFile.FileSize = fileSizeKb;
        // //相同文件存在，记录下来并保存版本记录
        // var content = await Files.ReadAllTextAsync(Path.Combine(file.FilePath, file.FileName));
        dncFile.Version += 1;
        await _dncFile.UpdateAsync(dncFile);
        await File.WriteAllTextAsync(Path.Combine(dncFile.FilePath, string.IsNullOrEmpty(dncFile.FileSuffix) ? dncFile.Id + ".txt" : dncFile.Id + dncFile.FileSuffix), input.Content);
    }
}