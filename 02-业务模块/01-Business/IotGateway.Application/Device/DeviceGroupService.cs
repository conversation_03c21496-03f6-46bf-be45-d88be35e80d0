using IotGateway.Application.Entity;
using NewLife.Caching;

namespace IotGateway.Application;

/// <summary>
///     设备分组
/// </summary>
[ApiDescriptionSettings("设备管理")]
public class DeviceGroupService : IDynamic<PERSON><PERSON><PERSON>ontroller, ITransient
{
    private readonly SqlSugarRepository<DeviceGroup> _deviceGroup;
    private readonly ICache _cache;
    private const string CacheKey = "DeviceGroup:Tree";

    /// <summary>
    /// </summary>
    /// <param name="deviceGroup"></param>
    /// <param name="cache"></param>
    public DeviceGroupService(SqlSugarRepository<DeviceGroup> deviceGroup, ICache cache)
    {
        _deviceGroup = deviceGroup;
        _cache = cache;
    }

    /// <summary>
    ///     分组-树
    /// </summary>
    /// <returns></returns>
    [HttpGet("/deviceGroup/tree")]
    public async Task<List<DeviceGroup>> DeviceGroupTree()
    {
        // 先尝试从缓存获取
        var cached = _cache.Get<List<DeviceGroup>>(CacheKey);
        if (cached != null)
            return cached;

        var list = await _deviceGroup.AsQueryable()
            .Includes(x => x.Parent)
            .ToTreeAsync(x => x.Children, x => x.ParentId, 0);
        return list;
    }

    /// <summary>
    ///     分组--新增
    /// </summary>
    /// <returns></returns>
    [HttpPost("/deviceGroup/add")]
    public async Task DeviceGroupAdd(DeviceGroupAddInput input)
    {
        var isExist = await _deviceGroup.IsAnyAsync(u => u.Name == input.Name);
        if (isExist)
            throw Oops.Oh(ErrorCode.Com1004);
        var deviceGroup = input.Adapt<DeviceGroup>();
        await _deviceGroup.CopyNew().InsertAsync(deviceGroup);
        _cache.Remove(CacheKey); // 更新后清除缓存
    }

    /// <summary>
    ///     分组--修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/deviceGroup/update")]
    public async Task DeviceGroupUpdate(DeviceGroup input)
    {
        var isExist = await _deviceGroup.IsAnyAsync(u => u.Name == input.Name && u.Id != input.Id);
        if (isExist)
            throw Oops.Oh(ErrorCode.Com1004);
        // 设备分组
        var deviceGroup = await _deviceGroup.GetFirstAsync(f => f.Id == input.Id);
        if (deviceGroup == null)
            throw Oops.Oh(ErrorCode.D1002);

        deviceGroup = input.Adapt<DeviceGroup>();
        await _deviceGroup.CopyNew().UpdateAsync(deviceGroup);
    }

    /// <summary>
    ///     分组--删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/deviceGroup/delete")]
    public async Task DeviceGroupDelete(BaseId input)
    {
        var deviceGroup = await _deviceGroup.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (deviceGroup == null)
            throw Oops.Oh(ErrorCode.D1002);
        if (await _deviceGroup.AsSugarClient().Queryable<Device>().AnyAsync(a => a.DeviceGroupId == input.Id))
            throw Oops.Oh("该分组已经被设备绑定,请先解除绑定!");
        if (await _deviceGroup.AsSugarClient().Queryable<DeviceGroup>().AnyAsync(a => a.ParentId == input.Id))
            throw Oops.Oh("请先删除下级分组!");
        await _deviceGroup.CopyNew().DeleteAsync(deviceGroup);
    }
}