using DateTime = Common.Extension.DateTime;

namespace IotGateway.Application;

/// <summary>
///     全局单例服务,用来记录某些执行记录
/// </summary>
public class SingletonService : ISingleton
{
    /// <summary>
    ///     最新执行sql语句
    /// </summary>
    /// <remarks>默认只保存 10 条</remarks>
    public Queue<dynamic> SqlActionRecord { get; set; } = new();

    /// <summary>
    ///     添加执行sql语句
    /// </summary>
    /// <param name="sql"></param>
    public void AddToSqlActionRecord(string sql)
    {
        if (SqlActionRecord.Count > 10)
            SqlActionRecord.Dequeue();

        SqlActionRecord.Enqueue(new
        {
            Time = DateTime.ShangHai(),
            Sql = sql
        });
    }
    
    /// <summary>
    ///     执行策略日志记录
    /// </summary>
    /// <remarks>默认只保存 100 条</remarks>
    public ConcurrentDictionary<long, List<RunRecordLine>> RecordLines { get; set; } = new();
    
    /// <summary>
    ///     添加任务执行记录
    /// </summary>
    public void AddRecord(long id, string msg)
    {
        const int maxRecordCount = 50;

        if (RecordLines.ContainsKey(id))
        {
            var recordList = RecordLines[id];
            if (recordList.Count > maxRecordCount)
                recordList.RemoveAt(0);

            recordList.Add(new RunRecordLine
            {
                Time = DateTime.ShangHai(),
                Message = msg
            });

            RecordLines[id] = recordList;
            return;
        }

        var newRecordList = new List<RunRecordLine>
        {
            new()
            {
                Time = DateTime.ShangHai(),
                Message = msg
            }
        };

        RecordLines.TryAdd(id, newRecordList);
    }
}