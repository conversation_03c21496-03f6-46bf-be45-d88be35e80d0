using Common.Extension;
using IotGateway.Application.Entity;
using TDengIne.Dto;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Application.OpenApiServer;

/// <summary>
///     对接开放Api
///     版 本:V3.0.7.4+
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-05-17
/// </summary>
[ApiDescriptionSettings("系统配置")]
public class OpenApiService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<OpenApiEntity> _openApi;
    private readonly DeviceHostedService _deviceHostedService;
    private readonly DeviceExec _deviceExec;
    private readonly DeviceService _deviceService;
    private readonly ReadService _readService;

    /// <summary>
    /// </summary>
    /// <param name="openApiEntity"></param>
    /// <param name="deviceHostedService"></param>
    /// <param name="deviceExec"></param>
    /// <param name="deviceService"></param>
    public OpenApiService(SqlSugarRepository<OpenApiEntity> openApiEntity, DeviceHostedService deviceHostedService, DeviceExec deviceExec, DeviceService deviceService, ReadService readService)
    {
        _openApi = openApiEntity;
        _deviceHostedService = deviceHostedService;
        _deviceExec = deviceExec;
        _deviceService = deviceService;
        _readService = readService;
    }

    #region Web管理

    /// <summary>
    ///     开放Api-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/open/page")]
    public async Task<SqlSugarPagedList<OpenApiEntity>> OpenApiPage([FromQuery] OpenApiPageInput input)
    {
        var list = await _openApi.AsQueryable()
            .WhereIF(input.SearchValue.IsNotNull(),
                w => w.Name.Contains(input.SearchValue) || w.Token.Contains(input.SearchValue))
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return list;
    }

    /// <summary>
    ///     开放Api-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/open/detail")]
    [AllowAnonymous]
    public async Task<OpenApiEntity> OpenApiDetail([FromQuery] BaseId input)
    {
        var openApiEntity = await _openApi.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (openApiEntity == null)
            throw Oops.Oh("数据已经被删除!");
        return openApiEntity;
    }

    /// <summary>
    ///     开放Api--新增
    /// </summary>
    /// <returns></returns>
    [HttpPost("/open/add")]
    public async Task OpenApiEntityAdd(OpenApiEntityAddInput input)
    {
        var isExist = await _openApi.IsAnyAsync(u => u.Name == input.Name);
        if (isExist)
            throw Oops.Oh(ErrorCode.Com1004);
        var openApi = input.Adapt<OpenApiEntity>();
        await _openApi.InsertAsync(openApi);
    }

    /// <summary>
    ///     开放Api--修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/open/update")]
    public async Task OpenApiEntityUpdate(OpenApiEntityUpdateInput input)
    {
        var isExist = await _openApi.IsAnyAsync(u => u.Name == input.Name && u.Id != input.Id);
        if (isExist)
            throw Oops.Oh(ErrorCode.Com1004);

        var openApi = await _openApi.GetFirstAsync(f => f.Id == input.Id);
        if (openApi == null)
            throw Oops.Oh(ErrorCode.D1002);

        openApi = input.Adapt<OpenApiEntity>();
        await _openApi.AsSugarClient().Updateable(openApi).IgnoreColumns(w => w.CreatedTime).ExecuteCommandAsync();
    }

    /// <summary>
    ///     开放Api--删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/open/delete")]
    public async Task OpenApiEntityDelete(BaseId input)
    {
        var openApi = await _openApi.AsQueryable().Where(f => f.Id == input.Id)
            .Includes(w => w.OpenApiDetailEntity)
            .FirstAsync();
        if (openApi == null)
            throw Oops.Oh(ErrorCode.D1002);
        await _openApi.AsSugarClient().DeleteNav(openApi).Include(w => w.OpenApiDetailEntity)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     获取临时ToKen
    /// </summary>
    /// <param name="input"></param>
    /// <remarks>默认用户名/密码：admin/admin</remarks>
    /// <returns></returns>
    [HttpGet("/authorize/getToken")]
    [AllowAnonymous]
    public async Task<string> GetToken([FromQuery] GetTokenInput input)
    {
        if (input.Token.IsNotNull())
        {
            //Token认证
            var openApi = await _openApi.AsQueryable()
                .Where(f => f.AuthorizeType == AuthorizeTypeEnum.Token && f.Token == input.Token && f.Id == Convert.ToInt64(input.AppId))
                .Includes(w => w.OpenApiDetailEntity).FirstAsync();
            if (openApi == null)
                throw Oops.Oh("身份认证失败！");
        }
        else if (input.Sign.IsNotNull())
        {
            throw Oops.Oh("暂未实现!");
        }
        else
        {
            throw Oops.Oh("暂不支持!");
        }

        // 生成Token令牌
        var accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
        {
            {ClaimConst.RunMode, 2}
        }, 120);
        // 设置刷新Token令牌
        return accessToken;
    }

    #endregion

    #region Api文档

    /// <summary>
    ///     开放Api--授权Api列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/open/select")]
    public Task<Dictionary<string, List<string>>> OpenApiEntitySelect()
    {
        var resultData = new Dictionary<string, List<string>>();
        var assembly = Assembly.GetExecutingAssembly();
        var types = assembly.GetTypes().Where(w => w.IsPublic && !w.IsSealed && !w.IsAbstract).ToArray();

        foreach (var type in types)
        {
            var methods = type.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly)
                .Where(w => !w.IsSpecialName).ToArray();
            foreach (var method in methods)
            {
                var attribute = method.GetCustomAttribute<OpenApiAttribute>();
                if (attribute == null) continue;
                if (resultData.ContainsKey(attribute.Module))
                {
                    var data = resultData[attribute.Module];
                    data.Add(attribute.Name);
                    resultData[attribute.Module] = data;
                }
                else
                {
                    resultData.Add(attribute.Module, new List<string> {attribute.Name});
                }
            }
        }

        return Task.FromResult(resultData);
    }

    /// <summary>
    ///     开放Api--已经授权Api列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/open/grant")]
    public async Task<List<string>> OpenApiEntityGrant([FromQuery] BaseId input)
    {
        var output = new List<string>();
        var openApi = await _openApi.AsQueryable().Where(w => w.Id == input.Id).Includes(w => w.OpenApiDetailEntity)
            .FirstAsync();
        if (openApi == null)
            throw Oops.Bah("配置已经被删除！");
        foreach (var openApiDetailEntity in openApi.OpenApiDetailEntity)
        {
            if (!output.Contains(openApiDetailEntity.Name))
                output.Add(openApiDetailEntity.Name);
            if (!output.Contains(openApiDetailEntity.Module))
                output.Add(openApiDetailEntity.Module);
        }

        return output;
    }

    /// <summary>
    ///     开放Api--授权Api
    /// </summary>
    /// <returns></returns>
    [HttpPost("/open/authorization")]
    public async Task OpenApiAuthorization([FromBody] OpenApiAuthorizationInput input)
    {
        var openApi = await _openApi.AsQueryable().Where(w => w.Id == input.Id).Includes(w => w.OpenApiDetailEntity)
            .FirstAsync();
        if (openApi == null)
            throw Oops.Bah("配置已经被删除！");
        openApi.OpenApiDetailEntity.Clear();

        var assembly = Assembly.GetExecutingAssembly();
        var types = assembly.GetTypes().Where(w => w.IsPublic && !w.IsSealed && !w.IsAbstract).ToArray();

        foreach (var type in types)
        {
            var methods = type.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly)
                .Where(w => !w.IsSpecialName).ToArray();

            foreach (var method in methods)
                try
                {
                    var attribute = method.GetCustomAttribute<OpenApiAttribute>();

                    if (attribute == null || !input.Names.Contains(attribute.Name)) continue;

                    var openApiDetailEntity = new OpenApiDetailEntity
                    {
                        HttpRequestType = attribute.HttpRequestType,
                        ModuleCore = attribute.ModuleCore,
                        Module = attribute.Module,
                        Name = attribute.Name,
                        Params = new List<RequestParams>(),
                        OpenApiEntityId = input.Id,
                        Result = new List<ResultParams>()
                    };

                    // 获取请求参数
                    var parameters = method.GetParameters();
                    foreach (var parameter in parameters)
                    {
                        var properties = parameter.ParameterType.GetProperties();
                        foreach (var property in properties)
                        {
                            var propertyType = StringExtension.HandleGenericType(property.PropertyType);
                            var attribute2 = property.GetCustomAttribute(typeof(DescriptionAttribute)) as DescriptionAttribute;
                            var isRequired = property.GetCustomAttributes(typeof(RequiredAttribute), false).Length > 0;
                            openApiDetailEntity.Params.Add(new RequestParams
                            {
                                Name = property.Name,
                                Type = propertyType,
                                Remark = attribute2?.Description ?? "",
                                IsRequired = isRequired
                            });
                        }
                    }

                    // 是否是异步
                    var isAsync = method.GetCustomAttribute<AsyncMethodBuilderAttribute>() != null ||
                                  method.ToString().StartsWith(typeof(Task).FullName) ||
                                  method.ToString().StartsWith(typeof(ValueTask).FullName);

                    var returnType = method.ReturnType;
                    var finalType = GetFinalType(isAsync, returnType);

                    if (typeof(IEnumerable).IsAssignableFrom(finalType) && finalType != typeof(string))
                        AddResultParams(finalType.GenericTypeArguments[0], openApiDetailEntity.Result);
                    else
                        AddResultParams(finalType, openApiDetailEntity.Result);

                    // 获取 API 的 URL
                    GetApiUrl(method, openApiDetailEntity);

                    openApi.OpenApiDetailEntity.Add(openApiDetailEntity);
                }
                catch (Exception e)
                {
                    Log.Error("开放Api解析:" + e.Message);
                }
        }

        await _openApi.AsSugarClient().UpdateNav(openApi).Include(w => w.OpenApiDetailEntity).ExecuteCommandAsync();
    }

    #region 私有方法

    private Type GetFinalType(bool isAsync, Type returnType)
    {
        if (isAsync)
        {
            if (returnType == typeof(Task) || returnType == typeof(ValueTask))
                return typeof(void);
            if (returnType.IsGenericType) return returnType.GenericTypeArguments[0];
        }

        return returnType;
    }

    private void AddResultParams(Type type, List<ResultParams> resultParamsList)
    {
        var propertyInfoPs = type.GetProperties();
        foreach (var property in propertyInfoPs)
        {
            var attribute2 = property.GetCustomAttribute(typeof(DescriptionAttribute)) as DescriptionAttribute;
            var propertyType = StringExtension.HandleGenericType(property.PropertyType);
            resultParamsList.Add(new ResultParams
            {
                Name = property.Name,
                Type = propertyType,
                Remark = attribute2?.Description ?? ""
            });
        }
    }

    private void GetApiUrl(MethodInfo method, OpenApiDetailEntity openApiDetailEntity)
    {
        var route = method.GetCustomAttribute<HttpGetAttribute>();
        if (route != null)
        {
            openApiDetailEntity.Path = route.Template ?? "";
        }
        else
        {
            var route2 = method.GetCustomAttribute<HttpPostAttribute>();
            if (route2 != null) openApiDetailEntity.Path = route2.Template ?? "";
        }
    }

    #endregion

    /// <summary>
    ///     开放Api--获取授权模块
    /// </summary>
    /// <returns></returns>
    [HttpGet("/open/{id}/getModuleList")]
    [AllowAnonymous]
    public async Task<List<GetModuleListOutput>> GetModuleList(long id)
    {
        var openApi = await _openApi.AsQueryable().Where(w => w.Id == id).Includes(w => w.OpenApiDetailEntity).FirstAsync();
        if (openApi == null)
            throw Oops.Bah("配置已经被删除！");
        List<GetModuleListOutput> output = new();
        foreach (var openApiDetail in from openApiDetail in openApi.OpenApiDetailEntity
                 let cores = output.Select(s => s.Core)
                 where !cores.Contains(openApiDetail.ModuleCore)
                 select openApiDetail)
            output.Add(new GetModuleListOutput {Core = openApiDetail.ModuleCore, Module = openApiDetail.Module});

        return output;
    }

    /// <summary>
    ///     开放Api--获取api描述信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("/open/{id}/getApiDocs")]
    [AllowAnonymous]
    public async Task<GetApiDocsOutput> GetApiDocs(long id, string core)
    {
        var openApi = await _openApi.AsQueryable().Where(w => w.Id == id).Includes(w => w.OpenApiDetailEntity).FirstAsync();
        if (openApi == null)
            throw Oops.Bah("配置已经被删除！");
        var output = openApi.Adapt<GetApiDocsOutput>();
        output.Result ??= new List<ResultOutput>();
        output.Result = openApi.OpenApiDetailEntity.Where(w => w.ModuleCore == core).Adapt<List<ResultOutput>>();
        foreach (var openApiDetailEntity in output.Result)
            openApiDetailEntity.Params = openApiDetailEntity.Params.OrderByDescending(o => o.IsRequired).ThenBy(o => o.Name).ToList();
        return output;
    }

    #endregion

    #region 开放Api

    #region 设备管理

    /// <summary>
    ///     开放Api --查询设备
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/openapi/device/list")]
    [OpenApi(Name = "查询设备", Module = "设备管理", ModuleCore = "DeviceManagement")]
    [AllowAnonymous]
    public async Task<SqlSugarPagedList<QueryDeviceOutput>> QueryDevice([FromQuery] QueryDeviceInput input)
    {
        List<long> filterId = new();
        // 全部在线设备
        filterId.AddRange(_deviceHostedService.DeviceThreads.Values.Where(w => w.Driver.IsConnected).Select(s => s.Device.Id));
        var devices = await _openApi.AsSugarClient().Queryable<Device>()
            .WhereIF(input.Enable > 0, w => input.Enable == 1 ? w.Enable : !w.Enable)
            .WhereIF(input.Status == 1, w => filterId.Contains(w.Id) && w.Enable == true)
            .WhereIF(input.Status == 2, w => !filterId.Contains(w.Id) || w.Enable == false)
            .Includes(w => w.Driver)
            .Includes(w => w.DeviceConfigs)
            .Includes(w => w.DeviceVariable)
            .OrderBy(w => w.Index)
            .ToPagedListAsync(input.PageNo, input.PageSize);

        var deviceOutputs = devices.Adapt<SqlSugarPagedList<QueryDeviceOutput>>();
        foreach (var row in deviceOutputs.Rows)
        {
            var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.Id == row.Id);
            if (deviceThread != null)
                row.IsConnect = deviceThread.Driver.IsConnected;
            else
                row.NotRunning = true;

            var device = devices.Rows.FirstOrDefault(w => w.Id == row.Id);
            if (device != null)
            {
                row.VariableCount = device.DeviceVariable.Count;
                row.DriverName = device.Driver.DriverName;
            }

            if (row.DeviceConfigs.Select(s => s.DeviceConfigName).Contains("IpAddress"))
                //网口设备
                row.IpAddress = row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "IpAddress")!.Value + ":" +
                                row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "Port")!.Value;
            else if (row.DeviceConfigs.Select(s => s.DeviceConfigName).Contains("url"))
                //url 设备
                row.IpAddress = row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "url")!.Value;
            else if (row.DeviceConfigs.Select(s => s.DeviceConfigName).Contains("SerialNumber"))
                //串口设备
                row.IpAddress = row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "SerialNumber")!.Value;

            row.MinPeriod = row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "MinPeriod")!.Value + "ms";
            // row.ReConnTime = row.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == "ReConnTime")!.Value + "s";
        }

        return deviceOutputs;
    }

    /// <summary>
    ///     开放Api --查询设备属性
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/deviceVariable/list")]
    [OpenApi(Name = "查询设备属性", Module = "设备管理", ModuleCore = "DeviceManagement")]
    [AllowAnonymous]
    public async Task<SqlSugarPagedList<DeviceVariablePageOutput>> QueryDeviceVariable([FromQuery] QueryDeviceVariableInput input)
    {
        //1.为了支持实时读取状态查询,需要从实时里面查询到当前的状态
        var deviceValues = new List<string>();
        if (input.VariableStatus > 0)
        {
            var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.DeviceName == input.DeviceName);
            if (deviceThread != null)
                //GOOD
                deviceValues.AddRange(input.VariableStatus == 1
                    ? deviceThread.DeviceVariables.Where(w => w.Value.VariableStatus == VariableStatusTypeEnum.Good).Select(s => s.Key)
                    //Bad
                    : deviceThread.DeviceVariables.Where(w => w.Value.VariableStatus != VariableStatusTypeEnum.Good).Select(s => s.Key));
        }

        if (input.VariableStatus > 0 && !deviceValues.Any())
            return new SqlSugarPagedList<DeviceVariablePageOutput>();

        var deviceVariableList = await _openApi.AsSugarClient().Queryable<DeviceVariable>()
            .Where(w => w.Device.DeviceName == input.DeviceName)
            .WhereIF(input.ValueSource > 0, w => w.ValueSource == (ValueSourceEnum) input.ValueSource)
            .WhereIF(input.TransitionType > 0, w => w.TransitionType == (TransPondDataTypeEnum) input.TransitionType)
            .WhereIF(input.SendType > 0, w => w.SendType == (SendTypeEnum) input.SendType)
            .WhereIF(input.VariableStatus > 0 && deviceValues.Any(), w => deviceValues.Contains(w.Identifier))
            .WhereIF(!string.IsNullOrEmpty(input.Identifier), w => w.Identifier.Contains(input.Identifier))
            .WhereIF(!string.IsNullOrEmpty(input.Tag), w => SqlFunc.JsonLike(w.Tags, input.Tag))
            .WhereIF(!string.IsNullOrEmpty(input.Name), w => w.Identifier.Contains(input.Name))
            .WhereIF(!string.IsNullOrEmpty(input.RegisterAddress), w => SqlFunc.JsonLike(w.DeviceVariableEx, input.RegisterAddress))
            .OrderBy(u => u.Identifier)
            .Select<DeviceVariablePageOutput>()
            .ToPagedListAsync(input.PageNo, input.PageSize);

        return deviceVariableList;
    }

    /// <summary>
    ///     开放Api --设备属性下写
    /// </summary>
    /// <returns></returns>
    [HttpPost("/openapi/deviceVariable/write")]
    [OpenApi(Name = "设备属性下写(已过时)", Module = "设备管理", ModuleCore = "DeviceManagement", HttpRequestType = HttpRequestTypeEnum.Post)]
    [AllowAnonymous]
    public async Task<WriteResponse> WriteDataToDevice(WriteDataToDeviceInput input)
    {
        var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.DeviceName == input.DeviceName);
        if (deviceThread == null)
            throw Oops.Oh("设备连接已断开,请连接后重试！");

        var method = deviceThread.Methods.FirstOrDefault(x => x.Name == "WriteAsync");
        if (method == null)
            throw Oops.Oh("设备暂不支持,敬请期待！");

        if (deviceThread.Device.Driver.DriverType == DriverTypeEnum.Cnc)
            throw Oops.Oh("CNC类型设备暂时不支持写入调试！");

        if (!deviceThread.DeviceVariableSource.ContainsKey(input.Identifier))
            return new WriteResponse {IsSuccess = false, Description = $"属性标识符【{input.Identifier}】未匹配成功,请确认设备是否添加该标识符！"};

        var val = deviceThread.DeviceVariableSource[input.Identifier];

        var req = new DriverAddressIoArgModel(0L, val.DeviceVariableEx.RegisterAddress, 0, val.DeviceVariableEx.DataType, input.Value, encoding: val.DeviceVariableEx.Encoding);
        var ret = await ((Task<WriteResponse>) method.Invoke(deviceThread.Driver, new object[]
        {
            req
        }))!;
        return ret;
    }

    /// <summary>
    ///     开放Api --设备属性批量下写
    /// </summary>
    /// <returns></returns>
    [HttpPost("/openapi/deviceVariable/batch/write")]
    [OpenApi(Name = "设备属性批量下写", Module = "设备管理", ModuleCore = "DeviceManagement", HttpRequestType = HttpRequestTypeEnum.Post)]
    [AllowAnonymous]
    public async Task<List<BatchWriteDataToDeviceOutput>> BatchWriteDataToDevice(BatchWriteDataToDeviceInput input)
    {
        var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.DeviceName == input.DeviceName);
        if (deviceThread == null)
            throw Oops.Oh("设备连接已断开,请连接后重试！");

        var method = deviceThread.Methods.FirstOrDefault(x => x.Name == "WriteAsync");
        if (method == null)
            throw Oops.Oh("设备暂不支持,敬请期待！");

        if (deviceThread.Device.Driver.DriverType == DriverTypeEnum.Cnc)
            throw Oops.Oh("CNC类型设备暂时不支持写入调试！");

        var output = new List<BatchWriteDataToDeviceOutput>();
        var dicKey = input.Identifiers.Select(s => s.Key).ToList();
        // 采集点位
        var variableList = deviceThread.Device.DeviceVariable.Where(w => dicKey.Contains(w.Identifier)).ToList();

        foreach (var (identifier, value) in input.Identifiers)
        {
            var variable = variableList.FirstOrDefault(f => f.Identifier == identifier);
            if (variable == null)
            {
                output.Add(new BatchWriteDataToDeviceOutput
                {
                    IsSuccess = false,
                    Description = $"属性标识符【{identifier}】未匹配成功,请确认设备是否添加该标识符！",
                    Identifier = identifier
                });
                continue;
            }

            switch (variable.ValueSource)
            {
                case ValueSourceEnum.Get:
                {
                    variableList.Remove(variable);
                    var req = new DriverAddressIoArgModel(0L, variable.DeviceVariableEx.RegisterAddress, 0, variable.DeviceVariableEx.DataType, value.ToString() ?? string.Empty,
                        encoding: variable.DeviceVariableEx.Encoding);
                    var ret = await ((Task<WriteResponse>) method.Invoke(deviceThread.Driver, new object[]
                    {
                        req
                    }))!;
                    output.Add(new BatchWriteDataToDeviceOutput
                    {
                        IsSuccess = ret.IsSuccess,
                        Description = ret.Description,
                        Identifier = identifier
                    });
                    break;
                }
                // 值相同就不改变
                case ValueSourceEnum.Static when variable.DefaultValue == value.ToString():
                    variableList.Remove(variable);
                    continue;
                // 改变默认值
                case ValueSourceEnum.Static:
                {
                    variable.DefaultValue = value.ToString();

                    output.Add(new BatchWriteDataToDeviceOutput
                    {
                        IsSuccess = true,
                        Description = "",
                        Identifier = identifier
                    });
                    // 实时采集当前属性是否存在
                    if (!deviceThread.DeviceVariableSource.ContainsKey(identifier))
                        // 不存在就新增进去
                        deviceThread.DeviceVariableSource.TryAdd(identifier, variable);
                    else
                        // 修改默认值
                        deviceThread.DeviceVariableSource[identifier].DefaultValue = value.ToString();
                    break;
                }
            }
        }

        if (variableList.Any())
            await _openApi.AsSugarClient().Updateable(variableList).UpdateColumns(w => w.DefaultValue).ExecuteCommandAsync();
        return output;
    }

    /// <summary>
    ///     开放Api --获取实时属性采集信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/openapi/deviceVariable/realTime")]
    [OpenApi(Name = "获取实时属性采集信息", Module = "设备管理", ModuleCore = "DeviceManagement")]
    [AllowAnonymous]
    public Task<List<GetRealTimeAttributeCollectionInfoOutput>> GetRealTimeAttributeCollectionInfo([FromQuery] GetRealTimeAttributeCollectionInfoInput input)
    {
        var deviceThread = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.DeviceName == input.DeviceName);
        var sourceData = deviceThread == null
            ? new ConcurrentDictionary<string, DriverReturnValueModel>(DataStorage.Instance.All().Where(kv => kv.Key.Contains(input.DeviceName))
                .ToDictionary(s => s.Key, s => s.Value.Adapt<DriverReturnValueModel>()))
            : deviceThread.DeviceVariables;

        //设备关机
        if (input.Identifier.Any())
        {
            var keyList = input.Identifier.Select(identifier => deviceThread != null ? identifier : input.DeviceName + "." + identifier).ToList();
            var query = sourceData
                .Where(kv => keyList.Contains(kv.Key))
                .Select(s => new GetRealTimeAttributeCollectionInfoOutput
                {
                    Identifier = s.Key.Replace($"{input.DeviceName}.", ""),
                    Value = s.Value.Value,
                    ReadTime = s.Value.ReadTime
                })
                .ToList();
            return Task.FromResult(query);
        }
        else
        {
            var query = sourceData
                .Select(s => new GetRealTimeAttributeCollectionInfoOutput
                {
                    Identifier = s.Key.Replace($"{input.DeviceName}.", ""),
                    Value = s.Value.Value,
                    ReadTime = s.Value.ReadTime
                })
                .ToList();
            return Task.FromResult(query);
        }
    }

    /// <summary>
    ///     开放Api --采集设备连接状态
    /// </summary>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpGet("/openapi/device/connect")]
    [OpenApi(Name = "采集设备连接状态", Module = "设备管理", ModuleCore = "DeviceManagement")]
    [AllowAnonymous]
    public Task<bool> CollectDeviceConnectionStatus([FromQuery] CollectDeviceConnectionStatusInput input)
    {
        var status = _deviceHostedService.DeviceConnectStatus(input.DeviceName);
        if (status || input.Duration <= 0) return Task.FromResult(status);
        var deviceStatus = _deviceHostedService.DeviceThreads.Values.FirstOrDefault(x => x.Device.DeviceName == input.DeviceName)?.DeviceStatus;
        if (deviceStatus == null)
            return Task.FromResult(false);
        var durationTime = Convert.ToInt64((DateTime.Now() - Convert.ToDateTime(deviceStatus.DeviceStatusChangeTime)).TotalMilliseconds);
        return Task.FromResult(durationTime <= input.Duration * 1000);
    }

    #endregion

    #region Dnc管理

    /// <summary>
    ///     读取设备程序底层路径信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/openapi/deviceFile/routeInfo")]
    [OpenApi(Name = "读取设备程序底层路径信息", Module = "Dnc管理", ModuleCore = "DncManagement", HttpRequestType = HttpRequestTypeEnum.Get)]
    [AllowAnonymous]
    public async Task<dynamic> DeviceFileRouteInfo([FromQuery] OpenApiDeviceFileRouteInfoInput input)
    {
        return await _deviceService.DeviceFileRouteInfo(input);
    }

    /// <summary>
    ///     读取设备程序底层文件内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/openapi/deviceFile/read")]
    [OpenApi(Name = " 读取设备程序文件内容", Module = "Dnc管理", ModuleCore = "DncManagement", HttpRequestType = HttpRequestTypeEnum.Post)]
    [AllowAnonymous]
    public async Task<string> DeviceFileRead(OpenApiFileReadInput input)
    {
        return await _deviceService.DeviceFileRead(input);
    }

    /// <summary>
    ///     删除设备程序底层文件
    /// </summary>
    /// <param name="input"></param>
    /// <param name="AppId"></param>
    /// <returns></returns>
    [HttpPost("/openapi/deviceFile/del")]
    [OpenApi(Name = " 删除设备程序文件", Module = "Dnc管理", ModuleCore = "DncManagement", HttpRequestType = HttpRequestTypeEnum.Post)]
    [AllowAnonymous]
    public async Task<bool> DeviceFileDel(OpenApiFileDelInput input)
    {
        return await _deviceService.DeviceFileDel(input);
    }

    /// <summary>
    ///     写入设备程序底层文件内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/openapi/deviceFile/write")]
    [OpenApi(Name = " 写入设备程序文件内容", Module = "Dnc管理", ModuleCore = "DncManagement", HttpRequestType = HttpRequestTypeEnum.Post)]
    [AllowAnonymous]
    public async Task<bool> DeviceFileWrite(OpenApiFileWriteInput input)
    {
        return await _deviceService.DeviceFileWrite(new FileWriteByFileInput
        {
            DeviceName = input.DeviceName,
            FileName = input.FileName,
            Path = input.Path
        });
    }

    /// <summary>
    ///     设置为主程序
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/openapi/deviceFile/setCurrentProgram")]
    [OpenApi(Name = " 设置为主程序", Module = "Dnc管理", ModuleCore = "DncManagement", HttpRequestType = HttpRequestTypeEnum.Post)]
    [AllowAnonymous]
    public async Task<bool> DeviceFileSetCurrentProgram(SetCurrentProgramInput input)
    {
        return await _deviceService.DeviceFileSetCurrentProgram(input);
    }

    #endregion

    #region 实时数据查询

    /// <summary>
    ///     查询指定设备的实时数据
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/realtime/device/online")]
    [OpenApi(Name = "查询指定设备的实时数据", Module = "实时数据查询", ModuleCore = "QueryRealtimeData")]
    [AllowAnonymous]
    public Task<dynamic> GetRealtimeDataOfInstance([FromQuery] GetRealtimeDataOfInstanceInput input, [Required] [Description("应用Id")] long appId)
    {
        return _readService.GetRealtimeDataOfInstance(input);
    }

    #endregion

    #region 历史数据查询

    /// <summary>
    ///     查询设备的时间窗口数据
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/historian/device/window")]
    [OpenApi(Name = "查询设备的时间窗口数据", Module = "历史数据查询", ModuleCore = "QueryHistoricalData")]
    [AllowAnonymous]
    public Task<dynamic> GetTimeWindowDataOfModelInstances([FromQuery] GetTimeWindowDataOfModelInstancesInput input, [Required] [Description("应用Id")] long appId)
    {
        return _readService.GetTimeWindowDataOfModelInstances(input);
    }

    /// <summary>
    ///     查询指定设备的时间聚集数据
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/historian/device/aggregation")]
    [OpenApi(Name = "查询指定设备的时间聚集数据", Module = "历史数据查询", ModuleCore = "QueryHistoricalData")]
    [AllowAnonymous]
    public Task<dynamic> GetAggregatedDataOfInstance([FromQuery] GetAggregatedDataOfInstanceInput input, [Required] [Description("应用Id")] long appId)
    {
        return _readService.GetAggregatedDataOfInstance(input);
    }

    /// <summary>
    ///     查询指定设备的历史数据
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/historian/device/historical")]
    [OpenApi(Name = "查询指定设备的历史数据", Module = "历史数据查询", ModuleCore = "QueryHistoricalData")]
    [AllowAnonymous]
    public Task<dynamic> GetHistoricalDataOfInstance([FromQuery] GetHistoricalDataOfInstanceInput input, [Required] [Description("应用Id")] long appId)
    {
        return _readService.GetHistoricalDataOfInstance(input);
    }

    #endregion

    #endregion
}