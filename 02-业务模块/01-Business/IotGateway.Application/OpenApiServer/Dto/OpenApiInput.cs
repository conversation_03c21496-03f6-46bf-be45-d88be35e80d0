using DateTime = System.DateTime;

namespace IotGateway.Application.OpenApiServer.Dto;

/// <summary>
///     开放Api 返回
/// </summary>
public class OpenApiBaseOutput
{
    /// <summary>
    ///     唯一主键
    /// </summary>
    [Description("唯一主键")]
    public long Id { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [Description("创建时间")]
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    [Description("更新时间")]
    public DateTime? UpdatedTime { get; set; }
}

/// <summary>
/// 开放Api公共参数
/// </summary>
public class OpenApiBaseInput
{
    /// <summary>
    /// 应用Id
    /// </summary>
    [Required]
    [Description("应用Id")]
    public long AppId { get; set; }
}

/// <summary>
///     开放Api-分页请求基类参数
/// </summary>
public class OpenApiBasePageInput : OpenApiBaseInput
{
    /// <summary>
    /// </summary>
    public OpenApiBasePageInput()
    {
        PageNo = 1;
        PageSize = 20;
    }

    /// <summary>
    ///     当前页码
    /// </summary>
    [Description("当前页码")]
    public int PageNo { get; set; }

    /// <summary>
    ///     页码容量
    /// </summary>
    [Description("页码容量")]
    public int PageSize { get; set; }
}

/// <summary>
///     开放Api --查询设备输入参数
/// </summary>
public class QueryDeviceInput : OpenApiBasePageInput
{
    /// <summary>
    ///     设备运行状态:0:全部；1:在线；2:离线
    /// </summary>
    [Description("设备运行状态:0:全部；1:在线；2:离线")]
    public short Status { get; set; }

    /// <summary>
    ///     0全部,1启用,2禁用
    /// </summary>
    [Description("0全部,1启用,2禁用")]
    public short Enable { get; set; }
}

/// <summary>
///     开放Api --查询设备返回参数
/// </summary>
public class QueryDeviceOutput : OpenApiBaseOutput
{
    /// <summary>
    ///     名称
    /// </summary>
    [Description("名称")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    [Description("排序")]
    public uint Index { get; set; }

    /// <summary>
    ///     设备别名
    /// </summary>
    [Description("设备别名")]
    public string OtherName { get; set; }

    /// <summary>
    /// 采集协议名称
    /// </summary>
    [Description("采集协议名称")]
    public string DriverName { get; set; }

    /// <summary>
    ///     采集间隔ms
    /// </summary>
    [Description("采集间隔ms")]
    public string MinPeriod { get; set; }

    /// <summary>
    ///     连接地址
    /// </summary>
    [Description("连接地址")]
    public string IpAddress { get; set; }

    /// <summary>
    ///     设备配置
    /// </summary>
    [JsonIgnore]
    public List<DeviceConfig> DeviceConfigs { get; set; }

    /// <summary>
    /// </summary>
    [JsonIgnore]
    public long DriverId { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    [Description("是否启用")]
    public bool Enable { get; set; }

    /// <summary>
    ///     是否连接
    /// </summary>
    [Description("是否连接")]
    public bool IsConnect { get; set; }

    /// <summary>
    ///     没有运行 == true 显示启动运行,== false  显示停止运行
    /// </summary>
    [Description("是否运行")]
    public bool NotRunning { get; set; }

    /// <summary>
    ///     采集点数量
    /// </summary>
    [Description("采集点数量")]
    public int VariableCount { get; set; }
}

/// <summary>
///     开放Api --查询设备属性输入参数
/// </summary>
public class QueryDeviceVariableInput : OpenApiBasePageInput
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [Required]
    [Description("设备名称")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     转换数据类型：1.Bool；2.Int；3.Double；4.String;5.Bytes
    /// </summary>
    [Description("转换数据类型：1.Bool；2.Int；3.Double；4.String;5.Bytes")]
    public int TransitionType { get; set; }

    /// <summary>
    ///     数据来源：1直接取值；2计算赋值；3手动写值
    /// </summary>
    [Description("数据来源：1直接取值；2计算赋值；3手动写值")]
    public int ValueSource { get; set; }

    /// <summary>
    ///     读取状态：1 Good,其他 Bad
    /// </summary>
    [Description("读取状态：1.Good;其他.Bad")]
    public int VariableStatus { get; set; }

    /// <summary>
    ///     上报方式;1:总是上报;2:从不上报;3:变化上报
    /// </summary>
    [Description("上报方式;1:总是上报;2:从不上报;3:变化上报")]
    public int SendType { get; set; }

    /// <summary>
    ///     标识符
    /// </summary>
    [Description("标识符")]
    public string Identifier { get; set; }
    
    /// <summary>
    ///     标签
    /// </summary>
    [Description("标签")]
    public string Tag { get; set; }
    /// <summary>
    ///     属性名称
    /// </summary>
    [Description("属性名称")]
    public string Name { get; set; }
    
    /// <summary>
    ///     属性读取地址
    /// </summary>
    [Description("属性读取地址")]
    public string RegisterAddress { get; set; }
    
}


/// <summary>
/// 开放Api --设备属性下写输入参数
/// </summary>
public class WriteDataToDeviceInput  : OpenApiBaseInput
{
    /// <summary>
    /// 设备名称
    /// </summary>
    [Description("设备名称")]
    [Required]
    public string DeviceName { get; set; }
    
    /// <summary>
    /// 设备属性标识
    /// </summary>
    [Description("设备属性标识")]
    [Required]
    public string Identifier { get; set; }
    
    /// <summary>
    /// 写入值
    /// </summary>
    [Description("写入值")]
    [Required]
    public string Value { get; set; }
}
/// <summary>
/// 开放Api --设备属性批量下写输入参数
/// </summary>
public class BatchWriteDataToDeviceInput : OpenApiBaseInput
{
    /// <summary>
    /// 设备名称
    /// </summary>
    [Description("设备名称")]
    [Required]
    public string DeviceName { get; set; }
    
    /// <summary>
    /// 设备属性标识集合
    /// </summary>
    [Description("设备属性标识集合")]
    [Required]
    public Dictionary<string,object> Identifiers { get; set; }
}
/// <summary>
/// 开放Api --设备属性批量下写返回结果
/// </summary>
public class BatchWriteDataToDeviceOutput
{
    /// <summary>
    ///     属性标识
    /// </summary>
    [Description("属性标识")]
    public string Identifier { get; set; }
    
    /// <summary>
    ///     操作结果
    /// </summary>
    [Description("操作结果")]
    public bool IsSuccess { get; set; }

    /// <summary>
    ///     返回消息
    /// </summary>
    [Description("返回消息")]
    public string Description { get; set; }
}

/// <summary>
/// 开放Api --获取实时属性采集信息输入参数
/// </summary>
public class GetRealTimeAttributeCollectionInfoInput :OpenApiBaseInput
{
    /// <summary>
    /// 设备名称
    /// </summary>
    [Description("设备名称")]
    [Required]
    public string DeviceName { get; set; }

    /// <summary>
    /// 设备属性标识,不填默认返回全部
    /// </summary>
    [Description("设备属性标识,不填默认返回全部")]
    public List<string> Identifier { get; set; } = new();
}

/// <summary>
/// 开放Api --获取实时属性采集信息返回参数
/// </summary>
public class GetRealTimeAttributeCollectionInfoOutput
{
    /// <summary>
    /// 属性标识符
    /// </summary>
    [Description("属性标识符")]
    public string Identifier { get; set; }
    
    /// <summary>
    /// 值
    /// </summary>
    [Description("值")]
    public object Value { get; set; }
    
    /// <summary>
    /// 读取时间戳（毫秒）
    /// </summary>
    [Description("读取时间戳（毫秒）")]
    public long ReadTime { get; set; }
}

/// <summary>
/// 开放Api --采集设备连接状态输入参数
/// </summary>
public class CollectDeviceConnectionStatusInput : OpenApiBaseInput
{
    /// <summary>
    /// 设备名称
    /// </summary>
    [Description("设备名称")]
    [Required]
    public string DeviceName { get; set; }

    /// <summary>
    /// 状态持续时间(秒)：例：设置60s 当前状态持续超过60s才算有效，防止网络差的设备无法准备判断设备状态
    /// </summary>
    [Description("状态持续时间(秒)：例：设置60s 当前状态持续超过60s才算有效，防止网络差的设备无法准备判断设备状态")]
    public int Duration { get; set; }
}