using Furion.Schedule;
using IotGateway.Application.Entity;
using IotGateway.Engine;
using Console = System.Console;

namespace IotGateway.Application.EdgeComputingServer;

/// <summary>
///     任务中心
/// </summary>
public class ScriptExecutionSingleton : ISingleton
{
    /// <summary>
    /// </summary>
    private readonly EngineSingletonService _engine;
    /// <summary>
    /// 
    /// </summary>
    private readonly ISqlSugarClient _db;
    private readonly SingletonService _singletonService;
    private readonly ISchedulerFactory _schedulerFactory;
    private readonly IEventBusFactory _eventBusFactory;

    /// <summary>
    ///     定时任务配置中心
    /// </summary>
    public readonly ConcurrentDictionary<long, ScriptExecute> ScriptExecuteThreads = new();

    public ScriptExecutionSingleton(EngineSingletonService engine, ISqlSugarClient db, SingletonService singletonService, ISchedulerFactory schedulerFactory, IEventBusFactory eventBusFactory)
    {
        _engine = engine;
        _db = db;
        _singletonService = singletonService;
        _schedulerFactory = schedulerFactory;
        _eventBusFactory = eventBusFactory;
    }

    /// <summary>
    ///     边缘计算-初始化任务
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task Initialize()
    {
        var strategyList = await _db.CopyNew().Queryable<ScriptExecutionStrategy>()
            .Where(w => w.Enable)
            .Includes(w => w.EdgeComputingScript)
            .ToListAsync();
        Log.Information($"边缘计算策略初始化任务数：{strategyList.Count}");
        foreach (var strategy in strategyList.Where(w => w.EdgeComputingScript.Enable && w.Enable))
        {
            try
            {
                Log.Information($"边缘计算策略初始化任务：{strategy.EdgeComputingScript.ScriptName}");
                ScriptExecuteThreads.TryAdd(strategy.Id, new ScriptExecute(_db, _schedulerFactory, _eventBusFactory, _singletonService, _engine)
                {
                    Strategy = strategy
                });
            }
            catch (Exception e)
            {
                Log.Error($"边缘计算策略Error：{e.Message}");
            }
        }

        foreach (var (_, scriptExecuteThread) in ScriptExecuteThreads)
            ThreadPool.QueueUserWorkItem(_ => scriptExecuteThread?.Start());
    }

    /// <summary>
    ///     新增任务
    /// </summary>
    public void Add(ScriptExecutionStrategy strategy)
    {
        // 检查是否运行
        Stop(strategy.Id);
        // 
        var scriptExecute = new ScriptExecute(_db, _schedulerFactory, _eventBusFactory, _singletonService, _engine) {Strategy = strategy};
        ScriptExecuteThreads.TryAdd(strategy.Id, scriptExecute);
        ThreadPool.QueueUserWorkItem(_ => scriptExecute?.Start());
    }

    /// <summary>
    ///     新增任务
    /// </summary>
    public void Add(List<ScriptExecutionStrategy> strategyList)
    {
        foreach (var strategy in strategyList)
            Add(strategy);
    }

    /// <summary>
    ///     停止线程
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public void Stop(long id)
    {
        if (!ScriptExecuteThreads.TryGetValue(id, out var thread)) return;
        thread?.Dispose();
        ScriptExecuteThreads.TryRemove(id, out _);
    }

    /// <summary>
    ///     停止线程
    /// </summary>
    /// <param name="strategyList"></param>
    /// <returns></returns>
    public void Stop(List<ScriptExecutionStrategy> strategyList)
    {
        foreach (var strategy in strategyList)
            Stop(strategy.Id);
    }
}