using IotGateway.Application.EdgeComputingServer.Dtos;
using IotGateway.Application.Entity;
using DateTime = System.DateTime;

namespace IotGateway.Application.EdgeComputingServer;

/// <summary>
///     边缘计算-脚本
///     版 本:V3.1.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-06-19
/// </summary>
[ApiDescriptionSettings("边缘计算")]
public class EdgeComputingScriptService : ITransient, IDynamicApiController
{
    private readonly SqlSugarRepository<EdgeComputingScript> _edgeComputing;
    private readonly ScriptExecutionStrategyService _executionStrategy;
    private readonly ScriptExecutionSingleton _scriptExec;

    public EdgeComputingScriptService(SqlSugarRepository<EdgeComputingScript> edgeComputing, ScriptExecutionStrategyService executionStrategy, ScriptExecutionSingleton scriptExec)
    {
        _edgeComputing = edgeComputing;
        _executionStrategy = executionStrategy;
        _scriptExec = scriptExec;
    }

    /// <summary>
    ///     边缘计算-脚本列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeComputing/script/page")]
    public async Task<SqlSugarPagedList<EdgeComputingScript>> Page([FromQuery] BasePageInput input)
    {
        var edgeComputingScriptList = await _edgeComputing.AsQueryable()
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue), w => w.ScriptName.Contains(input.SearchValue)
                                                                    || w.Describe.Contains(input.SearchValue))
            .OrderBy(u => u.ScriptName)
            .ToPagedListAsync(input.PageNo, input.PageSize);

        return edgeComputingScriptList;
    }

    /// <summary>
    ///     边缘计算-脚本详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeComputing/script/detail")]
    public async Task<EdgeComputingScript> Detail([FromQuery] BaseId input)
    {
        var edgeComputingScript = await _edgeComputing.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (edgeComputingScript == null)
            throw Oops.Oh(ErrorCode.D1002);
        return edgeComputingScript;
    }

    /// <summary>
    ///     边缘计算-脚本下拉
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeComputing/script/select")]
    public async Task<List<EdgeComputingScriptSelectOutput>> Select()
    {
        var edgeComputingScriptList = await _edgeComputing.AsQueryable()
            .Where(w => w.Enable)
            .OrderBy(u => u.ScriptName)
            .Select<EdgeComputingScriptSelectOutput>()
            .ToListAsync();
        return edgeComputingScriptList;
    }

    /// <summary>
    ///     边缘计算-脚本-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/script/add")]
    public async Task Add(EdgeComputingScriptAdd input)
    {
        if (await _edgeComputing.IsAnyAsync(a => a.ScriptName == input.ScriptName))
            throw Oops.Oh($"脚本名称:【{input.ScriptName}】,已存在！");
        var edgeComputingScript = input.Adapt<EdgeComputingScript>();
        await _edgeComputing.InsertAsync(edgeComputingScript);
    }

    /// <summary>
    ///     边缘计算-脚本-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/script/update")]
    public async Task Update(EdgeComputingScriptUpdate input)
    {
        if (await _edgeComputing.IsAnyAsync(a => a.ScriptName == input.ScriptName && a.Id != input.Id))
            throw Oops.Oh($"脚本名称:【{input.ScriptName}】,已存在！");
        var script = await _edgeComputing.AsQueryable().Where(f => f.Id == input.Id)
            .Includes(w => w.ScriptExecutionStrategy, w => w.EdgeComputingScript).FirstAsync();
        if (script == null) throw Oops.Oh(ErrorCode.D1002);

        script.Describe = input.Describe;
        script.ScriptName = input.ScriptName;

        //如果脚本内容发生改变,将策略状态改成未同步的
        if (script.Content != input.Content)
        {
            script.Content = input.Content;
            foreach (var scriptExecutionStrategy in script.ScriptExecutionStrategy)
                scriptExecutionStrategy.EdgeComputingScript = script;
            await _executionStrategy.Sync(script.ScriptExecutionStrategy);
        }

        await _edgeComputing.AsSugarClient().UpdateNav(script).Include(w => w.ScriptExecutionStrategy).ExecuteCommandAsync();
    }

    /// <summary>
    ///     边缘计算-脚本-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/script/delete")]
    public async Task Delete(BaseId<List<long>> input)
    {
        var scriptList = await _edgeComputing.AsQueryable().Where(f => input.Id.Contains(f.Id))
            .Includes(w => w.ScriptExecutionStrategy).ToListAsync();
        foreach (var script in scriptList.Where(script => script.ScriptExecutionStrategy.Any()))
            throw Oops.Oh($"【{script.ScriptName}】 正在使用中,请先删除对应策略");
        await _edgeComputing.DeleteAsync(scriptList);
    }

    /// <summary>
    ///     边缘计算-脚本-启用/禁用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/script/enable")]
    
    public async Task<bool> Enable(EnableInput<List<long>> input)
    {
        var scriptList = await _edgeComputing.AsQueryable().Where(w => input.Id.Contains(w.Id))
            .Includes(w => w.ScriptExecutionStrategy, w => w.EdgeComputingScript)
            .ToListAsync();
        foreach (var script in scriptList)
        {
            script.Enable = input.Enable;

            if (script.Enable)
                _scriptExec.Add(script.ScriptExecutionStrategy);
            else
                _scriptExec.Stop(script.ScriptExecutionStrategy);
        }

        await _edgeComputing.AsUpdateable(scriptList).UpdateColumns(w => w.Enable).ExecuteCommandAsync();
        return true;
    }

    /// <summary>
    ///     导入边缘计算-脚本
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/script/inPort")]
    
    public async Task<DataInPortOutput> InPort([FromForm] ComputingScriptInPort input)
    {
        var stream = new MemoryStream();
        await input.File.CopyToAsync(stream);
        // 返回结果
        var result = new DataInPortOutput {ErrorColumn = new List<ErrorColumn>()};
        // 需要新增的属性
        var computingScriptList = new List<EdgeComputingScript>();
        // 当前所在行
        var line = 2;
        foreach (var computingScriptInPort in await stream.QueryAsync<ComputingScriptInPortDto>())
            try
            {
                //检查必填项
                var success = CheckInPortData(computingScriptInPort, line, result);
                if (!success)
                    continue;
                var computingScript = computingScriptInPort.Adapt<EdgeComputingScript>();
                computingScript.Id = YitIdHelper.NextId();
                computingScript.CreatedTime = DateTime.Now;
                computingScriptList.Add(computingScript);
                line++;
            }
            catch (Exception e)
            {
                result.ErrorCount += 1;
                result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 Error:【{e.Message}】 "});
            }

        // 查找出来相同名称脚本
        var computingScriptAny = await _edgeComputing.AsQueryable()
            .Where(w => computingScriptList.Select(s => s.ScriptName).Contains(w.ScriptName))
            .ToListAsync();

        foreach (var computingScript in computingScriptAny)
        {
            var deviceVariableVal = computingScriptList.FirstOrDefault(f => f.ScriptName == computingScript.ScriptName);
            if (deviceVariableVal == null)
                continue;
            if (input.InPortType == DeviceVariableInPortTypeEnum.OverLook)
            {
                computingScriptList.Remove(deviceVariableVal);
            }
            else
            {
                //覆盖原始数据
                //1.覆盖属性
                //1.1 移除对象,修改Id后重新加入
                computingScriptList.Remove(deviceVariableVal);
                deviceVariableVal.Id = computingScript.Id;
                computingScriptList.Add(deviceVariableVal);
            }
        }

        if (computingScriptList.Any())
            await _edgeComputing.AsSugarClient().Storageable(computingScriptList).ExecuteCommandAsync();
        result.SuccessCount = line - 1 - result.ErrorCount < 0 ? 0 : line - 2 - result.ErrorCount;
        return result;
    }

    /// <summary>
    ///     导出边缘计算-脚本
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/script/exPort")]
    public async Task<IActionResult> ExPort(BaseId<List<long>> input)
    {
        var computingScriptList = await _edgeComputing.AsQueryable().Where(f => input.Id.Contains(f.Id)).ToListAsync();

        var value = new
        {
            script = computingScriptList.Select(s => new
            {
                s.ScriptName,
                s.Content,
                s.Describe,
                s.Enable
            })
        };

        try
        {
            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsByTemplateAsync("Templates/EdgeComputingScriptImport.xlsx", value);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            {
                FileDownloadName = $"{DateTime.Now:yyyyMMddHHmmssfff}.xlsx"
            };
        }
        catch (Exception e)
        {
            throw Oops.Bah($"导出失败：{e.Message}");
        }
    }

    /// <summary>
    ///     检查属性导入必填项
    /// </summary>
    private bool CheckInPortData(ComputingScriptInPortDto deviceVariableExPort, int line, DataInPortOutput result)
    {
        if (deviceVariableExPort.ScriptName.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 【ScriptName】 不能是空！ ", Line = line});
            return false;
        }

        if (deviceVariableExPort.Content.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"第【{line}】行 【Content】 不能是空！ ", Line = line});
            return false;
        }

        return true;
    }
}