using Furion.Schedule;
using IotGateway.Application.EdgeComputingServer.Dtos;
using IotGateway.Application.Entity;
using IotGateway.Engine;
using IDisposable = System.IDisposable;

namespace IotGateway.Application.EdgeComputingServer;

/// <summary>
///     边缘计算-执行策略
/// </summary>
public class ScriptExecute : IDisposable
{
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private readonly SingletonService _singletonService;
    private readonly ISqlSugarClient _db;
    private readonly ISchedulerFactory _schedulerFactory;
    private readonly IEventBusFactory _eventBusFactory;
    private readonly EngineSingletonService _engine;
    private ScriptExecuteTaskModel _scriptExecuteTask;
    public ScriptExecutionStrategy Strategy { get; set; }

    /// <summary>
    /// </summary>
    /// <param name="db"></param>
    /// <param name="schedulerFactory"></param>
    /// <param name="eventBusFactory"></param>
    /// <param name="singletonService"></param>
    /// <param name="engine"></param>
    public ScriptExecute(ISqlSugarClient db, ISchedulerFactory schedulerFactory, IEventBusFactory eventBusFactory, SingletonService singletonService, EngineSingletonService engine)
    {
        _singletonService = singletonService;
        _engine = engine;
        _db = db;
        _schedulerFactory = schedulerFactory;
        _eventBusFactory = eventBusFactory;
    }

    /// <summary>
    ///     启动订阅该设备的数据
    /// </summary>
    public async Task Start()
    {
        var cancellationToken = _cancellationTokenSource.Token;
        switch (Strategy.ExecutionStrategyType)
        {
            case ExecutionStrategyTypeEnum.网关启动触发:
                await ExecuteScript(Strategy.Id, Strategy.EdgeComputingScript.Content);
                break;
            case ExecutionStrategyTypeEnum.定时触发:
            {
                await TimerTaskAdd();
                break;
            }
            case ExecutionStrategyTypeEnum.属性触发:
            {
                var tagModel = JSON.Deserialize<PropertyModel>(Strategy.Config);
                foreach (var code in tagModel.Name)
                {
                    _scriptExecuteTask = new ScriptExecuteTaskModel
                    {
                        Content = Strategy.EdgeComputingScript.Content,
                        Compare = tagModel.Compare,
                        FirstEven = tagModel.FirstEven,
                        Code = code,
                        Value = tagModel.Value,
                        Id = Strategy.Id
                    };
                    switch (_scriptExecuteTask.Compare)
                    {
                        case CompareEnum.值变化时执行:
                            DataStorage.Instance.SubscribeValueChangedEvent(_scriptExecuteTask.Code, ValueChangedHandler);
                            break;
                        case CompareEnum.时间变化时执行:
                            DataStorage.Instance.SubscribeTimeChangedEvent(_scriptExecuteTask.Code, TimeChangedHandler);
                            break;
                        case CompareEnum.等于:
                        case CompareEnum.大于:
                        case CompareEnum.小于:
                        case CompareEnum.不等于:
                            DataStorage.Instance.SubscribeCheckSubscriptionConditionEvent(_scriptExecuteTask.Code, SubscribeTimeChanged);
                            break;
                    }
                }

                break;
            }
            case ExecutionStrategyTypeEnum.变量触发:
            {
                var variableModel = JSON.Deserialize<VariableModel>(Strategy.Config);
                _scriptExecuteTask = new ScriptExecuteTaskModel {Content = Strategy.EdgeComputingScript.Content, Id = Strategy.Id, Code = variableModel.Key};
                // 动态订阅消息
                await _eventBusFactory.Subscribe(_scriptExecuteTask.Code, async context =>
                {
                    if (cancellationToken.IsCancellationRequested)
                        return;
                    var content = _scriptExecuteTask.Content;
                    content = content.Replace("${trigger.key}", $"{_scriptExecuteTask.Code}");
                    await ExecuteScript(Strategy.Id, content);
                }, cancellationToken: cancellationToken);
                break;
            }
        }
    }

    /// <summary>
    ///     添加定时任务
    /// </summary>
    /// <exception cref="AppFriendlyException"></exception>
    private Task TimerTaskAdd()
    {
        if (Strategy.ExecutionTime == null)
            throw Oops.Oh("请选择策略执行周期");
        // 任务间隔
        var period = Strategy.ExecutionTime.Period;
        // 通过 ISchedulerFactory 创建
        _schedulerFactory.TryAddJob((_, _) =>
            {
                try
                {
                    _ = ExecuteScript(Strategy.Id, Strategy.EdgeComputingScript.Content);
                }
                catch (Exception e)
                {
                    Log.Error($"【边缘计算】 Error:【{e.Message}】");
                }

                return Task.CompletedTask;
            }, Strategy.Id.ToString(), Strategy.ExecutionTime.Type is null or 0 or 1, new[]
            {
                // 周期执行
                Strategy.ExecutionTime.ExecutionType == ExecutionTypeEnum.周期执行
                    // 按秒间隔
                    ? Strategy.ExecutionTime.PeriodUnit == ExecutionStrategyByTimePeriodUnitEnum.秒
                        ? Triggers.PeriodSeconds(period).SetTriggerId(Strategy.Id.ToString()).SetDescription(Strategy.EdgeComputingScript.Describe)
                        // 按分钟间隔
                        : Strategy.ExecutionTime.PeriodUnit == ExecutionStrategyByTimePeriodUnitEnum.分钟
                            ? Triggers.PeriodMinutes(period).SetTriggerId(Strategy.Id.ToString()).SetDescription(Strategy.EdgeComputingScript.Describe)
                            // 按小时间隔
                            : Triggers.PeriodHours(period).SetTriggerId(Strategy.Id.ToString()).SetDescription(Strategy.EdgeComputingScript.Describe)
                    : Triggers.Cron(Strategy.ExecutionTime.Cron, CronStringFormat.WithSecondsAndYears).SetTriggerId(Strategy.Id.ToString()).SetDescription(Strategy.EdgeComputingScript.Describe)
            },
            out var scheduler);
        // 设置描述,分组
        scheduler.UpdateDetail(jobBuilder => { jobBuilder.SetGroupName(Strategy.EdgeComputingScript.ScriptName); });
        return Task.CompletedTask;
    }

    /// <summary>
    ///     执行任务
    /// </summary>
    /// <param name="id">策略任务Id</param>
    /// <param name="content"></param>
    private async Task ExecuteScript(long id, string content)
    {
        await Scoped.CreateAsync((_, _) =>
        {
            var message = "";
            try
            {
                // 解析其他服务
                var value = _engine.Engine.Evaluate(content, new ScriptParsingOptions {Tolerant = true, AllowReturnOutsideFunction = true}).ToObject();
                // Log.Information(id + "脚本执行完成：" + value);
                message = "脚本执行完成：" + value.ToJson();
            }
            catch (Exception ex)
            {
                Log.Error($"【边缘计算】 执行策略Id【{id}】,Error:{ex.Message}");
            }

            try
            {
                // Log.Information(id + "添加执行记录：" + message);
                _singletonService.AddRecord(id, message);
            }
            catch (Exception ex)
            {
                Log.Error($"【边缘计算】 添加执行记录Id【{id}】,Error:{ex.Message}");
            }

            return Task.CompletedTask;

            // GC.Collect();
        });
    }

    /// <summary>
    /// </summary>
    private void Stop()
    {
        _cancellationTokenSource.Cancel();
        switch (Strategy.ExecutionStrategyType)
        {
            case ExecutionStrategyTypeEnum.网关启动触发:
                break;
            case ExecutionStrategyTypeEnum.定时触发:
                _schedulerFactory.RemoveJob(Strategy.Id.ToString());
                break;
            case ExecutionStrategyTypeEnum.属性触发:
                switch (_scriptExecuteTask.Compare)
                {
                    case CompareEnum.值变化时执行:
                        DataStorage.Instance.UnsubscribeValueChangedEvent(_scriptExecuteTask.Code, ValueChangedHandler);
                        break;
                    case CompareEnum.时间变化时执行:
                        DataStorage.Instance.UnsubscribeTimeChangedEvent(_scriptExecuteTask.Code, TimeChangedHandler);
                        break;
                    case CompareEnum.等于:
                    case CompareEnum.大于:
                    case CompareEnum.小于:
                    case CompareEnum.不等于:
                        DataStorage.Instance.UnsubscribeCheckSubscriptionConditionEvent(_scriptExecuteTask.Code, SubscribeTimeChanged);
                        break;
                }

                break;
            case ExecutionStrategyTypeEnum.变量触发:
                _eventBusFactory.Unsubscribe(_scriptExecuteTask.Code);
                break;
        }
    }

    /// <summary>
    ///     标记首次是否已经触发
    /// </summary>
    private bool _firstRun;

    /// <summary>
    ///     Value改变事件处理方法
    /// </summary>
    /// <param name="key"></param>
    /// <param name="newValue"></param>
    /// <param name="oldValue"></param>
    private void ValueChangedHandler(string key, object newValue, object oldValue)
    {
        if (!_scriptExecuteTask.FirstEven && !_firstRun)
        {
            _firstRun = true;
            return;
        }

        Log.Information($"【Value改变】 Value changed for key '{key}'. New value: {newValue}, Old value: {oldValue}");
        var content = _scriptExecuteTask.Content;
        content = content.Replace("${trigger.key}", $"{key}");
        content = content.Replace("${trigger.value}", $"{newValue}");
        _ = ExecuteScript(Strategy.Id, content);
    }

    /// <summary>
    ///     Time改变事件处理方法
    /// </summary>
    /// <param name="key"></param>
    /// <param name="newTime"></param>
    /// <param name="oldTime"></param>
    private void TimeChangedHandler(string key, long newTime, long oldTime)
    {
        if (!_scriptExecuteTask.FirstEven && !_firstRun)
        {
            _firstRun = true;
            return;
        }

        Log.Information($"【Time改变】Time changed for key '{key}'. New time: {newTime}, Old time: {oldTime}");
        var content = _scriptExecuteTask.Content;
        content = content.Replace("${trigger.key}", $"{key}");
        content = content.Replace("${trigger.value}", $"{newTime}");
        _ = ExecuteScript(Strategy.Id, content);
    }

    /// <summary>
    ///     数据更新事件处理方法
    /// </summary>
    /// <param name="key"></param>
    /// <param name="newValue"></param>
    /// <param name="newTime"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private void SubscribeTimeChanged(string key, object newValue, long newTime)
    {
        if (!_scriptExecuteTask.FirstEven && !_firstRun)
        {
            _firstRun = true;
            return;
        }

        var content = _scriptExecuteTask.Content;
        content = content.Replace("${trigger.key}", $"{key}");
        content = content.Replace("${trigger.value}", $"{newValue}");
        switch (_scriptExecuteTask.Compare)
        {
            case CompareEnum.等于:
            {
                if (newValue.ToString() == _scriptExecuteTask.Value)
                {
                    Log.Information($"【实时数据-条件触发-等于】  Data updated for key '{key}'. New value: {newValue}, New time: {newTime}");
                    _ = ExecuteScript(Strategy.Id, content);
                }

                break;
            }
            case CompareEnum.大于:
            {
                try
                {
                    if (Convert.ToDouble(newValue) > Convert.ToDouble(_scriptExecuteTask.Value))
                    {
                        Log.Information($"【实时数据-条件触发-大于】  Data updated for key '{key}'. New value: {newValue}, New time: {newTime}");
                        _ = ExecuteScript(Strategy.Id, content);
                    }
                }
                catch (Exception e)
                {
                    Log.Error($"【实时数据-条件触发-大于】 Data updated for key '{key}'. New value: {newValue}, New time: {newTime} ,Error:【{e.Message}】");
                }

                break;
            }
            case CompareEnum.小于:
            {
                try
                {
                    if (Convert.ToDouble(newValue) < Convert.ToDouble(_scriptExecuteTask.Value))
                    {
                        Log.Information($"【实时数据-条件触发-小于】  Data updated for key '{key}'. New value: {newValue}, New time: {newTime}");
                        _ = ExecuteScript(Strategy.Id, content);
                    }
                }
                catch (Exception e)
                {
                    Log.Error($"【实时数据-条件触发-小于】 Data updated for key '{key}'. New value: {newValue}, New time: {newTime} ,Error:【{e.Message}】");
                }

                break;
            }
            case CompareEnum.不等于:
            {
                if (newValue.ToString() != _scriptExecuteTask.Value)
                {
                    Log.Information($"【实时数据-条件触发-不等于】  Data updated for key '{key}'. New value: {newValue}, New time: {newTime}");
                    _ = ExecuteScript(Strategy.Id, content);
                }

                break;
            }
        }
    }

    public void Dispose()
    {
        Stop();
        _db?.Dispose();
        _cancellationTokenSource?.Dispose();
    }
}