using IotGateway.Application.Entity;

namespace IotGateway.Application.EdgeComputingServer.Dtos;

/// <summary>
///     边缘计算-执行策略-新增请求参数
/// </summary>
public class ScriptExecutionStrategyAdd
{
    /// <summary>
    ///     边缘计算脚本Id
    /// </summary>
    [Required]
    public long EdgeComputingScriptId { get; set; }

    /// <summary>
    ///     执行模式:1网关启动；2定时；3属性；4变量；5消息；6Http
    /// </summary>
    [Required]
    public ExecutionStrategyTypeEnum ExecutionStrategyType { get; set; }

    /// <summary>
    ///     周期执行的配置实体
    /// </summary>
    public ExecutionTime? ExecutionTime { get; set; }

    /// <summary>
    ///     属性触发任务的配置实体
    /// </summary>
    public PropertyModel? Propertys { get; set; }

    /// <summary>
    ///     变量触发的配置实体
    /// </summary>
    public VariableModel? Variable { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Describe { get; set; }
}

/// <summary>
///     边缘计算-执行策略-修改请求参数
/// </summary>
public class ScriptExecutionStrategyUpdate : ScriptExecutionStrategyAdd
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}


/// <summary>
///     触发任务统一模型
/// </summary>
public class ScriptExecuteTaskModel
{
    /// <summary>
    ///     内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    ///     唯一码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    ///     1.值变化时执行;2:时间触发时执行;3:等于;4：大于；5：小于；6：不等于
    /// </summary>
    public CompareEnum Compare { get; set; }

    /// <summary>
    ///     首次是否触发
    /// </summary>
    public bool FirstEven { get; set; }

    /// <summary>
    ///     任务Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public string Value { get; set; }
}

