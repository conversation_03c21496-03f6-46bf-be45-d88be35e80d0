using MiniExcelLibs.Attributes;

namespace IotGateway.Application.EdgeComputingServer.Dtos;

/// <summary>
/// </summary>
public class ComputingScriptInPort
{
    /// <summary>
    ///     上传文件
    /// </summary>
    public IFormFile File { get; set; }

    /// <summary>
    ///     1:覆盖原数据,2:忽略
    /// </summary>
    public DeviceVariableInPortTypeEnum InPortType { get; set; }
}

/// <summary>
///     边缘计算-脚本导入
/// </summary>
public class ComputingScriptInPortDto
{
    /// <summary>
    ///     脚本名称
    /// </summary>
    [ExcelColumnName("脚本名称")]
    public string ScriptName { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    [ExcelColumnName("脚本内容")]
    public string Content { get; set; }

    /// <summary>
    ///     启用/禁用
    /// </summary>
    [ExcelColumnName("是否启用")]
    public bool Enable { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [ExcelColumnName("描述 选填")]
    public string Describe { get; set; }
}

/// <summary>
///     边缘计算-执行策略导入
/// </summary>
public class ScriptExecutionInPortDto
{
    /// <summary>
    ///     脚本名称
    /// </summary>
    [ExcelColumnName("脚本名称")]
    public string ScriptName { get; set; }

    /// <summary>
    ///     执行模式:1:网关启动；2：定时；3：属性；4：变量
    /// </summary>
    [ExcelColumnName("执行模式")]
    public string ExecutionStrategyType { get; set; }

    /// <summary>
    ///     执行周期
    /// </summary>
    [ExcelColumnName("执行周期 选填")]
    public int Period { get; set; }

    /// <summary>
    ///     时间单位:1秒；2分钟；3小时
    /// </summary>
    [ExcelColumnName("执行周期单位 选填")]
    public string PeriodUnit { get; set; }

    /// <summary>
    ///     属性名称
    /// </summary>
    [ExcelColumnName("属性名称 选填")]
    public string Name { get; set; }

    /// <summary>
    ///     操作符
    /// </summary>
    [ExcelColumnName("操作符 选填")]
    public string Compare { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    [ExcelColumnName("值 选填")]
    public string Value { get; set; }

    /// <summary>
    ///     首次是否触发
    /// </summary>
    [ExcelColumnName("首次是否触发 选填")]
    public bool FirstEven { get; set; }

    /// <summary>
    ///     触发变量
    /// </summary>
    [ExcelColumnName("触发变量 选填")]
    public string Key { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述 选填")]
    public string Describe { get; set; }

    /// <summary>
    ///     启用/禁用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool Enable { get; set; } = true;
}