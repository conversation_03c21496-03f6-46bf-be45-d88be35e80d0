namespace IotGateway.Application.EdgeComputingServer.Dtos;

/// <summary>
/// 边缘计算-脚本-新增请求参数
/// </summary>
public class EdgeComputingScriptAdd
{
    /// <summary>
    ///     脚本名称
    /// </summary>
    [Required]
    public string ScriptName { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    [Required]
    public string Content { get; set; }
    
    /// <summary>
    ///     描述
    /// </summary>
    public string Describe { get; set; }
}

/// <summary>
/// 边缘计算-脚本-修改请求参数
/// </summary>
public class EdgeComputingScriptUpdate : EdgeComputingScriptAdd
{
    /// <summary>
    /// Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}

/// <summary>
/// 边缘计算-脚本下拉返回参数
/// </summary>
public class EdgeComputingScriptSelectOutput
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    ///     脚本名称
    /// </summary>
    public string ScriptName { get; set; }
}