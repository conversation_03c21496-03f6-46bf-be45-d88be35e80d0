using Furion.Schedule;
using IotGateway.Application.EdgeComputingServer.Dtos;
using IotGateway.Application.Entity;
using DateTime = System.DateTime;

namespace IotGateway.Application.EdgeComputingServer;

/// <summary>
///     边缘计算-执行策略
///     版 本:V3.1.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-06-19
/// </summary>
[ApiDescriptionSettings("边缘计算")]
public class ScriptExecutionStrategyService : ITransient, IDynamicApiController
{
    private readonly SqlSugarRepository<ScriptExecutionStrategy> _scriptExecution;
    private readonly ISchedulerFactory _schedulerFactory;
    private readonly SingletonService _singletonService;
    private readonly ScriptExecutionSingleton _scriptExec;

    public ScriptExecutionStrategyService(SqlSugarRepository<ScriptExecutionStrategy> scriptExecution, ISchedulerFactory schedulerFactory, SingletonService singletonService,
        ScriptExecutionSingleton scriptExec)
    {
        _scriptExecution = scriptExecution;
        _schedulerFactory = schedulerFactory;
        _singletonService = singletonService;
        _scriptExec = scriptExec;
    }

    /// <summary>
    ///     边缘计算-执行策略列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeComputing/strategy/page")]
    public async Task<SqlSugarPagedList<ScriptExecutionStrategy>> Page([FromQuery] BasePageInput input)
    {
        var scriptExecutionStrategyList = await _scriptExecution.AsQueryable()
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue), w => w.Describe.Contains(input.SearchValue))
            .Includes(w => w.EdgeComputingScript)
            .ToPagedListAsync(input.PageNo, input.PageSize);

        return scriptExecutionStrategyList;
    }

    /// <summary>
    ///     边缘计算-执行策略详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeComputing/strategy/detail")]
    public async Task<ScriptExecutionStrategy> Detail([FromQuery] BaseId input)
    {
        var scriptExecutionStrategy = await _scriptExecution.AsQueryable().Where(f => f.Id == input.Id).Includes(w => w.EdgeComputingScript).FirstAsync();
        if (scriptExecutionStrategy == null)
            throw Oops.Oh(ErrorCode.D1002);
        return scriptExecutionStrategy;
    }

    /// <summary>
    ///     边缘计算-执行策略-记录列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeComputing/strategy/record")]
    public Task<List<RunRecordLine>> RecordList([FromQuery] BaseId input)
    {
        return Task.FromResult(_singletonService.RecordLines.TryGetValue(input.Id, out var line) ? line.OrderByDescending(w => w.Time).ToList() : new List<RunRecordLine>());
    }

    /// <summary>
    ///     执行作业
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("/edgeComputing/strategy/run")]
    public void RunJob(BaseId input)
    {
        if (_schedulerFactory.TryRunJob(input.Id.ToString(), out _) != ScheduleResult.Succeed)
            throw Oops.Oh("执行作业失败");
    }

    /// <summary>
    ///     边缘计算-执行策略-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/strategy/add")]
    
    public async Task Add(ScriptExecutionStrategyAdd input)
    {
        var script = await _scriptExecution.AsSugarClient().Queryable<EdgeComputingScript>().FirstAsync(f => f.Id == input.EdgeComputingScriptId);
        if (script == null)
            throw Oops.Oh(ErrorCode.D1002);
        var strategy = input.Adapt<ScriptExecutionStrategy>();
        switch (strategy.ExecutionStrategyType)
        {
            case ExecutionStrategyTypeEnum.网关启动触发:
                strategy.Config = "";
                break;
            case ExecutionStrategyTypeEnum.定时触发:
                if (input.ExecutionTime == null)
                    throw Oops.Oh("定时触发任务配置出错！");
                strategy.Config = input.ExecutionTime.ToJson();
                break;
            case ExecutionStrategyTypeEnum.属性触发:
                if (input.Propertys == null)
                    throw Oops.Oh("属性触发任务配置出错！");
                strategy.Config = input.Propertys.ToJson();
                break;
            case ExecutionStrategyTypeEnum.变量触发:
                if (input.Variable == null)
                    throw Oops.Oh("变量触发任务配置出错！");
                strategy.Config = input.Variable.ToJson();
                break;
        }

        // 增加策略任务
        strategy.EdgeComputingScript = script;
        strategy.Id = YitIdHelper.NextId();
        _scriptExec.Add(strategy);
        await _scriptExecution.InsertAsync(strategy);
    }

    /// <summary>
    ///     边缘计算-执行策略-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/strategy/update")]
    
    public async Task Update(ScriptExecutionStrategyUpdate input)
    {
        var strategy = await _scriptExecution.AsQueryable().Where(f => f.Id == input.Id).Includes(w => w.EdgeComputingScript).FirstAsync();
        strategy.Describe = input.Describe;
        switch (strategy.ExecutionStrategyType)
        {
            case ExecutionStrategyTypeEnum.网关启动触发:
                strategy.Config = "";
                break;
            case ExecutionStrategyTypeEnum.定时触发:
                if (input.ExecutionTime == null)
                    throw Oops.Oh("定时触发任务配置出错！");
                strategy.Config = input.ExecutionTime.ToJson();
                break;
            case ExecutionStrategyTypeEnum.属性触发:
                if (input.Propertys == null)
                    throw Oops.Oh("属性触发任务配置出错！");
                strategy.Config = input.Propertys.ToJson();
                break;
            case ExecutionStrategyTypeEnum.变量触发:
                if (input.Variable == null)
                    throw Oops.Oh("变量触发任务配置出错！");
                strategy.Config = input.Variable.ToJson();
                break;
        }

        strategy.ExecutionStrategyType = input.ExecutionStrategyType;
        _scriptExec.Add(strategy);
        await _scriptExecution.UpdateAsync(strategy);
    }

    /// <summary>
    ///     边缘计算-执行策略-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/strategy/delete")]
    public async Task Delete(BaseId<List<long>> input)
    {
        // 策略
        var strategyList = await _scriptExecution.AsQueryable().Where(f => input.Id.Contains(f.Id)).Includes(w => w.EdgeComputingScript).ToListAsync();
        foreach (var strategy in strategyList)
            // 移除原有策略
            _scriptExec.Stop(strategy.Id);
        await _scriptExecution.DeleteAsync(strategyList);
    }

    /// <summary>
    ///     边缘计算-执行策略-启用/禁用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/strategy/enable")]
    
    public async Task<bool> Enable(EnableInput<List<long>> input)
    {
        var strategyList = await _scriptExecution.AsQueryable().Where(f => input.Id.Contains(f.Id)).Includes(w => w.EdgeComputingScript).ToListAsync();
        foreach (var strategy in strategyList)
        {
            strategy.Enable = input.Enable;
            if (strategy.Enable)
                _scriptExec.Add(strategy);
            else
                _scriptExec.Stop(strategy.Id);
        }

        await _scriptExecution.AsUpdateable(strategyList).UpdateColumns(w => w.Enable).ExecuteCommandAsync();
        return true;
    }

    /// <summary>
    ///     边缘计算-执行策略-重新同步
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [NonAction]
    public async Task Sync(List<ScriptExecutionStrategy> input)
    {
        foreach (var strategy in input)
            try
            {
                _scriptExec.Add(strategy);
            }
            catch
            {
                // ignored
            }
    }

    /// <summary>
    ///     导入边缘计算-策略任务
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/strategy/inPort")]
    
    public async Task<DataInPortOutput> InPort([FromForm] ComputingScriptInPort input)
    {
        var stream = new MemoryStream();
        await input.File.CopyToAsync(stream);
        // 返回结果
        var result = new DataInPortOutput {ErrorColumn = new List<ErrorColumn>()};
        // 需要新增的脚本
        var computingScriptList = new List<EdgeComputingScript>();
        // 需要新增的策略
        var scriptExecutionStrategyList = new List<ScriptExecutionStrategy>();
        // 当前所在行
        var line = 2;
        foreach (var computingScriptInPort in await stream.QueryAsync<ComputingScriptInPortDto>("脚本信息", startCell: "A2"))
            try
            {
                //检查必填项
                var success = CheckInPortScriptData(computingScriptInPort, line, result);
                if (!success)
                    continue;
                var computingScript = computingScriptInPort.Adapt<EdgeComputingScript>();
                computingScript.Id = YitIdHelper.NextId();
                computingScript.CreatedTime = DateTime.Now;
                computingScriptList.Add(computingScript);
                line++;
            }
            catch (Exception e)
            {
                result.ErrorCount += 1;
                result.ErrorColumn.Add(new ErrorColumn {Error = $"【脚本信息】 第【{line}】行 Error:【{e.Message}】 "});
            }

        foreach (var inPort in await stream.QueryAsync<ScriptExecutionInPortDto>("执行策略", startCell: "A2"))
            try
            {
                // 检查必填项
                var success = CheckInPortData(inPort, line, result);
                if (!success)
                    continue;
                var scriptExecutionStrategy = inPort.Adapt<ScriptExecutionStrategy>();
                scriptExecutionStrategy.Id = YitIdHelper.NextId();
                switch (scriptExecutionStrategy.ExecutionStrategyType)
                {
                    case ExecutionStrategyTypeEnum.定时触发:
                        scriptExecutionStrategy.Config = JSON.Serialize(new ExecutionTime
                        {
                            PeriodUnit = EnumUtil.GetEnum<ExecutionStrategyByTimePeriodUnitEnum>(inPort.PeriodUnit),
                            Period = inPort.Period
                        });
                        break;
                    case ExecutionStrategyTypeEnum.属性触发:
                        scriptExecutionStrategy.Config = JSON.Serialize(new PropertyModel
                        {
                            Name = JSON.Deserialize<List<string>>(inPort.Name),
                            Compare = EnumUtil.GetEnum<CompareEnum>(inPort.Compare),
                            FirstEven = inPort.FirstEven,
                            Value = inPort.Value
                        });
                        break;
                    case ExecutionStrategyTypeEnum.变量触发:
                        scriptExecutionStrategy.Config = JSON.Serialize(new VariableModel
                        {
                            Key = inPort.Name
                        });
                        break;
                    case ExecutionStrategyTypeEnum.网关启动触发:
                        break;
                    default:
                        result.ErrorCount += 1;
                        result.ErrorColumn.Add(new ErrorColumn {Error = $"【执行策略】 第【{line}】行 Error:【执行模式】不支持 "});
                        break;
                }

                var computingScript = computingScriptList.FirstOrDefault(f => f.ScriptName == inPort.ScriptName);
                if (computingScript == null)
                {
                    result.ErrorCount += 1;
                    result.ErrorColumn.Add(new ErrorColumn {Error = $"【执行策略】 第【{line}】行 Error:【脚本名称不存在】 "});
                }

                scriptExecutionStrategy.EdgeComputingScriptId = computingScript.Id;
                scriptExecutionStrategyList.Add(scriptExecutionStrategy);
                line++;
            }
            catch (Exception e)
            {
                result.ErrorCount += 1;
                result.ErrorColumn.Add(new ErrorColumn {Error = $"【执行策略】 第【{line}】行 Error:【{e.Message}】 "});
            }

        // 查找出来相同名称脚本
        var computingScriptAny = await _scriptExecution.AsSugarClient().Queryable<EdgeComputingScript>()
            .Where(w => computingScriptList.Select(s => s.ScriptName).Contains(w.ScriptName))
            .ToListAsync();

        foreach (var computingScript in computingScriptAny)
        {
            var deviceVariableVal = computingScriptList.FirstOrDefault(f => f.ScriptName == computingScript.ScriptName);
            if (deviceVariableVal == null)
                continue;
            if (input.InPortType == DeviceVariableInPortTypeEnum.OverLook)
            {
                computingScriptList.Remove(deviceVariableVal);
            }
            else
            {
                // 覆盖原始数据
                // 覆盖属性
                // 移除对象,修改Id后重新加入
                computingScriptList.Remove(deviceVariableVal);
                deviceVariableVal.Id = computingScript.Id;
                computingScriptList.Add(deviceVariableVal);
            }
        }

        if (computingScriptList.Any())
            await _scriptExecution.AsSugarClient().Storageable(computingScriptList).ExecuteCommandAsync();
        await _scriptExecution.InsertRangeAsync(scriptExecutionStrategyList);
        result.SuccessCount = line - 1 - result.ErrorCount < 0 ? 0 : line - 2 - result.ErrorCount;
        return result;
    }

    /// <summary>
    ///     导出边缘计算-策略任务
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/strategy/exPort")]
    public async Task<IActionResult> ExPort(BaseId<List<long>> input)
    {
        var executionStrategyList = await _scriptExecution.AsQueryable().Where(f => input.Id.Contains(f.Id))
            .Includes(w => w.EdgeComputingScript)
            .ToListAsync();

        var value = new
        {
            strategy = executionStrategyList.Select(s => new
            {
                s.EdgeComputingScript.ScriptName,
                ExecutionStrategyType = EnumUtil.GetEnumDesc(s.ExecutionStrategyType),
                s.ExecutionTime?.Period,
                PeriodUnit = s.ExecutionTime != null ? EnumUtil.GetEnumDesc(s.ExecutionTime.PeriodUnit) : "",
                s.Describe,
                s.Enable,
                Name = s.Propertys != null ? JSON.Serialize(s.Propertys?.Name) : "",
                Compare = s.Propertys != null ? EnumUtil.GetEnumDesc(s.Propertys.Compare) : "",
                s.Propertys?.Value,
                s.Propertys?.FirstEven,
                s.Variable?.Key
            }),
            script = executionStrategyList.GroupBy(w => w.EdgeComputingScript.ScriptName)
                .Select(s => new
                {
                    s.FirstOrDefault()?.EdgeComputingScript.ScriptName,
                    s.FirstOrDefault()?.EdgeComputingScript.Content,
                    s.FirstOrDefault()?.EdgeComputingScript.Describe,
                    s.FirstOrDefault()?.EdgeComputingScript.Enable
                })
        };

        try
        {
            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsByTemplateAsync("Templates/EdgeComputingPolicyImport.xlsx", value);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            {
                FileDownloadName = $"{DateTime.Now:yyyyMMddHHmmssfff}.xlsx"
            };
        }
        catch (Exception e)
        {
            throw Oops.Bah($"导出失败：{e.Message}");
        }
    }

    /// <summary>
    ///     检查属性导入必填项
    /// </summary>
    private bool CheckInPortScriptData(ComputingScriptInPortDto inPort, int line, DataInPortOutput result)
    {
        if (inPort.ScriptName.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"【脚本信息】 第【{line}】行 【脚本名称】 不能是空！ ", Line = line});
            return false;
        }

        if (inPort.Content.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"【脚本信息】 第【{line}】行 【脚本内容】 不能是空！ ", Line = line});
            return false;
        }

        return true;
    }

    /// <summary>
    ///     检查属性导入必填项
    /// </summary>
    private bool CheckInPortData(ScriptExecutionInPortDto inPort, int line, DataInPortOutput result)
    {
        if (inPort.ScriptName.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn {Error = $"【执行策略】 第【{line}】行 【脚本名称】 不能是空！ ", Line = line});
            return false;
        }

        switch (EnumUtil.GetEnum<ExecutionStrategyTypeEnum>(inPort.ExecutionStrategyType))
        {
            case ExecutionStrategyTypeEnum.网关启动触发:
                break;
            case ExecutionStrategyTypeEnum.定时触发:
            {
                if (inPort.Period <= 0)
                {
                    result.ErrorCount += 1;
                    result.ErrorColumn.Add(new ErrorColumn {Error = $"【执行策略】 第【{line}】行 【定时执行】 执行周期是必填！ ", Line = line});
                    return false;
                }

                if (EnumUtil.GetEnum<ExecutionStrategyByTimePeriodUnitEnum>(inPort.PeriodUnit) <= 0)
                {
                    result.ErrorCount += 1;
                    result.ErrorColumn.Add(new ErrorColumn {Error = $"【执行策略】 第【{line}】行 【定时执行】 执行周期单位是必填！ ", Line = line});
                    return false;
                }

                break;
            }

            case ExecutionStrategyTypeEnum.属性触发:
                if (string.IsNullOrEmpty(inPort.Name))
                {
                    result.ErrorCount += 1;
                    result.ErrorColumn.Add(new ErrorColumn {Error = $"【执行策略】 第【{line}】行 【属性触发】 属性名称是必填项！ ", Line = line});
                    return false;
                }

                if (string.IsNullOrEmpty(inPort.Compare))
                {
                    result.ErrorCount += 1;
                    result.ErrorColumn.Add(new ErrorColumn {Error = $"【执行策略】 第【{line}】行 【属性触发】 操作符是必填项！ ", Line = line});
                    return false;
                }

                break;
            case ExecutionStrategyTypeEnum.变量触发:
                break;
            default:
                result.ErrorCount += 1;
                result.ErrorColumn.Add(new ErrorColumn {Error = $"【执行策略】 第【{line}】行 请选择执行模式！ ", Line = line});
                return false;
        }

        return true;
    }
}