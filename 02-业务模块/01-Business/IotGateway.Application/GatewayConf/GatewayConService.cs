using IotGateway.Application.Entity;
using IotGateway.Application.GatewayConf.Dto;
using IotGateway.Application.TransPonds;
using IotGateway.EdgeDevice.TransPonds;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Application.GatewayConf;

/// <summary>
///     网关配置
///     版 本:V3.1.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-06-12
/// </summary>
[ApiDescriptionSettings("系统配置")]
public class GatewayConService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarClient _db;
    private readonly IEventPublisher _eventPublisher;
    private readonly TransPondService _transPondService;

    /// <summary>
    /// </summary>
    /// <param name="db"></param>
    /// <param name="eventPublisher"></param>
    /// <param name="transPondService"></param>
    public GatewayConService(ISqlSugarClient db, IEventPublisher eventPublisher, TransPondService transPondService)
    {
        _db = db;
        _eventPublisher = eventPublisher;
        _transPondService = transPondService;
    }

    /// <summary>
    ///     导出网关配置内容
    /// </summary>
    /// <returns></returns>
    [HttpGet("/gateway/exPort")]
    public async Task<IActionResult> GatewayExPort()
    {
        //网关设备配置
        var deviceList = await _db.Queryable<Device>()
            .Includes(w => w.DeviceConfigs)
            .Includes(w => w.DeviceVariable)
            .ToListAsync();
        //转发配置
        var transPondList = await _db.Queryable<TransPond>().Includes(w => w.TransPondTopic).ToListAsync();
        //网络配置
        var netWork = await "http://127.0.0.1:8093/network/getNetWork"
            .SetHttpMethod(HttpMethod.Get)
            .SetContentType("application/json")
            .SetClient(() => new HttpClient {Timeout = TimeSpan.FromSeconds(5)})
            .SetContentEncoding(Encoding.UTF8)
            .GetAsAsync<RESTfulResult<byte[]>>();

        var gatewayExPortOutput = new GatewayExPortOutput
        {
            Devices = deviceList,
            TransPonds = transPondList,
            NetWork = netWork?.Data
        };
        // 加密
        var memoryStream = new MemoryStream(AESEncryption.Encrypt(Encoding.UTF8.GetBytes(gatewayExPortOutput.ToJson()), "201001"));
        return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = DateTime.ShangHai().ToString("ddHHmmss") + "-encrypt.rar"
        };
    }

    /// <summary>
    ///     导入网关配置内容
    /// </summary>
    /// <returns></returns>
    [HttpPost("/gateway/inPort")]
    
    public async Task GatewayInPort([FromForm] GatewayInPortInput input)
    {
        var fileByte = AESEncryption.Decrypt(input.File.ToByteArray(), "201001");
        // 导入内容
        var gatewayExPortOutput = Encoding.UTF8.GetString(fileByte).ToObject<GatewayExPortOutput>();

        // 网络配置
        if (gatewayExPortOutput.NetWork != null && gatewayExPortOutput.NetWork.Any())
            await "http://127.0.0.1:8093/network/setNetWork"
                .SetHttpMethod(HttpMethod.Post)
                .SetBody(new BaseId<byte[]> {Id = gatewayExPortOutput.NetWork})
                .SetClient(() => new HttpClient {Timeout = TimeSpan.FromSeconds(5)})
                .SetContentType("application/json")
                .SetContentEncoding(Encoding.UTF8)
                .PostAsStringAsync();

        // 网关设备配置
        var deviceList = await _db.Queryable<Device>().ToListAsync();
        // 停止全部设备采集
        foreach (var device in deviceList)
            await _eventPublisher.PublishAsync(EventConst.StopDeviceThread, device);

        await _db.DeleteNav(deviceList).Include(w => w.DeviceConfigs)
            .Include(w => w.DeviceVariable)
            .ExecuteCommandAsync();

        // 转发配置
        var transPondList = await _db.Queryable<TransPond>().ToListAsync();
        foreach (var transPond in transPondList) await _transPondService.Delete(new BaseId {Id = transPond.Id});

        await _db.InsertNav(gatewayExPortOutput.Devices).Include(w => w.DeviceConfigs)
            .Include(w => w.DeviceVariable)
            .ExecuteCommandAsync();

        foreach (var transPond in gatewayExPortOutput.TransPonds)
            transPond.Enable = false;

        await _db.InsertNav(gatewayExPortOutput.TransPonds).Include(w => w.TransPondTopic)
            .ExecuteCommandAsync();
    }
}