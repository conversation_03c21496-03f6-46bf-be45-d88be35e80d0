using IotGateway.Application.Entity;

namespace IotGateway.Application.GatewayConf.Dto;

/// <summary>
/// 导出网关配置内容
/// </summary>
public class GatewayExPortOutput
{
    /// <summary>
    /// 设备配置
    /// </summary>
    public List<Device> Devices { get; set; }

    /// <summary>
    /// 转发配置
    /// </summary>
    public List<TransPond> TransPonds { get; set; }

    /// <summary>
    /// 网络配置
    /// </summary>
    public byte[] NetWork { get; set; }
}