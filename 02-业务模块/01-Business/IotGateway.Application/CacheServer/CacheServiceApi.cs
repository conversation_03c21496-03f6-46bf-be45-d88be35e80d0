using Feng.IotGateway.Core.Service.Cache;
using Feng.IotGateway.Core.Service.Cache.Dto;
using IotGateway.Application.CacheServer.Dto;

namespace IotGateway.Application.CacheServer;

/// <summary>
///     缓存信息
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-12-27
/// </summary>
[ApiDescriptionSettings("系统Api", Order = 410)]
public class CacheServiceApi : IDynamicApiController, ITransient
{
    private readonly CacheService _cacheService;
    private readonly Share _share;

    /// <summary>
    /// </summary>
    /// <param name="cacheService"></param>
    /// <param name="share"></param>
    public CacheServiceApi(CacheService cacheService, Share share)
    {
        _cacheService = cacheService;
        _share = share;
    }

    /// <summary>
    ///     获取所有缓存列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/cache/keyList")]
    public async Task<dynamic> GetAllCacheKeys()
    {
        var output = new List<dynamic>();
        output.Add(new
        {
            Type = "临时变量",
            List = _share.ShareDictionary.Select(s => s.Key).ToList()
        });

        output.Add(new
        {
            Type = "持久变量",
            List = await _cacheService.GetAllCacheKeys()
        });
        return output;
    }

    /// <summary>
    ///     根据父键清空
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    [HttpGet("/cache/delByParentKey")]
    public Task DelByParentKeyAsync(string key)
    {
        _cacheService.RemoveByPrefixKey(key);
        return Task.CompletedTask;
    }

    /// <summary>
    ///     增加对象缓存
    /// </summary>
    /// <param name="cacheKey"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    [HttpPost("/cache/addObject")]
    public Task SetAsync(string cacheKey, object value)
    {
        _cacheService.Set(cacheKey, value);
        return Task.CompletedTask;
    }

    /// <summary>
    ///     增加对象缓存,并设置过期时间
    /// </summary>
    /// <param name="cacheKey"></param>
    /// <param name="value"></param>
    /// <param name="expire"></param>
    /// <returns></returns>
    [HttpPost("/cache/addObject/expire")]
    public Task SetAsync(string cacheKey, object value, TimeSpan expire)
    {
        _cacheService.Set(cacheKey, value, expire);
        return Task.CompletedTask;
    }

    /// <summary>
    ///     增加字符串缓存
    /// </summary>
    /// <param name="cacheKey"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    [HttpPost("/cache/addString")]
    public Task SetStringAsync(string cacheKey, string value)
    {
        _cacheService.Set(cacheKey, value);
        return Task.CompletedTask;
    }

    /// <summary>
    ///     增加字符串缓存,并设置过期时间
    /// </summary>
    /// <param name="cacheKey"></param>
    /// <param name="value"></param>
    /// <param name="expire"></param>
    /// <returns></returns>
    [HttpPost("/cache/addString/expire")]
    public Task SetStringAsync(string cacheKey, string value, TimeSpan expire)
    {
        _cacheService.Set(cacheKey, value, expire);
        return Task.CompletedTask;
    }

    /// <summary>
    ///     获取缓存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/cache/detail")]
    public Task<object> GetStringAsync([FromQuery] CacheDetailInput input)
    {
        return Task.FromResult(input.Type == "持久变量"
            ? _cacheService.GetValue(input.Key)
            : _share.ShareDictionary.TryGetValue(input.Key, out var value)
                ? value
                : null);
    }

    /// <summary>
    ///     删除缓存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/cache/remove")]
    public Task RemoveAsync([FromBody] CacheRemoveInput input)
    {
        if (input.Type == "持久变量")
            _cacheService.Remove(input.Key);
        else
            _share.ShareDictionary.Remove(input.Key);
        return Task.CompletedTask;
    }
}