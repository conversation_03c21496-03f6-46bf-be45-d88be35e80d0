using Feng.IotGateway.Core.Service.Cache;
using IotGateway.Application.Entity;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Application.MessageServer;

/// <summary>
///     站内信
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2022-10-01
/// </summary>
[ApiDescriptionSettings("系统配置")]
public class MessageService : ITransient, IDynamicApiController
{
    private readonly SqlSugarRepository<Message> _message;
    private readonly SendMessageService _socket;
    private readonly ILogger<MessageService> _logger;
    private readonly CacheService _cacheService;

    public MessageService(SqlSugarRepository<Message> message, SendMessageService socket, ILogger<MessageService> logger, CacheService cacheService)
    {
        _message = message;
        _socket = socket;
        _logger = logger;
        _cacheService = cacheService;
    }

    #region Get

    /// <summary>
    ///     站内信列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/message/page")]
    public async Task<SqlSugarPagedList<Message>> Page([FromQuery] BasePageInput input)
    {
        var messageList = await _message.AsQueryable()
            .OrderBy(it => new {it.Read, name = SqlFunc.Desc(it.CreatedTime), it.Level})
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return messageList;
    }

    /// <summary>
    ///     站内信-未读消息数量
    /// </summary>
    /// <returns></returns>
    [HttpGet("/message/unreadCount")]
    public async Task<int> UnReadCount()
    {
        return await _message.CountAsync(a => !a.Read);
    }

    #endregion Get

    #region Post

    /// <summary>
    ///     站内信-全部设置成已读
    /// </summary>
    /// <returns></returns>
    [HttpPost("/message/setAllRead")]
    public async Task SetAllRead()
    {
        await _message.CopyNew().AsUpdateable()
            .SetColumns(it => it.Read == true)
            .Where(w => w.Read == false)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     站内信-批量删除
    /// </summary>
    /// <returns></returns>
    [HttpPost("/message/delete")]
    public async Task Delete(BaseId<List<long>> input)
    {
        await _message.CopyNew().AsUpdateable()
            .SetColumns(it => it.Read == true)
            .Where(w => input.Id.Contains(w.Id))
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     站内信-改为已读状态
    /// </summary>
    /// <returns></returns>
    [HttpPost("/message/setRead")]
    public async Task SetRead(BaseId input)
    {
        await _message.CopyNew().AsUpdateable()
            .SetColumns(it => it.Read == true)
            .Where(it => it.Id == input.Id)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     站内信-关闭/开启推送
    /// </summary>
    /// <returns></returns>
    [HttpPost("/message/enable")]
    public Task Enable(MessageEnableInput input)
    {
        _cacheService.Set("SendMessage", input.Enable ? "true" : "false");
        return Task.CompletedTask;
    }

    /// <summary>
    ///     站内信-关闭/开启状态
    /// </summary>
    /// <returns></returns>
    [HttpGet("/message/status")]
    public Task<bool> Status()
    {
        var isSend = _cacheService.Get<string>("SendMessage");
        if (isSend != null) return Task.FromResult(isSend.ToLower() == "true");
        _cacheService.Set("SendMessage", "true");
        return Task.FromResult(true);
    }

    /// <summary>
    ///     站内信-新增
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task Add(Message input)
    {
        await _message.CopyNew().InsertAsync(input);
        var isSend = _cacheService.Get<string>("SendMessage");
        if (isSend.ToLower() == "true")
            await _socket.Send($"{JSON.Serialize(input)}", ConstMethod.Message);
    }

    /// <summary>
    ///     站内信-删除超过7天的站内信
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task Delete()
    {
        var deleteNumber = await _message.CopyNew().AsDeleteable().Where(it => it.CreatedTime.Value.Date < DateTime.Now().AddDays(-1).Date).ExecuteCommandAsync();
        _logger.LogInformation($"------------------------------------定时清理站内信,删除【{deleteNumber}】条------------------------------------------------------");
    }

    #endregion Post
}