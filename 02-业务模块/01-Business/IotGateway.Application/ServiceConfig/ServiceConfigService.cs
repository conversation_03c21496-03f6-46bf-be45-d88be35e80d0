using IotGateway.Application.ServiceConfig.Dtos;
using IotGateway.ModbusServer.Models;
using IotGateway.OpcUaServer.Models;

namespace IotGateway.Application.ServiceConfig;

/// <summary>
///     服务配置管理
/// </summary>
[ApiDescriptionSettings("系统配置")]
public class ServiceConfigService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     配置路径
    /// </summary>
    private const string ConfigPath = "/etc/DeviceConf/Configs/";

    /// <summary>
    ///     默认配置字典
    /// </summary>
    public static readonly Dictionary<string, ServiceConfigDetailOutput> DefaultConfigs = new()
    {
        {
            "modbus", new ServiceConfigDetailOutput
            {
                ServiceType = "modbus",
                Enabled = false,
                MaxConnections = 100,
                Timeout = 3000,
                ExtConfig = new ExtConfig
                {
                    Schema = new ExtConfigSchema
                    {
                        Properties = new Dictionary<string, object>
                        {
                            {
                                "Port", new
                                {
                                    type = "integer",
                                    title = "端口号",
                                    description = "modbus服务器监听端口",
                                    minimum = 1,
                                    maximum = 65535,
                                    defaultValue = 502
                                }
                            },
                            {
                                "StationNumber", new
                                {
                                    type = "integer",
                                    title = "站号",
                                    description = "modbus服务器站号",
                                    minimum = 1,
                                    maximum = 20,
                                    defaultValue = 1
                                }
                            },
                            {
                                "DataFormat", new
                                {
                                    type = "select",
                                    title = "数据格式",
                                    description = "modbus服务器数据格式",
                                    @enum = new[] { "ABCD", "BADC", "CDAB", "DCBA" },
                                    defaultValue = "ABCD"
                                }
                            }
                        },
                        Required = new[] { "Port", "StationNumber", "DataFormat"}
                    },
                    Config = new ModbusConfig()
                }
            }
        },
        {
            "opcua", new ServiceConfigDetailOutput
            {
                ServiceType = "opcua",
                Enabled = false,
                MaxConnections = 100,
                Timeout = 5000,
                ExtConfig = new ExtConfig
                {
                    Schema = new ExtConfigSchema
                    {
                        Properties = new Dictionary<string, object>
                        {
                            {
                                "Port", new
                                {
                                    type = "integer",
                                    title = "端口号",
                                    description = "OPC UA服务器端口号",
                                    minimum = 1,
                                    maximum = 65535,
                                    defaultValue = 62541
                                }
                            },
                            {
                                "LocalIpAddress", new
                                {
                                    type = "string",
                                    title = "本地IP地址",
                                    description = "OPC UA服务器本地IP地址",
                                    defaultValue = "localhost"
                                }
                            }
                        },
                        Required = new[] { "Port", "LocalIpAddress" }
                    },
                    Config = new OpcUaConfig()
                }
            }
        }
    };

    private readonly ModbusServer.ModbusServer _modbusServer;
    private readonly Feng.IotGateway.OpcUaService.UaService _uaService;
    private readonly SqlSugarRepository<Device> _device;
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="modbusServer"></param>
    /// <param name="uaService">OPC UA 服务</param>
    public ServiceConfigService(ModbusServer.ModbusServer modbusServer, Feng.IotGateway.OpcUaService.UaService uaService, SqlSugarRepository<Device> device)
    {
        _modbusServer = modbusServer;
        _uaService = uaService;
        _device = device;
    }

    /// <summary>
    ///     获取服务配置
    /// </summary>
    /// <param name="serviceType">服务类型(opcua/modbus)</param>
    /// <returns></returns>
    [HttpGet("/serviceConfig/detail")]
    public Task<ServiceConfigDetailOutput> GetServiceConfig([Required] string serviceType)
    {
        var configFile = Path.Combine(ConfigPath, $"{serviceType.ToLower()}.json");

        // 如果配置文件存在，读取配置
        if (File.Exists(configFile))
        {
            var json = File.ReadAllText(configFile);
            var config = JSON.Deserialize<ServiceConfigDetailOutput>(json);
            return Task.FromResult(config);
        }

        // 配置文件不存在，返回默认配置
        if (DefaultConfigs.TryGetValue(serviceType.ToLower(), out var defaultConfig))
        {
            // 保存默认配置到文件
            File.WriteAllText(configFile, JSON.Serialize(defaultConfig));
            return Task.FromResult(defaultConfig);
        }

        throw Oops.Oh($"不支持的服务类型:{serviceType}");
    }

    /// <summary>
    ///     获取所有支持的服务类型
    /// </summary>
    /// <returns></returns>
    [HttpGet("/serviceConfig/types")]
    public Task<List<dynamic>> GetServiceTypes()
    {
        var output = new List<dynamic>();
        foreach (var (key, defConfig) in DefaultConfigs)
        {
            var configFile = Path.Combine(ConfigPath, $"{key.ToLower()}.json");
            // 如果配置文件存在，读取配置
            if (File.Exists(configFile))
            {
                var json = File.ReadAllText(configFile);

                var config = JSON.Deserialize<ServiceConfigDetailOutput>(json);

                output.Add(new
                {
                    Name = key,
                    config.Enabled
                });
            }
            else
            {
                output.Add(new
                {
                    Name = key,
                    defConfig.Enabled
                });
            }
        }

        return Task.FromResult(output);
    }

    /// <summary>
    ///     禁用/启用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/serviceConfig/enable")]
    public async Task EnableServiceConfig([Required] EnableServiceConfigInput input)
    {
        try
        {
            // 检查服务类型是否支持
            if (!DefaultConfigs.ContainsKey(input.ServiceType.ToLower()))
                throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");

            var configFile = Path.Combine(ConfigPath, $"{input.ServiceType.ToLower()}.json");

            // 更新配置文件
            if (File.Exists(configFile))
            {
                var json = await File.ReadAllTextAsync(configFile);
                var config = JSON.Deserialize<ServiceConfigDetail>(json);

                config.Enabled = input.Enable;
                await File.WriteAllTextAsync(configFile, JSON.Serialize(config));

                // 根据服务类型执行相应的启动/停止操作
                switch (input.ServiceType.ToLower())
                {
                    case "modbus":
                        if (input.Enable)
                        {
                            _modbusServer.Start();
                        }
                        else
                        {
                            _modbusServer.Stop();
                        }
                        break;
                    case "opcua":
                        if (input.Enable)
                        {
                            _uaService.Start();
                        }
                        else
                        {
                            _uaService.Stop();
                        }
                        break;
                        // 可以在这里添加其他服务类型的处理
                }
            }
            else if (DefaultConfigs.TryGetValue(input.ServiceType.ToLower(), out var defaultConfig))
            {
                defaultConfig.Enabled = false;
                await File.WriteAllTextAsync(configFile, JSON.Serialize(defaultConfig));
            }
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"启用/禁用服务失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     更新服务基本配置
    /// </summary>
    /// <param name="input">配置信息</param>
    /// <returns></returns>
    [HttpPost("/serviceConfig/update")]
    public async Task UpdateServiceConfig([Required] ServiceConfigUpdateInput input)
    {
        // 检查服务类型是否支持
        if (!DefaultConfigs.ContainsKey(input.ServiceType.ToLower()))
            throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");

        var configFile = Path.Combine(ConfigPath, $"{input.ServiceType.ToLower()}.json");

        // 读取现有配置或使用默认配置
        ServiceConfigDetail config;
        if (File.Exists(configFile))
        {
            var json = await File.ReadAllTextAsync(configFile);
            config = JSON.Deserialize<ServiceConfigDetail>(json);
        }
        else if (DefaultConfigs.TryGetValue(input.ServiceType.ToLower(), out var defaultConfig))
        {
            config = defaultConfig.Adapt<ServiceConfigDetail>();
        }
        else
        {
            throw Oops.Oh($"无法获取服务类型 {input.ServiceType} 的配置");
        }

        // 更新配置
        config.Enabled = input.Enabled;
        config.MaxConnections = input.MaxConnections;
        config.Timeout = input.Timeout;

        if (input.Config != null)
            config.ExtConfig.Config = input.Config;

        // 保存配置到文件
        await File.WriteAllTextAsync(configFile, JSON.Serialize(config));

        // 如果是modbus服务,发布配置更新事件
        if (input.ServiceType.ToLower() == "modbus")
        {
            var modbusConfig = input.Config as ModbusConfig;
            Dictionary<string, List<ModbusMappingConfig>>? mappingConfig = null;

            if (config.MappingConfig != null)
            {
                mappingConfig = JSON.Deserialize<Dictionary<string, List<ModbusMappingConfig>>>(
                    JSON.Serialize(config.MappingConfig));
            }

            await MessageCenter.PublishAsync("ModbusConfigChanged", (modbusConfig, mappingConfig));
        }

        await MessageCenter.PublishAsync(EventConst.SysLog, $"【更新{input.ServiceType}服务配置】");
    }

    /// <summary>
    ///     获取映射配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/serviceConfig/getMappingConfig")]
    public SqlSugarPagedList<dynamic> GetMappingConfig([FromQuery] GetMappingConfigInput input)
    {
        // 检查服务类型是否支持
        if (!DefaultConfigs.ContainsKey(input.ServiceType.ToLower()))
            throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");

        // 获取配置文件
        var configFile = Path.Combine(ConfigPath, $"{input.ServiceType.ToLower()}.json");

        if (!File.Exists(configFile))
        {
            return CreateEmptyPagedList(input.PageNo, input.PageSize);
        }

        var json = File.ReadAllText(configFile);
        var config = JSON.Deserialize<ServiceConfigDetail>(json);

        // 根据类型
        switch (input.ServiceType.ToLower())
        {
            case "modbus":
                return GetModbusMappingConfig(config, input);
            case "opcua":
                return GetOpcuaMappingConfig(config, input);
            default:
                throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");
        }
    }

    /// <summary>
    ///     创建空的分页列表
    /// </summary>
    /// <param name="pageNo">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>空的分页列表</returns>
    private SqlSugarPagedList<dynamic> CreateEmptyPagedList(int pageNo, int pageSize)
    {
        return new SqlSugarPagedList<dynamic>
        {
            PageNo = pageNo,
            PageSize = pageSize,
            Rows = new List<dynamic>(),
            TotalRows = 0,
            TotalPage = 0,
            HasNextPage = false,
            HasPrevPage = false
        };
    }

    /// <summary>
    ///     获取Modbus映射配置
    /// </summary>
    /// <param name="config">服务配置详情</param>
    /// <param name="input">输入参数</param>
    /// <returns>分页列表</returns>
    private SqlSugarPagedList<dynamic> GetModbusMappingConfig(ServiceConfigDetail config, GetMappingConfigInput input)
    {

        var modbusConfig = JSON.Deserialize<Dictionary<string, List<ModbusMappingConfig>>>(JSON.Serialize(config.MappingConfig));

        // 如果映射配置为空，返回空分页列表
        if (modbusConfig == null)
        {
            return CreateEmptyPagedList(input.PageNo, input.PageSize);
        }

        // 根据字典key过滤分区
        if (!string.IsNullOrEmpty(input.Partition))
        {
            modbusConfig = modbusConfig
                .Where(x => x.Key == input.Partition)
                .ToDictionary(x => x.Key, x => x.Value);
        }

        // 获取所有映射项
        var allModbusItems = new List<dynamic>();
        foreach (var kvp in modbusConfig)
        {
            foreach (var item in kvp.Value)
            {
                var dynamicItem = new
                {
                    Partition = kvp.Key,
                    item.DeviceName,
                    item.VariableName,
                    item.Address,
                    item.DataType,
                };
                allModbusItems.Add(dynamicItem);
            }
        }

        // 应用过滤条件
        allModbusItems = FilterMappingItems(allModbusItems, input.SearchValue, input.DataType);

        // 计算总行数和总页数
        int totalRows = allModbusItems.Count;
        int totalPages = (int)Math.Ceiling(totalRows / (double)input.PageSize);

        // 分页
        var skipCount = (input.PageNo - 1) * input.PageSize;

        var pagedModbusItems = allModbusItems
            .Skip(skipCount)
            .Take(input.PageSize)
            .ToList();

        // 返回分页结果
        var result = new SqlSugarPagedList<dynamic>
        {
            PageNo = input.PageNo,
            PageSize = input.PageSize,
            Rows = pagedModbusItems,
            TotalRows = totalRows,
            TotalPage = totalPages,
            HasNextPage = input.PageNo < totalPages,
            HasPrevPage = input.PageNo > 1
        };

        return result;
    }

    /// <summary>
    ///     获取OpcUa映射配置
    /// </summary>
    /// <param name="config">服务配置详情</param>
    /// <param name="input">输入参数</param>
    /// <returns>分页列表</returns>
    private SqlSugarPagedList<dynamic> GetOpcuaMappingConfig(ServiceConfigDetail config, GetMappingConfigInput input)
    {
        var opcuaConfig = JSON.Deserialize<Dictionary<string, List<OpcuaMappingConfig>>>(JSON.Serialize(config.MappingConfig));

        // 如果映射配置为空，返回空分页列表
        if (opcuaConfig == null)
        {
            return CreateEmptyPagedList(input.PageNo, input.PageSize);
        }
        
        // 根据字典key过滤分区
        if (!string.IsNullOrEmpty(input.Partition))
        {
            opcuaConfig = opcuaConfig
                .Where(x => x.Key == input.Partition)
                .ToDictionary(x => x.Key, x => x.Value);
        }

        // 获取所有映射项
        var allOpcuaItems = new List<dynamic>();
        foreach (var kvp in opcuaConfig)
        {
            foreach (var item in kvp.Value)
            {
                var dynamicItem = new
                {
                    Partition = kvp.Key,
                    item.DeviceName,
                    item.VariableName,
                    item.NodeId,
                    item.DataType,
                    item.DisplayName,
                    item.Description
                };
                allOpcuaItems.Add(dynamicItem);
            }
        }


        // 应用过滤条件
        allOpcuaItems = FilterMappingItems(allOpcuaItems, input.SearchValue, input.DataType);

        // 计算总行数和总页数
        int totalOpcuaRows = allOpcuaItems.Count;
        int totalOpcuaPages = (int)Math.Ceiling(totalOpcuaRows / (double)input.PageSize);

        // 分页
        var skipCount = (input.PageNo - 1) * input.PageSize;

        var pagedOpcuaItems = allOpcuaItems
            .Skip(skipCount)
            .Take(input.PageSize)
            .ToList();

        // 返回分页结果
        var result = new SqlSugarPagedList<dynamic>
        {
            PageNo = input.PageNo,
            PageSize = input.PageSize,
            Rows = pagedOpcuaItems,
            TotalRows = totalOpcuaRows,
            TotalPage = totalOpcuaPages,
            HasNextPage = input.PageNo < totalOpcuaPages,
            HasPrevPage = input.PageNo > 1
        };

        return result;
    }

    /// <summary>
    ///     安全获取动态对象的属性值
    /// </summary>
    /// <param name="item">动态对象</param>
    /// <param name="propertyName">属性名称</param>
    /// <returns>属性值，如果不存在则返回null</returns>
    private string GetDynamicPropertyValue(dynamic item, string propertyName)
    {
        try
        {
            var itemType = item.GetType();
            var property = itemType.GetProperty(propertyName);
            if (property != null)
            {
                var value = property.GetValue(item);
                var result = value?.ToString();
                return result;
            }
            return null;
        }
        catch (Exception ex)
        {
            return null;
        }
    }

    /// <summary>
    ///     过滤映射项
    /// </summary>
    /// <param name="items">映射项列表</param>
    /// <param name="searchValue">搜索值</param>
    /// <param name="dataType">数据类型</param>
    /// <returns>过滤后的列表</returns>
    private List<dynamic> FilterMappingItems(List<dynamic> items, string searchValue, string dataType)
    {

        // 应用搜索过滤
        if (!string.IsNullOrWhiteSpace(searchValue))
        {
            var searchTerm = searchValue.Trim();

            var filteredItems = new List<dynamic>();
            int itemIndex = 0;

            foreach (var item in items)
            {
                itemIndex++;

                // 获取各个属性值
                var deviceName = GetDynamicPropertyValue(item, "DeviceName");
                var variableName = GetDynamicPropertyValue(item, "VariableName");
                var address = GetDynamicPropertyValue(item, "Address");
                var nodeId = GetDynamicPropertyValue(item, "NodeId");
                var dataTypeValue = GetDynamicPropertyValue(item, "DataType");
                var displayName = GetDynamicPropertyValue(item, "DisplayName");
                var description = GetDynamicPropertyValue(item, "Description");

                // 检查每个字段是否匹配
                var deviceNameMatch = !string.IsNullOrEmpty(deviceName) && deviceName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase);
                var variableNameMatch = !string.IsNullOrEmpty(variableName) && variableName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase);
                var addressMatch = !string.IsNullOrEmpty(address) && address.Contains(searchTerm, StringComparison.OrdinalIgnoreCase);
                var nodeIdMatch = !string.IsNullOrEmpty(nodeId) && nodeId.Contains(searchTerm, StringComparison.OrdinalIgnoreCase);
                var dataTypeMatch = !string.IsNullOrEmpty(dataTypeValue) && dataTypeValue.Contains(searchTerm, StringComparison.OrdinalIgnoreCase);
                var displayNameMatch = !string.IsNullOrEmpty(displayName) && displayName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase);
                var descriptionMatch = !string.IsNullOrEmpty(description) && description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase);

                var isMatch = deviceNameMatch || variableNameMatch || addressMatch || nodeIdMatch || dataTypeMatch || displayNameMatch || descriptionMatch;

                if (isMatch)
                {
                    filteredItems.Add(item);
                }
            }

            items = filteredItems;
        }

        // 应用数据类型过滤
        if (!string.IsNullOrWhiteSpace(dataType))
        {
            var beforeDataTypeFilter = items.Count;

            items = items.Where(item =>
            {
                var dataTypeValue = GetDynamicPropertyValue(item, "DataType");
                var match = !string.IsNullOrEmpty(dataTypeValue) &&
                           dataTypeValue.Equals(dataType, StringComparison.OrdinalIgnoreCase);
                return match;
            }).ToList();

        }

        return items;
    }

    /// <summary>
    ///     新增映射配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/serviceConfig/updateMappingConfig")]
    public async Task UpdateMappingConfig([Required] UpdateMappingConfigInput input)
    {
        // 检查服务类型是否支持
        if (!DefaultConfigs.ContainsKey(input.ServiceType.ToLower()))
            throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");

        var configFile = Path.Combine(ConfigPath, $"{input.ServiceType.ToLower()}.json");

        // 读取现有配置或使用默认配置
        ServiceConfigDetail config;
        if (File.Exists(configFile))
        {
            var json = await File.ReadAllTextAsync(configFile);
            config = JSON.Deserialize<ServiceConfigDetail>(json);
        }
        else if (DefaultConfigs.TryGetValue(input.ServiceType.ToLower(), out var defaultConfig))
        {
            config = defaultConfig.Adapt<ServiceConfigDetail>();
        }
        else
        {
            throw Oops.Oh($"无法获取服务类型 {input.ServiceType} 的配置");
        }

        // 根据服务类型处理映射配置
        switch (input.ServiceType.ToLower())
        {
            case "modbus":
                if (input.ModbusMappingConfig != null)
                {
                    // // 生成地址
                    // var newMappings = GenerateModbusAddress(input.ModbusMappingConfig);
                    var newMappings = input.ModbusMappingConfig;
                    // 如果已有映射配置，合并新旧配置
                    if (config.MappingConfig != null)
                    {
                        var existingMappings = JSON.Deserialize<Dictionary<string, List<ModbusMappingConfig>>>(JSON.Serialize(config.MappingConfig));

                        // 合并配置
                        foreach (var (key, values) in newMappings)
                        {
                            if (!existingMappings.TryAdd(key, values))
                            {
                                // 合并列表，避免重复
                                foreach (var mapping in values)
                                {
                                    // 检查是否已存在相同的设备和变量
                                    var existingMapping = existingMappings[key].FirstOrDefault(
                                        m => m.DeviceName == mapping.DeviceName && m.VariableName == mapping.VariableName);

                                    if (existingMapping == null)
                                    {
                                        existingMappings[key].Add(mapping);
                                    }
                                    else
                                    {
                                        // 更新现有映射
                                        existingMapping.DataType = mapping.DataType;
                                        existingMapping.Address = mapping.Address;
                                    }
                                }
                            }
                        }

                        config.MappingConfig = existingMappings;
                    }
                    else
                    {
                        // 直接设置新的映射配置
                        config.MappingConfig = newMappings;
                    }
                }
                break;

            case "opcua":
                if (input.OpcuaMappingConfig != null)
                {
                    // 生成节点ID
                    var newMappings = GenerateOpcuaAddress(input.OpcuaMappingConfig);

                    // 如果已有映射配置，合并新旧配置
                    if (config.MappingConfig != null)
                    {
                        var existingMappings = JSON.Deserialize<Dictionary<string, List<OpcuaMappingConfig>>>(
                            JSON.Serialize(config.MappingConfig));

                        // 合并配置
                        foreach (var (key, values) in newMappings)
                        {
                            if (existingMappings.ContainsKey(key))
                            {
                                // 合并列表，避免重复
                                foreach (var mapping in values)
                                {
                                    // 检查是否已存在相同的设备和变量
                                    var existingMapping = existingMappings[key].FirstOrDefault(
                                        m => m.DeviceName == mapping.DeviceName && m.VariableName == mapping.VariableName);

                                    if (existingMapping == null)
                                    {
                                        existingMappings[key].Add(mapping);
                                    }
                                    else
                                    {
                                        // 更新现有映射
                                        existingMapping.DataType = mapping.DataType;
                                        existingMapping.NodeId = mapping.NodeId;
                                    }
                                }
                            }
                            else
                            {
                                // 添加新的分区
                                existingMappings[key] = values;
                            }
                        }

                        config.MappingConfig = existingMappings;
                    }
                    else
                    {
                        // 直接设置新的映射配置
                        config.MappingConfig = newMappings;
                    }
                }
                break;

            default:
                throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");
        }

        // 保存配置到文件
        await File.WriteAllTextAsync(configFile, JSON.Serialize(config));

        // 如果是modbus服务,发布配置更新事件
        if (input.ServiceType.ToLower() == "modbus")
        {
            var modbusConfig = config.ExtConfig?.Config as ModbusConfig;
            Dictionary<string, List<ModbusMappingConfig>>? mappingConfig = null;

            if (config.MappingConfig != null)
            {
                mappingConfig = JSON.Deserialize<Dictionary<string, List<ModbusMappingConfig>>>(
                    JSON.Serialize(config.MappingConfig));
            }

            await MessageCenter.PublishAsync("ModbusConfigChanged", (modbusConfig, mappingConfig));
        }

        await MessageCenter.PublishAsync(EventConst.SysLog, $"【更新{input.ServiceType}映射配置】");
    }

    /// <summary>
    ///     批量删除映射配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/serviceConfig/deleteMappingConfig")]
    public async Task DeleteMappingConfig([Required] DeleteMappingConfigInput input)
    {
        // 检查服务类型是否支持
        if (!DefaultConfigs.ContainsKey(input.ServiceType.ToLower()))
            throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");

        var configFile = Path.Combine(ConfigPath, $"{input.ServiceType.ToLower()}.json");
        if (!File.Exists(configFile))
        {
            throw Oops.Oh($"服务类型 {input.ServiceType} 的配置文件不存在");
        }

        // 读取现有配置
        var json = await File.ReadAllTextAsync(configFile);
        var config = JSON.Deserialize<ServiceConfigDetail>(json);

        // 如果没有映射配置，直接返回
        if (config.MappingConfig == null)
        {
            return;
        }

        // 根据服务类型处理映射配置
        switch (input.ServiceType.ToLower())
        {
            case "modbus":
                var modbusConfig = JSON.Deserialize<Dictionary<string, List<ModbusMappingConfig>>>(
                    JSON.Serialize(config.MappingConfig));

                // 检查指定的分区是否存在
                if (!modbusConfig.ContainsKey(input.Partition))
                {
                    throw Oops.Oh($"分区 {input.Partition} 不存在");
                }

                // 如果没有指定要删除的项，则删除整个分区
                if (input.Items == null || input.Items.Count == 0)
                {
                    modbusConfig.Remove(input.Partition);
                }
                else
                {
                    // 批量删除指定的映射项
                    foreach (var item in input.Items)
                    {
                        modbusConfig[input.Partition].RemoveAll(m =>
                            m.DeviceName == item.DeviceName && m.VariableName == item.VariableName);
                    }

                    // 如果分区为空，删除分区
                    if (modbusConfig[input.Partition].Count == 0)
                    {
                        modbusConfig.Remove(input.Partition);
                    }
                }

                config.MappingConfig = modbusConfig.Count > 0 ? modbusConfig : null;
                break;

            case "opcua":
                var opcuaConfig = JSON.Deserialize<Dictionary<string, List<OpcuaMappingConfig>>>(
                    JSON.Serialize(config.MappingConfig));

                // 检查指定的分区是否存在
                if (!opcuaConfig.ContainsKey(input.Partition))
                {
                    throw Oops.Oh($"分区 {input.Partition} 不存在");
                }

                // 如果没有指定要删除的项，则删除整个分区
                if (input.Items == null || input.Items.Count == 0)
                {
                    opcuaConfig.Remove(input.Partition);
                }
                else
                {
                    // 批量删除指定的映射项
                    foreach (var item in input.Items)
                    {
                        opcuaConfig[input.Partition].RemoveAll(m =>
                            m.DeviceName == item.DeviceName && m.VariableName == item.VariableName);
                    }

                    // 如果分区为空，删除分区
                    if (opcuaConfig[input.Partition].Count == 0)
                    {
                        opcuaConfig.Remove(input.Partition);
                    }
                }

                config.MappingConfig = opcuaConfig.Count > 0 ? opcuaConfig : null;
                break;

            default:
                throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");
        }

        // 保存配置到文件
        await File.WriteAllTextAsync(configFile, JSON.Serialize(config));

        // 记录日志
        var itemCount = input.Items?.Count ?? 0;
        var logMessage = itemCount > 0
            ? $"【批量删除{input.ServiceType}映射配置】分区:{input.Partition}, 删除{itemCount}项"
            : $"【删除{input.ServiceType}映射配置】分区:{input.Partition}";
        await MessageCenter.PublishAsync(EventConst.SysLog, logMessage);
    }

    /// <summary>
    ///     获取分区列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/serviceConfig/getPartitions")]
    public async Task<PartitionListOutput> GetPartitions([FromQuery] PartitionBaseInput input)
    {
        // 检查服务类型是否支持
        if (!DefaultConfigs.ContainsKey(input.ServiceType.ToLower()))
            throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");

        var configFile = Path.Combine(ConfigPath, $"{input.ServiceType.ToLower()}.json");
        var result = new PartitionListOutput();

        if (File.Exists(configFile))
        {
            var json = await File.ReadAllTextAsync(configFile);
            var config = JSON.Deserialize<ServiceConfigDetail>(json);

            if (config.MappingConfig != null)
            {
                switch (input.ServiceType.ToLower())
                {
                    case "modbus":
                        var modbusConfig = JSON.Deserialize<Dictionary<string, List<ModbusMappingConfig>>>(
                            JSON.Serialize(config.MappingConfig));

                        result.Partitions = modbusConfig.Keys.ToList();
                        foreach (var key in modbusConfig.Keys)
                        {
                            result.ItemCounts[key] = modbusConfig[key].Count;
                        }
                        break;

                    case "opcua":
                        var opcuaConfig = JSON.Deserialize<Dictionary<string, List<OpcuaMappingConfig>>>(
                            JSON.Serialize(config.MappingConfig));

                        result.Partitions = opcuaConfig.Keys.ToList();
                        foreach (var key in opcuaConfig.Keys)
                        {
                            result.ItemCounts[key] = opcuaConfig[key].Count;
                        }
                        break;

                    default:
                        throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");
                }
            }
        }

        return result;
    }


    /// <summary>
    ///     opcua 根据规则生成顺序地址
    /// </summary>
    /// <param name="config"></param>
    /// <returns></returns>
    private Dictionary<string, List<OpcuaMappingConfig>> GenerateOpcuaAddress(Dictionary<string, List<OpcuaMappingConfig>> config)
    {
        foreach (var (key, mappings) in config)
            foreach (var mapping in mappings)
                mapping.NodeId = key + "." + mapping.DeviceName + "." + mapping.VariableName;

        return config;
    }

    /// <summary>
    ///     创建分区
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/serviceConfig/createPartition")]
    public async Task CreatePartition([Required] CreatePartitionInput input)
    {
        // 检查服务类型是否支持
        if (!DefaultConfigs.ContainsKey(input.ServiceType.ToLower()))
            throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");

        var configFile = Path.Combine(ConfigPath, $"{input.ServiceType.ToLower()}.json");

        // 读取现有配置或使用默认配置
        ServiceConfigDetail config;
        if (File.Exists(configFile))
        {
            var json = await File.ReadAllTextAsync(configFile);
            config = JSON.Deserialize<ServiceConfigDetail>(json);
        }
        else if (DefaultConfigs.TryGetValue(input.ServiceType.ToLower(), out var defaultConfig))
        {
            config = defaultConfig.Adapt<ServiceConfigDetail>();
        }
        else
        {
            throw Oops.Oh($"无法获取服务类型 {input.ServiceType} 的配置");
        }

        // 根据服务类型处理映射配置
        switch (input.ServiceType.ToLower())
        {
            case "modbus":
                Dictionary<string, List<ModbusMappingConfig>> modbusConfig;

                if (config.MappingConfig != null)
                {
                    modbusConfig = JSON.Deserialize<Dictionary<string, List<ModbusMappingConfig>>>(
                        JSON.Serialize(config.MappingConfig));

                    // 检查分区是否已存在
                    if (modbusConfig.ContainsKey(input.PartitionName))
                    {
                        throw Oops.Oh($"分区 {input.PartitionName} 已存在");
                    }
                }
                else
                {
                    modbusConfig = new Dictionary<string, List<ModbusMappingConfig>>();
                }

                // 创建新分区
                modbusConfig[input.PartitionName] = new List<ModbusMappingConfig>();
                config.MappingConfig = modbusConfig;
                break;

            case "opcua":
                Dictionary<string, List<OpcuaMappingConfig>> opcuaConfig;

                if (config.MappingConfig != null)
                {
                    opcuaConfig = JSON.Deserialize<Dictionary<string, List<OpcuaMappingConfig>>>(
                        JSON.Serialize(config.MappingConfig));

                    // 检查分区是否已存在
                    if (opcuaConfig.ContainsKey(input.PartitionName))
                    {
                        throw Oops.Oh($"分区 {input.PartitionName} 已存在");
                    }
                }
                else
                {
                    opcuaConfig = new Dictionary<string, List<OpcuaMappingConfig>>();
                }

                // 创建新分区
                opcuaConfig[input.PartitionName] = new List<OpcuaMappingConfig>();
                config.MappingConfig = opcuaConfig;

                // 如果OPC UA服务正在运行，同步创建底层分组节点
                if (_uaService.IsRunning)
                {
                    try
                    {
                        _uaService.CreateDeviceGroup(input.PartitionName);
                    }
                    catch (Exception ex)
                    {
                        // 记录警告但不中断流程，配置仍然保存
                        await MessageCenter.PublishAsync(EventConst.SysLog, $"【警告】创建{input.ServiceType}底层分组节点失败: {ex.Message}");
                    }
                }
                break;

            default:
                throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");
        }

        // 保存配置到文件
        await File.WriteAllTextAsync(configFile, JSON.Serialize(config));

        // 记录日志
        await MessageCenter.PublishAsync(EventConst.SysLog, $"【创建{input.ServiceType}分区】{input.PartitionName}");
    }

    /// <summary>
    ///     重命名分区
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/serviceConfig/renamePartition")]
    public async Task RenamePartition([Required] RenamePartitionInput input)
    {
        // 检查服务类型是否支持
        if (!DefaultConfigs.ContainsKey(input.ServiceType.ToLower()))
            throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");

        // 检查新旧分区名称是否相同
        if (input.OldPartitionName == input.NewPartitionName)
        {
            return; // 名称相同，无需操作
        }

        var configFile = Path.Combine(ConfigPath, $"{input.ServiceType.ToLower()}.json");
        if (!File.Exists(configFile))
        {
            throw Oops.Oh($"服务类型 {input.ServiceType} 的配置文件不存在");
        }

        // 读取现有配置
        var json = await File.ReadAllTextAsync(configFile);
        var config = JSON.Deserialize<ServiceConfigDetail>(json);

        // 如果没有映射配置，直接返回
        if (config.MappingConfig == null)
        {
            throw Oops.Oh($"服务类型 {input.ServiceType} 没有映射配置");
        }

        // 根据服务类型处理映射配置
        switch (input.ServiceType.ToLower())
        {
            case "modbus":
                var modbusConfig = JSON.Deserialize<Dictionary<string, List<ModbusMappingConfig>>>(
                    JSON.Serialize(config.MappingConfig));

                // 检查原分区是否存在
                if (!modbusConfig.ContainsKey(input.OldPartitionName))
                {
                    throw Oops.Oh($"分区 {input.OldPartitionName} 不存在");
                }

                // 检查新分区名称是否已存在
                if (modbusConfig.ContainsKey(input.NewPartitionName))
                {
                    throw Oops.Oh($"分区 {input.NewPartitionName} 已存在");
                }

                // 重命名分区
                var mappings = modbusConfig[input.OldPartitionName];
                modbusConfig.Remove(input.OldPartitionName);
                modbusConfig[input.NewPartitionName] = mappings;

                config.MappingConfig = modbusConfig;
                break;

            case "opcua":
                var opcuaConfig = JSON.Deserialize<Dictionary<string, List<OpcuaMappingConfig>>>(
                    JSON.Serialize(config.MappingConfig));

                // 检查原分区是否存在
                if (!opcuaConfig.ContainsKey(input.OldPartitionName))
                {
                    throw Oops.Oh($"分区 {input.OldPartitionName} 不存在");
                }

                // 检查新分区名称是否已存在
                if (opcuaConfig.ContainsKey(input.NewPartitionName))
                {
                    throw Oops.Oh($"分区 {input.NewPartitionName} 已存在");
                }

                // 如果OPC UA服务正在运行，同步处理底层节点重命名
                if (_uaService.IsRunning)
                {
                    try
                    {
                        // 先删除旧分组，再创建新分组
                        _uaService.DeleteDeviceGroup(input.OldPartitionName);

                        // 创建新分组
                        _uaService.CreateDeviceGroup(input.NewPartitionName);

                        // 如果有变量，重新创建变量
                        var variableMappings = opcuaConfig[input.OldPartitionName];
                        if (variableMappings != null && variableMappings.Any())
                        {
                            // 转换为OpcUaTagMapping
                            var tagMappings = variableMappings.Select(m => new Feng.IotGateway.OpcUaService.Models.OpcUaTagMapping
                            {
                                DeviceName = m.DeviceName,
                                VariableName = m.VariableName,
                                NodeId = m.NodeId,
                                DataType = m.DataType,
                                DisplayName = m.DisplayName,
                                Description = m.Description,
                                Enabled = true
                            }).ToList();

                            _uaService.CreateDeviceVariables(tagMappings, input.NewPartitionName);
                        }
                    }
                    catch (Exception ex)
                    {
                        // 记录警告但不中断流程，配置仍然保存
                        await MessageCenter.PublishAsync(EventConst.SysLog, $"【警告】重命名{input.ServiceType}底层分组节点失败: {ex.Message}");
                    }
                }

                // 重命名分区
                var opcuaMappings = opcuaConfig[input.OldPartitionName];
                opcuaConfig.Remove(input.OldPartitionName);
                opcuaConfig[input.NewPartitionName] = opcuaMappings;

                config.MappingConfig = opcuaConfig;
                break;

            default:
                throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");
        }

        // 保存配置到文件
        await File.WriteAllTextAsync(configFile, JSON.Serialize(config));

        // 记录日志
        await MessageCenter.PublishAsync(EventConst.SysLog, $"【重命名{input.ServiceType}分区】{input.OldPartitionName} -> {input.NewPartitionName}");
    }

    /// <summary>
    ///     删除分区
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/serviceConfig/deletePartition")]
    public async Task DeletePartition([Required] DeletePartitionInput input)
    {
        // 检查服务类型是否支持
        if (!DefaultConfigs.ContainsKey(input.ServiceType.ToLower()))
            throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");

        var configFile = Path.Combine(ConfigPath, $"{input.ServiceType.ToLower()}.json");
        if (!File.Exists(configFile))
        {
            throw Oops.Oh($"服务类型 {input.ServiceType} 的配置文件不存在");
        }

        // 读取现有配置
        var json = await File.ReadAllTextAsync(configFile);
        var config = JSON.Deserialize<ServiceConfigDetail>(json);

        // 如果没有映射配置，直接返回
        if (config.MappingConfig == null)
        {
            return;
        }

        // 根据服务类型处理映射配置
        switch (input.ServiceType.ToLower())
        {
            case "modbus":
                var modbusConfig = JSON.Deserialize<Dictionary<string, List<ModbusMappingConfig>>>(
                    JSON.Serialize(config.MappingConfig));

                // 检查分区是否存在
                if (!modbusConfig.ContainsKey(input.PartitionName))
                {
                    throw Oops.Oh($"分区 {input.PartitionName} 不存在");
                }

                // 删除分区
                modbusConfig.Remove(input.PartitionName);

                // 如果没有分区了，设置为null
                config.MappingConfig = modbusConfig.Count > 0 ? modbusConfig : null;
                break;

            case "opcua":
                var opcuaConfig = JSON.Deserialize<Dictionary<string, List<OpcuaMappingConfig>>>(
                    JSON.Serialize(config.MappingConfig));

                // 检查分区是否存在
                if (!opcuaConfig.ContainsKey(input.PartitionName))
                {
                    throw Oops.Oh($"分区 {input.PartitionName} 不存在");
                }

                // 如果OPC UA服务正在运行，先删除底层分组节点
                if (_uaService.IsRunning)
                {
                    try
                    {
                        _uaService.DeleteDeviceGroup(input.PartitionName);
                    }
                    catch (Exception ex)
                    {
                        // 记录警告但不中断流程，配置仍然删除
                        await MessageCenter.PublishAsync(EventConst.SysLog, $"【警告】删除{input.ServiceType}底层分组节点失败: {ex.Message}");
                    }
                }

                // 删除分区
                opcuaConfig.Remove(input.PartitionName);

                // 如果没有分区了，设置为null
                config.MappingConfig = opcuaConfig.Count > 0 ? opcuaConfig : null;
                break;

            default:
                throw Oops.Oh($"不支持的服务类型:{input.ServiceType}");
        }

        // 保存配置到文件
        await File.WriteAllTextAsync(configFile, JSON.Serialize(config));

        // 记录日志
        await MessageCenter.PublishAsync(EventConst.SysLog, $"【删除{input.ServiceType}分区】{input.PartitionName}");
    }

    /// <summary>
    ///     一键创建OPC UA分组和节点
    /// </summary>
    /// <returns>创建结果</returns>
    [HttpPost("/serviceConfig/createOpcUaNodesFromDevices")]
    public async Task<dynamic> CreateOpcUaNodesFromDevices()
    {
        // 获取所有设备及其点位
        var devices = await _device.AsQueryable()
            .Includes(d => d.DeviceVariable)
            .ToListAsync();

        if (devices == null || !devices.Any())
        {
            throw Oops.Oh("未找到任何设备数据");
        }

        // 读取现有OPC UA配置
        var configFile = Path.Combine(ConfigPath, "opcua.json");
        ServiceConfigDetail config;
        
        if (File.Exists(configFile))
        {
            var json = await File.ReadAllTextAsync(configFile);
            config = JSON.Deserialize<ServiceConfigDetail>(json);
        }
        else if (DefaultConfigs.TryGetValue("opcua", out var defaultConfig))
        {
            config = defaultConfig.Adapt<ServiceConfigDetail>();
        }
        else
        {
            throw Oops.Oh("无法获取OPC UA服务配置");
        }

        // 初始化或清空映射配置
        Dictionary<string, List<OpcuaMappingConfig>> opcuaConfig = new();
        
        // 创建设备分组和变量
        int groupCount = 0;
        int nodeCount = 0;

        foreach (var device in devices)
        {
            if (device.DeviceVariable == null || !device.DeviceVariable.Any())
                continue;

            // 使用设备名称作为分组名
            string groupName = device.DeviceName;
            
            // 创建OPC UA分组
            try
            {
                _uaService.CreateDeviceGroup(groupName);
                groupCount++;
            }
            catch (Exception ex)
            {
                await MessageCenter.PublishAsync(EventConst.SysLog, $"【警告】创建分组 {groupName} 失败: {ex.Message}");
                continue;
            }

            // 为每个设备点位创建变量
            var mappings = new List<OpcuaMappingConfig>();
            var tagMappings = new List<Feng.IotGateway.OpcUaService.Models.OpcUaTagMapping>();

            foreach (var variable in device.DeviceVariable)
            {
                var mapping = new OpcuaMappingConfig
                {
                    DeviceName = device.DeviceName,
                    VariableName = variable.Identifier,
                    NodeId = $"{groupName}.{variable.Identifier}",
                    DataType = MapDataType(variable.TransitionType),
                    DisplayName = variable.Name,
                    Description = variable.Description
                };
                
                mappings.Add(mapping);
                
                // 创建OPC UA标签映射
                tagMappings.Add(new Feng.IotGateway.OpcUaService.Models.OpcUaTagMapping
                {
                    DeviceName = device.DeviceName,
                    VariableName = variable.Identifier,
                    NodeId = mapping.NodeId,
                    DataType = mapping.DataType,
                    DisplayName = mapping.DisplayName,
                    Description = mapping.Description,
                    Enabled = true
                });
                
                nodeCount++;
            }

            // 添加到配置
            opcuaConfig[groupName] = mappings;
            
            // 创建变量
            try
            {
                _uaService.CreateDeviceVariables(tagMappings, groupName);
            }
            catch (Exception ex)
            {
                await MessageCenter.PublishAsync(EventConst.SysLog, $"【警告】为分组 {groupName} 创建变量失败: {ex.Message}");
            }
        }

        // 更新配置
        config.MappingConfig = opcuaConfig;
        await File.WriteAllTextAsync(configFile, JSON.Serialize(config));
        
        // 记录日志
        await MessageCenter.PublishAsync(EventConst.SysLog, $"【一键创建OPC UA节点】创建了 {groupCount} 个分组和 {nodeCount} 个节点");
        
        return new
        {
            Success = true,
            GroupCount = groupCount,
            NodeCount = nodeCount,
            Message = $"成功创建 {groupCount} 个分组和 {nodeCount} 个节点"
        };
    }
    
    /// <summary>
    /// 将设备点位数据类型映射到OPC UA数据类型
    /// </summary>
    /// <param name="dataType">设备点位数据类型</param>
    /// <returns>OPC UA数据类型</returns>
    private string MapDataType(TransPondDataTypeEnum dataType)
    {
        switch (dataType)
        {
            case TransPondDataTypeEnum.Bool:
                return "bool";
            case TransPondDataTypeEnum.Int:
                return "int64";
            case TransPondDataTypeEnum.Double:
                return "double";
            case TransPondDataTypeEnum.String:
            case TransPondDataTypeEnum.Bytes:
            default:
                return "string";
        }
    }
}
