using IotGateway.Application.ServiceConfig.Dtos;
using IotGateway.ModbusServer.Models;
using Microsoft.Extensions.Hosting;

namespace IotGateway.Application.ServiceConfig;

/// <summary>
/// 服务初始化后台服务
/// </summary>
public class ServiceInitializationHostedService : IHostedService
{
  private readonly ModbusServer.ModbusServer _modbusServer;
  private readonly Feng.IotGateway.OpcUaService.UaService _uaService;
  private const string ConfigPath = "/etc/DeviceConf/Configs/";

  public ServiceInitializationHostedService(ModbusServer.ModbusServer modbusServer, Feng.IotGateway.OpcUaService.UaService uaService)
  {
    _modbusServer = modbusServer;
    _uaService = uaService;
  }

  /// <summary>
  /// 
  /// </summary>
  /// <param name="cancellationToken"></param>
  /// <returns></returns>
  public Task StartAsync(CancellationToken cancellationToken)
  {
    try
    {
      // 确保配置目录存在
      if (!Directory.Exists(ConfigPath))
        Directory.CreateDirectory(ConfigPath);

      // 检查 Modbus 服务配置
      var modbusConfigFile = Path.Combine(ConfigPath, "modbus.json");
      if (File.Exists(modbusConfigFile))
      {
        var json = File.ReadAllText(modbusConfigFile);
        var config = JSON.Deserialize<ServiceConfigDetail>(json);

        if (config.Enabled)
        {
          _modbusServer.Start();
        }
      }

      // 检查 OPC UA 服务配置
      var opcuaConfigFile = Path.Combine(ConfigPath, "opcua.json");
      if (File.Exists(opcuaConfigFile))
      {
        var json = File.ReadAllText(opcuaConfigFile);
        var config = JSON.Deserialize<ServiceConfigDetail>(json);

        if (config.Enabled)
        {
          _uaService.Start();
        }
      }
    }
    catch (Exception ex)
    {
      Log.Error($"初始化服务失败: {ex.Message}");
    }

    return Task.CompletedTask;
  }

  public Task StopAsync(CancellationToken cancellationToken)
  {
    return Task.CompletedTask;
  }
}