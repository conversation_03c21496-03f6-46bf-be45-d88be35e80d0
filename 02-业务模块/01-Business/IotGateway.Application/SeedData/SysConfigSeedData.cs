namespace IotGateway.Application.SeedData;

/// <summary>
///     系统配置表种子数据
/// </summary>
public class SysConfigSeedData : ISqlSugarEntitySeedData<SysConfig>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysConfig> HasData()
    {
        return new[]
        {
            new SysConfig {Id = 1300000000201, Name = "服务器配置", Code = ConfigConst.SysFLinkIp, Value = "127.0.0.1"},
            new SysConfig {Id = 1300000000202, Name = "flink服务器是否启动", Code = ConfigConst.SysFLinkOpen, Value = "False"},
            new SysConfig {Id = 1300000000203, Name = "UTC时区", Code = ConfigConst.UtcTime, Value = "True"},
            new SysConfig {Id = 1300000000204, Name = "网关采集是否授权", Code = ConfigConst.Authorization, Value = "True"},
            new SysConfig {Id = 1300000000205, Name = "缓存最大数量", Code = ConfigConst.MaxOffLine, Value = "1000000"},
            new SysConfig {Id = 1300000000206, Name = "默认加载平台logo", Code = ConfigConst.Platform, Value = "edge"},
            new SysConfig {Id = 1300000000207, Name = "是否保存已发送数据", Code = ConfigConst.SaveData, Value = "False"},
            new SysConfig {Id = 1300000000208, Name = "dnc文件存储路径", Code = ConfigConst.DncFilePath, Value = "/Edge/DncFile/"},
            new SysConfig {Id = 1300000000209, Name = "是否允许设备写入", Code = ConfigConst.WhetherToAllowTheDeviceToWrite, Value = "True"},
            new SysConfig {Id = 1300000000210, Name = "flink服务器端口", Code = ConfigConst.SysFLinkPort, Value = "8090"},
        };
    }
}