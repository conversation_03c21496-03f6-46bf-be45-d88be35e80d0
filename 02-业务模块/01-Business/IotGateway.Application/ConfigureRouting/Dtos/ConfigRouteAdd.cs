namespace IotGateway.Application.ConfigureRouting.Dtos;

/// <summary>
///     配置路由-新增 请求参数
/// </summary>
public class ConfigRouteAdd
{
    /// <summary>
    ///     目的地址
    /// </summary>
    [Required]
    public string InsideAddress { get; set; }

    /// <summary>
    ///     子网掩码
    /// </summary>
    [Required]
    public short Genmask { get; set; }

    /// <summary>
    ///     出接口
    /// </summary>
    [Required]
    public string OutInterface { get; set; }

    /// <summary>
    ///     下一跳
    /// </summary>
    public string Via { get; set; }

    /// <summary>
    ///     优先级，值越小优先级越高
    /// </summary>
    public short Metric { get; set; }
}

/// <summary>
///     配置路由-修改 请求参数
/// </summary>
public class ConfigRouteUpdate : ConfigRouteAdd
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}