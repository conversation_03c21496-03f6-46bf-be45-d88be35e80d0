using Common.Extension;
using IotGateway.Application.ConfigureRouting.Dtos;
using IotGateway.Application.Entity;

namespace IotGateway.Application.ConfigureRouting;

/// <summary>
///     配置路由
///     版 本:V3.1.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-06-09
/// </summary>
[ApiDescriptionSettings("网络配置")]
public class ConfigureRoutingService : ITransient, IDynamicApiController
{
    private readonly SqlSugarRepository<ConfigRoute> _configRoute;

    public ConfigureRoutingService(SqlSugarRepository<ConfigRoute> configRoute)
    {
        _configRoute = configRoute;
    }

    /// <summary>
    ///     配置路由-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/configRoute/page")]
    public async Task<SqlSugarPagedList<ConfigRoute>> Page([FromQuery] BasePageInput input)
    {
        var configRoutes = await _configRoute.AsQueryable()
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue), w => w.InsideAddress.Contains(input.SearchValue)
                                                                    || w.OutInterface.Contains(input.SearchValue)
                                                                    || w.Via.Contains(input.SearchValue))
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return configRoutes;
    }

    /// <summary>
    ///     配置路由-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/configRoute/detail")]
    public async Task<ConfigRoute> Detail([FromQuery] BaseId input)
    {
        var configRoute = await _configRoute.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (configRoute == null)
            throw Oops.Oh(ErrorCode.D1002);
        return configRoute;
    }

    /// <summary>
    ///     配置路由-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/configRoute/add")]
    
    public async Task Add(ConfigRouteAdd input)
    {
        var configRoute = input.Adapt<ConfigRoute>();
        var via = configRoute.Via.Trim().IsNull() ? "0.0.0.0" : configRoute.Via.Trim();
        var command = $"ip route add {configRoute.InsideAddress.Trim()}/{configRoute.Genmask} via {via} dev {configRoute.OutInterface}";
        if (configRoute.Metric > 0)
            command += $" metric {configRoute.Metric}";
        configRoute.Command = command;
        await ShellUtil.Bash(command);
        _ = MessageCenter.PublishAsync(EventConst.SysLog, $"【添加路由】 {command}");
        await _configRoute.InsertAsync(configRoute);
    }

    /// <summary>
    ///     配置路由-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/configRoute/update")]
    
    public async Task Update(ConfigRouteUpdate input)
    {
        var configRoute = await _configRoute.GetFirstAsync(f => f.Id == input.Id);
        if (configRoute == null)
            throw Oops.Oh(ErrorCode.D1002);
        configRoute.Metric = input.Metric;
        configRoute.OutInterface = input.OutInterface;
        configRoute.InsideAddress = input.InsideAddress;
        configRoute.Genmask = input.Genmask;
        configRoute.Via = input.Via;

        var delCommand = configRoute.Command.Replace("add", "del");
        await ShellUtil.Bash(delCommand);
        _ = MessageCenter.PublishAsync(EventConst.SysLog, $"【删除路由】 {delCommand}");

        var via = configRoute.Via.Trim().IsNull() ? "0.0.0.0" : configRoute.Via.Trim();
        var command = $"ip route add {configRoute.InsideAddress.Trim()}/{configRoute.Genmask} via {via} dev {configRoute.OutInterface}";
        if (configRoute.Metric > 0)
            command += $" metric {configRoute.Metric}";
        configRoute.Command = command;
        await ShellUtil.Bash(command);
        _ = MessageCenter.PublishAsync(EventConst.SysLog, $"【添加路由】 {command}");
        await _configRoute.UpdateAsync(configRoute);
    }

    /// <summary>
    ///     配置路由-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/configRoute/delete")]
    
    public async Task Delete(BaseId input)
    {
        var configRoute = await _configRoute.GetFirstAsync(f => f.Id == input.Id);
        if (configRoute == null)
            throw Oops.Oh(ErrorCode.D1002);

        var delCommand = configRoute.Command.Replace("add", "del");
        await ShellUtil.Bash(delCommand);
        _ = MessageCenter.PublishAsync(EventConst.SysLog, $"【删除路由】 {delCommand}");
        await _configRoute.DeleteAsync(configRoute);
    }

    /// <summary>
    ///     配置路由-再次执行添加路由
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/configRoute/command")]
    
    public async Task Command(BaseId input)
    {
        var configRoute = await _configRoute.GetFirstAsync(f => f.Id == input.Id);
        if (configRoute == null)
            throw Oops.Oh(ErrorCode.D1002);

        var delCommand = configRoute.Command.Replace("add", "del");
        await ShellUtil.Bash(delCommand);
        _ = MessageCenter.PublishAsync(EventConst.SysLog, $"【尝试删除路由】 {delCommand}");

        await ShellUtil.Bash(configRoute.Command);
        _ = MessageCenter.PublishAsync(EventConst.SysLog, $"【尝试添加路由】 {configRoute.Command}");
    }
}