using YamlDotNet.Serialization;

namespace IotGateway.Equipment.Networks.Dto;

/// <summary>
///     网络配置
/// </summary>
public class NetworkInit
{
    /// <summary>
    /// </summary>
    [YamlMember(Alias = "network", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public Network Network { get; set; }
}

/// <summary>
/// </summary>
public class Network
{
    [YamlMember(Alias = "renderer", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public string Renderer { get; set; }

    [YamlMember(Alias = "ethernets", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public Dictionary<string, NeworkeThernets> Ethernets { get; set; }
}

/// <summary>
/// </summary>
public class NeworkeThernets
{
    [YamlMember(Alias = "addresses", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public List<string> Addresses { get; set; }

    [YamlMember(Alias = "gateway4", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public string Gateway { get; set; }

    [YamlMember(Alias = "nameservers", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public Nameservers nameservers { get; set; }

    [YamlMember(Alias = "dhcp4", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public bool Dhcp { get; set; }
}

/// <summary>
/// </summary>
public class Nameservers
{
    [YamlMember(Alias = "addresses", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public List<string> Addresses { get; set; }
}