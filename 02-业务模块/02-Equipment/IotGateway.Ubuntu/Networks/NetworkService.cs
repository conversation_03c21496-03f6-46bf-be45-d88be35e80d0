using Common.Extension;
using Feng.Common.Extension;
using Feng.IotGateway.Core.Const;
using Feng.IotGateway.Core.Models.Networks;
using IotGateway.Equipment.Networks.Dto;
using Microsoft.AspNetCore.Authorization;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace IotGateway.Equipment.Networks;

/// <summary>
///     网络配置
/// </summary>
public class NetworkService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     保存服务器配置
    /// </summary>
    /// <returns></returns>
    [HttpPost("/network/save")]
    public async Task SetNetWork(NetworkSettingModel input)
    {
        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .Build();
        //读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync(GatewayFilePath.UbuntuNetworkPath);
        //配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        var defNetwork = "";
        foreach (var network in input.Network)
        {
            var neworkeThernets = new NeworkeThernets
            {
                // 是否是dhcp
                Dhcp = network.NetWorkType == NetWorkTypeEnum.Dhcp
            };

            neworkeThernets.Gateway = network.Gateway;
            if (network.NetWorkType != NetWorkTypeEnum.Dhcp)
                //设置成静态ip
                // neworkeThernets.Gateway = network.Gateway;
                if (network.IPAddress.IsNotNull())
                {
                    neworkeThernets.Addresses ??= new List<string>();
                    neworkeThernets.Addresses.Add(network.IPAddress + "/24");
                }

            if (network.Dns.IsNotNull() || network.DnsBack.IsNotNull())
            {
                //如果设置了DNS就修改
                neworkeThernets.nameservers = new Nameservers {Addresses = new List<string>()};
                if (network.Dns.IsNotNull())
                    neworkeThernets.nameservers.Addresses.Add(network.Dns);
                if (network.DnsBack.IsNotNull())
                    neworkeThernets.nameservers.Addresses.Add(network.DnsBack);
            }

            if (network.DefRoute)
            {
                defNetwork = network.NetWorkName;
            }
            networkInit.Network.Ethernets ??= new Dictionary<string, NeworkeThernets>();
            networkInit.Network.Ethernets[network.NetWorkName] = neworkeThernets;
        }

        var serializer = new SerializerBuilder().WithNamingConvention(CamelCaseNamingConvention.Instance).IgnoreFields().Build();
        
        var yaml = serializer.Serialize(networkInit);
        await File.WriteAllTextAsync(GatewayFilePath.UbuntuNetworkPath, yaml);

        // 4g重新拨号
        await ShellUtil.Bash("systemctl restart quectel-ec20.service");
        // 网络生效
        await ShellUtil.Bash("netplan apply");
        // 保存默认网卡名称
        await File.WriteAllTextAsync(GatewayFilePath.DefRoutePath, defNetwork);
    }

    /// <summary>
    ///     获取服务器ip配置列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/list")]
    [AllowAnonymous]
    public async Task<NetworkSettingModel> NetWorkList()
    {
        var deserializer = new DeserializerBuilder().WithNamingConvention(UnderscoredNamingConvention.Instance).Build();
        // 读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync(GatewayFilePath.UbuntuNetworkPath);
        // 配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        var resultData = new NetworkSettingModel
        {
            Network = new List<NetworkInfoModel>(),
            Mobile = new MobileModel
            {
                Apn = "123",
                Password = "123",
                IsApn = true,
                NetworkType = 1,
                OperatorName = "中国电信",
                UserName = "test"
            }
        };
        foreach (var ethernets in networkInit.Network.Ethernets)
        {
            var netWork = new NetworkInfoModel
            {
                NetWorkName = ethernets.Key
            };
            if (ethernets.Value.Dhcp)
            {
                netWork.DefRoute = false;
                netWork.NetWorkType = NetWorkTypeEnum.Dhcp;
            }
            else
            {
                netWork.Gateway = ethernets.Value.Gateway;
                netWork.DefRoute = false;
                netWork.SubnetMark = "*************";
                netWork.IPAddress = ethernets.Value.Addresses.FirstOrDefault()?.Replace("/24", "");
                netWork.NetWorkType = NetWorkTypeEnum.Static;
            }

            if (ethernets.Value.nameservers != null)
            {
                switch (ethernets.Value.nameservers.Addresses.Count)
                {
                    case 2:
                        netWork.Dns = ethernets.Value.nameservers.Addresses[0];
                        netWork.DnsBack = ethernets.Value.nameservers.Addresses[1];
                        break;
                    case 1:
                        netWork.Dns = ethernets.Value.nameservers.Addresses[0];
                        break;
                }
            }

            if (File.Exists(GatewayFilePath.DefRoutePath))
                netWork.DefRoute = await File.ReadAllTextAsync(GatewayFilePath.DefRoutePath) == netWork.NetWorkName;
            resultData.Network.Add(netWork);
        }

        return resultData;
    }
}