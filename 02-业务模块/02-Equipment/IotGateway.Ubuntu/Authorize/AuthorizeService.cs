using Feng.IotGateway.Core.Const;
using IotGateway.Equipment.Util;
using Microsoft.AspNetCore.Authorization;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Equipment.Authorize;

/// <summary>
///     网关授权信息
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2022-10-01
/// </summary>
public class AuthorizeService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     授权
    /// </summary>
    /// <returns></returns>
    [HttpPost("/authorization/set")]
    public async Task<SetAuthorizeOutput> SetAuthorization(BaseId<string> input)
    {
        var privateKey = await File.ReadAllTextAsync(GatewayFilePath.RsaPath);
        var decryptPwd = AuthorizationUtil.Decrypt(input.Id, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() != sn)
            throw new Exception("Sn不一致！");
        // 校验是否到期
        if (DateTime.Now() >= Convert.ToDateTime(authorize.EndTime))
            throw new Exception("已过期！");

        await File.WriteAllTextAsync(GatewayFilePath.KeyPath, input.Id);
        if (!File.Exists(GatewayFilePath.IdentPath))
        {
            await File.WriteAllTextAsync(GatewayFilePath.IdentPath, "fengEdge-200");
            authorize.Ident = "fengEdge-200";
        }
        else
        {
            var readIdent = await File.ReadAllTextAsync(GatewayFilePath.IdentPath);
            authorize.Ident = readIdent ?? "fengEdge-200";
        }

        return authorize;
    }

    /// <summary>
    ///     校验是否授权
    /// </summary>
    /// <returns></returns>
    public async Task<bool> Authorization()
    {
        if (!File.Exists(GatewayFilePath.KeyPath))
            File.CreateText(GatewayFilePath.KeyPath);
        var value = await File.ReadAllTextAsync(GatewayFilePath.KeyPath);
        if (value == null)
            throw Oops.Oh("未授权！");
        var privateKey = await File.ReadAllTextAsync(GatewayFilePath.RsaPath);
        var decryptPwd = AuthorizationUtil.Decrypt(value, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() != sn)
            return false;
        return DateTime.Now() < Convert.ToDateTime(authorize.EndTime);
    }

    /// <summary>
    ///     获取授权信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/getAuthorization")]
    [AllowAnonymous]
    public async Task<SetAuthorizeOutput> GetAuthorization()
    {
        var setAuthor = await AuthorizationUtil.GetAuthorization();
        if (setAuthor != null) return setAuthor;
        var sn = await GatewayUtil.GetSn();
        var auth = new SetAuthorizeOutput
        {
            Fingerprint = "",
            Ident = "fengEdge-200",
            Sn = sn,
            Version = MachineUtil.Version,
            DeviceNumber = 0,
            EndTime = null,
            VariableNumber = 0
        };
        if (!File.Exists(GatewayFilePath.IdentPath)) return auth;
        var readIdent = await File.ReadAllTextAsync(GatewayFilePath.IdentPath);
        auth.Ident = readIdent ?? "fengEdge-200";
        return auth;
    }
}