using System.Dynamic;
using System.Globalization;
using Feng.IotGateway.Core.Const;
using Yitter.IdGenerator;

namespace IotGateway.Equipment.Util;

/// <summary>
///     网关工具
/// </summary>
public static class GatewayUtil
{
    /// <summary>
    ///     获取资源使用信息
    /// </summary>
    /// <returns></returns>
    public static async Task<dynamic> UseInfo()
    {
        var ramInfo = MachineUtil.RamInfo();
        var cpuRate = Math.Ceiling(double.Parse(await CpuRate()));
        var hddInfo = ReadHddInfo();
        var networkList = new List<string> {"eth0", "eth1", "eth2", "wlan0"};
        var ipAddr = new Dictionary<string, string>();
        foreach (var network in networkList)
        {
            var ip = await ReadIp(network);
            if (ip != null)
            {
                ipAddr.Add(network, ip);
                switch (network)
                {
                    case "eth0":
                        MachineUtil.Clay.Eth0 = ip;
                        break;
                    case "eth1":
                        MachineUtil.Clay.Eth1 = ip;
                        break;
                    case "eth2":
                    case "usb0":
                        MachineUtil.Clay.Eth2 = ip;
                        break;
                    case "wlan0":
                        MachineUtil.Clay.Wifi = ip;
                        break;
                }
            }
        }

        MachineUtil.UseInfo = new UserInfo
        {
            Network = ipAddr, //网络ip
            DiskInfo = hddInfo, //磁盘使用
            MemInfo = ramInfo, //内存
            CpuRate = cpuRate // cpu使用率
        };
        return MachineUtil.UseInfo;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public static DiskInfo ReadHddInfo()
    {
        try
        {
            DiskInfo hdd = new DiskInfo();
            var hddInfo = ShellUtil.Bash("df -hk").GetAwaiter().GetResult();
            var lines = hddInfo.Split('\n');
            foreach (var item in lines)
            {
                if (!item.Contains("/dev/root")) continue;
                var li = item.Split(new[] {' '}, StringSplitOptions.RemoveEmptyEntries);
                for (var i = 0; i < li.Length; i++)
                {
                    if (!li[i].Contains("%")) continue;
                    hdd.DiskSize = Convert.ToInt64(li[i - 3]);
                    hdd.DiskUsed = Convert.ToInt64(li[i - 2]);
                    hdd.DiskAvailable = Convert.ToInt64(li[i - 1]);
                    hdd.DiskRate = Convert.ToDouble(li[i].Replace("%", "").Trim());
                    break;
                }
            }
            return hdd;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }

    /// <summary>
    ///     获取CPU使用率
    /// </summary>
    /// <returns></returns>
    public static async Task<string> CpuRate()
    {
        string cpuRate;
        var output = await ShellUtil.Bash("top -b -n1 | grep \"Cpu(s)\" | awk '{print $2}'");
        cpuRate = output.Trim();
        return cpuRate;
    }

    /// <summary>
    ///     获取Sn码
    /// </summary>
    /// <returns></returns>
    public static async Task<string> GetSn()
    {
        string sn;
        if (!File.Exists(GatewayFilePath.SnPath))
        {
            sn = Guid.NewGuid().ToString();
            await File.WriteAllTextAsync(GatewayFilePath.SnPath, $"{sn}");
            return sn;
        }

        sn = await File.ReadAllTextAsync(GatewayFilePath.SnPath);
        return sn;
    }

    /// <summary>
    ///     IfConfig
    /// </summary>
    /// <returns></returns>
    public static async Task<string> IfConfig(string ipName)
    {
        var config = "";
        var hddInfo = await ShellUtil.Bash("ifconfig");
        var isName = false;
        var lines = hddInfo.Split('\n');
        foreach (var item in lines)
        {
            if (item.StartsWith(ipName))
                isName = true;
            if (!isName)
                continue;
            if (item.Contains("inet") && !item.Contains("inet6"))
                config = item;
            if (!item.Contains("ether")) continue;
            config += " " + item;
            return config;
        }

        return config;
    }

    /// <summary>
    ///     获取网关 IP信息
    /// </summary>
    /// <returns></returns>
    private static async Task<string> ReadIp(string networkName)
    {
        var hddInfo = await ShellUtil.Bash("ifconfig");

        var isName = false;

        var lines = hddInfo.Split('\n');
        foreach (var item in lines)
        {
            if (item.StartsWith(networkName))
                isName = true;
            if (!isName) continue;
            if (!item.Contains("inet") && !item.Contains("ether"))
                continue;
            if (item.Contains("ether")) return "无";
            var li = item.Split(new[] {' '}, StringSplitOptions.RemoveEmptyEntries);
            return li[1].Trim();
        }

        return null;
    }
}