using Common.Extension;
using Feng.Common.Extension;
using Feng.IotGateway.Core.Const;
using Feng.IotGateway.Core.Models.Shells;
using Furion.LinqBuilder;
using Furion.Logging;
using Furion.TaskQueue;
using Furion.Templates;
using IotGateway.Equipment.Util;
using Console = System.Console;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Equipment.Shell;

/// <summary>
///     Shell
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2022-10-01
/// </summary>
public class ShellService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     IpAddress
    /// </summary>
    /// <returns></returns>
    [HttpGet("/shell/ipAddress")]
    public async Task<List<string>> Ip()
    {
        var networkNames = new List<string> {"eth0", "eth1"};
        List<string> ipList = new();
        foreach (var networkName in networkNames)
        {
            var config = await GatewayUtil.IfConfig(networkName);
            if (config.IsNullOrEmpty())
            {
                continue;
            }
            config = config.TrimStart();
            
            var ip = config.GetStringBetween("inet", "netmask").Trim();
            var bcast = config.GetStringBetween("broadcast", "ether").Trim();
            var mask = config.GetStringBetween("netmask", "broadcast").Trim();
            var hWaddr = config.GetStringBetween("ether", "txqueuelen").Trim();
            var message = TP.Wrapper(networkName,
                $"##时间## 【{DateTime.NowString()}】",
                $"##IP地址## 【{ip}】",
                $"{(bcast.IsNotNull() ? "##广播地址##" + "【" + bcast + "】" : "")}",
                $"##子网掩码## 【{mask}】",
                $"{(hWaddr.IsNotNull() ? "##Mac地址##" + "【" + hWaddr + "】" : "")}");
            ipList.Add(message);
        }

        return ipList;
    }
}