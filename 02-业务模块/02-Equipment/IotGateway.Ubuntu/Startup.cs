using IotGateway.Equipment.Authorize;
using IotGateway.Equipment.BackgroundServices;
using Microsoft.Extensions.DependencyInjection;

namespace IotGateway.Equipment;

/// <summary>
/// </summary>
[AppStartup(1)]
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        MachineUtil.CurrentSystemEnvironment = CurrentSystemEnvironmentEnum.Ubuntu;
        MachineUtil.Authorize = App.GetService<AuthorizeService>().GetAuthorization().GetAwaiter().GetResult();
        //网关运行状态
        services.AddHostedService<RunStatusTimer>();
    }
}