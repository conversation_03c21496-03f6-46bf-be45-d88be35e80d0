using Console = Feng.Common.Extension.Console;

namespace IotGateway.Equipment.Util;

/// <summary>
///     网关工具
/// </summary>
public static class GatewayUtil
{
    /// <summary>
    ///     获取资源使用信息
    /// </summary>
    /// <returns></returns>
    public static async Task<dynamic> UseInfo()
    {
        var ramInfo = MachineUtil.RamInfo();
        var cpuRate = Math.Ceiling(double.Parse(await CpuRate()));
        var hddInfo = ReadHddInfo();
        var networkList = new List<string> {"eth0", "eth1", "wlan0","usb0"};
        var ipAddr = new Dictionary<string, string>();
        foreach (var network in networkList)
        {
            var ip = await ReadIp(network);
            if (ip != null)
            {
                ipAddr.Add(network, ip);
                switch (network)
                {
                    case "eth0":
                        MachineUtil.Clay.Eth0 = ip;
                        break;
                    case "eth1":
                        MachineUtil.Clay.Eth1 = ip;
                        break;
                    case "wlan0":
                        MachineUtil.Clay.Wifi = ip;
                        break;
                    case "usb0":
                        MachineUtil.Clay.Eth2 = ip;
                        break;
                }
            }
        }
        // Console.WriteLine("ipAddr:"+JSON.Serialize(ipAddr));
        // Console.WriteLine("hddInfo:"+JSON.Serialize(hddInfo));
        // Console.WriteLine("ramInfo:"+JSON.Serialize(ramInfo));
        // Console.WriteLine("cpuRate:"+JSON.Serialize(cpuRate));
        MachineUtil.UseInfo.Network = ipAddr; // 网络ip
        MachineUtil.UseInfo.DiskInfo = hddInfo; // 磁盘使用
        MachineUtil.UseInfo.MemInfo = ramInfo; // 内存
        MachineUtil.UseInfo.CpuRate = cpuRate; // cpu使用率
        return MachineUtil.UseInfo;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    private static DiskInfo ReadHddInfo()
    {
        try
        {
            DiskInfo hdd = null;
            var hddInfo = ShellUtil.Bash("df -hk").GetAwaiter().GetResult();
            var lines = hddInfo.Split('\n');
            foreach (var item in lines)
            {
                if (!item.Contains("/dev/root") && !item.Contains("/dev/mmcblk2p7")) continue;
                var li = item.Split(new[] {' '}, StringSplitOptions.RemoveEmptyEntries);
                for (var i = 0; i < li.Length; i++)
                {
                    if (!li[i].Contains("%")) continue;
                    hdd = new DiskInfo
                    {
                        DiskSize = Convert.ToInt64(li[i - 3]),
                        DiskUsed = Convert.ToInt64(li[i - 2]),
                        DiskAvailable = Convert.ToInt64(li[i - 1]),
                        DiskRate = Convert.ToDouble(li[i].Replace("%", "").Trim())
                    };
                    break;
                }
            }

            return hdd;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }

    /// <summary>
    ///     获取CPU使用率
    /// </summary>
    /// <returns></returns>
    public static async Task<string> CpuRate()
    {
        var output = await ShellUtil.Bash("top -bn1 | grep '%Cpu' | awk '{print $2}'");
        var cpuRate = output.Trim();
        return cpuRate;
    }

    /// <summary>
    ///     获取Sn码
    /// </summary>
    /// <returns></returns>
    public static async Task<string> GetSn()
    {
        // var val = await ShellUtil.Bash("/hogo/utility/hogodevinfotools sn -r");
        var val = await ShellUtil.Bash("hogodevinfotools sn -r");
        var number = val.Replace("read the sn :", "").Trim();
        return number;
    }

    /// <summary>
    ///     IfConfig
    /// </summary>
    /// <returns></returns>
    public static async Task<string> IfConfig(string ipName)
    {
        var hddInfo = await ShellUtil.Bash($"ifconfig {ipName}");
        var lines = hddInfo.Split('\n');
        var output = "";
        
        foreach (var item in lines)
        {
            if (item.Contains("inet "))
            {
                var spList = item.Split(" ", StringSplitOptions.RemoveEmptyEntries);
                if (spList.Length > 4)
                    output += $"addr:{spList[1]} Mask:{spList[3]} Bcast:{spList[5]}";
                else
                    output += $"addr:{spList[1]} Mask:{spList[3]} ";
            }

            if (item.Contains("ether"))
            {
                var spList = item.Split(" ", StringSplitOptions.RemoveEmptyEntries);
                output += $" HWaddr:{spList[1]} ";
                return output;
            }
        }

        return output;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="networkName"></param>
    /// <returns></returns>
    private static async Task<string> ReadIp(string networkName)
    {
        var ifconfigOutput = await ShellUtil.Bash("ifconfig");
        var lines = ifconfigOutput.Split('\n');
        var isTargetNetwork = false;

        foreach (var line in lines)
        {
            // 如果遇到新的网络接口（行开头不是空格或制表符），检查是否是目标网络
            if (!string.IsNullOrWhiteSpace(line) && !line.StartsWith(" ") && !line.StartsWith("\t"))
            {
                if (line.StartsWith(networkName))
                {
                    isTargetNetwork = true;
                    continue;
                }
                else if (isTargetNetwork)
                {
                    // 如果已经在目标网络中，但遇到了新的网络接口，说明目标网络配置结束
                    break;
                }
                else
                {
                    // 还没找到目标网络，继续查找
                    isTargetNetwork = false;
                    continue;
                }
            }

            // 只有在目标网络中才查找IP地址
            if (isTargetNetwork && line.Contains("inet "))
            {
                var parts = line.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length > 1)
                {
                    return parts[1].Trim();
                }
            }
        }

        return null;
    }
}