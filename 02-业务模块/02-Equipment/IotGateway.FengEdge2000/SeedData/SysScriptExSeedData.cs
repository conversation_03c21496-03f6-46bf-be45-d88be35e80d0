using Feng.IotGateway.Core.Entity;
using Feng.IotGateway.Core.SqlSugar;

namespace IotGateway.Equipment.SeedData;

public class SysScriptExSeedData : ISqlSugarEntitySeedData<SysScript>
{
    public IEnumerable<SysScript> HasData()
    {
        return new[]
        {
            #region 实时数据操作

            new SysScript
            {
                Id = 470815679512700, Name = "查询-返回单条数据", Content = "data.First('select * from [设备名] where xxx ...');", ScriptType = SysScriptTypeEnum.OnLine,
                Describe =
                    "// 方法:data.First()\n// 参数: 'Select * from JCJ75MAX5 limit 10' 自定义查询SQL语句\n// 返回类型:List<Dictionary<string,object>> 集合，根据设备实际点位返回\n// 描述:该方法用于使用自定义的SQL查询数据返回单条,如果无数据返回空对象{}\nvar getData = data.First('Select * from JCJ75MAX5 limit 10');\nreturn  getData"
            },
            new SysScript
            {
                Id = 470815679512701, Name = "查询-返回多条数据", Content = "data.Select('select * from [设备名] where xxx ...');", ScriptType = SysScriptTypeEnum.OnLine,
                Describe =
                    "// 方法:data.Select()\n// 参数: 'Select * from JCJ75MAX5 limit 10' 自定义查询SQL语句\n// 返回类型:List<Dictionary<string,object>> 集合，根据设备实际点位返回\n// 描述:该方法用于使用自定义的SQL查询集合数据,如果无数据返回空数组[]\nvar getData = data.Select('Select * from JCJ75MAX5 limit 10');\nreturn  getData"
            },
            new SysScript
            {
                Id = 470815679512702, Name = "查询-返回固定格式", Content = "data.Columns('select * from [设备名] where xxx ...');", ScriptType = SysScriptTypeEnum.OnLine,
                Describe =
                    "// 方法:data.Columns()\n// 参数: 'Select * from JCJ75MAX5 limit 10' 自定义查询SQL语句\n// 返回类型:List<Dictionary<string,object>> 集合，根据设备实际点位返回\n// 描述:该方法用于使用自定义的SQL查询数据返回固定格式(字段和值分开返回),如果无数据返回空数组\nvar getData = data.Columns('Select * from JCJ75MAX5 limit 10');\nreturn  getData\n\n// 返回示例结果\n{\n  \"Columns\": [\n    \"ts\",\n    \"online\",\n    \"product\",\n    \"alarm\",\n    \"devicestatus\",\n    \"dayproduct\"\n  ],\n  \"Rows\": [\n    [\n      \"2023-10-13 16:46:49:092\",\n      true,\n      69362,\n      false,\n      2,\n      -2146\n    ],\n    [\n      \"2023-10-13 16:46:50:138\",\n      true,\n      69363,\n      false,\n      2,\n      -2145\n    ]\n  ]\n}"
            },
            new SysScript
            {
                Id = 470815679512703, Name = "查询-返回最近一条数据", Content = "data.Last(['NS99','NS98'...],['product','status'...]);", ScriptType = SysScriptTypeEnum.OnLine,
                Describe =
                    "// 方法:data.Columns()\n// 参数一: deviceNameList 查询设备名称集合\n// 参数二: properList 非必填，查询设备属性字段集合，不填则返回全部字段\n// 返回类型:List<Dictionary<string,object>> 集合，根据设备实际点位返回\n\n// 重载方法：\n// 参数一：deviceName 查询设备名称(单个,非集合,字符串)\n// 参数二: properList 非必填，查询设备属性字段集合，不填则返回全部字段\n// 描述:该方法查询设备的最近一条记录，返回对象格式\n\n// 查询设备名称集合\nvar deviceNameList =['JCJ75MAX5','NS98'];\n// 查询属性集合\nvar properList = ['product','dayproduct'];\nvar getData = data.Last(deviceNameList,properList);\nreturn  getData\n\n//示例返回格式\n{\n  \"JCJ75MAX5\": {\n    \"ts\": 1697709458401,\n    \"product\": 23503,\n    \"dayproduct\": 9471\n  },\n  \"NS98\": {}\n}"
            },
            new SysScript
            {
                Id = 470815679512704, Name = "查询-返回历史数据", Content = "data.Historical(['NS99','NS98'...],1697693011238,1697696611238,20,['product','status'...]);", ScriptType = SysScriptTypeEnum.OnLine,
                Describe =
                    "// 方法:data.Historical()\n// 参数一: deviceNameList 查询设备名称集合\n// 参数二：1697706166024 开始时间戳\n// 参数三：1697709766025 结束时间戳\n// 参数四：100 返回数量\n// 参数五: properList 非必填，查询设备属性字段集合，不填则返回全部字段\n// 返回类型:List<Dictionary<string,object>> 集合，根据设备实际点位返回\n\n// 重载方法：\n// 参数一: deviceName 查询设备名称(单个,非集合,字符串)\n// 参数二：1697706166024 开始时间戳\n// 参数三：1697709766025 结束时间戳\n// 参数四：100 返回数量\n// 参数五: properList 非必填，查询设备属性字段集合，不填则返回全部字段\n// 描述:该方法查询设备的隶属数据记录，返回对象格式\n\n// 查询设备名称集合\nvar deviceNameList =['JCJ75MAX5','NS98'];\n// 查询属性集合\nvar properList = ['product','dayProduct'];\nvar getData = data.Historical(deviceNameList,1697706166024,1697709766025,100, properList);\nreturn  getData\n\n//示例返回格式\n{\n  \"JCJ75MAX5\": [\n    {\n      \"ts\": 1697708120536,\n      \"product\": 22326,\n      \"dayproduct\": 8096\n    },\n    {\n      \"ts\": 1697708122936,\n      \"product\": 22327,\n      \"dayproduct\": 8097\n    }\n  ],\n  \"NS98\": []\n}\n"
            },
          
            new SysScript
            {
                Id = 470815679512705, Name = "查询-时间窗口数据", Content = "data.Window(['NS99','NS98'...],['product','status'...],1697693011238,1697696611238);", ScriptType = SysScriptTypeEnum.OnLine,
                Describe =
                    "// 方法:data.Window()\n// 参数一: deviceNameList 查询设备名称集合\n// 参数二: properList 参与统计的设备属性字段，\n// 参数三：1697706166024 开始时间戳\n// 参数四：1697709766025 结束时间戳\n// 返回类型:Dictionary<string,List<string,Object>>\n// 描述:该方法通常用来计算某个值持续时间,返回值的最早开始时间和结束时间，持续时间和持续次数\n\n// 查询设备名称集合\nvar deviceNameList =['JCJ75MAX5','NS98'];\n// 查询属性集合\nvar properList = ['dayProduct'];\nvar getData = data.Window(deviceNameList,properList,1697706736024,1697709766025);\nreturn  getData\n\n//示例返回格式\n{\n  \"JCJ75MAX5\": {\n    \"dayProduct\": [\n      {\n        \"fst\": 1697708120536,\n        \"ent\": 1697708120536,\n        \"totaltime\": 0,\n        \"cnt\": 1,\n        \"dayproduct\": 8096\n      },\n      {\n        \"fst\": 1697708122936,\n        \"ent\": 1697708122936,\n        \"totaltime\": 0,\n        \"cnt\": 1,\n        \"dayproduct\": 8097\n      }\n\t  ...\n    ]\n  },\n  \"NS98\": {\n    \"dayProduct\": []\n  }\n}\n"
            },
            
            new SysScript
            {
                Id = 470815679512706, Name = "查询-聚合数据", Content = "data.Aggregated(['NS99','NS98'...],['product','status'...],'Max','5s',1697693011238,1697696611238,100);", ScriptType = SysScriptTypeEnum.OnLine,
                Describe =
                    "// 方法:data.Aggregated()\n// 参数一: deviceNameList 查询设备名称集合\n// 参数二: properList 参与统计的设备属性字段，\n// 参数三：'Max' 聚合函数 （支持函数 FIRST LAST COUNT MODE MAX MIN AVG SUM STDDEV SPREAD  字段举例: avg, 表示平均数; 属性为String、Json、Binary类型仅支持FIRST、LAST和COUNT聚集函数）\n// 参数四：'5s' 聚合间隔，按多长时间维度聚合数据，支持s,m,h,d,w \n// 参数五：1697706166024 开始时间戳\n// 参数六：1697709766025 结束时间戳\n// 参数七：2 返回条数(部分函数不生效)\n// 返回类型:Dictionary<string,List<string,Object>> \n// 描述:该方法通常用聚合函数获取一些数据\n\n// 查询设备名称集合\nvar deviceNameList =['JCJ75MAX5','NS98'];\n// 查询属性集合\nvar properList = ['dayProduct'];\nvar getData = data.Aggregated(deviceNameList,properList,'Max','5s',1697706736024,1697709766025,2);\nreturn  getData\n\n//示例返回格式\n{\n  \"JCJ75MAX5\": [\n    {\n      \"time\": 1697708120000,\n      \"dayproduct\": 8098\n    },\n    {\n      \"time\": 1697708125000,\n      \"dayproduct\": 8105\n    }\n  ]\n}"
            },
            #endregion 实时数据操作
                
            #region 示例代码

            // new SysScript
            // {
            //     Id = 473152550518981, Name = "设备在线状态",
            //     Content =
            //         "// 获取当前设备的连接状态,返回Bool值\nvar status = device.Status('${this.DeviceName}');\nif (status) {\n  // 如果status == true 代表在线,则返回2(根据实际情况调整)\n  return 2;\n} else {\n  // 假设1代表关机\n  return 1;\n}",
            //     ScriptType = SysScriptTypeEnum.Template, Describe = "当前设备的连接状态,示例 在线返回2;关机则返回1", Method = ScriptMethodTypeEnum.Template
            // },
            
            #endregion 示例代码
        };
    }
}