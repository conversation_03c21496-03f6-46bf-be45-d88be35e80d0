using System.Net;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Equipment.BackgroundServices;

/// <summary>
///     局域网Udp服务
/// </summary>
public class UdpClientTimer : BackgroundService
{
    private readonly ILogger<UdpClientTimer> _logger;
    private readonly Crontab _crontab;
    private readonly CancellationTokenSource _tokenSource = new();

    /// <summary>
    /// </summary>
    private readonly Socket _sock;

    /// <summary>
    /// </summary>
    private readonly IPEndPoint _iep;

    /// <summary>
    /// </summary>
    private string _ip;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    public UdpClientTimer(ILogger<UdpClientTimer> logger)
    {
        _logger = logger;
        _crontab = Crontab.Parse("0/10 * * ? * *", CronStringFormat.WithSeconds);
        Task.Factory.StartNew(ReceiveBroadcast);

        _sock = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
        _iep = new IPEndPoint(IPAddress.Broadcast, 56789);
        _sock.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.Broadcast, 1);
    }

    /// <summary>
    /// </summary>
    public override void Dispose()
    {
        _tokenSource.Cancel();
        _sock.Close();
    }

    /// <summary>
    ///     获取当前默认路由
    /// </summary>
    /// <returns></returns>
    private async Task<string> Route()
    {
        var routeList = await ShellUtil.Bash("route");
        var lines = routeList.Split('\n');
        // 
        var lastRoute = lines[2].Split(" ");
        return lastRoute[^1];
    }

    /// <summary>
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var taskFactory = new TaskFactory(TaskScheduler.Current);
            await taskFactory.StartNew(async () =>
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                    try
                    {
                        var route = await Route();
                        switch (route)
                        {
                            case "eth0":
                                _ip = MachineUtil.Clay.Eth0;
                                break;
                            case "eth1":
                                _ip = MachineUtil.Clay.Eth1;
                                break;
                            case "wlan0":
                                _ip = MachineUtil.Clay.Wifi;
                                break;
                        }

                        var publishMsg = JSON.Serialize(new MachineUtil.PublishGatewayMessage {Ip = _ip.Trim(), Version = MachineUtil.Version, PublishTime = DateTime.ShangHai()});
                        var data = Encoding.ASCII.GetBytes(publishMsg);
                        _sock.SendTo(data, _iep);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"局域网定时发送消息 Error:【{ex.Message}】");
                    }
            }, stoppingToken);
            await Task.Delay((int) _crontab.GetSleepMilliseconds(DateTime.Now()), stoppingToken);
        }
    }

    /// <summary>
    ///     接收局域网中的消息
    /// </summary>
    private void ReceiveBroadcast()
    {
        var socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
        var receiveIep = new IPEndPoint(IPAddress.Any, 56789);
        EndPoint ep = receiveIep;
        socket.Bind(receiveIep);
        while (!_tokenSource.IsCancellationRequested)
            try
            {
                var buffer = new byte[1024];
                socket.ReceiveFrom(buffer, ref ep);
                var message = Encoding.ASCII.GetString(buffer).Trim();
                // 去掉 Unicode 码位为 \u0000 的字符
                var result = Regex.Replace(message, @"\u0000", "");
                var gatewayMessage = JSON.Deserialize<MachineUtil.PublishGatewayMessage>(result);
                if (gatewayMessage.Ip == null) continue;
                // ip是否有效
                var flag = IPAddress.TryParse(gatewayMessage.Ip, out _);
                if (!flag || _ip == gatewayMessage.Ip) continue;
                MachineUtil.LanGatewayDevice[gatewayMessage.Ip] = gatewayMessage;
            }
            catch (Exception exception)
            {
                _logger.LogError("【局域网Udp】 收到异常消息" + exception.Message);
            }
    }
}