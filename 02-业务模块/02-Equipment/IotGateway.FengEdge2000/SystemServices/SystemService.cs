using System.Text.RegularExpressions;
using Common.Extension;
using Microsoft.AspNetCore.Authorization;
using Console = Feng.Common.Extension.Console;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Equipment.SystemServices;

/// <summary>
///     网关默认配置
/// </summary>
[ApiDescriptionSettings("网关配置")]
public class SystemService : IDynamicApiController, ITransient
{
    private const string BasePath = "/etc/DeviceConf/";

    #region 网关授权

    /// <summary>
    ///     授权
    /// </summary>
    /// <returns></returns>
    [HttpPost("/authorization/set")]
    public async Task<SetAuthorizeOutput> SetAuthorization(BaseId<string> input)
    {
        var privateKey = await File.ReadAllTextAsync(BasePath + "RSA.Private");
        var decryptPwd = AuthorizationUtil.Decrypt(input.Id, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() != sn)
            throw new Exception("Sn不一致！");
        // 校验是否到期
        if (DateTime.Now() >= Convert.ToDateTime(authorize.EndTime))
            throw new Exception("已过期！");
        await File.WriteAllTextAsync(BasePath + "key.txt", input.Id);
        if (!File.Exists(BasePath + "Ident.txt"))
        {
            await File.WriteAllTextAsync(BasePath + "Ident.txt", "fengEdge-2000");
            authorize.Ident = "fengEdge-2000";
        }
        else
        {
            var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
            authorize.Ident = readIdent ?? "fengEdge-2000";
        }

        return authorize;
    }

    /// <summary>
    ///     校验是否授权
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/isAuthorization")]
    public async Task<bool> Authorization()
    {
        var value = await File.ReadAllTextAsync(BasePath + "key.txt");
        if (value == null)
            throw Oops.Oh("未授权！");
        var privateKey = await File.ReadAllTextAsync(BasePath + "RSA.Private");
        var decryptPwd = AuthorizationUtil.Decrypt(value, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() == sn.Trim()) return DateTime.Now() < Convert.ToDateTime(authorize.EndTime);
        Log.Information($"sn:【{authorize.Sn}】,getSn:【{sn}】");
        return false;
    }

    /// <summary>
    ///     获取授权信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/getAuthorization")]
    [AllowAnonymous]
    public async Task<SetAuthorizeOutput> GetAuthorization()
    {
        var setAuthor = await AuthorizationUtil.GetAuthorization();
        if (setAuthor != null) return setAuthor;
        //如果无法获取到授权信息，默认自行组装
        var sn = await GatewayUtil.GetSn();
        var auth = new SetAuthorizeOutput
        {
            Fingerprint = "",
            Ident = "fengEdge-2000",
            Sn = sn,
            Version = "V" + MachineUtil.Version,
            DeviceNumber = 0,
            EndTime = null,
            VariableNumber = 0
        };
        if (!File.Exists(BasePath + "Ident.txt")) return auth;
        var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
        auth.Ident = readIdent ?? "fengEdge-2000";
        return auth;
    }

    #endregion

    #region 网络配置

    /// <summary>
    ///     wifi扫描
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/wifi/scan")]
    [AllowAnonymous]
    public async Task<dynamic> WifiScan()
    {
        var output = new Dictionary<string, object>();

        // 执行wifi扫描命令
        var textResult = await ShellUtil.Bash("iwlist wlan0 scan | grep -E \"ESSID|Quality\"");
        var textSplit = textResult.Split("\n");

        var ssid = "";
        foreach (var text in textSplit)
            try
            {
                var setText = text.TrimStart().TrimEnd();
                if (setText.StartsWith("ESSID"))
                {
                    const string pattern = "ESSID:\"(.*?)\"";
                    var match = Regex.Match(setText, pattern);
                    if (match.Success)
                    {
                        var result = match.Groups[1].Value;
                        ssid = result.StartsWith("\\x") ? Encoding.UTF8.GetString(StringToByteArray(result)) : result;
                    }
                }

                if (setText.StartsWith("Quality"))
                {
                    const string pattern = "Quality:(.*?) Signal level";
                    var match = Regex.Match(setText, pattern);
                    if (!match.Success) continue;
                    var result = match.Groups[1].Value.Trim();
                    if (string.IsNullOrEmpty(ssid)) continue;
                    var resultSp = result.Split('/');
                    output.Add(ssid, new
                    {
                        MaxSignal = resultSp[1],
                        CurrentSignal = resultSp[0]
                    });
                }
            }
            catch
            {
                // ignored
            }

        return output;
    }

    /// <summary>
    ///     linux 不支持中文转义中文
    /// </summary>
    /// <param name="hex"></param>
    /// <returns></returns>
    private static byte[] StringToByteArray(string hex)
    {
        hex = hex.Replace("\\x", ""); // 去除 "\\x" 字符
        var result = new byte[hex.Length / 2];

        for (var i = 0; i < hex.Length; i += 2) result[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16); // 16进制转换为字节

        return result;
    }

    /// <summary>
    ///     获取网络配置文件内容
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/getNetWork")]
    [AllowAnonymous]
    public async Task<byte[]> GetNetWork()
    {
        return await File.ReadAllBytesAsync("/etc/netplan/01-eth.yaml");
    }

    /// <summary>
    ///     写入网络配置文件内容
    /// </summary>
    /// <returns></returns>
    [HttpPost("/network/setNetWork")]
    [AllowAnonymous]
    public async Task SetNetWork(BaseId<byte[]> input)
    {
        try
        {
            if (input.Id is {Length: > 0})
            {
                await ShellUtil.Bash("cp /etc/netplan/01-eth.yaml /etc/netplan/01-eth.yaml-back");
                await File.WriteAllBytesAsync("/etc/netplan/01-eth.yaml", input.Id);
            }
        }
        catch (Exception e)
        {
            Log.Error($"【写入网络配置文件】 Error:【{e.Message}】");
        }
    }

    #region 改文件方式修改ip

    /// <summary>
    ///     保存服务器配置
    /// </summary>
    /// <returns></returns>
    [HttpPost("/network/save")]
    [AllowAnonymous]
    public async Task SetNetWork(NetworkSettingModel input)
    {
        var defNetwork = await SaveNetWork(input);
        await SaveWifiNetWork(input);
        // 开启wifi
        if (input.Wifi is {Enable: true, DefRoute: true})
            defNetwork = "wlan0";
        // 将4G,5G配置写入到文件中
        var jsonfile = Path.Combine("/Edge/"); //文件路径
        await File.WriteAllTextAsync(jsonfile + "mobileConfig.txt", JSON.Serialize(input.Mobile));
        // 网络生效
        await ShellUtil.Bash("netplan apply");
        // 保存默认网卡名称
        await File.WriteAllTextAsync(GatewayFilePath.DefRoutePath, defNetwork);
    }

    /// <summary>
    ///     修改网络配置
    /// </summary>
    /// <param name="input"></param>
    private async Task<string> SaveNetWork(NetworkSettingModel input)
    {
        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .Build();

        // 读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync("/etc/netplan/01-eth.yaml");
        // 配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        var defNetwork = "";
        foreach (var network in input.Network)
        {
            var networkThereNets = new NetworkThereNets
            {
                // 是否是dhcp
                Dhcp = network.NetWorkType == NetWorkTypeEnum.Dhcp ? "yes" : null,
                Gateway = null
            };

            if (network.NetWorkType != NetWorkTypeEnum.Dhcp)
                // 设置成静态ip
                // networkThereNets.Gateway = network.Gateway;
                if (network.IPAddress.IsNotNull())
                {
                    networkThereNets.Addresses ??= new List<string>();
                    var subnetSize = StringExtension.GetSubnetSize(network.SubnetMark);
                    networkThereNets.Addresses.Add(network.IPAddress + $"/{subnetSize}");
                }

            if (network.Dns.IsNotNull() || network.DnsBack.IsNotNull())
            {
                // 设置了DNS就修改
                networkThereNets.nameservers = new Nameservers {Addresses = new List<string>()};
                if (network.Dns.IsNotNull())
                    networkThereNets.nameservers.Addresses.Add(network.Dns);
                if (network.DnsBack.IsNotNull())
                    networkThereNets.nameservers.Addresses.Add(network.DnsBack);
            }

            networkThereNets.Routes ??= new List<Routes>();
            var routes = new Routes
            {
                To = "0.0.0.0/0",
                Via = network.Gateway,
                Metric = network.NetWorkName == "eth1" ? "101" : "102"
            };
            if (network.DefRoute)
            {
                defNetwork = network.NetWorkName;
                routes.Metric = "100";
            }

            networkThereNets.Routes.Add(routes);
            networkInit.Network.Ethernets ??= new Dictionary<string, NetworkThereNets>();
            networkInit.Network.Ethernets[network.NetWorkName] = networkThereNets;
        }

        var serializer = new SerializerBuilder()
            .WithNamingConvention(CamelCaseNamingConvention.Instance)
            .IgnoreFields()
            .Build();
        var yaml = serializer.Serialize(networkInit);
        await File.WriteAllTextAsync("/etc/netplan/01-eth.yaml", yaml);

        return defNetwork;
    }

    /// <summary>
    ///     修改Wifi网络配置
    /// </summary>
    /// <param name="input"></param>
    private async Task SaveWifiNetWork(NetworkSettingModel input)
    {
        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .Build();

        // 读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync("/etc/netplan/02-wifi.yaml");
        // var networkYaml = await Files.ReadAllTextAsync("D:\\02-wifi.yaml");
        //配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        // wifi开启
        if (input.Wifi.Enable)
        {
            var network = input.Wifi;
            var networkThereNets = new WifiNetworkThereNets
            {
                //是否是dhcp
                Dhcp = network.NetWorkType == NetWorkTypeEnum.Dhcp ? "yes" : null,
                Gateway = null,
                AccessPoints = new Dictionary<string, AccessPoint>
                {
                    {
                        network.UserName, new AccessPoint
                        {
                            Password = network.Password
                        }
                    }
                },
                Dhcp4Overrides = new Dhcp4Overrides
                {
                    RouteMetric = network.DefRoute ? "100" : "600"
                },
                Optional = true
            };

            if (network.NetWorkType != NetWorkTypeEnum.Dhcp)
                // 设置成静态ip
                // networkThereNets.Gateway = network.Gateway;
                if (network.IPAddress.IsNotNull())
                {
                    networkThereNets.Addresses ??= new List<string>();
                    var subnetSize = StringExtension.GetSubnetSize(network.SubnetMark);
                    networkThereNets.Addresses.Add(network.IPAddress + $"/{subnetSize}");
                }

            if ((network.Dns.IsNotNull() || network.DnsBack.IsNotNull()) && network.NetWorkType != NetWorkTypeEnum.Dhcp)
            {
                // 设置了DNS就修改
                networkThereNets.nameservers = new Nameservers {Addresses = new List<string>()};
                if (network.Dns.IsNotNull())
                    networkThereNets.nameservers.Addresses.Add(network.Dns);
                if (network.DnsBack.IsNotNull())
                    networkThereNets.nameservers.Addresses.Add(network.DnsBack);
            }

            networkThereNets.Routes ??= new List<Routes>();
            if (network.NetWorkType != NetWorkTypeEnum.Dhcp)
            {
                var routes = new Routes
                {
                    To = "0.0.0.0/0",
                    Via = network.Gateway,
                    Metric = network.DefRoute ? "100" : "600"
                };

                networkThereNets.Routes.Add(routes);
            }

            networkInit.Network.Wifis ??= new Dictionary<string, WifiNetworkThereNets>();
            networkInit.Network.Wifis[network.NetWorkName] = networkThereNets;
        }

        var serializer = new SerializerBuilder()
            .WithNamingConvention(CamelCaseNamingConvention.Instance)
            .IgnoreFields()
            .Build();
        var yaml = serializer.Serialize(networkInit);

        // await Files.WriteAllTextAsync("D:\\02-wifi.yaml", yaml);
        await File.WriteAllTextAsync("/etc/netplan/02-wifi.yaml", yaml);
        await WriteWifiStatus(input.Wifi is {Enable: true} ? 0 : 1);
    }

    /// <summary>
    ///     获取服务器ip配置列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/list")]
    [AllowAnonymous]
    public async Task<NetworkSettingModel> NetWorkList()
    {
        var output = new NetworkSettingModel
        {
            Network = new List<NetworkInfoModel>(),
            Mobile = new MobileModel
            {
                NetworkType = 1,
                OperatorName = "中国电信",
                Enable = false
            }
        };
        
        output.Network = await GetNetWorkList();
        output.Wifi = await GetWifiNetWork();
        
        // 读取wifi状态
        var wifiStatus = await ReadWifiStatus();
        output.Wifi.Enable = false;
        if (wifiStatus.IsNotNullOrWhiteSpace())
            if (wifiStatus.Contains("wifi=1"))
                output.Wifi.Enable = true;
        
        var mobileConfig = await ReadMobileConfig();
        if (!mobileConfig.IsNotNullOrWhiteSpace()) return output;
        output.Mobile = JSON.Deserialize<MobileModel>(mobileConfig);
        return output;
    }
    /// <summary>
    ///     Wifi Status
    /// </summary>
    /// <param name="type"></param>
    private async Task WriteWifiStatus(int type)
    {
        var jsonfile = Path.Combine("/Edge/"); //文件路径
        var content = "wifi=1";
        if (type == 1)
            await File.WriteAllTextAsync(jsonfile + "wifi.txt", "");
        else
            await File.WriteAllTextAsync(jsonfile + "wifi.txt", content);
    }

    /// <summary>
    ///     读取wifi的状态
    /// </summary>
    /// <returns>JSON文件中的value值</returns>
    private async Task<string> ReadWifiStatus()
    {
        var path = Path.Combine("/Edge/wifi.txt"); //文件路径
        if (!File.Exists(path))
            File.CreateText(path);
        using var file = File.OpenText(path);
        var jsonData = await file.ReadToEndAsync();
        file.Dispose();
        return jsonData;
    }
    
    /// <summary>
    ///     获取网络配置
    /// </summary>
    /// <returns></returns>
    private async Task<List<NetworkInfoModel>> GetNetWorkList()
    {
        var output = new List<NetworkInfoModel>();
        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .Build();
        //读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync("/etc/netplan/01-eth.yaml");
        // var networkYaml = await Files.ReadAllTextAsync("D:\\02-wifi.yaml");
        //配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        foreach (var ethernets in networkInit.Network.Ethernets)
        {
            var netWork = new NetworkInfoModel
            {
                NetWorkName = ethernets.Key
            };
            if (ethernets.Value.Dhcp != null)
            {
                netWork.DefRoute = false;
                netWork.NetWorkType = NetWorkTypeEnum.Dhcp;
            }
            else
            {
                netWork.Gateway = ethernets.Value.Gateway ?? ethernets.Value.Routes.FirstOrDefault()?.Via;
                netWork.DefRoute = false;
                var ip = ethernets.Value.Addresses.FirstOrDefault() ?? string.Empty;
                var cidrPrefixLength = StringExtension.GetCidrPrefixLength(ip);
                netWork.SubnetMark = StringExtension.GenerateSubnetMask(cidrPrefixLength);
                netWork.IPAddress = ip.Replace($"/{cidrPrefixLength}", "");
                netWork.NetWorkType = NetWorkTypeEnum.Static;
            }

            if (ethernets.Value.nameservers != null && ethernets.Value.nameservers.Addresses != null)
                switch (ethernets.Value.nameservers.Addresses.Count)
                {
                    case 2:
                        netWork.Dns = ethernets.Value.nameservers.Addresses[0];
                        netWork.DnsBack = ethernets.Value.nameservers.Addresses[1];
                        break;
                    case 1:
                        netWork.Dns = ethernets.Value.nameservers.Addresses[0];
                        break;
                }

            if (File.Exists(GatewayFilePath.DefRoutePath))
                netWork.DefRoute = await File.ReadAllTextAsync(GatewayFilePath.DefRoutePath) == netWork.NetWorkName;
            output.Add(netWork);
        }

        return output;
    }

    /// <summary>
    ///     获取Wifi网络配置
    /// </summary>
    /// <returns></returns>
    private async Task<WifiModel> GetWifiNetWork()
    {
        var netWork = new WifiModel();
        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .Build();
        //读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync("/etc/netplan/02-wifi.yaml");
        //配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        foreach (var (networkName, wifi) in networkInit.Network.Wifis)
        {
            netWork.NetWorkName = networkName;
            netWork.UserName = wifi.AccessPoints.FirstOrDefault().Key;
            netWork.Password = wifi.AccessPoints.FirstOrDefault().Value.Password;
            if (wifi.Dhcp is "yes" or "true")
            {
                netWork.DefRoute = false;
                netWork.NetWorkType = NetWorkTypeEnum.Dhcp;
            }
            else
            {
                netWork.Gateway = wifi.Gateway ?? wifi.Routes.FirstOrDefault()?.Via;
                netWork.DefRoute = false;

                var ip = wifi.Addresses.FirstOrDefault() ?? string.Empty;
                var cidrPrefixLength = StringExtension.GetCidrPrefixLength(ip);
                netWork.SubnetMark = StringExtension.GenerateSubnetMask(cidrPrefixLength);
                netWork.IPAddress = ip.Replace($"/{cidrPrefixLength}", "");
                netWork.NetWorkType = NetWorkTypeEnum.Static;
            }

            if (wifi.nameservers is {Addresses: not null})
                switch (wifi.nameservers.Addresses.Count)
                {
                    case 2:
                        netWork.Dns = wifi.nameservers.Addresses[0];
                        netWork.DnsBack = wifi.nameservers.Addresses[1];
                        break;
                    case 1:
                        netWork.Dns = wifi.nameservers.Addresses[0];
                        break;
                }

            if (File.Exists(GatewayFilePath.DefRoutePath))
                netWork.DefRoute = await File.ReadAllTextAsync(GatewayFilePath.DefRoutePath) == netWork.NetWorkName;
        }

        return netWork;
    }

    #endregion

    /// <summary>
    ///     Read  Mobile 配置
    /// </summary>
    /// <returns>JSON文件中的value值</returns>
    private async Task<string> ReadMobileConfig()
    {
        var path = Path.Combine("/Edge/mobileConfig.txt"); //文件路径
        if (!File.Exists(path))
            File.CreateText(path);
        using var file = File.OpenText(path);
        var jsonData = await file.ReadToEndAsync();
        file.Dispose();
        return jsonData;
    }
    
    #endregion

    #region Shell

    /// <summary>
    /// 支持串口集合
    /// </summary>
    [HttpGet("/shell/serialPorts")]
    public Task<dynamic> GetSerialPorts()
    {
        return Task.FromResult<dynamic>(new Dictionary<string, string>
        {
            {"/dev/ttyS0", "ttyS0：RX1/TX1(232)"},
            {"/dev/ttyS3", "ttyS3：A1/B1(485)"},
            {"/dev/ttyS4", "ttyS4：A2/B2(485)"},
            {"/dev/ttyS9", "ttyS9：RX2/TX2(232)"},
        });
    }
    
    /// <summary>
    /// 网卡名称集合
    /// </summary>
    [HttpGet("/shell/networks")]
    public async Task<dynamic> GetNetworks()
    {
        var result = await ShellUtil.Bash("ip addr show | grep inet | awk '$NF ~ /^[a-zA-Z0-9]+$/ && $NF !~ /^(docker0|host|link|lo|tap0)$/ {print $2,$NF}'");
        var lines = result.Split('\n');
        var ipToInterface = new Dictionary<string, string> {{"不指定", ""}};
        foreach (var line in lines)
        {
            var parts = line.Split(new[] {' '}, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length != 2) continue;
            var ipAddress = parts[0];
            var interfaceName = parts[1];
            ipToInterface[interfaceName] = ipAddress;
        }
        return ipToInterface;
    }
    
    /// <summary>
    ///     IpAddress
    /// </summary>
    /// <returns></returns>
    [HttpGet("/shell/ipAddress")]
    public async Task<List<string>> Ip()
    {
        var networkNames = new List<string> {"eth0", "eth1", "wlan0", "usb0"};
        var ipList = new List<string>();
        foreach (var networkName in networkNames)
        {
            var config = await GatewayUtil.IfConfig(networkName);
            // Console.WriteLine($"networkName：{networkName}：" +config);
            if (config.IsNullOrEmpty())
                continue;
            var ip = config.Contains("addr:") ? config.GetRegex("addr:", " ") : "";
            var bcast = config.Contains("Bcast:") ? config.GetRegex("Bcast:", " ") : "";
            var mask = config.Contains("Mask:") ? config.GetRegex("Mask:", " ") : "";
            var hWaddr = config.Contains("HWaddr:") ? config.GetRegex("HWaddr:", " ") : "";

            List<string> content = new List<string>();
            if (ip.IsNotNullOrWhiteSpace())
                content.Add("##IP地址##" + "【" + ip + "】");
            if (bcast.IsNotNullOrWhiteSpace())
                content.Add("##广播地址##" + "【" + bcast + "】");
            if (mask.IsNotNullOrWhiteSpace())
                content.Add("##子网掩码##" + "【" + mask + "】");
            if (hWaddr.IsNotNullOrWhiteSpace())
                content.Add("##Mac地址##" + "【" + hWaddr + "】");
            
            var message = TP.Wrapper(networkName,$"##时间## 【{DateTime.NowString()}】",content.ToArray());
            ipList.Add(message);
        }

        return ipList;
    }

    #endregion

    #region NTP设置

    /// <summary>
    ///     ntp配置
    /// </summary>
    /// <returns></returns>
    [HttpGet("/ntp/info")]
    public async Task<dynamic> Info()
    {
        var status = await ShellUtil.Bash("timedatectl");
        status = status.TrimStart();
        var statusSpl = status.Split("\n");
        var runStatus = false;
        var synchronized = false;
        foreach (var spl in statusSpl)
        {
            if (!spl.Contains("System clock synchronized") && !spl.Contains("NTP service")) continue;
            var splObj = spl.Split(":");
            if (spl.Contains("System clock synchronized"))
            {
                if (splObj.Length > 1)
                    synchronized = splObj[1].Trim(' ') == "yes";
            }
            else
            {
                if (splObj.Length > 1)
                    runStatus = splObj[1].Trim(' ') == "active";
            }
        }

        // 读取ntp配置文件
        var timeSyncd = File.OpenText("/etc/systemd/timesyncd.conf");
        var ntp = "ntp1.aliyun.com";
        var interVal = 2048;
        while (!timeSyncd.EndOfStream)
        {
            var line = await timeSyncd.ReadLineAsync();
            if (line.Contains("NTP=") && !line.Contains("FallbackNTP="))
            {
                if (!line.EndsWith(" "))
                {
                    var ntpSp = line.Trim().Split("NTP=");
                    if (ntpSp.Length > 1)
                    {
                        foreach (var sp in ntpSp)
                            Log.Information(sp);
                        ntp = string.IsNullOrWhiteSpace(ntpSp[1]) ? "ntp1.aliyun.com" : ntpSp[1];
                    }
                }

                continue;
            }

            if (line.Contains("PollIntervalMaxSec="))
            {
                var ntpSp = line.Split("PollIntervalMaxSec=");
                if (ntpSp.Length > 1)
                    interVal = Convert.ToInt32(ntpSp[1]) / 60;
            }
        }

        timeSyncd.Dispose();
        return new
        {
            DateTime = DateTime.Now(),
            RunStatus = runStatus,
            Synchronized = synchronized,
            Ntp = ntp,
            InterVal = interVal
        };
    }

    /// <summary>
    ///     保存ntp设置
    /// </summary>
    /// <returns></returns>
    [HttpPost("/ntp/save")]
    public async Task NtpSave(NtpSaveInput input)
    {
        if (input.RunStatus)
        {
            await ShellUtil.Bash("mv /usr/sbin/ntpd /usr/sbin/ntpd-bak");
            // 更新配置
            var timeSyncd = File.OpenText("/etc/systemd/timesyncd.conf");
            var builder = new StringBuilder();
            while (!timeSyncd.EndOfStream)
            {
                var line = await timeSyncd.ReadLineAsync();
                if (line.Contains("NTP=") && !line.Contains("FallbackNTP="))
                {
                    line = $"NTP={input.Ntp}";
                    builder.AppendLine(line);
                    continue;
                }

                if (line.Contains("PollIntervalMaxSec="))
                {
                    line = $"PollIntervalMaxSec={input.InterVal * 60}";
                    builder.AppendLine(line);
                    continue;
                }

                builder.AppendLine(line);
            }

            await File.WriteAllTextAsync("/etc/systemd/timesyncd.conf", builder.ToString());
            // 重启服务
            await ShellUtil.Bash("sudo systemctl restart systemd-timesyncd.service");
            timeSyncd.Dispose();
        }
        else
        {
            await ShellUtil.Bash("sudo timedatectl set-ntp no");
        }
    }

    #endregion
}