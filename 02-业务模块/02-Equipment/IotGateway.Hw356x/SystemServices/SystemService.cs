using Common.Extension;
using IotGateway.Hw356x.SystemServices.Dto;
using IotGateway.Hw356x.Util;
using Microsoft.AspNetCore.Authorization;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Hw356x.SystemServices;

/// <summary>
///     网关默认配置
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-12-19
/// </summary>
[ApiDescriptionSettings("网关配置")]
public class SystemService : IDynamicApiController, ITransient
{
    private const string BasePath = "/etc/DeviceConf/";

    #region 网关授权

    /// <summary>
    ///     授权
    /// </summary>
    /// <returns></returns>
    [HttpPost("/authorization/set")]
    public async Task<SetAuthorizeOutput> SetAuthorization(BaseId<string> input)
    {
        var privateKey = await File.ReadAllTextAsync(BasePath + "RSA.Private");
        var decryptPwd = AuthorizationUtil.Decrypt(input.Id, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() != sn)
            throw new Exception("Sn不一致！");
        //校验是否到期
        if (DateTime.Now() >= Convert.ToDateTime(authorize.EndTime))
            throw new Exception("已过期！");
        await File.WriteAllTextAsync(BasePath + "key.txt", input.Id);
        if (!File.Exists(BasePath + "Ident.txt"))
        {
            await File.WriteAllTextAsync(BasePath + "Ident.txt", "fengEdge-Hw356x");
            authorize.Ident = "fengEdge-Hw356x";
        }
        else
        {
            var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
            authorize.Ident = readIdent ?? "fengEdge-200";
        }

        return authorize;
    }

    /// <summary>
    ///     校验是否授权
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/isAuthorization")]
    public async Task<bool> Authorization()
    {
        var value = await File.ReadAllTextAsync(BasePath + "key.txt");
        if (value == null)
            throw Oops.Oh("未授权！");
        var privateKey = await File.ReadAllTextAsync(BasePath + "RSA.Private");
        var decryptPwd = AuthorizationUtil.Decrypt(value, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() == sn.Trim()) return DateTime.Now() < Convert.ToDateTime(authorize.EndTime);
        Log.Information($"sn:【{authorize.Sn}】,getSn:【{sn}】");
        return false;
    }

    /// <summary>
    ///     获取授权信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/getAuthorization")]
    [AllowAnonymous]
    public async Task<SetAuthorizeOutput> GetAuthorization()
    {
        var setAuthor = await AuthorizationUtil.GetAuthorization();
        if (setAuthor != null) return setAuthor;
        //如果无法获取到授权信息，默认自行组装
        var sn = await GatewayUtil.GetSn();
        var auth = new SetAuthorizeOutput
        {
            Fingerprint = "",
            Ident = "fengEdge-Hw356x",
            Sn = sn,
            Version = "V" + MachineUtil.Version,
            DeviceNumber = 0,
            EndTime = null,
            VariableNumber = 0
        };
        if (!File.Exists(BasePath + "Ident.txt")) return auth;
        var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
        auth.Ident = readIdent ?? "fengEdge-Hw356x";
        return auth;
    }

    #endregion

    /// <summary>
    ///     保存服务器配置
    /// </summary>
    /// <returns></returns>
    [HttpPost("/network/save")]
    public async Task SetNetWork(NetworkSettingModel input)
    {
        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .Build();
        // 读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync(GatewayFilePath.UbuntuNetworkPath);
        // 配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        var defNetwork = "";
        foreach (var network in input.Network)
        {
            var neworkeThernets = new NeworkeThernets
            {
                // 是否是dhcp
                Dhcp = network.NetWorkType == NetWorkTypeEnum.Dhcp
            };

            neworkeThernets.Gateway = network.Gateway;
            if (network.NetWorkType != NetWorkTypeEnum.Dhcp)
                // 设置成静态ip
                // neworkeThernets.Gateway = network.Gateway;
                if (network.IPAddress.IsNotNull())
                {
                    neworkeThernets.Addresses ??= new List<string>();
                    neworkeThernets.Addresses.Add(network.IPAddress + "/24");
                }

            if (network.Dns.IsNotNull() || network.DnsBack.IsNotNull())
            {
                // 设置了DNS就修改
                neworkeThernets.nameservers = new Nameservers {Addresses = new List<string>()};
                if (network.Dns.IsNotNull())
                    neworkeThernets.nameservers.Addresses.Add(network.Dns);
                if (network.DnsBack.IsNotNull())
                    neworkeThernets.nameservers.Addresses.Add(network.DnsBack);
            }

            if (network.DefRoute) defNetwork = network.NetWorkName;
            networkInit.Network.Ethernets ??= new Dictionary<string, NeworkeThernets>();
            networkInit.Network.Ethernets[network.NetWorkName] = neworkeThernets;
        }

        var serializer = new SerializerBuilder().WithNamingConvention(CamelCaseNamingConvention.Instance).IgnoreFields().Build();

        var yaml = serializer.Serialize(networkInit);
        await File.WriteAllTextAsync(GatewayFilePath.UbuntuNetworkPath, yaml);

        await ShellUtil.Bash("quectel-CM");
        // 4g重新拨号
        await ShellUtil.Bash("systemctl restart quectel-ec20.service");
        // 网络生效
        await ShellUtil.Bash("netplan apply");
        // 保存默认网卡名称
        await File.WriteAllTextAsync(GatewayFilePath.DefRoutePath, defNetwork);
    }

    /// <summary>
    ///     获取服务器ip配置列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/list")]
    [AllowAnonymous]
    public async Task<NetworkSettingModel> NetWorkList()
    {
        var deserializer = new DeserializerBuilder().WithNamingConvention(UnderscoredNamingConvention.Instance).Build();
        //读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync(GatewayFilePath.UbuntuNetworkPath);
        //配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        var resultData = new NetworkSettingModel
        {
            Network = new List<NetworkInfoModel>(),
            Mobile = new MobileModel
            {
                Apn = "",
                Password = "",
                IsApn = true,
                NetworkType = 1,
                OperatorName = "中国电信",
                UserName = "",
                Enable = false
            }
        };
        foreach (var ethernets in networkInit.Network.Ethernets)
        {
            var netWork = new NetworkInfoModel
            {
                NetWorkName = ethernets.Key
            };
            if (ethernets.Value.Dhcp)
            {
                netWork.DefRoute = false;
                netWork.NetWorkType = NetWorkTypeEnum.Dhcp;
            }
            else
            {
                netWork.Gateway = ethernets.Value.Gateway;
                netWork.DefRoute = false;
                netWork.SubnetMark = "*************";
                netWork.IPAddress = ethernets.Value.Addresses.FirstOrDefault()?.Replace("/24", "");
                netWork.NetWorkType = NetWorkTypeEnum.Static;
            }

            if (ethernets.Value.nameservers != null)
                switch (ethernets.Value.nameservers.Addresses.Count)
                {
                    case 2:
                        netWork.Dns = ethernets.Value.nameservers.Addresses[0];
                        netWork.DnsBack = ethernets.Value.nameservers.Addresses[1];
                        break;
                    case 1:
                        netWork.Dns = ethernets.Value.nameservers.Addresses[0];
                        break;
                }

            if (File.Exists(GatewayFilePath.DefRoutePath))
                netWork.DefRoute = await File.ReadAllTextAsync(GatewayFilePath.DefRoutePath) == netWork.NetWorkName;
            resultData.Network.Add(netWork);
        }

        // 读取Mobile配置
        var mobileConfig = await ReadMobileConfig();
        if (mobileConfig.IsNotNullOrWhiteSpace())
        {
            resultData.Mobile = JSON.Deserialize<MobileModel>(mobileConfig);
        }

        return resultData;
    }

    /// <summary>
    ///     Read  Mobile 配置
    /// </summary>
    /// <returns>JSON文件中的value值</returns>
    private async Task<string> ReadMobileConfig()
    {
        var path = Path.Combine("/Edge/mobileConfig.txt"); //文件路径
        if (!File.Exists(path))
            File.CreateText(path);
        using var file = File.OpenText(path);
        var jsonData = await file.ReadToEndAsync();
        file.Dispose();
        return jsonData;
    }

    #region Shell

    /// <summary>
    ///     IpAddress
    /// </summary>
    /// <returns></returns>
    [HttpGet("/shell/ipAddress")]
    public async Task<List<string>> Ip()
    {
        var networkNames = new List<string> {"eth0", "eth1"};
        List<string> ipList = new();
        foreach (var networkName in networkNames)
        {
            var config = await GatewayUtil.IfConfig(networkName);
            if (config.IsNullOrEmpty()) continue;
            config = config.TrimStart();

            var ip = config.GetStringBetween("inet", "netmask").Trim();
            var bcast = config.GetStringBetween("broadcast", "ether").Trim();
            var mask = config.GetStringBetween("netmask", "broadcast").Trim();
            var hWaddr = config.GetStringBetween("ether", "txqueuelen").Trim();
            var message = TP.Wrapper(networkName,
                $"##时间## 【{DateTime.NowString()}】",
                $"##IP地址## 【{ip}】",
                $"{(bcast.IsNotNull() ? "##广播地址##" + "【" + bcast + "】" : "")}",
                $"##子网掩码## 【{mask}】",
                $"{(hWaddr.IsNotNull() ? "##Mac地址##" + "【" + hWaddr + "】" : "")}");
            ipList.Add(message);
        }

        return ipList;
    }

    #endregion
}