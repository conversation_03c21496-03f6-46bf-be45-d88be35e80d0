using Console = Feng.Common.Extension.Console;

namespace IotGateway.Hw356x.Util;

/// <summary>
///     网关工具
/// </summary>
public static class GatewayUtil
{
    /// <summary>
    ///     获取资源使用信息
    /// </summary>
    /// <returns></returns>
    public static async Task<dynamic> UseInfo()
    {
        var ramInfo = MachineUtil.RamInfo();
        var cpuRate = Math.Ceiling(double.Parse(await CpuRate()));
        var hddInfo = ReadHddInfo();
        var networkList = new List<string> {"eth0", "eth1", "wlan0"};
        var ipAddr = new Dictionary<string, string>();
        foreach (var network in networkList)
        {
            var ip = await ReadIp(network);
            if (ip != null)
            {
                ipAddr.Add(network, ip);
                switch (network)
                {
                    case "eth0":
                        MachineUtil.Clay.Eth0 = ip;
                        break;
                    case "eth1":
                        MachineUtil.Clay.Eth1 = ip;
                        break;
                    case "wlan0":
                        MachineUtil.Clay.Wifi = ip;
                        break;
                }
            }
        }

        MachineUtil.UseInfo.Network = ipAddr; // 网络ip
        MachineUtil.UseInfo.DiskInfo = hddInfo; // 磁盘使用
        MachineUtil.UseInfo.MemInfo = ramInfo; // 内存
        MachineUtil.UseInfo.CpuRate = cpuRate; // cpu使用率
        return MachineUtil.UseInfo;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    private static DiskInfo ReadHddInfo()
    {
        try
        {
            DiskInfo hdd = null;
            var hddInfo = ShellUtil.Bash("df -hk").GetAwaiter().GetResult();
            var lines = hddInfo.Split('\n');
            foreach (var item in lines)
            {
                if (!item.Contains("/dev/root")) continue;
                var li = item.Split(new[] {' '}, StringSplitOptions.RemoveEmptyEntries);
                for (var i = 0; i < li.Length; i++)
                {
                    if (!li[i].Contains("%")) continue;
                    hdd = new DiskInfo
                    {
                        DiskSize = Convert.ToInt64(li[i - 3]),
                        DiskUsed = Convert.ToInt64(li[i - 2]),
                        DiskAvailable = Convert.ToInt64(li[i - 1]),
                        DiskRate = Convert.ToDouble(li[i].Replace("%", "").Trim())
                    };
                    break;
                }
            }

            return hdd;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }

    /// <summary>
    ///     获取CPU使用率
    /// </summary>
    /// <returns></returns>
    public static async Task<string> CpuRate()
    {
        var output = await ShellUtil.Bash("top -bn1 | grep '%Cpu' | awk '{print $2}'");
        var cpuRate = output.Trim();
        return cpuRate;
    }

    /// <summary>
    ///     获取Sn码
    /// </summary>
    /// <returns></returns>
    public static async Task<string> GetSn()
    {
        var path = "/etc/DeviceConf/guid.txt";
        if (!File.Exists(path)) await File.WriteAllTextAsync(path, $"{GenerateId()}");
        var guid = await File.ReadAllTextAsync(path);
        return guid.IsNullOrEmpty() ? null : guid;
    }

    private static string GenerateId()
    {
        long i = 1;
        foreach (var b in Guid.NewGuid().ToByteArray()) i *= b + 1;
        return string.Format("{0:x}", i - DateTime.Now.Ticks);
    }

    /// <summary>
    ///     IfConfig
    /// </summary>
    /// <returns></returns>
    public static async Task<string> IfConfig(string ipName)
    {
        var hddInfo = await ShellUtil.Bash($"ifconfig {ipName}");
        var lines = hddInfo.Split('\n');
        var output = "";

        foreach (var item in lines)
        {
            if (item.Contains("inet "))
            {
                var spList = item.Split(" ", StringSplitOptions.RemoveEmptyEntries);
                if (spList.Length > 4)
                    output += $"addr:{spList[1]} Mask:{spList[3]} Bcast:{spList[5]}";
                else
                    output += $"addr:{spList[1]} Mask:{spList[3]} ";
            }

            if (item.Contains("ether"))
            {
                var spList = item.Split(" ", StringSplitOptions.RemoveEmptyEntries);
                output += $" HWaddr::{spList[1]}";
                Console.WriteLine(output);
                return output;
            }
        }

        return output;
    }

    /// <summary>
    ///     获取网关 IP信息
    /// </summary>
    /// <returns></returns>
    private static async Task<string> ReadIp(string networkName)
    {
        var hddInfo = await ShellUtil.Bash("ifconfig");
        var isName = false;
        var lines = hddInfo.Split('\n');
        foreach (var item in lines)
        {
            if (item.StartsWith(networkName))
                isName = true;
            if (!item.Contains("inet") || !isName) continue;
            var li = item.Split(new[] {' '}, StringSplitOptions.RemoveEmptyEntries);
            return li[1].Trim();
        }

        return null;
    }
}