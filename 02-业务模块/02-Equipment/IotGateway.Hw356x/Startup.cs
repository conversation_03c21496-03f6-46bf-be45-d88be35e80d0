using System.Runtime.InteropServices;
using IotGateway.Hw356x.BackgroundServices;
using IotGateway.Hw356x.SystemServices;
using Microsoft.Extensions.DependencyInjection;

namespace IotGateway.Hw356x;

/// <summary>
/// </summary>
[AppStartup(1)]
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        if (!RuntimeInformation.IsOSPlatform(OSPlatform.Linux)) return;
        MachineUtil.CurrentSystemEnvironment = CurrentSystemEnvironmentEnum.Hw356X;
        MachineUtil.Authorize = App.GetService<SystemService>().GetAuthorization().GetAwaiter().GetResult();
        MachineUtil.SystemRunTime = Convert.ToDateTime(ShellUtil.Bash("uptime -s").Result);
        
        // 网关运行状态
        services.AddHostedService<RunStatusTimer>();
        // 
        Task.Run(async () => { await ShellUtil.Bash("quectel-CM"); });
        // 创建基本目录
        Task.Run(async () => { await ShellUtil.Bash("mkdir -p /Edge/Data  /Edge/Redis /etc/DeviceConf /Edge/DncFile "); });
        // 
        Task.Run(async () => { await ShellUtil.Bash("chmod -R 777 /usr/local/src /Edge /etc/DeviceConf "); });
        // 删除历史更新包
        Task.Run(async () => { await ShellUtil.Bash("rm -rf /usr/local/src/*-encrypt.rar"); });
    }
}