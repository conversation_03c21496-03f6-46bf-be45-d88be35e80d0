using System.Net;
using System.Net.Sockets;
using Common.Extension;
using Feng.IotGateway.Core.Extension;
using Furion.Logging;
using IotGateway.Equipment.SystemServices.Dto;

namespace IotGateway.Equipment.Util;

/// <summary>
///     网关工具
/// </summary>
public static class GatewayUtil
{
    /// <summary>
    ///     获取Sn码
    /// </summary>
    /// <returns></returns>
    public static async Task<string> GetSn()
    {
        var val = await ShellUtil.Bash("/hogo/bin/hogodevinfotools sn -r");
        if (!val.StartsWith("read the sn"))
            return null;
        var number = val.Replace("read the sn :", "").Trim();

        return number;
    }

    /// <summary>
    ///     IfConfig
    /// </summary>
    /// <returns></returns>
    public static async Task<string> IfConfig(string ipName)
    {
        var hddInfo = await ShellUtil.Bash($"ifconfig {ipName}");

        var lines = hddInfo.Split('\n');
        var macAddr = "";

        foreach (var item in lines)
        {
            if (item.Contains("Link"))
            {
                var spList = item.Split(" ");
                var mac = false;
                foreach (var sp in spList)
                {
                    if (mac && sp.IsNotNull()) macAddr += " HWaddr:" + sp;

                    if (sp == "HWaddr")
                        mac = true;
                }
            }

            if (!item.Contains("inet")) continue;
            return ipName == "usb0"
                ? "5G" + "    " + item.TrimStart() + macAddr + " "
                : ipName + "    " + item.TrimStart() + macAddr + " ";
        }

        return "";
    }

    /// <summary>
    ///     获取CPU使用率
    /// </summary>
    /// <returns></returns>
    private static async Task<string> CpuRate()
    {
        var output = await ShellUtil.Bash("top -b -n1 | grep \"CPU\" | grep -v grep  | awk '{print $2}' | head -n 1");
        var cpuRate = output.Trim().Replace("%", "");
        return cpuRate;
    }

    private static long ConvertToKB(string size)
    {
        try
        {
            size = size.Trim();
            var number = double.Parse(size[..^1]); // 去掉最后一个字符(单位)并解析数字
            var unit = size[^1]; // 获取最后一个字符(单位)

            return unit switch
            {
                'G' => (long)(number * 1024 * 1024), // GB to KB
                'M' => (long)(number * 1024),        // MB to KB
                'K' => (long)number,                 // KB
                _ => (long)number                    // 假设是KB
            };
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    private static DiskInfo ReadHddInfo()
    {
        try
        {
            var hdd = new DiskInfo();
            // 大小
            long size = 0;
            // 使用
            long used = 0;
            // 可使用
            long avail = 0;
            var hddInfo = ShellUtil.Bash("df -h").GetAwaiter().GetResult();

            var lines = hddInfo.Split('\n');

            foreach (var item in lines)
            {
                // 跳过标题行和空行
                if (string.IsNullOrWhiteSpace(item) || item.StartsWith("Filesystem"))
                {
                    continue;
                }

                var li = item.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                if (li.Length < 6)
                {
                    continue;
                }


                // 累计所有磁盘空间
                var diskSize = ConvertToKB(li[1]);
                size += diskSize;

                var diskUsed = ConvertToKB(li[2]);
                used += diskUsed;

                var diskAvail = ConvertToKB(li[3]);
                avail += diskAvail;
            }

            hdd.DiskSize = size;
            hdd.DiskUsed = used;
            hdd.DiskAvailable = avail;
            //使用率 = 已经使用 /总量 *100
            hdd.DiskRate = size > 0 ? (double)Math.Round((decimal)(used * 100.0 / size), 2) : 0;

            return hdd;
        }
        catch (Exception ex)
        {
            Log.Error($"【ReadHddInfo】异常: {ex.Message}\n堆栈: {ex.StackTrace}");
            return null;
        }
    }

    /// <summary>
    ///     获取网关 IP信息
    /// </summary>
    /// <returns></returns>
    private static async Task<string> ReadIp(string networkName)
    {
        var hddInfo = await ShellUtil.Bash("ifconfig");

        var isName = false;

        var lines = hddInfo.Split('\n');
        foreach (var item in lines)
        {
            if (item.StartsWith(networkName))
                isName = true;
            if (!item.Contains("inet") || !isName) continue;
            var li = item.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            return li[1].Replace("addr:", "").Trim();
        }

        return null;
    }

    /// <summary>
    ///     获取资源使用信息
    /// </summary>
    /// <returns></returns>
    public static async Task<dynamic> UseInfo()
    {
        // 内存获取
        var ramInfo = MachineUtil.RamInfo();
        var cpuRate = Math.Ceiling(double.Parse(await CpuRate()));
        var hddInfo = ReadHddInfo();
        var networkList = new List<string> { "eth0", "eth1", "eth2", "wlan0", "usb0" };
        var ipAddr = new Dictionary<string, string>();
        foreach (var network in networkList)
        {
            var ip = await ReadIp(network);
            if (ip != null)
            {
                // 5G网卡名强制转换
                ipAddr.Add(network == "usb0" ? "eth2" : network, ip);
                switch (network)
                {
                    case "eth0":
                        MachineUtil.Clay.Eth0 = ip;
                        break;
                    case "eth1":
                        MachineUtil.Clay.Eth1 = ip;
                        break;
                    case "eth2":
                    case "usb0":
                        MachineUtil.Clay.Eth2 = ip;
                        break;
                    case "wlan0":
                        MachineUtil.Clay.Wifi = ip;
                        break;
                }
            }
        }

        MachineUtil.UseInfo.Network = ipAddr; //网络ip
        MachineUtil.UseInfo.DiskInfo = hddInfo; //磁盘使用
        MachineUtil.UseInfo.MemInfo = ramInfo; //内存
        MachineUtil.UseInfo.CpuRate = cpuRate; // cpu使用率
        MachineUtil.UseInfo.Signal = _signal; //4G信号
        return MachineUtil.UseInfo;
    }

    /// <summary>
    ///     4G网关信号
    /// </summary>
    private static NetworkSignalDto _signal = new();

    /// <summary>
    ///     获取硬件信号
    /// </summary>
    public static void GetNetworkSignal()
    {
        try
        {
            var locatePoint = new IPEndPoint(IPAddress.Parse("127.0.0.1"), 9900);
            var udpClient = new UdpClient(locatePoint);
            //目标机器
            var remotePoint = new IPEndPoint(IPAddress.Parse("127.0.0.1"), 9988);
            var buffer = Encoding.UTF8.GetBytes("{\"type\":\"modem\",\"who\":\"all\"}");
            Task.Factory.StartNew(() =>
            {
                while (true)
                {
                    try
                    {
                        if (udpClient != null)
                        {
                            udpClient.Send(buffer, buffer.Length, remotePoint);
                            var received = udpClient.Receive(ref remotePoint);
                            var info = Encoding.UTF8.GetString(received);
                            _signal = info.ToObject<NetworkSignalDto>();
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error("获取设备信号异常:" + ex.Message);
                    }

                    Thread.Sleep(5000);
                }
            }, TaskCreationOptions.LongRunning);
        }
        catch (Exception ex)
        {
            Log.Error("获取设备信号初始化异常:" + ex.Message);
        }
    }
}