<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <OutputPath>..\..\..\01-应用服务\IotGateway/bin/$(Configuration)/$(TargetFramework)/plugins/equipment/</OutputPath>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
        <DocumentationFile>IotGateway.FengEdge150.xml</DocumentationFile>
        <RootNamespace>IotGateway.Equipment</RootNamespace>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
        <DebugType>none</DebugType>
        <DocumentationFile>IotGateway.FengEdge150.xml</DocumentationFile>
        <RootNamespace>IotGateway.Equipment</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\04-架构核心\IotGateway.Core\IotGateway.Core.csproj"/>
        <ProjectReference Include="..\..\..\04-架构核心\IotGateway.WebSocket\IotGateway.WebSocket.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <None Remove="IotGateway.FengEdge150.xml"/>
        <None Update="IotGateway.FengEdge150.xml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>
