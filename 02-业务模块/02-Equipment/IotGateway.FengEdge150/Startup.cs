using IotGateway.Equipment.BackgroundServices;
using IotGateway.Equipment.SystemServices;
using Microsoft.Extensions.DependencyInjection;

namespace IotGateway.Equipment;

/// <summary>
/// </summary>
[AppStartup(1)]
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        MachineUtil.CurrentSystemEnvironment = CurrentSystemEnvironmentEnum.FengEdge150;
        MachineUtil.Authorize = App.GetService<SystemService>().GetAuthorization().GetAwaiter().GetResult();
        
        // 网关运行状态
        services.AddHostedService<RunStatusTimer>();
        // 
        services.AddHostedService<HostedService>();
    }
}