using System.Runtime.InteropServices;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Equipment.BackgroundServices;

/// <summary>
///     网关运行状态
/// </summary>
public class RunStatusTimer : BackgroundService
{
    private readonly ILogger<RunStatusTimer> _logger;
    private readonly Crontab _crontab;

    /// <summary>
    ///     socket消息推送
    /// </summary>
    private readonly SendMessageService _socket;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="socket"></param>
    public RunStatusTimer(ILogger<RunStatusTimer> logger, SendMessageService socket)
    {
        _logger = logger;
        _socket = socket;
        _crontab = Crontab.Parse("0/5 * * ? * *", CronStringFormat.WithSeconds);
    }

    /// <summary>
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                    {
                        var infoMsg = await GatewayUtil.UseInfo();
                        if (!stoppingToken.IsCancellationRequested)
                        {
                            await _socket.Send(JSON.Serialize(infoMsg), ConstMethod.SysRunStatus);
                        }
                    }
                }
                catch (Exception ex) when (ex is not OperationCanceledException)
                {
                    _logger.LogError($"定时推送网关运行状态 Error:【{ex.Message}】");
                }

                if (!stoppingToken.IsCancellationRequested)
                {
                    await Task.Delay((int)_crontab.GetSleepMilliseconds(DateTime.Now()), stoppingToken);
                }
            }
        }
        catch (OperationCanceledException)
        {
            // 正常的取消操作，不需要记录错误
        }
        catch (Exception ex)
        {
            _logger.LogError($"RunStatusTimer service error: {ex.Message}");
            throw;
        }
    }
}