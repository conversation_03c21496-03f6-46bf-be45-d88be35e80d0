namespace IotGateway.Equipment.SystemServices.Dto;

/// <summary>
///     网络配置
/// </summary>
public class NetworkInit
{
    /// <summary>
    /// </summary>
    [YamlMember(Alias = "network", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public Network Network { get; set; }
}

/// <summary>
/// </summary>
public class Network
{
    [YamlMember(Alias = "version", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public int Version { get; set; }

    [YamlMember(Alias = "renderer", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public string Renderer { get; set; }

    [YamlMember(Alias = "ethernets", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public Dictionary<string, NetworkThereNets> Ethernets { get; set; }

    [YamlMember(Alias = "wifis", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public Dictionary<string, NetworkThereNets> Wifis { get; set; }
}

/// <summary>
/// </summary>
public class NetworkThereNets
{
    [YamlMember(Alias = "addresses", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public List<string> Addresses { get; set; }

    [YamlMember(Alias = "gateway4", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public string Gateway { get; set; }

    [YamlMember(Alias = "nameservers", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public Nameservers nameservers { get; set; }

    [YamlMember(Alias = "dhcp4", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public string? Dhcp { get; set; }

    [YamlMember(Alias = "routes", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public List<Routes> Routes { get; set; }

    [YamlMember(Alias = "optional", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public bool Optional { get; set; }

    [YamlMember(Alias = "access-points", DefaultValuesHandling = DefaultValuesHandling.OmitNull, ApplyNamingConventions = false)]
    public Dictionary<string, AccessPoint> AccessPoints { get; set; }
}

/// <summary>
/// </summary>
public class Routes
{
    [YamlMember(Alias = "to", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public string To { get; set; }

    [YamlMember(Alias = "via", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public string? Via { get; set; }

    [YamlMember(Alias = "metric", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public string Metric { get; set; }
}

/// <summary>
/// </summary>
public class Nameservers
{
    [YamlMember(Alias = "addresses", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public List<string> Addresses { get; set; }
}

/// <summary>
/// </summary>
public class AccessPoint
{
    [YamlMember(Alias = "password", DefaultValuesHandling = DefaultValuesHandling.OmitNull)]
    public string Password { get; set; }
}