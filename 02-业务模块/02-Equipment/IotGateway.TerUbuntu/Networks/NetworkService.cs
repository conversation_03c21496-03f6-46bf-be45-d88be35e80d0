using Common.Extension;
using Feng.Common.Extension;
using Feng.IotGateway.Core.Const;
using Feng.IotGateway.Core.Models.Networks;
using IotGateway.Equipment.Networks.Dto;
using Microsoft.AspNetCore.Authorization;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;

namespace IotGateway.Equipment.Networks;

/// <summary>
///     网关默认配置
/// </summary>
[ApiDescriptionSettings("网关配置")]
public class SystemService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     获取服务器ip配置列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/list")]
    [AllowAnonymous]
    public async Task<NetworkSettingModel> NetWorkList()
    {
        var output = new NetworkSettingModel
        {
            Network = new List<NetworkInfoModel>(),
            Mobile = new MobileModel
            {
                Apn = "123",
                Password = "123",
                IsApn = true,
                NetworkType = 1,
                OperatorName = "中国电信",
                UserName = "test"
            }
        };
        output.Network = await GetNetWorkList();
        output.Wifi = await GetWifiNetWork();
        return output;
    }

    /// <summary>
    ///     保存服务器配置
    /// </summary>
    /// <returns></returns>
    [HttpPost("/network/save")]
    [AllowAnonymous]
    public async Task SetNetWork(NetworkSettingModel input)
    {
        var defNetwork = await SaveNetWork(input);
        await SaveWifiNetWork(input);
        // 开启wifi
        if (input.Wifi is {Enable: true, DefRoute: true})
            defNetwork = "wlan0";

        //网络生效
        await ShellUtil.Bash("netplan apply");
        //保存默认网卡名称
        await File.WriteAllTextAsync(GatewayFilePath.DefRoutePath, defNetwork);
    }

    /// <summary>
    ///     ip信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/ipInfo")]
    public async Task<dynamic> Ip()
    {
        var networkNames = new List<string> {"eth0", "eth1", "wlan0"};
        Dictionary<string, object> ipList = new();
        foreach (var networkName in networkNames)
        {
            var config = await IfConfig(networkName);
            if (string.IsNullOrEmpty(config))
                continue;
            config = config.TrimStart();

            var ip = config.GetStringBetween("inet", "netmask").Trim();
            var bcast = config.GetStringBetween("broadcast", "ether").Trim();
            var mask = config.GetStringBetween("netmask", "broadcast").Trim();
            var mac = config.GetStringBetween("ether", "txqueuelen").Trim();
            ipList.Add(networkName, new
            {
                Mac = mac,
                Inet = ip,
                Broadcast = bcast,
                Netmask = mask
            });
        }

        return ipList;
    }

    #region 私有方法

    /// <summary>
    ///     IfConfig
    /// </summary>
    /// <returns></returns>
    private static async Task<string> IfConfig(string ipName)
    {
        var config = "";
        var hddInfo = await ShellUtil.Bash("ifconfig");
        var isName = false;
        var lines = hddInfo.Split('\n');
        foreach (var item in lines)
        {
            if (item.StartsWith(ipName))
                isName = true;
            if (!isName)
                continue;
            if (item.Contains("inet") && !item.Contains("inet6"))
                config = item;
            if (!item.Contains("ether")) continue;
            config += " " + item;
            return config;
        }

        return config;
    }

    /// <summary>
    ///     修改网络配置
    /// </summary>
    /// <param name="input"></param>
    private async Task<string> SaveNetWork(NetworkSettingModel input)
    {
        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .Build();

        //读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync("/etc/netplan/01-eth.yaml");
        //配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        var defNetwork = "";
        foreach (var network in input.Network)
        {
            var networkThereNets = new NetworkThereNets
            {
                //是否是dhcp
                Dhcp = network.NetWorkType == NetWorkTypeEnum.Dhcp ? "yes" : null,
                Gateway = null
            };

            if (network.NetWorkType != NetWorkTypeEnum.Dhcp)
                //设置成静态ip
                // networkThereNets.Gateway = network.Gateway;
                if (network.IPAddress.IsNotNull())
                {
                    networkThereNets.Addresses ??= new List<string>();
                    var subnetSize = StringExtension.GetSubnetSize(network.SubnetMark);
                    networkThereNets.Addresses.Add(network.IPAddress + $"/{subnetSize}");
                }

            if (network.Dns.IsNotNull() || network.DnsBack.IsNotNull())
            {
                //如果设置了DNS就修改
                networkThereNets.nameservers = new Nameservers {Addresses = new List<string>()};
                if (network.Dns.IsNotNull())
                    networkThereNets.nameservers.Addresses.Add(network.Dns);
                if (network.DnsBack.IsNotNull())
                    networkThereNets.nameservers.Addresses.Add(network.DnsBack);
            }

            networkThereNets.Routes ??= new List<Routes>();
            var routes = new Routes
            {
                To = "0.0.0.0/0",
                Via = network.Gateway,
                Metric = network.NetWorkName == "eth1" ? "101" : "102"
            };
            if (network.DefRoute)
            {
                defNetwork = network.NetWorkName;
                routes.Metric = "100";
            }

            networkThereNets.Routes.Add(routes);
            networkInit.Network.Ethernets ??= new Dictionary<string, NetworkThereNets>();
            networkInit.Network.Ethernets[network.NetWorkName] = networkThereNets;
        }

        var serializer = new SerializerBuilder()
            .WithNamingConvention(CamelCaseNamingConvention.Instance)
            .IgnoreFields()
            .Build();
        var yaml = serializer.Serialize(networkInit);
        await File.WriteAllTextAsync("/etc/netplan/01-eth.yaml", yaml);

        return defNetwork;
    }

    /// <summary>
    ///     修改Wifi网络配置
    /// </summary>
    /// <param name="input"></param>
    private async Task SaveWifiNetWork(NetworkSettingModel input)
    {
        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .Build();

        //读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync("/etc/netplan/02-wifi.yaml");
        // var networkYaml = await Files.ReadAllTextAsync("D:\\02-wifi.yaml");
        //配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        // wifi开启
        if (input.Wifi.Enable)
        {
            var network = input.Wifi;
            var networkThereNets = new WifiNetworkThereNets
            {
                //是否是dhcp
                Dhcp = network.NetWorkType == NetWorkTypeEnum.Dhcp ? "yes" : null,
                Gateway = null,
                AccessPoints = new Dictionary<string, AccessPoint>
                {
                    {
                        network.UserName, new AccessPoint
                        {
                            Password = network.Password
                        }
                    }
                },
                Dhcp4Overrides = new Dhcp4Overrides
                {
                    RouteMetric = network.DefRoute ? "100" : "600"
                },
                Optional = true
            };

            if (network.NetWorkType != NetWorkTypeEnum.Dhcp)
                //设置成静态ip
                // networkThereNets.Gateway = network.Gateway;
                if (network.IPAddress.IsNotNull())
                {
                    networkThereNets.Addresses ??= new List<string>();
                    var subnetSize = StringExtension.GetSubnetSize(network.SubnetMark);
                    networkThereNets.Addresses.Add(network.IPAddress + $"/{subnetSize}");
                }

            if ((network.Dns.IsNotNull() || network.DnsBack.IsNotNull()) && network.NetWorkType != NetWorkTypeEnum.Dhcp)
            {
                //如果设置了DNS就修改
                networkThereNets.nameservers = new Nameservers {Addresses = new List<string>()};
                if (network.Dns.IsNotNull())
                    networkThereNets.nameservers.Addresses.Add(network.Dns);
                if (network.DnsBack.IsNotNull())
                    networkThereNets.nameservers.Addresses.Add(network.DnsBack);
            }

            networkThereNets.Routes ??= new List<Routes>();
            if (network.NetWorkType != NetWorkTypeEnum.Dhcp)
            {
                var routes = new Routes
                {
                    To = "0.0.0.0/0",
                    Via = network.Gateway,
                    Metric = network.DefRoute ? "100" : "600"
                };

                networkThereNets.Routes.Add(routes);
            }

            networkInit.Network.Wifis ??= new Dictionary<string, WifiNetworkThereNets>();
            networkInit.Network.Wifis[network.NetWorkName] = networkThereNets;
        }

        var serializer = new SerializerBuilder()
            .WithNamingConvention(CamelCaseNamingConvention.Instance)
            .IgnoreFields()
            .Build();
        var yaml = serializer.Serialize(networkInit);

        // await Files.WriteAllTextAsync("D:\\02-wifi.yaml", yaml);
        await File.WriteAllTextAsync("/etc/netplan/02-wifi.yaml", yaml);
    }

    /// <summary>
    ///     获取网络配置
    /// </summary>
    /// <returns></returns>
    private async Task<List<NetworkInfoModel>> GetNetWorkList()
    {
        var output = new List<NetworkInfoModel>();
        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .Build();
        //读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync("/etc/netplan/01-eth.yaml");
        // var networkYaml = await Files.ReadAllTextAsync("D:\\02-wifi.yaml");
        //配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        foreach (var ethernets in networkInit.Network.Ethernets)
        {
            var netWork = new NetworkInfoModel
            {
                NetWorkName = ethernets.Key
            };
            if (ethernets.Value.Dhcp != null)
            {
                netWork.DefRoute = false;
                netWork.NetWorkType = NetWorkTypeEnum.Dhcp;
            }
            else
            {
                netWork.Gateway = ethernets.Value.Gateway ?? ethernets.Value.Routes.FirstOrDefault()?.Via;
                netWork.DefRoute = false;
                var ip = ethernets.Value.Addresses.FirstOrDefault() ?? string.Empty;
                var cidrPrefixLength = StringExtension.GetCidrPrefixLength(ip);
                netWork.SubnetMark = StringExtension.GenerateSubnetMask(cidrPrefixLength);
                netWork.IPAddress = ip.Replace($"/{cidrPrefixLength}", "");
                netWork.NetWorkType = NetWorkTypeEnum.Static;
            }

            if (ethernets.Value.nameservers != null && ethernets.Value.nameservers.Addresses != null)
                switch (ethernets.Value.nameservers.Addresses.Count)
                {
                    case 2:
                        netWork.Dns = ethernets.Value.nameservers.Addresses[0];
                        netWork.DnsBack = ethernets.Value.nameservers.Addresses[1];
                        break;
                    case 1:
                        netWork.Dns = ethernets.Value.nameservers.Addresses[0];
                        break;
                }

            if (File.Exists(GatewayFilePath.DefRoutePath))
                netWork.DefRoute = await File.ReadAllTextAsync(GatewayFilePath.DefRoutePath) == netWork.NetWorkName;
            output.Add(netWork);
        }

        return output;
    }

    /// <summary>
    ///     获取Wifi网络配置
    /// </summary>
    /// <returns></returns>
    private async Task<WifiModel> GetWifiNetWork()
    {
        var netWork = new WifiModel();
        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .Build();
        //读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync("/etc/netplan/02-wifi.yaml");
        //配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        foreach (var (networkName, wifi) in networkInit.Network.Wifis)
        {
            netWork.NetWorkName = networkName;
            netWork.UserName = wifi.AccessPoints.FirstOrDefault().Key;
            netWork.Password = wifi.AccessPoints.FirstOrDefault().Value.Password;
            if (wifi.Dhcp is "yes" or "true")
            {
                netWork.DefRoute = false;
                netWork.NetWorkType = NetWorkTypeEnum.Dhcp;
            }
            else
            {
                netWork.Gateway = wifi.Gateway ?? wifi.Routes.FirstOrDefault()?.Via;
                netWork.DefRoute = false;

                var ip = wifi.Addresses.FirstOrDefault() ?? string.Empty;
                var cidrPrefixLength = StringExtension.GetCidrPrefixLength(ip);
                netWork.SubnetMark = StringExtension.GenerateSubnetMask(cidrPrefixLength);
                netWork.IPAddress = ip.Replace($"/{cidrPrefixLength}", "");
                netWork.NetWorkType = NetWorkTypeEnum.Static;
            }

            if (wifi.nameservers is {Addresses: not null})
                switch (wifi.nameservers.Addresses.Count)
                {
                    case 2:
                        netWork.Dns = wifi.nameservers.Addresses[0];
                        netWork.DnsBack = wifi.nameservers.Addresses[1];
                        break;
                    case 1:
                        netWork.Dns = wifi.nameservers.Addresses[0];
                        break;
                }

            if (File.Exists(GatewayFilePath.DefRoutePath))
                netWork.DefRoute = await File.ReadAllTextAsync(GatewayFilePath.DefRoutePath) == netWork.NetWorkName;
        }

        return netWork;
    }

    #endregion
}