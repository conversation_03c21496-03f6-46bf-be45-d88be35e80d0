using Microsoft.Extensions.DependencyInjection;

namespace IotGateway.Equipment;

/// <summary>
/// </summary>
[AppStartup(1)]
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        // 设置系统环境
        MachineUtil.CurrentSystemEnvironment = CurrentSystemEnvironmentEnum.TerUbuntu;
        // 创建基本目录
        _ = ShellUtil.Bash("sudo mkdir -p /media/app/Data /etc/DeviceConf /media/app/Data/DncFile ");
        // 授权目录
        _ = ShellUtil.Bash("sudo chmod -R 777 /usr/local/src /media/app/ /etc/DeviceConf");
    }
}