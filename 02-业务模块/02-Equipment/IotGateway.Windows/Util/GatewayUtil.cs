using System.Diagnostics;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Security.Cryptography;
using Common.Extension;
using Furion.Templates;
using Console = System.Console;
using DateTime = System.DateTime;

namespace IotGateway.Equipment.Util;

/// <summary>
///     网关工具
/// </summary>
public static class GatewayUtil
{
    /// <summary>
    ///     获取网关 IP信息
    /// </summary>
    /// <returns></returns>
    public static async Task<List<string>> IfConfig()
    {
        // 获取本机的所有网卡
        var interfaces = NetworkInterface.GetAllNetworkInterfaces();
        var output = new List<string>();
        foreach (var nic in interfaces)
            // 过滤掉非物理网卡和回环网卡
            if (nic.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                nic.NetworkInterfaceType != NetworkInterfaceType.Tunnel &&
                nic.OperationalStatus == OperationalStatus.Up)
            {
                // 获取网卡的IP地址
                var ipProps = nic.GetIPProperties();
                var ipAddr = "";
                var broadcastAddress = "";
                var mask = "";
                var macAddress = nic.GetPhysicalAddress().ToString();
                foreach (var ip in ipProps.UnicastAddresses)
                    // 输出IPv4地址
                    if (ip.Address.AddressFamily == AddressFamily.InterNetwork)
                    {
                        ipAddr = ip.Address.ToString();
                        mask = ip.IPv4Mask.ToString();
                        // 计算广播地址
                        var broadcastBytes = new byte[ip.Address.GetAddressBytes().Length];
                        for (var i = 0; i < broadcastBytes.Length; i++) broadcastBytes[i] = (byte)(ip.Address.GetAddressBytes()[i] | ~ip.IPv4Mask.GetAddressBytes()[i]);
                        broadcastAddress = new IPAddress(broadcastBytes).ToString();
                    }

                var message = TP.Wrapper(nic.Name,
                    $"##时间## 【{DateTime.Now}】",
                    $"##IP地址## 【{ipAddr}】",
                    $"{(broadcastAddress.IsNotNull() ? "##广播地址##" + "【" + broadcastAddress + "】" : "")}",
                    $"##子网掩码## 【{mask}】",
                    $"{(macAddress.IsNotNull() ? "##Mac地址##" + "【" + macAddress + "】" : "")}");
                output.Add(message);
            }

        return output;
    }

    /// <summary>
    ///     获取网关 IP信息
    /// </summary>
    /// <returns></returns>
    private static async Task<Dictionary<string, string>> ReadIp()
    {
        // 获取本机的所有网卡
        var interfaces = NetworkInterface.GetAllNetworkInterfaces();
        var output = new Dictionary<string, string>();
        foreach (var nic in interfaces)
            // 过滤掉非物理网卡和回环网卡
            if (nic.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                nic.NetworkInterfaceType != NetworkInterfaceType.Tunnel &&
                nic.OperationalStatus == OperationalStatus.Up)
            {
                // 获取网卡的IP地址
                var ipProps = nic.GetIPProperties();
                foreach (var ip in ipProps.UnicastAddresses)
                {
                    // 输出IPv4地址
                    if (ip.Address.AddressFamily != AddressFamily.InterNetwork) continue;
                    // windows环境会有网卡名称相同情况
                    if (!output.ContainsKey(nic.Name))
                        output.Add(nic.Name, ip.Address.ToString());
                    else
                        // 相同网卡名称 对地址做拼接处理
                        output[nic.Name] = output[nic.Name] + "/" + ip.Address;
                }
            }

        return output;
    }

    /// <summary>
    ///     获取系统运行时间
    /// </summary>
    /// <returns></returns>
    public static DateTime? GetRunTime()
    {
        var output = ShellUtil.Cmd("wmic", "OS get LastBootUpTime/Value").Result;
        var outputArr = output.Split('=', (char)StringSplitOptions.RemoveEmptyEntries);
        if (outputArr.Length == 2)
            return outputArr[1].Split('.')[0].ParseToDateTime();
        return null;
    }

    /// <summary>
    ///     获取资源使用信息
    /// </summary>
    /// <returns></returns>
    public static async Task<dynamic> UseInfo()
    {
        // 内存获取
        var ramInfo = RamInfo();
        var cpuRate = Math.Ceiling(_cpuUsage);
        var hddInfo = ReadHddInfo();
        var ipAddr = await ReadIp();
        MachineUtil.UseInfo.Network = ipAddr; //网络ip
        MachineUtil.UseInfo.DiskInfo = hddInfo; //磁盘使用
        MachineUtil.UseInfo.MemInfo = ramInfo; //内存
        MachineUtil.UseInfo.CpuRate = cpuRate; // cpu使用率
        return MachineUtil.UseInfo;
    }

    /// <summary>
    ///     内存信息
    /// </summary>
    /// <returns></returns>
    public static MemInfo RamInfo()
    {
        var memoryMetrics = GetWindowsMetrics();
        var memInfo = new MemInfo();
        memInfo.MemAvailable = memoryMetrics.Free.ToLong();
        memInfo.MemUsage = memoryMetrics.Used.ToLong();
        memInfo.MemTotal = memoryMetrics.Total.ToLong();
        memInfo.MemRate = Math.Ceiling(100 * memoryMetrics.Used / memoryMetrics.Total);
        return memInfo;
    }

    /// <summary>
    ///     磁盘信息
    /// </summary>
    /// <returns></returns>
    public static DiskInfo ReadHddInfo()
    {
        // 获取当前程序所在的磁盘
        var currentDrive = Path.GetPathRoot(AppDomain.CurrentDomain.BaseDirectory);
        // 创建DriveInfo实例
        var driveInfo = new DriveInfo(currentDrive);
        var diskSize = driveInfo.TotalSize / 1024;
        var diskAvailable = driveInfo.AvailableFreeSpace / 1024;
        var used = diskSize - diskAvailable;
        var obj = new DiskInfo
        {
            DiskSize = diskSize,
            DiskAvailable = diskAvailable,
            DiskUsed = diskSize - diskAvailable,
            // ReSharper disable once PossibleLossOfFraction
            DiskRate = Math.Ceiling((double)(100 * used / diskSize))
        };
        return obj;
    }

    /// <summary>
    ///     cpu使用情况
    /// </summary>
    private static float _cpuUsage;

    /// <summary>
    ///     cpu使用率
    /// </summary>
    /// <returns></returns>
    public static void CpuRate()
    {
        var cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
        cpuCounter.NextValue(); // 第一次获取值需要先调用一次 NextValue()
        Task.Factory.StartNew(() =>
        {
            while (true)
            {
                _cpuUsage = cpuCounter.NextValue();
                Thread.Sleep(1000); // 间隔一秒钟获取一次
            }
        });
    }

    /// <summary>
    ///     windows系统获取内存信息
    /// </summary>
    /// <returns></returns>
    public static MemoryMetrics GetWindowsMetrics()
    {
        var output = ShellUtil.Cmd("wmic", "OS get FreePhysicalMemory,TotalVisibleMemorySize /Value").Result;
        var metrics = new MemoryMetrics();
        var lines = output.Trim().Split('\n', (char)StringSplitOptions.RemoveEmptyEntries);
        if (lines.Length <= 0) return metrics;
        var freeMemoryParts = lines[0].Split('=', (char)StringSplitOptions.RemoveEmptyEntries);
        var totalMemoryParts = lines[1].Split('=', (char)StringSplitOptions.RemoveEmptyEntries);
        metrics.Total = double.Parse(totalMemoryParts[1]);
        metrics.Free = double.Parse(freeMemoryParts[1]);
        metrics.Used = metrics.Total - metrics.Free;
        return metrics;
    }

    /// <summary>
    ///     内存信息
    /// </summary>
    public class MemoryMetrics
    {
        /// <summary>
        /// </summary>
        public double Total { get; set; }

        /// <summary>
        /// </summary>
        public double Used { get; set; }

        /// <summary>
        /// </summary>
        public double Free { get; set; }
    }

    /// <summary>
    ///     获取或生成网关序列号
    /// </summary>
    /// <returns>16位的随机字符串作为序列号</returns>
    public static string GetSn()
    {
        // SN文件存储路径
        var snFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "gateway_sn.txt");

        // 如果文件存在，直接读取并返回内容
        if (File.Exists(snFilePath))
        {
            var sn = File.ReadAllText(snFilePath).Trim();
            // 确保读取的SN是有效的（长度为16且不为空）
            if (!string.IsNullOrWhiteSpace(sn) && sn.Length == 16)
            {
                return sn;
            }
        }

        // 文件不存在或SN无效，先尝试基于MAC地址生成序列号
        var macBasedSn = GenerateSnFromMac();
        if (!string.IsNullOrEmpty(macBasedSn))
        {
            // 确保目录存在
            var directory = Path.GetDirectoryName(snFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 将基于MAC地址生成的SN写入文件
            File.WriteAllText(snFilePath, macBasedSn);
            return macBasedSn;
        }

        // 如果无法从MAC地址生成，则使用随机生成
        var random = new Random();
        var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        var stringBuilder = new StringBuilder(16);

        for (int i = 0; i < 16; i++)
        {
            stringBuilder.Append(chars[random.Next(chars.Length)]);
        }

        var newSn = stringBuilder.ToString();

        // 确保目录存在
        var directory2 = Path.GetDirectoryName(snFilePath);
        if (!string.IsNullOrEmpty(directory2) && !Directory.Exists(directory2))
        {
            Directory.CreateDirectory(directory2);
        }

        // 将新生成的SN写入文件
        File.WriteAllText(snFilePath, newSn);

        return newSn;
    }

    /// <summary>
    ///     基于MAC地址生成16位序列号
    /// </summary>
    /// <returns>16位序列号，如果无法获取MAC地址则返回null</returns>
    private static string GenerateSnFromMac()
    {
        try
        {
            // 使用PowerShell命令获取网络适配器信息，使用简单的CSV格式输出便于解析
            var output = ShellUtil.Cmd("powershell", "Get-NetAdapter | Select-Object Name, MacAddress | ConvertTo-Csv -NoTypeInformation").Result;
            
            // 解析CSV格式的PowerShell输出获取MAC地址
            string macAddressRaw = null;
            var lines = output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            
            // CSV格式: 第一行是标题，从第二行开始解析
            for (int i = 1; i < lines.Length; i++)
            {
                var line = lines[i].Trim();
                if (!string.IsNullOrEmpty(line))
                {
                    // 解析CSV格式，正确处理引号
                    var parts = line.Split(',');
                    if (parts.Length >= 2)
                    {
                        // 第二列是MAC地址，移除可能的引号
                        var macPart = parts[1].Trim().Trim('"');
                        if (!string.IsNullOrEmpty(macPart) && macPart != "----------")
                        {
                            macAddressRaw = macPart;
                            break;
                        }
                    }
                }
            }
            
            // 如果CSV解析失败，尝试使用备选方法
            if (string.IsNullOrEmpty(macAddressRaw) || macAddressRaw == "----------")
            {
                // 使用更直接的PowerShell命令获取第一个网络适配器的MAC地址
                output = ShellUtil.Cmd("powershell", "(Get-NetAdapter)[0].MacAddress").Result;
                output = output.Trim();
                if (!string.IsNullOrEmpty(output) && output != "----------")
                {
                    macAddressRaw = output;
                }
            }
            
            // 如果仍然失败，使用原来的NetworkInterface方法
            if (string.IsNullOrEmpty(macAddressRaw) || macAddressRaw == "----------")
            {
                // 获取本机的所有网卡
                var interfaces = NetworkInterface.GetAllNetworkInterfaces();
                
                // 查找第一个有效的物理网卡（非回环、非隧道、活动状态）
                foreach (var nic in interfaces)
                {
                    if (nic.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                        nic.NetworkInterfaceType != NetworkInterfaceType.Tunnel &&
                        nic.OperationalStatus == OperationalStatus.Up)
                    {
                        // 获取MAC地址
                        var nicMac = nic.GetPhysicalAddress().ToString();
                        if (!string.IsNullOrEmpty(nicMac))
                        {
                            macAddressRaw = nicMac;
                            Console.WriteLine($"[GatewayUtil] NetworkInterface获取到MAC地址: {macAddressRaw} (来自网卡: {nic.Name})");
                            break;
                        }
                    }
                }
            }
            
            
            // 如果找到MAC地址
            if (!string.IsNullOrEmpty(macAddressRaw) && macAddressRaw != "----------")
            {
                // 移除所有非字母数字字符
                var macAddress = System.Text.RegularExpressions.Regex.Replace(macAddressRaw, "[^a-zA-Z0-9]", "");
                Console.WriteLine($"[GatewayUtil] 处理后的MAC地址: {macAddress}");
                return macAddress;
            }
            
            Console.WriteLine("[GatewayUtil] 未能获取有效MAC地址，将返回null");
            return null; // 如果没有找到有效的MAC地址
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[GatewayUtil] 获取MAC地址时发生异常: {ex.Message}");
            // 出现异常时返回null，让调用方回退到随机生成
            return null;
        }
    }
}