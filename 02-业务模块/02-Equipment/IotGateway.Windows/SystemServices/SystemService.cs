using Microsoft.AspNetCore.Authorization;

namespace IotGateway.Equipment.SystemServices;

/// <summary>
///     网关默认配置
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-12-19
/// </summary>
[ApiDescriptionSettings("网关配置")]
public class SystemService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     获取网络配置文件内容
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/getNetWork")]
    [AllowAnonymous]
    public async Task<byte[]> GetNetWork()
    {
        throw Oops.Oh("暂不支持!");
    }

    /// <summary>
    ///     写入网络配置文件内容
    /// </summary>
    /// <returns></returns>
    [HttpPost("/network/setNetWork")]
    [AllowAnonymous]
    public async Task SetNetWork(BaseId<byte[]> input)
    {
        throw Oops.Oh("暂不支持!");
    }

    /// <summary>
    ///     保存服务器配置
    /// </summary>
    /// <returns></returns>
    [HttpPost("/network/save")]
    public async Task SetNetWorkConfig(NetworkSettingModel input)
    {
        throw Oops.Oh("暂不支持!");
    }

    /// <summary>
    ///     获取服务器ip配置列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/list")]
    [AllowAnonymous]
    public async Task<NetworkSettingModel> GetNetWorkList()
    {
        throw Oops.Oh("暂不支持!");
    }

    /// <summary>
    ///     IpAddress
    /// </summary>
    /// <returns></returns>
    [HttpGet("/shell/ipAddress")]
    public async Task<List<string>> Ip()
    {
        return await GatewayUtil.IfConfig();
    }
}