using Feng.IotGateway.Core.Base;
using Feng.IotGateway.Core.Const;
using Feng.IotGateway.Core.Util;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Furion.FriendlyException;
using Furion.JsonSerialization;
using Furion.LinqBuilder;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using DateTime = Common.Extension.DateTime;

namespace IotGateway_Web_Windows.Services;

/// <summary>
///     网关默认配置
///     版 本:V4.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2024-04-08
/// </summary>
[ApiDescriptionSettings("网关配置")]
public class AuthorizationService : IDynamicApiController, ITransient
{
    private const string BasePath = "/etc/DeviceConf/";

    /// <summary>
    ///     授权
    /// </summary>
    /// <returns></returns>
    [HttpPost("/authorization/set")]
    public async Task<SetAuthorizeOutput> SetAuthorization(BaseId<string> input)
    {
        var currentDirectory = Directory.GetCurrentDirectory(); // 获取当前目录
        var keyFilePath = Path.Combine(currentDirectory, "RSA.Private"); // 构建完整文件路径
        var privateKey = await File.ReadAllTextAsync(keyFilePath);
        var decryptPwd = AuthorizationUtil.Decrypt(input.Id, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = AuthorizationCodeGenerator.ValidateAuthorizationCode(authorize.Sn);
        if (!sn)
            throw new Exception("Sn不一致！");
        // 校验是否到期
        if (DateTime.Now() >= Convert.ToDateTime(authorize.EndTime))
            throw new Exception("已过期！");
        if (!Directory.Exists(BasePath))
            Directory.CreateDirectory(BasePath);
        await File.WriteAllTextAsync(BasePath + "key.txt", input.Id);
        if (!File.Exists(BasePath + "Ident.txt"))
        {
            await File.WriteAllTextAsync(BasePath + "Ident.txt", "fengEdge-windows");
            authorize.Ident = "fengEdge-windows";
        }
        else
        {
            var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
            authorize.Ident = readIdent ?? "fengEdge-windows";
        }

        return authorize;
    }

    /// <summary>
    ///     校验是否授权
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/isAuthorization")]
    [HttpGet("/api/authorization/isAuthorization")]
    public async Task<bool> Authorization()
    {
        var value = await File.ReadAllTextAsync(BasePath + "key.txt");
        if (value == null)
            throw Oops.Oh("未授权！");
        var currentDirectory = Directory.GetCurrentDirectory(); // 获取当前目录
        var keyFilePath = Path.Combine(currentDirectory, "RSA.Private"); // 构建完整文件路径
        var privateKey = await File.ReadAllTextAsync(keyFilePath);
        var decryptPwd = AuthorizationUtil.Decrypt(value, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = AuthorizationCodeGenerator.ValidateAuthorizationCode(authorize.Sn);
        if (sn) return DateTime.Now() < Convert.ToDateTime(authorize.EndTime);
        return false;
    }

    /// <summary>
    ///     获取授权信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/getAuthorization")]
    [HttpGet("/api/authorization/getAuthorization")]
    [AllowAnonymous]
    public async Task<SetAuthorizeOutput> GetAuthorization()
    {
        var setAuthor = await AuthorizationConfig();
        if (setAuthor != null) return setAuthor;
        var auth = new SetAuthorizeOutput
        {
            Fingerprint = "",
            Ident = "fengEdge-windows",
            Sn = AuthorizationCodeGenerator.GenerateAuthorizationCode(),
            Version = "V" + MachineUtil.Version,
            DeviceNumber = 0,
            EndTime = null,
            VariableNumber = 0
        };
        if (!File.Exists(BasePath + "Ident.txt")) return auth;
        var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
        auth.Ident = readIdent ?? "fengEdge-windows";
        return auth;
    }
    
    /// <summary>
    ///     获取授权信息
    /// </summary>
    /// <returns></returns>
    private async Task<SetAuthorizeOutput?> AuthorizationConfig()
    {
        if (!File.Exists(GatewayFilePath.KeyPath))
            return null;
        var readKeyValue = await File.ReadAllTextAsync(GatewayFilePath.KeyPath);
        if (readKeyValue.IsNullOrEmpty())
            return null;
        try
        {
            var currentDirectory = Directory.GetCurrentDirectory(); // 获取当前目录
            var keyFilePath = Path.Combine(currentDirectory, "RSA.Private"); // 构建完整文件路径
            var privateKey = await File.ReadAllTextAsync(keyFilePath);
            var decryptPwd = AuthorizationUtil.Decrypt(readKeyValue, privateKey);
            var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
            if (authorize.Sn.IsNullOrEmpty())
                return null;
            authorize.Version = "V" + MachineUtil.Version;
            authorize.Ident = await AuthorizationUtil.GatewayName();
            return authorize;
        }
        catch
        {
            return null;
        }
    }
}