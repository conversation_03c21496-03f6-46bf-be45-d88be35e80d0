using System.Text;
using System.Net.NetworkInformation;
using System.IO;
using System.Linq;
using Furion.DataEncryption;

namespace IotGateway_Web_Windows;

/// <summary>
/// </summary>
public class AuthorizationCodeGenerator
{
  /// <summary>
  ///     根据指定的硬件信息生成授权码。
  /// </summary>
  /// <param name="hardDiskSerialNumber">硬盘序列号。</param>
  /// <param name="cpuId">CPU ID。</param>
  /// <param name="macAddress">MAC地址。</param>
  /// <returns>生成的授权码字符串。</returns>
  public static string GenerateAuthorizationCode(string hardDiskSerialNumber, string cpuId, string macAddress)
  {
    // 拼接硬件特征信息
    var hardwareFingerprint = $"{hardDiskSerialNumber}-{cpuId}-{macAddress}";
    // 使用MD5算法对硬件特征信息进行加密
    var authorizationCode = ComputeMD5Hash(hardwareFingerprint);
    return authorizationCode;
  }

  /// <summary>
  ///     验证给定的授权码与当前系统的硬件信息是否一致。
  /// </summary>
  /// <param name="providedAuthorizationCode">要验证的授权码。</param>
  /// <returns>如果授权码与当前系统硬件信息生成的授权码一致，返回true；否则返回false。</returns>
  public static bool ValidateAuthorizationCode(string? providedAuthorizationCode)
  {
    var currentAuthorizationCode = GenerateAuthorizationCode(
        GetHardDiskSerialNumber(),
        GetCpuId(),
        GetMacAddress());

    return string.Equals(providedAuthorizationCode, currentAuthorizationCode, StringComparison.OrdinalIgnoreCase);
  }

  /// <summary>
  ///     根据当前系统的硬件信息生成授权码。
  /// </summary>
  /// <returns>生成的授权码字符串。</returns>
  public static string GenerateAuthorizationCode()
  {
    // 获取硬件信息
    var hardDiskSerialNumber = GetHardDiskSerialNumber();
    var cpuId = GetCpuId();
    var macAddress = GetMacAddress();

    // 拼接硬件特征信息
    var hardwareFingerprint = $"{hardDiskSerialNumber}-{cpuId}-{macAddress}";

    // 使用MD5算法对硬件特征信息进行加密
    var authorizationCode = ComputeMD5Hash(hardwareFingerprint);
    return authorizationCode;
  }

  /// <summary>
  ///     获取硬盘序列号。
  /// </summary>
  /// <returns>硬盘序列号，若无法获取则返回空字符串。</returns>
  private static string GetHardDiskSerialNumber()
  {
    // 使用持久化的设备ID
    return GetOrCreateDeviceId("disk");
  }

  /// <summary>
  ///     获取CPU ID。
  /// </summary>
  /// <returns>CPU ID，若无法获取则返回空字符串。</returns>
  private static string GetCpuId()
  {
    // 使用持久化的设备ID
    return GetOrCreateDeviceId("cpu");
  }

  /// <summary>
  ///     获取物理网络适配器的MAC地址。
  /// </summary>
  /// <returns>MAC地址，若无法获取则返回空字符串。</returns>
  private static string GetMacAddress()
  {
    // 尝试使用NetworkInterface API获取MAC地址
    try
    {
      // 获取所有网络接口
      var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();

      // 查找第一个有效的物理网卡（非回环、非隧道、活动状态）
      foreach (var nic in networkInterfaces)
      {
        if (nic.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
            nic.NetworkInterfaceType != NetworkInterfaceType.Tunnel &&
            nic.OperationalStatus == OperationalStatus.Up)
        {
          // 获取MAC地址
          var macBytes = nic.GetPhysicalAddress().GetAddressBytes();
          if (macBytes.Length > 0)
          {
            return BitConverter.ToString(macBytes).Replace("-", ":");
          }
        }
      }
    }
    catch
    {
      // 如果方法失败，使用持久化的设备ID
    }

    // 如果无法获取MAC地址，使用持久化的设备ID
    return GetOrCreateDeviceId("mac");
  }

  /// <summary>
  ///     计算指定字符串的MD5哈希值。
  /// </summary>
  /// <param name="input">待计算哈希的字符串。</param>
  /// <returns>MD5哈希值的字节数组。</returns>
  private static string ComputeMD5Hash(string input)
  {
    return MD5Encryption.Encrypt(Encoding.UTF8.GetBytes(input), false, true);
  }

  /// <summary>
  ///     获取或创建设备唯一标识符
  /// </summary>
  /// <param name="fileType">标识符类型，用于文件名</param>
  /// <returns>设备唯一标识符</returns>
  private static string GetOrCreateDeviceId(string fileType)
  {
    // 定义存储标识符的文件路径 - 使用应用程序目录
    string filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, $"device_{fileType}_id.txt");

    // 如果文件存在，读取并返回标识符
    if (File.Exists(filePath))
    {
      try
      {
        var id = File.ReadAllText(filePath).Trim();
        if (!string.IsNullOrEmpty(id))
          return id;
      }
      catch
      {
        // 如果读取失败，继续创建新的标识符
      }
    }

    // 生成新的唯一标识符
    string newId;
    if (fileType == "disk")
    {
      // 对于磁盘ID，使用一种基于时间和随机数的算法
      var random = new Random();
      var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
      newId = $"DISK-{timestamp}-{random.Next(1000, 9999)}";
    }
    else if (fileType == "cpu")
    {
      // 对于CPU ID，使用另一种格式
      var random = new Random();
      newId = $"CPU-{random.Next(10000, 99999)}-{random.Next(10000, 99999)}";
    }
    else if (fileType == "mac")
    {
      // 对于MAC地址，使用特定格式
      var random = new Random();
      newId = string.Join(":", Enumerable.Range(0, 6)
        .Select(_ => random.Next(0, 256).ToString("X2")));
    }
    else
    {
      // 默认使用GUID
      newId = Guid.NewGuid().ToString("N");
    }

    // 尝试写入文件
    try
    {
      File.WriteAllText(filePath, newId);
    }
    catch
    {
      // 如果写入失败，仍然返回生成的ID
    }

    return newId;
  }
}