using IotGateway_Web_Windows.Services;
using IotGateway.Equipment.BackgroundServices;
using IotGateway.Equipment.SystemServices;
using Microsoft.Extensions.DependencyInjection;
using DateTime = System.DateTime;

namespace IotGateway.Equipment;

/// <summary>
/// </summary>
[AppStartup(1)]
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        MachineUtil.CurrentSystemEnvironment = CurrentSystemEnvironmentEnum.Windows;
        MachineUtil.Authorize = App.GetService<AuthorizationService>().GetAuthorization().GetAwaiter().GetResult();
        MachineUtil.SystemRunTime = GatewayUtil.GetRunTime() ?? DateTime.Now;
        GatewayUtil.CpuRate();
        // 网关运行状态
        services.AddHostedService<RunStatusTimer>();
    }
}