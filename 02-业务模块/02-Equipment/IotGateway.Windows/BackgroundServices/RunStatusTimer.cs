using System.Runtime.InteropServices;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Equipment.BackgroundServices;

/// <summary>
///     网关运行状态
/// </summary>
public class RunStatusTimer : BackgroundService
{
    private readonly ILogger<RunStatusTimer> _logger;
    private readonly Crontab _crontab;

    /// <summary>
    ///     socket消息推送
    /// </summary>
    private readonly SendMessageService _socket;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="socket"></param>
    public RunStatusTimer(ILogger<RunStatusTimer> logger, SendMessageService socket)
    {
        _logger = logger;
        _socket = socket;
        _crontab = Crontab.Parse("0/5 * * ? * *", CronStringFormat.WithSeconds);
    }

    /// <summary>
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var taskFactory = new TaskFactory(TaskScheduler.Current);
            await taskFactory.StartNew(async () =>
            {
                try
                {
                    var infoMsg = await GatewayUtil.UseInfo();
                    await _socket.Send(JSON.Serialize(infoMsg), ConstMethod.SysRunStatus);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"定时推送网关运行状态 Error:【{ex.Message}】");
                }
            }, stoppingToken);
            await Task.Delay((int) _crontab.GetSleepMilliseconds(DateTime.Now()), stoppingToken);
        }
    }
}