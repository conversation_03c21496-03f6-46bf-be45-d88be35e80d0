<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <OutputPath>..\..\..\01-应用服务\IotGateway/bin/$(Configuration)/$(TargetFramework)/plugins/equipment/</OutputPath>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
        <DocumentationFile>IotGateway.Gt675x.xml</DocumentationFile>
        <RootNamespace>IotGateway.Equipment</RootNamespace>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
        <DocumentationFile>IotGateway.Gt675x.xml</DocumentationFile>
        <RootNamespace>IotGateway.Equipment</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\04-架构核心\IotGateway.Core\IotGateway.Core.csproj"/>
        <ProjectReference Include="..\..\..\04-架构核心\IotGateway.WebSocket\IotGateway.WebSocket.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <None Remove="IotGateway.Gt675x.xml"/>
        <None Update="IotGateway.Gt675x.xml">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>
