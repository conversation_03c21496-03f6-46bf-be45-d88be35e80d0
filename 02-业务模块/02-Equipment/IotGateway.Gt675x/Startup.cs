using IotGateway.Equipment.BackgroundServices;
using IotGateway.Equipment.SystemServices;
using Microsoft.Extensions.DependencyInjection;

namespace IotGateway.Equipment;

/// <summary>
/// </summary>
[AppStartup(1)]
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        // lite版本小网关
        MachineUtil.CurrentSystemEnvironment = CurrentSystemEnvironmentEnum.Gt675X;
        MachineUtil.Authorize = App.GetService<SystemService>().GetAuthorization().GetAwaiter().GetResult();
        // 网关运行状态
        services.AddHostedService<RunStatusTimer>();
        // 创建基本目录
        Task.Run(async () => { await ShellUtil.Bash("mkdir -p /Edge/Data  /Edge/picture /Edge/Redis /etc/DeviceConf /Edge/DncFile "); });
        // 授权目录
        Task.Run(async () => { await ShellUtil.Bash("chmod -R 777 /usr/local/src /Edge /etc/DeviceConf"); });
        // 删除历史更新包
        Task.Run(async () => { await ShellUtil.Bash("rm -rf /usr/local/src/*-encrypt.rar"); });
    }
}