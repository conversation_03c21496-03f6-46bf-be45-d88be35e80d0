using Furion.Logging;
using StringExtension = Common.Extension.StringExtension;

namespace IotGateway.Equipment.Util;

/// <summary>
///     网关工具
/// </summary>
public static class GatewayUtil
{
    /// <summary>
    ///     获取Sn码
    /// </summary>
    /// <returns></returns>
    public static async Task<string> GetSn()
    {
        var path = "/etc/DeviceConf/guid.txt";
        if (!File.Exists(path))
        {
            await File.WriteAllTextAsync(path, $"{GenerateId()}");
        }
        var guid = await File.ReadAllTextAsync(path);
        return guid.IsNullOrEmpty() ? null : guid;
    }
    
    private static string GenerateId()
    {
        long i = 1;
        foreach (byte b in Guid.NewGuid().ToByteArray())
        {
            i *= ((int)b + 1);
        }
        return string.Format("{0:x}", i - DateTime.Now.Ticks);
    }

    /// <summary>
    ///     IfConfig
    /// </summary>
    /// <returns></returns>
    public static async Task<string> IfConfig(string ipName)
    {
        var hddInfo = await ShellUtil.Bash($"ifconfig {ipName}");

        var lines = hddInfo.Split('\n');
        var macAddr = "";

        foreach (var item in lines)
        {
            if (item.Contains("Link"))
            {
                var spList = item.Split(" ");
                var mac = false;
                foreach (var sp in spList)
                {
                    if (mac && StringExtension.IsNotNull(sp)) macAddr += " HWaddr:" + sp;

                    if (sp == "HWaddr")
                        mac = true;
                }
            }

            if (!item.Contains("inet")) continue;
            return ipName == "usb0"
                ? "5G" + "    " + item.TrimStart() + macAddr + " "
                : ipName + "    " + item.TrimStart() + macAddr + " ";
        }

        return "";
    }

    /// <summary>
    ///     获取CPU使用率
    /// </summary>
    /// <returns></returns>
    private static async Task<string> CpuRate()
    {
        var output = await ShellUtil.Bash("top -b -n1 | grep \"CPU\" | grep -v grep  | awk '{print $2}' | head -n 1");
        var cpuRate = output.Trim().Replace("%", "");
        return cpuRate;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    private static DiskInfo ReadHddInfo()
    {
        try
        {
            var hdd = new DiskInfo();
            // 大小
            long size = 0;
            // 使用
            long used = 0;
            // 可使用
            long avail = 0;
            var hddInfo = ShellUtil.Bash("df -k").GetAwaiter().GetResult();
            var lines = hddInfo.Split('\n');
            foreach (var item in lines)
            {
                if (!item.Contains("/dev/mmcblk0p3")) continue;
                var li = item.Split(new[] {' '}, StringSplitOptions.RemoveEmptyEntries);
                for (var i = 0; i < li.Length; i++)
                {
                    if (!li[i].Contains("%")) continue;

                    size += Convert.ToInt64(li[i - 3]);
                    used += Convert.ToInt64(li[i - 2]);
                    avail += Convert.ToInt64(li[i - 1]);
                    // Usage = Convert.ToDouble(li[i].Replace("%", "").Trim())
                    break;
                }
            }

            hdd.DiskSize = size;
            hdd.DiskUsed = used;
            hdd.DiskAvailable = avail;
            //使用率 = 已经使用 /总量 *100
            hdd.DiskRate = (double) Math.Round((decimal) (used / size * 100), 2);

            return hdd;
        }
        catch (Exception ex)
        {
            Log.Error("【ReadHddInfo:】" + ex.Message);
            return null;
        }
    }

    /// <summary>
    ///     获取网关 IP信息
    /// </summary>
    /// <returns></returns>
    private static async Task<string> ReadIp(string networkName)
    {
        var hddInfo = await ShellUtil.Bash("ifconfig");

        var isName = false;

        var lines = hddInfo.Split('\n');
        foreach (var item in lines)
        {
            if (item.StartsWith(networkName))
                isName = true;
            if (!item.Contains("inet") || !isName) continue;
            var li = item.Split(new[] {' '}, StringSplitOptions.RemoveEmptyEntries);
            return li[1].Replace("addr:", "").Trim();
        }

        return null;
    }

    /// <summary>
    ///     获取资源使用信息
    /// </summary>
    /// <returns></returns>
    public static async Task<dynamic> UseInfo()
    {
        // 内存获取
        var ramInfo = MachineUtil.RamInfo();
        var cpuRate = Math.Ceiling(double.Parse(await CpuRate()));
        var hddInfo = ReadHddInfo();
        var networkList = new List<string> {"eth0", "eth1", "eth2", "wlan0", "usb0"};
        var ipAddr = new Dictionary<string, string>();
        foreach (var network in networkList)
        {
            var ip = await ReadIp(network);
            if (ip != null)
            {
                // 5G网卡名强制转换
                ipAddr.Add(network == "usb0" ? "eth2" : network, ip);
                switch (network)
                {
                    case "eth0":
                        MachineUtil.Clay.Eth0 = ip;
                        break;
                    case "eth1":
                        MachineUtil.Clay.Eth1 = ip;
                        break;
                    case "eth2":
                    case "usb0":
                        MachineUtil.Clay.Eth2 = ip;
                        break;
                    case "wlan0":
                        MachineUtil.Clay.Wifi = ip;
                        break;
                }
            }
        }

        MachineUtil.UseInfo.Network = ipAddr; //网络ip
        MachineUtil.UseInfo.DiskInfo = hddInfo; //磁盘使用
        MachineUtil.UseInfo.MemInfo = ramInfo; //内存
        MachineUtil.UseInfo.CpuRate = cpuRate; // cpu使用率
        MachineUtil.UseInfo.Signal = null; //4G信号
        return MachineUtil.UseInfo;
    }
}