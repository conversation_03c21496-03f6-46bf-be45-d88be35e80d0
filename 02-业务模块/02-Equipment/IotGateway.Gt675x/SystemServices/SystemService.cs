using Common.Extension;
using Furion.Templates;
using Microsoft.AspNetCore.Authorization;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Equipment.SystemServices;

/// <summary>
///     网关默认配置
///     版 本:V3.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2024-01-22
/// </summary>
[ApiDescriptionSettings("网关配置")]
public class SystemService : IDynamicApiController, ITransient
{
    private readonly ILogger<SystemService> _logger;
    private readonly string _baseDirectory = AppDomain.CurrentDomain.BaseDirectory;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    public SystemService(ILogger<SystemService> logger)
    {
        _logger = logger;
    }
    
    /// <summary>
    /// 支持串口集合
    /// </summary>
    [HttpGet("/shell/serialPorts")]
    public Task<dynamic> GetSerialPorts()
    {
        return Task.FromResult<dynamic>(new Dictionary<string, string>
        {
            {"/dev/ttyS1", "ttyS1：A1/B1(485)"},
            {"/dev/ttyS2", "ttyS2：A2/B2(485)"},
            {"/dev/ttyS3", "ttyS3：A1/B1(485) TX/RX(232)"},
            {"/dev/ttyS4", "ttyS4：A1/B1(485) TX/RX(232)"},
        });
    }
    
    /// <summary>
    /// 网卡名称集合
    /// </summary>
    [HttpGet("/shell/networks")]
    public async Task<dynamic> GetNetworks()
    {
        var result = await ShellUtil.Bash("ip addr show | grep inet | awk '$NF ~ /^[a-zA-Z0-9]+$/ && $NF !~ /^(docker0|host|link|lo|tap0)$/ {print $2,$NF}'");
        var lines = result.Split('\n');
        var ipToInterface = new Dictionary<string, string> {{"不指定", ""}};
        foreach (var line in lines)
        {
            var parts = line.Split(new[] {' '}, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length != 2) continue;
            var ipAddress = parts[0];
            var interfaceName = parts[1];
            ipToInterface[interfaceName] = ipAddress;
        }
        return ipToInterface;
    }

    #region 网关授权

    private const string BasePath = "/etc/DeviceConf/";

    /// <summary>
    ///     授权
    /// </summary>
    /// <returns></returns>
    [HttpPost("/authorization/set")]
    public async Task<SetAuthorizeOutput> SetAuthorization(BaseId<string> input)
    {
        var privateKey = await File.ReadAllTextAsync(BasePath + "RSA.Private");
        var decryptPwd = AuthorizationUtil.Decrypt(input.Id, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() != sn)
            throw new Exception("Sn不一致！");
        // 校验是否到期
        if (DateTime.Now() >= Convert.ToDateTime(authorize.EndTime))
            throw new Exception("已过期！");
        await File.WriteAllTextAsync(BasePath + "key.txt", input.Id);
        if (!File.Exists(BasePath + "Ident.txt"))
        {
            await File.WriteAllTextAsync(BasePath + "Ident.txt", "fengEdge-675");
            authorize.Ident = "fengEdge-675";
        }
        else
        {
            var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
            authorize.Ident = readIdent ?? "fengEdge-675";
        }

        return authorize;
    }

    /// <summary>
    ///     校验是否授权
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/isAuthorization")]
    public async Task<bool> Authorization()
    {
        var value = await File.ReadAllTextAsync(BasePath + "key.txt");
        if (value == null)
            throw Oops.Oh("未授权！");
        var privateKey = await File.ReadAllTextAsync(BasePath + "RSA.Private");
        var decryptPwd = AuthorizationUtil.Decrypt(value, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() == sn.Trim()) return DateTime.Now() < Convert.ToDateTime(authorize.EndTime);
        return false;
    }

    /// <summary>
    ///     获取授权信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/getAuthorization")]
    [AllowAnonymous]
    public async Task<SetAuthorizeOutput> GetAuthorization()
    {
        var setAuthor = await AuthorizationUtil.GetAuthorization();
        if (setAuthor != null) return setAuthor;
        //如果无法获取到授权信息，默认自行组装
        var sn = await GatewayUtil.GetSn();
        var auth = new SetAuthorizeOutput
        {
            Fingerprint = "",
            Ident = "fengEdge-675",
            Sn = sn,
            Version = "V" + MachineUtil.Version,
            DeviceNumber = 0,
            EndTime = null,
            VariableNumber = 0
        };
        if (!File.Exists(BasePath + "Ident.txt")) return auth;
        var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
        auth.Ident = readIdent ?? "fengEdge-675";
        return auth;
    }

    #endregion

    #region 网络配置

    /// <summary>
    ///     保存服务器配置
    /// </summary>
    /// <returns></returns>
    [HttpPost("/network/save")]
    public async Task SetNetWorkConfig(NetworkSettingModel input)
    {
        await TaskQueued.EnqueueAsync(async (_, cancellationToken) =>
        {
            if (input.Wifi is {Enable: true})
            {
                // 配置wifi
                await SetEth(input.Wifi.Adapt<NetworkInfoModel>());
                // 修改wifi
                await SaveWifiConfig(input.Wifi);
                _logger.LogWarning($"【网络配置】-----【wifi】修改完成！");
            }
            await WriteWifiStatus(input.Wifi is {Enable: true} ? 0 : 1);
            _logger.LogWarning("【网络配置】-----【wifi】状态,写入完成！");

            foreach (var network in input.Network)
            {
                // 修改ip
                await SetEth(network);
                _logger.LogWarning($"【网络配置】-----【{network.NetWorkName}】配置修改完成！");
            }

            // 修改Dns
            await SaveDnsConfig(input.Network);
            _logger.LogWarning("【网络配置】-----【DNS】配置修改完成！");

            // 将4G,5G配置写入到文件中
            var jsonfile = Path.Combine("/Edge/"); //文件路径
            await File.WriteAllTextAsync(jsonfile + "mobileConfig.txt", JSON.Serialize(input.Mobile), cancellationToken);
            _logger.LogWarning("【网络配置】-----【重启】 自动重启生效网络配置！");
            // 手动重启
            await ShellUtil.Bash("reboot");
        }, 3000);
    }

    /// <summary>
    ///     获取服务器ip配置列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/list")]
    [AllowAnonymous]
    public async Task<NetworkSettingModel> GetNetWorkList()
    {
        var output = new NetworkSettingModel {Network = new List<NetworkInfoModel>()};
        output.Network.Add(await GetNetworkConfig("eth0"));
        output.Network.Add(await GetNetworkConfig("eth1"));
        output.Network = await GetDnsConfig(output.Network);
        output.Mobile = new MobileModel {OperatorName = ""};
        // output.Wifi = new(){ NetWorkName = "wlan0"};
        output.Wifi = (await GetNetworkConfig("wlan0")).Adapt<WifiModel>();
        // 获取wifi的连接信息
        await GetWifiConfig(output.Wifi);
        output.Wifi.Enable = false;
        // 读取wifi状态
        var wifiStatus = await ReadWifiStatus();
        if (wifiStatus.IsNotNullOrWhiteSpace())
            if (wifiStatus.Contains("wifi=1") && output.Wifi != null)
                output.Wifi.Enable = true;

        var mobileConfig = await ReadMobileConfig();
        if (!mobileConfig.IsNotNullOrWhiteSpace()) return output;
        output.Mobile = JSON.Deserialize<MobileModel>(mobileConfig);
        return output;
    }

    #region 私有方法

    /// <summary>
    ///     修改ip
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task SetEth(NetworkInfoModel input)
    {
        var path = input.NetWorkName == "eth0" ? "/etc/net.conf" :  input.NetWorkName == "eth1" ? "/etc/net2.conf" : "/etc/wlan0.conf";
        var eth0 = File.OpenText(path);
        var builder = new StringBuilder();
        while (!eth0.EndOfStream)
        {
            var line = await eth0.ReadLineAsync();
            if (line == null) continue;
            if (line.StartsWith("METHOD"))
            {
                builder.AppendLine(input.NetWorkType == NetWorkTypeEnum.Static ? "METHOD=STATIC" : "METHOD=DHCP");
                continue;
            }

            if (line.Contains("IPADDR"))
            {
                builder.AppendLine($"IPADDR={input.IPAddress}");
                continue;
            }

            if (line.Contains("NETMASK"))
            {
                builder.AppendLine($"NETMASK={input.SubnetMark}");
                continue;
            }

            if (line.Contains("#GATEWAY") || line.Contains("GATEWAY"))
            {
                builder.AppendLine(input.DefRoute ? $"GATEWAY={input.Gateway}" : $"#GATEWAY={input.Gateway}");
                continue;
            }

            if (line.IsNullOrEmpty()) builder.AppendLine(line);
        }

        eth0.Dispose();
        await File.WriteAllTextAsync(path, builder.ToString());
    }

    /// <summary>
    ///     设备Dns
    /// </summary>
    /// <param name="input"></param>
    private async Task SaveDnsConfig(List<NetworkInfoModel> input)
    {
        var dnsConfig = File.OpenText(_baseDirectory + "Shell/dns_config.sh");
        var builder = new StringBuilder();
        while (!dnsConfig.EndOfStream)
        {
            var line = await dnsConfig.ReadLineAsync();

            if (line == null) continue;
            if (!line.StartsWith("nameserver1=") && !line.StartsWith("nameserver2=") && !line.StartsWith("nameserver3=") && !line.StartsWith("nameserver4="))
            {
                builder.AppendLine(line);
                continue;
            }

            if (line.StartsWith("nameserver1="))
            {
                // 网口0
                var eth0 = input.FirstOrDefault(w => w.NetWorkName == "eth0");
                if (eth0 != null && !string.IsNullOrEmpty(eth0.Dns))
                    builder.AppendLine($"nameserver1=\"{eth0.Dns}\"");
                else
                    builder.AppendLine(line);
                continue;
            }

            if (line.StartsWith("nameserver2="))
            {
                // 网口0
                var eth0 = input.FirstOrDefault(w => w.NetWorkName == "eth0");
                if (eth0 != null && !string.IsNullOrEmpty(eth0.DnsBack))
                    builder.AppendLine($"nameserver2=\"{eth0.DnsBack}\"");
                else
                    builder.AppendLine(line);
                continue;
            }

            if (line.StartsWith("nameserver3="))
            {
                // 网口0
                var eth1 = input.FirstOrDefault(w => w.NetWorkName == "eth1");
                if (eth1 != null && !string.IsNullOrEmpty(eth1.Dns))
                    builder.AppendLine($"nameserver3=\"{eth1.Dns}\"");
                else
                    builder.AppendLine(line);
                continue;
            }

            if (line.Contains("nameserver4="))
            {
                // 网口1
                var eth1 = input.FirstOrDefault(w => w.NetWorkName == "eth1");
                if (eth1 != null && !string.IsNullOrEmpty(eth1.DnsBack))
                    builder.AppendLine($"nameserver4=\"{eth1.DnsBack}\"");
                else
                    builder.AppendLine(line);
            }
        }

        dnsConfig.Dispose();
        await File.WriteAllTextAsync(_baseDirectory + "Shell/dns_config.sh", builder.ToString());
    }

    /// <summary>
    ///     获取ip
    /// </summary>
    /// <returns></returns>
    [NonAction]
    private async Task<NetworkInfoModel> GetNetworkConfig(string networkName)
    {
        // eth0 对应 /etc/net.conf
        // eth1 对应 /etc/net2.conf
        // wlan0 对应 /etc/wlan0.conf
        var path = networkName == "eth0" ? "/etc/net.conf" :  networkName == "eth1" ? "/etc/net2.conf" : "/etc/wlan0.conf";
        var output = new NetworkInfoModel {NetWorkName = networkName};
        var configFile = File.OpenText(path);
        while (!configFile.EndOfStream)
        {
            var line = await configFile.ReadLineAsync();
            if (line == null)  continue;
            if (line.StartsWith("METHOD"))
            {
                output.NetWorkType = line.Contains("STATIC") ? NetWorkTypeEnum.Static : NetWorkTypeEnum.Dhcp;
                continue;
            }

            if (line.Contains("IPADDR"))
            {
                output.IPAddress = line.Replace("IPADDR=", "").Trim();
                continue;
            }

            if (line.Contains("NETMASK"))
            {
                output.SubnetMark = line.Replace("NETMASK=", "").Trim();
                continue;
            }

            if (line.Contains("#GATEWAY") || line.Contains("GATEWAY"))
            {
                output.DefRoute = !line.Contains("#GATEWAY");
                output.Gateway = line.Replace("GATEWAY=", "").Replace("#","").Trim();
                continue;
            }
        }

        configFile.Dispose();

        return output;
    }


    /// <summary>
    ///     获取Dns
    /// </summary>
    /// <returns></returns>
    [NonAction]
    private async Task<List<NetworkInfoModel>> GetDnsConfig(List<NetworkInfoModel> input)
    {
        var configFile = File.OpenText(_baseDirectory + "Shell/dns_config.sh");
        while (!configFile.EndOfStream)
        {
            var line = await configFile.ReadLineAsync();
            if (line == null)
                continue;

            if (!line.StartsWith("nameserver1=") && !line.StartsWith("nameserver2=") && !line.StartsWith("nameserver3=") && !line.StartsWith("nameserver4="))
                continue;
            if (line.StartsWith("nameserver1="))
            {
                // 网口0
                var eth0 = input.FirstOrDefault(w => w.NetWorkName == "eth0");
                if (eth0 != null)
                    eth0.Dns = line.Replace("nameserver1=\"", "").Replace("\"", "").Trim();
                continue;
            }

            if (line.StartsWith("nameserver2="))
            {
                // 网口0
                var eth0 = input.FirstOrDefault(w => w.NetWorkName == "eth0");
                if (eth0 != null)
                    eth0.DnsBack = line.Replace("nameserver2=\"", "").Replace("\"", "").Trim();
                continue;
            }

            if (line.Contains("nameserver3="))
            {
                // 网口0
                var eth1 = input.FirstOrDefault(w => w.NetWorkName == "eth1");
                if (eth1 != null)
                    eth1.Dns = line.Replace("nameserver3=\"", "").Replace("\"", "").Trim();
                continue;
            }

            if (line.StartsWith("nameserver4="))
            {
                // 网口0
                var eth0 = input.FirstOrDefault(w => w.NetWorkName == "eth1");
                if (eth0 != null)
                    eth0.DnsBack = line.Replace("nameserver4=\"", "").Replace("\"", "").Trim();
            }
        }

        configFile.Dispose();
        return input;
    }

    /// <summary>
    ///     获取wifi账号密码
    /// </summary>
    /// <returns></returns>
    [NonAction]
    private async Task GetWifiConfig(WifiModel wifi)
    {
        var configFile = File.OpenText("/etc/wpa_supplicant.conf");
        var lineNumber = 0;
        var nameList = new List<string> {"ssid", "psk", "key_mgmt"};
        while (!configFile.EndOfStream)
        {
            var line = await configFile.ReadLineAsync();
            lineNumber++;
            if (line != null && (line.IsNullOrEmpty() || !line.Contains("=") || line.Contains("#")))
                continue;
            //split the line
            var parts = line.Split(new[] {'='}, 2);
            if (parts.Length != 2)
                throw new Exception($"Settings must be in the format 'name = value' (line {lineNumber})");

            parts[0] = parts[0].Trim();
            parts[1] = parts[1].Trim();

            if (!nameList.Contains(parts[0]))
                continue;
            switch (parts[0])
            {
                case "ssid":
                    wifi.UserName = parts[1].Replace("\"", "");
                    break;
                case "psk":
                    wifi.Password = parts[1].Replace("\"", "");
                    break;
                case "key_mgmt":
                    wifi.KeyType = parts[1].Replace("\"", "");
                    break;
            }
        }
        configFile.Dispose();
    }

    #region Wifi

    /// <summary>
    ///     修改wifi
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task SaveWifiConfig(WifiModel input)
    {
        var configFile = File.OpenText("/etc/wpa_supplicant.conf");

        var lineNumber = 0;
        var builder = new StringBuilder();
        var nameList = new List<string> {"ssid", "psk", "key_mgmt"};
        while (!configFile.EndOfStream)
        {
            var line = await configFile.ReadLineAsync();
            lineNumber++;
            if (line.IsNullOrEmpty() || !line.Contains("=") || line.Contains("#"))
            {
                builder.AppendLine(line);
                continue;
            }

            //split the line
            var parts = line.Split(new[] {'='}, 2);
            if (parts.Length != 2)
                throw new Exception($"Settings must be in the format 'name = value' (line {lineNumber})");

            parts[0] = parts[0].Trim();
            parts[1] = parts[1].Trim();

            if (!nameList.Contains(parts[0]))
            {
                builder.AppendLine(line);
                continue;
            }

            string strValue;
            switch (parts[0])
            {
                case "ssid":
                    strValue = "\t" + parts[0] + "=" + "\"" + input.UserName + "\"";
                    builder.AppendLine(strValue);
                    break;
                case "psk":
                    strValue = "\t" + parts[0] + "=" + "\"" + input.Password + "\"";
                    builder.AppendLine(strValue);
                    break;
                case "key_mgmt":
                    strValue = "\t" + parts[0] + "=" + input.KeyType;
                    builder.AppendLine(strValue);
                    break;
            }
        }

        configFile.Dispose();
        await File.WriteAllTextAsync("/etc/wpa_supplicant.conf", builder.ToString());
    }

    #endregion Wifi

    #region wifi or mobil status

    /// <summary>
    ///     Wifi Status
    /// </summary>
    /// <param name="type"></param>
    private async Task WriteWifiStatus(int type)
    {
        var jsonfile = Path.Combine("/Edge/"); //文件路径
        var content = "wifi=1";
        if (type == 1)
            await File.WriteAllTextAsync(jsonfile + "wifi.txt", "");
        else
            await File.WriteAllTextAsync(jsonfile + "wifi.txt", content);
    }

    /// <summary>
    ///     读取wifi的状态
    /// </summary>
    /// <returns>JSON文件中的value值</returns>
    private async Task<string> ReadWifiStatus()
    {
        var path = Path.Combine("/Edge/wifi.txt"); //文件路径
        if (!File.Exists(path))
            File.CreateText(path);
        using var file = File.OpenText(path);
        var jsonData = await file.ReadToEndAsync();
        file.Dispose();
        return jsonData;
    }

    /// <summary>
    ///     Read  Mobile 配置
    /// </summary>
    /// <returns>JSON文件中的value值</returns>
    private async Task<string> ReadMobileConfig()
    {
        var path = Path.Combine("/Edge/mobileConfig.txt"); //文件路径
        if (!File.Exists(path))
            File.CreateText(path);
        using var file = File.OpenText(path);
        var jsonData = await file.ReadToEndAsync();
        file.Dispose();
        return jsonData;
    }

    #endregion wifi or mobil status

    #endregion

    #endregion

    #region Shell

    /// <summary>
    ///     IpAddress
    /// </summary>
    /// <returns></returns>
    [HttpGet("/shell/ipAddress")]
    public async Task<List<string>> Ip()
    {
        var networkNames = new List<string> {"eth0", "eth1", "eth2", "wlan0", "lo"};
        var ipList = new List<string>();
        foreach (var networkName in networkNames)
        {
            var config = await GatewayUtil.IfConfig(networkName);
            if (config.IsNullOrEmpty())
                continue;
            var ip = config.GetRegex("addr:", " ");
            var bcast = config.GetRegex("Bcast:", " ");
            var mask = config.GetRegex("Mask:", " ");
            var hWaddr = config.GetRegex("HWaddr:", " ");
            var message = TP.Wrapper(networkName,
                $"##时间## 【{DateTime.NowString()}】",
                $"##IP地址## 【{ip}】",
                $"{(bcast.IsNotNull() ? "##广播地址##" + "【" + bcast + "】" : "")}",
                $"##子网掩码## 【{mask}】",
                $"{(hWaddr.IsNotNull() ? "##Mac地址##" + "【" + hWaddr + "】" : "")}");
            ipList.Add(message);
        }

        return ipList;
    }

    #endregion
}