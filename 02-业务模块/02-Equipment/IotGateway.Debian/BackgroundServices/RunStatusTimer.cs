using Feng.IotGateway.WebSocket;
using Feng.IotGateway.WebSocket.Const;
using Furion.TimeCrontab;
using IotGateway.Equipment.Util;
using Microsoft.Extensions.Hosting;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Equipment.BackgroundServices;

/// <summary>
///     网关运行状态
/// </summary>
public class RunStatusTimer : BackgroundService
{
    private readonly ILogger<RunStatusTimer> _logger;
    private readonly Crontab _crontab;
    private readonly SendMessageService _socket;
    private readonly CancellationTokenSource _cts = new();

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="socket"></param>
    public RunStatusTimer(ILogger<RunStatusTimer> logger, SendMessageService socket)
    {
        _logger = logger;
        _socket = socket;
        _crontab = Crontab.Parse("0/5 * * ? * *", CronStringFormat.WithSeconds);
    }

    /// <summary>
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            // Link the stopping token with our local cancellation token
            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(stoppingToken, _cts.Token);

            while (!linkedCts.Token.IsCancellationRequested)
            {
                try
                {
                    var infoMsg = await GatewayUtil.UseInfo();
                    if (_socket != null)
                    {
                        await _socket.Send(JSON.Serialize(infoMsg), ConstMethod.SysRunStatus);
                    }
                }
                catch (ObjectDisposedException)
                {
                    // Service is being disposed, exit gracefully
                    _logger.LogInformation("Service is shutting down, stopping status updates");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError($"定时推送网关运行状态 Error:【{ex.Message}】");
                }

                try
                {
                    await Task.Delay((int)_crontab.GetSleepMilliseconds(DateTime.Now()), linkedCts.Token);
                }
                catch (OperationCanceledException)
                {
                    // Token was cancelled, exit gracefully
                    break;
                }
            }
        }
        finally
        {
            _cts.Dispose();
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _cts.Cancel();
        await base.StopAsync(cancellationToken);
    }
}