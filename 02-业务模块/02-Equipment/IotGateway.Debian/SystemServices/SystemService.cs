using Common.Extension;
using Feng.IotGateway.Core.Const;
using IotGateway.Equipment.SystemServices.Dto;
using IotGateway.Equipment.Util;
using Microsoft.AspNetCore.Authorization;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Equipment.SystemServices;

/// <summary>
///     网关默认配置
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-12-19
/// </summary>
[ApiDescriptionSettings("网关配置")]
public class SystemService : IDynamicApiController, ITransient
{
    #region 网关授权

    private const string BasePath = "/etc/DeviceConf/";

    /// <summary>
    ///     授权
    /// </summary>
    /// <returns></returns>
    [HttpPost("/authorization/set")]
    public async Task<SetAuthorizeOutput> SetAuthorization(BaseId<string> input)
    {
        var privateKey = await File.ReadAllTextAsync(BasePath + "RSA.Private");
        var decryptPwd = AuthorizationUtil.Decrypt(input.Id, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() != sn)
            throw new Exception("Sn不一致！");
        //校验是否到期
        if (DateTime.Now() >= Convert.ToDateTime(authorize.EndTime))
            throw new Exception("已过期！");

        await File.WriteAllTextAsync(BasePath + "key.txt", input.Id);
        if (!File.Exists(BasePath + "Ident.txt"))
        {
            await File.WriteAllTextAsync(BasePath + "Ident.txt", "fengEdge-200");
            authorize.Ident = "fengEdge-200";
        }
        else
        {
            var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
            authorize.Ident = readIdent ?? "fengEdge-200";
        }

        return authorize;
    }

    /// <summary>
    ///     校验是否授权
    /// </summary>
    /// <returns></returns>
    public async Task<bool> Authorization()
    {
        if (!File.Exists(BasePath + "key.txt"))
            File.CreateText(BasePath + "key.txt");
        var value = await File.ReadAllTextAsync(BasePath + "key.txt");
        if (value == null)
            throw Oops.Oh("未授权！");
        var privateKey = await File.ReadAllTextAsync(BasePath + "RSA.Private");
        var decryptPwd = AuthorizationUtil.Decrypt(value, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() != sn)
            return false;
        return DateTime.Now() < Convert.ToDateTime(authorize.EndTime);
    }

    /// <summary>
    ///     获取授权信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/getAuthorization")]
    [AllowAnonymous]
    public async Task<SetAuthorizeOutput> GetAuthorization()
    {
        var setAuthor = await AuthorizationUtil.GetAuthorization();
        if (setAuthor != null) return setAuthor;
        var sn = await GatewayUtil.GetSn();
        var auth = new SetAuthorizeOutput
        {
            Fingerprint = "",
            Ident = "fengEdge-200",
            Sn = sn,
            Version = MachineUtil.Version,
            DeviceNumber = 0,
            EndTime = null,
            VariableNumber = 0
        };
        if (!File.Exists(BasePath + "Ident.txt")) return auth;
        var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
        auth.Ident = readIdent ?? "fengEdge-200";
        return auth;
    }

    #endregion

    #region 网络配置

    /// <summary>
    ///     保存服务器配置
    /// </summary>
    /// <returns></returns>
    [HttpPost("/network/save")]
    public async Task SetNetWork(NetworkSettingModel input)
    {
        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .Build();
        //读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync("/etc/netplan/50-network-init.yaml");
        //配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        var defNetwork = "";
        foreach (var network in input.Network)
        {
            var networkThereNets = new NetworkThereNets
            {
                //是否是dhcp
                Dhcp = network.NetWorkType == NetWorkTypeEnum.Dhcp ? "yes" : null,
                Gateway = null
            };

            if (network.NetWorkType != NetWorkTypeEnum.Dhcp)
                //设置成静态ip
                // networkThereNets.Gateway = network.Gateway;
                if (network.IPAddress.IsNotNull())
                {
                    networkThereNets.Addresses ??= new List<string>();
                    networkThereNets.Addresses.Add(network.IPAddress + "/24");
                }

            if (network.Dns.IsNotNull() || network.DnsBack.IsNotNull())
            {
                //如果设置了DNS就修改
                networkThereNets.nameservers = new Nameservers {Addresses = new List<string>()};
                if (network.Dns.IsNotNull())
                    networkThereNets.nameservers.Addresses.Add(network.Dns);
                if (network.DnsBack.IsNotNull())
                    networkThereNets.nameservers.Addresses.Add(network.DnsBack);
            }

            networkThereNets.Routes ??= new List<Routes>();
            var routes = new Routes
            {
                To = "0.0.0.0/0",
                Via = network.Gateway,
                Metric = "101"
            };
            if (network.DefRoute)
            {
                defNetwork = network.NetWorkName;
                routes.Metric = "100";
            }

            networkThereNets.Routes.Add(routes);
            networkInit.Network.Ethernets ??= new Dictionary<string, NetworkThereNets>();
            networkInit.Network.Ethernets[network.NetWorkName] = networkThereNets;
        }

        var serializer = new SerializerBuilder()
            .WithNamingConvention(CamelCaseNamingConvention.Instance)
            .IgnoreFields()
            .Build();
        var yaml = serializer.Serialize(networkInit);
        await File.WriteAllTextAsync("/etc/netplan/50-network-init.yaml", yaml);

        //网络生效
        await ShellUtil.Bash("netplan apply");
        //保存默认网卡名称
        await File.WriteAllTextAsync(GatewayFilePath.DefRoutePath, defNetwork);
    }

    /// <summary>
    ///     获取服务器ip配置列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/list")]
    [AllowAnonymous]
    public async Task<NetworkSettingModel> NetWorkList()
    {
        var deserializer = new DeserializerBuilder()
            .WithNamingConvention(UnderscoredNamingConvention.Instance)
            .Build();
        //读取网络配置文件
        var networkYaml = await File.ReadAllTextAsync("/etc/netplan/50-network-init.yaml");
        //配置文件
        var networkInit = deserializer.Deserialize<NetworkInit>(networkYaml);
        var resultData = new NetworkSettingModel
        {
            Network = new List<NetworkInfoModel>(),
            Mobile = new MobileModel
            {
                Apn = "123",
                Password = "123",
                IsApn = true,
                NetworkType = 1,
                OperatorName = "中国电信",
                UserName = "test"
            }
        };
        foreach (var ethernets in networkInit.Network.Ethernets)
        {
            var netWork = new NetworkInfoModel
            {
                NetWorkName = ethernets.Key
            };
            if (ethernets.Value.Dhcp != null)
            {
                netWork.DefRoute = false;
                netWork.NetWorkType = NetWorkTypeEnum.Dhcp;
            }
            else
            {
                netWork.Gateway = ethernets.Value.Gateway ?? ethernets.Value.Routes.FirstOrDefault()?.Via;
                netWork.DefRoute = false;
                netWork.SubnetMark = "*************";
                netWork.IPAddress = ethernets.Value.Addresses.FirstOrDefault()?.Replace("/24", "");
                netWork.NetWorkType = NetWorkTypeEnum.Static;
            }

            if (ethernets.Value.nameservers != null && ethernets.Value.nameservers.Addresses != null)
            {
                if (ethernets.Value.nameservers.Addresses.Count == 2)
                {
                    netWork.Dns = ethernets.Value.nameservers.Addresses[0];
                    netWork.DnsBack = ethernets.Value.nameservers.Addresses[1];
                }

                if (ethernets.Value.nameservers.Addresses.Count == 1)
                    netWork.Dns = ethernets.Value.nameservers.Addresses[0];
            }

            if (File.Exists(GatewayFilePath.DefRoutePath))
                netWork.DefRoute = await File.ReadAllTextAsync(GatewayFilePath.DefRoutePath) == netWork.NetWorkName;
            resultData.Network.Add(netWork);
        }

        // 读取Mobile配置
        var mobileConfig = await ReadMobileConfig();
        if (mobileConfig.IsNotNullOrWhiteSpace())
        {
            resultData.Mobile = JSON.Deserialize<MobileModel>(mobileConfig);
        }

        return resultData;
    }

    #endregion

    /// <summary>
    ///     Read  Mobile 配置
    /// </summary>
    /// <returns>JSON文件中的value值</returns>
    private async Task<string> ReadMobileConfig()
    {
        var path = Path.Combine("/Edge/mobileConfig.txt"); //文件路径
        if (!File.Exists(path))
            File.CreateText(path);
        using var file = File.OpenText(path);
        var jsonData = await file.ReadToEndAsync();
        file.Dispose();
        return jsonData;
    }

    #region Shell

    /// <summary>
    ///     IpAddress
    /// </summary>
    /// <returns></returns>
    [HttpGet("/shell/ipAddress")]
    public async Task<List<string>> Ip()
    {
        var networkNames = new List<string> {"eth0", "eth1", "eth2", "lo"};
        var ipList = new List<string>();
        foreach (var network in networkNames)
        {
            var ip = await GatewayUtil.IfConfig(network);
            ipList.Add(ip);
            switch (network)
            {
                case "eth0":
                    MachineUtil.Clay.Eth0 = ip;
                    break;
                case "eth1":
                    MachineUtil.Clay.Eth1 = ip;
                    break;
                case "eth2":
                    MachineUtil.Clay.Eth2 = ip;
                    break;
            }
        }

        return networkNames;
    }

    #endregion
}