using Timer = System.Threading.Timer;

namespace IotGateway.Equipment;

/// <summary>
///     物实例初始化
/// </summary>
public class HostedService : IHostedService, IDisposable
{
    /// <summary>
    ///     定时任务
    /// </summary>
    private Timer _timer;

    public void Dispose()
    {
        _timer.Dispose();
    }

    /// <summary>
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        // 启动获取4G信号功能
        _ = Task.Run(() =>
        {
            GatewayUtil.GetNetworkSignal();
            return Task.CompletedTask;
        }, cancellationToken);

        // 创建基本目录
        await ShellUtil.Bash("mkdir -p /Edge/Data  /Edge/picture /Edge/Redis /etc/DeviceConf /Edge/DncFile ");
        // 授权目录
        await ShellUtil.Bash("chmod -R 777 /usr/local/src /Edge /etc/DeviceConf");
        // 开启IP转发功能
        await ShellUtil.Bash("sysctl -w net.ipv4.ip_forward=1");
        // 删除历史更新包
        await ShellUtil.Bash("rm -rf /usr/local/src/*-encrypt.rar");
        // 写入dns
        await ShellUtil.Bash(AppDomain.CurrentDomain.BaseDirectory + "Shell/dns_config.sh");
        _timer = new Timer(DoWork, null, TimeSpan.Zero, TimeSpan.FromMinutes(15)); // 每15分钟执行一次
    }

    /// <summary>
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        _timer.Dispose();
        return Task.CompletedTask;
    }

    /// <summary>
    /// </summary>
    /// <param name="state"></param>
    private void DoWork(object? state)
    {
        // 写入dns
        _ = ShellUtil.Bash(AppDomain.CurrentDomain.BaseDirectory + "Shell/dns_config.sh");
    }
}