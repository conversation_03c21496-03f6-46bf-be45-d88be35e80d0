using System.Runtime.InteropServices;
using Common.Extension;
using Furion.Logging;
using Furion.Templates;
using Microsoft.AspNetCore.Authorization;
using Console = Feng.Common.Extension.Console;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Equipment.SystemServices;

/// <summary>
///     网关默认配置
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-12-19
/// </summary>
[ApiDescriptionSettings("网关配置")]
public class SystemService : IDynamicApiController, ITransient
{
    private readonly ILogger<SystemService> _logger;
    private readonly string _baseDirectory = AppDomain.CurrentDomain.BaseDirectory;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    public SystemService(ILogger<SystemService> logger)
    {
        _logger = logger;
    }
    
    /// <summary>
    /// 支持串口集合
    /// </summary>
    [HttpGet("/shell/serialPorts")]
    public Task<dynamic> GetSerialPorts()
    {
        return Task.FromResult<dynamic>(new Dictionary<string, string>
        {
            {"/dev/ttyS0", "ttyS0：A4/B4(485)"},
            {"/dev/ttyS1", "ttyS1：TX1/RX1(232) A1/B1(485)"},
            {"/dev/ttyS3", "ttyS3：TX2/RX2(232) A2/B2(485)"},
            {"/dev/ttyS5", "ttyS5：A3/B3(485)"}
        });
    }
    
    /// <summary>
    /// 网卡名称集合
    /// </summary>
    [HttpGet("/shell/networks")]
    public async Task<dynamic> GetNetworks()
    {
        var result = await ShellUtil.Bash("ip addr show | grep inet | awk '$NF ~ /^[a-zA-Z0-9]+$/ && $NF !~ /^(docker0|host|link|lo|tap0)$/ {print $2,$NF}'");
        var lines = result.Split('\n');
        var ipToInterface = new Dictionary<string, string> {{"不指定", ""}};
        foreach (var line in lines)
        {
            var parts = line.Split(new[] {' '}, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length != 2) continue;
            var ipAddress = parts[0];
            var interfaceName = parts[1];
            ipToInterface[interfaceName] = ipAddress;
        }
        return ipToInterface;
    }

    #region 网关授权

    private const string BasePath = "/etc/DeviceConf/";

    /// <summary>
    ///     授权
    /// </summary>
    /// <returns></returns>
    [HttpPost("/authorization/set")]
    public async Task<SetAuthorizeOutput> SetAuthorization(BaseId<string> input)
    {
        var privateKey = await File.ReadAllTextAsync(BasePath + "RSA.Private");
        var decryptPwd = AuthorizationUtil.Decrypt(input.Id, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() != sn)
            throw new Exception("Sn不一致！");
        //校验是否到期
        if (DateTime.Now() >= Convert.ToDateTime(authorize.EndTime))
            throw new Exception("已过期！");
        await File.WriteAllTextAsync(BasePath + "key.txt", input.Id);
        if (!File.Exists(BasePath + "Ident.txt"))
        {
            await File.WriteAllTextAsync(BasePath + "Ident.txt", "fengEdge-200");
            authorize.Ident = "fengEdge-200";
        }
        else
        {
            var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
            authorize.Ident = readIdent ?? "fengEdge-200";
        }

        return authorize;
    }

    /// <summary>
    ///     校验是否授权
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/isAuthorization")]
    public async Task<bool> Authorization()
    {
        var value = await File.ReadAllTextAsync(BasePath + "key.txt");
        if (value == null)
            throw Oops.Oh("未授权！");
        var privateKey = await File.ReadAllTextAsync(BasePath + "RSA.Private");
        var decryptPwd = AuthorizationUtil.Decrypt(value, privateKey);
        var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
        // 校验sn是否一致
        var sn = await GatewayUtil.GetSn();
        if (authorize.Sn.Trim() == sn.Trim()) return DateTime.Now() < Convert.ToDateTime(authorize.EndTime);
        Log.Information($"sn:【{authorize.Sn}】,getSn:【{sn}】");
        return false;
    }

    /// <summary>
    ///     获取授权信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorization/getAuthorization")]
    [AllowAnonymous]
    public async Task<SetAuthorizeOutput> GetAuthorization()
    {
        var setAuthor = await AuthorizationUtil.GetAuthorization();
        if (setAuthor != null) return setAuthor;
        //如果无法获取到授权信息，默认自行组装
        var sn = await GatewayUtil.GetSn();
        var auth = new SetAuthorizeOutput
        {
            Fingerprint = "",
            Ident = "fengEdge-200",
            Sn = sn,
            Version = "V" + MachineUtil.Version,
            DeviceNumber = 0,
            EndTime = null,
            VariableNumber = 0
        };
        if (!File.Exists(BasePath + "Ident.txt")) return auth;
        var readIdent = await File.ReadAllTextAsync(BasePath + "Ident.txt");
        auth.Ident = readIdent ?? "fengEdge-200";
        return auth;
    }

    #endregion

    #region 网络配置

    /// <summary>
    ///     获取网络配置文件内容
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/getNetWork")]
    [AllowAnonymous]
    public async Task<byte[]> GetNetWork()
    {
        return await File.ReadAllBytesAsync("/etc/network/interfaces");
    }

    /// <summary>
    ///     写入网络配置文件内容
    /// </summary>
    /// <returns></returns>
    [HttpPost("/network/setNetWork")]
    [AllowAnonymous]
    public async Task SetNetWork(BaseId<byte[]> input)
    {
        try
        {
            if (input.Id is {Length: > 0})
            {
                await ShellUtil.Bash("cp /etc/network/interfaces /etc/network/interfaces-back");
                await File.WriteAllBytesAsync("/etc/network/interfaces", input.Id);
            }
        }
        catch (Exception e)
        {
            _logger.LogError($"【写入网络配置文件】 Error:【{e.Message}】");
        }
    }

    /// <summary>
    ///     保存服务器配置
    /// </summary>
    /// <returns></returns>
    [HttpPost("/network/save")]
    [AllowAnonymous]
    public async Task SetNetWorkConfig(NetworkSettingModel input)
    {
        await TaskQueued.EnqueueAsync(async (_, cancellationToken) =>
        {
            await Dhcp(input.Network);
            _logger.LogWarning("【网络配置】-----【第一步】dhcpcd,修改完成！");
            // 修改wifi
            await SaveWifiConfig(input.Wifi);
            _logger.LogWarning("【网络配置】-----【第二步】wifi,修改完成！");
            await WriteWifiStatus(input.Wifi is {Enable: true} ? 0 : 1);
            _logger.LogWarning("【网络配置】-----【第三步】wifi状态,写入完成！");
            var thread = new Thread(RestartWifi)
            {
                IsBackground = true
            };
            thread.Start();
            _logger.LogWarning("【网络配置】-----【第四步】wifi重启完成！");
            try
            {
                if (input.Wifi != null)
                    input.Network.Add(input.Wifi.Adapt<NetworkInfoModel>());
            }
            catch (Exception e)
            {
                _logger.LogError("wifi映射出错了," + e.Message);
            }

            // 修改ip
            await SaveNetWorkConfig(input.Network, input.Wifi is {Enable: true});

            // 修改Dns
            await SaveDnsConfig(input.Network);
            _logger.LogWarning("【网络配置】-----【第五步】网络配置修改完成！");
            // 将4G,5G配置写入到文件中
            var jsonfile = Path.Combine("/Edge/"); //文件路径
            await File.WriteAllTextAsync(jsonfile + "mobileConfig.txt", JSON.Serialize(input.Mobile), cancellationToken);
            _logger.LogWarning("【网络配置】-----【第六步】4G,5G配置完成！");
            await WriteMobileStatus(input.Mobile);
            _logger.LogWarning("【网络配置】-----【第七步】4G,5G网络配置重置完成！");

            foreach (var network in input.Network.Where(network => network.DefRoute))
                switch (network.NetWorkName)
                {
                    case "eth0":
                        await Eth0Shell();
                        _logger.LogWarning("【网络配置】-----【Eth0】重置完成！");
                        break;
                    case "eth1":
                        await Eth1Shell();
                        _logger.LogWarning("【网络配置】-----【Eth1】重置完成！");
                        break;
                }

            if (input.Mobile is {Enable: true, DefRoute: true})
            {
                await MobileShell();
                _logger.LogWarning("【网络配置】-----【4G,5G】重置完成！");
            }

            if (input.Wifi is {Enable: true, DefRoute: true})
            {
                await WifiShell();
                _logger.LogWarning("【网络配置】-----【wifi】重置完成！");
            }
        }, 3000);
    }

    /// <summary>
    ///     获取服务器ip配置列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/network/list")]
    [AllowAnonymous]
    public async Task<NetworkSettingModel> GetNetWorkList()
    {
        var resultData = new NetworkSettingModel();
        resultData.Network = await GetNetworkConfig();
        resultData.Network = await GetDnsConfig(resultData.Network);
        resultData.Mobile = new MobileModel {OperatorName = ""};
        if (resultData.Network.Any(a => a.NetWorkName == "wlan0"))
        {
            var networkMod = resultData.Network.FirstOrDefault(f => f.NetWorkName == "wlan0");
            resultData.Wifi = networkMod?.Adapt<WifiModel>();
            resultData.Network.Remove(networkMod);
        }

        resultData.Wifi = await GetWifiConfig(resultData.Wifi);
        // 读取wifi状态
        var wifiStatus = await ReadWifiStatus();
        resultData.Wifi.Enable = false;
        if (wifiStatus.IsNotNullOrWhiteSpace())
            if (wifiStatus.Contains("wifi=1"))
                resultData.Wifi.Enable = true;

        var mobileConfig = await ReadMobileConfig();
        if (!mobileConfig.IsNotNullOrWhiteSpace()) return resultData;

        resultData.Mobile = JSON.Deserialize<MobileModel>(mobileConfig);
        var defRoute = await Route() == "eth2";
        resultData.Mobile.DefRoute = defRoute;
        return resultData;
    }

    #region 私有方法

    /// <summary>
    ///     修改ip
    /// </summary>
    /// <param name="input"></param>
    /// <param name="openWifi">是否开启wifi</param>
    /// <returns></returns>
    private async Task SaveNetWorkConfig(List<NetworkInfoModel> input, bool openWifi)
    {
        var configFile = File.OpenText("/etc/network/interfaces");
        var builder = new StringBuilder();
        var netWork = new NetworkInfoModel();
        var dhcp = false;
        var writeGateway = false;
        while (!configFile.EndOfStream)
        {
            var line = await configFile.ReadLineAsync();

            if (line == null) continue;
            if (netWork == null)
            {
                builder.AppendLine(line);
                continue;
            }
            if (line.StartsWith("#iface wlan0 inet static"))
            {
                writeGateway = true;
                continue;
            }

            if (line.Contains("address"))
            {
                if (dhcp && netWork.NetWorkName == "wlan0")
                    builder.AppendLine($"#address {netWork.IPAddress}");
                else
                    builder.AppendLine($"address {netWork.IPAddress}");
                continue;
            }

            if (line.Contains("netmask"))
            {
                if (dhcp && netWork.NetWorkName == "wlan0")
                    builder.AppendLine($"#netmask {netWork.SubnetMark}");
                else
                    builder.AppendLine($"netmask {netWork.SubnetMark}");
                continue;
            }

            if (line.Contains("gateway"))
            {
                //如果是动态网络 or 开启wifi
                if (dhcp || openWifi)
                {
                    //当前是wifi网卡
                    if (netWork.NetWorkName == "wlan0")
                    {
                        //如果是动态网络 or 默认网关地址未配置 就注释掉
                        if (netWork.Gateway.IsNullOrEmpty() || dhcp)
                        {
                            builder.AppendLine("#gateway ");
                        }
                        else // 否则就添加网关地址
                        {
                            if (netWork.DefRoute)
                                builder.AppendLine($"gateway {netWork.Gateway}");
                            else
                                builder.AppendLine($"#gateway {netWork.Gateway}");
                        }
                    }
                    else
                    {
                        if (netWork.DefRoute)
                            builder.AppendLine($"gateway {netWork.Gateway}");
                        else
                            builder.AppendLine($"#gateway {netWork.Gateway}");
                    }
                }
                else
                {
                    if (netWork.DefRoute)
                        builder.AppendLine($"gateway {netWork.Gateway}");
                    else
                        builder.AppendLine($"#gateway {netWork.Gateway}");
                }

                continue;
            }

            if (line.StartsWith("auto"))
            {
                builder.AppendLine(line);
                continue;
            }

            if (line.StartsWith("iface"))
            {
                if (line.Contains("eth0"))
                    netWork = input.FirstOrDefault(f => f.NetWorkName == "eth0");
                if (line.Contains("eth1"))
                    netWork = input.FirstOrDefault(f => f.NetWorkName == "eth1");
                if (line.Contains("wlan0"))
                    netWork = input.FirstOrDefault(f => f.NetWorkName == "wlan0");

                if (netWork == null)
                    continue;
                if (line.EndsWith("static"))
                    line = line.Replace("static", EnumUtil.GetEnumDesc(netWork.NetWorkType).ToLower());
                else if (line.EndsWith("dhcp"))
                    line = line.Replace("dhcp", EnumUtil.GetEnumDesc(netWork.NetWorkType).ToLower());
                dhcp = netWork.NetWorkType == NetWorkTypeEnum.Dhcp;
                builder.AppendLine(line);
                continue;
            }

            if (line.IsNullOrEmpty() || line.StartsWith("#")) builder.AppendLine(line);
        }

        if (writeGateway)
        {
            netWork = input.FirstOrDefault(f => f.NetWorkName == "wlan0");
            if (netWork != null)
            {
                if (netWork.NetWorkType == NetWorkTypeEnum.Static)
                {
                    if (netWork.Gateway.IsNullOrEmpty())
                        builder.AppendLine("#gateway ");
                    else
                        builder.AppendLine($"gateway {netWork.Gateway}");
                }
                else
                {
                    builder.AppendLine("#gateway ");
                }
            }
        }

        configFile.Dispose();
        await File.WriteAllTextAsync("/etc/network/interfaces", builder.ToString());
    }

    /// <summary>
    ///     设备Dns
    /// </summary>
    /// <param name="input"></param>
    private async Task SaveDnsConfig(List<NetworkInfoModel> input)
    {
        var dnsConfig = File.OpenText(_baseDirectory + "Shell/dns_config.sh");
        var builder = new StringBuilder();
        while (!dnsConfig.EndOfStream)
        {
            var line = await dnsConfig.ReadLineAsync();

            if (line == null) continue;
            if (!line.StartsWith("nameserver1=") && !line.StartsWith("nameserver2=") && !line.StartsWith("nameserver3=") && !line.StartsWith("nameserver4="))
            {
                builder.AppendLine(line);
                continue;
            }

            if (line.StartsWith("nameserver1="))
            {
                // 网口0
                var eth0 = input.FirstOrDefault(w => w.NetWorkName == "eth0");
                if (eth0 != null && !string.IsNullOrEmpty(eth0.Dns))
                    builder.AppendLine($"nameserver1=\"{eth0.Dns}\"");
                else
                    builder.AppendLine(line);
                continue;
            }

            if (line.StartsWith("nameserver2="))
            {
                // 网口0
                var eth0 = input.FirstOrDefault(w => w.NetWorkName == "eth0");
                if (eth0 != null)
                    builder.AppendLine($"nameserver2=\"{eth0.DnsBack}\"");
                else
                    builder.AppendLine(line);
                continue;
            }

            if (line.StartsWith("nameserver3="))
            {
                // 网口0
                var eth1 = input.FirstOrDefault(w => w.NetWorkName == "eth1");
                if (eth1 != null && !string.IsNullOrEmpty(eth1.Dns))
                    builder.AppendLine($"nameserver3=\"{eth1.Dns}\"");
                else
                    builder.AppendLine(line);
                continue;
            }

            if (line.Contains("nameserver4="))
            {
                // 网口1
                var eth1 = input.FirstOrDefault(w => w.NetWorkName == "eth1");
                if (eth1 != null && !string.IsNullOrEmpty(eth1.DnsBack))
                    builder.AppendLine($"nameserver4=\"{eth1.DnsBack}\"");
                else
                    builder.AppendLine(line);
            }
        }

        dnsConfig.Dispose();
        await File.WriteAllTextAsync(_baseDirectory + "Shell/dns_config.sh", builder.ToString());
    }

    /// <summary>
    ///     获取当前默认路由
    /// </summary>
    /// <returns></returns>
    private async Task<string> Route()
    {
        var configFile = File.OpenText("/hogo/config/net_select");
        while (!configFile.EndOfStream)
        {
            var line = await configFile.ReadLineAsync();
            if (line.IsNullOrEmpty())
                continue;
            if (line != null) return line.Trim();
        }

        configFile.Dispose();
        return "";
    }

    /// <summary>
    ///     获取ip
    /// </summary>
    /// <returns></returns>
    [NonAction]
    private async Task<List<NetworkInfoModel>> GetNetworkConfig()
    {
        var configFile = File.OpenText("/etc/network/interfaces");
        //var configFile = Files.OpenText("D:\\Data\\Edge\\interface");
        var resultData = new List<NetworkInfoModel>();
        var name = "";
        var type = "";
        var addr = "";
        var mark = "";
        while (!configFile.EndOfStream)
        {
            var line = await configFile.ReadLineAsync();
            if (line == null)
                continue;

            if (line.StartsWith("iface"))
            {
                if (line.Contains("eth0"))
                    name = "eth0";
                if (line.Contains("eth1"))
                    name = "eth1";
                if (line.Contains("wlan0"))
                    name = "wlan0";
                type = line.Contains("dhcp") ? "dhcp" : "static";
                continue;
            }

            if (type == "dhcp")
            {
                if (resultData.All(a => a.NetWorkName != name))
                {
                    resultData.Add(new NetworkInfoModel {NetWorkName = name, NetWorkType = NetWorkTypeEnum.Dhcp, SubnetMark = "", IPAddress = "", Gateway = ""});
                    type = "";
                    name = "";
                }

                continue;
            }

            if (line.StartsWith("address"))
            {
                addr = line.Replace("address", "").Trim();
                continue;
            }

            if (line.StartsWith("netmask"))
            {
                mark = line.Replace("netmask", "").Trim();
                continue;
            }

            if (!line.Contains("gateway")) continue;

            {
                var gateway = line.Replace("gateway", "").Trim();
                if (gateway.Contains("#"))
                    gateway = gateway.Replace("#", "").Trim();
                if (resultData.All(a => a.NetWorkName != name) && name.IsNotNull())
                {
                    var defRoute = await Route() == name;
                    resultData.Add(new NetworkInfoModel
                    {
                        NetWorkName = name, NetWorkType = type == "dhcp" ? NetWorkTypeEnum.Dhcp : NetWorkTypeEnum.Static, Gateway = gateway, IPAddress = addr, SubnetMark = mark, DefRoute = defRoute
                    });
                    name = "";
                    type = "";
                    addr = "";
                    mark = "";
                }
            }
        }

        configFile.Dispose();

        return resultData;
    }

    /// <summary>
    ///     获取Dns
    /// </summary>
    /// <returns></returns>
    [NonAction]
    private async Task<List<NetworkInfoModel>> GetDnsConfig(List<NetworkInfoModel> input)
    {
        var configFile = File.OpenText(_baseDirectory + "Shell/dns_config.sh");
        while (!configFile.EndOfStream)
        {
            var line = await configFile.ReadLineAsync();
            if (line == null)
                continue;

            if (!line.StartsWith("nameserver1=") && !line.StartsWith("nameserver2=") && !line.StartsWith("nameserver3=") && !line.StartsWith("nameserver4="))
                continue;
            if (line.StartsWith("nameserver1="))
            {
                // 网口0
                var eth0 = input.FirstOrDefault(w => w.NetWorkName == "eth0");
                if (eth0 != null)
                    eth0.Dns = line.Replace("nameserver1=\"", "").Replace("\"", "").Trim();
                continue;
            }

            if (line.StartsWith("nameserver2="))
            {
                // 网口0
                var eth0 = input.FirstOrDefault(w => w.NetWorkName == "eth0");
                if (eth0 != null)
                    eth0.DnsBack = line.Replace("nameserver2=\"", "").Replace("\"", "").Trim();
                continue;
            }

            if (line.Contains("nameserver3="))
            {
                // 网口0
                var eth1 = input.FirstOrDefault(w => w.NetWorkName == "eth1");
                if (eth1 != null)
                    eth1.Dns = line.Replace("nameserver3=\"", "").Replace("\"", "").Trim();
                continue;
            }

            if (line.StartsWith("nameserver4="))
            {
                // 网口0
                var eth0 = input.FirstOrDefault(w => w.NetWorkName == "eth1");
                if (eth0 != null)
                    eth0.DnsBack = line.Replace("nameserver4=\"", "").Replace("\"", "").Trim();
            }
        }

        configFile.Dispose();
        return input;
    }

    /// <summary>
    ///     获取wifi账号密码
    /// </summary>
    /// <returns></returns>
    [NonAction]
    private async Task<WifiModel> GetWifiConfig(WifiModel resultData)
    {
        var configFile = File.OpenText("/etc/wpa_supplicant.conf");
        var lineNumber = 0;
        var defRoute = await Route() == "wlan0";
        resultData.DefRoute = defRoute;
        var nameList = new List<string> {"ssid", "psk", "key_mgmt"};
        while (!configFile.EndOfStream)
        {
            var line = await configFile.ReadLineAsync();
            lineNumber++;
            if (line != null && (line.IsNullOrEmpty() || !line.Contains("=") || line.Contains("#")))
                continue;
            //split the line
            var parts = line.Split(new[] {'='}, 2);
            if (parts.Length != 2)
                throw new Exception($"Settings must be in the format 'name = value' (line {lineNumber})");

            parts[0] = parts[0].Trim();
            parts[1] = parts[1].Trim();

            if (!nameList.Contains(parts[0]))
                continue;
            switch (parts[0])
            {
                case "ssid":
                    resultData.UserName = parts[1].Replace("\"", "");
                    break;
                case "psk":
                    resultData.Password = parts[1].Replace("\"", "");
                    break;
                case "key_mgmt":
                    resultData.KeyType = parts[1].Replace("\"", "");
                    break;
            }
        }

        configFile.Dispose();
        return resultData;
    }

    #region 默认路由切换

    /// <summary>
    ///     切换成Eth0
    /// </summary>
    /// <returns></returns>
    private async Task Eth0Shell()
    {
        await Task.Factory.StartNew(async () => { await ShellUtil.Bash("/hogo/sh/use_eth0.sh"); });
    }

    /// <summary>
    ///     切换成Eth1
    /// </summary>
    /// <returns></returns>
    private async Task Eth1Shell()
    {
        await Task.Factory.StartNew(async () => { await ShellUtil.Bash("/hogo/sh/use_eth1.sh"); });
    }

    /// <summary>
    ///     切换成4G
    /// </summary>
    /// <returns></returns>
    private async Task MobileShell()
    {
        await Task.Factory.StartNew(async () => { await ShellUtil.Bash("/hogo/sh/use_4g.sh"); });
    }

    /// <summary>
    ///     切换成wifi
    /// </summary>
    /// <returns></returns>
    private async Task WifiShell()
    {
        await Task.Factory.StartNew(async () => { await ShellUtil.Bash("/hogo/sh/use_wifi.sh"); });
    }

    #endregion 默认路由切换

    /// <summary>
    ///     Dhcp配置文件修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private static async Task Dhcp(List<NetworkInfoModel> input)
    {
        if (!File.Exists("/etc/dhcpcd.conf"))
        {
            Console.WriteLine("未找到文件");
            return;
        }

        var configFile = File.OpenText("/etc/dhcpcd.conf");
        var builder = new StringBuilder();
        var isAdd = false;
        while (!configFile.EndOfStream)
        {
            var line = await configFile.ReadLineAsync();
            if (line.Contains("slaac private"))
            {
                isAdd = true;
                builder.AppendLine(line);
                continue;
            }

            if (!isAdd)
            {
                builder.AppendLine(line);
                continue;
            }

            break;
        }

        if (isAdd)
        {
            builder.AppendLine("");
            foreach (var network in input)
            {
                var metric = network.NetWorkName == "eth0" ? "200" : network.NetWorkName == "eth1" ? "204" : "201";
                if (network.NetWorkType == NetWorkTypeEnum.Dhcp)
                {
                    builder.AppendLine($"interface {network.NetWorkName}");
                    builder.AppendLine($"metric={metric}");
                }
                else
                {
                    builder.AppendLine($"interface {network.NetWorkName}");
                    builder.AppendLine("static ip_address=");
                    builder.AppendLine($"metric={metric}");
                }
            }
        }

        configFile.Dispose();
        await File.WriteAllTextAsync("/etc/dhcpcd.conf", builder.ToString());
    }

    #region Wifi

    /// <summary>
    ///     修改wifi
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task SaveWifiConfig(WifiModel input)
    {
        var configFile = File.OpenText("/etc/wpa_supplicant.conf");

        var lineNumber = 0;
        var builder = new StringBuilder();
        var nameList = new List<string> {"ssid", "psk", "key_mgmt"};
        while (!configFile.EndOfStream)
        {
            var line = await configFile.ReadLineAsync();
            lineNumber++;
            if (line.IsNullOrEmpty() || !line.Contains("=") || line.Contains("#"))
            {
                builder.AppendLine(line);
                continue;
            }

            //split the line
            var parts = line.Split(new[] {'='}, 2);
            if (parts.Length != 2)
                throw new Exception($"Settings must be in the format 'name = value' (line {lineNumber})");

            parts[0] = parts[0].Trim();
            parts[1] = parts[1].Trim();

            if (!nameList.Contains(parts[0]))
            {
                builder.AppendLine(line);
                continue;
            }

            string strValue;
            switch (parts[0])
            {
                case "ssid":
                    strValue = "\t" + parts[0] + "=" + "\"" + input.UserName + "\"";
                    builder.AppendLine(strValue);
                    break;
                case "psk":
                    strValue = "\t" + parts[0] + "=" + "\"" + input.Password + "\"";
                    builder.AppendLine(strValue);
                    break;
                case "key_mgmt":
                    strValue = "\t" + parts[0] + "=" + input.KeyType;
                    builder.AppendLine(strValue);
                    break;
            }
        }

        configFile.Dispose();
        await File.WriteAllTextAsync("/etc/wpa_supplicant.conf", builder.ToString());
    }

    /// <summary>
    ///     重启wifi
    /// </summary>
    /// <returns></returns>
    private async void RestartWifi()
    {
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            await ShellUtil.Bash("wpa_cli disconnect && sleep 1 && wpa_cli reconnect");    
    }

    #endregion Wifi

    #region wifi or mobil status

    /// <summary>
    ///     Wifi Status
    /// </summary>
    /// <param name="type"></param>
    private async Task WriteWifiStatus(int type)
    {
        var jsonfile = Path.Combine("/Edge/"); //文件路径
        var content = "wifi=1";
        if (type == 1)
            await File.WriteAllTextAsync(jsonfile + "wifi.txt", "");
        else
            await File.WriteAllTextAsync(jsonfile + "wifi.txt", content);
    }

    /// <summary>
    ///     Mobile Status
    /// </summary>
    /// <param name="input"></param>
    private async Task WriteMobileStatus(MobileModel input)
    {
        if (input.Enable)
        {
            //如果model 是开启状态
            var cmd = " " + input.Apn + " ";
            if (input.UserName.IsNotNull() && input.Password.IsNotNull())
                cmd += input.UserName + " " + input.Password + " ";

            // 4g/5g
            if (input.NetworkType == 1)
            {
                //取消5g
                await ShellUtil.Bash(_baseDirectory + "Shell/autoapn.sh /dev/ttyUSB2");
                cmd = "/dev/ttyUSB0" + cmd;
                await ShellUtil.Bash(_baseDirectory + $"Shell/apn_m5700.sh {cmd}");
            }
            else
            {
                // 取消4g
                await ShellUtil.Bash(_baseDirectory + "Shell/apn_m5700.sh /dev/ttyUSB0 CMNET");
                cmd = "/dev/ttyUSB2" + cmd;
                await ShellUtil.Bash(_baseDirectory + $"Shell/apn_rg200u.sh {cmd}");
            }
        }
        else
        {
            // 取消5g
            await ShellUtil.Bash(_baseDirectory + "Shell/autoapn.sh /dev/ttyUSB2");
            // 取消4g
            await ShellUtil.Bash(_baseDirectory + "Shell/apn_m5700.sh /dev/ttyUSB0 CMNET");
        }
    }

    /// <summary>
    ///     读取wifi的状态
    /// </summary>
    /// <returns>JSON文件中的value值</returns>
    private async Task<string> ReadWifiStatus()
    {
        var path = Path.Combine("/Edge/wifi.txt"); //文件路径
        if (!File.Exists(path))
            File.CreateText(path);
        using var file = File.OpenText(path);
        var jsonData = await file.ReadToEndAsync();
        file.Dispose();
        return jsonData;
    }

    /// <summary>
    ///     Read  Mobile 配置
    /// </summary>
    /// <returns>JSON文件中的value值</returns>
    private async Task<string> ReadMobileConfig()
    {
        var path = Path.Combine("/Edge/mobileConfig.txt"); //文件路径
        if (!File.Exists(path))
            File.CreateText(path);
        using var file = File.OpenText(path);
        var jsonData = await file.ReadToEndAsync();
        file.Dispose();
        return jsonData;
    }

    #endregion wifi or mobil status

    #endregion

    #endregion

    #region Shell

    /// <summary>
    ///     IpAddress
    /// </summary>
    /// <returns></returns>
    [HttpGet("/shell/ipAddress")]
    public async Task<List<string>> Ip()
    {
        var networkNames = new List<string> {"eth0", "eth1", "eth2", "wlan0", "lo"};
        var ipList = new List<string>();
        foreach (var networkName in networkNames)
        {
            var config = await GatewayUtil.IfConfig(networkName);
            if (config.IsNullOrEmpty())
                continue;
            var ip = config.GetRegex("addr:", " ");
            var bcast = config.GetRegex("Bcast:", " ");
            var mask = config.GetRegex("Mask:", " ");
            var hWaddr = config.GetRegex("HWaddr:", " ");
            var message = TP.Wrapper(networkName,
                $"##时间## 【{DateTime.NowString()}】",
                $"##IP地址## 【{ip}】",
                $"{(bcast.IsNotNull() ? "##广播地址##" + "【" + bcast + "】" : "")}",
                $"##子网掩码## 【{mask}】",
                $"{(hWaddr.IsNotNull() ? "##Mac地址##" + "【" + hWaddr + "】" : "")}");
            ipList.Add(message);
        }

        return ipList;
    }

    #endregion
}