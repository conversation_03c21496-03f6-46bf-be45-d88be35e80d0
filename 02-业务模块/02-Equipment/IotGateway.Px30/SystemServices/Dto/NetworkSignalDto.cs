using System.Text.Json.Serialization;

namespace IotGateway.Equipment.SystemServices.Dto;

/// <summary>
/// 网关4g信号
/// </summary>
public class NetworkSignalDto
{
    /// <summary>
    ///     功能码
    /// </summary>
    [JsonPropertyName("type")]
    public string Type { get; set; }

    /// <summary>
    ///     4G 类型
    /// </summary>
    [JsonPropertyName("name")]
    public string Name { get; set; }

    /// <summary>
    ///     true:模块 usb 连接正常
    ///     false:模块 usb 连接异常
    /// </summary>
    [JsonPropertyName("usblink")]
    public bool Usblink { get; set; }

    /// <summary>
    ///     0:at 指令无响应
    ///     1:at 指令有响应
    /// </summary>
    [JsonPropertyName("atack")]
    public int Atack { get; set; }

    /// <summary>
    ///     模块具体类型
    /// </summary>
    [JsonPropertyName("modemtype")]
    public string Modemtype { get; set; }

    /// <summary>
    ///     信号值
    /// </summary>
    [JsonPropertyName("signal")]
    public int Signal { get; set; }

    /// <summary>
    ///     运营商类型
    /// </summary>
    [JsonPropertyName("oper")]
    public string Oper { get; set; }

    /// <summary>
    ///     SIM 卡状态
    ///     0：未知
    ///     1：未插入
    ///     2：锁住
    ///     3：READY
    /// </summary>
    [JsonPropertyName("simstatus")]
    public int Simstatus { get; set; }
}