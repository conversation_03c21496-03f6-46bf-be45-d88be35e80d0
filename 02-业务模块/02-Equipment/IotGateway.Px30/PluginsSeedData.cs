// using Feng.IotGateway.Core.Entity;
// using Feng.IotGateway.Core.SqlSugar;
//
// namespace IotGateway.Equipment;
//
// /// <summary>
// ///     插件种子数据
// /// </summary>
// public class PluginsSeedData : ISqlSugarEntitySeedData<Plugins>
// {
//     public IEnumerable<Plugins> HasData()
//     {
//         return new[]
//         {
//             new Plugins
//             {
//                 Id = 353207444390092,
//                 PluginPurpose = PluginPurposeEnum.HostService,
//                 Config = "{\"Ssid\":" + "\"" + GetSn() + "\"" + ",\"WpaPassphrase\":\"12345678\"}",
//                 Enable = false,
//                 PluginType = PluginTypeEnum.Manage
//             }
//         };
//     }
//
//     /// <summary>
//     ///     获取Sn码
//     /// </summary>
//     /// <returns></returns>
//     private static string GetSn()
//     {
//         try
//         {
//             //px30
//             var val = ShellUtil.Bash("/hogo/bin/hogodevinfotools sn -r").Result;
//             if (!val.StartsWith("read the sn")) return "test";
//
//             var number = val.Replace("read the sn :", "").Trim();
//             var newNumber = number.Substring(number.Length - 4, 4);
//             return newNumber;
//         }
//         catch
//         {
//             return "test";
//         }
//     }
// }