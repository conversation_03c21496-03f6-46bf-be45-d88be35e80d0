
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32516.85
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01-应用服务", "01-应用服务", "{10587DCC-F344-428B-835E-B576D9332E93}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02-业务模块", "02-业务模块", "{095B9952-DE0D-4774-B59B-B62F1FD0DFEA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "03-数据模块", "03-数据模块", "{9688C482-F6B7-4EBF-8430-D28381CBB18C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "04-架构核心", "04-架构核心", "{C2E9BDB9-26C2-4196-B2E9-A7215F37DF2A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "05-协议模块", "05-协议模块", "{FE9BE163-01BE-4CBE-B047-1D161EE27D4C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Driver", "Driver", "{A7639F66-0861-42FA-95AE-ACA4A48082E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Cnc", "Cnc", "{56DB414E-A750-4338-8C06-B48A4CA1F148}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Plc", "Plc", "{8D26DA6B-325D-48B0-9FB3-9B8664C99412}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Other", "Other", "{B83989D4-337A-4FD8-AC41-0EEE9EDED1CD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Modbus", "Modbus", "{E72936EE-A849-4401-8672-6B961FA05E5C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AllenBradley", "AllenBradley", "{C498BAD4-B422-4191-85B5-6123E9029A52}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Delta", "Delta", "{9DB98674-1459-4340-80E4-9A7F44188FAA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Melsec", "Melsec", "{08A43E96-F789-4A7F-BC13-623E745E1442}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Omron", "Omron", "{7169B424-9FB1-4B03-B3D2-80E07F732580}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Panasonic", "Panasonic", "{933F7BE3-86F1-472F-9AB6-E69E76207FCA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Siemens", "Siemens", "{B5080A92-7886-4670-B8E6-0A6E4049D896}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Fanuc", "Fanuc", "{4D942C41-4CDD-4F35-A87A-AA9BC4A51D48}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Gsk", "Gsk", "{F24ABEB2-9B0A-4A03-A4EB-51D242044F0A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Knd", "Knd", "{F9E869F1-4500-4622-8161-233E1F37DEDB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Mitsub", "Mitsub", "{B9819914-F87B-40EA-8B30-10F3023A0F2C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Siemens", "Siemens", "{AA3964EF-F6C6-41AD-8D93-015E7167B195}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Syntec", "Syntec", "{7A83F46E-F6F7-47AE-9215-3A33CE043E35}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "OpcUa", "OpcUa", "{79AFB2C1-8A2E-4BC2-9CEC-83978B82887E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01-Business", "01-Business", "{E74D7F7D-1432-43B6-A9B6-C12AF08085B9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "XinJe", "XinJe", "{DE4F5AF9-0D77-4E12-9F40-2CAD01ED465C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "TcpClient", "TcpClient", "{AB7881BD-31BA-44CB-B589-29776F365C55}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02-Equipment", "02-Equipment", "{60D833F2-5A37-42F0-A871-C52C038BBE88}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Fatek", "Fatek", "{7FBBDE3E-6E84-4ABD-BDC7-4CDD53889A51}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "03-Plugins", "03-Plugins", "{4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Mazak", "Mazak", "{85E76C54-632E-4AA6-BE83-4AF939DF3D98}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Dlt", "Dlt", "{11F538F4-043C-4116-900D-A4BFAB84014E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Inovance", "Inovance", "{532CD58C-A6D8-47D4-9D7D-A8C185644796}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Snmp", "Snmp", "{5778C49B-B0E3-4D98-B101-E0C75906AC8C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Heidenhain", "Heidenhain", "{45113D93-789E-488A-BEE0-5476DC33A962}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "MqttServer", "MqttServer", "{FC1F154F-8F34-46CB-A069-8628ABDDD08F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Lnc", "Lnc", "{6D50B2F0-C151-45AF-ABB2-C3FB31C1E9D3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Fagor", "Fagor", "{16FE2BBE-E411-4845-A81E-E643FD2AEF90}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Hnc", "Hnc", "{80B8A4AC-83C2-42A4-8BEE-6354D943DA1E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Virtual", "Virtual", "{DE0633BC-8814-4F7A-879A-7B90CFB74103}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "InjectionMoldingMachine", "InjectionMoldingMachine", "{EFC3AEC2-4B66-4A74-9AC4-21B8412FD61B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Keyence", "Keyence", "{91E19FEF-BA9C-417C-855D-5915498689BA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XinJEInternalNet", "05-协议模块\Driver\Plc\XinJe\XinJEInternalNet\XinJEInternalNet.csproj", "{B01D491D-A9D0-4F5C-B5AF-E4C0EBC1C20E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XinJESerial", "05-协议模块\Driver\Plc\XinJe\XinJESerial\XinJESerial.csproj", "{74F5A437-A931-487C-AD0C-96900720E6A1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XinJESerialOverTcp", "05-协议模块\Driver\Plc\XinJe\XinJESerialOverTcp\XinJESerialOverTcp.csproj", "{E662A263-B22B-496E-B714-E0946AD70CF0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XinJETcpNet", "05-协议模块\Driver\Plc\XinJe\XinJETcpNet\XinJETcpNet.csproj", "{11B30E1B-3ECF-4A4A-9484-9DC5E7F8E8A8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SiemensPPI", "05-协议模块\Driver\Plc\Siemens\SiemensPPI\SiemensPPI.csproj", "{0EFAD08F-043E-4200-B25D-AD8AA4528A27}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SiemensS7", "05-协议模块\Driver\Plc\Siemens\SiemensS7\SiemensS7.csproj", "{5A77124A-F35B-468B-B4ED-BF4A26A36389}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MewtocolOverTcp", "05-协议模块\Driver\Plc\Panasonic\MewtocolOverTcp\MewtocolOverTcp.csproj", "{811E3DDE-7393-4F69-82F9-29862276845A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Driver.Core", "05-协议模块\Driver.Core\Driver.Core.csproj", "{E4F07DDB-8158-4A41-8BA4-2B6D4955E290}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CipNet", "05-协议模块\Driver\Plc\Omron\CipNet\CipNet.csproj", "{76E34317-69E8-4DF0-9ED9-9D1FE83878BB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FinsNet", "05-协议模块\Driver\Plc\Omron\FinsNet\FinsNet.csproj", "{82EA793F-4F58-494A-A5C5-CEAD5A3B60A5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FinsUdp", "05-协议模块\Driver\Plc\Omron\FinsUdp\FinsUdp.csproj", "{A3DA7E9E-64BD-4041-9C2F-7C98EB9D111A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HostLink", "05-协议模块\Driver\Plc\Omron\HostLink\HostLink.csproj", "{E7834DDF-5CB6-4E72-8A93-6AD600EA8697}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HostLinkOverTcp", "05-协议模块\Driver\Plc\Omron\HostLinkOverTcp\HostLinkOverTcp.csproj", "{520D4425-AA83-42D2-9265-EF12B50D5F2F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ModbusAscii", "05-协议模块\Driver\Plc\Modbus\ModbusAscii\ModbusAscii.csproj", "{3BC4186C-FDBB-4E9D-8DC2-1A0B0359A557}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ModbusAsciiOverTcp", "05-协议模块\Driver\Plc\Modbus\ModbusAsciiOverTcp\ModbusAsciiOverTcp.csproj", "{3F608DD6-86D1-46CA-A8F4-5FFDC49E4936}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ModbusRtu", "05-协议模块\Driver\Plc\Modbus\ModbusRtu\ModbusRtu.csproj", "{6DA12085-2F18-468F-A44D-D29898BBC1C0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ModbusRtuOverTcp", "05-协议模块\Driver\Plc\Modbus\ModbusRtuOverTcp\ModbusRtuOverTcp.csproj", "{955E6B78-A20B-4598-945F-D1CD9DEFA618}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ModbusTcp", "05-协议模块\Driver\Plc\Modbus\ModbusTcp\ModbusTcp.csproj", "{F77C07FC-4986-4591-BC88-A58CA69CF166}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ModbusUdpNet", "05-协议模块\Driver\Plc\Modbus\ModbusUdpNet\ModbusUdpNet.csproj", "{B923F838-DA13-4145-85CD-D7A6A1C6FC17}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "McA1E", "05-协议模块\Driver\Plc\Melsec\McA1E\McA1E.csproj", "{3AA9FDD2-762A-4E83-9D17-A3A951870A42}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "McFxLinks", "05-协议模块\Driver\Plc\Melsec\McFxLinks\McFxLinks.csproj", "{C4DE794D-389F-4D4B-B81A-0A11FA437CFC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "McQna3E", "05-协议模块\Driver\Plc\Melsec\McQna3E\McQna3E.csproj", "{838EB8C0-27B9-4C31-9304-AB105098E425}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "McUdp", "05-协议模块\Driver\Plc\Melsec\McUdp\McUdp.csproj", "{FCB27481-349D-449B-878C-A476543C7439}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MelsecA1EAscii", "05-协议模块\Driver\Plc\Melsec\MelsecA1EAscii\MelsecA1EAscii.csproj", "{D807F61D-DB8C-4A82-BD5F-05BF6C4A3B58}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InovanceTcp", "05-协议模块\Driver\Plc\Inovance\InovanceTcp\InovanceTcp.csproj", "{6C9C314E-6DB7-4E3C-A87A-AB575CDC4149}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FatekProgramOverTcp", "05-协议模块\Driver\Plc\Fatek\FatekProgramOverTcp\FatekProgramOverTcp.csproj", "{6770E69F-70B2-4175-9C2B-CBF7045F625C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DLT645", "05-协议模块\Driver\Plc\Dlt\DLT645\DLT645.csproj", "{49C8ED23-59BE-46DE-B714-43897406C5AA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DLT645OverTcp", "05-协议模块\Driver\Plc\Dlt\DLT645OverTcp\DLT645OverTcp.csproj", "{163B0DD9-CDA8-44F2-A394-3B4EA75DC39B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DLT698", "05-协议模块\Driver\Plc\Dlt\DLT698\DLT698.csproj", "{98A33DDA-89EF-4F54-AFD0-B7C5E7F0C801}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DLT698OverTcp", "05-协议模块\Driver\Plc\Dlt\DLT698OverTcp\DLT698OverTcp.csproj", "{D49FE9E2-BF91-4DC7-B4A2-CCE9F3B5EE85}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DLT698TcpNet", "05-协议模块\Driver\Plc\Dlt\DLT698TcpNet\DLT698TcpNet.csproj", "{DD33758E-246A-4C75-86E8-44D3AB21AE04}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DeltaSerialAscii", "05-协议模块\Driver\Plc\Delta\DeltaSerialAscii\DeltaSerialAscii.csproj", "{AD104492-1DB0-48D7-A904-F6D510C914F8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DeltaSerialAsciiOverTcp", "05-协议模块\Driver\Plc\Delta\DeltaSerialAsciiOverTcp\DeltaSerialAsciiOverTcp.csproj", "{F867E406-5823-475B-8C28-E4FF7164CB86}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DeltaTcp", "05-协议模块\Driver\Plc\Delta\DeltaTcp\DeltaTcp.csproj", "{F904E0E2-757B-4F5E-BCBE-B720B949CA61}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AllenBradleyCip", "05-协议模块\Driver\Plc\AllenBradley\AllenBradleyCip\AllenBradleyCip.csproj", "{51454431-ACE8-40BA-A11E-4C70F1004C19}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AllenBradleyPccc", "05-协议模块\Driver\Plc\AllenBradley\AllenBradleyPccc\AllenBradleyPccc.csproj", "{49EBD847-A5B4-488E-970F-3BE65358AB3E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Syntec6AE", "05-协议模块\Driver\Cnc\Syntec\Syntec6AE\Syntec6AE.csproj", "{D61D891E-3177-4805-9003-D73B4BA35380}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Syntec6MA", "05-协议模块\Driver\Cnc\Syntec\Syntec6MA\Syntec6MA.csproj", "{FBDAEFDB-5836-43FF-B360-C20DEE0ADD7C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SyntecV2", "05-协议模块\Driver\Cnc\Syntec\SyntecV2\SyntecV2.csproj", "{0C00AFD7-8592-4D00-9027-736EBEC24FF2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SyntecV3", "05-协议模块\Driver\Cnc\Syntec\SyntecV3\SyntecV3.csproj", "{1EAD2365-0349-4F82-92EA-A3D6662D8E90}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SyntecV4", "05-协议模块\Driver\Cnc\Syntec\SyntecV4\SyntecV4.csproj", "{AD43B39B-6384-4B13-8890-E233CE3BEB5E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Siemens", "05-协议模块\Driver\Cnc\Siemens\Siemens\Siemens.csproj", "{67C67941-EF9A-4EF7-A7E0-C1292DBCEB16}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mitsub", "05-协议模块\Driver\Cnc\Mitsub\Mitsub\Mitsub.csproj", "{F5A8E7AF-E5F2-48EF-A3CC-AF0298465F45}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MazakMatrix", "05-协议模块\Driver\Cnc\Mazak\MazakMatrix\MazakMatrix.csproj", "{B958F23E-FA27-48DA-9FE5-EF4C60752217}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MazakSmart", "05-协议模块\Driver\Cnc\Mazak\MazakSmart\MazakSmart.csproj", "{2680257F-D892-4059-95C8-2D35F5AC8161}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MazakSmooth", "05-协议模块\Driver\Cnc\Mazak\MazakSmooth\MazakSmooth.csproj", "{943EA3A6-3384-42BA-83A1-B5BF334B09E5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LncTcp", "05-协议模块\Driver\Cnc\Lnc\LncTcp\LncTcp.csproj", "{E30FAD6A-9457-4B21-94FE-0A0D377DF3B9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Knd", "05-协议模块\Driver\Cnc\Knd\Knd\Knd.csproj", "{B4A11E47-F855-4C29-A2F6-8E43104B40FB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Hnc", "05-协议模块\Driver\Cnc\Hnc\Hnc\Hnc.csproj", "{1785D22E-BF64-457E-931A-9A63A2718C83}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Heidenhain", "05-协议模块\Driver\Cnc\Heidenhain\Heidenhain\Heidenhain.csproj", "{030A1452-0B98-49C2-AB64-7114354E609A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Gsk25im", "05-协议模块\Driver\Cnc\Gsk\Gsk25im\Gsk25im.csproj", "{267CD9DF-0804-4C7C-9425-D185976F87B1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GskTcp", "05-协议模块\Driver\Cnc\Gsk\GskTcp\GskTcp.csproj", "{558EE40F-9DB8-4057-A1C5-A15DBEBD27E3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GskUdp", "05-协议模块\Driver\Cnc\Gsk\GskUdp\GskUdp.csproj", "{4AE8AE12-98EA-470C-A1A3-9705B79C4526}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fanuc", "05-协议模块\Driver\Cnc\Fanuc\Fanuc\Fanuc.csproj", "{4147BE4F-803E-4924-859B-5D2F3C888EE7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fanuc18i", "05-协议模块\Driver\Cnc\Fanuc\Fanuc18i\Fanuc18i.csproj", "{349005A1-DF60-4DF4-9551-60DD6DA20AF2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FanucMultiStation", "05-协议模块\Driver\Cnc\Fanuc\FanucMultiStation\FanucMultiStation.csproj", "{F243D409-E806-4E76-A702-A12170DFB1C4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FanucTtc", "05-协议模块\Driver\Cnc\Fanuc\FanucTtc\FanucTtc.csproj", "{E3A41A47-EF13-4B3F-8B11-B41622552D0D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FagorNet", "05-协议模块\Driver\Cnc\Fagor\FagorNet\FagorNet.csproj", "{D4A83110-5183-4728-B53E-8E4352ED3DDE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PORCHESON_Ps660Am", "05-协议模块\Driver\InjectionMoldingMachine\PORCHESON_Ps660Am\PORCHESON_Ps660Am.csproj", "{9C181965-E07E-46BD-A4EB-8A92034EC8D7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PORCHESON_Ps660Bm", "05-协议模块\Driver\InjectionMoldingMachine\PORCHESON_Ps660Bm\PORCHESON_Ps660Bm.csproj", "{66B54780-CF21-429B-8816-65AA48E5B2DD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TechMationAk", "05-协议模块\Driver\InjectionMoldingMachine\TechMationAk\TechMationAk.csproj", "{35551C87-6363-4EDC-B66D-E9AC80BB49F2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OpcUaClient", "05-协议模块\Driver\Other\OpcUa\OpcUaClient\OpcUaClient.csproj", "{C0C823E5-7187-4E2E-A438-530468312633}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Snmp", "05-协议模块\Driver\Other\Snmp\Snmp\Snmp.csproj", "{FF877281-05DA-448D-B980-6D7ABC3321A1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SnmpBase", "05-协议模块\Driver\Other\Snmp\SnmpBase\SnmpBase.csproj", "{2FFBAE92-91AB-44F8-9E92-001C0EF5EA51}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TcpClient", "05-协议模块\Driver\Other\TcpClient\TcpClient\TcpClient.csproj", "{4FEBD07D-1A5F-42C4-AA4A-34D3195E6B6A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VirtualProtocol", "05-协议模块\Driver\Other\Virtual\VirtualProtocol\VirtualProtocol.csproj", "{9DDCB33C-EC7B-49E3-8043-7769740FEAD1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DriversInterface", "05-协议模块\DriversInterface\DriversInterface.csproj", "{4108D567-39DB-4BE1-8540-E24D5B8658CE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.Common", "04-架构核心\IotGateway.Common\IotGateway.Common.csproj", "{E33D6F5D-5287-47C0-8E66-70B349900DD1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.Core", "04-架构核心\IotGateway.Core\IotGateway.Core.csproj", "{F4ED1C94-3E0D-44B2-9082-3032D308A8D3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.Web.Core", "04-架构核心\IotGateway.Web.Core\IotGateway.Web.Core.csproj", "{********-B752-45CE-80A4-A952D282B8A9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.WebSocket", "04-架构核心\IotGateway.WebSocket\IotGateway.WebSocket.csproj", "{351109FE-1E0E-4812-89FE-B4D1E9651F5B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.TDengIne", "03-数据模块\IotGateway.TDengIne\IotGateway.TDengIne.csproj", "{7452053E-DC4C-482E-AC8B-AC37E2473651}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.Application", "02-业务模块\01-Business\IotGateway.Application\IotGateway.Application.csproj", "{10D3E117-562E-4B3F-B022-39BED591B708}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.FengEdge2000", "02-业务模块\02-Equipment\IotGateway.FengEdge2000\IotGateway.FengEdge2000.csproj", "{770A6346-B7BA-432C-9E52-40D8D6E5E96E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.Debian", "02-业务模块\02-Equipment\IotGateway.Debian\IotGateway.Debian.csproj", "{3AAA7D9A-D7C0-4FAA-BFD5-29DBC6E73EF2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.Gt675x", "02-业务模块\02-Equipment\IotGateway.Gt675x\IotGateway.Gt675x.csproj", "{9504AF97-8661-439A-8D54-760931F841E5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.Px30", "02-业务模块\02-Equipment\IotGateway.Px30\IotGateway.Px30.csproj", "{C0124E08-72CD-4E84-A04A-A09EABBCB365}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.Ubuntu", "02-业务模块\02-Equipment\IotGateway.Ubuntu\IotGateway.Ubuntu.csproj", "{0C6CE22D-9973-44E4-A5DA-1A0471B2432F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.Windows", "02-业务模块\02-Equipment\IotGateway.Windows\IotGateway.Windows.csproj", "{FE9BC5BB-CC06-43F9-97D6-68139AA6D463}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.HostApd", "02-业务模块\03-Plugins\IotGateway.HostApd\IotGateway.HostApd.csproj", "{AE047C6C-0DF2-4B2F-B902-980B65C3B76D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.ModbusServer", "02-业务模块\03-Plugins\IotGateway.ModbusServer\IotGateway.ModbusServer.csproj", "{47D5DEDB-5D6F-4932-8DAE-9742226ADB74}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.OpcUaServer", "02-业务模块\03-Plugins\IotGateway.OpcUaServer\IotGateway.OpcUaServer.csproj", "{47DD6CA8-9341-456B-A4CA-56405CBC6FE8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway", "01-应用服务\IotGateway\IotGateway.csproj", "{41737C95-E005-41E8-B95F-877CA4CEDC69}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway_Web_Windows", "01-应用服务\IotGateway_Web_Windows\IotGateway_Web_Windows.csproj", "{3412AD89-44DF-41D2-81C4-1B2676AE4233}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "McNet", "05-协议模块\Driver\Plc\Keyence\McNet\McNet.csproj", "{297A4191-8DF6-44C0-B866-D12B81EB353C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "McAsciiNet", "05-协议模块\Driver\Plc\Keyence\McAsciiNet\McAsciiNet.csproj", "{047C3BCB-18E4-42BC-958F-4240362CDF1C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MqttServer", "05-协议模块\Driver\Other\MqttServer\MqttServer\MqttServer.csproj", "{75622958-E93C-4AD2-AF51-FF64B0513C83}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NanoSerial", "05-协议模块\Driver\Plc\Keyence\NanoSerial\NanoSerial.csproj", "{F66C5BBB-703B-4168-972A-EF8580D3C365}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NanoSerialOverTcp", "05-协议模块\Driver\Plc\Keyence\NanoSerialOverTcp\NanoSerialOverTcp.csproj", "{83A636B4-1DC9-43DE-BD54-FB138346C943}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Serial", "Serial", "{A8C041B7-C098-42E9-90F0-4A39C6C72767}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SerialClient", "05-协议模块\Driver\Other\Serial\SerialClient\SerialClient.csproj", "{72A3EE17-7AB5-46D4-8A67-D37C4CBCD149}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.Hw356x", "02-业务模块\02-Equipment\IotGateway.Hw356x\IotGateway.Hw356x.csproj", "{25713A9E-2ABF-4BDB-B1F5-FC1EABB6759B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.FengLink", "02-业务模块\03-Plugins\IotGateway.FengLink\IotGateway.FengLink.csproj", "{CBF3DE51-BB3A-4B98-BA4C-BEC940F94D4F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.EdgeDevice", "02-业务模块\01-Business\IotGateway.EdgeDevice\IotGateway.EdgeDevice.csproj", "{578D6138-402F-40BF-A92C-5287EAAF0863}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGatewayTerminal.Application", "02-业务模块\01-Business\IotGatewayTerminal.Application\IotGatewayTerminal.Application.csproj", "{447A6174-7A9C-4AE7-88BD-4EF11DEB8BB1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGatewayTerminal.Web.Core", "04-架构核心\IotGatewayTerminal.Web.Core\IotGatewayTerminal.Web.Core.csproj", "{FE04D4EB-CC4A-40EB-857D-D1549E2302A3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.Application.Entity", "02-业务模块\01-Business\IotGateway.Application.Entity\IotGateway.Application.Entity.csproj", "{F3C53405-C699-41C4-B2E8-4A1E53081871}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.TerUbuntu", "02-业务模块\02-Equipment\IotGateway.TerUbuntu\IotGateway.TerUbuntu.csproj", "{A3571B7B-8A22-45AD-BFDD-E4CAA394ACF6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGatewayTerminal", "01-应用服务\IotGatewayTerminal\IotGatewayTerminal.csproj", "{31284FE5-4142-45A9-828B-43FCFE0BD2D5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KeQiang_T6F3", "05-协议模块\Driver\InjectionMoldingMachine\KeQiang_T6F3\KeQiang_T6F3.csproj", "{8F77F473-D85F-405F-B25D-1759A1C74783}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MqttServerEx", "05-协议模块\Driver\Other\MqttServer\MqttServerEx\MqttServerEx.csproj", "{608A75DE-B83E-4B1F-8322-7465827349A9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "OpcDa", "OpcDa", "{24389F16-D313-4DAC-96A1-579F0909C0E8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OpcDaClient", "05-协议模块\Driver\Other\OpcDa\OpcDaClient\OpcDaClient.csproj", "{B114CC74-7997-4A08-AD31-A2981B305AA3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KeQiang_T6H3", "05-协议模块\Driver\InjectionMoldingMachine\KeQiang_T6H3\KeQiang_T6H3.csproj", "{6AB28BF5-1061-43BC-9289-0A2BAE28A6A2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WarpingMachine", "05-协议模块\Driver\WarpingMachine\WarpingMachine.csproj", "{9B225776-7A77-4FF1-84B6-C7F0A8BD249A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WarpingMachine", "WarpingMachine", "{6F3DEE71-D651-423B-942A-FE3CE47915CA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TechMation5521", "05-协议模块\Driver\InjectionMoldingMachine\TechMation5521\TechMation5521.csproj", "{F340F23D-C7B7-4AF5-A4DF-4A575CE6FB89}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.FengEdge200", "02-业务模块\02-Equipment\IotGateway.FengEdge200\IotGateway.FengEdge200.csproj", "{36A922C8-93B7-4091-A61C-B77CA2703543}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.FengEdge150", "02-业务模块\02-Equipment\IotGateway.FengEdge150\IotGateway.FengEdge150.csproj", "{918F0500-10D6-497B-A066-E29F2A6B37BB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Console_5521", "05-协议模块\Driver\InjectionMoldingMachine\Console_5521\Console_5521.csproj", "{3F1C7B9E-6634-4BDA-826C-0F6F55EC033C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Brother", "Brother", "{2720AF53-2AD8-4484-AA57-A8D32CB403F8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Brother", "05-协议模块\Driver\Cnc\Brother\Brother\Brother.csproj", "{CA852BAA-E397-4D53-B44D-DD44AAF271C2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.FtpServer", "02-业务模块\03-Plugins\IotGateway.FtpServer\IotGateway.FtpServer.csproj", "{D3DC19D3-D42A-4DCD-A8CD-364DE716F2CE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.Plugin.Core", "02-业务模块\03-Plugins\IotGateway.Plugin.Core\IotGateway.Plugin.Core.csproj", "{CE0DBC76-A59E-4DF3-8D08-33D833790A68}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HongXunAkDouble", "05-协议模块\Driver\InjectionMoldingMachine\HongXunAkDouble\HongXunAkDouble.csproj", "{1E87399D-4D46-4401-BE7B-563999C9E4DF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.SerialPortByTcp", "02-业务模块\03-Plugins\IotGateway.SerialPortByTcp\IotGateway.SerialPortByTcp.csproj", "{1D4BEF8E-5659-49A6-A5F4-639C5345D752}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "McFxSerial", "05-协议模块\Driver\Plc\Melsec\McFxSerial\McFxSerial.csproj", "{AEC19574-D1FB-4F3A-8CE7-F1A6C70A6ABC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.FtpSiemensService", "02-业务模块\03-Plugins\IotGateway.FtpSiemensService\IotGateway.FtpSiemensService.csproj", "{B5CAD537-0841-4C4A-BD98-9893397E4A40}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.FtpSiemensImplant", "02-业务模块\03-Plugins\IotGateway.FtpSiemensImplant\IotGateway.FtpSiemensImplant.csproj", "{0B87AA95-C48D-4968-BF50-EF5507FEBAE1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.SmbServer", "02-业务模块\03-Plugins\IotGateway.SmbServer\IotGateway.SmbServer.csproj", "{BF6C143F-C1A4-4E72-B4CB-1364FC52297C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.SerialPortSIEMENS", "02-业务模块\03-Plugins\IotGateway.SerialPortSIEMENS\IotGateway.SerialPortSIEMENS.csproj", "{9C572FD7-C7BD-4A47-81B7-458CC403754B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.Nfs", "02-业务模块\03-Plugins\IotGateway.Nfs\IotGateway.Nfs.csproj", "{DC084153-**************-11A6F317821A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "IotGateway.SerialPortByTcp.Siemens", "02-业务模块\03-Plugins\IotGateway.SerialPortByTcp.Siemens\IotGateway.SerialPortByTcp.Siemens.csproj", "{1CF7270C-96B4-4AF3-92B6-9F3B2FF66C2F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Kede", "Kede", "{96C4D9CE-3C4F-4D53-8427-AEB49FB03060}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Kede", "05-协议模块\Driver\Cnc\Kede\Kede\Kede.csproj", "{E9EE48B4-2917-44E9-820D-290C88B86CE7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B01D491D-A9D0-4F5C-B5AF-E4C0EBC1C20E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B01D491D-A9D0-4F5C-B5AF-E4C0EBC1C20E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B01D491D-A9D0-4F5C-B5AF-E4C0EBC1C20E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B01D491D-A9D0-4F5C-B5AF-E4C0EBC1C20E}.Release|Any CPU.Build.0 = Release|Any CPU
		{74F5A437-A931-487C-AD0C-96900720E6A1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{74F5A437-A931-487C-AD0C-96900720E6A1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{74F5A437-A931-487C-AD0C-96900720E6A1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{74F5A437-A931-487C-AD0C-96900720E6A1}.Release|Any CPU.Build.0 = Release|Any CPU
		{E662A263-B22B-496E-B714-E0946AD70CF0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E662A263-B22B-496E-B714-E0946AD70CF0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E662A263-B22B-496E-B714-E0946AD70CF0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E662A263-B22B-496E-B714-E0946AD70CF0}.Release|Any CPU.Build.0 = Release|Any CPU
		{11B30E1B-3ECF-4A4A-9484-9DC5E7F8E8A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{11B30E1B-3ECF-4A4A-9484-9DC5E7F8E8A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{11B30E1B-3ECF-4A4A-9484-9DC5E7F8E8A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{11B30E1B-3ECF-4A4A-9484-9DC5E7F8E8A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{0EFAD08F-043E-4200-B25D-AD8AA4528A27}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0EFAD08F-043E-4200-B25D-AD8AA4528A27}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0EFAD08F-043E-4200-B25D-AD8AA4528A27}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0EFAD08F-043E-4200-B25D-AD8AA4528A27}.Release|Any CPU.Build.0 = Release|Any CPU
		{5A77124A-F35B-468B-B4ED-BF4A26A36389}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A77124A-F35B-468B-B4ED-BF4A26A36389}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A77124A-F35B-468B-B4ED-BF4A26A36389}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A77124A-F35B-468B-B4ED-BF4A26A36389}.Release|Any CPU.Build.0 = Release|Any CPU
		{811E3DDE-7393-4F69-82F9-29862276845A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{811E3DDE-7393-4F69-82F9-29862276845A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{811E3DDE-7393-4F69-82F9-29862276845A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{811E3DDE-7393-4F69-82F9-29862276845A}.Release|Any CPU.Build.0 = Release|Any CPU
		{E4F07DDB-8158-4A41-8BA4-2B6D4955E290}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E4F07DDB-8158-4A41-8BA4-2B6D4955E290}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E4F07DDB-8158-4A41-8BA4-2B6D4955E290}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E4F07DDB-8158-4A41-8BA4-2B6D4955E290}.Release|Any CPU.Build.0 = Release|Any CPU
		{76E34317-69E8-4DF0-9ED9-9D1FE83878BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{76E34317-69E8-4DF0-9ED9-9D1FE83878BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{76E34317-69E8-4DF0-9ED9-9D1FE83878BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{76E34317-69E8-4DF0-9ED9-9D1FE83878BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{82EA793F-4F58-494A-A5C5-CEAD5A3B60A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{82EA793F-4F58-494A-A5C5-CEAD5A3B60A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{82EA793F-4F58-494A-A5C5-CEAD5A3B60A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{82EA793F-4F58-494A-A5C5-CEAD5A3B60A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3DA7E9E-64BD-4041-9C2F-7C98EB9D111A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3DA7E9E-64BD-4041-9C2F-7C98EB9D111A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3DA7E9E-64BD-4041-9C2F-7C98EB9D111A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3DA7E9E-64BD-4041-9C2F-7C98EB9D111A}.Release|Any CPU.Build.0 = Release|Any CPU
		{E7834DDF-5CB6-4E72-8A93-6AD600EA8697}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E7834DDF-5CB6-4E72-8A93-6AD600EA8697}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E7834DDF-5CB6-4E72-8A93-6AD600EA8697}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E7834DDF-5CB6-4E72-8A93-6AD600EA8697}.Release|Any CPU.Build.0 = Release|Any CPU
		{520D4425-AA83-42D2-9265-EF12B50D5F2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{520D4425-AA83-42D2-9265-EF12B50D5F2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{520D4425-AA83-42D2-9265-EF12B50D5F2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{520D4425-AA83-42D2-9265-EF12B50D5F2F}.Release|Any CPU.Build.0 = Release|Any CPU
		{3BC4186C-FDBB-4E9D-8DC2-1A0B0359A557}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3BC4186C-FDBB-4E9D-8DC2-1A0B0359A557}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3BC4186C-FDBB-4E9D-8DC2-1A0B0359A557}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3BC4186C-FDBB-4E9D-8DC2-1A0B0359A557}.Release|Any CPU.Build.0 = Release|Any CPU
		{3F608DD6-86D1-46CA-A8F4-5FFDC49E4936}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3F608DD6-86D1-46CA-A8F4-5FFDC49E4936}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3F608DD6-86D1-46CA-A8F4-5FFDC49E4936}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3F608DD6-86D1-46CA-A8F4-5FFDC49E4936}.Release|Any CPU.Build.0 = Release|Any CPU
		{6DA12085-2F18-468F-A44D-D29898BBC1C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6DA12085-2F18-468F-A44D-D29898BBC1C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6DA12085-2F18-468F-A44D-D29898BBC1C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6DA12085-2F18-468F-A44D-D29898BBC1C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{955E6B78-A20B-4598-945F-D1CD9DEFA618}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{955E6B78-A20B-4598-945F-D1CD9DEFA618}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{955E6B78-A20B-4598-945F-D1CD9DEFA618}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{955E6B78-A20B-4598-945F-D1CD9DEFA618}.Release|Any CPU.Build.0 = Release|Any CPU
		{F77C07FC-4986-4591-BC88-A58CA69CF166}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F77C07FC-4986-4591-BC88-A58CA69CF166}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F77C07FC-4986-4591-BC88-A58CA69CF166}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F77C07FC-4986-4591-BC88-A58CA69CF166}.Release|Any CPU.Build.0 = Release|Any CPU
		{B923F838-DA13-4145-85CD-D7A6A1C6FC17}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B923F838-DA13-4145-85CD-D7A6A1C6FC17}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B923F838-DA13-4145-85CD-D7A6A1C6FC17}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B923F838-DA13-4145-85CD-D7A6A1C6FC17}.Release|Any CPU.Build.0 = Release|Any CPU
		{3AA9FDD2-762A-4E83-9D17-A3A951870A42}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3AA9FDD2-762A-4E83-9D17-A3A951870A42}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3AA9FDD2-762A-4E83-9D17-A3A951870A42}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3AA9FDD2-762A-4E83-9D17-A3A951870A42}.Release|Any CPU.Build.0 = Release|Any CPU
		{C4DE794D-389F-4D4B-B81A-0A11FA437CFC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4DE794D-389F-4D4B-B81A-0A11FA437CFC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4DE794D-389F-4D4B-B81A-0A11FA437CFC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4DE794D-389F-4D4B-B81A-0A11FA437CFC}.Release|Any CPU.Build.0 = Release|Any CPU
		{838EB8C0-27B9-4C31-9304-AB105098E425}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{838EB8C0-27B9-4C31-9304-AB105098E425}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{838EB8C0-27B9-4C31-9304-AB105098E425}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{838EB8C0-27B9-4C31-9304-AB105098E425}.Release|Any CPU.Build.0 = Release|Any CPU
		{FCB27481-349D-449B-878C-A476543C7439}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FCB27481-349D-449B-878C-A476543C7439}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FCB27481-349D-449B-878C-A476543C7439}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FCB27481-349D-449B-878C-A476543C7439}.Release|Any CPU.Build.0 = Release|Any CPU
		{D807F61D-DB8C-4A82-BD5F-05BF6C4A3B58}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D807F61D-DB8C-4A82-BD5F-05BF6C4A3B58}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D807F61D-DB8C-4A82-BD5F-05BF6C4A3B58}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D807F61D-DB8C-4A82-BD5F-05BF6C4A3B58}.Release|Any CPU.Build.0 = Release|Any CPU
		{6C9C314E-6DB7-4E3C-A87A-AB575CDC4149}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6C9C314E-6DB7-4E3C-A87A-AB575CDC4149}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6C9C314E-6DB7-4E3C-A87A-AB575CDC4149}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6C9C314E-6DB7-4E3C-A87A-AB575CDC4149}.Release|Any CPU.Build.0 = Release|Any CPU
		{6770E69F-70B2-4175-9C2B-CBF7045F625C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6770E69F-70B2-4175-9C2B-CBF7045F625C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6770E69F-70B2-4175-9C2B-CBF7045F625C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6770E69F-70B2-4175-9C2B-CBF7045F625C}.Release|Any CPU.Build.0 = Release|Any CPU
		{49C8ED23-59BE-46DE-B714-43897406C5AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{49C8ED23-59BE-46DE-B714-43897406C5AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{49C8ED23-59BE-46DE-B714-43897406C5AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{49C8ED23-59BE-46DE-B714-43897406C5AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{163B0DD9-CDA8-44F2-A394-3B4EA75DC39B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{163B0DD9-CDA8-44F2-A394-3B4EA75DC39B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{163B0DD9-CDA8-44F2-A394-3B4EA75DC39B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{163B0DD9-CDA8-44F2-A394-3B4EA75DC39B}.Release|Any CPU.Build.0 = Release|Any CPU
		{98A33DDA-89EF-4F54-AFD0-B7C5E7F0C801}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{98A33DDA-89EF-4F54-AFD0-B7C5E7F0C801}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{98A33DDA-89EF-4F54-AFD0-B7C5E7F0C801}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{98A33DDA-89EF-4F54-AFD0-B7C5E7F0C801}.Release|Any CPU.Build.0 = Release|Any CPU
		{D49FE9E2-BF91-4DC7-B4A2-CCE9F3B5EE85}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D49FE9E2-BF91-4DC7-B4A2-CCE9F3B5EE85}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D49FE9E2-BF91-4DC7-B4A2-CCE9F3B5EE85}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D49FE9E2-BF91-4DC7-B4A2-CCE9F3B5EE85}.Release|Any CPU.Build.0 = Release|Any CPU
		{DD33758E-246A-4C75-86E8-44D3AB21AE04}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD33758E-246A-4C75-86E8-44D3AB21AE04}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD33758E-246A-4C75-86E8-44D3AB21AE04}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD33758E-246A-4C75-86E8-44D3AB21AE04}.Release|Any CPU.Build.0 = Release|Any CPU
		{AD104492-1DB0-48D7-A904-F6D510C914F8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AD104492-1DB0-48D7-A904-F6D510C914F8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AD104492-1DB0-48D7-A904-F6D510C914F8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AD104492-1DB0-48D7-A904-F6D510C914F8}.Release|Any CPU.Build.0 = Release|Any CPU
		{F867E406-5823-475B-8C28-E4FF7164CB86}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F867E406-5823-475B-8C28-E4FF7164CB86}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F867E406-5823-475B-8C28-E4FF7164CB86}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F867E406-5823-475B-8C28-E4FF7164CB86}.Release|Any CPU.Build.0 = Release|Any CPU
		{F904E0E2-757B-4F5E-BCBE-B720B949CA61}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F904E0E2-757B-4F5E-BCBE-B720B949CA61}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F904E0E2-757B-4F5E-BCBE-B720B949CA61}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F904E0E2-757B-4F5E-BCBE-B720B949CA61}.Release|Any CPU.Build.0 = Release|Any CPU
		{51454431-ACE8-40BA-A11E-4C70F1004C19}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{51454431-ACE8-40BA-A11E-4C70F1004C19}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{51454431-ACE8-40BA-A11E-4C70F1004C19}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{51454431-ACE8-40BA-A11E-4C70F1004C19}.Release|Any CPU.Build.0 = Release|Any CPU
		{49EBD847-A5B4-488E-970F-3BE65358AB3E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{49EBD847-A5B4-488E-970F-3BE65358AB3E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{49EBD847-A5B4-488E-970F-3BE65358AB3E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{49EBD847-A5B4-488E-970F-3BE65358AB3E}.Release|Any CPU.Build.0 = Release|Any CPU
		{D61D891E-3177-4805-9003-D73B4BA35380}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D61D891E-3177-4805-9003-D73B4BA35380}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D61D891E-3177-4805-9003-D73B4BA35380}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D61D891E-3177-4805-9003-D73B4BA35380}.Release|Any CPU.Build.0 = Release|Any CPU
		{FBDAEFDB-5836-43FF-B360-C20DEE0ADD7C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FBDAEFDB-5836-43FF-B360-C20DEE0ADD7C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FBDAEFDB-5836-43FF-B360-C20DEE0ADD7C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FBDAEFDB-5836-43FF-B360-C20DEE0ADD7C}.Release|Any CPU.Build.0 = Release|Any CPU
		{0C00AFD7-8592-4D00-9027-736EBEC24FF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0C00AFD7-8592-4D00-9027-736EBEC24FF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0C00AFD7-8592-4D00-9027-736EBEC24FF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0C00AFD7-8592-4D00-9027-736EBEC24FF2}.Release|Any CPU.Build.0 = Release|Any CPU
		{1EAD2365-0349-4F82-92EA-A3D6662D8E90}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1EAD2365-0349-4F82-92EA-A3D6662D8E90}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1EAD2365-0349-4F82-92EA-A3D6662D8E90}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1EAD2365-0349-4F82-92EA-A3D6662D8E90}.Release|Any CPU.Build.0 = Release|Any CPU
		{AD43B39B-6384-4B13-8890-E233CE3BEB5E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AD43B39B-6384-4B13-8890-E233CE3BEB5E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AD43B39B-6384-4B13-8890-E233CE3BEB5E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AD43B39B-6384-4B13-8890-E233CE3BEB5E}.Release|Any CPU.Build.0 = Release|Any CPU
		{67C67941-EF9A-4EF7-A7E0-C1292DBCEB16}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{67C67941-EF9A-4EF7-A7E0-C1292DBCEB16}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{67C67941-EF9A-4EF7-A7E0-C1292DBCEB16}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{67C67941-EF9A-4EF7-A7E0-C1292DBCEB16}.Release|Any CPU.Build.0 = Release|Any CPU
		{F5A8E7AF-E5F2-48EF-A3CC-AF0298465F45}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F5A8E7AF-E5F2-48EF-A3CC-AF0298465F45}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F5A8E7AF-E5F2-48EF-A3CC-AF0298465F45}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F5A8E7AF-E5F2-48EF-A3CC-AF0298465F45}.Release|Any CPU.Build.0 = Release|Any CPU
		{B958F23E-FA27-48DA-9FE5-EF4C60752217}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B958F23E-FA27-48DA-9FE5-EF4C60752217}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B958F23E-FA27-48DA-9FE5-EF4C60752217}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B958F23E-FA27-48DA-9FE5-EF4C60752217}.Release|Any CPU.Build.0 = Release|Any CPU
		{2680257F-D892-4059-95C8-2D35F5AC8161}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2680257F-D892-4059-95C8-2D35F5AC8161}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2680257F-D892-4059-95C8-2D35F5AC8161}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2680257F-D892-4059-95C8-2D35F5AC8161}.Release|Any CPU.Build.0 = Release|Any CPU
		{943EA3A6-3384-42BA-83A1-B5BF334B09E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{943EA3A6-3384-42BA-83A1-B5BF334B09E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{943EA3A6-3384-42BA-83A1-B5BF334B09E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{943EA3A6-3384-42BA-83A1-B5BF334B09E5}.Release|Any CPU.Build.0 = Release|Any CPU
		{E30FAD6A-9457-4B21-94FE-0A0D377DF3B9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E30FAD6A-9457-4B21-94FE-0A0D377DF3B9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E30FAD6A-9457-4B21-94FE-0A0D377DF3B9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E30FAD6A-9457-4B21-94FE-0A0D377DF3B9}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4A11E47-F855-4C29-A2F6-8E43104B40FB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4A11E47-F855-4C29-A2F6-8E43104B40FB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4A11E47-F855-4C29-A2F6-8E43104B40FB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4A11E47-F855-4C29-A2F6-8E43104B40FB}.Release|Any CPU.Build.0 = Release|Any CPU
		{1785D22E-BF64-457E-931A-9A63A2718C83}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1785D22E-BF64-457E-931A-9A63A2718C83}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1785D22E-BF64-457E-931A-9A63A2718C83}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1785D22E-BF64-457E-931A-9A63A2718C83}.Release|Any CPU.Build.0 = Release|Any CPU
		{030A1452-0B98-49C2-AB64-7114354E609A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{030A1452-0B98-49C2-AB64-7114354E609A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{030A1452-0B98-49C2-AB64-7114354E609A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{030A1452-0B98-49C2-AB64-7114354E609A}.Release|Any CPU.Build.0 = Release|Any CPU
		{267CD9DF-0804-4C7C-9425-D185976F87B1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{267CD9DF-0804-4C7C-9425-D185976F87B1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{267CD9DF-0804-4C7C-9425-D185976F87B1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{267CD9DF-0804-4C7C-9425-D185976F87B1}.Release|Any CPU.Build.0 = Release|Any CPU
		{558EE40F-9DB8-4057-A1C5-A15DBEBD27E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{558EE40F-9DB8-4057-A1C5-A15DBEBD27E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{558EE40F-9DB8-4057-A1C5-A15DBEBD27E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{558EE40F-9DB8-4057-A1C5-A15DBEBD27E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{4AE8AE12-98EA-470C-A1A3-9705B79C4526}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4AE8AE12-98EA-470C-A1A3-9705B79C4526}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4AE8AE12-98EA-470C-A1A3-9705B79C4526}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4AE8AE12-98EA-470C-A1A3-9705B79C4526}.Release|Any CPU.Build.0 = Release|Any CPU
		{4147BE4F-803E-4924-859B-5D2F3C888EE7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4147BE4F-803E-4924-859B-5D2F3C888EE7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4147BE4F-803E-4924-859B-5D2F3C888EE7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4147BE4F-803E-4924-859B-5D2F3C888EE7}.Release|Any CPU.Build.0 = Release|Any CPU
		{349005A1-DF60-4DF4-9551-60DD6DA20AF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{349005A1-DF60-4DF4-9551-60DD6DA20AF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{349005A1-DF60-4DF4-9551-60DD6DA20AF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{349005A1-DF60-4DF4-9551-60DD6DA20AF2}.Release|Any CPU.Build.0 = Release|Any CPU
		{F243D409-E806-4E76-A702-A12170DFB1C4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F243D409-E806-4E76-A702-A12170DFB1C4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F243D409-E806-4E76-A702-A12170DFB1C4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F243D409-E806-4E76-A702-A12170DFB1C4}.Release|Any CPU.Build.0 = Release|Any CPU
		{E3A41A47-EF13-4B3F-8B11-B41622552D0D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E3A41A47-EF13-4B3F-8B11-B41622552D0D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E3A41A47-EF13-4B3F-8B11-B41622552D0D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E3A41A47-EF13-4B3F-8B11-B41622552D0D}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4A83110-5183-4728-B53E-8E4352ED3DDE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4A83110-5183-4728-B53E-8E4352ED3DDE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4A83110-5183-4728-B53E-8E4352ED3DDE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4A83110-5183-4728-B53E-8E4352ED3DDE}.Release|Any CPU.Build.0 = Release|Any CPU
		{9C181965-E07E-46BD-A4EB-8A92034EC8D7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9C181965-E07E-46BD-A4EB-8A92034EC8D7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9C181965-E07E-46BD-A4EB-8A92034EC8D7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9C181965-E07E-46BD-A4EB-8A92034EC8D7}.Release|Any CPU.Build.0 = Release|Any CPU
		{66B54780-CF21-429B-8816-65AA48E5B2DD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{66B54780-CF21-429B-8816-65AA48E5B2DD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{66B54780-CF21-429B-8816-65AA48E5B2DD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{66B54780-CF21-429B-8816-65AA48E5B2DD}.Release|Any CPU.Build.0 = Release|Any CPU
		{35551C87-6363-4EDC-B66D-E9AC80BB49F2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{35551C87-6363-4EDC-B66D-E9AC80BB49F2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{35551C87-6363-4EDC-B66D-E9AC80BB49F2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{35551C87-6363-4EDC-B66D-E9AC80BB49F2}.Release|Any CPU.Build.0 = Release|Any CPU
		{C0C823E5-7187-4E2E-A438-530468312633}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0C823E5-7187-4E2E-A438-530468312633}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0C823E5-7187-4E2E-A438-530468312633}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0C823E5-7187-4E2E-A438-530468312633}.Release|Any CPU.Build.0 = Release|Any CPU
		{FF877281-05DA-448D-B980-6D7ABC3321A1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FF877281-05DA-448D-B980-6D7ABC3321A1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FF877281-05DA-448D-B980-6D7ABC3321A1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FF877281-05DA-448D-B980-6D7ABC3321A1}.Release|Any CPU.Build.0 = Release|Any CPU
		{2FFBAE92-91AB-44F8-9E92-001C0EF5EA51}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2FFBAE92-91AB-44F8-9E92-001C0EF5EA51}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2FFBAE92-91AB-44F8-9E92-001C0EF5EA51}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2FFBAE92-91AB-44F8-9E92-001C0EF5EA51}.Release|Any CPU.Build.0 = Release|Any CPU
		{4FEBD07D-1A5F-42C4-AA4A-34D3195E6B6A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4FEBD07D-1A5F-42C4-AA4A-34D3195E6B6A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4FEBD07D-1A5F-42C4-AA4A-34D3195E6B6A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4FEBD07D-1A5F-42C4-AA4A-34D3195E6B6A}.Release|Any CPU.Build.0 = Release|Any CPU
		{9DDCB33C-EC7B-49E3-8043-7769740FEAD1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9DDCB33C-EC7B-49E3-8043-7769740FEAD1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9DDCB33C-EC7B-49E3-8043-7769740FEAD1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9DDCB33C-EC7B-49E3-8043-7769740FEAD1}.Release|Any CPU.Build.0 = Release|Any CPU
		{4108D567-39DB-4BE1-8540-E24D5B8658CE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4108D567-39DB-4BE1-8540-E24D5B8658CE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4108D567-39DB-4BE1-8540-E24D5B8658CE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4108D567-39DB-4BE1-8540-E24D5B8658CE}.Release|Any CPU.Build.0 = Release|Any CPU
		{E33D6F5D-5287-47C0-8E66-70B349900DD1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E33D6F5D-5287-47C0-8E66-70B349900DD1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E33D6F5D-5287-47C0-8E66-70B349900DD1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E33D6F5D-5287-47C0-8E66-70B349900DD1}.Release|Any CPU.Build.0 = Release|Any CPU
		{F4ED1C94-3E0D-44B2-9082-3032D308A8D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F4ED1C94-3E0D-44B2-9082-3032D308A8D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F4ED1C94-3E0D-44B2-9082-3032D308A8D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F4ED1C94-3E0D-44B2-9082-3032D308A8D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-B752-45CE-80A4-A952D282B8A9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-B752-45CE-80A4-A952D282B8A9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-B752-45CE-80A4-A952D282B8A9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-B752-45CE-80A4-A952D282B8A9}.Release|Any CPU.Build.0 = Release|Any CPU
		{351109FE-1E0E-4812-89FE-B4D1E9651F5B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{351109FE-1E0E-4812-89FE-B4D1E9651F5B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{351109FE-1E0E-4812-89FE-B4D1E9651F5B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{351109FE-1E0E-4812-89FE-B4D1E9651F5B}.Release|Any CPU.Build.0 = Release|Any CPU
		{7452053E-DC4C-482E-AC8B-AC37E2473651}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7452053E-DC4C-482E-AC8B-AC37E2473651}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7452053E-DC4C-482E-AC8B-AC37E2473651}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7452053E-DC4C-482E-AC8B-AC37E2473651}.Release|Any CPU.Build.0 = Release|Any CPU
		{10D3E117-562E-4B3F-B022-39BED591B708}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{10D3E117-562E-4B3F-B022-39BED591B708}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{10D3E117-562E-4B3F-B022-39BED591B708}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{10D3E117-562E-4B3F-B022-39BED591B708}.Release|Any CPU.Build.0 = Release|Any CPU
		{770A6346-B7BA-432C-9E52-40D8D6E5E96E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{770A6346-B7BA-432C-9E52-40D8D6E5E96E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{770A6346-B7BA-432C-9E52-40D8D6E5E96E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{770A6346-B7BA-432C-9E52-40D8D6E5E96E}.Release|Any CPU.Build.0 = Release|Any CPU
		{3AAA7D9A-D7C0-4FAA-BFD5-29DBC6E73EF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3AAA7D9A-D7C0-4FAA-BFD5-29DBC6E73EF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3AAA7D9A-D7C0-4FAA-BFD5-29DBC6E73EF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3AAA7D9A-D7C0-4FAA-BFD5-29DBC6E73EF2}.Release|Any CPU.Build.0 = Release|Any CPU
		{9504AF97-8661-439A-8D54-760931F841E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9504AF97-8661-439A-8D54-760931F841E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9504AF97-8661-439A-8D54-760931F841E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9504AF97-8661-439A-8D54-760931F841E5}.Release|Any CPU.Build.0 = Release|Any CPU
		{C0124E08-72CD-4E84-A04A-A09EABBCB365}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0124E08-72CD-4E84-A04A-A09EABBCB365}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0124E08-72CD-4E84-A04A-A09EABBCB365}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0124E08-72CD-4E84-A04A-A09EABBCB365}.Release|Any CPU.Build.0 = Release|Any CPU
		{0C6CE22D-9973-44E4-A5DA-1A0471B2432F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0C6CE22D-9973-44E4-A5DA-1A0471B2432F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0C6CE22D-9973-44E4-A5DA-1A0471B2432F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0C6CE22D-9973-44E4-A5DA-1A0471B2432F}.Release|Any CPU.Build.0 = Release|Any CPU
		{FE9BC5BB-CC06-43F9-97D6-68139AA6D463}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE9BC5BB-CC06-43F9-97D6-68139AA6D463}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE9BC5BB-CC06-43F9-97D6-68139AA6D463}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE9BC5BB-CC06-43F9-97D6-68139AA6D463}.Release|Any CPU.Build.0 = Release|Any CPU
		{AE047C6C-0DF2-4B2F-B902-980B65C3B76D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AE047C6C-0DF2-4B2F-B902-980B65C3B76D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AE047C6C-0DF2-4B2F-B902-980B65C3B76D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AE047C6C-0DF2-4B2F-B902-980B65C3B76D}.Release|Any CPU.Build.0 = Release|Any CPU
		{47D5DEDB-5D6F-4932-8DAE-9742226ADB74}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47D5DEDB-5D6F-4932-8DAE-9742226ADB74}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47D5DEDB-5D6F-4932-8DAE-9742226ADB74}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47D5DEDB-5D6F-4932-8DAE-9742226ADB74}.Release|Any CPU.Build.0 = Release|Any CPU
		{47DD6CA8-9341-456B-A4CA-56405CBC6FE8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47DD6CA8-9341-456B-A4CA-56405CBC6FE8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47DD6CA8-9341-456B-A4CA-56405CBC6FE8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47DD6CA8-9341-456B-A4CA-56405CBC6FE8}.Release|Any CPU.Build.0 = Release|Any CPU
		{41737C95-E005-41E8-B95F-877CA4CEDC69}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{41737C95-E005-41E8-B95F-877CA4CEDC69}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{41737C95-E005-41E8-B95F-877CA4CEDC69}.Release|Any CPU.ActiveCfg = Release|x86
		{41737C95-E005-41E8-B95F-877CA4CEDC69}.Release|Any CPU.Build.0 = Release|x86
		{3412AD89-44DF-41D2-81C4-1B2676AE4233}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3412AD89-44DF-41D2-81C4-1B2676AE4233}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3412AD89-44DF-41D2-81C4-1B2676AE4233}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3412AD89-44DF-41D2-81C4-1B2676AE4233}.Release|Any CPU.Build.0 = Release|Any CPU
		{297A4191-8DF6-44C0-B866-D12B81EB353C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{297A4191-8DF6-44C0-B866-D12B81EB353C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{297A4191-8DF6-44C0-B866-D12B81EB353C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{297A4191-8DF6-44C0-B866-D12B81EB353C}.Release|Any CPU.Build.0 = Release|Any CPU
		{047C3BCB-18E4-42BC-958F-4240362CDF1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{047C3BCB-18E4-42BC-958F-4240362CDF1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{047C3BCB-18E4-42BC-958F-4240362CDF1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{047C3BCB-18E4-42BC-958F-4240362CDF1C}.Release|Any CPU.Build.0 = Release|Any CPU
		{75622958-E93C-4AD2-AF51-FF64B0513C83}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{75622958-E93C-4AD2-AF51-FF64B0513C83}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{75622958-E93C-4AD2-AF51-FF64B0513C83}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{75622958-E93C-4AD2-AF51-FF64B0513C83}.Release|Any CPU.Build.0 = Release|Any CPU
		{F66C5BBB-703B-4168-972A-EF8580D3C365}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F66C5BBB-703B-4168-972A-EF8580D3C365}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F66C5BBB-703B-4168-972A-EF8580D3C365}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F66C5BBB-703B-4168-972A-EF8580D3C365}.Release|Any CPU.Build.0 = Release|Any CPU
		{83A636B4-1DC9-43DE-BD54-FB138346C943}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{83A636B4-1DC9-43DE-BD54-FB138346C943}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{83A636B4-1DC9-43DE-BD54-FB138346C943}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{83A636B4-1DC9-43DE-BD54-FB138346C943}.Release|Any CPU.Build.0 = Release|Any CPU
		{72A3EE17-7AB5-46D4-8A67-D37C4CBCD149}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{72A3EE17-7AB5-46D4-8A67-D37C4CBCD149}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{72A3EE17-7AB5-46D4-8A67-D37C4CBCD149}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{72A3EE17-7AB5-46D4-8A67-D37C4CBCD149}.Release|Any CPU.Build.0 = Release|Any CPU
		{25713A9E-2ABF-4BDB-B1F5-FC1EABB6759B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{25713A9E-2ABF-4BDB-B1F5-FC1EABB6759B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{25713A9E-2ABF-4BDB-B1F5-FC1EABB6759B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{25713A9E-2ABF-4BDB-B1F5-FC1EABB6759B}.Release|Any CPU.Build.0 = Release|Any CPU
		{CBF3DE51-BB3A-4B98-BA4C-BEC940F94D4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CBF3DE51-BB3A-4B98-BA4C-BEC940F94D4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CBF3DE51-BB3A-4B98-BA4C-BEC940F94D4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CBF3DE51-BB3A-4B98-BA4C-BEC940F94D4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{578D6138-402F-40BF-A92C-5287EAAF0863}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{578D6138-402F-40BF-A92C-5287EAAF0863}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{578D6138-402F-40BF-A92C-5287EAAF0863}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{578D6138-402F-40BF-A92C-5287EAAF0863}.Release|Any CPU.Build.0 = Release|Any CPU
		{447A6174-7A9C-4AE7-88BD-4EF11DEB8BB1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{447A6174-7A9C-4AE7-88BD-4EF11DEB8BB1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{447A6174-7A9C-4AE7-88BD-4EF11DEB8BB1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{447A6174-7A9C-4AE7-88BD-4EF11DEB8BB1}.Release|Any CPU.Build.0 = Release|Any CPU
		{FE04D4EB-CC4A-40EB-857D-D1549E2302A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE04D4EB-CC4A-40EB-857D-D1549E2302A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE04D4EB-CC4A-40EB-857D-D1549E2302A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE04D4EB-CC4A-40EB-857D-D1549E2302A3}.Release|Any CPU.Build.0 = Release|Any CPU
		{F3C53405-C699-41C4-B2E8-4A1E53081871}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F3C53405-C699-41C4-B2E8-4A1E53081871}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F3C53405-C699-41C4-B2E8-4A1E53081871}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F3C53405-C699-41C4-B2E8-4A1E53081871}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3571B7B-8A22-45AD-BFDD-E4CAA394ACF6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3571B7B-8A22-45AD-BFDD-E4CAA394ACF6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3571B7B-8A22-45AD-BFDD-E4CAA394ACF6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3571B7B-8A22-45AD-BFDD-E4CAA394ACF6}.Release|Any CPU.Build.0 = Release|Any CPU
		{31284FE5-4142-45A9-828B-43FCFE0BD2D5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{31284FE5-4142-45A9-828B-43FCFE0BD2D5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{31284FE5-4142-45A9-828B-43FCFE0BD2D5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{31284FE5-4142-45A9-828B-43FCFE0BD2D5}.Release|Any CPU.Build.0 = Release|Any CPU
		{8F77F473-D85F-405F-B25D-1759A1C74783}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8F77F473-D85F-405F-B25D-1759A1C74783}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8F77F473-D85F-405F-B25D-1759A1C74783}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8F77F473-D85F-405F-B25D-1759A1C74783}.Release|Any CPU.Build.0 = Release|Any CPU
		{608A75DE-B83E-4B1F-8322-7465827349A9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{608A75DE-B83E-4B1F-8322-7465827349A9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{608A75DE-B83E-4B1F-8322-7465827349A9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{608A75DE-B83E-4B1F-8322-7465827349A9}.Release|Any CPU.Build.0 = Release|Any CPU
		{B114CC74-7997-4A08-AD31-A2981B305AA3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B114CC74-7997-4A08-AD31-A2981B305AA3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B114CC74-7997-4A08-AD31-A2981B305AA3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B114CC74-7997-4A08-AD31-A2981B305AA3}.Release|Any CPU.Build.0 = Release|Any CPU
		{6AB28BF5-1061-43BC-9289-0A2BAE28A6A2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6AB28BF5-1061-43BC-9289-0A2BAE28A6A2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6AB28BF5-1061-43BC-9289-0A2BAE28A6A2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6AB28BF5-1061-43BC-9289-0A2BAE28A6A2}.Release|Any CPU.Build.0 = Release|Any CPU
		{9B225776-7A77-4FF1-84B6-C7F0A8BD249A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9B225776-7A77-4FF1-84B6-C7F0A8BD249A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9B225776-7A77-4FF1-84B6-C7F0A8BD249A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9B225776-7A77-4FF1-84B6-C7F0A8BD249A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F340F23D-C7B7-4AF5-A4DF-4A575CE6FB89}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F340F23D-C7B7-4AF5-A4DF-4A575CE6FB89}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F340F23D-C7B7-4AF5-A4DF-4A575CE6FB89}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F340F23D-C7B7-4AF5-A4DF-4A575CE6FB89}.Release|Any CPU.Build.0 = Release|Any CPU
		{36A922C8-93B7-4091-A61C-B77CA2703543}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{36A922C8-93B7-4091-A61C-B77CA2703543}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{36A922C8-93B7-4091-A61C-B77CA2703543}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{36A922C8-93B7-4091-A61C-B77CA2703543}.Release|Any CPU.Build.0 = Release|Any CPU
		{918F0500-10D6-497B-A066-E29F2A6B37BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{918F0500-10D6-497B-A066-E29F2A6B37BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{918F0500-10D6-497B-A066-E29F2A6B37BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{918F0500-10D6-497B-A066-E29F2A6B37BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{3F1C7B9E-6634-4BDA-826C-0F6F55EC033C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3F1C7B9E-6634-4BDA-826C-0F6F55EC033C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3F1C7B9E-6634-4BDA-826C-0F6F55EC033C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3F1C7B9E-6634-4BDA-826C-0F6F55EC033C}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA852BAA-E397-4D53-B44D-DD44AAF271C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA852BAA-E397-4D53-B44D-DD44AAF271C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA852BAA-E397-4D53-B44D-DD44AAF271C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA852BAA-E397-4D53-B44D-DD44AAF271C2}.Release|Any CPU.Build.0 = Release|Any CPU
		{D3DC19D3-D42A-4DCD-A8CD-364DE716F2CE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D3DC19D3-D42A-4DCD-A8CD-364DE716F2CE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D3DC19D3-D42A-4DCD-A8CD-364DE716F2CE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D3DC19D3-D42A-4DCD-A8CD-364DE716F2CE}.Release|Any CPU.Build.0 = Release|Any CPU
		{CE0DBC76-A59E-4DF3-8D08-33D833790A68}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CE0DBC76-A59E-4DF3-8D08-33D833790A68}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CE0DBC76-A59E-4DF3-8D08-33D833790A68}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CE0DBC76-A59E-4DF3-8D08-33D833790A68}.Release|Any CPU.Build.0 = Release|Any CPU
		{1E87399D-4D46-4401-BE7B-563999C9E4DF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E87399D-4D46-4401-BE7B-563999C9E4DF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E87399D-4D46-4401-BE7B-563999C9E4DF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E87399D-4D46-4401-BE7B-563999C9E4DF}.Release|Any CPU.Build.0 = Release|Any CPU
		{1D4BEF8E-5659-49A6-A5F4-639C5345D752}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1D4BEF8E-5659-49A6-A5F4-639C5345D752}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1D4BEF8E-5659-49A6-A5F4-639C5345D752}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1D4BEF8E-5659-49A6-A5F4-639C5345D752}.Release|Any CPU.Build.0 = Release|Any CPU
		{AEC19574-D1FB-4F3A-8CE7-F1A6C70A6ABC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AEC19574-D1FB-4F3A-8CE7-F1A6C70A6ABC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AEC19574-D1FB-4F3A-8CE7-F1A6C70A6ABC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AEC19574-D1FB-4F3A-8CE7-F1A6C70A6ABC}.Release|Any CPU.Build.0 = Release|Any CPU
		{B5CAD537-0841-4C4A-BD98-9893397E4A40}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B5CAD537-0841-4C4A-BD98-9893397E4A40}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B5CAD537-0841-4C4A-BD98-9893397E4A40}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B5CAD537-0841-4C4A-BD98-9893397E4A40}.Release|Any CPU.Build.0 = Release|Any CPU
		{0B87AA95-C48D-4968-BF50-EF5507FEBAE1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0B87AA95-C48D-4968-BF50-EF5507FEBAE1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0B87AA95-C48D-4968-BF50-EF5507FEBAE1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0B87AA95-C48D-4968-BF50-EF5507FEBAE1}.Release|Any CPU.Build.0 = Release|Any CPU
		{BF6C143F-C1A4-4E72-B4CB-1364FC52297C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BF6C143F-C1A4-4E72-B4CB-1364FC52297C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BF6C143F-C1A4-4E72-B4CB-1364FC52297C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BF6C143F-C1A4-4E72-B4CB-1364FC52297C}.Release|Any CPU.Build.0 = Release|Any CPU
		{9C572FD7-C7BD-4A47-81B7-458CC403754B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9C572FD7-C7BD-4A47-81B7-458CC403754B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9C572FD7-C7BD-4A47-81B7-458CC403754B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9C572FD7-C7BD-4A47-81B7-458CC403754B}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC084153-**************-11A6F317821A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC084153-**************-11A6F317821A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC084153-**************-11A6F317821A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC084153-**************-11A6F317821A}.Release|Any CPU.Build.0 = Release|Any CPU
		{1CF7270C-96B4-4AF3-92B6-9F3B2FF66C2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1CF7270C-96B4-4AF3-92B6-9F3B2FF66C2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1CF7270C-96B4-4AF3-92B6-9F3B2FF66C2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1CF7270C-96B4-4AF3-92B6-9F3B2FF66C2F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E9EE48B4-2917-44E9-820D-290C88B86CE7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9EE48B4-2917-44E9-820D-290C88B86CE7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E9EE48B4-2917-44E9-820D-290C88B86CE7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E9EE48B4-2917-44E9-820D-290C88B86CE7}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A7639F66-0861-42FA-95AE-ACA4A48082E8} = {FE9BE163-01BE-4CBE-B047-1D161EE27D4C}
		{56DB414E-A750-4338-8C06-B48A4CA1F148} = {A7639F66-0861-42FA-95AE-ACA4A48082E8}
		{8D26DA6B-325D-48B0-9FB3-9B8664C99412} = {A7639F66-0861-42FA-95AE-ACA4A48082E8}
		{B83989D4-337A-4FD8-AC41-0EEE9EDED1CD} = {A7639F66-0861-42FA-95AE-ACA4A48082E8}
		{E72936EE-A849-4401-8672-6B961FA05E5C} = {8D26DA6B-325D-48B0-9FB3-9B8664C99412}
		{C498BAD4-B422-4191-85B5-6123E9029A52} = {8D26DA6B-325D-48B0-9FB3-9B8664C99412}
		{9DB98674-1459-4340-80E4-9A7F44188FAA} = {8D26DA6B-325D-48B0-9FB3-9B8664C99412}
		{08A43E96-F789-4A7F-BC13-623E745E1442} = {8D26DA6B-325D-48B0-9FB3-9B8664C99412}
		{7169B424-9FB1-4B03-B3D2-80E07F732580} = {8D26DA6B-325D-48B0-9FB3-9B8664C99412}
		{933F7BE3-86F1-472F-9AB6-E69E76207FCA} = {8D26DA6B-325D-48B0-9FB3-9B8664C99412}
		{B5080A92-7886-4670-B8E6-0A6E4049D896} = {8D26DA6B-325D-48B0-9FB3-9B8664C99412}
		{4D942C41-4CDD-4F35-A87A-AA9BC4A51D48} = {56DB414E-A750-4338-8C06-B48A4CA1F148}
		{F24ABEB2-9B0A-4A03-A4EB-51D242044F0A} = {56DB414E-A750-4338-8C06-B48A4CA1F148}
		{F9E869F1-4500-4622-8161-233E1F37DEDB} = {56DB414E-A750-4338-8C06-B48A4CA1F148}
		{B9819914-F87B-40EA-8B30-10F3023A0F2C} = {56DB414E-A750-4338-8C06-B48A4CA1F148}
		{AA3964EF-F6C6-41AD-8D93-015E7167B195} = {56DB414E-A750-4338-8C06-B48A4CA1F148}
		{7A83F46E-F6F7-47AE-9215-3A33CE043E35} = {56DB414E-A750-4338-8C06-B48A4CA1F148}
		{79AFB2C1-8A2E-4BC2-9CEC-83978B82887E} = {B83989D4-337A-4FD8-AC41-0EEE9EDED1CD}
		{E74D7F7D-1432-43B6-A9B6-C12AF08085B9} = {095B9952-DE0D-4774-B59B-B62F1FD0DFEA}
		{DE4F5AF9-0D77-4E12-9F40-2CAD01ED465C} = {8D26DA6B-325D-48B0-9FB3-9B8664C99412}
		{AB7881BD-31BA-44CB-B589-29776F365C55} = {B83989D4-337A-4FD8-AC41-0EEE9EDED1CD}
		{60D833F2-5A37-42F0-A871-C52C038BBE88} = {095B9952-DE0D-4774-B59B-B62F1FD0DFEA}
		{7FBBDE3E-6E84-4ABD-BDC7-4CDD53889A51} = {8D26DA6B-325D-48B0-9FB3-9B8664C99412}
		{4EC9D2AC-E138-4D08-89EA-953FD9BFBB56} = {095B9952-DE0D-4774-B59B-B62F1FD0DFEA}
		{85E76C54-632E-4AA6-BE83-4AF939DF3D98} = {56DB414E-A750-4338-8C06-B48A4CA1F148}
		{11F538F4-043C-4116-900D-A4BFAB84014E} = {8D26DA6B-325D-48B0-9FB3-9B8664C99412}
		{532CD58C-A6D8-47D4-9D7D-A8C185644796} = {8D26DA6B-325D-48B0-9FB3-9B8664C99412}
		{5778C49B-B0E3-4D98-B101-E0C75906AC8C} = {B83989D4-337A-4FD8-AC41-0EEE9EDED1CD}
		{45113D93-789E-488A-BEE0-5476DC33A962} = {56DB414E-A750-4338-8C06-B48A4CA1F148}
		{FC1F154F-8F34-46CB-A069-8628ABDDD08F} = {B83989D4-337A-4FD8-AC41-0EEE9EDED1CD}
		{6D50B2F0-C151-45AF-ABB2-C3FB31C1E9D3} = {56DB414E-A750-4338-8C06-B48A4CA1F148}
		{16FE2BBE-E411-4845-A81E-E643FD2AEF90} = {56DB414E-A750-4338-8C06-B48A4CA1F148}
		{80B8A4AC-83C2-42A4-8BEE-6354D943DA1E} = {56DB414E-A750-4338-8C06-B48A4CA1F148}
		{DE0633BC-8814-4F7A-879A-7B90CFB74103} = {B83989D4-337A-4FD8-AC41-0EEE9EDED1CD}
		{EFC3AEC2-4B66-4A74-9AC4-21B8412FD61B} = {A7639F66-0861-42FA-95AE-ACA4A48082E8}
		{91E19FEF-BA9C-417C-855D-5915498689BA} = {8D26DA6B-325D-48B0-9FB3-9B8664C99412}
		{B01D491D-A9D0-4F5C-B5AF-E4C0EBC1C20E} = {DE4F5AF9-0D77-4E12-9F40-2CAD01ED465C}
		{74F5A437-A931-487C-AD0C-96900720E6A1} = {DE4F5AF9-0D77-4E12-9F40-2CAD01ED465C}
		{E662A263-B22B-496E-B714-E0946AD70CF0} = {DE4F5AF9-0D77-4E12-9F40-2CAD01ED465C}
		{11B30E1B-3ECF-4A4A-9484-9DC5E7F8E8A8} = {DE4F5AF9-0D77-4E12-9F40-2CAD01ED465C}
		{0EFAD08F-043E-4200-B25D-AD8AA4528A27} = {B5080A92-7886-4670-B8E6-0A6E4049D896}
		{5A77124A-F35B-468B-B4ED-BF4A26A36389} = {B5080A92-7886-4670-B8E6-0A6E4049D896}
		{811E3DDE-7393-4F69-82F9-29862276845A} = {933F7BE3-86F1-472F-9AB6-E69E76207FCA}
		{E4F07DDB-8158-4A41-8BA4-2B6D4955E290} = {FE9BE163-01BE-4CBE-B047-1D161EE27D4C}
		{76E34317-69E8-4DF0-9ED9-9D1FE83878BB} = {7169B424-9FB1-4B03-B3D2-80E07F732580}
		{82EA793F-4F58-494A-A5C5-CEAD5A3B60A5} = {7169B424-9FB1-4B03-B3D2-80E07F732580}
		{A3DA7E9E-64BD-4041-9C2F-7C98EB9D111A} = {7169B424-9FB1-4B03-B3D2-80E07F732580}
		{E7834DDF-5CB6-4E72-8A93-6AD600EA8697} = {7169B424-9FB1-4B03-B3D2-80E07F732580}
		{520D4425-AA83-42D2-9265-EF12B50D5F2F} = {7169B424-9FB1-4B03-B3D2-80E07F732580}
		{3BC4186C-FDBB-4E9D-8DC2-1A0B0359A557} = {E72936EE-A849-4401-8672-6B961FA05E5C}
		{3F608DD6-86D1-46CA-A8F4-5FFDC49E4936} = {E72936EE-A849-4401-8672-6B961FA05E5C}
		{6DA12085-2F18-468F-A44D-D29898BBC1C0} = {E72936EE-A849-4401-8672-6B961FA05E5C}
		{955E6B78-A20B-4598-945F-D1CD9DEFA618} = {E72936EE-A849-4401-8672-6B961FA05E5C}
		{F77C07FC-4986-4591-BC88-A58CA69CF166} = {E72936EE-A849-4401-8672-6B961FA05E5C}
		{B923F838-DA13-4145-85CD-D7A6A1C6FC17} = {E72936EE-A849-4401-8672-6B961FA05E5C}
		{3AA9FDD2-762A-4E83-9D17-A3A951870A42} = {08A43E96-F789-4A7F-BC13-623E745E1442}
		{C4DE794D-389F-4D4B-B81A-0A11FA437CFC} = {08A43E96-F789-4A7F-BC13-623E745E1442}
		{838EB8C0-27B9-4C31-9304-AB105098E425} = {08A43E96-F789-4A7F-BC13-623E745E1442}
		{FCB27481-349D-449B-878C-A476543C7439} = {08A43E96-F789-4A7F-BC13-623E745E1442}
		{D807F61D-DB8C-4A82-BD5F-05BF6C4A3B58} = {08A43E96-F789-4A7F-BC13-623E745E1442}
		{6C9C314E-6DB7-4E3C-A87A-AB575CDC4149} = {532CD58C-A6D8-47D4-9D7D-A8C185644796}
		{6770E69F-70B2-4175-9C2B-CBF7045F625C} = {7FBBDE3E-6E84-4ABD-BDC7-4CDD53889A51}
		{49C8ED23-59BE-46DE-B714-43897406C5AA} = {11F538F4-043C-4116-900D-A4BFAB84014E}
		{163B0DD9-CDA8-44F2-A394-3B4EA75DC39B} = {11F538F4-043C-4116-900D-A4BFAB84014E}
		{98A33DDA-89EF-4F54-AFD0-B7C5E7F0C801} = {11F538F4-043C-4116-900D-A4BFAB84014E}
		{D49FE9E2-BF91-4DC7-B4A2-CCE9F3B5EE85} = {11F538F4-043C-4116-900D-A4BFAB84014E}
		{DD33758E-246A-4C75-86E8-44D3AB21AE04} = {11F538F4-043C-4116-900D-A4BFAB84014E}
		{AD104492-1DB0-48D7-A904-F6D510C914F8} = {9DB98674-1459-4340-80E4-9A7F44188FAA}
		{F867E406-5823-475B-8C28-E4FF7164CB86} = {9DB98674-1459-4340-80E4-9A7F44188FAA}
		{F904E0E2-757B-4F5E-BCBE-B720B949CA61} = {9DB98674-1459-4340-80E4-9A7F44188FAA}
		{51454431-ACE8-40BA-A11E-4C70F1004C19} = {C498BAD4-B422-4191-85B5-6123E9029A52}
		{49EBD847-A5B4-488E-970F-3BE65358AB3E} = {C498BAD4-B422-4191-85B5-6123E9029A52}
		{D61D891E-3177-4805-9003-D73B4BA35380} = {7A83F46E-F6F7-47AE-9215-3A33CE043E35}
		{FBDAEFDB-5836-43FF-B360-C20DEE0ADD7C} = {7A83F46E-F6F7-47AE-9215-3A33CE043E35}
		{0C00AFD7-8592-4D00-9027-736EBEC24FF2} = {7A83F46E-F6F7-47AE-9215-3A33CE043E35}
		{1EAD2365-0349-4F82-92EA-A3D6662D8E90} = {7A83F46E-F6F7-47AE-9215-3A33CE043E35}
		{AD43B39B-6384-4B13-8890-E233CE3BEB5E} = {7A83F46E-F6F7-47AE-9215-3A33CE043E35}
		{67C67941-EF9A-4EF7-A7E0-C1292DBCEB16} = {AA3964EF-F6C6-41AD-8D93-015E7167B195}
		{F5A8E7AF-E5F2-48EF-A3CC-AF0298465F45} = {B9819914-F87B-40EA-8B30-10F3023A0F2C}
		{B958F23E-FA27-48DA-9FE5-EF4C60752217} = {85E76C54-632E-4AA6-BE83-4AF939DF3D98}
		{2680257F-D892-4059-95C8-2D35F5AC8161} = {85E76C54-632E-4AA6-BE83-4AF939DF3D98}
		{943EA3A6-3384-42BA-83A1-B5BF334B09E5} = {85E76C54-632E-4AA6-BE83-4AF939DF3D98}
		{E30FAD6A-9457-4B21-94FE-0A0D377DF3B9} = {6D50B2F0-C151-45AF-ABB2-C3FB31C1E9D3}
		{B4A11E47-F855-4C29-A2F6-8E43104B40FB} = {F9E869F1-4500-4622-8161-233E1F37DEDB}
		{1785D22E-BF64-457E-931A-9A63A2718C83} = {80B8A4AC-83C2-42A4-8BEE-6354D943DA1E}
		{030A1452-0B98-49C2-AB64-7114354E609A} = {45113D93-789E-488A-BEE0-5476DC33A962}
		{267CD9DF-0804-4C7C-9425-D185976F87B1} = {F24ABEB2-9B0A-4A03-A4EB-51D242044F0A}
		{558EE40F-9DB8-4057-A1C5-A15DBEBD27E3} = {F24ABEB2-9B0A-4A03-A4EB-51D242044F0A}
		{4AE8AE12-98EA-470C-A1A3-9705B79C4526} = {F24ABEB2-9B0A-4A03-A4EB-51D242044F0A}
		{4147BE4F-803E-4924-859B-5D2F3C888EE7} = {4D942C41-4CDD-4F35-A87A-AA9BC4A51D48}
		{349005A1-DF60-4DF4-9551-60DD6DA20AF2} = {4D942C41-4CDD-4F35-A87A-AA9BC4A51D48}
		{F243D409-E806-4E76-A702-A12170DFB1C4} = {4D942C41-4CDD-4F35-A87A-AA9BC4A51D48}
		{E3A41A47-EF13-4B3F-8B11-B41622552D0D} = {4D942C41-4CDD-4F35-A87A-AA9BC4A51D48}
		{D4A83110-5183-4728-B53E-8E4352ED3DDE} = {16FE2BBE-E411-4845-A81E-E643FD2AEF90}
		{9C181965-E07E-46BD-A4EB-8A92034EC8D7} = {EFC3AEC2-4B66-4A74-9AC4-21B8412FD61B}
		{66B54780-CF21-429B-8816-65AA48E5B2DD} = {EFC3AEC2-4B66-4A74-9AC4-21B8412FD61B}
		{35551C87-6363-4EDC-B66D-E9AC80BB49F2} = {EFC3AEC2-4B66-4A74-9AC4-21B8412FD61B}
		{C0C823E5-7187-4E2E-A438-530468312633} = {79AFB2C1-8A2E-4BC2-9CEC-83978B82887E}
		{FF877281-05DA-448D-B980-6D7ABC3321A1} = {5778C49B-B0E3-4D98-B101-E0C75906AC8C}
		{2FFBAE92-91AB-44F8-9E92-001C0EF5EA51} = {5778C49B-B0E3-4D98-B101-E0C75906AC8C}
		{4FEBD07D-1A5F-42C4-AA4A-34D3195E6B6A} = {AB7881BD-31BA-44CB-B589-29776F365C55}
		{9DDCB33C-EC7B-49E3-8043-7769740FEAD1} = {DE0633BC-8814-4F7A-879A-7B90CFB74103}
		{4108D567-39DB-4BE1-8540-E24D5B8658CE} = {FE9BE163-01BE-4CBE-B047-1D161EE27D4C}
		{E33D6F5D-5287-47C0-8E66-70B349900DD1} = {C2E9BDB9-26C2-4196-B2E9-A7215F37DF2A}
		{F4ED1C94-3E0D-44B2-9082-3032D308A8D3} = {C2E9BDB9-26C2-4196-B2E9-A7215F37DF2A}
		{********-B752-45CE-80A4-A952D282B8A9} = {C2E9BDB9-26C2-4196-B2E9-A7215F37DF2A}
		{351109FE-1E0E-4812-89FE-B4D1E9651F5B} = {C2E9BDB9-26C2-4196-B2E9-A7215F37DF2A}
		{7452053E-DC4C-482E-AC8B-AC37E2473651} = {9688C482-F6B7-4EBF-8430-D28381CBB18C}
		{10D3E117-562E-4B3F-B022-39BED591B708} = {E74D7F7D-1432-43B6-A9B6-C12AF08085B9}
		{770A6346-B7BA-432C-9E52-40D8D6E5E96E} = {60D833F2-5A37-42F0-A871-C52C038BBE88}
		{3AAA7D9A-D7C0-4FAA-BFD5-29DBC6E73EF2} = {60D833F2-5A37-42F0-A871-C52C038BBE88}
		{9504AF97-8661-439A-8D54-760931F841E5} = {60D833F2-5A37-42F0-A871-C52C038BBE88}
		{C0124E08-72CD-4E84-A04A-A09EABBCB365} = {60D833F2-5A37-42F0-A871-C52C038BBE88}
		{0C6CE22D-9973-44E4-A5DA-1A0471B2432F} = {60D833F2-5A37-42F0-A871-C52C038BBE88}
		{FE9BC5BB-CC06-43F9-97D6-68139AA6D463} = {60D833F2-5A37-42F0-A871-C52C038BBE88}
		{AE047C6C-0DF2-4B2F-B902-980B65C3B76D} = {4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}
		{47D5DEDB-5D6F-4932-8DAE-9742226ADB74} = {4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}
		{47DD6CA8-9341-456B-A4CA-56405CBC6FE8} = {4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}
		{41737C95-E005-41E8-B95F-877CA4CEDC69} = {10587DCC-F344-428B-835E-B576D9332E93}
		{3412AD89-44DF-41D2-81C4-1B2676AE4233} = {10587DCC-F344-428B-835E-B576D9332E93}
		{297A4191-8DF6-44C0-B866-D12B81EB353C} = {91E19FEF-BA9C-417C-855D-5915498689BA}
		{047C3BCB-18E4-42BC-958F-4240362CDF1C} = {91E19FEF-BA9C-417C-855D-5915498689BA}
		{75622958-E93C-4AD2-AF51-FF64B0513C83} = {FC1F154F-8F34-46CB-A069-8628ABDDD08F}
		{F66C5BBB-703B-4168-972A-EF8580D3C365} = {91E19FEF-BA9C-417C-855D-5915498689BA}
		{83A636B4-1DC9-43DE-BD54-FB138346C943} = {91E19FEF-BA9C-417C-855D-5915498689BA}
		{A8C041B7-C098-42E9-90F0-4A39C6C72767} = {B83989D4-337A-4FD8-AC41-0EEE9EDED1CD}
		{72A3EE17-7AB5-46D4-8A67-D37C4CBCD149} = {A8C041B7-C098-42E9-90F0-4A39C6C72767}
		{25713A9E-2ABF-4BDB-B1F5-FC1EABB6759B} = {60D833F2-5A37-42F0-A871-C52C038BBE88}
		{CBF3DE51-BB3A-4B98-BA4C-BEC940F94D4F} = {4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}
		{578D6138-402F-40BF-A92C-5287EAAF0863} = {E74D7F7D-1432-43B6-A9B6-C12AF08085B9}
		{447A6174-7A9C-4AE7-88BD-4EF11DEB8BB1} = {E74D7F7D-1432-43B6-A9B6-C12AF08085B9}
		{FE04D4EB-CC4A-40EB-857D-D1549E2302A3} = {C2E9BDB9-26C2-4196-B2E9-A7215F37DF2A}
		{F3C53405-C699-41C4-B2E8-4A1E53081871} = {E74D7F7D-1432-43B6-A9B6-C12AF08085B9}
		{A3571B7B-8A22-45AD-BFDD-E4CAA394ACF6} = {60D833F2-5A37-42F0-A871-C52C038BBE88}
		{31284FE5-4142-45A9-828B-43FCFE0BD2D5} = {10587DCC-F344-428B-835E-B576D9332E93}
		{8F77F473-D85F-405F-B25D-1759A1C74783} = {EFC3AEC2-4B66-4A74-9AC4-21B8412FD61B}
		{608A75DE-B83E-4B1F-8322-7465827349A9} = {FC1F154F-8F34-46CB-A069-8628ABDDD08F}
		{24389F16-D313-4DAC-96A1-579F0909C0E8} = {B83989D4-337A-4FD8-AC41-0EEE9EDED1CD}
		{B114CC74-7997-4A08-AD31-A2981B305AA3} = {24389F16-D313-4DAC-96A1-579F0909C0E8}
		{6AB28BF5-1061-43BC-9289-0A2BAE28A6A2} = {EFC3AEC2-4B66-4A74-9AC4-21B8412FD61B}
		{9B225776-7A77-4FF1-84B6-C7F0A8BD249A} = {6F3DEE71-D651-423B-942A-FE3CE47915CA}
		{6F3DEE71-D651-423B-942A-FE3CE47915CA} = {A7639F66-0861-42FA-95AE-ACA4A48082E8}
		{F340F23D-C7B7-4AF5-A4DF-4A575CE6FB89} = {EFC3AEC2-4B66-4A74-9AC4-21B8412FD61B}
		{36A922C8-93B7-4091-A61C-B77CA2703543} = {60D833F2-5A37-42F0-A871-C52C038BBE88}
		{918F0500-10D6-497B-A066-E29F2A6B37BB} = {60D833F2-5A37-42F0-A871-C52C038BBE88}
		{3F1C7B9E-6634-4BDA-826C-0F6F55EC033C} = {EFC3AEC2-4B66-4A74-9AC4-21B8412FD61B}
		{2720AF53-2AD8-4484-AA57-A8D32CB403F8} = {56DB414E-A750-4338-8C06-B48A4CA1F148}
		{CA852BAA-E397-4D53-B44D-DD44AAF271C2} = {2720AF53-2AD8-4484-AA57-A8D32CB403F8}
		{D3DC19D3-D42A-4DCD-A8CD-364DE716F2CE} = {4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}
		{CE0DBC76-A59E-4DF3-8D08-33D833790A68} = {4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}
		{1E87399D-4D46-4401-BE7B-563999C9E4DF} = {EFC3AEC2-4B66-4A74-9AC4-21B8412FD61B}
		{1D4BEF8E-5659-49A6-A5F4-639C5345D752} = {4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}
		{AEC19574-D1FB-4F3A-8CE7-F1A6C70A6ABC} = {08A43E96-F789-4A7F-BC13-623E745E1442}
		{B5CAD537-0841-4C4A-BD98-9893397E4A40} = {4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}
		{0B87AA95-C48D-4968-BF50-EF5507FEBAE1} = {4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}
		{BF6C143F-C1A4-4E72-B4CB-1364FC52297C} = {4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}
		{9C572FD7-C7BD-4A47-81B7-458CC403754B} = {4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}
		{DC084153-**************-11A6F317821A} = {4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}
		{1CF7270C-96B4-4AF3-92B6-9F3B2FF66C2F} = {4EC9D2AC-E138-4D08-89EA-953FD9BFBB56}
		{96C4D9CE-3C4F-4D53-8427-AEB49FB03060} = {56DB414E-A750-4338-8C06-B48A4CA1F148}
		{E9EE48B4-2917-44E9-820D-290C88B86CE7} = {96C4D9CE-3C4F-4D53-8427-AEB49FB03060}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {36AB90FA-CC75-4A58-85B0-7F20FE2CD1C8}
	EndGlobalSection
EndGlobal
