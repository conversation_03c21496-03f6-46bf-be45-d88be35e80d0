using System.Net.Security;
using Feng.IotGateway.Core.Service.Cache;
using IotGateway.EdgeDevice.TransPonds;
using IotGateway.MQTT;
using IotGateway.Web.Core.Handlers;
using IotGatewayTerminal.Web.Core.EventBus;
using IotGatewayTerminal.Web.Core.BackgroundServices;
using NewLife.Caching;
using DateTime = Common.Extension.DateTime;

namespace IotGatewayTerminal.Web.Core;

[AppStartup(99)]
public class Startup : AppStartup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // 雪花Id
        YitIdHelper.SetIdGenerator(new IdGeneratorOptions {WorkerId = 3});

        // 统一配置项目选项注册
        services.AddProjectOptions();

        // 初始化数据库上下文
        services.AddSqlSugar();

        // 实现 JWT 身份验证过程控制
        services.AddJwt<JwtHandler>();

        // 配置跨越
        services.AddCorsAccessor();

        // 注册远程请求
        services.AddRemoteRequest(options =>
        {
            // 在所有客户端之前注册
            options.ApproveAllCerts();
            options.AddHttpClient(string.Empty)
                .ConfigurePrimaryHttpMessageHandler(u => new SocketsHttpHandler
                {
                    SslOptions = new SslClientAuthenticationOptions
                    {
                        RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true
                    }
                });
        });
        // 注册任务队列
        services.AddTaskQueue();

        // 注册定时任务
        services.AddSchedule();

        // 缓存注册
        services.AddCache();

        #region 标准请求和返回配置

        services.AddControllersWithViews()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.PropertyNamingPolicy = null;
                options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
                options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
                options.JsonSerializerOptions.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
                options.JsonSerializerOptions.Converters.AddDateTimeTypeConverters();
            })
            .AddInjectWithUnifyResult();
        services.AddControllers()
            .AddJsonOptions(config => { config.JsonSerializerOptions.PropertyNamingPolicy = null; });

        #endregion 标准请求和返回配置

        #region 事件总线

        // 注册日志事件订阅者(支持自定义消息队列组件)
        services.AddEventBus(builder =>
        {
            if (App.Configuration["Cache:CacheType"] == CacheTypeEnum.RedisCache.ToString())
                try
                {
                    // 替换事件源存储器
                    builder.ReplaceStorerOrFallback(serviceProvider =>
                    {
                        var redisCache = serviceProvider.GetService<ICache>();
                        // 创建默认内存通道事件源对象，可自定义队列路由key，比如这里是 eventbus
                        return new RedisEventSourceStorer(redisCache, "TerminalEventbus", 3000);
                    });
                }
                catch
                {
                    //如果redis无法连接,就使用默认的源 
                }

            //采集设备事件订阅
            builder.AddSubscriber<DeviceThreadEventSubscriber>();
        });

        #endregion 事件总线

        // 注册即时通讯
        services.AddSignalR();

        // 注册初始化服务
        services.AddHostedService<InitializationHostedService>();

        // 增加Logo输出显示
        AddConsoleLogo();

        #region 日志写入

        // 日志写入文件-消息、警告、错误
        Array.ForEach(new[] {LogLevel.Information, LogLevel.Warning, LogLevel.Error}, logLevel =>
        {
            services.AddFileLogging("logs/" + logLevel + "/{0:yyyy}-{0:MM}-{0:dd}.log", options =>
            {
                options.FileNameRule = fileName => string.Format(fileName, DateTime.ShangHai()); // 每天创建一个文件
                options.WriteFilter = logMsg => logMsg.LogLevel == logLevel;
                options.FileSizeLimitBytes = 10 * 1024 * 1024;
                options.MaxRollingFiles = 7;
            });
        });

        #endregion 日志写入
        
        services.AddSingleton<TransPondEventSubscriber>();

        services.AddSingleton<TransPondHostedService>();
        services.AddHostedService(provider => provider.GetRequiredService<TransPondHostedService>());
        
        services.AddSingleton<MasterClient>();
    }

    /// <summary>
    /// </summary>
    /// <param name="app"></param>
    /// <param name="env"></param>
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        //  NGINX 反向代理获取真实IP
        app.UseForwardedHeaders(new ForwardedHeadersOptions
            {ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto});
        if (env.IsDevelopment()) app.UseDeveloperExceptionPage();
        app.UseDefaultFiles();
        app.UseStaticFiles();
        //
        app.UseHttpsRedirection();
        //
        app.UseRouting();
        //
        app.EnableBuffering();
        //跨域
        app.UseCorsAccessor();
        //
        app.UseAuthentication();
        app.UseAuthorization();

        // 注册基础中间件
        app.UseInject(string.Empty);
        //
        app.UseEndpoints(endpoints =>
        {
            //DeviceHub
            endpoints.MapHub<DeviceHub>("/device");
            endpoints.MapControllers();
        });
        // 定时任务看板
        app.UseScheduleUI();
        // 时区 - 从全局配置获取，如果没有则使用默认值
        DateTime.UtcTime = GlobalConfigManager.GetConfigValue<bool>(ConfigConst.UtcTime);
    }

    public void AddConsoleLogo()
    {
        Console.ForegroundColor = ConsoleColor.Blue;
        Console.WriteLine(@"  
          _____       _                             _______                  _             _ 
         / ____|     | |                           |__   __|                (_)           | |
        | |  __  __ _| |_ _____      ____ _ _   _     | | ___ _ __ _ __ ___  _ _ __   __ _| |
        | | |_ |/ _` | __/ _ \ \ /\ / / _` | | | |    | |/ _ \ '__| '_ ` _ \| | '_ \ / _` | |
        | |__| | (_| | ||  __/\ V  V / (_| | |_| |    | |  __/ |  | | | | | | | | | | (_| | |
        |\_____|\__,_|\__\___| \_/\_/ \__,_|\__, |    |_|\___|_|  |_| |_| |_|_|_| |_|\__,_|_|
                                             __/ |    |___/                                            ");
        Console.ForegroundColor = ConsoleColor.Yellow;
    }
}