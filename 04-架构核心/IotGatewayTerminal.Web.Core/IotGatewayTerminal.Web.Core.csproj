<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\02-业务模块\01-Business\IotGatewayTerminal.Application\IotGatewayTerminal.Application.csproj" />
      <ProjectReference Include="..\..\02-业务模块\03-Plugins\IotGateway.FengLink\IotGateway.FengLink.csproj" />
    </ItemGroup>

</Project>
