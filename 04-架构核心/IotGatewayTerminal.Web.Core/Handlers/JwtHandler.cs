namespace IotGateway.Web.Core.Handlers;

public class JwtHandler : AppAuthorizeHandler
{
    /// <summary>
    ///     自动刷新Token
    /// </summary>
    /// <param name="context"></param>
    /// <param name="httpContext"></param>
    /// <returns></returns>
    public override async Task HandleAsync(AuthorizationHandlerContext context, DefaultHttpContext httpContext)
    {
        if (JWTEncryption.AutoRefreshToken(context, context.GetCurrentHttpContext(),
                App.GetOptions<JWTSettingsOptions>().ExpiredTime,
                App.GetOptions<RefreshTokenOptions>().ExpiredTime))
        {
            await AuthorizeHandleAsync(context);
        }
        else
        {
            context.Fail(); // 授权失败
            var currentHttpContext = context.GetCurrentHttpContext();
            if (currentHttpContext == null)
                return;
            currentHttpContext.SignoutToSwagger();
        }
    }

    /// <summary>
    ///     授权判断逻辑,授权通过返回 true,否则返回 false
    /// </summary>
    /// <param name="context"></param>
    /// <param name="httpContext"></param>
    /// <returns></returns>
    public override async Task<bool> PipelineAsync(AuthorizationHandlerContext context, DefaultHttpContext httpContext)
    {
        // 此处已经自动验证 Jwt Token的有效性了,无需手动验证
        return await CheckAuthorizeAsync(httpContext);
    }

    /// <summary>
    ///     检查权限
    /// </summary>
    /// <param name="httpContext"></param>
    /// <returns></returns>
    private static Task<bool> CheckAuthorizeAsync(DefaultHttpContext httpContext)
    {
        return Task.FromResult(true);
    }
}