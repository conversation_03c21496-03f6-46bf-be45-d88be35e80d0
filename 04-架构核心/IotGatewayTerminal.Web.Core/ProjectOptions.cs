namespace IotGatewayTerminal.Web.Core;

public static class ProjectOptions
{
    /// <summary>
    ///     注册项目配置选项
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddProjectOptions(this IServiceCollection services)
    {
        services.AddConfigurableOptions<DbConnectionOptions>();
        services.AddConfigurableOptions<RefreshTokenOptions>();
        services.AddConfigurableOptions<CacheOptions>();
        return services;
    }
}