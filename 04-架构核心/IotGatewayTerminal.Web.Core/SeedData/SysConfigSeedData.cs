namespace IotGatewayTerminal.Web.Core.SeedData;

/// <summary>
///     系统配置表种子数据
/// </summary>
public class SysConfigSeedData : ISqlSugarEntitySeedData<SysConfig>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysConfig> HasData()
    {
        return new[]
        {
            new SysConfig {Id = 1300000000201, Name = "服务器配置", Code = ConfigConst.SysFLinkIp, Value = "127.0.0.1"},
            new SysConfig {Id = 1300000000202, Name = "flink服务器是否启动", Code = ConfigConst.SysFLinkOpen, Value = "False"},
            new SysConfig {Id = 1300000000203, Name = "UTC时区", Code = ConfigConst.UtcTime, Value = "True"},
            new SysConfig {Id = 1300000000204, Name = "网关采集是否授权", Code = ConfigConst.Authorization, Value = "False"},
            new SysConfig {Id = 1300000000205, Name = "缓存最大数量", Code = ConfigConst.MaxOffLine, Value = "1000000"},
            new SysConfig {Id = 1300000000206, Name = "默认加载平台logo", Code = ConfigConst.Platform, Value = "supos"},
            new SysConfig {Id = 1300000000207, Name = "是否保存已发送数据", Code = ConfigConst.SaveData, Value = "False"},
            new SysConfig {Id = 1300000000208, Name = "dnc文件存储路径", Code = ConfigConst.DncFilePath, Value = "/Edge/DncFile/"},
            new SysConfig {Id = 1300000000209, Name = "是否允许设备写入", Code = ConfigConst.WhetherToAllowTheDeviceToWrite, Value = "True"},
            new SysConfig {Id = 1300000000210, Name = "系统解锁密码", Code = ConfigConst.SysUnLockPassword, Value = "e10adc3949ba59abbe56e057f20f883e"},
            new SysConfig {Id = 1300000000211, Name = "系统默认主页", Code = ConfigConst.SysDefIndexPage, Value = "/index"},
            new SysConfig {Id = 1300000000212, Name = "外部页面地址", Code = ConfigConst.SysExternalLink, Value = ""},
        };
    }
}