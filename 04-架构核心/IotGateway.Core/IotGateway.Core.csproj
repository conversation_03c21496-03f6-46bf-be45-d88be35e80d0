<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <NoWarn>1701;1702;1591</NoWarn>
        <DocumentationFile>IotGateway.Core.xml</DocumentationFile>
        <RootNamespace>Feng.IotGateway.Core</RootNamespace>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
        <DocumentationFile>IotGateway.Core.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
        <DocumentationFile>IotGateway.Core.xml</DocumentationFile>
    </PropertyGroup>

    <ItemGroup>
        <None Remove="IotGateway.Core.xml"/>
        <None Remove="Entity\c6db8820e43f54c707c54139236f293.png"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="FubarDev.FtpServer" Version="3.1.2" />
        <PackageReference Include="FubarDev.FtpServer.FileSystem.DotNet" Version="3.1.2" />
        <PackageReference Include="Microsoft.Data.Sqlite" Version="9.0.3" />
        <PackageReference Include="NewLife.Redis" Version="5.7.2024.801"/>
        <PackageReference Include="SqlSugarCore" Version="5.1.4.188" />
        <PackageReference Include="UAParser" Version="3.1.47"/>
        <PackageReference Include="Yitter.IdGenerator" Version="1.0.15"/>
        <PackageReference Include="DynamicExpresso.Core" Version="2.16.1"/>
        <PackageReference Include="Furion.Extras.Authentication.JwtBearer" Version="4.9.5.26" />
        <PackageReference Include="Furion.Extras.ObjectMapper.Mapster" Version="4.9.5.26" />
        <PackageReference Include="Google.Protobuf" Version="4.0.0-rc2"/>
        <PackageReference Include="Jint" Version="4.0.1"/>
        <PackageReference Include="MiniExcel" Version="1.34.1"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\IotGateway.Common\IotGateway.Common.csproj"/>
    </ItemGroup>

</Project>
