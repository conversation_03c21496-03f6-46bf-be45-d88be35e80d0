using System.Security.Cryptography;
using Feng.IotGateway.Core.Service.Cache;
using Furion.LinqBuilder;
using Microsoft.Extensions.Hosting;
using DateTime = Common.Extension.DateTime;

namespace Feng.IotGateway.Core.Util;

/// <summary>
///     网关鉴权
/// </summary>
public static class AuthorizationUtil
{
    /// <summary>
    /// 检查是否已授权
    /// </summary>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public static async Task<bool> IsAuthorized()
    {
        // 开发环境跳过授权校验
        if (IsDevelopmentEnvironment())
        {
            return true;
        }

        if (GlobalConfigManager.GetConfigValue<bool>(ConfigConst.Authorization))
        {
            var authorize = await GetAuthorization();
            if (authorize == null)
                throw Oops.Oh("未授权,请联系管理员！");
            if (DateTime.Now() >= Convert.ToDateTime(authorize.EndTime))
                throw Oops.Oh("授权过期！");
        }
        else
            return false;

        return true;
    }

    /// <summary>
    /// 检查是否为开发环境
    /// </summary>
    /// <returns></returns>
    private static bool IsDevelopmentEnvironment()
    {
        // 检查环境变量
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        if (!string.IsNullOrEmpty(environment) && environment.Equals("Development", StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }

        // 检查是否在调试模式下运行
        #if DEBUG
        return true;
        #endif

        return false;
    }

    /// <summary>
    ///     获取授权信息
    /// </summary>
    /// <returns></returns>
    public static async Task<SetAuthorizeOutput?> GetAuthorization()
    {
        if (!File.Exists(GatewayFilePath.KeyPath))
            return null;
        var readKeyValue = await File.ReadAllTextAsync(GatewayFilePath.KeyPath);
        if (readKeyValue.IsNullOrEmpty())
            return null;
        try
        {
            var privateKey = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                var currentDirectory = Directory.GetCurrentDirectory(); // 获取当前目录
                var keyFilePath = Path.Combine(currentDirectory, "RSA.Private"); // 构建完整文件路径
                privateKey = await File.ReadAllTextAsync(keyFilePath);
            }
            else
            {
                privateKey = await File.ReadAllTextAsync(GatewayFilePath.RsaPath);
            }

            var decryptPwd = Decrypt(readKeyValue, privateKey);
            var authorize = JSON.Deserialize<SetAuthorizeOutput>(decryptPwd);
            if (authorize.Sn.IsNullOrEmpty())
                return null;
            authorize.Version = "V" + MachineUtil.Version;
            authorize.Ident = await GatewayName();
            return authorize;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public static async Task<string> GatewayName()
    {
        if (!File.Exists(GatewayFilePath.IdentPath))
        {
            await File.WriteAllTextAsync(GatewayFilePath.IdentPath, "fengEdge-200");
            return "fengEdge-200";
        }

        var readIdent = await File.ReadAllTextAsync(GatewayFilePath.IdentPath);
        return readIdent ?? "fengEdge-200";
    }

    /// <summary>
    ///     Decrypts encrypted text given a RSA private key file path.给定路径的RSA私钥文件解密 加密文本
    /// </summary>
    /// <param name="encryptedText">加密的密文</param>
    /// <param name="privateXmlKey">用于加密的私钥</param>
    /// <returns>未加密数据的字符串</returns>
    public static string Decrypt(string encryptedText, string privateXmlKey)
    {
        using var rsa = new RSACryptoServiceProvider(2048);
        try
        {
            rsa.FromXmlString(privateXmlKey);

            var bytesEncrypted = Convert.FromBase64String(encryptedText);

            var bytesPlainText = rsa.Decrypt(bytesEncrypted, false);

            return Encoding.Unicode.GetString(bytesPlainText);
        }
        finally
        {
            rsa.PersistKeyInCsp = false;
        }
    }
}

/// <summary>
///     授权
/// </summary>
public class SetAuthorizeOutput
{
    /// <summary>
    ///     Sn
    /// </summary>
    public string? Sn { get; set; }

    /// <summary>
    ///     设备数量
    /// </summary>
    public short DeviceNumber { get; set; }

    /// <summary>
    ///     属性数量
    /// </summary>
    public short VariableNumber { get; set; }

    /// <summary>
    ///     指纹
    /// </summary>
    public string Fingerprint { get; set; }

    /// <summary>
    ///     状态
    /// </summary>
    public string Status => string.IsNullOrEmpty(EndTime) ? "未授权" : System.DateTime.Now >= Convert.ToDateTime(EndTime) ? "过期" : "正常";

    /// <summary>
    ///     到期时间
    /// </summary>
    public string EndTime { get; set; }

    /// <summary>
    ///     网关名称
    /// </summary>
    public string Ident { get; set; }

    /// <summary>
    ///     后端版本号
    /// </summary>
    public string Version { get; set; }
}