namespace Feng.IotGateway.Core.Util;

/// <summary>
///     获取服务器信息
/// </summary>
public static class MachineUtil
{
    public static dynamic Clay = Furion.ClayObject.Clay.Object(new
    {
        Eth0 = "",
        Eth1 = "",
        Eth2 = "",
        Usb0 = "",
        Wifi = ""
    });

    /// <summary>
    ///     发送网关自身的消息
    /// </summary>
    public class PublishGatewayMessage
    {
        /// <summary>
        ///     Ip
        /// </summary>
        public string Ip { get; set; }

        /// <summary>
        ///     版本号
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        ///     发送时间
        /// </summary>
        public DateTime PublishTime { get; set; }
    }

    /// <summary>
    ///     资源使用信息
    /// </summary>
    public static UserInfo UseInfo = new();

    /// <summary>
    ///     获取内存信息
    /// </summary>
    /// <returns></returns>
    public static MemInfo RamInfo()
    {
        try
        {
            var output = ShellUtil.Bash("free").GetAwaiter().GetResult(); // 获取内存信息
            var lines = output.Split("\n"); // 分割内存信息
            var memory = lines[1].Split(" ", StringSplitOptions.RemoveEmptyEntries); // 分割内存信息
            var memInfo = new MemInfo(); // 内存信息
            memInfo.MemTotal = long.Parse(memory[1]); // 总内存
            memInfo.MemAvailable = long.Parse(memory[3]); // 可用内存
            memInfo.MemUsage = long.Parse(memory[2]); // 已使用内存
            memInfo.MemRate = Math.Round(Convert.ToDouble(100 * memInfo.MemUsage / memInfo.MemTotal), 2); // 内存使用率
            return memInfo;
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"内存信息获取失败:{ex.Message}");
        }
    }

    /// <summary>
    ///     程序版本号
    /// </summary>
    public static string Version { get; set; } = string.Empty;

    /// <summary>
    ///     当前系统环境
    /// </summary>
    public static CurrentSystemEnvironmentEnum CurrentSystemEnvironment;

    /// <summary>
    ///     授权
    /// </summary>
    public static SetAuthorizeOutput Authorize = new();

    /// <summary>
    ///     SupOS服务器协议版本信息
    /// </summary>
    public static ProtocolVersionInfo ServerVersion { get; set; }

    /// <summary>
    ///     局域网中网关设备
    /// </summary>
    public static readonly Dictionary<string, PublishGatewayMessage> LanGatewayDevice = new();

    /// <summary>
    ///     采集服务启动时间
    /// </summary>
    public static DateTime ServiceOnlineTime = global::Common.Extension.DateTime.Now();

    /// <summary>
    ///     系统运行时间
    /// </summary>
    public static DateTime SystemRunTime = global::Common.Extension.DateTime.Now();

    /// <summary>
    /// 离线数据总量
    /// </summary>
    public static long OffLineCount;
    /// <summary>
    ///     下行次数
    /// </summary>
    public static long ReadCount = 0;

    /// <summary>
    ///     上行次数
    /// </summary>
    public static long SendCount = 0;

    /// <summary>
    ///     是否Linux
    /// </summary>
    /// <returns></returns>
    public static bool IsUnix()
    {
        return RuntimeInformation.IsOSPlatform(OSPlatform.OSX) || RuntimeInformation.IsOSPlatform(OSPlatform.Linux);
    }

    /// <summary>
    ///     获取采集网关mac地址
    /// </summary>
    /// <returns></returns>
    public static string Eth1HwAddr()
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                return "";

            var resultData = "";
            var process = new Process
            {
                StartInfo = new ProcessStartInfo("ifconfig", "eth1")
                {
                    RedirectStandardOutput = true,
                    UseShellExecute = false
                }
            };
            process.Start();
            var hddInfo = process.StandardOutput.ReadToEnd();
            process.WaitForExit();
            process.Dispose();
            var isName = false;

            var lines = hddInfo.Split('\n');
            foreach (var item in lines)
            {
                if (item.StartsWith("eth1"))
                    isName = true;
                if (!item.Contains("HWaddr") || !isName) continue;
                resultData = item.Substring(item.IndexOf("HWaddr", StringComparison.Ordinal) + 6).Trim();
                break;
            }

            return resultData;
        }
        catch (Exception ex)
        {
            Log.Error(ex.Message);
            throw new Exception(ex.Message);
        }
    }

    /// <summary>
    ///     修改本机时间
    /// </summary>
    /// <returns></returns>
    public static async Task SetTime(DateTime time)
    {
        await ShellUtil.Bash($"date -s '{time:yyyy-MM-dd HH:mm:ss}' && hwclock -w --systohc");
    }
}

/// <summary>
/// 协议版本信息
/// </summary>
public class ProtocolVersionInfo
{
    /// <summary>
    /// 主版本号
    /// </summary>
    public uint Major { get; set; }

    /// <summary>
    /// 次版本号
    /// </summary>
    public uint Minor { get; set; }

    /// <summary>
    /// 转换为字符串
    /// </summary>
    public override string ToString()
    {
        return $"{Major}.{Minor}";
    }
}

/// <summary>
/// 网关系统属性
/// </summary>
public class UserInfo
{
    /// <summary>
    ///     网络
    /// </summary>
    public Dictionary<string, string> Network { get; set; }

    /// <summary>
    /// 网络信号
    /// </summary>
    public object Signal { get; set; }

    /// <summary>
    ///     磁盘使用
    /// </summary>
    public DiskInfo DiskInfo { get; set; }

    /// <summary>
    ///     内存
    /// </summary>
    public MemInfo MemInfo { get; set; }

    /// <summary>
    ///     cpu使用率
    /// </summary>
    public double CpuRate { get; set; }
}

/// <summary>
///     硬盘使用大小
/// </summary>
public class DiskInfo
{
    /// <summary>
    ///     硬盘大小
    /// </summary>
    public long DiskSize { get; set; }

    /// <summary>
    ///     已使用大小
    /// </summary>
    public long DiskUsed { get; set; }

    /// <summary>
    ///     可用大小
    /// </summary>
    public long DiskAvailable { get; set; }

    /// <summary>
    ///     使用率
    /// </summary>
    public double DiskRate { get; set; }
}

/// <summary>
/// 内存相关
/// </summary>
public class MemInfo
{
    /// <summary>
    /// 总内存字节数
    /// </summary>
    public long MemTotal { get; set; }

    /// <summary>
    /// 总可用内存字节数
    /// </summary>
    public long MemAvailable { get; set; }

    /// <summary>
    /// 总已使用内存字节数
    /// </summary>
    public long MemUsage { get; set; }

    /// <summary>
    /// 内存使用率
    /// </summary>
    public double MemRate { get; set; }
}

/// <summary>
/// 当前系统
/// </summary>
public enum CurrentSystemEnvironmentEnum
{
    /// <summary>
    ///     px30
    /// </summary>
    [Description("Px30")] Px30 = 1,

    /// <summary>
    ///     Debian
    /// </summary>
    [Description("Debian")] Debian = 2,

    /// <summary>
    ///     Ubuntu
    /// </summary>
    [Description("Ubuntu")] Ubuntu = 3,

    /// <summary>
    ///     Wr610
    /// </summary>
    [Description("Wr610")] Wr610 = 4,

    /// <summary>
    ///     Gt675X
    /// </summary>
    [Description("Gt675X")] Gt675X = 6,

    /// <summary>
    ///     Windows
    /// </summary>
    [Description("Windows")] Windows = 7,

    /// <summary>
    ///     Hw356X
    /// </summary>
    [Description("Hw356X")] Hw356X = 8,

    /// <summary>
    ///     TerUbuntu
    /// </summary>
    [Description("TerUbuntu")] TerUbuntu = 9,

    /// <summary>
    ///     FengEdge150
    /// </summary>
    [Description("FengEdge150")] FengEdge150 = 20,

    /// <summary>
    ///     FengEdge200
    /// </summary>
    [Description("FengEdge200")] FengEdge200 = 30,

    /// <summary>
    ///     FengEdge2000
    /// </summary>
    [Description("FengEdge2000")] FengEdge2000 = 40,

}