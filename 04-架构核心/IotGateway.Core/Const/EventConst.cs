namespace Feng.IotGateway.Core.Const;

/// <summary>
///     事件总线订阅配置
/// </summary>
public class EventConst
{
    /// <summary>
    ///     采集的数据写入到数据库中
    /// </summary>
    public const string SendDeviceData = "SendDeviceData";

    /// <summary>
    ///     设备事件
    /// </summary>
    public const string DeviceEvent = "DeviceEvent";

    /// <summary>
    ///     设备属性元数据
    /// </summary>
    public const string DeviceMeta = "DeviceMeta";
    
    #region 采集线程

    /// <summary>
    ///     创建采集线程
    /// </summary>
    public const string CreateDeviceThread = "CreateDeviceThread";

    /// <summary>
    ///     停止采集线程
    /// </summary>
    public const string StopDeviceThread = "StopDeviceThread";

    /// <summary>
    ///     停止全部采集线程
    /// </summary>
    public const string StopDeviceThreadAll = "StopDeviceThreadAll";

    /// <summary>
    ///     修改采集线程
    /// </summary>
    public const string UpdateDeviceThread = "UpdateDeviceThread";

    /// <summary>
    ///     Cmd命令重启采集线程
    /// </summary>
    public const string CmdReStartDeviceThread = "CmdReStartDeviceThread";
    
    
    #endregion 采集线程
    
    /// <summary>
    /// 系统日志
    /// </summary>
    public const string SysLog = "SysLog";
    
    /// <summary>
    /// 下写标签
    /// </summary>
    public const string WriteShare = "WriteShare";
    
    /// <summary>
    /// 下写属性
    /// </summary>
    public const string WriteVariable = "WriteVariable";
    
    /// <summary>
    /// 下写脚本属性
    /// </summary>
    public const string WriteScriptVariable = "WriteScriptVariable";
    /// <summary>
    /// 删除缓存数据
    /// </summary>
    public const string DeleteCache = "DeleteCache";
    
    /// <summary>
    /// 记录发送成功的数据
    /// </summary>
    public const string PubSuccess = "PubSuccess";
    
    /// <summary>
    ///     采集的数据写入到数据库中
    /// </summary>
    public const string FLinkSendDeviceData = "FLinkSendDeviceData";
}