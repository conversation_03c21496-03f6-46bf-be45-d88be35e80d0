namespace Feng.IotGateway.Core.Const;

/// <summary>
///     设备相关
/// </summary>
public class DeviceConst
{
    /// <summary>
    ///     读取数据类型
    /// </summary>
    public const string ReadDataType = "DataType";

    /// <summary>
    /// </summary>
    public const string Method = "Method";

    /// <summary>
    /// </summary>
    public const string RegisterAddress = "RegisterAddress";

    /// <summary>
    /// </summary>
    public const string ProtectType = "ProtectType";

    /// <summary>
    /// 字符编码
    /// </summary>
    public const string Encoding = "Encoding";
    
    /// <summary>
    /// 读取长度
    /// </summary>
    public const string Length = "Length";
    /// <summary>
    /// </summary>
    public const string ProtectTypeValue = "{\"只读\":\"1\",\"读写\":\"2\",\"只写\":\"3\"}";

    /// <summary>
    /// </summary>
    public const string Read = "{\"只读\":\"1\"}";
    
    /// <summary>
    /// </summary>
    public const string Value = "{\"Read\":\"Read\"}";

    /// <summary>
    /// CNC读取数据类型
    /// </summary>
    public const string CncReadDataTypeValue = "{\"bit\":\"1\",\"bool\":\"2\",\"int16\":\"4\",\"int32\":\"6\",\"double\":\"10\"}";
    
    /// <summary>
    /// 读取数据类型
    /// </summary>
    public const string ReadDataTypeValue =
        "{\"bit\":\"1\",\"bool\":\"2\",\"uint16\":\"3\",\"int16\":\"4\",\"uint32\":\"5\",\"int32\":\"6\",\"float\":\"7\",\"uint64\":\"8\",\"int64\":\"9\",\"double\":\"10\",\"string\":\"11\"}";
    
    /// <summary>
    /// 字符编码
    /// </summary>
    public const string EncodingValue =
        "{\"utf8\":\"1\",\"unicode\":\"2\",\"ascii\":\"3\",\"unicodeBig\":\"4\",\"utf32\":\"5\",\"gb2312\":\"6\"}";
    
    /// <summary>
    /// 带有Bcd专属读取类型
    /// </summary>
    public const string BcdReadDataTypeValue =
        "{\"bit\":\"1\",\"bool\":\"2\",\"uint16\":\"3\",\"int16\":\"4\",\"uint32\":\"5\",\"int32\":\"6\",\"float\":\"7\",\"uint64\":\"8\",\"int64\":\"9\",\"double\":\"10\",\"string\":\"11\",\"bcd\":\"14\",\"bcd-32\":\"15\"}";
    
    /// <summary>
    /// TcpClient读取数据类型
    /// </summary>
    public const string TcpClientReadDataTypeValue = "{\"string\":\"11\",\"byte\":\"12\"}";
    
}