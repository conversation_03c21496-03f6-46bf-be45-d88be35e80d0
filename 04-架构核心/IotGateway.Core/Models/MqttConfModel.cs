namespace Feng.IotGateway.Core.Models;

/// <summary>
///     MQTT配置
/// </summary>
public class MqttConfModel : TransPondBaseModel
{
    /// <summary>
    ///     Ip
    /// </summary>
    public string Ip { get; set; }

    /// <summary>
    ///     以太网端口
    /// </summary>
    public int Port { get; set; }

    /// <summary>
    ///     Mqtt连接用户名
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    ///     Mqtt连接密码
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    ///     连接Id
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    ///     链路保活时间
    /// </summary>
    public int KeepAlive { get; set; }

    /// <summary>
    ///     输出平台
    /// </summary>
    public IoTPlatformType IoTPlatformType { get; set; }

    /// <summary>
    ///     离线数据上报速率：1快速；2一般；3慢速
    /// </summary>
    public OffLinePubSpeedTypeEnum OffLinePubSpeedType { get; set; }

    /// <summary>
    ///     个性化配置扩展表
    /// </summary>
    public List<MqttConfigExtend> MqttConfigExtend { get; set; }
}

/// <summary>
///     输出平台
/// </summary>
public enum IoTPlatformType
{
    /// <summary>
    ///     ELink
    /// </summary>
    [Display(Name = "ELink")] ELink = 1,

    /// <summary>
    ///     SuperOs
    /// </summary>
    [Display(Name = "蓝卓")] SupOS = 2,

    /// <summary>
    ///     RootCloud
    /// </summary>
    [Display(Name = "树根")] RootCloud = 3,

    /// <summary>
    ///     MQTT
    /// </summary>
    [Display(Name = "MQTT")] MQTT = 4,

    /// <summary>
    ///     IotSuite
    /// </summary>
    [Display(Name = "IotSuite")] IotSuite = 5
}

/// <summary>
///     离线数据上报速率：1快速；2一般；3慢速
/// </summary>
public enum OffLinePubSpeedTypeEnum
{
    /// <summary>
    ///     快速
    /// </summary>
    [Display(Name = "快速")] Fast = 1,

    /// <summary>
    ///     一般
    /// </summary>
    [Display(Name = "一般")] Moderate = 2,

    /// <summary>
    ///     慢速
    /// </summary>
    [Display(Name = "慢速")] Slow = 3
}