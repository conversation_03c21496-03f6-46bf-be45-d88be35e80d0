namespace Feng.IotGateway.Core.Models.Networks;

/// <summary>
/// </summary>
public class NetworkInfoModel
{
    /// <summary>
    ///     是否启用
    /// </summary>
    public bool Enable { get; set; } = true;

    /// <summary>
    ///     默认路由名称
    /// </summary>
    public bool DefRoute { get; set; }

    /// <summary>
    ///     固定两种DHCP.STATIC
    /// </summary>
    [Required(ErrorMessage = "请选择网络类型")]
    public NetWorkTypeEnum NetWorkType { get; set; }

    /// <summary>
    /// </summary>
    [Required(ErrorMessage = "网卡名称不能是空")]
    public string NetWorkName { get; set; }

    /// <summary>
    ///     IP地址
    /// </summary>
    public string IPAddress { get; set; }

    /// <summary>
    ///     子掩码
    /// </summary>
    public string SubnetMark { get; set; }

    /// <summary>
    ///     网关
    /// </summary>
    public string? Gateway { get; set; }

    /// <summary>
    ///     Dns
    /// </summary>
    public string Dns { get; set; }

    /// <summary>
    ///     备用Dns
    /// </summary>
    public string DnsBack { get; set; }
}

/// <summary>
///     网络模式 DHCP.STATIC
/// </summary>
public enum NetWorkTypeEnum
{
    /// <summary>
    ///     DHCP
    /// </summary>
    [Description("DHCP")] Dhcp = 1,

    /// <summary>
    ///     STATIC
    /// </summary>
    [Description("STATIC")] Static = 2
}