namespace Feng.IotGateway.Core.Models.Networks;

/// <summary>
///     网络配置
/// </summary>
public class NetworkSettingModel
{
    /// <summary>
    ///     网络
    /// </summary>
    public List<NetworkInfoModel> Network { get; set; }

    /// <summary>
    ///     4G配置
    /// </summary>
    public MobileModel? Mobile { get; set; }
    
    /// <summary>
    ///     Wifi
    /// </summary>
    public WifiModel? Wifi { get; set; } 
}

/// <summary>
///     4G配置
/// </summary>
public class MobileModel
{
    /// <summary>
    ///     默认路由名称
    /// </summary>
    public bool DefRoute { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    public bool Enable { get; set; } = true;

    /// <summary>
    ///     运营商名称
    /// </summary>
    public string OperatorName { get; set; }

    /// <summary>
    ///     网络类型:4G;5G
    /// </summary>
    public int NetworkType { get; set; }

    /// <summary>
    /// </summary>
    public bool IsApn { get; set; }

    /// <summary>
    ///     Apn
    /// </summary>
    public string Apn { get; set; }

    /// <summary>
    ///     用户名
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    public string Password { get; set; }
}

/// <summary>
/// wifi配置
/// </summary>
public class WifiModel
{
    /// <summary>
    ///     用户名
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    ///     加密方式
    /// </summary>
    public string KeyType { get; set; }
    
    /// <summary>
    ///     是否启用
    /// </summary>
    public bool Enable { get; set; } = true;

    /// <summary>
    ///     默认路由名称
    /// </summary>
    public bool DefRoute { get; set; }

    /// <summary>
    ///     固定两种DHCP.STATIC
    /// </summary>
    public NetWorkTypeEnum NetWorkType { get; set; }

    /// <summary>
    /// </summary>
    public string NetWorkName { get; set; }

    /// <summary>
    ///     IP地址
    /// </summary>
    public string IPAddress { get; set; }

    /// <summary>
    ///     子掩码
    /// </summary>
    public string SubnetMark { get; set; }

    /// <summary>
    ///     网关
    /// </summary>
    public string? Gateway { get; set; }

    /// <summary>
    ///     Dns
    /// </summary>
    public string Dns { get; set; }

    /// <summary>
    ///     备用Dns
    /// </summary>
    public string DnsBack { get; set; }
}