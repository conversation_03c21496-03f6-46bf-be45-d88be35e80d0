namespace Feng.IotGateway.Core.Models;

/// <summary>
///     数据库连接配置
/// </summary>
public class SqlConnectModel
{
    /// <summary>
    ///     数据库类型
    /// </summary>
    public int DbType { get; set; }

    /// <summary>
    ///     连接Ip
    /// </summary>
    public string Ip { get; set; }

    /// <summary>
    ///     端口号
    /// </summary>
    public short Port { get; set; }

    /// <summary>
    ///     数据库
    /// </summary>
    public string Database { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    ///     用户
    /// </summary>
    public string Uid { get; set; }

    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }
}