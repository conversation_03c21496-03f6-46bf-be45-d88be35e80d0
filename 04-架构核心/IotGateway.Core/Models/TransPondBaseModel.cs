namespace Feng.IotGateway.Core.Models;

/// <summary>
///     转发-高级配置基类
/// </summary>
public class TransPondBaseModel
{
    /// <summary>
    ///     转发上报类型：1：实时上报;2:周期上报;3:变化上报
    /// </summary>
    public TransPondSendTypeEnum SendType { get; set; }

    /// <summary>
    ///     上报周期
    /// </summary>
    public short PubPeriod { get; set; } = 1000;

    /// <summary>
    ///     上报周期单位(秒,分钟,小时)
    /// </summary>
    public string PubPeriodUnit { get; set; } = "毫秒";
}

/// <summary>
///     转发上报类型：1：实时上报;2:周期上报;3:变化上报
/// </summary>
public enum TransPondSendTypeEnum
{
    /// <summary>
    ///     实时上报
    /// </summary>
    [Description("实时上报")] [Display(Name = "实时上报")]
    Always = 1,

    /// <summary>
    ///     周期上报
    /// </summary>
    [Description("周期上报")] [Display(Name = "周期上报")]
    PubPeriod = 2,

    /// <summary>
    ///     变化上报
    /// </summary>
    [Description("变化上报")] [Display(Name = "变化上报")]
    Changed = 3
}