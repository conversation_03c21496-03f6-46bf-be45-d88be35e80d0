namespace Feng.IotGateway.Core.Models;

/// <summary>
///     Sql配置
/// </summary>
public class SqlConfModel : TransPondBaseModel
{
    /// <summary>
    ///     数据源配置
    /// </summary>
    public SqlConnectModel Config { get; set; }

    /// <summary>
    ///     批量写入
    /// </summary>
    public bool Batch { get; set; }

    /// <summary>
    ///  预览sql-model
    /// </summary>
    public PreviewSqlInput? PreviewSql { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    public string Content { get; set; }
}

/// <summary>
/// 转发扩展方法-预览Sql
/// </summary>
public class PreviewSqlInput
{
    /// <summary>
    /// 表名
    /// </summary>
    public string Table { get; set; }

    /// <summary>
    /// 参数
    /// </summary>
    public List<PreviewSqlValue> Params { get; set; } = new();
}

/// <summary>
/// 
/// </summary>
public class PreviewSqlValue
{
    /// <summary>
    /// 字段名
    /// </summary>
    [Required]
    public string FiledName { get; set; }
    
    /// <summary>
    ///  数据类型：支持:int;string;bool;
    /// </summary>
    [Required]
    public string DataType { get; set; }

    /// <summary>
    /// 参数值
    /// </summary>
    public string Value { get; set; }
}


/// <summary>
///     转发到 OpcUa配置
/// </summary>
public class OpcUaConfModel : TransPondBaseModel
{
    /// <summary>
    ///     数据源配置
    /// </summary>
    public OpcUaConnectModel Config { get; set; }

    /// <summary>
    ///     OpcUa地址映射
    /// </summary>
    public List<OpcUaMapping> OpcUaMappings { get; set; }

    /// <summary>
    ///     发送方式:1地址映射;2脚本解析
    /// </summary>
    public short OpcUaSendType { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    public string Content { get; set; }
}

/// <summary>
///     OpcUa连接需要配置
/// </summary>
public class OpcUaConnectModel
{
    /// <summary>
    ///     连接Url
    /// </summary>
    [Required]
    public string Url { get; set; }
}

/// <summary>
///     地址映射到OpcUa地址中
/// </summary>
public class OpcUaMapping
{
    /// <summary>
    ///     协议名称
    /// </summary>
    public string DriverName { get; set; }

    /// <summary>
    ///     设备Id
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    ///     变量Id
    /// </summary>
    public long DeviceVariableId { get; set; }

    /// <summary>
    ///     地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    ///     OpcUa服务端变量的数据类型
    /// </summary>
    public string OpcUaDataType { get; set; }
}