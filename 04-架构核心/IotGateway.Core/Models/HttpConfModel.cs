namespace Feng.IotGateway.Core.Models;

/// <summary>
///     Http转发配置
/// </summary>
public class HttpConfModel : TransPondBaseModel
{
    /// <summary>
    /// </summary>
    [Required]
    public string Url { get; set; }

    /// <summary>
    ///     请求头
    /// </summary>
    [Required]
    public object Headers { get; set; }

    /// <summary>
    ///     Http请求Header值类型：1.静态参数;2:脚本解析
    /// </summary>
    [Required]
    public HeaderTypeEnum HeaderType { get; set; }

    /// <summary>
    ///     超时时间，默认5秒
    /// </summary>
    public short TimeOut { get; set; } = 5;

    /// <summary>
    /// 请求方式
    /// </summary>
    public HttpTypeEnum HttpType { get; set; }

    /// <summary>
    ///     数据解析规则：1直接发送；2脚本解析
    /// </summary>
    public DataParsingRuleTypeEnum DataParsingRuleType { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    public string Content { get; set; }
}

/// <summary>
///     Http请求Header值类型：1.静态参数;2:脚本解析
/// </summary>
public enum HeaderTypeEnum
{
    [Description("静态参数")] [Display(Name = "静态参数")]
    Static = 1,

    [Description("脚本解析")] [Display(Name = "脚本解析")]
    Script = 2
}

/// <summary>
///     数据解析规则：1直接发送；2脚本解析
/// </summary>
public enum DataParsingRuleTypeEnum
{
    [Description("直接上报")] [Display(Name = "直接上报")]
    DirectSend = 1,

    [Description("脚本解析")] [Display(Name = "脚本解析")]
    Script = 2
}

/// <summary>
///     Http请求
/// </summary>
public enum HttpTypeEnum
{
    [Description("Get")] [Display(Name = "Get")]
    Get = 1,

    [Description("Post")] [Display(Name = "Post")]
    Post = 2
}