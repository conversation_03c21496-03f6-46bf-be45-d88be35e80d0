namespace Feng.IotGateway.Core.Service.User.Dtos;

/// <summary>
///     设置用户状态输入参数
/// </summary>
public class UserInput : BaseId
{
    /// <summary>
    ///     状态
    /// </summary>
    public bool Status { get; set; }
}

/// <summary>
///     获取用户分页列表输入参数
/// </summary>
public class PageUserInput : BasePageInput
{
    /// <summary>
    ///     账号
    /// </summary>
    public string Account { get; set; }

    /// <summary>
    ///     姓名
    /// </summary>
    public string Name { get; set; }
}

/// <summary>
///     增加用户输入参数
/// </summary>
public class AddUserInput
{
    /// <summary>
    ///     账号
    /// </summary>
    [Required(ErrorMessage = "账号不能为空")]
    public string Account { get; set; }

    /// <summary>
    ///     真实姓名
    /// </summary>
    [Required(ErrorMessage = "真实姓名不能为空")]
    public string Name { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    public string Password { get; set; }
    
    /// <summary>
    /// 工号
    /// </summary>
    /// <returns></returns>
    public string? JobNumber { get; set; }

    /// <summary>
    ///     头像
    /// </summary>
    public string? Avatar { get; set; }
    
}

/// <summary>
///     更新用户输入参数
/// </summary>
public class UpdateUserInput : AddUserInput
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public long Id { get; set; }
}