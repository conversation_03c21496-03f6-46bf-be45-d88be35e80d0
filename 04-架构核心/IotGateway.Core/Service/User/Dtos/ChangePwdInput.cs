namespace Feng.IotGateway.Core.Service.User.Dtos;

/// <summary>
/// 修改密码
/// </summary>
public class ChangePwdInput
{
    /// <summary>
    /// 用户Id
    /// </summary>
    [Required]
    public long Id { get; set; }
    
    /// <summary>
    /// 当前密码
    /// </summary>
    [Required(ErrorMessage = "当前密码不能为空")]
    public string PasswordOld { get; set; }

    /// <summary>
    /// 新密码
    /// </summary>
    [Required(ErrorMessage = "原始密码不能为空")]
    [StringLength(20, MinimumLength = 5, ErrorMessage = "密码需要大于5个字符")]
    public string PasswordNew { get; set; }
}