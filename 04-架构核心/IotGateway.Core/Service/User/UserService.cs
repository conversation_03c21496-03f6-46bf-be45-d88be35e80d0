using Common.Extension;
using Feng.Common.Extension;
using Feng.IotGateway.Core.Service.User.Dtos;

namespace Feng.IotGateway.Core.Service.User;

/// <summary>
///     用户服务
/// </summary>
[ApiDescriptionSettings("系统Api", Order = 410)]
public class UserService : IDynamic<PERSON>pi<PERSON>ontroller, ITransient
{
    private readonly SqlSugarRepository<SysUser> _sysUserRep;

    /// <summary>
    /// </summary>
    /// <param name="sysUserRep"></param>
    public UserService(SqlSugarRepository<SysUser> sysUserRep)
    {
        _sysUserRep = sysUserRep;
    }

    /// <summary>
    ///     修改用户密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/user/setPwd")]
    public async Task<int> ChangeUserPwd(ChangePwdInput input)
    {
        var user = await _sysUserRep.GetFirstAsync(u => u.Id == input.Id);
        if (MD5Encryption.Encrypt(input.PasswordOld) != user.Password)
            throw Oops.Oh("旧密码不匹配");
        user.Password = MD5Encryption.Encrypt(input.PasswordNew);
        return await _sysUserRep.AsUpdateable(user).UpdateColumns(u => u.Password).ExecuteCommandAsync();
    }

    /// <summary>
    ///     重置用户密码
    /// </summary>
    /// <returns></returns>
    [HttpPost("/user/resetPwd")]
    public async Task<int> ResetUserPwd([FromBody]BaseId input)
    {
        var user = await _sysUserRep.GetFirstAsync(u => u.Id == input.Id);
        user.Password = MD5Encryption.Encrypt(CommonConst.DefaultPassword);
        return await _sysUserRep.AsUpdateable(user).UpdateColumns(u => u.Password).ExecuteCommandAsync();
    }

    /// <summary>
    ///     获取用户分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/user/page")]
    public virtual async Task<SqlSugarPagedList<SysUser>> Page([FromQuery] PageUserInput input)
    {
        return await _sysUserRep.AsQueryable()
            .Where(u => u.AdminType != AdminTypeEnum.SuperAdmin)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Account), u => u.Account.Contains(input.Account))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name))
            .OrderBy(u => new {u.Id})
            .ToPagedListAsync(input.PageNo, input.PageSize);
    }

    /// <summary>
    ///     增加用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/user/add")]
    public virtual async Task<long> AddUser(AddUserInput input)
    {
        var isExist = await _sysUserRep.AsQueryable().ClearFilter().AnyAsync(u => u.Account == input.Account);
        if (isExist) throw Oops.Oh("账号已存在");
        var user = input.Adapt<SysUser>();
        if (input.Password.IsNull())
            user.Password = MD5Encryption.Encrypt(CommonConst.DefaultPassword);
        var newUser = await _sysUserRep.AsInsertable(user).ExecuteReturnEntityAsync();
        return newUser.Id;
    }

    /// <summary>
    ///     更新用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/user/update")]
    public virtual async Task UpdateUser(UpdateUserInput input)
    {
        if (await _sysUserRep.AsQueryable().ClearFilter().AnyAsync(u => u.Account == input.Account && u.Id != input.Id))
            throw Oops.Oh("账号已存在");

        await _sysUserRep.AsUpdateable(input.Adapt<SysUser>()).IgnoreColumns(true)
            .IgnoreColumns(u => new {u.Password, u.Status}).ExecuteCommandAsync();
    }

    /// <summary>
    ///     删除用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/user/delete")]
    public virtual async Task DeleteUser(BaseId input)
    {
        var user = await _sysUserRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh("账号不存在");
        if (user.AdminType == AdminTypeEnum.SuperAdmin)
            throw Oops.Oh("禁止删除超级管理员");

        await _sysUserRep.DeleteAsync(user);
    }
}