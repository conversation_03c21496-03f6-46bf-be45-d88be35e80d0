namespace Feng.IotGateway.Core.Service.User;

/// <summary>
///     用户管理
/// </summary>
public class UserManager : IScoped
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly SqlSugarRepository<SysUser> _sysUserRep; // 用户表仓储

    /// <summary>
    /// </summary>
    /// <param name="sysUserRep"></param>
    /// <param name="httpContextAccessor"></param>
    public UserManager(SqlSugarRepository<SysUser> sysUserRep, IHttpContextAccessor httpContextAccessor)
    {
        _sysUserRep = sysUserRep;
        _httpContextAccessor = httpContextAccessor;
    }

    /// <summary>
    ///     用户Id
    /// </summary>
    public long UserId => long.Parse(_httpContextAccessor.HttpContext.User.FindFirst(ClaimConst.UserId)?.Value);

    /// <summary>
    ///     用户账号
    /// </summary>
    public string Account => _httpContextAccessor.HttpContext.User.FindFirst(ClaimConst.Account)?.Value;

    /// <summary>
    ///     用户名称
    /// </summary>
    public string Name => _httpContextAccessor.HttpContext.User.FindFirst(ClaimConst.UserName)?.Value;

    /// <summary>
    ///     超级管理员
    /// </summary>
    public bool SuperAdmin => _httpContextAccessor.HttpContext.User.FindFirst(ClaimConst.SuperAdmin)?.Value == ((int) AdminTypeEnum.SuperAdmin).ToString();

    /// <summary>
    ///     用户信息
    /// </summary>
    public SysUser User => _sysUserRep.GetFirst(u => u.Id == UserId);

    /// <summary>
    ///     获取用户信息
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    public async Task<SysUser> CheckUserAsync(long userId)
    {
        var user = await _sysUserRep.GetFirstAsync(u => u.Id == userId);
        return user ?? throw Oops.Oh(ErrorCode.D1002);
    }
}