using Feng.IotGateway.Core.Base;
using Feng.IotGateway.Core.Service.Dict.Dto;
using Feng.IotGateway.Core.Service.User;
using Microsoft.AspNetCore.Authorization;

namespace Feng.IotGateway.Core.Service.Dict;

/// <summary>
///     字典类型服务
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2022-10-01
/// </summary>
[ApiDescriptionSettings("系统Api", Order = 410)]
public class DictTypeService : IDynamicApiController, ITransient
{
    private readonly DictDataService _sysDictDataService;
    private readonly SqlSugarRepository<SysDictType> _sysDictTypeRep; // 字典类型表仓储

    /// <summary>
    /// </summary>
    /// <param name="sysDictDataService"></param>
    /// <param name="sysDictTypeRep"></param>
    /// <param name="userManager"></param>
    public DictTypeService(DictDataService sysDictDataService,
        SqlSugarRepository<SysDictType> sysDictTypeRep,
        UserManager userManager)
    {
        _sysDictDataService = sysDictDataService;
        _sysDictTypeRep = sysDictTypeRep;
    }

    /// <summary>
    ///     分页查询字典类型
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sysDictType/page")]
    public async Task<SqlSugarPagedList<SysDictType>> QueryDictTypePageList([FromQuery] DictTypePageInput input)
    {
        var dictTypes = await _sysDictTypeRep.AsQueryable()
            .WhereIF(!string.IsNullOrEmpty(input.Code?.Trim()), w => w.Code.Contains(input.Code.Trim()))
            .WhereIF(!string.IsNullOrEmpty(input.Name?.Trim()), w => w.Name.Contains(input.Name.Trim()))
            .Where(u => u.Enable == true).OrderBy(u => u.Sort)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return dictTypes;
    }

    /// <summary>
    ///     获取字典类型列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sysDictType/list")]
    public async Task<List<SysDictType>> GetDictTypeList()
    {
        return await _sysDictTypeRep.AsQueryable().Where(u => u.Enable != false).ToListAsync();
    }

    /// <summary>
    ///     获取字典类型下所有字典值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpGet("/sysDictType/dropDown")]
    public async Task<List<SysDictData>> GetDictTypeDropDown([FromQuery] DropDownDictTypeInput input)
    {
        var dictType = await _sysDictTypeRep.GetFirstAsync(u => u.Code == input.Code);
        if (dictType == null) throw Oops.Oh(ErrorCode.D3000);
        return await _sysDictDataService.GetDictDataListByDictTypeId(dictType.Id);
    }

    /// <summary>
    ///     添加字典类型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysDictType/add")]
    public async Task AddDictType(AddDictTypeInput input)
    {
        var isExist = await _sysDictTypeRep.IsAnyAsync(u => u.Name == input.Name || u.Code == input.Code);
        if (isExist) throw Oops.Oh(ErrorCode.D3001);

        var dictType = input.Adapt<SysDictType>();
        await _sysDictTypeRep.InsertAsync(dictType);
    }

    /// <summary>
    ///     删除字典类型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysDictType/delete")]
    public async Task DeleteDictType(DeleteDictTypeInput input)
    {
        var dictType = await _sysDictTypeRep.GetFirstAsync(u => u.Id == input.Id);
        if (dictType == null) throw Oops.Oh(ErrorCode.D3000);
        await _sysDictTypeRep.DeleteAsync(dictType);
    }

    /// <summary>
    ///     更新字典类型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysDictType/edit")]
    public async Task UpdateDictType(UpdateDictTypeInput input)
    {
        var isExist = await _sysDictTypeRep.IsAnyAsync(u => u.Id == input.Id);
        if (!isExist) throw Oops.Oh(ErrorCode.D3000);

        // 排除自己并且判断与其他是否相同
        isExist = await _sysDictTypeRep.IsAnyAsync(u => (u.Name == input.Name || u.Code == input.Code) && u.Id != input.Id);
        if (isExist) throw Oops.Oh(ErrorCode.D3001);

        var dictType = input.Adapt<SysDictType>();
        await _sysDictTypeRep.UpdateAsync(dictType);
    }

    /// <summary>
    ///     字典类型详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysDictType/detail")]
    public async Task<SysDictType> GetDictType([FromQuery] QueryDictTypeInfoInput input)
    {
        return await _sysDictTypeRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    ///     更新字典类型状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysDictType/changeStatus")]
    public async Task ChangeDictTypeStatus(EnableInput<long> input)
    {
        var dictType = await _sysDictTypeRep.GetFirstAsync(u => u.Id == input.Id);
        if (dictType == null) throw Oops.Oh(ErrorCode.D3000);

        dictType.Enable = input.Enable;
    }

    // /// <summary>
    // ///     字典类型与字典值构造的字典树
    // /// </summary>
    // /// <returns></returns>
    // [AllowAnonymous]
    // [HttpGet("/sysDictType/tree")]
    // public async Task<List<DictTreeOutput>> GetDictTree()
    // {
    //     return await _sysDictTypeRep.AsQueryable().Select(u => new DictTreeOutput
    //     {
    //         Id = u.Id,
    //         Code = u.Code,
    //         Name = u.Name,
    //         Children = u.SysDictDatas.Select(c => new DictTreeOutput
    //         {
    //             Id = c.Id,
    //             Pid = c.DictTypeId,
    //             Code = c.Code,
    //             Name = c.Value
    //         }).ToList()
    //     }).ToListAsync();
    // }
}