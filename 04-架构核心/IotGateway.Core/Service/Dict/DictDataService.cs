using Feng.IotGateway.Core.Base;
using Feng.IotGateway.Core.Service.Dict.Dto;

namespace Feng.IotGateway.Core.Service.Dict;

/// <summary>
///     字典值服务
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2022-10-01
/// </summary>
[ApiDescriptionSettings("系统Api", Order = 410)]
public class DictDataService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<SysDictData> _sysDictDataRep; // 字典类型表仓储

    /// <summary>
    /// </summary>
    /// <param name="sysDictDataRep"></param>
    public DictDataService(SqlSugarRepository<SysDictData> sysDictDataRep)
    {
        _sysDictDataRep = sysDictDataRep;
    }

    /// <summary>
    ///     分页查询字典值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysDictData/page")]
    public async Task<SqlSugarPagedList<DictDataOutput>> QueryDictDataPageList([FromQuery] DictDataPageInput input)
    {
        var dictDatas = await _sysDictDataRep.AsQueryable()
            .Where(u => u.DictTypeId == input.TypeId)
            .WhereIF(!string.IsNullOrEmpty(input.Code?.Trim()), u => u.Code.Contains(input.Code.Trim()))
            .WhereIF(!string.IsNullOrEmpty(input.Value?.Trim()), u => u.Code.Contains(input.Value.Trim()))
            .Select(s => s.Adapt<DictDataOutput>())
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return dictDatas;
    }

    /// <summary>
    ///     获取某个字典类型下字典值列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sysDictData/list")]
    public async Task<List<SysDictData>> GetDictDataList([FromQuery] QueryDictDataListInput input)
    {
        return await _sysDictDataRep.AsQueryable().Where(u => u.DictTypeId == input.TypeId)
            .ToListAsync();
    }

    /// <summary>
    ///     增加字典值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysDictData/add")]
    public async Task AddDictData(AddDictDataInput input)
    {
        var isExist = await _sysDictDataRep.IsAnyAsync(u => (u.Code == input.Code || u.Value == input.Value) && u.DictTypeId == input.TypeId);
        if (isExist) throw Oops.Oh(ErrorCode.D3003);

        var dictData = input.Adapt<SysDictData>();
        await _sysDictDataRep.InsertAsync(dictData);
    }

    /// <summary>
    ///     删除字典值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysDictData/delete")]
    public async Task DeleteDictData(BaseId input)
    {
        var dictData = await _sysDictDataRep.GetFirstAsync(u => u.Id == input.Id);
        if (dictData == null) throw Oops.Oh(ErrorCode.D3004);
        await _sysDictDataRep.DeleteAsync(dictData);
    }

    /// <summary>
    ///     更新字典值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysDictData/edit")]
    public async Task UpdateDictData(UpdateDictDataInput input)
    {
        var isExist = await _sysDictDataRep.IsAnyAsync(u => u.Id == input.Id);
        if (!isExist) throw Oops.Oh(ErrorCode.D3004);

        // 排除自己并且判断与其他是否相同
        isExist = await _sysDictDataRep.IsAnyAsync(u => (u.Value == input.Value || u.Code == input.Code) && u.DictTypeId == input.TypeId && u.Id != input.Id);
        if (isExist) throw Oops.Oh(ErrorCode.D3003);

        var dictData = input.Adapt<SysDictData>();
        await _sysDictDataRep.UpdateAsync(dictData);
    }

    /// <summary>
    ///     字典值详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysDictData/detail")]
    public async Task<SysDictData> GetDictData([FromQuery] QueryDictDataInput input)
    {
        return await _sysDictDataRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    ///     修改字典值状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysDictData/changeStatus")]
    public async Task ChangeDictDataStatus(EnableInput<long> input)
    {
        var dictData = await _sysDictDataRep.GetFirstAsync(u => u.Id == input.Id);
        if (dictData == null) throw Oops.Oh(ErrorCode.D3004);
    }

    /// <summary>
    ///     根据字典类型Id获取字典值集合
    /// </summary>
    /// <param name="dictTypeId"></param>
    /// <returns></returns>
    [NonAction]
    public async Task<List<SysDictData>> GetDictDataListByDictTypeId(long dictTypeId)
    {
        return await _sysDictDataRep.AsQueryable().Where(u => u.DictTypeId == dictTypeId)
            .ToListAsync();
    }

    /// <summary>
    ///     删除字典下所有值
    /// </summary>
    /// <param name="dictTypeId"></param>
    [NonAction]
    public async Task DeleteByTypeId(long dictTypeId)
    {
        var dictDatas = await _sysDictDataRep.AsQueryable().Where(u => u.DictTypeId == dictTypeId).ToListAsync();
        await _sysDictDataRep.DeleteAsync(dictDatas);
    }
}