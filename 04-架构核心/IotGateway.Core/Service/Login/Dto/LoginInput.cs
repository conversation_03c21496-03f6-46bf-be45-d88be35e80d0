using System.Text.Json.Serialization;
using Newtonsoft.Json;

namespace Feng.IotGateway.Core.Service.Login.Dto;

/// <summary>
///     登录输入参数
/// </summary>
public class LoginInput
{
    /// <summary>
    ///     用户名
    /// </summary>
    /// <example>superAdmin</example>
    [Required(ErrorMessage = "用户名不能为空")]
    [MinLength(5, ErrorMessage = "用户名不能少于5位字符")]
    public string Account { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    /// <example>c246266016e1e235aa62adf75ea29e18</example>
    [Required(ErrorMessage = "密码不能为空")]
    [MinLength(5, ErrorMessage = "密码不能少于5位字符")]
    public string Password { get; set; }
}

public class GetTokenInput
{
    [Required]
    [JsonPropertyName("authorization")]
    public string Authorization { get; set; }
}