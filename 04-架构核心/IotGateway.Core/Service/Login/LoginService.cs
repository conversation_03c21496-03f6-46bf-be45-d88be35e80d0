using Common.Extension;
using Feng.Common.Extension;
using Feng.IotGateway.Core.Service.Login.Dto;
using Furion.SpecificationDocument;
using Microsoft.AspNetCore.Authorization;
using DateTime = System.DateTime;

namespace Feng.IotGateway.Core.Service.Login;

/// <summary>
///     登录授权相关服务
///     版 本:V3.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2022-10-01
/// </summary>
[ApiDescriptionSettings("系统Api", Order = 1)]
public class LoginService : IDynamicApiController, ITransient
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly SqlSugarRepository<SysUser> _sysUserRep; // 用户表仓储
    private readonly UserManager _userManager; // 用户管理

    /// <summary>
    /// </summary>
    /// <param name="sysUserRep"></param>
    /// <param name="httpContextAccessor"></param>
    /// <param name="userManager"></param>
    public LoginService(SqlSugarRepository<SysUser> sysUserRep, IHttpContextAccessor httpContextAccessor, UserManager userManager)
    {
        _sysUserRep = sysUserRep;
        _httpContextAccessor = httpContextAccessor;
        _userManager = userManager;
    }

    /// <summary>
    ///     用户登录
    /// </summary>
    /// <param name="input"></param>
    /// <remarks>默认用户名/密码:admin/admin</remarks>
    /// <returns></returns>
    [HttpPost("/login")]
    [AllowAnonymous]
    public async Task<string> LoginAsync([Required] LoginInput input)
    {
        // 判断用户名和密码是否正确 忽略全局过滤器
        var user = await _sysUserRep.GetFirstAsync(u => u.Account.Equals(input.Account) && u.Password.Equals(input.Password));
        _ = user ?? throw Oops.Oh(ErrorCode.D1000);

        // 验证账号是否被冻结
        if (!user.Status)
            throw Oops.Oh(ErrorCode.D1017);

        var endTime = App.Configuration["JWTSettings:ExpiredTime"];
        var expiredTime = Convert.ToInt32(endTime);
        // 生成Token令牌
        //var accessToken = await _jwtBearerManager.CreateTokenAdmin(user);
        var accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
        {
            { ClaimConst.UserId, user.Id },
            { ClaimConst.Account, user.Account },
            { ClaimConst.UserName, user.Name },
            { ClaimConst.SuperAdmin, user.AdminType }
        }, expiredTime);

        // 设置Swagger自动登录
        _httpContextAccessor.HttpContext.SigninToSwagger(accessToken);
        // 生成刷新Token令牌
        var refExpiredTime = Convert.ToInt32(App.Configuration["RefreshToken:ExpiredTime"]);
        var refreshToken = JWTEncryption.GenerateRefreshToken(accessToken, refExpiredTime);
        // 设置刷新Token令牌
        if (_httpContextAccessor.HttpContext == null) return accessToken;
        _httpContextAccessor.HttpContext.Response.Headers["access-token"] = accessToken;
        _httpContextAccessor.HttpContext.Response.Headers["x-access-token"] = refreshToken;

        return accessToken;
    }

    /// <summary>
    ///     获取当前登录用户信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("/getLoginUser")]
    public async Task<SysUser> GetLoginUserAsync()
    {
        try
        {
            var user = await _userManager.CheckUserAsync(_userManager.UserId);
            if (user == null)
                throw Oops.Oh(ErrorCode.D1011);
            return user;
        }
        catch
        {
            throw Oops.Oh("登录已过期");
        }
    }

    /// <summary>
    ///     获取Token
    /// </summary>
    /// <returns></returns>
    [HttpGet("/getToken")]
    [AllowAnonymous]
    public async Task<string> GetToken([FromQuery] GetTokenInput input)
    {
        Log.Warning(input.Authorization);
        var key = "b2252bd356842342"; // 16个字符的密钥
        var encryptedBytes = Convert.FromBase64String(input.Authorization);
        var authorization = StringExtension.DecryptStringFromBytes_Aes(encryptedBytes, Encoding.UTF8.GetBytes(key));
        if (string.IsNullOrEmpty(authorization))
            throw Oops.Oh("401 Error: Unauthorized").StatusCode(401);
        var tokenInfo = JWTEncryption.ReadJwtToken(authorization);
        if (DateTime.Now > tokenInfo.ValidTo)
            throw Oops.Oh("401 Error: Unauthorized").StatusCode(401);
        var user = await _sysUserRep.GetFirstAsync(u => u.Account.Equals("admin"));
        if (user == null)
            throw Oops.Oh("401 Error: Unauthorized").StatusCode(401);
        return await LoginAsync(new LoginInput
        {
            Account = user.Account,
            Password = user.Password
        });
    }

    /// <summary>
    ///     退出
    /// </summary>
    /// <returns></returns>
    [HttpPost("/logout")]
    [AllowAnonymous]
    public Task LogoutAsync()
    {
        _httpContextAccessor.HttpContext.SignoutToSwagger();
        _httpContextAccessor.HttpContext?.Response.Headers.Remove("access-token");
        return Task.CompletedTask;
    }

    /// <summary>
    ///     Swagger登录检查
    /// </summary>
    /// <returns></returns>
    [HttpPost("/Swagger/CheckUrl")]
    [NonUnify]
    [AllowAnonymous]
    public async Task<int> SwaggerCheckUrl()
    {
        try
        {
            return _httpContextAccessor.HttpContext.User.Identity.IsAuthenticated ? 200 : 401;
        }
        catch
        {
            return 200;
        }
    }

    /// <summary>
    ///     Swagger登录
    /// </summary>
    /// <param name="auth"></param>
    /// <returns></returns>
    [HttpPost("/Swagger/SubmitUrl")]
    [NonUnify]
    [AllowAnonymous]
    public async Task<int> SwaggerSubmitUrl([FromForm] SpecificationAuth auth)
    {
        await LoginAsync(new LoginInput
        {
            Account = auth.UserName,
            Password = MD5Encryption.Encrypt(auth.Password)
        });
        return 200;
    }
}