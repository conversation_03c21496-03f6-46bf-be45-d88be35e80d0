using System.IO;
using Common.Enums;
using Common.Extension;
using Feng.Common.Extension;
using Feng.Common.Util;
using Feng.IotGateway.Core.Base;
using Feng.IotGateway.Core.Models;
using Feng.IotGateway.Core.Models.Shells;
using Feng.IotGateway.Core.Service.Cache;
using Feng.IotGateway.Core.Util;
using Furion.EventBus;
using Furion.TaskQueue;
using Microsoft.AspNetCore.Authorization;
using Yitter.IdGenerator;
using DateTime = System.DateTime;

namespace Feng.IotGateway.Core.Service;

/// <summary>
///     公共方法
///     版 本:V3.1.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2022-11-23
/// </summary>
[ApiDescriptionSettings("系统Api", Order = 410)]
public class CommonService : IDynamicApiController, ITransient
{
    private readonly IEventPublisher _eventPublisher;
    private readonly SqlSugarRepository<SysConfig> _deviceConfig;
    
    public CommonService(IEventPublisher eventPublisher, SqlSugarRepository<SysConfig> deviceConfig)
    {
        _eventPublisher = eventPublisher;
        _deviceConfig = deviceConfig;
    }
    
    

    /// <summary>
    ///     生成雪花Id
    /// </summary>
    /// <returns></returns>
    [HttpGet("/common/getId")]
    public Task<long> GetId()
    {
        return Task.FromResult(YitIdHelper.NextId());
    }

    /// <summary>
    ///     设备属性类型枚举
    /// </summary>
    /// <returns></returns>
    [HttpGet("/common/dataTypeEnum")]
    public Task<Dictionary<string, int>> GetDataTypeEnum()
    {
        var dictionary = Enum.GetValues(typeof(DataTypeEnum))
            .Cast<DataTypeEnum>()
            .ToDictionary(k => k.ToString(), v => (int) v);
        return Task.FromResult(dictionary);
    }
    
    /// <summary>
    ///     设备属性类型枚举
    ///  该方法被弃用，直接使用内部方法
    /// </summary>
    /// <returns></returns>
    [HttpGet("/common/transPondDataTypeEnum")]
    public Task<Dictionary<string, int>> GetTransPondDataTypeEnum()
    {
        // var dictionary = Enum.GetValues(typeof(TransPondDataTypeEnum))
        //     .Cast<TransPondDataTypeEnum>()
        //     .ToDictionary(k => k.ToString(), v => (int) v);
        // return Task.FromResult(dictionary);

        return GetDataTypeEnum();
    }
    
    /// <summary>
    ///     任务配置-获取最近5次执行时间
    /// </summary>
    /// <param name="cron"></param>
    /// <returns></returns>
    [HttpGet("/sysTimers/getNextTime")]
    public Task<List<string>> GetNextTime([FromQuery] string cron)
    {
        var crontab = Crontab.Parse(StringExtension.ConvertCron(cron), CronStringFormat.WithSeconds);
        var nextTime = crontab.GetNextOccurrence(DateTime.Now);
        var list = new List<string>();
        for (var i = 0; i < 5; i++)
        {
            var time2 = crontab.GetNextOccurrence(nextTime);
            list.Add(time2.ToString("yyyy-MM-dd HH:mm:ss"));
            nextTime = time2;
        }

        return Task.FromResult(list);
    }
    
    /// <summary>
    ///     平台
    /// </summary>
    /// <returns></returns>
    [HttpGet("/common/platform")]
    [AllowAnonymous]
    public async Task<dynamic> GetPlatform()
    {
        var logo = GlobalConfigManager.GetConfigValue(ConfigConst.Platform);
        if (string.IsNullOrEmpty(logo))
        {
            var config = await _deviceConfig.CopyNew().AsQueryable().FirstAsync(w => w.Code == ConfigConst.Platform);
            if (config != null)
            {
                logo = config.Value;
                GlobalConfigManager.SetConfigValue(ConfigConst.Platform, logo);
            }
        }

        return new
        {
            Platform = logo,
            CurrentSystem = EnumUtil.GetEnumDesc(MachineUtil.CurrentSystemEnvironment)
        };
    }

    /// <summary>
    ///     手动设置当前时间
    /// </summary>
    /// <returns></returns>
    [HttpPost("/time/set")]
    public async Task SetTime(SetTimeInput input)
    {
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            await ShellUtil.Bash($"date -s \"{input.Time:yyyy-MM-dd HH:mm:ss}\"");
            await ShellUtil.Bash("hwclock -wu");
        }
    }
    private const string BasePath = "/etc/DeviceConf/";
    
    /// <summary>
    ///     修改别名
    /// </summary>
    /// <returns></returns>
    [HttpPost("/authorization/setIdent")]
    public async Task SetIdent([FromBody] BaseId<string> input)
    {
        await File.WriteAllTextAsync(BasePath + "Ident.txt", input.Id);
    }
    
    /// <summary>
    ///     Ping
    /// </summary>
    /// <returns></returns>
    [HttpGet("/shell/ping")]
    public Task<bool> Ping([FromQuery] string ip)
    {
        var pingSender = new Ping();
        var timeOut = 1000;
        var reply = pingSender.Send(ip, timeOut);
        return Task.FromResult(reply!.Status == IPStatus.Success);
    }
    
    /// <summary>
    ///     重启硬件
    /// </summary>
    /// <returns></returns>
    [HttpPost("/shell/reboot")]
    public async Task Reboot()
    {
        //停止全部采集线程
        await _eventPublisher.PublishAsync(EventConst.StopDeviceThreadAll, "");
        await TaskQueued.EnqueueAsync(async (provider, token) => { await ShellUtil.Bash("reboot"); }, 5000);
    }
    
    /// <summary>
    ///     Ping指定网卡
    /// </summary>
    /// <returns></returns>
    [HttpGet("/shell/pingByNetwork")]
    public async Task<List<string>> PingByNetWork([FromQuery] PingInput input)
    {
        var output = await ShellUtil.Bash($"ping -c 4 -I {input.NetworkName} {input.Ip}");
        var outputValue = output.Trim().Split("\n");
        var log = new List<string>();
        log.AddRange(outputValue);
        return log;
    }
    /// <summary>
    ///     Telnet
    /// </summary>
    /// <returns></returns>
    [HttpGet("/shell/telnet")]
    public async Task<string> Telnet([FromQuery] TelnetInput input)
    {
        var output = await ShellUtil.Bash($"nc -z {input.Ip}  && echo ok || echo no");
        return output.Trim();
    }
    
    /// <summary>
    ///     Route
    /// </summary>
    /// <returns></returns>
    [HttpGet("/shell/route")]
    public async Task<List<RouteOutput>> Route()
    {
        var routeList = await ShellUtil.Bash("route -n");
        var lines = routeList.Split('\n');
        var routeModels = (from item in lines
            where !item.Contains("Kernel IP routing table")
            where !item.Contains("Destination")
            select item.Split(new[] {' '}, StringSplitOptions.RemoveEmptyEntries)
            into li
            where li.Length >= 7
            select new RouteOutput
            {
                Destination = li[0],
                Gateway = li[1],
                Genmask = li[2],
                Flags = li[3],
                Metric = li[4],
                Ref = li[5],
                Use = li[6],
                Iface = li[7]
            }).ToList();
        return routeModels;
    }
}