using MQTTnet;
using MQTTnet.Client;

namespace Feng.IotGateway.Core.Service;

/// <summary>
///     ManageGatewayWorker
/// </summary>
public class ManageGatewayWorker : BackgroundService
{
    private readonly ILogger _logger;

    private IMqttClient _mqttClient;

    private MqttClientSubscribeOptions _mqttSubscribeOptions;

    /// <inheritdoc cref="ManageGatewayWorker" />
    public ManageGatewayWorker(ILoggerFactory loggerFactory)
    {
        _logger = loggerFactory.CreateLogger("GatewayWorker");
    }

    /// <summary>
    ///     服务状态
    /// </summary>
    public bool ClientStatus { get; set; }


    #region worker服务

    /// <inheritdoc />
    public override async Task StartAsync(CancellationToken token)
    {
        _logger?.LogInformation("GatewayWorker启动");
        await RestartAsync();
        await base.StartAsync(token);
    }

    /// <inheritdoc />
    public override async Task StopAsync(CancellationToken token)
    {
        _logger?.LogInformation("GatewayWorker停止");
        await StopAsync();
        await base.StopAsync(token);
    }

    /// <inheritdoc />
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
            try
            {
                if (_mqttClient != null)
                {
                    //持续重连
                    ClientStatus = await TryMqttClientAsync(stoppingToken);
                    if (ClientStatus)
                        _logger.LogDebug("连接正常");
                    else
                        _logger.LogWarning("连接错误");
                }

                await Task.Delay(10000, stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ToString());
            }
    }

    #endregion

    #region public

    /// <summary>
    ///     重启
    /// </summary>
    /// <returns></returns>
    public async Task RestartAsync()
    {
        await StopAsync();
        await StartAsync();
    }

    /// <summary>
    ///     下载配置信息到网关
    /// </summary>
    /// <returns></returns>
    public async Task<bool> SetClientGatewayDBAsync(string gatewayId, int timeOut = 3000, CancellationToken token = default)
    {
        try
        {
            // var buffer = Encoding.UTF8.GetBytes(mqttDBRpc?.ToJsonString() ?? string.Empty);
            // var response = await RpcDataExecuteAsync(gatewayId, ClientGatewayConfig.DBDownTopic, buffer, timeOut, MqttQualityOfServiceLevel.AtMostOnce, token);
            // var data = Encoding.UTF8.GetString(response).FromJsonString<OperResult>();
            // return data;
            return false;
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    #endregion

    #region 核心实现

    internal async Task StartAsync()
    {
        try
        {
            await InitAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动错误");
        }
    }

    internal async Task StopAsync()
    {
        try
        {
            _mqttClient?.Dispose();
            _mqttClient = null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止错误");
        }
    }

    /// <summary>
    ///     初始化
    /// </summary>
    private async Task InitAsync()
    {
        try
        {
            var mqttFactory = new MqttFactory();
            // _mqttClientOptions = mqttFactory.CreateClientOptionsBuilder()
            //     .WithCredentials(ClientGatewayConfig.UserName, ClientGatewayConfig.Password) //账密
            //     .WithTcpServer(ClientGatewayConfig.MqttBrokerIP, ClientGatewayConfig.MqttBrokerPort) //服务器
            //     .WithClientId(ClientGatewayConfig.GatewayId)
            //     .WithCleanSession()
            //     .WithKeepAlivePeriod(TimeSpan.FromSeconds(120.0))
            //     .Build();
            _mqttSubscribeOptions = mqttFactory.CreateSubscribeOptionsBuilder()
                .Build();
            _mqttClient = mqttFactory.CreateMqttClient();
            _mqttClient.ConnectedAsync += MqttClient_ConnectedAsync;
            _mqttClient.ApplicationMessageReceivedAsync += MqttClient_ApplicationMessageReceivedAsync;
            await TryMqttClientAsync(CancellationToken.None);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化失败");
        }
    }

    private MqttClientOptions _mqttClientOptions;

    private async Task MqttClient_ApplicationMessageReceivedAsync(MqttApplicationMessageReceivedEventArgs args)
    {
    }

    private async Task MqttClient_ConnectedAsync(MqttClientConnectedEventArgs args)
    {
        var subResult = await _mqttClient.SubscribeAsync(_mqttSubscribeOptions);
    }

    private async Task<bool> TryMqttClientAsync(CancellationToken token)
    {
        if (_mqttClient?.IsConnected == true)
            return true;
        return await Cilent();

        async Task<bool> Cilent()
        {
            if (_mqttClient?.IsConnected == true)
                return true;
            try
            {
                if (_mqttClient?.IsConnected == true)
                    return true;
                using var timeoutToken = new CancellationTokenSource(TimeSpan.FromMilliseconds(5000));
                using var stoppingToken = CancellationTokenSource.CreateLinkedTokenSource(token, timeoutToken.Token);
                if (_mqttClient?.IsConnected == true)
                    return true;
                if (_mqttClient == null) return false;
                var result = await _mqttClient.ConnectAsync(_mqttClientOptions, stoppingToken.Token);
                return result is {ResultCode: MqttClientConnectResultCode.Success};
            }
            catch (Exception ex)
            {
                throw Oops.Oh(ex.Message);
            }
        }
    }

    #endregion
}