namespace Feng.IotGateway.Core.Service.Config;

/// <summary>
///     系统参数配置服务
/// </summary>
[ApiDescriptionSettings(Order = 440)]
public class SysConfigService : IDynamicApiController, ITransient
{
    /// <summary>
    /// 用户管理
    /// </summary>
    private readonly UserManager _userManager;

    /// <summary>
    /// 系统配置表仓储
    /// </summary>
    private readonly SqlSugarRepository<SysConfig> _sysConfigRep;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="sysConfigRep"></param>
    /// <param name="userManager"></param>
    public SysConfigService(SqlSugarRepository<SysConfig> sysConfigRep, UserManager userManager)
    {
        _sysConfigRep = sysConfigRep;
        _userManager = userManager;
    }

    /// <summary>
    ///     管理员配置
    /// </summary>
    private static readonly string[] adminConfig = {
        ConfigConst.SysFLinkIp,
        ConfigConst.SysFLinkPort,
        ConfigConst.SysFLinkOpen,
        ConfigConst.Authorization,
        ConfigConst.Platform,
        ConfigConst.DncFilePath
    };

    /// <summary>
    ///     获取参数配置列表
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取参数配置列表")]
    [HttpGet("/sysConfig/list")]
    public async Task<List<SysConfig>> Page()
    {
        var config = await _sysConfigRep.CopyNew().AsQueryable().ToListAsync();
        config = _userManager.User.AdminType != AdminTypeEnum.SuperAdmin
            ? config.Where(w => !adminConfig.Contains(w.Code)).ToList()
            : config.Where(w => w.Code != ConfigConst.SysFLinkIp
                && w.Code != ConfigConst.SysFLinkPort
                && w.Code != ConfigConst.SysFLinkOpen).ToList();
        // 遍历检查全局配置中是否存在，如果不存在则添加
        foreach (var item in config)
        {
            if (!GlobalConfigManager.ContainsConfig(item.Code))
                GlobalConfigManager.SetConfigValue(item.Code, item?.Value);
        }
        return config;
    }

    /// <summary>
    ///     更新参数配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysConfig/update")]
    [DisplayName("更新参数配置")]
    public async Task UpdateConfig(SysConfig input)
    {
        // 判断是否存在
        var isExist = await _sysConfigRep.IsAnyAsync(u => (u.Name == input.Name || u.Code == input.Code) && u.Id != input.Id);
        if (isExist)
            throw Oops.Oh(ErrorCode.D9000);
        var config = input.Adapt<SysConfig>();
        // 更新
        await _sysConfigRep.CopyNew().AsUpdateable(config).IgnoreColumns(true).ExecuteCommandAsync();
        // 更新全局配置
        GlobalConfigManager.SetConfigValue(config.Code, config?.Value);
    }

    /// <summary>
    ///     更新参数配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysConfig/batch/update")]
    [DisplayName("更新参数配置")]
    public async Task BatchUpdateConfig(List<SysConfig> input)
    {
        // 更新
        await _sysConfigRep.CopyNew().AsUpdateable(input).IgnoreColumns(true).ExecuteCommandAsync();
        // 更新全局配置
        foreach (var config in input)
            GlobalConfigManager.SetConfigValue(config.Code, config?.Value);
    }

    /// <summary>
    ///     获取参数配置详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取参数配置详情")]
    [HttpGet("/sysConfig/detail")]
    public async Task<SysConfig> GetDetail([FromQuery] BaseId input)
    {
        var config = await _sysConfigRep.CopyNew().GetFirstAsync(u => u.Id == input.Id);
        if (config == null)
            throw Oops.Oh("未找到该配置！");
        if (!GlobalConfigManager.ContainsConfig(config.Code))
            GlobalConfigManager.SetConfigValue(config.Code, config?.Value);
        return config;
    }

    /// <summary>
    /// 获取参数配置详情 批量
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysConfig/batch/detail")]
    public async Task<List<SysConfig>> GetBatchDetail([FromQuery] BaseId<List<long>> input)
    {
        // 获取配置
        var configs = await _sysConfigRep.CopyNew().AsQueryable().Where(u => input.Id.Contains(u.Id)).ToListAsync();
        // 更新全局配置
        foreach (var config in configs)
        {
            if (!GlobalConfigManager.ContainsConfig(config.Code))
                GlobalConfigManager.SetConfigValue(config.Code, config?.Value);
        }
        return configs;
    }

    /// <summary>
    ///     获取参数配置值
    /// </summary>
    /// <param name="code"></param>
    /// <returns></returns>
    [NonAction]
    public async Task<T> GetConfigValue<T>(string code)
    {
        if (string.IsNullOrWhiteSpace(code)) return default;

        var value = GlobalConfigManager.GetConfigValue(code);
        if (string.IsNullOrEmpty(value))
        {
            var config = await _sysConfigRep.CopyNew().GetFirstAsync(u => u.Code == code);
            value = config?.Value;
            GlobalConfigManager.SetConfigValue(code, value);
        }

        if (string.IsNullOrWhiteSpace(value)) return default;
        return (T)Convert.ChangeType(value, typeof(T));
    }

    /// <summary>
    /// 初始化全局配置 - 从数据库加载所有配置到内存
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task InitializeGlobalConfigs()
    {
        var configs = await _sysConfigRep.CopyNew().AsQueryable().ToListAsync();
        var configDict = configs.Where(c => !string.IsNullOrWhiteSpace(c.Code))
                               .ToDictionary(c => c.Code, c => c.Value);
        Console.WriteLine($"configDict:{JSON.Serialize(configDict)}");
        GlobalConfigManager.SetConfigValues(configDict);
    }
}