using NewLife.Caching;

namespace Feng.IotGateway.Core.Service.Cache;

/// <summary>
///     系统缓存服务
/// </summary>
public class CacheService : ISingleton
{
    private readonly ICache _cache;
    private const string Prefix = "IotGateway:";

    /// <summary>
    /// </summary>
    /// <param name="cache"></param>
    public CacheService(ICache cache)
    {
        _cache = cache;
    }

    /// <summary>
    ///     获取所有缓存列表
    /// </summary>
    /// <returns></returns>
    public async Task<List<string>> GetAllCacheKeys()
    {
        return _cache == NewLife.Caching.Cache.Default
            ? _cache.Keys.Where(u => u.StartsWith(Prefix)).Select(u => u[Prefix.Length..]).OrderBy(u => u.Replace(Prefix, "")).ToList()
            : ((FullRedis) _cache).Search($"{Prefix}*", int.MaxValue).Select(s => s.Replace(Prefix, "")).ToList();
    }

    /// <summary>
    ///     获取缓存
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="cacheKey"></param>
    /// <returns></returns>
    [NonAction]
    public T? Get<T>(string cacheKey)
    {
        return _cache.Get<T>($"{Prefix}{cacheKey}");
    }


    /// <summary>
    ///     检查给定 key 是否存在
    /// </summary>
    /// <param name="cacheKey">键</param>
    /// <returns></returns>
    [NonAction]
    public bool Exists(string cacheKey)
    {
        return _cache.ContainsKey($"{Prefix}{cacheKey}");
    }


    /// <summary>
    ///     增加缓存
    /// </summary>
    /// <param name="key"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    [NonAction]
    public bool Set(string key, object value)
    {
        return _cache.Set($"{Prefix}{key}", value);
    }

    /// <summary>
    ///     增加对象缓存,并设置过期时间
    /// </summary>
    /// <param name="key"></param>
    /// <param name="value"></param>
    /// <param name="expire"></param>
    /// <returns></returns>
    public bool Set(string key, object value, TimeSpan expire)
    {
        return _cache.Set($"{Prefix}{key}", value, expire);
    }

    /// <summary>
    ///     删除缓存
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public int Remove(string key)
    {
        return _cache.Remove($"{Prefix}{key}");
    }

    /// <summary>
    ///     删除某特征关键字缓存
    /// </summary>
    /// <param name="prefixKey"></param>
    /// <returns></returns>
    [NonAction]
    public int RemoveByPrefixKey(string prefixKey)
    {
        var delKeys = _cache == NewLife.Caching.Cache.Default
            ? _cache.Keys.Where(u => u.StartsWith($"{Prefix}{prefixKey}")).ToArray()
            : ((FullRedis) _cache).Search($"{Prefix}{prefixKey}*", int.MaxValue).ToArray();

        return _cache.Remove(delKeys);
    }

    /// <summary>
    ///     根据键名前缀获取键名集合
    /// </summary>
    /// <param name="prefixKey"></param>
    /// <returns></returns>
    public List<string> GetKeysByPrefixKey(string prefixKey)
    {
        return _cache == NewLife.Caching.Cache.Default
            ? _cache.Keys.Where(u => u.StartsWith($"{Prefix}{prefixKey}")).Select(u => u[Prefix.Length..]).ToList()
            : ((FullRedis) _cache).Search($"{Prefix}{prefixKey}*", int.MaxValue).ToList();
    }

    /// <summary>
    ///     获取缓存值
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public object? GetValue(string key)
    {
        return _cache == NewLife.Caching.Cache.Default
            ? _cache.Get<object>($"{Prefix}{key}")
            : _cache.Get<string>($"{Prefix}{key}");
    }

    /// <summary>
    ///     获取或添加缓存，在数据不存在时执行委托请求数据
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="key"></param>
    /// <param name="callback"></param>
    /// <param name="expire">过期时间，单位秒</param>
    /// <returns></returns>
    [NonAction]
    public T? GetOrAdd<T>(string key, Func<string, T> callback, int expire = -1)
    {
        return _cache.GetOrAdd($"{Prefix}{key}", callback, expire);
    }
}