using NewLife.Caching;

namespace Feng.IotGateway.Core.Service.Cache;

/// <summary>
/// </summary>
public static class CacheSetup
{
    /// <summary>
    ///     缓存注册（新生命Redis组件）
    /// </summary>
    /// <param name="services"></param>
    public static void AddCache(this IServiceCollection services)
    {
        var cache = NewLife.Caching.Cache.Default;

        var cacheOptions = App.GetOptions<CacheOptions>();
        if (cacheOptions.CacheType == CacheTypeEnum.RedisCache.ToString())
            cache = new FullRedis(new RedisOptions
            {
                Configuration = cacheOptions.RedisConnectionString,
                Prefix = "IotGateway:"
            });

        services.AddSingleton(cache);
    }
}