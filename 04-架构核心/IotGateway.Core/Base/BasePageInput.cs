namespace Feng.IotGateway.Core.Base;

/// <summary>
///     全局分页查询输入参数
/// </summary>
public class BasePageInput
{
    /// <summary>
    /// </summary>
    public BasePageInput()
    {
        PageNo = 1;
        PageSize = 20;
        SearchBeginTime = "1970-01-01 00:00:00";
        SearchEndTime = DateTime.Now.AddDays(1).ToString("yyyy-MM-dd HH:mm:ss");
        DescStr = "descend";
        SearchValue = "";
    }

    /// <summary>
    ///     搜索值
    /// </summary>
    [Description("搜索值")]
    public string SearchValue { get; set; }

    /// <summary>
    ///     当前页码
    /// </summary>
    [Description("当前页码")]
    public int PageNo { get; set; }

    /// <summary>
    ///     页码容量
    /// </summary>
    [Description("页码容量")]
    public int PageSize { get; set; }

    /// <summary>
    ///     搜索开始时间
    /// </summary>
    [Description("搜索开始时间")]
    public string SearchBeginTime { get; set; }

    /// <summary>
    ///     搜索结束时间
    /// </summary>
    [Description("搜索结束时间")]
    public string SearchEndTime { get; set; }

    /// <summary>
    ///     排序字段
    /// </summary>
    [Description("排序字段")]
    public string SortField { get; set; }

    /// <summary>
    ///     排序方法,默认升序,否则降序(配合antd前端,约定参数为 Ascend,Dscend)
    /// </summary>
    [Description("排序方法,默认升序,否则降序(配合antd前端,约定参数为 Ascend,Dscend)")]
    public string SortOrder { get; set; }

    /// <summary>
    ///     降序排序
    /// </summary>
    [Description("降序排序")]
    public string DescStr { get; set; }
}