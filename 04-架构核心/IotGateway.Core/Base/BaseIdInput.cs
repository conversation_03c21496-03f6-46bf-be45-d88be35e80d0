namespace Feng.IotGateway.Core.Base;

/// <summary>
///     主键Id输入参数
/// </summary>
public class BaseId
{
    /// <summary>
    ///     主键Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    [DataValidation(ValidationTypes.Numeric)]
    public long Id { get; set; }
}

/// <summary>
///     主键Id映射DTO
/// </summary>
public class BaseId<T>
{
    /// <summary>
    ///     主键Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public T Id { get; set; }
}

/// <summary>
/// 启/禁用
/// </summary>
/// <typeparam name="T"></typeparam>
public class EnableInput<T> : BaseId<T>
{
    /// <summary>
    ///     是否启用
    /// </summary>
    public bool Enable { get; set; }
}

/// <summary>
/// 重命名
/// </summary>
/// <typeparam name="T"></typeparam>
public class ReNameInput<T> : BaseId<T>
{
    /// <summary>
    /// 名称
    /// </summary>
    [Required]
    public string Name { get; set; }
}