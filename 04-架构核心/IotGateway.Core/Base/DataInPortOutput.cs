namespace Feng.IotGateway.Core.Base;

/// <summary>
///     数据导入结果
/// </summary>
public class DataInPortOutput
{
    /// <summary>
    ///     失败行数
    /// </summary>
    public int ErrorCount { get; set; }

    /// <summary>
    ///     成功行数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    ///     失败的数据
    /// </summary>
    public List<ErrorColumn> ErrorColumn { get; set; }
}

/// <summary>
///     失败的数据
/// </summary>
public class ErrorColumn
{
    /// <summary>
    ///     第几行
    /// </summary>
    public int Line { get; set; }

    /// <summary>
    ///     原因
    /// </summary>
    public string Error { get; set; }
}