namespace Feng.IotGateway.Core.Entity;

/// <summary>
///     系统标准脚本表
/// </summary>
[SugarTable("sys_script", "系统标准脚本表")]
public class SysScript
{
    /// <summary>
    ///     雪花Id
    /// </summary>
    [SugarColumn(ColumnDescription = "Id", IsPrimaryKey = true, IsIdentity = false)]
    public long Id { get; set; }

    /// <summary>
    ///     脚本名称
    /// </summary>
    [SugarColumn(ColumnDescription = "脚本名称")]
    [Required]
    [MaxLength(64)]
    public string Name { get; set; }

    /// <summary>
    ///     脚本所属类型:1:设备操作；2:共享数据库操作；3:时间操作；4:系统方法；5:串口相关函数；6:Http相关函数; 10:模板方法
    /// </summary>
    [SugarColumn(ColumnDescription = "脚本所属类型:1:设备操作；2:共享数据库操作；3:时间操作；4:系统方法；5:串口相关函数；6:Http相关函数; 10:模板方法")]
    public SysScriptTypeEnum ScriptType { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    [SugarColumn(ColumnDescription = "脚本内容")]
    public string Content { get; set; }

    /// <summary>
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string ScriptTypeName => EnumUtil.GetEnumDesc(ScriptType);

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述")]
    public string Describe { get; set; }

    /// <summary>
    ///     方法类型 1基础方法；2示例代码
    /// </summary>
    public ScriptMethodTypeEnum Method { get; set; } = ScriptMethodTypeEnum.Basic;
}

/// <summary>
///     1基础方法；2示例代码
/// </summary>
public enum ScriptMethodTypeEnum
{
    /// <summary>
    ///     基础方法
    /// </summary>
    [Description("基础方法")] Basic = 1,

    /// <summary>
    ///     示例代码
    /// </summary>
    [Description("示例代码")] Template = 2
}

/// <summary>
/// </summary>
public enum SysScriptTypeEnum
{
    /// <summary>
    ///     设备操作
    /// </summary>
    [Description("设备操作")] Device = 1,

    /// <summary>
    ///     共享数据库操作
    /// </summary>
    [Description("共享数据库操作")] OpenData = 2,

    /// <summary>
    ///     时间操作
    /// </summary>
    [Description("时间操作")] DateTime = 3,

    /// <summary>
    ///     系统方法
    /// </summary>
    [Description("系统方法")] System = 4,

    /// <summary>
    ///     串口相关函数
    /// </summary>
    [Description("串口相关函数")] Serial = 5,

    /// <summary>
    ///     Http相关函数
    /// </summary>
    [Description("Http函数")] Http = 6,

    /// <summary>
    ///     TCP相关函数
    /// </summary>
    [Description("TCP操作")] Tcp = 7,

    /// <summary>
    ///     数据查询(历史查询)
    /// </summary>
    [Description("数据查询(历史查询)")] OnLine = 8,
    
    /// <summary>
    ///     UDP相关函数
    /// </summary>
    [Description("UDP操作")] Udp = 9,

    /// <summary>
    ///     模板方法
    /// </summary>
    [Description("模板方法")] Template = 10,
    
    /// <summary>
    ///     MQTT
    /// </summary>
    [Description("MQTT-客户端")] Mqtt = 11,
    
    /// <summary>
    ///     Files
    /// </summary>
    [Description("文件操作")] File = 12,
}