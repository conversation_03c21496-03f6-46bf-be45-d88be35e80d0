using Common.Enums;

namespace Feng.IotGateway.Core.Entity;

/// <summary>
///     设备采集变量表
/// </summary>
[SugarTable("device_variable", "设备采集变量表")]
public class DeviceVariable : EntityBaseId
{
    /// <summary>
    ///     标识符
    /// </summary>
    [SugarColumn(ColumnDescription = "标识符", Length = 128)]
    public string Identifier { get; set; }

    /// <summary>
    ///     变量名
    /// </summary>
    [SugarColumn(ColumnDescription = "变量名", Length = 128)]
    public string Name { get; set; }

    /// <summary>
    ///     默认值
    /// </summary>
    [SugarColumn(ColumnDescription = "默认值", Length = 128)]
    public string? DefaultValue { get; set; }

    /// <summary>
    ///     长度(属性值小数点保留位数长度)
    /// </summary>
    [SugarColumn(ColumnDescription = "长度", Length = 12)]
    public ushort Length { get; set; }

    /// <summary>
    ///     转换数据类型
    /// </summary>
    [SugarColumn(ColumnDescription = "转换数据类型", Length = 6)]
    public TransPondDataTypeEnum TransitionType { get; set; }

    /// <summary>
    ///     数据来源
    /// </summary>
    [SugarColumn(ColumnDescription = "数据来源", Length = 6)]
    public ValueSourceEnum ValueSource { get; set; }

    /// <summary>
    ///     单位
    /// </summary>
    [SugarColumn(ColumnDescription = "单位", IsNullable = true, Length = 32)]
    public string? Unit { get; set; }

    /// <summary>
    ///     执行优先级:0最优
    /// </summary>
    [SugarColumn(ColumnDescription = "执行优先级:0最优", Length = 6)]
    public short Order { get; set; }

    /// <summary>
    ///     为属性值添加自定义
    /// </summary>
    [SugarColumn(ColumnDescription = "为属性值添加自定义", ColumnDataType = "longtext,text,clob")]
    public string? Custom { get; set; }

    /// <summary>
    ///     表达式
    /// </summary>
    [SugarColumn(ColumnDescription = "表达式", ColumnDataType = "longtext,text,clob")]
    public string? Expressions { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    [SugarColumn(ColumnDescription = "脚本内容", ColumnDataType = "longtext,text,clob")]
    public string? Script { get; set; }

    /// <summary>
    ///     所属设备
    /// </summary>
    [SugarColumn(ColumnDescription = "所属设备", Length = 32)]
    public long DeviceId { get; set; }

    /// <summary>
    ///     上送方式
    /// </summary>
    [SugarColumn(ColumnDescription = "上送方式", Length = 6)]
    public SendTypeEnum SendType { get; set; }

    /// <summary>
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启动")]
    public bool Enable { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    [SugarColumn(ColumnDescription = "标签", IsJson = true, ColumnDataType = "longtext,text,clob",IsNullable = true)]
    public List<string>? Tags { get; set; }

    /// <summary>
    ///     采集周期
    /// </summary>
    [SugarColumn(ColumnDescription = "采集周期", Length = 32)]
    public int Period { get; set; }

    /// <summary>
    ///     强制归档时间
    /// </summary>
    [SugarColumn(ColumnDescription = "强制归档时间", Length = 32)]
    public int ArchiveTime { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", ColumnDataType = "longtext,text,clob")]
    public string? Description { get; set; }

    /// <summary>
    ///     取值范围配置
    /// </summary>
    [SugarColumn(ColumnDescription = "取值范围配置", IsJson = true)]
    public DeviceVariableFilter? DeviceVariableFilter { get; set; } = new();

    /// <summary>
    /// </summary>
    [SugarColumn(IsJson = true)]
    public DeviceVariableEx DeviceVariableEx { get; set; } = new();

    /// <summary>
    ///     是否是系统属性
    /// </summary>
    [SugarColumn(ColumnDescription = "是否是系统属性")]
    public bool IsSystem { get; set; }

    /// <summary>
    ///     脚本计算类型-数据持久化
    /// </summary>
    [SugarColumn(ColumnDescription = "脚本计算类型-数据持久化")]
    public bool Persistence { get; set; }

    /// <summary>
    ///     发布
    /// </summary>
    [SugarColumn(ColumnDescription = "发布")]
    public bool Release { get; set; }

    #region 忽略属性

    /// <summary>
    ///     设备名称
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string DeviceName => Device?.DeviceName ?? "";

    #endregion

    #region 关联对象

    /// <summary>
    ///     设备
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(DeviceId))]
    public Device Device { get; set; }

    #endregion
}

/// <summary>
///     取值范围
/// </summary>
public class DeviceVariableFilter
{
    /// <summary>
    ///     最小值
    /// </summary>
    public double Min { get; set; }

    /// <summary>
    ///     最大值
    /// </summary>
    public double Max { get; set; }

    /// <summary>
    ///     设备属性取值范围最大最小值类型枚举：1:原值；2：最小值；3:最大值；4：指定值；5：上一有效值 6：丢弃
    /// </summary>
    public DeviceVariableFilterTypeEnum MinFilterType { get; set; } = DeviceVariableFilterTypeEnum.This;

    /// <summary>
    ///     最小取值范围指定值
    /// </summary>
    public double SetMin { get; set; }

    /// <summary>
    ///     最大取值范围指定值
    /// </summary>
    public double SetMax { get; set; }

    /// <summary>
    ///     设备属性取值范围最大最小值类型枚举：1:原值；2：最小值；3:最大值；4：指定值；5：上一有效值 6：丢弃
    /// </summary>
    public DeviceVariableFilterTypeEnum MaxFilterType { get; set; } = DeviceVariableFilterTypeEnum.This;

    /// <summary>
    ///     是否保存
    /// </summary>
    public bool Save { get; set; }
}

/// <summary>
/// </summary>
public class DeviceVariableEx
{
    /// <summary>
    ///     读写方式
    /// </summary>
    [Description("读写方式")]
    public ProtectTypeEnum ProtectType { get; set; }

    /// <summary>
    ///     读写方式
    /// </summary>
    [Description("读写方式中文描述")]
    [SugarColumn(IsIgnore = true)]
    public string ProtectTypeName => ProtectType == 0 ? "只读" : EnumUtil.GetEnumDesc(ProtectType);

    /// <summary>
    ///     读取方法
    /// </summary>
    [Description("读取方法")]
    public string Method { get; set; }

    /// <summary>
    ///     读取地址
    /// </summary>
    [Description("读取地址")]
    public string RegisterAddress { get; set; }

    /// <summary>
    ///     读取数据类型
    /// </summary>
    [Description("读取数据类型")]
    public DataTypeEnum DataType { get; set; } = DataTypeEnum.Int32;

    /// <summary>
    ///     读取数据类型
    /// </summary>
    [Description("读取数据类型中文描述")]
    [SugarColumn(IsIgnore = true)]
    public string DataTypeName => EnumUtil.GetEnumDesc(DataType);

    /// <summary>
    ///     字符串编码
    /// </summary>
    [Description("字符串编码")]
    public StringEnum Encoding { get; set; } = StringEnum.Utf8;

    /// <summary>
    ///     字符串编码
    /// </summary>
    [Description("字符串编码中文描述")]
    [SugarColumn(IsIgnore = true)]
    public string EncodingName => EnumUtil.GetEnumDesc(Encoding);

    /// <summary>
    ///     长度(属性值小数点保留位数长度)
    /// </summary>
    [Description("读取长度")]
    [SugarColumn(ColumnDescription = "读取长度")]
    public ushort Length { get; set; }
}

/// <summary>
///     设备属性取值范围最大最小值类型枚举：1:原值；2：最小值；3:最大值；4：指定值；5：上一有效值 6：丢弃
/// </summary>
public enum DeviceVariableFilterTypeEnum
{
    /// <summary>
    ///     原值
    /// </summary>
    [Description("原值")] This = 1,

    /// <summary>
    ///     最小值
    /// </summary>
    [Description("最小值")] Min = 2,

    /// <summary>
    ///     最大值
    /// </summary>
    [Description("最大值")] Max = 3,

    /// <summary>
    ///     指定值
    /// </summary>
    [Description("指定值")] Set = 4,

    /// <summary>
    ///     上一有效值
    /// </summary>
    [Description("上一有效值")] Cookie = 5,

    /// <summary>
    ///     丢弃
    /// </summary>
    [Description("丢弃")] Clear = 6
}