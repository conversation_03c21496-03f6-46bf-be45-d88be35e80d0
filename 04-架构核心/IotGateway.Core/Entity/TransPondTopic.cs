using System.ComponentModel;
using Feng.Common.Util;
using MQTTnet.Protocol;

namespace IotGateway.Application.Entity;

/// <summary>
///     上行转发Topic表
/// </summary>
[SugarTable("transPond_Topic", "上行转发Topic表")]
public class TransPondTopic : EntityBaseId
{
    /// <summary>
    ///     Topic
    /// </summary>
    [SugarColumn(ColumnDescription = "Topic")]
    public string Topic { get; set; }

    /// <summary>
    ///     发布Topic作用：1:实时数据;2:离线数据;3:设备属性同步;4:时间同步
    /// </summary>
    [SugarColumn(ColumnDescription = "转发Topic作用：1:实时数据;2:离线数据;3:设备属性同步;4:时间同步")]
    public TransPondTopicPurposeEnum TransPondTopicPurpose { get; set; }

    /// <summary>
    ///     转发Topic规则：1静态；2动态
    /// </summary>
    [SugarColumn(ColumnDescription = "转发Topic规则：1静态；2动态")]
    public TransPondTopicRuleEnum Rule { get; set; } = TransPondTopicRuleEnum.Static;

    /// <summary>
    ///     转发Topic类型：1:订阅；2：发布
    /// </summary>
    [SugarColumn(ColumnDescription = "转发Topic类型：1:订阅；2：发布")]
    public TransPondTopicTypeEnum TransPondTopicType { get; set; }

    /// <summary>
    ///     上行转发配置Id
    /// </summary>
    [SugarColumn(ColumnDescription = "上行转发配置Id")]
    public long TransPondId { get; set; }

    /// <summary>
    ///     不同平台默认的解析脚本内容
    /// </summary>
    [SugarColumn(ColumnDescription = "不同平台默认的解析脚本内容", ColumnDataType = "longtext,text,clob",IsNullable = true)]
    public string? Config { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述",IsNullable = true)]
    public string? Description { get; set; }

    /// <summary>
    ///     qos等级
    /// </summary>
    [SugarColumn(ColumnDescription = "qos")]
    public MqttQualityOfServiceLevel Qos { get; set; } = MqttQualityOfServiceLevel.AtLeastOnce;

    /// <summary>
    ///     消息发送超时时间(s)
    /// </summary>
    [SugarColumn(ColumnDescription = "消息发送超时时间(s)")]
    public int TimeOut { get; set; }

    #region 忽略字段

    /// <summary>
    ///     qps名称
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string QosName => Qos == MqttQualityOfServiceLevel.AtMostOnce ? "最多传输一次" :
        Qos == MqttQualityOfServiceLevel.AtLeastOnce ? "至少传输一次" : "恰好传输一次";

    /// <summary>
    ///     发布Topic作用
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string TransPondTopicPurposeName => EnumUtil.GetEnumDesc(TransPondTopicPurpose);

    /// <summary>
    ///     转发Topic类型
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string TransPondTopicTypeName => EnumUtil.GetEnumDesc(TransPondTopicType);

    /// <summary>
    ///     转发Topic规则
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string RuleName => EnumUtil.GetEnumDesc(Rule);

    #endregion
    
    #region 关联表

    /// <summary>
    ///     上行转发配置
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(TransPondId))]
    public TransPond TransPond { get; set; }

    #endregion
}

/// <summary>
///     发布转发Topic作用：1:实时数据;2:离线数据;3:设备属性同步;4:时间同步；5:心跳；6:云端要求同步设备属性回复
/// </summary>
public enum TransPondTopicPurposeEnum
{
    #region Pub

    /// <summary>
    ///     实时数据
    /// </summary>
    [Description("实时数据")] OnLine = 1,

    /// <summary>
    ///     离线数据
    /// </summary>
    [Description("离线数据")] OffLine = 2,

    /// <summary>
    ///     设备属性同步
    /// </summary>
    [Description("设备属性同步")] Meta = 3,

    /// <summary>
    ///     时间同步
    /// </summary>
    [Description("时间同步")] SysTime = 4,

    /// <summary>
    ///     心跳
    /// </summary>
    [Description("心跳")] Heartbeat = 5,

    /// <summary>
    ///     云端要求同步设备属性回复
    /// </summary>
    [Description("云端要求同步设备属性回复")] MetaPushReply = 6,

    /// <summary>
    ///     云端要求刷新设备实时数据回复
    /// </summary>
    [Description("云端要求刷新设备实时数据回复")] CloudRequestRefreshRealTimeDeviceDataReply = 7,

    /// <summary>
    ///     Ota响应
    /// </summary>
    [Description("Ota响应")] PubOta = 9,
    #endregion Pub

    #region Sub

    /// <summary>
    ///     属性下写
    /// </summary>
    [Description("属性下写")] Variable = 10,

    /// <summary>
    ///     变量下发
    /// </summary>
    [Description("变量下发")] Share = 11,

    /// <summary>
    ///     时钟同步响应
    /// </summary>
    [Description("时钟同步响应")] SysTimeSub = 12,

    /// <summary>
    ///     云端要求同步设备属性
    /// </summary>
    [Description("云端要求同步设备属性")] MetaPush = 13,

    /// <summary>
    ///     指令下发
    /// </summary>
    [Description("指令下发")] Cmd = 14,

    /// <summary>
    ///     云端要求刷新设备实时数据
    /// </summary>
    [Description("云端要求刷新设备实时数据")] CloudRequestRefreshRealTimeDeviceData = 15,

    /// <summary>
    ///     设备指令下发
    /// </summary>
    [Description("设备指令下发")] DeviceCmd = 16,
    
    /// <summary>
    ///     Ota
    /// </summary>
    [Description("Ota")] SubOta = 18,
    
    #endregion Sub
}

/// <summary>
///     转发Topic规则：1静态；2动态
/// </summary>
public enum TransPondTopicRuleEnum
{
    /// <summary>
    ///     静态
    /// </summary>
    [Description("静态")] Static = 1,

    /// <summary>
    ///     动态
    /// </summary>
    [Description("动态")] Dynamic = 2
}

/// <summary>
///     转发Topic类型：1:订阅；2：发布
/// </summary>
public enum TransPondTopicTypeEnum
{
    /// <summary>
    ///     订阅
    /// </summary>
    [Description("订阅")] Sub = 1,

    /// <summary>
    ///     发布
    /// </summary>
    [Description("发布")] Pub = 2
}