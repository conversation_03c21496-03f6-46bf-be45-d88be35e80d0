using System.Text.Json.Serialization;
using Feng.IotGateway.Core.Attribute;

namespace Feng.IotGateway.Core.Entity;

/// <summary>
/// </summary>
[SugarTable("system_user", "用户表")]
public class SysUser : EntityBase
{
    /// <summary>
    ///     账号
    /// </summary>
    [SugarColumn(ColumnDescription = " 名称", Length = 50)]
    [Required]
    [MaxLength(50)]
    public virtual string Account { get; set; }

    /// <summary>
    ///     密码（默认MD5加密）
    /// </summary>
    [SugarColumn(ColumnDescription = " 密码（默认MD5加密）", Length = 64)]
    [Required]
    [MaxLength(50)]
    [JsonIgnore]
    [IgnoreUpdateSeedColumn]
    public string Password { get; set; }

    /// <summary>
    ///     姓名
    /// </summary>
    [SugarColumn(ColumnDescription = " 姓名", Length = 20)]
    [MaxLength(20)]
    public virtual string Name { get; set; }

    /// <summary>
    ///     最后登录时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后登录时间")]
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    ///     管理员类型-超级管理员_1、管理员_2、普通账号_3
    /// </summary>
    [SugarColumn(ColumnDescription = " 管理员类型-超级管理员_1、管理员_2、普通账号_3")]
    public AdminTypeEnum AdminType { get; set; } = AdminTypeEnum.None;

    /// <summary>
    ///     启用状态
    /// </summary>
    [SugarColumn(ColumnDescription = " 启用状态")]
    public bool Status { get; set; } = true;

    /// <summary>
    /// 工号
    /// </summary>
    /// <returns></returns>
    [SugarColumn(ColumnDescription = " 工号", Length = 64)]
    public string? JobNumber { get; set; }

    /// <summary>
    ///     头像
    /// </summary>
    [SugarColumn(ColumnDescription = "头像", Length = 512)]
    public string? Avatar { get; set; }
}