using Feng.IotGateway.Core.Attribute;

namespace Feng.IotGateway.Core.Entity;

/// <summary>
///     离线缓存数据表
/// </summary>
[SugarTable("offLine", "离线缓存数据表")]
[Tenant(SqlSugarConst.EdgeData)]
[SugarIndex("index_offLine_timeStamp",nameof(TimeStamp),OrderByType.Asc)]
[IgnoreUpdateSeed]
public class OffLine : EntityBaseId
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [SugarColumn(ColumnDescription = "设备名称", Length = 128)]
    [IgnoreUpdateSeedColumn]
    public string DeviceName { set; get; }

    /// <summary>
    ///     点位
    /// </summary>
    [SugarColumn(ColumnDataType = "longtext,text,clob")]
    [IgnoreUpdateSeedColumn]
    public string Params { set; get; }

    /// <summary>
    ///     采集时间戳
    /// </summary>
    [SugarColumn(ColumnDescription = "采集时间戳")]
    [IgnoreUpdateSeedColumn]
    public long TimeStamp { get; set; }
}