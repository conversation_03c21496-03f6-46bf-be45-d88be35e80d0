using Common.Enums;

namespace Feng.IotGateway.Core.Entity;

/// <summary>
///     设备事件表
/// </summary>
[SugarTable("device_customEvent", "设备事件表")]
public class DeviceEvent : EntityBaseId
{
    /// <summary>
    ///     设备Id
    /// </summary>
    [SugarColumn(ColumnDescription = "设备Id")]
    public long DeviceId { get; set; }

    /// <summary>
    ///     触发方式：1属性触发；2定时触发
    /// </summary>
    [SugarColumn(ColumnDescription = "触发方式：1属性触发；2定时触发；")]
    public TriggerEventTypeEnum TriggerEventType { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string EventName { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool Status { get; set; }

    /// <summary>
    ///     事件配置参数
    /// </summary>
    [SugarColumn(ColumnDescription = "事件配置参数", IsJson = true, ColumnDataType = StaticConfig.CodeFirst_BigString, IsNullable = true)]
    public CustomEventConfig CustomEventConfig { get; set; }

    /// <summary>
    ///     事件执行条件
    /// </summary>
    [SugarColumn(ColumnDescription = "事件执行条件", IsJson = true, ColumnDataType = StaticConfig.CodeFirst_BigString, IsNullable = true)]
    public List<CustomEventWhere> CustomEventWhereList { get; set; }

    #region 忽略字段

    /// <summary>
    ///     触发方式
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string TriggerEventTypeDisplay => EnumUtil.GetEnumDesc(TriggerEventType);

    /// <summary>
    /// 最后触发时间
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DateTime? LastTime { get; set; } = null;
    #endregion

    #region 关联表

    /// <summary>
    ///     设备
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(DeviceId))]
    public Device Device { get; set; }

    /// <summary>
    ///     设备事件日志
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Entity.DeviceEventLog.DeviceEventId))]
    [SugarColumn(IsIgnore = true)]
    public List<DeviceEventLog> DeviceEventLog { get; set; }
    
    #endregion
}

/// <summary>
///     执行动作
/// </summary>
public class CustomEventAction
{
    /// <summary>
    ///     排序
    /// </summary>
    public short Order { get; set; }

    /// <summary>
    ///     动作名称
    /// </summary>
    public string ActionName { get; set; }

    /// <summary>
    ///     动作：1属性下写；2消息通知；3触发脚本；4休眠；5设备重连；
    /// </summary>
    public EventActionTypeEnum EventActionType { get; set; }

    /// <summary>
    ///     属性下写
    /// </summary>
    public List<DeviceVariableWrite> DeviceVariableWrite { get; set; } = new();

    /// <summary>
    ///     属性读取
    /// </summary>
    public List<string> DeviceVariableRead { get; set; } = new();
    
    /// <summary>
    ///     消息通知
    /// </summary>
    public MessagePushConfig? MessagePushConfig { get; set; }

    /// <summary>
    ///     触发脚本
    /// </summary>
    public ScriptConfig? ScriptConfig { get; set; }

    /// <summary>
    ///     休眠
    /// </summary>
    public SleepConfig? SleepConfig { get; set; }

    /// <summary>
    ///     触发条件集合
    /// </summary>
    public List<TriggerConditionList> TriggerConditionList { get; set; } = new();

    /// <summary>
    ///     属性触发表达式
    /// </summary>
    public string Expressions { get; set; }
}

/// <summary>
///     属性下写
/// </summary>
public class DeviceVariableWrite
{
    /// <summary>
    ///     属性标识符
    /// </summary>
    public string Identifier { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public object? Value { get; set; }
}

/// <summary>
///     消息通知
/// </summary>
public class MessagePushConfig
{
    // todo
}

/// <summary>
///     休眠配置
/// </summary>
public class SleepConfig
{
    /// <summary>
    ///     休眠时间
    /// </summary>
    public double Sleep { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public ExecutionStrategyByTimePeriodUnitEnum Unit { get; set; }
}

/// <summary>
///     脚本触发配置
/// </summary>
public class ScriptConfig
{
    /// <summary>
    ///     执行脚本
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    ///     参数
    /// </summary>
    public Dictionary<string, ScriptParams> Params { get; set; } = new();
}

/// <summary>
/// 参数值
/// </summary>
public class ScriptParams
{
    /// <summary>
    /// 数据类型
    /// </summary>
    public TransPondDataTypeEnum DataType { get; set; }
    
    /// <summary>
    /// 值
    /// </summary>
    public string Value { get; set; }
}

/// <summary>
///     事件执行条件
/// </summary>
public class CustomEventWhere
{
    /// <summary>
    ///     触发条件集合
    /// </summary>
    public List<TriggerConditionList> TriggerConditionList { get; set; } = new();

    /// <summary>
    ///     事件执行动作配置参数
    /// </summary>
    public List<CustomEventAction> CustomEventActionList { get; set; } = new();

    /// <summary>
    ///     属性触发表达式
    /// </summary>
    public string Expressions { get; set; }

    /// <summary>
    ///     启用防抖
    /// </summary>
    public bool AntiShake { get; set; }

    /// <summary>
    ///     防抖时间（秒）-设置周期内不重复触发
    /// </summary>
    public short AntiShakeTime { get; set; }

    /// <summary>
    ///     排序顺序
    /// </summary>
    public short Order { get; set; }

    /// <summary>
    ///     生成的唯一编码
    /// </summary>
    public string Guid { get; set; } = System.Guid.NewGuid().ToString("N");
}

/// <summary>
///     自定义事件配置参数
/// </summary>
public class CustomEventConfig
{
    /// <summary>
    ///     定时触发
    /// </summary>
    public EventTimedConfig EventTimedConfig { get; set; }

    /// <summary>
    ///     设备触发
    /// </summary>
    public EventDeviceConfig EventDeviceConfig { get; set; }

    /// <summary>
    ///     设备属性触发
    /// </summary>
    public EventDeviceVariableConfig EventDeviceVariableConfig { get; set; }
}

/// <summary>
///     设备触发
/// </summary>
public class EventDeviceConfig
{
    /// <summary>
    ///     设备触发类型：设备上线:onLine；设备离线:offLine
    /// </summary>
    public string Type { get; set; }
}

/// <summary>
///     设备属性触发
/// </summary>
public class EventDeviceVariableConfig
{
    // todo 待定是否需要指定属性触发
}

/// <summary>
///     自定义事件配置
/// </summary>
public class TriggerConditionList
{
    /// <summary>
    ///     关联关系
    /// </summary>
    public List<EventDeviceVariable> EventDeviceVariable { get; set; }

    /// <summary>
    ///     关联关系 "and" "or"
    /// </summary>
    public string? Type { get; set; }
}

/// <summary>
///     属性触发
/// </summary>
public class EventDeviceVariable
{
    /// <summary>
    ///     属性标识符
    /// </summary>
    [Required]
    public string Identifier { get; set; }

    /// <summary>
    ///     操作符
    /// </summary>
    [Required]
    public CompareEnum Compare { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public object? Value { get; set; }

    /// <summary>
    /// 字段类型
    /// </summary>
    [Required]
    public DataTypeEnum DataType { get; set; }

    /// <summary>
    ///     关联关系 "and" "or"
    /// </summary>
    public string? Type { get; set; }
}

/// <summary>
///     定时触发
/// </summary>
public class EventTimedConfig
{
    /// <summary>
    ///     执行方式：1周期执行；2Cron
    /// </summary>
    public ExecutionTypeEnum ExecutionType { get; set; }

    /// <summary>
    ///     执行方式为【周期执行】时必填
    /// </summary>
    public ExecutionTime ExecutionTime { get; set; }

    /// <summary>
    ///     执行方式为【Cron】时必填
    /// </summary>
    public ExecutionCron ExecutionCron { get; set; }
}

/// <summary>
///     周期执行
/// </summary>
public class ExecutionTime
{
    /// <summary>
    ///     执行方式：1周期执行；2Cron
    /// </summary>
    public ExecutionTypeEnum ExecutionType { get; set; } = ExecutionTypeEnum.周期执行;

    /// <summary>
    ///     表达式
    /// </summary>
    public string? Cron { get; set; }

    /// <summary>
    ///     执行周期
    /// </summary>
    [SugarColumn(ColumnDescription = "采集周期")]
    public int Period { get; set; } = 5;

    /// <summary>
    ///     时间单位:1秒；2分钟；3小时
    /// </summary>
    [SugarColumn(ColumnDescription = "时间单位:1秒；2分钟；3小时")]
    public ExecutionStrategyByTimePeriodUnitEnum PeriodUnit { get; set; } = ExecutionStrategyByTimePeriodUnitEnum.秒;

    /// <summary>
    ///     1：触发时总是执行；2：触发时上周期未执行完毕则不执行；
    /// </summary>
    public short? Type { get; set; }
}

/// <summary>
///     Cron表达式
/// </summary>
public class ExecutionCron
{
    /// <summary>
    ///     表达式
    /// </summary>
    public string Cron { get; set; }
}

/// <summary>
///     动作：1属性下写；2消息通知；3触发脚本；4休眠；5设备重连；6上报配置
/// </summary>
public enum EventActionTypeEnum
{
    /// <summary>
    ///     属性下写
    /// </summary>
    [Description("属性下写")] 属性下写 = 1,

    /// <summary>
    ///     消息通知
    /// </summary>
    [Description("消息通知")] 消息通知 = 2,

    /// <summary>
    ///     执行脚本
    /// </summary>
    [Description("执行脚本")] 执行脚本 = 3,

    /// <summary>
    ///     延迟执行
    /// </summary>
    [Description("延迟执行")] 延迟执行 = 4,

    /// <summary>
    ///     设备重连
    /// </summary>
    [Description("设备重连")] 设备重连 = 5,
    /// <summary>
    ///     属性读取
    /// </summary>
    [Description("属性读取")] 属性读取 = 6,
    
}

/// <summary>
///     执行方式：1周期执行；2Cron
/// </summary>
public enum ExecutionTypeEnum
{
    /// <summary>
    ///     周期执行
    /// </summary>
    [Description("周期执行")] 周期执行 = 1,

    /// <summary>
    ///     Cron
    /// </summary>
    [Description("Cron")] Cron = 2
}

/// <summary>
///     触发方式：1属性触发；2定时触发；3设备触发
/// </summary>
public enum TriggerEventTypeEnum
{
    /// <summary>
    ///     属性触发
    /// </summary>
    [Description("属性触发")] 属性触发 = 1,

    /// <summary>
    ///     定时触发
    /// </summary>
    [Description("定时触发")] 定时触发 = 2,

    /// <summary>
    ///     设备触发
    /// </summary>
    [Description("设备触发")] 设备触发 = 3
}