using Feng.IotGateway.Core.Attribute;

namespace Feng.IotGateway.Core.Entity;

/// <summary>
///     上报成功数据
/// </summary>
[SplitTable(SplitType._Custom01)]
[SugarTable("reportData", "上报成功数据记录表")]
[Tenant(SqlSugarConst.EdgeData)]
[IgnoreUpdateSeed]
public class ReportData : EntityBaseId
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [SugarColumn(ColumnDescription = "设备名称", Length = 64)]
    public string DeviceName { set; get; }

    /// <summary>
    ///     采集数据
    /// </summary>
    [SugarColumn(ColumnDataType = "text,clob")]
    public string Data { set; get; }

    /// <summary>
    ///     上报数据类型：1：实时上报；2：离线上报
    /// </summary>
    [SugarColumn(ColumnDescription = "上报数据类型：1：实时上报；2：离线上报")]
    public ReportDataTypeEnum ReportDataType { get; set; }

    /// <summary>
    ///     采集时间
    /// </summary>
    [SugarColumn(ColumnDescription = "采集时间戳")]
    public DateTime ReadTime { get; set; }

    /// <summary>
    ///     标识分表字段
    /// </summary>
    [SplitField]
    [SugarColumn(IsIgnore = true)]
    public string Time { get; set; }
}

/// <summary>
///     上报数据类型：1：实时上报；2：离线上报
/// </summary>
public enum ReportDataTypeEnum
{
    /// <summary>
    ///     实时上报
    /// </summary>
    [Description("实时上报")] OnLine = 1,

    /// <summary>
    ///     离线上报
    /// </summary>
    [Description("离线上报")] OffLine = 2
}