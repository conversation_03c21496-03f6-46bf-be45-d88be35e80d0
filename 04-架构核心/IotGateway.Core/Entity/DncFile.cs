namespace Feng.IotGateway.Core.Entity;

/// <summary>
/// </summary>
[SugarTable("dnc_file", "Dnc程序")]
public class DncFile : EntityBaseId
{
    /// <summary>
    /// 设备Id
    /// </summary>
    [SugarColumn(ColumnDescription = "设备Id", Length = 50)]
    public long DeviceId { get; set; }
    /// <summary>
    ///     文件名称
    /// </summary>
    [SugarColumn(ColumnDescription = "文件名称", Length = 50)]
    public string FileName { get; set; }

    /// <summary>
    ///     文件后缀
    /// </summary>
    [SugarColumn(ColumnDescription = "文件后缀", Length = 50)]
    public string FileSuffix { get; set; }

    /// <summary>
    ///     文件大小kb
    /// </summary>
    [SugarColumn(ColumnDescription = "文件大小kb", Length = 10)]
    public long FileSize { get; set; }

    /// <summary>
    ///     存储路径
    /// </summary>
    [SugarColumn(ColumnDescription = "存储路径", Length = 100)]
    public string FilePath { get; set; }

    /// <summary>
    ///     版本号
    /// </summary>
    [SugarColumn(ColumnDescription = "版本号")]
    public double Version { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间")]
    public DateTime CreatedTime { get; set; }
}