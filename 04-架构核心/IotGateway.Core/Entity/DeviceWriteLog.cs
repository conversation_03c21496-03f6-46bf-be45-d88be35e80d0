namespace Feng.IotGateway.Core.Entity;

/// <summary>
///     设备操作下写日志
/// </summary>
[SugarTable("device_writeLog", "设备操作下写日志")]
public class DeviceWriteLog : EntityBaseId
{
    /// <summary>
    ///     操作发起方
    /// </summary>
    [SugarColumn(ColumnDescription = "操作发起方")]
    public DeviceWriteSideEnum WriteSide { get; set; }

    /// <summary>
    ///     开始操作时间
    /// </summary>
    [SugarColumn(ColumnDescription = "开始时间")]
    public DateTime StartTime { get; set; }

    /// <summary>
    ///     操作设备
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(DeviceId))]
    public Device Device { get; set; }

    /// <summary>
    ///     操作设备ID
    /// </summary>
    [SugarColumn(ColumnDescription = "设备Id")]
    public long DeviceId { get; set; }

    /// <summary>
    ///     请求参数
    /// </summary>
    [SugarColumn(ColumnDescription = "请求参数")]
    public string Params { get; set; }

    /// <summary>
    ///     执行完毕时间
    /// </summary>
    [SugarColumn(ColumnDescription = "结束时间")]
    public DateTime EndTime { get; set; }

    /// <summary>
    ///     写入结果
    /// </summary>
    [SugarColumn(ColumnDescription = "结果")]
    public bool IsSuccess { get; set; }

    /// <summary>
    ///     描述信息
    /// </summary>
    [SugarColumn(ColumnDescription = "描述信息")]
    public string Description { get; set; }
}

/// <summary>
///     操作发起方
/// </summary>
public enum DeviceWriteSideEnum
{
    [Display(Name = "网关侧请求")] ClientSide = 1,
    [Display(Name = "服务端请求")] ServerSide = 2
}