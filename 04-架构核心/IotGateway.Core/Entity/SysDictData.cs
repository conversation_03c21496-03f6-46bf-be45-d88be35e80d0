namespace Feng.IotGateway.Core.Entity;

/// <summary>
///     字典值表
/// </summary>
[SugarTable("sys_dict_data", "字典值表")]
public class SysDictData : EntityBase
{
    /// <summary>
    ///     字典类型Id
    /// </summary>
    [SugarColumn(ColumnDescription = "字典类型Id")]
    public long DictTypeId { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    [SugarColumn(ColumnDescription = "值", Length = 50)]
    [Required]
    public string Value { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    [SugarColumn(ColumnDescription = "编码", Length = 64)]
    public string Code { get; set; }

    /// <summary>
    /// 输入框类型
    /// </summary>
    [SugarColumn(ColumnDescription = "输入框类型")]
    public string Type { get; set; } = "select";

    /// <summary>
    /// 是否必填
    /// </summary>
    [SugarColumn(ColumnDescription = "是否必填")]
    public bool Required { get; set; } = true;
    
    /// <summary>
    /// 是否显示
    /// </summary>
    [SugarColumn(ColumnDescription = "是否显示")]
    public bool Display { get; set; } = true;

    /// <summary>
    /// 字段显示条件表达式
    /// </summary>
    public string DisplayExpress { get; set; } = "";
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }
    
    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 500,IsNullable = true)]
    public string Remark { get; set; }
    
    /// <summary>
    ///     默认值
    /// </summary>
    [SugarColumn(ColumnDescription = "默认值",IsNullable = true)]
    public string DefaultValue { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    [SugarColumn(ColumnDescription = "排序")]
    public short Sort { get; set; } = 0;
    
    /// <summary>
    ///     状态
    /// </summary>
    [SugarColumn(ColumnDescription = "启用状态")]
    public bool Enable { get; set; } = true;
}