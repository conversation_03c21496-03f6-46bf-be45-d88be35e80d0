namespace Feng.IotGateway.Core.Entity;

/// <summary>
///     设备事件表
/// </summary>
[SugarTable("device_eventLog", "设备事件表")]
public class DeviceEventLog : EntityBaseId
{
    /// <summary>
    ///     设备Id
    /// </summary>
    [SugarColumn(ColumnDescription = "设备Id")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备事件Id
    /// </summary>
    [SugarColumn(ColumnDescription = "设备事件Id",IsNullable = true)]
    public long DeviceEventId { get; set; }
    
    /// <summary>
    ///     设备
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(DeviceId))]
    public Device Device { get; set; }

    /// <summary>
    ///     事件标题
    /// </summary>
    [SugarColumn(ColumnDescription = "事件标题")]
    public string Title { get; set; }

    /// <summary>
    ///     事件类型
    /// </summary>
    [SugarColumn(ColumnDescription = "事件类型")]
    public string EventType { get; set; }

    /// <summary>
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", ColumnDataType = "longtext,text,clob")]
    public string? Description { get; set; }

    /// <summary>
    ///     发生时间
    /// </summary>
    [SugarColumn(ColumnDescription = "发生时间")]
    public DateTime CreatedTime { get; set; }
}