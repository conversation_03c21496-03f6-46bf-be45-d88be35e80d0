using Newtonsoft.Json;

namespace Feng.IotGateway.Core.Entity;

/// <summary>
/// </summary>
[SugarTable("device", "设备表")]
public class Device : EntityBase
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 128)]
    public string DeviceName { get; set; }

    /// <summary>
    ///     别名
    /// </summary>
    [SugarColumn(ColumnDescription = "别名", Length = 128)]
    public string? OtherName { get; set; }

    /// <summary>
    ///     采集数据上报规则：1根据属性实际配置进行上报；2任一属性变化上报整组数据；3仅上报变化属性；4设备不上报数据
    /// </summary>
    public DataCollectionReportingRuleEnum DataCollectionReportingRule { get; set; } = DataCollectionReportingRuleEnum.ReportAccordingToActualAttributeConfiguration;

    /// <summary>
    ///     排序
    /// </summary>
    [SugarColumn(ColumnDescription = "排序")]
    public uint Index { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", ColumnDataType = "longtext,text,clob")]
    public string? Description { get; set; }

    /// <summary>
    ///     驱动Id
    /// </summary>
    [SugarColumn(ColumnDescription = "驱动Id")]
    public long DriverId { get; set; }

    /// <summary>
    ///     设备基本信息
    /// </summary>
    [SugarColumn(ColumnDescription = "设备基本信息", IsJson = true)]
    public Dictionary<string, string> DeviceInfo { get; set; } = new();

    /// <summary>
    ///     设备基本扩展信息
    /// </summary>
    [SugarColumn(ColumnDescription = "设备基本扩展信息", IsJson = true)]
    public List<DeviceInfoExtension> DeviceInfoExtensions { get; set; } = new();

    /// <summary>
    ///     是否启动
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启动")]
    public bool Enable { get; set; }

    /// <summary>
    ///     发布
    /// </summary>
    [SugarColumn(ColumnDescription = "发布")]
    public bool Release { get; set; }

    /// <summary>
    /// 设备分组Id
    /// </summary>
    [SugarColumn(ColumnDescription = "设备分组Id",IsNullable = true)]
    public long DeviceGroupId { get; set; }
    
    #region 关联表

    /// <summary>
    ///     采集协议
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(DriverId))]
    public Driver? Driver { get; set; }

    /// <summary>
    ///     设备配置
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(DeviceConfig.DeviceId))]
    [SugarColumn(IsIgnore = true)]
    public List<DeviceConfig> DeviceConfigs { get; set; }

    /// <summary>
    ///     设备属性
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Entity.DeviceVariable.DeviceId))]
    [SugarColumn(IsIgnore = true)]
    public List<DeviceVariable> DeviceVariable { get; set; }

    /// <summary>
    ///     设备事件日志
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Entity.DeviceEventLog.DeviceId))]
    [SugarColumn(IsIgnore = true)]
    public List<DeviceEventLog> DeviceEventLog { get; set; }

    /// <summary>
    ///     设备事件
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Entity.DeviceEvent.DeviceId))]
    [SugarColumn(IsIgnore = true)]
    public List<DeviceEvent> DeviceEvent { get; set; }

    #endregion 关联表

    #region 忽略字段

    /// <summary>
    ///     采集数据上报规则
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string DataCollectionReportingRuleName => EnumUtil.GetEnumDesc(DataCollectionReportingRule);

    /// <summary>
    ///     协议名称
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string DriverName => Driver?.DriverName ?? "";

    /// <summary>
    ///     连接地址
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string IpAddress { get; set; }

    /// <summary>
    ///     是否连接
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public bool IsConnect { get; set; }

    /// <summary>
    ///     属性已经全部发布
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public bool ReleaseAll { get; set; }

    #endregion
}

/// <summary>
///     设备基本扩展信息
/// </summary>
public class DeviceInfoExtension
{
    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string DisplayName { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public string Value { get; set; }
}

/// <summary>
///     采集数据上报规则：1根据属性实际配置进行上报；2任一属性变化上报整组数据；3仅上报变化属性；4设备不上报数据
/// </summary>
public enum DataCollectionReportingRuleEnum
{
    /// <summary>
    ///     根据属性实际配置进行上报
    /// </summary>
    [Description("根据属性实际配置进行上报")] ReportAccordingToActualAttributeConfiguration = 1,

    /// <summary>
    ///     任一属性变化上报整组数据
    /// </summary>
    [Description("任一属性变化上报整组数据")] ReportWholeGroupDataWhenAnyAttributeChanges = 2,

    /// <summary>
    ///     仅上报变化属性
    /// </summary>
    [Description("仅上报变化属性")] ReportOnlyChangedAttributes = 3,

    /// <summary>
    ///     设备不上报数据
    /// </summary>
    [Description("设备不上报数据")] DeviceDoesNotReportData = 4,
    
    /// <summary>
    ///     根据属性实际配置进行上报(离线后仅上报一次数据)
    /// </summary>
    [Description("根据属性实际配置进行上报(离线后仅上报一次数据)")] ReportAccordingToActualAttributeConfigurationTestamentaryMessage = 5,
}