using Feng.IotGateway.Core.Attribute;

namespace Feng.IotGateway.Core.Entity;

/// <summary>
///     系统参数配置表
/// </summary>
[SugarTable(null, "系统参数配置表")]
[SugarIndex("index_{table}_N", nameof(Name), OrderByType.Asc)]
[SugarIndex("index_{table}_C", nameof(Code), OrderByType.Asc, IsUnique = true)]
public class SysConfig : EntityBaseId
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    [Required]
    [MaxLength(64)]
    public virtual string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    [SugarColumn(ColumnDescription = "编码", Length = 64)]
    [MaxLength(64)]
    public string? Code { get; set; }

    /// <summary>
    ///     属性值
    /// </summary>
    [SugarColumn(ColumnDescription = "属性值", Length = 64)]
    [MaxLength(64)]
    [IgnoreUpdateSeedColumn]
    public string? Value { get; set; }
}