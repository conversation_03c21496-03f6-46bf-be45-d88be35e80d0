namespace Feng.IotGateway.Core.Entity;

/// <summary>
/// </summary>
[SugarTable("device_config", "设备配置表")]
public class DeviceConfig : EntityBaseId
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称")]
    public string DeviceConfigName { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述")]
    public string? Description { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    [SugarColumn(ColumnDescription = "值")]
    public string Value { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注")]
    public string? EnumInfo { get; set; }

    /// <summary>
    ///     设备Id
    /// </summary>
    [SugarColumn(ColumnDescription = "设备Id")]
    public long DeviceId { get; set; }

    /// <summary>
    ///     设备
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(DeviceId))]
    public Device Device { get; set; }

    /// <summary>
    ///     分组名称
    /// </summary>
    [SugarColumn(ColumnDescription = "分组名称", IsNullable = true)]
    public string? GroupName { get; set; } = "连接配置";

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", IsNullable = true)]
    public string Remark { get; set; }

    /// <summary>
    ///     是否是必填
    /// </summary>
    [SugarColumn(ColumnDescription = "是否是必填", IsNullable = true)]
    public bool IsRequired { get; set; } = true;

    /// <summary>
    ///     是否显示
    /// </summary>
    [SugarColumn(ColumnDescription = "是否显示", IsNullable = true)]
    public bool Display { get; set; }

    /// <summary>
    ///     字段显示条件表达式
    /// </summary>
    [SugarColumn(ColumnDescription = "字段显示条件表达式", IsNullable = true)]
    public string DisplayExpress { get; set; }

    /// <summary>
    ///     排序,越小越在前
    /// </summary>
    [SugarColumn(ColumnDescription = "排序,越小越在前", IsNullable = true)]
    public short Order { get; set; }

    /// <summary>
    ///     输入框类型
    /// </summary>
    [SugarColumn(ColumnDescription = "输入框类型")]
    public string Type { get; set; } = "text";
}