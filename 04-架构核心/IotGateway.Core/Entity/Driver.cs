using Common.Enums;

namespace Feng.IotGateway.Core.Entity;

/// <summary>
///     采集协议表
/// </summary>
[SugarTable("driver", "采集协议表")]
public class Driver : EntityBaseId
{
    /// <summary>
    ///     驱动名
    /// </summary>
    [SugarColumn(ColumnDescription = "驱动名")]
    [Description("驱动名")]
    public string DriverName { get; set; }

    /// <summary>
    ///     文件名
    /// </summary>
    [SugarColumn(ColumnDescription = "文件名")]
    [Description("文件名")]
    public string FileName { get; set; }

    /// <summary>
    /// </summary>
    [SugarColumn(ColumnDescription = "程序集名")]
    [Description("程序集名")]
    public string AssembleName { get; set; }

    /// <summary>
    ///     协议类型
    /// </summary>
    [SugarColumn(ColumnDescription = "协议类型")]
    [Description("协议类型")]
    public DriverTypeEnum DriverType { get; set; } = DriverTypeEnum.Cnc;

    /// <summary>
    ///     连接方式1:网口;2:串口：3：udp
    /// </summary>
    [SugarColumn(ColumnDescription = "连接方式1:网口;2:串口：3：udp")]
    [Description("连接方式1:网口;2:串口：3：udp")]
    public ConnectTypeEnum ConnectType { get; set; } = ConnectTypeEnum.NetWork;

    /// <summary>
    /// </summary>
    [SugarColumn(ColumnDescription = "剩余授权数量")]
    [Description("剩余授权数量")]
    public int AuthorizesNum { get; set; } = 10;

    /// <summary>
    /// 驱动厂商
    /// </summary>
    [SugarColumn(ColumnDescription = "驱动厂商")]
    [Description("驱动厂商")]
    public string? DriveManufacturer { get; set; }
}

/// <summary>
/// </summary>
public enum DriverTypeEnum
{
    [Description("自由协议")]
    [Display(Name = "自由协议")]
    Free = 0,

    [Description("Cnc")]
    [Display(Name = "Cnc")]
    Cnc = 1,

    [Description("Plc")]
    [Display(Name = "Plc")]
    Plc = 2
}

/// <summary>
///     协议Id
/// </summary>
public static class DriverId
{
    public const long Fanuc = 1423070709139001;
    public const long Fanuc18i = 1423070709139002;
    public const long FanucMultiStation = 1423070709139003;
    public const long FanucTtc = 1523070709139040;
    public const long SyntecV4 = 1423070709139005;
    public const long SyntecV2 = 1423070709139006;
    public const long SyntecV3 = 1423070709139007;
    public const long Syntec6AE = 1523070709139018;
    public const long Syntec6MA = 1523070709139045;
    public const long Gsk25im = 1423070709139009;
    public const long GskTcp = 1423070709139010;
    public const long GskUdp = 1423070709139011;
    public const long Knd = 1423070709139012;
    public const long ModbusTcp = 1423070709139000;
    public const long ModbusRtu = 1523070709139000;
    public const long ModbusAscii = 1523070709139003;
    public const long ModbusRtuOverTcp = 1523070709139019;
    public const long ModbusAsciiOverTcp = 1523070709139020;
    public const long ModbusUdpNet = 1523070709139021;
    public const long DeltaTcp = 1523070709139001;
    public const long DeltaSerialAscii = 1523070709139002;
    public const long Mitsub = 1423070709139008;
    public const long McA1E = 1523070709139004;
    public const long McFxLinks = 1523070709139005;
    public const long McQna3E = 1523070709139006;
    public const long McUdp = 1523070709139007;
    public const long MelsecA1EAscii = 1523070709139041;
    public const long AllenBradleyCip = 1523070709139008;
    public const long FinsNet = 1523070709139009;
    public const long FinsUdp = 1523070709139010;
    public const long CipNet = 1523070709139011;
    public const long HostLink = 1523070709139034;
    public const long HostLinkOverTcp = 1523070709139035;
    public const long MewtocolOverTcp = 1523070709139012;
    public const long Siemens = 1423070709139004;
    public const long SiemensS7 = 1523070709139013;
    public const long SiemensPPI = 1523070709139014;
    public const long OpcUaClient = 1523070709139015;
    public const long MtConnectClient = 1523070709139016;
    public const long TcpClient = 1523070709139026;
    public const long XinJETcpNet = 1523070709139022; //XinJE TcpNet(ModBus)
    public const long XinJESerialOverTcp = 1523070709139023;
    public const long XinJESerial = 1523070709139024;
    public const long XinJeTcpNet = 1523070709139025; //XinJE TcpNet(专用)
    public const long ProgramOverTcp = 1523070709139027;
    public const long Dlt645 = 1523070709139028;
    public const long Dlt645OverTcp = 1523070709139029;
    public const long Dlt698 = 1523070709139030;
    public const long Dlt698OverTcp = 1523070709139031;
    public const long Dlt698TcpNet = 1523070709139032;
    public const long InovanceTcp = 1523070709139033;
    public const long MazakMatrix = 1523070709139036;
    public const long MazakSmooth = 1523070709139037;
    public const long MazakSmart = 1523070709139044;
    public const long Snmp = 1523070709139038;
    public const long Heidenhain = 1523070709139039;
    public const long LncTcp = 1523070709139042;
    public const long MqttServer = 1523070709139043;
    public const long FagorNet = 1523070709139046;
    public const long Hnc = 1523070709139047;
    public const long OpcDa = 1523070709139048;
    public const long VirtualProtocol = 1523070709139049;
    public const long AllenBradleyPccc = 1523070709139050;
    public const long PORCHESON_Ps660Bm = 1523070709139051;
    public const long TechMationAk = 1523070709139052;
    public const long DeltaSerialAsciiOverTcp = 1523070709139053;
    public const long PORCHESON_Ps660Am = 1523070709139054;
    public const long McNet = 1523070709139055;
    public const long McAsciiNet = 1523070709139056;
    public const long NanoSerial = 1523070709139057;
    public const long NanoSerialOverTcp = 1523070709139058;
    public const long SerialClient = 1523070709139059;
    public const long KeQiangT6H3 = 1523070709139060;
    public const long KeQiangT6F3 = 1523070709139061;
    // public const long Excel = 1523070709139062;
    public const long MqttServerEx = 1523070709139063;
    public const long WarpingMachine = 1523070709139064;
    public const long TechMation5521 = 1523070709139065;
    public const long Brother = 1523070709139066;
    public const long HongXunAkDouble = 1523070709139067;
    public const long McFxSerial = 1523070709139068;
    public const long Kede = 1523070709139069;

    //该使用 1523070709139070
}