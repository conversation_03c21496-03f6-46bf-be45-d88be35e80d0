namespace Feng.IotGateway.Core.Entity;

/// <summary>
///     设备事件模板
/// </summary>
[SugarTable("device_eventTemplate", "设备事件模板")]
public class DeviceEventTemplate : EntityBaseId
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     描述信息
    /// </summary>
    [SugarColumn(ColumnDescription = "描述信息", Length = 256)]
    public string Description { get; set; }

    /// <summary>
    ///     Icon
    /// </summary>
    [SugarColumn(ColumnDescription = "Icon", IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string Icon { get; set; }

    /// <summary>
    /// </summary>
    public string Env { get; set; }

    #region 关联表

    /// <summary>
    ///     事件模板配置
    /// </summary>
    [SugarColumn(IsJson = true)]
    public List<DeviceEvent> DeviceEvents { get; set; }

    #endregion
}