namespace Feng.IotGateway.Core.Entity;

/// <summary>
///     字典类型表
/// </summary>
[SugarTable("sys_dict_type", "字典类型表")]
public class SysDictType : EntityBase
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 50)]
    [MaxLength(50)]
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    [Required]
    [MaxLength(50)]
    [SugarColumn(ColumnDescription = "编码", Length = 50)]
    public string Code { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    [SugarColumn(ColumnDescription = "排序")]
    public short Sort { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [MaxLength(500)]
    [SugarColumn(ColumnDescription = "备注", Length = 500)]
    public string Remark { get; set; }

    /// <summary>
    ///     启用状态
    /// </summary>
    [SugarColumn(ColumnDescription = "启用状态")]
    public bool Enable { get; set; } = true;

    /// <summary>
    ///     是否是系统字典（系统字典不允许删除）
    /// </summary>
    [SugarColumn(ColumnDescription = "是否是系统字典（系统字典不允许删除）")]
    public bool System { get; set; }
}