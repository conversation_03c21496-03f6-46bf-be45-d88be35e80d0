namespace Feng.IotGateway.Core.Entity;

/// <summary>
///     框架实体基类Id
/// </summary>
public class EntityBaseId
{
    /// <summary>
    ///     雪花Id
    /// </summary>
    [SugarColumn(ColumnDescription = "Id", IsPrimaryKey = true, IsIdentity = false)]
    public long Id { get; set; }
}

/// <summary>
///     框架实体基类
/// </summary>
public class EntityBase : EntityBaseId
{
    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间",IsOnlyIgnoreUpdate = true)]
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    [SugarColumn(ColumnDescription = "更新时间", IsOnlyIgnoreInsert = true)]
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    ///     创建者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "创建者Id",IsOnlyIgnoreUpdate = true)]
    public long? CreatedUserId { get; set; }

    /// <summary>
    ///     修改者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "修改者Id", IsOnlyIgnoreInsert = true)]
    public long? UpdatedUserId { get; set; }

    /// <summary>
    ///     创建者名称
    /// </summary>
    [SugarColumn(ColumnDescription = "创建者名称",IsOnlyIgnoreUpdate = true,IsNullable = true)]
    public string? CreatedUserName { get; set; }

    /// <summary>
    ///     修改者名称
    /// </summary>
    [SugarColumn(ColumnDescription = "修改者名称", IsOnlyIgnoreInsert = true,IsNullable = true)]
    public string? UpdatedUserName { get; set; }
}