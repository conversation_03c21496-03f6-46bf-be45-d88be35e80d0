using System.ComponentModel;
using Feng.IotGateway.Core.Models;
using Furion.JsonSerialization;

namespace IotGateway.Application.Entity;

/// <summary>
///     上行转发表
/// </summary>
[SugarTable("transPond", "上行转发表")]
public class TransPond : EntityBase
{
    /// <summary>
    ///     标识符
    /// </summary>
    [SugarColumn(ColumnDescription = "标识符")]
    public string Identifier { get; set; }

    /// <summary>
    ///     转发类型
    /// </summary>
    [SugarColumn(ColumnDescription = "转发类型;1:MQTT;2:HTTP;3:SQL;")]
    public TransPondTypeEnum TransPondType { get; set; }

    /// <summary>
    ///     转发配置
    /// </summary>
    [SugarColumn(ColumnDescription = "转发配置")]
    public string Config { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool Enable { get; set; }

    /// <summary>
    ///     主节点
    /// </summary>
    [SugarColumn(ColumnDescription = "主节点")]
    public bool Master { get; set; }

    /// <summary>
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Entity.TransPondTopic.TransPondId))]
    [SugarColumn(IsIgnore = true)]
    public List<TransPondTopic> TransPondTopic { get; set; }

    #region 忽略实体

    /// <summary>
    ///     Mqtt配置实体
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public MqttConfModel? MqttConfModel => TransPondType == TransPondTypeEnum.Mqtt ? JSON.Deserialize<MqttConfModel>(Config) : null;

    /// <summary>
    ///     Sql配置实体
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public SqlConfModel? SqlConfModel => TransPondType == TransPondTypeEnum.Sql ? JSON.Deserialize<SqlConfModel>(Config) : null;

    /// <summary>
    ///     Http转发配置实体
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public HttpConfModel? HttpConfModel => TransPondType == TransPondTypeEnum.Http ? JSON.Deserialize<HttpConfModel>(Config) : null;

    /// <summary>
    ///     OpcUa配置实体
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public OpcUaConfModel? OpcUaConfModel => TransPondType == TransPondTypeEnum.OpcUa ? JSON.Deserialize<OpcUaConfModel>(Config) : null;

    /// <summary>
    ///     连接状态
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public bool IsConnected { get; set; }

    #endregion 忽略实体
}

/// <summary>
///     转发类型
/// </summary>
public enum TransPondTypeEnum
{
    [Description("MQTT")] [Display(Name = "MQTT")]
    Mqtt = 1,

    [Description("HTTP")] [Display(Name = "HTTP")]
    Http = 2,

    [Description("SQL")] [Display(Name = "SQL")]
    Sql = 3,

    [Description("OPC UA")] [Display(Name = "OPC UA")]
    OpcUa = 4
}