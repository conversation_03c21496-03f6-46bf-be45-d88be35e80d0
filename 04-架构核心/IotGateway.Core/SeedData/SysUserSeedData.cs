namespace Feng.IotGateway.Core.SeedData;

public class SysUserSeedData : ISqlSugarEntitySeedData<SysUser>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysUser> HasData()
    {
        yield return new SysUser
        {
            Id = ***************, Account = "superAdmin", Name = "超级管理员", Password = "c246266016e1e235aa62adf75ea29e18", AdminType = AdminTypeEnum.SuperAdmin, Status = true
        };
        yield return new SysUser
        {
            //e2bccc591468baebad4f937577f35261  正式密码
            Id = ***************, Account = "admin", Name = "管理员", Password = "e10adc3949ba59abbe56e057f20f883e", AdminType = AdminTypeEnum.Admin, Status = true
        };
    }
}