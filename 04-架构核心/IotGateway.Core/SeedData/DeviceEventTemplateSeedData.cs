// namespace Feng.IotGateway.Core.SeedData;
//
// /// <summary>
// ///     设备事件模板
// /// </summary>
// public class DeviceEventTemplateSeedData : ISqlSugarEntitySeedData<DeviceEventTemplate>
// {
//     public IEnumerable<DeviceEventTemplate> HasData()
//     {
//         return new[]
//         {
//             new DeviceEventTemplate
//             {
//                 Id = 15384939579461,
//                 Description = "设备连接成功",
//                 Env = "",
//                 Icon = "",
//                 Name = "",
//                 DeviceEvents = new List<DeviceEvent>
//                 {
//                     new()
//                     {
//                         EventName = "设备连接成功",
//                         Status = true,
//                         TriggerEventType = TriggerEventTypeEnum.连接成功触发,
//                         CustomEventAction = new List<CustomEventAction>
//                         {
//                             new()
//                         }
//                     }
//                 }
//             }
//         };
//     }
// }