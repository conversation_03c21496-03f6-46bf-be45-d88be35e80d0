using DateTime = Common.Extension.DateTime;

namespace Feng.IotGateway.Core.BackgroundServices;

/// <summary>
///     看门狗定时喂狗
/// </summary>
public class WatchdogTimer : BackgroundService
{
    private readonly ILogger<WatchdogTimer> _logger;
    private readonly Crontab _crontab;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    public WatchdogTimer(ILogger<WatchdogTimer> logger)
    {
        _logger = logger;
        _crontab = Crontab.Parse("0/10 * * ? * *", CronStringFormat.WithSeconds);
    }

    /// <summary>
    /// </summary>
    public override void Dispose()
    {
        // 关闭看门狗
        File.WriteAllText("/Edge/watchdog.txt", DESEncryption.Encrypt("STOP", "Feng-Dog"));
    }

    /// <summary>
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var taskFactory = new TaskFactory(TaskScheduler.Current);
            await taskFactory.StartNew(async () =>
            {
                try
                {
                    // 定时喂狗
                    await File.WriteAllTextAsync("/Edge/watchdog.txt", DESEncryption.Encrypt("ONLINE", "Feng-Dog"), stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"【喂狗】 Error:【{ex.Message}】");
                }
            }, stoppingToken);
            await Task.Delay((int)_crontab.GetSleepMilliseconds(DateTime.Now()), stoppingToken);
        }
    }
}