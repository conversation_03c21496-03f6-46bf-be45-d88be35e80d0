using DateTime = Common.Extension.DateTime;

namespace Feng.IotGateway.Core.BackgroundServices;

/// <summary>
///     释放linux缓存
/// </summary>
public class DropCachesTimer : BackgroundService
{
    private readonly ILogger<DropCachesTimer> _logger;
    private readonly Crontab _crontab;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    public DropCachesTimer(ILogger<DropCachesTimer> logger)
    {
        _logger = logger;
        _crontab = Crontab.Parse("00 0/05 * ? * *", CronStringFormat.WithSeconds);
    }

    /// <summary>
    ///     释放Linux-Cache占用
    /// </summary>
    /// <param name="stoppingToken"></param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var taskFactory = new TaskFactory(TaskScheduler.Current);
            await taskFactory.StartNew(async () =>
            {
                try
                {
                    // 首先检查可用内存
                    var memoryInfo = await GetSystemMemoryInfo();

                    // 当可用内存低于某个阈值时（例如20%）才执行清理
                    if (memoryInfo.AvailablePercent < 20)
                    {
                        // 同步文件系统缓存
                        await ShellUtil.Bash("sync");

                        // 使用sysctl命令代替直接echo
                        await ShellUtil.Bash("sysctl -w vm.drop_caches=1");

                        _logger.LogInformation("由于可用内存不足，执行了缓存清理");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"内存缓存管理操作失败：【{ex.Message}】");
                }
            }, stoppingToken);

            await Task.Delay((int)_crontab.GetSleepMilliseconds(DateTime.Now()), stoppingToken);
        }
    }

    /// <summary>
    /// 获取系统内存信息
    /// </summary>
    /// <returns></returns>
    private async Task<(double TotalMb, double AvailableMb, double AvailablePercent)> GetSystemMemoryInfo()
    {
        try
        {
            // 获取内存信息
            var meminfo = await File.ReadAllTextAsync("/proc/meminfo");
            // 分割行
            var lines = meminfo.Split('\n');
            // 初始化内存值
            double totalKb = 0;
            // 可用内存 
            double availableKb = 0;
            // 遍历行
            foreach (var line in lines)
            {
                // 如果行以MemTotal:开头
                if (line.StartsWith("MemTotal:"))
                {
                    // 提取内存值
                    totalKb = ExtractMemoryValue(line);
                }
                // 如果行以MemAvailable:开头
                else if (line.StartsWith("MemAvailable:"))
                {
                    // 提取内存值
                    availableKb = ExtractMemoryValue(line);
                }
                // 如果内存值大于0
                if (totalKb > 0 && availableKb > 0)
                {
                    // 跳出循环
                    break;
                }
            }

            // 转换为MB
            var totalMb = totalKb / 1024.0;
            var availableMb = availableKb / 1024.0;

            // 计算可用内存百分比
            var availablePercent = totalMb > 0 ? (availableMb / totalMb) * 100 : 0;

            _logger.LogInformation($"系统内存状态 - 总内存: {totalMb:F2}MB, 可用内存: {availableMb:F2}MB, 可用比例: {availablePercent:F2}%");

            return (totalMb, availableMb, availablePercent);
        }
        catch (Exception ex)
        {
            _logger.LogError($"读取系统内存信息失败: {ex.Message}");
            return (0, 0, 0);
        }
    }

    /// <summary>
    /// 从meminfo的行中提取内存值（KB）
    /// </summary>
    private double ExtractMemoryValue(string line)
    {
        try
        {
            // 格式类似于: "MemTotal:       16384000 kB"
            var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length >= 2)
            {
                return double.Parse(parts[1]);
            }
        }
        catch
        {
            _logger.LogWarning($"解析内存信息行失败: {line}");
        }
        return 0;
    }
}