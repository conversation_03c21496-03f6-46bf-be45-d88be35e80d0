global using Furion;
global using Furion.DataValidation;
global using Furion.DependencyInjection;
global using Furion.FriendlyException;
global using SqlSugar;
global using System.ComponentModel.DataAnnotations;
global using Furion.ConfigurableOptions;
global using System.ComponentModel;
global using System;
global using System.Data;
global using System.Collections.Generic;
global using System.Linq;
global using System.Reflection;
global using Feng.IotGateway.Core.Enums;
global using System.Diagnostics;
global using System.Net.NetworkInformation;
global using System.Runtime.InteropServices;
global using System.Text;
global using System.Threading.Tasks;
global using Furion.Logging;
global using Microsoft.Extensions.DependencyInjection;
global using Feng.IotGateway.Core.Const;
global using Furion.DynamicApiController;
global using Furion.JsonSerialization;
global using Microsoft.AspNetCore.Mvc;
global using Feng.Common.Util;
global using Feng.IotGateway.Core.Extension;
global using MQTTnet.Protocol;
global using Feng.IotGateway.Core.Entity;
global using Feng.IotGateway.Core.SqlSugar;
global using Yitter.IdGenerator;
global using Feng.IotGateway.Core.Option;
global using Mapster;
global using Furion.DataEncryption;
global using Microsoft.AspNetCore.Http;
global using System.IO;
global using System.Threading;
global using Furion.TimeCrontab;
global using Microsoft.Extensions.Hosting;
global using Microsoft.Extensions.Logging;
global using Feng.IotGateway.Core.Util;
global using Feng.IotGateway.Core.Base;
global using Feng.IotGateway.Core.Service.Cache;
global using Feng.IotGateway.Core.Service.User;