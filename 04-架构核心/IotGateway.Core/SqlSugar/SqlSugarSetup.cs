using System.Collections;
using Feng.IotGateway.Core.Attribute;
using DateTime = Common.Extension.DateTime;
using System.Text;
using System.Diagnostics;

namespace Feng.IotGateway.Core.SqlSugar;

public static class SqlSugarSetup
{
    /// <summary>
    ///     SqlSugar 上下文初始化
    /// </summary>
    /// <param name="services"></param>
    public static void AddSqlSugar(this IServiceCollection services)
    {
        var sw = new Stopwatch();
        sw.Start();
        Console.WriteLine("┌──────────────────────────────────────────────────┐");
        Console.WriteLine($"│            开始配置 SqlSugar...                │");
        Console.WriteLine("└──────────────────────────────────────────────────┘");

        // 自定义 SqlSugar 雪花ID算法
        StaticConfig.CustomSnowFlakeFunc = YitIdHelper.NextId;

        sw.Restart();
        // 获取数据库连接选项
        var dbOptions = App.GetOptions<DbConnectionOptions>();
        // 设置数据库连接配置
        dbOptions.ConnectionConfigs.ForEach(SetDbConfig);
        Console.WriteLine($"数据库连接配置完成 (耗时: {sw.ElapsedMilliseconds}ms)");

        sw.Restart();
        // 创建SqlSugarScope
        SqlSugarScope sqlSugar = new(dbOptions.ConnectionConfigs.Adapt<List<ConnectionConfig>>(), db =>
        {
            // 设置数据库连接配置
            dbOptions.ConnectionConfigs.ForEach(SetDbConfig);
            // 设置数据库Aop
            dbOptions.ConnectionConfigs.ForEach(config =>
            {
                var dbProvider = db.GetConnectionScope(config.ConfigId);
                SetDbAop(dbProvider, dbOptions.EnableConsoleSql);
            });
        });
        Console.WriteLine($"SqlSugarScope 创建完成 (耗时: {sw.ElapsedMilliseconds}ms)");

        sw.Restart();
        // 单例注册
        services.AddSingleton<ISqlSugarClient>(sqlSugar);
        // 仓储注册
        services.AddScoped(typeof(SqlSugarRepository<>));
        // 事务与工作单元注册
        services.AddUnitOfWork<SqlSugarUnitOfWork>();
        Console.WriteLine($"依赖注入注册完成 (耗时: {sw.ElapsedMilliseconds}ms)");

        sw.Restart();
        // 初始化数据库表结构及种子数据
        dbOptions.ConnectionConfigs.ForEach(config =>
        {
            var configSw = new Stopwatch();
            configSw.Start();
            InitDatabase(sqlSugar, config);
            Console.WriteLine($"数据库 {config.ConfigId} 初始化完成 (耗时: {configSw.ElapsedMilliseconds}ms)");
        });
        Console.WriteLine($"所有数据库初始化完成 (耗时: {sw.ElapsedMilliseconds}ms)");

        Console.WriteLine("┌──────────────────────────────────────────────────┐");
        Console.WriteLine($"│        SqlSugar 配置完成 (总耗时: {sw.ElapsedMilliseconds}ms)     │");
        Console.WriteLine("└──────────────────────────────────────────────────┘");
    }

    /// <summary>
    ///     获取当前内存使用情况
    /// </summary>
    /// <returns>当前使用的内存（MB）</returns>
    private static double GetCurrentMemoryUsage()
    {
        // 强制GC收集，确保获取准确的内存使用情况
        GC.Collect();
        GC.WaitForPendingFinalizers();

        // 获取当前进程的内存使用情况
        var process = Process.GetCurrentProcess();
        var memoryInBytes = process.WorkingSet64;
        return Math.Round(memoryInBytes / (1024.0 * 1024.0), 2); // 转换为MB并保留两位小数
    }

    /// <summary>
    ///     配置连接属性
    /// </summary>
    /// <param name="config"></param>
    public static void SetDbConfig(DbConnectionConfig config)
    {
        var configureExternalServices = new ConfigureExternalServices
        {
            EntityNameService = (type, entity) => // 处理表
            {
                entity.IsDisabledDelete = true; // 禁止删除非 sqlsugar 创建的列
                // 只处理贴了特性[SugarTable]表
                if (!type.GetCustomAttributes<SugarTable>().Any())
                    return;
            },
            EntityService = (type, column) => // 处理列
            {
                // 只处理贴了特性[SugarColumn]列
                if (!type.GetCustomAttributes<SugarColumn>().Any())
                    return;
                if (new NullabilityInfoContext().Create(type).WriteState is NullabilityState.Nullable)
                    column.IsNullable = true;
            }
        };
        config.ConfigureExternalServices = configureExternalServices;
        config.InitKeyType = InitKeyType.Attribute;
        config.IsAutoCloseConnection = true;
        config.MoreSettings = new ConnMoreSettings
        {
            SqliteCodeFirstEnableDropColumn = true,
            SqliteCodeFirstEnableDescription = true,
            SqliteCodeFirstEnableDefaultValue = true,
            DisableNvarchar = true,
            IsAutoRemoveDataCache = true,
            IsAutoDeleteQueryFilter = true, // 启用删除查询过滤器
            IsAutoUpdateQueryFilter = true, // 启用更新查询过滤器
            SqlServerCodeFirstNvarchar = true // 采用Nvarchar
        };
    }

    /// <summary>
    ///     配置Aop
    /// </summary>
    /// <param name="db"></param>
    /// <param name="enableConsoleSql"></param>
    public static void SetDbAop(SqlSugarScopeProvider db, bool enableConsoleSql)
    {
        var config = db.CurrentConnectionConfig;

        // 设置超时时间
        db.Ado.CommandTimeOut = 30;

        // 打印SQL语句
        if (enableConsoleSql)
        {
            db.Aop.OnLogExecuting = (sql, pars) =>
            {
                var originColor = Console.ForegroundColor;
                if (sql.StartsWith("SELECT", StringComparison.OrdinalIgnoreCase))
                    Console.ForegroundColor = ConsoleColor.Green;
                if (sql.StartsWith("UPDATE", StringComparison.OrdinalIgnoreCase) || sql.StartsWith("INSERT", StringComparison.OrdinalIgnoreCase))
                    Console.ForegroundColor = ConsoleColor.Yellow;
                if (sql.StartsWith("DELETE", StringComparison.OrdinalIgnoreCase))
                    Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine("【" + DateTime.Now + "——执行SQL】\r\n" + UtilMethods.GetSqlString(config.DbType, sql, pars) + "\r\n");
                Console.ForegroundColor = originColor;
                App.PrintToMiniProfiler("SqlSugar", "Info", sql + "\r\n" + db.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value)));
            };
            db.Aop.OnError = ex =>
            {
                if (ex.Message.Contains("database is locked"))
                {
                    // 数据库锁定时的重试逻辑
                    var retryCount = 3;
                    var retryDelay = 1000; // 1秒

                    while (retryCount > 0)
                    {
                        try
                        {
                            Thread.Sleep(retryDelay);
                            // 重新执行命令
                            db.Ado.ExecuteCommand(ex.Sql, (SugarParameter[])ex.Parametres);
                            return;
                        }
                        catch
                        {
                            retryCount--;
                            retryDelay *= 2; // 指数退避
                        }
                    }
                }

                if (ex.Parametres == null) return;
                var originColor = Console.ForegroundColor;
                Console.ForegroundColor = ConsoleColor.DarkRed;
                var pars = db.Utilities.SerializeObject(((SugarParameter[])ex.Parametres).ToDictionary(it => it.ParameterName, it => it.Value));
                Console.WriteLine("【" + DateTime.Now + "——错误SQL】\r\n" + UtilMethods.GetSqlString(config.DbType, ex.Sql, (SugarParameter[])ex.Parametres) + "\r\n");
                Console.ForegroundColor = originColor;
                App.PrintToMiniProfiler("SqlSugar", "Error", $"{ex.Message}{Environment.NewLine}{ex.Sql}{pars}{Environment.NewLine}");
            };
            db.Aop.OnLogExecuted = (sql, pars) =>
            {
                var executionTime = db.Ado.SqlExecutionTime;
                var originColor = Console.ForegroundColor;

                // 输出执行时间信息
                Console.WriteLine($"【SQL执行耗时】：{executionTime.TotalMilliseconds:F2}ms");

                // 如果执行时间超过5秒，输出详细的堆栈信息
                if (executionTime.TotalSeconds > 5)
                {
                    Console.ForegroundColor = ConsoleColor.DarkYellow;
                    var fileName = db.Ado.SqlStackTrace.FirstFileName;
                    var fileLine = db.Ado.SqlStackTrace.FirstLine;
                    var firstMethodName = db.Ado.SqlStackTrace.FirstMethodName;

                    var log = new StringBuilder();
                    log.AppendLine("【警告：SQL执行时间过长】")
                       .AppendLine($"【执行时间】：{executionTime.TotalSeconds:F2}秒")
                       .AppendLine($"【所在文件】：{fileName}")
                       .AppendLine($"【代码行数】：{fileLine}")
                       .AppendLine($"【调用方法】：{firstMethodName}")
                       .AppendLine($"【SQL语句】：{UtilMethods.GetSqlString(config.DbType, sql, pars)}");

                    Console.WriteLine(log.ToString());

                    // 记录到日志文件
                    Log.Warning(log.ToString());
                }

                Console.ForegroundColor = originColor;
            };
        }

        // 数据审计
        db.Aop.DataExecuting = (oldValue, entityInfo) =>
        {
            if (entityInfo.OperationType == DataFilterType.InsertByObject)
            {
                // 主键(long类型)且没有值的---赋值雪花Id
                if (entityInfo.EntityColumnInfo.IsPrimarykey && entityInfo.EntityColumnInfo.PropertyInfo.PropertyType == typeof(long))
                {
                    var id = entityInfo.EntityColumnInfo.PropertyInfo.GetValue(entityInfo.EntityValue);
                    if (id == null || (long)id == 0)
                        entityInfo.SetValue(YitIdHelper.NextId());
                }

                if (entityInfo.PropertyName == "CreatedTime")
                    entityInfo.SetValue(DateTime.ShangHai());
                if (App.User != null)
                {
                    if (entityInfo.PropertyName == "CreatedUserId")
                    {
                        var createUserId = ((dynamic)entityInfo.EntityValue).CreatedUserId;
                        if (createUserId == 0 || createUserId == null)
                            entityInfo.SetValue(App.User.FindFirst(ClaimConst.UserId)?.Value);
                    }

                    if (entityInfo.PropertyName == "CreatedUserName")
                    {
                        var createdUserName = ((dynamic)entityInfo.EntityValue).CreatedUserName;
                        if (string.IsNullOrEmpty(createdUserName) || createdUserName == null)
                            entityInfo.SetValue(App.User.FindFirst(ClaimConst.UserName)?.Value);
                    }
                }
            }

            if (entityInfo.OperationType == DataFilterType.UpdateByObject)
            {
                if (entityInfo.PropertyName == "UpdatedTime")
                    entityInfo.SetValue(DateTime.ShangHai());
                if (entityInfo.PropertyName == "UpdatedUserId")
                    entityInfo.SetValue(App.User?.FindFirst(ClaimConst.UserId)?.Value);
                if (entityInfo.PropertyName == "UpdatedUserName")
                    entityInfo.SetValue(App.User?.FindFirst(ClaimConst.UserName)?.Value);
            }
        };

        // 添加连接池监控
        db.Aop.OnExecutingChangeSql = (sql, pars) =>
        {
            // 监控连接池使用情况
            var poolSize = db.Ado.Connection.GetType()
                .GetField("PoolSize", BindingFlags.NonPublic | BindingFlags.Instance)
                ?.GetValue(db.Ado.Connection);

            if (poolSize != null && (int)poolSize >= 90) // 连接池使用超过90%
            {
                Log.Warning($"数据库连接池使用率过高: {poolSize}");
            }

            return new KeyValuePair<string, SugarParameter[]>(sql, pars);
        };
    }

    /// <summary>
    ///     初始化数据库
    /// </summary>
    /// <param name="db"></param>
    /// <param name="config"></param>
    private static void InitDatabase(SqlSugarScope db, DbConnectionConfig config)
    {
        var dbProvider = db.GetConnectionScope(config.ConfigId);

        // 检查版本文件
        var versionFile = Path.Combine(AppContext.BaseDirectory, "wwwroot", "db_version", $"{config.ConfigId}.txt");
        Console.WriteLine("┌──────────────────────────────────────────────────┐");
        Console.WriteLine($"│            正在初始化数据库...          │");
        var currentVersion = MachineUtil.Version; // 当前系统版本
        Console.WriteLine($"│            当前系统版本: {currentVersion}          │");
        Console.WriteLine("└──────────────────────────────────────────────────┘");
        // 确保目录存在
        Directory.CreateDirectory(Path.GetDirectoryName(versionFile));

        // 读取已存储的版本号
        if (File.Exists(versionFile))
        {
            Console.WriteLine("┌──────────────────────────────────────────────────┐");
            Console.WriteLine($"│            正在读取已存储的版本号...          │");
            var storedVersion = File.ReadAllText(versionFile).Trim();
            Console.WriteLine($"│            已存储的版本号: {storedVersion}          │");
            Console.WriteLine("└──────────────────────────────────────────────────┘");
            if (storedVersion == currentVersion)
            {
                Console.WriteLine("┌──────────────────────────────────────────────────┐");
                Console.WriteLine($"│            数据库已是最新版本,无需初始化          │");
                Console.WriteLine("└──────────────────────────────────────────────────┘");
                return; // 版本一致,不需要初始化
            }
        }
        Console.WriteLine("┌──────────────────────────────────────────────────┐");
        Console.WriteLine($"│            正在初始化数据库...          │");
        Console.WriteLine("└──────────────────────────────────────────────────┘");

        // 初始化/创建数据库
        dbProvider.DbMaintenance.CreateDatabase();
        Console.WriteLine("┌──────────────────────────────────────────────────┐");
        Console.WriteLine($"│            数据库已创建          │");
        Console.WriteLine("└──────────────────────────────────────────────────┘");

        // 初始化表结构
        if (config.EnableInitTable)
        {
            Console.WriteLine("┌──────────────────────────────────────────────────┐");
            Console.WriteLine($"│            正在初始化表结构...          │");
            Console.WriteLine("└──────────────────────────────────────────────────┘");
            var entityTypes = App.EffectiveTypes.Where(u => !u.IsInterface && !u.IsAbstract && u.IsClass && u.IsDefined(typeof(SugarTable), false))
                .Where(u => !u.GetCustomAttributes<IgnoreTableAttribute>().Any())
                .ToList();

            if (config.ConfigId.ToString() == SqlSugarConst.Default || config.ConfigId.ToString() == SqlSugarConst.TerminalDefault) // 默认库（有系统表特性、没有日志表和租户表特性）
                entityTypes = entityTypes.Where(u => !u.GetCustomAttributes<TenantAttribute>().Any()).ToList();
            else
                entityTypes = entityTypes.Where(u => u.GetCustomAttribute<TenantAttribute>()?.configId.ToString() == config.ConfigId.ToString()).ToList(); // 自定义的库

            int count = 0, sum = entityTypes.Count;

            // 分批处理表初始化，每批处理5个表，并在每批之间进行GC回收
            const int batchSize = 5;
            for (int i = 0; i < entityTypes.Count; i += batchSize)
            {
                // 获取当前批次的实体类型
                var batchEntityTypes = entityTypes.Skip(i).Take(batchSize).ToList();

                foreach (var entityType in batchEntityTypes)
                {
                    count++;
                    Console.WriteLine($"│            正在初始化表结构...{count}/{sum}          │");
                    try
                    {
                        if (entityType.GetCustomAttribute<SplitTableAttribute>() == null)
                            dbProvider.CodeFirst.InitTables(entityType);
                        // else
                        //     dbProvider.CodeFirst.SplitTables().InitTables(entityType);
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但继续处理其他表
                        Console.WriteLine($"│            初始化表 {entityType.Name} 失败: {ex.Message}          │");
                        Log.Error($"初始化表 {entityType.Name} 失败: {ex.Message}");
                    }
                }

                // 每批次处理完成后，强制进行垃圾回收以释放内存
                GC.Collect();
                GC.WaitForPendingFinalizers();

                // 在批次之间添加短暂延迟，避免CPU和内存持续高负载
                Thread.Sleep(100);
            }
        }
        Console.WriteLine("┌──────────────────────────────────────────────────┐");
        Console.WriteLine($"│            数据库表结构已创建          │");
        Console.WriteLine("└──────────────────────────────────────────────────┘");

        // 初始化种子数据
        if (config.EnableInitSeed)
        {
            Console.WriteLine("┌──────────────────────────────────────────────────┐");
            Console.WriteLine($"│            正在初始化种子数据...          │");
            Console.WriteLine("└──────────────────────────────────────────────────┘");
            var seedDataTypes = App.EffectiveTypes.Where(u => !u.IsInterface && !u.IsAbstract && u.IsClass && u.GetInterfaces().Any(i => i.HasImplementedRawGeneric(typeof(ISqlSugarEntitySeedData<>))))
                .OrderBy(u => u.GetCustomAttributes(typeof(SeedDataAttribute), false).Length > 0 ? ((SeedDataAttribute)u.GetCustomAttributes(typeof(SeedDataAttribute), false)[0]).Order : 0).ToList();

            int count = 0, sum = seedDataTypes.Count;

            // 分批处理种子数据初始化，每批处理3个种子数据类型
            const int batchSize = 3;
            for (int i = 0; i < seedDataTypes.Count; i += batchSize)
            {
                // 获取当前批次的种子数据类型
                var batchSeedTypes = seedDataTypes.Skip(i).Take(batchSize).ToList();

                foreach (var seedType in batchSeedTypes)
                {
                    count++;
                    Console.WriteLine($"│            正在初始化种子数据...{count}/{sum}          │");
                    try
                    {
                        var entityType = seedType.GetInterfaces().First().GetGenericArguments().First();
                        if (config.ConfigId.ToString() == SqlSugarConst.Default || config.ConfigId.ToString() == SqlSugarConst.TerminalDefault) // 默认库（有系统表特性、没有日志表和租户表特性）
                        {
                            if (entityType.GetCustomAttribute<TenantAttribute>() != null)
                                continue;
                        }
                        else
                        {
                            var att = entityType.GetCustomAttribute<TenantAttribute>(); // 自定义的库
                            if (att == null || att.configId.ToString() != config.ConfigId.ToString()) continue;
                        }

                        var instance = Activator.CreateInstance(seedType);
                        var hasDataMethod = seedType.GetMethod("HasData");
                        var seedData = ((IEnumerable)hasDataMethod?.Invoke(instance, null))?.Cast<object>();
                        if (seedData == null) continue;

                        var entityInfo = dbProvider.EntityMaintenance.GetEntityInfo(entityType);
                        try
                        {
                            //开始初始化，移除种子数据
                            switch (entityInfo.DbTableName)
                            {
                                case "sys_dict_data":
                                    db.Ado.ExecuteCommand("delete from `sys_dict_data`;");
                                    break;
                                    // case "sys_script":
                                    //     db.Ado.ExecuteCommand("delete from `sys_script`;");
                                    //     break;
                            }
                        }
                        catch
                        {
                            // ignored
                        }

                        if (entityType.GetCustomAttribute<SplitTableAttribute>(true) != null)
                        {
                            //拆分表的操作需要实体类型，而通过反射很难实现
                            //所以，这里将Init方法写在"种子数据类"内部，再传入 db 反射调用
                            var hasInitMethod = seedType.GetMethod("Init");
                            var parameters = new object[] { db };
                            hasInitMethod?.Invoke(instance, parameters);
                        }
                        else
                        {
                            if (entityInfo.Columns.Any(u => u.IsPrimarykey))
                            {
                                // 将种子数据转换为列表并分批处理，避免一次性加载过多数据
                                var seedDataList = seedData.ToList();
                                const int dataBatchSize = 100; // 每批处理100条数据

                                for (int j = 0; j < seedDataList.Count; j += dataBatchSize)
                                {
                                    var batchData = seedDataList.Skip(j).Take(dataBatchSize).ToList();

                                    // 按主键进行批量增加和更新
                                    var storage = dbProvider.StorageableByObject(batchData).ToStorage();
                                    storage.AsInsertable.ExecuteCommand();
                                    if (seedType.GetCustomAttribute<IgnoreUpdateSeedAttribute>() == null) // 有忽略更新种子特性时则不更新
                                        storage.AsUpdateable
                                            .IgnoreColumns(entityInfo.Columns.Where(u => u.PropertyInfo.GetCustomAttribute<IgnoreUpdateSeedColumnAttribute>() != null).Select(u => u.PropertyName).ToArray())
                                            .ExecuteCommand();
                                }
                            }
                            else
                            {
                                // 无主键则只进行插入，同样分批处理
                                if (!dbProvider.Queryable(entityInfo.DbTableName, entityInfo.DbTableName).Any())
                                {
                                    var seedDataList = seedData.ToList();
                                    const int dataBatchSize = 100; // 每批处理100条数据

                                    for (int j = 0; j < seedDataList.Count; j += dataBatchSize)
                                    {
                                        var batchData = seedDataList.Skip(j).Take(dataBatchSize).ToList();
                                        dbProvider.InsertableByObject(batchData).ExecuteCommand();
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但继续处理其他种子数据
                        Console.WriteLine($"│            初始化种子数据 {seedType.Name} 失败: {ex.Message}          │");
                        Log.Error($"初始化种子数据 {seedType.Name} 失败: {ex.Message}");
                    }
                }

                // 每批次处理完成后，强制进行垃圾回收以释放内存
                GC.Collect();
                GC.WaitForPendingFinalizers();

                // 在批次之间添加短暂延迟，避免CPU和内存持续高负载
                Thread.Sleep(100);
            }
        }
        Console.WriteLine("┌──────────────────────────────────────────────────┐");
        Console.WriteLine($"│            数据库种子数据已初始化          │");
        Console.WriteLine("└──────────────────────────────────────────────────┘");

        // 保存新版本号
        File.WriteAllText(versionFile, currentVersion);
        Console.WriteLine("┌──────────────────────────────────────────────────┐");
        Console.WriteLine($"│            数据库版本已保存          │");
        Console.WriteLine("└──────────────────────────────────────────────────┘");
    }
}