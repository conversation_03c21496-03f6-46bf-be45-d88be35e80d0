namespace Feng.IotGateway.Core.Enums;

/// <summary>
///     系统错误码
/// </summary>
[ErrorCodeType]
public enum ErrorCode
{
    /// <summary>
    ///     该类型不存在
    /// </summary>
    [ErrorCodeItemMetadata("该类型不存在")] D1501,

    /// <summary>
    ///     该类型不是枚举类型
    /// </summary>
    [ErrorCodeItemMetadata("该类型不是枚举类型")] D1503,

    /// <summary>
    ///     新增数据失败
    /// </summary>
    [ErrorCodeItemMetadata("新增数据失败")] Com1000,

    /// <summary>
    ///     主键不能为空
    /// </summary>
    [ErrorCodeItemMetadata("主键不能为空")] D1509,

    /// <summary>
    ///     修改数据失败
    /// </summary>
    [ErrorCodeItemMetadata("修改数据失败")] Com1001,

    /// <summary>
    ///     删除数据失败
    /// </summary>
    [ErrorCodeItemMetadata("删除数据失败")] Com1002,

    /// <summary>
    ///     数据库连接失败
    /// </summary>
    [ErrorCodeItemMetadata("数据库连接失败")] D1507,

    /// <summary>
    ///     数据库连接失败
    /// </summary>
    [ErrorCodeItemMetadata("连接数据库类型错误")] D1506,

    /// <summary>
    ///     用户名或密码不正确
    /// </summary>
    [ErrorCodeItemMetadata("用户名或密码不正确")] D1000,

    /// <summary>
    ///     记录不存在
    /// </summary>
    [ErrorCodeItemMetadata("记录不存在")] D1002,


    /// <summary>
    ///     已存在同名或同编码数据
    /// </summary>
    [ErrorCodeItemMetadata("已存在同名或同编码数据")] Com1004,

    /// <summary>
    ///     数据已存在
    /// </summary>
    [ErrorCodeItemMetadata("数据已存在")] D1006,

    /// <summary>
    ///     非法数据
    /// </summary>
    [ErrorCodeItemMetadata("非法数据")] D1011,

    /// <summary>
    ///     Id不能为空
    /// </summary>
    [ErrorCodeItemMetadata("Id不能为空")] D1012,

    /// <summary>
    ///     账号已冻结
    /// </summary>
    [ErrorCodeItemMetadata("账号已冻结")] D1017,


    /// <summary>
    ///     字典类型不存在
    /// </summary>
    [ErrorCodeItemMetadata("字典类型不存在")] D3000,

    /// <summary>
    ///     字典类型已存在
    /// </summary>
    [ErrorCodeItemMetadata("字典类型已存在,名称或编码重复")]
    D3001,

    /// <summary>
    ///     字典类型下面有字典值禁止删除
    /// </summary>
    [ErrorCodeItemMetadata("字典类型下面有字典值禁止删除")]
    D3002,

    /// <summary>
    ///     字典值已存在
    /// </summary>
    [ErrorCodeItemMetadata("字典值已存在,名称或编码重复")]
    D3003,

    /// <summary>
    ///     字典值不存在
    /// </summary>
    [ErrorCodeItemMetadata("字典值不存在")] D3004,

    /// <summary>
    ///     已存在同名或同编码参数配置
    /// </summary>
    [ErrorCodeItemMetadata("已存在同名或同编码参数配置")]
    D9000,

    /// <summary>
    ///     设备数据已被删除,操作失败
    /// </summary>
    [ErrorCodeItemMetadata("设备数据已被删除,操作失败")]
    Dev404,

    /// <summary>
    ///     协议数据已被删除,操作失败
    /// </summary>
    [ErrorCodeItemMetadata("协议数据已被删除,操作失败")]
    Dri404,

    /// <summary>
    ///     该协议授权数量不足,操作失败
    /// </summary>
    [ErrorCodeItemMetadata("该协议授权数量不足,操作失败")]
    Dri301,

    /// <summary>
    ///     采集点数据已被删除,操作失败
    /// </summary>
    [ErrorCodeItemMetadata("采集点数据已被删除,操作失败")]
    Dvr404,

    /// <summary>
    ///     转发标识符已存在,操作失败
    /// </summary>
    [ErrorCodeItemMetadata("转发标识符已存在,操作失败")]
    Tra402,

    /// <summary>
    ///     转发配置数据已被删除,操作失败
    /// </summary>
    [ErrorCodeItemMetadata("转发配置数据已被删除,操作失败")]
    Tra404
}