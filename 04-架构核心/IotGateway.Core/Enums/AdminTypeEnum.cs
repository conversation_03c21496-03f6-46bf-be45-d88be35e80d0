namespace Feng.IotGateway.Core.Enums;

/// <summary>
///     账号类型枚举
/// </summary>
public enum AdminTypeEnum
{
    /// <summary>
    ///     超级管理员
    /// </summary>
    [Description("超级管理员")] SuperAdmin = 1,

    /// <summary>
    ///     管理员
    /// </summary>
    [Description("管理员")] Admin = 2,

    /// <summary>
    ///     普通账号
    /// </summary>
    [Description("普通账号")] None = 3
}

/// <summary>
///     Http请求方式:1:GET;2:POST
/// </summary>
public enum HttpRequestTypeEnum
{
    [Description("GET")] Get = 1,
    [Description("POST")] Post = 2
}