namespace Feng.IotGateway.Core.Enums;

/// <summary>
///     执行模式:1网关启动；2定时；3属性；4变量；5消息；6Http
/// </summary>
public enum ExecutionStrategyTypeEnum
{
    /// <summary>
    ///     网关启动时执行
    /// </summary>
    [Description("网关启动触发")] 网关启动触发 = 1,

    /// <summary>
    ///     定时触发
    /// </summary>
    [Description("定时触发")] 定时触发 = 2,

    /// <summary>
    ///     属性触发
    /// </summary>
    [Description("属性触发")] 属性触发 = 3,

    /// <summary>
    ///     变量触发
    /// </summary>
    [Description("变量触发")] 变量触发 = 4,
}