using Feng.IotGateway.Core.Service.Config;
using IotGateway.Application.EdgeComputingServer;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Web.Core.BackgroundServices;

/// <summary>
/// 初始化服务
/// </summary>
public class InitializationHostedService : IHostedService
{
  /// <summary>
  /// 服务作用域工厂  
  /// </summary>
  private readonly IServiceScopeFactory _serviceScopeFactory;

  /// <summary>
  /// 初始化
  /// </summary>
  /// <param name="serviceScopeFactory">服务作用域工厂</param>
  public InitializationHostedService(IServiceScopeFactory serviceScopeFactory)
  {
    _serviceScopeFactory = serviceScopeFactory;
  }

  /// <summary>
  /// 启动
  /// </summary>
  /// <param name="cancellationToken">取消令牌</param>
  public async Task StartAsync(CancellationToken cancellationToken)
  {
    using var scope = _serviceScopeFactory.CreateScope();
    var scriptExecutionSingleton = scope.ServiceProvider.GetRequiredService<ScriptExecutionSingleton>();
    var sysConfigService = scope.ServiceProvider.GetRequiredService<SysConfigService>();

    // 初始化全局配置 - 从数据库加载所有配置到内存
    await sysConfigService.InitializeGlobalConfigs();

    // 初始化时区
    DateTime.UtcTime = await sysConfigService.GetConfigValue<bool>(ConfigConst.UtcTime);

    // 初始化脚本执行
    await scriptExecutionSingleton.Initialize();
  }

  /// <summary>
  /// 停止
  /// </summary>
  /// <param name="cancellationToken">取消令牌</param>
  public Task StopAsync(CancellationToken cancellationToken)
  {
    return Task.CompletedTask;
  }
}