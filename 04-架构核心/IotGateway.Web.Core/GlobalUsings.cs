global using Furion;
global using Furion.DataEncryption;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Http;
global using Feng.IotGateway.Core.Option;
global using Microsoft.Extensions.DependencyInjection;
global using System.Threading.Tasks;
global using Feng.IotGateway.Core.SqlSugar;
global using Feng.IotGateway.Core.Extension;
global using Microsoft.AspNetCore.Builder;
global using Microsoft.AspNetCore.Hosting;
global using Microsoft.Extensions.Hosting;
global using Feng.IotGateway.WebSocket;
global using Furion.EventBus;
global using System;
global using Feng.IotGateway.Core.Const;
global using Furion.Authorization;
global using Feng.IotGateway.Core.Entity;
global using Furion.JsonSerialization;
global using Furion.Logging;
global using Microsoft.Extensions.Logging;
global using Yitter.IdGenerator;
global using System.Threading;
global using System.Threading.Channels;
global using System.Text.Encodings.Web;
global using System.Text.Json;
global using System.Text.Json.Serialization;
global using Feng.IotGateway.Core.Util;
global using Microsoft.AspNetCore.HttpOverrides;
global using System.Globalization;
global using Feng.IotGateway.Core.Enums;
global using Feng.IotGateway.WebSocket.Const;
global using System.Collections.Concurrent;
global using System.Collections.Generic;
global using System.Diagnostics;
global using System.Linq;
global using Feng.Common.Util;
global using SqlSugar;
