using System.Net.Http;
using System.Net.Security;
using System.Runtime.InteropServices;
using Feng.IotGateway.Core.BackgroundServices;
using Feng.IotGateway.Core.Service.Cache;
using IotGateway.Application.BackgroundServices;
using IotGateway.Application.ServiceConfig;
using IotGateway.EdgeDevice.TransPonds;
using IotGateway.FengLink;
using IotGateway.MQTT;
using IotGateway.Web.Core.BackgroundServices;
using IotGateway.Web.Core.EventBus;
using IotGateway.Web.Core.Filter;
using IotGateway.Web.Core.Handlers;
using NewLife.Caching;
using DateTime = Common.Extension.DateTime;
using IotGateway.Application.Firewall;
namespace IotGateway.Web.Core;

[AppStartup(99)]
public class Startup : AppStartup
{
    private IServiceProvider _serviceProvider;

    public void ConfigureServices(IServiceCollection services)
    {
        var sw = Stopwatch.StartNew();
        var loggerFactory = LoggerFactory.Create(builder =>
        {
            builder.AddConsole();
            builder.AddDebug();
        });
        var logger = loggerFactory.CreateLogger<Startup>();

        logger.LogInformation("开始配置服务...");

        // 雪花Id
        YitIdHelper.SetIdGenerator(new IdGeneratorOptions { WorkerId = 3 });
        logger.LogInformation($"配置雪花ID耗时: {sw.ElapsedMilliseconds}ms");

        // 统一配置项目选项注册
        services.AddProjectOptions();
        logger.LogInformation($"配置项目选项耗时: {sw.ElapsedMilliseconds}ms");

        // 初始化数据库上下文
        services.AddSqlSugar();
        logger.LogInformation($"初始化数据库耗时: {sw.ElapsedMilliseconds}ms");

        // 实现 JWT 身份验证过程控制
        services.AddJwt<JwtHandler>(enableGlobalAuthorize: true);
        // 配置跨越
        services.AddCorsAccessor();
        // 注册远程请求
        services.AddRemoteRequest(options =>
        {
            // 需在所有客户端注册之前注册
            options.ApproveAllCerts();
            // 注册HttpClient
            options.AddHttpClient(string.Empty)
                .ConfigurePrimaryHttpMessageHandler(u => new SocketsHttpHandler
                {
                    SslOptions = new SslClientAuthenticationOptions
                    {
                        RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true
                    }
                });
        });

        // 注册任务队列
        services.AddTaskQueue();
        // 注册定时任务
        services.AddSchedule(options => { options.UseUtcTimestamp = true; });
        // 缓存注册
        services.AddCache();

        #region 标准请求和返回配置

        services.AddControllersWithViews()
            .AddMvcFilter<RequestActionFilter>()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.PropertyNamingPolicy = null;
                options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
                options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
                options.JsonSerializerOptions.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
                options.JsonSerializerOptions.Converters.AddDateTimeTypeConverters();
            })
            .AddInjectWithUnifyResult();
        services.AddControllers()
            .AddJsonOptions(config => { config.JsonSerializerOptions.PropertyNamingPolicy = null; });

        #endregion 标准请求和返回配置

        #region 事件总线

        // 注册日志事件订阅者(支持自定义消息队列组件)
        services.AddEventBus(builder =>
        {
            // 禁用日志
            builder.LogEnabled = false;
            if (App.Configuration["Event:Type"] == CacheTypeEnum.RedisCache.ToString())
                try
                {
                    // 替换事件源存储器
                    builder.ReplaceStorerOrFallback(serviceProvider =>
                    {
                        var redisCache = serviceProvider.GetService<ICache>();
                        // 创建默认内存通道事件源对象，可自定义队列路由key，比如这里是 eventbus
                        return new RedisEventSourceStorer(redisCache, "eventbus", 3000);
                    });
                }
                catch
                {
                    //如果redis无法连接,就使用默认的源 
                }

            //采集设备事件订阅
            builder.AddSubscriber<DeviceThreadEventSubscriber>();
            //消息回调事件
            builder.AddSubscriber<ReceiveEventSubscriber>();
        });

        #endregion 事件总线

        services.AddSingleton<TransPondEventSubscriber>();
        services.AddSingleton<TransPondHostedService>();
        services.AddHostedService(provider => provider.GetRequiredService<TransPondHostedService>());

        services.AddHostedService<FengLinkHostedServer>();
        
        #region 后台任务

        // 将这些服务改为瞬时服务而不是直接注册为 HostedService
        services.AddSingleton<CheckMaxOffLineTimer>();
        services.AddSingleton<DeviceStatusTimer>();
        services.AddSingleton<DropMessageTimer>();
        services.AddSingleton<DropReportDataTimer>();
        services.AddSingleton<IndexDataTimer>();
        services.AddSingleton<GatewayDateTime>();
        services.AddSingleton<FirewallHostedService>();

        // 保留立即启动的关键服务
        services.AddHostedService<InitializationHostedService>();
        services.AddHostedService<TransPondHostedService>();

        // 保留立即启动的关键服务
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            services.AddSingleton<DropCachesTimer>();
            services.AddSingleton<WatchdogTimer>();
        }
        #endregion

        // 注册即时通讯
        services.AddSignalR();
        // 增加Logo输出显示
        services.AddLogoDisplay();

        #region 日志写入

        // 日志写入文件-消息、警告、错误
        Array.ForEach(new[] { LogLevel.Information, LogLevel.Warning, LogLevel.Error }, logLevel =>
        {
            services.AddFileLogging("logs/" + logLevel + "/{0:yyyy}-{0:MM}-{0:dd}.log", options =>
            {
                options.FileNameRule = fileName => string.Format(fileName, DateTime.ShangHai()); // 每天创建一个文件
                options.WriteFilter = logMsg => logMsg.LogLevel == logLevel;
                options.FileSizeLimitBytes = 10 * 1024 * 1024;
                options.MaxRollingFiles = 7;
            });
        });

        #endregion 日志写入

        services.AddSingleton<MasterClient>();

        services.AddHostedService<ServiceInitializationHostedService>();
        logger.LogInformation($"服务配置总耗时: {sw.ElapsedMilliseconds}ms");
        sw.Stop();
    }

    /// <summary>
    /// </summary>
    /// <param name="app"></param>
    /// <param name="env"></param>
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        _serviceProvider = app.ApplicationServices;

        var sw = Stopwatch.StartNew();
        var logger = _serviceProvider.GetRequiredService<ILogger<Startup>>();

        logger.LogInformation("开始配置中间件...");

        // NGINX 反向代理获取真实IP
        app.UseForwardedHeaders(new ForwardedHeadersOptions
        { ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto });
        logger.LogInformation($"配置ForwardedHeaders耗时: {sw.ElapsedMilliseconds}ms");
        if (env.IsDevelopment()) app.UseDeveloperExceptionPage();
        app.UseDefaultFiles();
        app.UseStaticFiles();
        //
        app.UseHttpsRedirection();
        //
        app.UseRouting();
        //
        app.EnableBuffering();
        //跨域
        app.UseCorsAccessor();
        //
        app.UseAuthentication();
        app.UseAuthorization();

        // 注册基础中间件
        app.UseInject(string.Empty);
        //
        app.UseEndpoints(endpoints =>
        {
            //DeviceHub
            endpoints.MapHub<DeviceHub>("/device");
            endpoints.MapControllers();
        });
        // 定时任务看板
        app.UseScheduleUI();

        // 在最后添加延迟启动的代码
        Task.Run(async () =>
        {
            // 等待3秒再启动非关键服务
            await Task.Delay(3000);
            await StartBackgroundServices();
        });


        logger.LogInformation($"中间件配置总耗时: {sw.ElapsedMilliseconds}ms");
        sw.Stop();
    }

    /// <summary>
    /// 启动后台服务
    /// </summary>
    /// <returns></returns>
    private async Task StartBackgroundServices()
    {
        // 缓存数据是否超过预警
        await StartHostedService<CheckMaxOffLineTimer>();
        // 采集状态
        await StartHostedService<DeviceStatusTimer>();
        // 清理站内信
        await StartHostedService<DropMessageTimer>();
        // 清理设备保存的数据
        await StartHostedService<DropReportDataTimer>();
        // 首页数据
        await StartHostedService<IndexDataTimer>();
        // 系统时间
        await StartHostedService<GatewayDateTime>();
        // 防火墙规则应用
        await StartHostedService<FirewallHostedService>();
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            // 释放缓存
            await StartHostedService<DropCachesTimer>();
            // 看门狗
            await StartHostedService<WatchdogTimer>();
        }
    }

    /// <summary>
    ///     启动后台服务
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    private async Task StartHostedService<T>() where T : IHostedService
    {
        var service = _serviceProvider.GetService<T>();
        if (service != null)
        {
            await service.StartAsync(CancellationToken.None);
        }
    }
}