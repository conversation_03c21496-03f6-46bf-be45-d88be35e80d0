using Driver.Core.Models;
using Feng.IotGateway.Core.Service;
using Feng.IotGateway.Core.Service.Cache;
using IotGateway.Application;
using IotGateway.Application.Entity;
using IotGateway.Engine;
using Jint;

namespace IotGateway.Web.Core.EventBus;

/// <summary>
///     网关收到的数据处理中心
/// </summary>
public class ReceiveEventSubscriber : IEventSubscriber
{
    private IServiceProvider Services { get; }
    private readonly CacheService _cacheService;
    private readonly SendMessageService _socket;
    private readonly EngineSingletonService _engineSingleton;

    public ReceiveEventSubscriber(IServiceScopeFactory services, SendMessageService socket, CacheService cacheService, EngineSingletonService engineSingleton)
    {
        Services = services.CreateScope().ServiceProvider;
        _socket = socket;
        _cacheService = cacheService;
        _engineSingleton = engineSingleton;
    }

    #region ELink

    /// <summary>
    ///     ELink 下写标签
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe(EventConst.WriteShare)]
    public async Task WriteShare(EventHandlerExecutingContext context)
    {
        // 下写静态变量
        var writeInput = context.GetPayload<DeviceWriteRequest>();
        // 数量非常少，直接遍历写
        foreach (var (identifier, value) in writeInput.Params)
            try
            {
                _cacheService.Set(identifier, DateTime.TryParseExact(value, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out var time)
                    ? time.ToString("yyyy-MM-dd HH:mm:ss")
                    : value);
            }
            catch (Exception e)
            {
                Log.Error($"ELink下写标签Error:【{e.Message}】,请求参数:【{context.Source.Payload}】");
            }
    }

    /// <summary>
    ///     【标签】下写 发送站内信 记录通知
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe(EventConst.WriteShare)]
    public async Task WriteShareSetLog(EventHandlerExecutingContext context)
    {
        var messageResp = Services.GetRequiredService<SqlSugarRepository<Message>>();
        var message = new Message
        {
            Content = context.Source.Payload.ToString() ?? "",
            Level = "info",
            Title = "标签下写"
        };
        await messageResp.InsertAsync(message);
        var isSend = _cacheService.Get<string>("SendMessage");
        if (isSend == null)
            return;
        if (isSend.ToLower() == "true")
            _ = _socket.Send($"{JSON.Serialize(message)}", ConstMethod.Message);
    }

    /// <summary>
    ///     【属性】下写 发送站内信 记录通知
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe(EventConst.WriteVariable)]
    public async Task WriteVariableSetLog(EventHandlerExecutingContext context)
    {
        var messageResp = Services.GetRequiredService<SqlSugarRepository<Message>>();
        var message = new Message
        {
            Content = context.Source.Payload.ToString() ?? "",
            Level = "info",
            Title = "属性下写"
        };
        await messageResp.CopyNew().InsertAsync(message);
        var isSend = _cacheService.Get<string>("SendMessage");
        if (isSend == null)
            return;
        if (isSend.ToLower() == "true")
            _ = _socket.Send($"{JSON.Serialize(message)}", ConstMethod.Message);
    }

    #endregion

    /// <summary>
    ///     远程下写脚本点位-触发-执行脚本内容
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(EventConst.WriteScriptVariable)]
    public async Task Action(EventHandlerExecutingContext context)
    {
        // 执行的脚本内容
        var deviceWriteScript = context.GetPayload<DeviceWriteScriptRequest>();
        var writeLog = new DeviceWriteLog
        {
            Id = YitIdHelper.NextId(),
            DeviceId = deviceWriteScript.DeviceId,
            StartTime = Common.Extension.DateTime.Now(),
            WriteSide = DeviceWriteSideEnum.ServerSide,
            Params = JSON.Serialize(deviceWriteScript.Params)
        };

        try
        {
            // 平台下写数据
            foreach (var (key, value) in deviceWriteScript.Params)
                _engineSingleton.Share.Save(key, value);
            var completionValue = _engineSingleton.Engine.Evaluate(deviceWriteScript.Script, new ScriptParsingOptions { Tolerant = true, AllowReturnOutsideFunction = true }).ToObject();
            writeLog.IsSuccess = true;
            writeLog.Description = completionValue?.ToString() ?? "";
        }
        catch (Exception e)
        {
            writeLog.IsSuccess = false;
            writeLog.Description = e.Message;
        }

        var rep = Services.GetRequiredService<SqlSugarRepository<DeviceWriteLog>>();
        await rep.InsertAsync(writeLog);
    }

    /// <summary>
    ///     网关同步时间后-删除同步时间后需要丢弃的缓存数据
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(EventConst.DeleteCache)]
    public async Task DeleteOutTimeCache(EventHandlerExecutingContext context)
    {
        // 根据逻辑批量删除缓存数据
        var offLineRepo = Services.GetRequiredService<SqlSugarRepository<OffLine>>();
        var timeStamp = Convert.ToInt64(context.Source.Payload.ToString());
        // 超过当前时间的数据全部删除掉
        await offLineRepo.DeleteAsync(w => w.TimeStamp > timeStamp);
    }

    /// <summary>
    ///     记录发送成功的数据
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(EventConst.PubSuccess)]
    public async Task PubSuccessEvent(EventHandlerExecutingContext context)
    {
        var offLineDbRep = Services.GetRequiredService<ISqlSugarClient>();
        var reportDataList = JSON.Deserialize<List<ReportData>>(context.GetPayload<string>());
        if (reportDataList == null)
            return;
        offLineDbRep = offLineDbRep.AsTenant().GetConnection(SqlSugarConst.EdgeData);
        offLineDbRep.CurrentConnectionConfig.ConfigureExternalServices.SplitTableService = new WordSplitService();
        await offLineDbRep.CopyNew().Insertable(reportDataList).SplitTable().ExecuteCommandAsync();
        offLineDbRep.Dispose();
    }
}