using IotGateway.Application.Entity;
using IotGateway.EdgeDevice;
using IotGateway.EdgeDevice.DeviceEvents;
using DateTime = Common.Extension.DateTime;

namespace IotGateway.Web.Core.EventBus;

/// <summary>
///     采集设备事件订阅
/// </summary>
public class DeviceThreadEventSubscriber : IEventSubscriber
{
    public DeviceThreadEventSubscriber(IServiceProvider services)
    {
        Services = services;
    }

    public IServiceProvider Services { get; }

    /// <summary>
    ///     创建采集设备线程
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe(EventConst.CreateDeviceThread)]
    public async Task CreateDeviceThread(EventHandlerExecutingContext context)
    {
        using var scope = Services.CreateScope();
        var rep = scope.ServiceProvider.GetRequiredService<DeviceHostedService>();
        var deviceId = Convert.ToInt64(context.Source.Payload.ToString());
        await rep.RemoveDeviceThread(deviceId);
        await rep.CreateDeviceThread(deviceId);
    }

    /// <summary>
    ///     Cmd命令重启采集线程
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe(EventConst.CmdReStartDeviceThread)]
    public async Task CmdReStartDeviceThread(EventHandlerExecutingContext context)
    {
        using var scope = Services.CreateScope();
        var deviceName = context.GetPayload<string>();
        var deviceRepo = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<Device>>();
        var device = await deviceRepo.GetFirstAsync(f => f.DeviceName == deviceName);
        if (device == null)
            return;
        if (!device.Enable)
            return;
        var rep = scope.ServiceProvider.GetRequiredService<DeviceHostedService>();
        await rep.RemoveDeviceThread(device);
        await rep.CreateDeviceThread(device.Id);
    }

    /// <summary>
    ///     修改采集设备线程
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe(EventConst.UpdateDeviceThread)]
    public async Task UpdateDeviceThread(EventHandlerExecutingContext context)
    {
        using var scope = Services.CreateScope();
        var rep = scope.ServiceProvider.GetRequiredService<DeviceHostedService>();
        var device = context.GetPayload<Device>();
        await rep.RemoveDeviceThread(device);
        if (device.Enable)
            await rep.CreateDeviceThread(device.Id);
    }

    /// <summary>
    ///     停止采集设备线程
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe(EventConst.StopDeviceThread)]
    public async Task RemoveDeviceThread(EventHandlerExecutingContext context)
    {
        using var scope = Services.CreateScope();
        var rep = scope.ServiceProvider.GetRequiredService<DeviceHostedService>();
        var device = context.GetPayload<Device>();
        await rep.RemoveDeviceThread(device);
    }

    /// <summary>
    ///     停止全部采集设备线程
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe(EventConst.StopDeviceThreadAll)]
    public async Task RemoveDeviceThreadAll(EventHandlerExecutingContext context)
    {
        using var scope = Services.CreateScope();
        var rep = scope.ServiceProvider.GetRequiredService<DeviceHostedService>();
        await rep.RemoveDeviceThreadAll();
    }

    /// <summary>
    ///     设备事件
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe(EventConst.DeviceEvent)]
    public async Task DeviceDisconnected(EventHandlerExecutingContext context)
    {
        using var scope = Services.CreateScope();
        var rep = scope.ServiceProvider.GetRequiredService<DeviceEventLogService>();
        var deviceEvent = context.GetPayload<DeviceEventLog>();
        await rep.Add(deviceEvent);
    }

    /// <summary>
    ///     记录系统事件日志
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe(EventConst.SysLog)]
    public async Task WriteSystemLog(EventHandlerExecutingContext context)
    {
        using var scope = Services.CreateScope();
        var sysLogRepo = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<Message>>();
        var sysLog = new Message
        {
            Id = YitIdHelper.NextId(),
            Title = "系统日志",
            CreatedTime = DateTime.Now(),
            Content = context.Source.Payload.ToString() ?? "",
            Level = "warning",
            Read = false
        };
        await sysLogRepo.CopyNew().InsertAsync(sysLog);
    }

    /// <summary>
    ///     消费采集产生的数据
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe(EventConst.SendDeviceData)]
    public Task DeviceData(EventHandlerExecutingContext context)
    {
        return Task.CompletedTask;
        //todos
    }

    /// <summary>
    ///     消费元数据产生的数据
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    [EventSubscribe(EventConst.DeviceMeta)]
    public Task DeviceMeta(EventHandlerExecutingContext context)
    {
        return Task.CompletedTask;
        //todos
    }
}