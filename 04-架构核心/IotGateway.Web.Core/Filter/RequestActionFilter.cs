using Common.Extension;
using Feng.Common.Extension;
using IotGateway.Application.Entity;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json.Linq;

namespace IotGateway.Web.Core.Filter;

/// <summary>
/// </summary>
public class RequestActionFilter : IAsyncActionFilter
{
    private readonly SqlSugarRepository<OpenApiEntity> _openApiEntity;

    public RequestActionFilter(SqlSugarRepository<OpenApiEntity> openApiEntity)
    {
        _openApiEntity = openApiEntity;
    }

    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        if (context.HttpContext.Request.Path.Value!.StartsWith("/openapi"))
            try
            {
                long appId = 0;
                if (context.HttpContext.Request.Method == "POST")
                {
                    var body = await context.HttpContext.ReadBodyContentAsync();
                    var jsonObject = JObject.Parse(body);
                    appId = (long) jsonObject["AppId"];

                    // 获取 POST 请求的 Body 中的 appid 参数
                }
                else if (context.HttpContext.Request.Method == "GET")
                {
                    // 获取 GET 请求的 Query 中的 appid 参数
                    appId = Convert.ToInt64(context.HttpContext.Request.Query["AppId"]);
                    if (appId <= 0)
                    {
                        context.ActionArguments.TryGetValue("AppId", out var appIdObj);
                        appId = Convert.ToInt64(appIdObj);
                    }
                }

                if (appId <= 0)
                {
                    context.HttpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    return;
                }

                // 从请求头中获取token
                var token = context.HttpContext.Request.Headers["Authorization"].ToString();
                // 在此处进行token的有效性判断，例如调用验证token的方法
                if (!await IsTokenValid(token, context.HttpContext.Request.Path.Value, appId))
                {
                    context.HttpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    return;
                }
            }
            catch
            {
                context.HttpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                return;
            }

        await next();
    }

    /// <summary>
    ///     校验是否有效
    /// </summary>
    /// <param name="token"></param>
    /// <param name="path"></param>
    /// <param name="appId"></param>
    /// <returns></returns>
    private async Task<bool> IsTokenValid(string token, string path, long appId)
    {
        //无认证
        if (!token.IsNull())
        {
            token = token.Replace("Bearer ", "").Trim();
            var (isValid, _, _) = JWTEncryption.Validate(token);
            if (isValid)
                return true;
        }

        var openApi = await _openApiEntity.AsQueryable()
            .Where(w => w.Id == appId)
            .Where(w => w.AuthorizeType == AuthorizeTypeEnum.No)
            .Includes(w => w.OpenApiDetailEntity.Where(x => x.Path == path).ToList())
            .FirstAsync();
        return openApi != null && openApi.OpenApiDetailEntity.Any();
    }
}