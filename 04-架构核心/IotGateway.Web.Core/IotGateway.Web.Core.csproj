<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <NoWarn>1701;1702;1591</NoWarn>
        <DocumentationFile>IotGateway.Web.Core.xml</DocumentationFile>
        <RootNamespace>IotGateway.Web.Core</RootNamespace>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <DebugType>portable</DebugType>
        <Optimize>False</Optimize>
        <DocumentationFile>IotGateway.Web.Core.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
        <DocumentationFile>bin\Release\IotGateway.Web.Core.xml</DocumentationFile>
    </PropertyGroup>

    <ItemGroup>
        <None Remove="IotGateway.Web.Core.xml" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\02-业务模块\01-Business\IotGateway.Application\IotGateway.Application.csproj" />
        <ProjectReference Include="..\..\02-业务模块\03-Plugins\IotGateway.FengLink\IotGateway.FengLink.csproj" />
        <ProjectReference Include="..\..\02-业务模块\03-Plugins\IotGateway.Plugin.Core\IotGateway.Plugin.Core.csproj" />
        <ProjectReference Include="..\..\03-数据模块\IotGateway.TDengIne\IotGateway.TDengIne.csproj" />
    </ItemGroup>

</Project>
