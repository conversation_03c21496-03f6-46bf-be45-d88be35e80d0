namespace IotGateway.Web.Core;

public static class ProjectOptions
{
    /// <summary>
    ///     注册项目配置选项
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddProjectOptions(this IServiceCollection services)
    {
        // 注册数据库连接选项
        services.AddConfigurableOptions<DbConnectionOptions>();
        // 注册刷新令牌选项
        services.AddConfigurableOptions<RefreshTokenOptions>();
        // 注册缓存选项
        services.AddConfigurableOptions<CacheOptions>();
        return services;
    }
}