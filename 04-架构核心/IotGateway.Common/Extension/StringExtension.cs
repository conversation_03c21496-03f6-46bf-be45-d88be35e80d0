using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Common.Enums;

namespace Common.Extension;

/// <summary>
/// </summary>
public static class StringExtension
{
    /// <summary>
    ///     将驼峰字符串的第一个字符小写.
    /// </summary>
    public static string ToLowerCase(this string str)
    {
        if (string.IsNullOrEmpty(str) || !char.IsUpper(str[0]))
            return str;

        if (str.Length == 1)
            return char.ToLower(str[0]).ToString();

        return char.ToLower(str[0]) + str.Substring(1, str.Length - 1);
    }

    #region 转换为帕斯卡命名法

    /// <summary>
    ///     将字符串转为帕斯卡命名法.
    /// </summary>
    /// <param name="original">源字符串.</param>
    /// <returns></returns>
    public static string ParseToPascalCase(this string original)
    {
        var invalidCharsRgx = new Regex("[^_a-zA-Z0-9]");
        var whiteSpace = new Regex(@"(?<=\s)");
        var startsWithLowerCaseChar = new Regex("^[a-z]");
        var firstCharFollowedByUpperCasesOnly = new Regex("(?<=[A-Z])[A-Z0-9]+$");
        var lowerCaseNextToNumber = new Regex("(?<=[0-9])[a-z]");
        var upperCaseInside = new Regex("(?<=[A-Z])[A-Z]+?((?=[A-Z][a-z])|(?=[0-9]))");

        // 用undescore替换空白,然后用空字符串替换所有无效字符
        var pascalCase = invalidCharsRgx.Replace(whiteSpace.Replace(original, "_"), string.Empty)

            // 用下划线分割
            .Split(new[] {'_'}, StringSplitOptions.RemoveEmptyEntries)

            // 首字母设置为大写
            .Select(w => startsWithLowerCaseChar.Replace(w, m => m.Value.ToUpper()))

            // 如果没有下一个小写字母(ABC -> Abc),则将第二个及所有后面的大写字母替换为小写字母
            .Select(w => firstCharFollowedByUpperCasesOnly.Replace(w, m => m.Value.ToLower()))

            // 数字后面的第一个小写字母 设置大写(Ab9cd -> Ab9Cd)
            .Select(w => lowerCaseNextToNumber.Replace(w, m => m.Value.ToUpper()))

            // 第二个小写字母和下一个大写字母,除非最后一个字母后跟任何小写字母 (ABcDEf -> AbcDef)
            .Select(w => upperCaseInside.Replace(w, m => m.Value.ToLower()));

        return string.Concat(pascalCase);
    }

    #endregion

    /// <summary>
    ///     判断字符串是否空
    /// </summary>
    /// <param name="text"></param>
    /// <returns></returns>
    public static bool IsNotNull(this string text)
    {
        return !string.IsNullOrEmpty(text);
    }

    /// <summary>
    ///     指示指定的字符串是 null、空还是仅由空白字符组成。
    /// </summary>
    public static bool IsNotNullOrWhiteSpace(this string value)
    {
        return !string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    ///     判断字符串是否空
    /// </summary>
    /// <param name="text"></param>
    /// <returns></returns>
    public static bool IsNull(this string? text)
    {
        return string.IsNullOrEmpty(text);
    }

    /// <summary>
    ///     是否是数字
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    public static bool IsInt(this string value)
    {
        if (value.IsNull())
            return false;
        const string pattern = "^[0-9]*$";
        var rx = new Regex(pattern);
        return value != null && rx.IsMatch(value);
    }
    
    /// <summary>
    ///     是否是数字
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    public static bool IsNumber(this string value)
    {
        if (value.IsNull())
            return false;
        const string pattern = @"^-?\d+(\.\d+)?$"; // 更新正则表达式以支持小数
        var rx = new Regex(pattern);
        return value != null && rx.IsMatch(value);
    }

    /// <summary>
    ///     获得字符串中开始和结束字符串中间得值
    /// </summary>
    /// <param name="str">字符串</param>
    /// <param name="s">开始</param>
    /// <param name="e">结束</param>
    /// <returns></returns>
    public static string GetRegex(this string str, string s, string e)
    {
        var rg = new Regex("(?<=(" + s + "))[.\\s\\S]*?(?=(" + e + "))",
            RegexOptions.Multiline | RegexOptions.Singleline);
        return rg.Match(str).Value;
    }

    /// <summary>
    ///     获得字符串中开始和结束字符串中间得值
    /// </summary>
    /// <param name="str"></param>
    /// <param name="startStr"></param>
    /// <param name="endStr"></param>
    /// <returns></returns>
    public static string GetStringBetween(this string str, string startStr, string endStr)
    {
        var startIndex = str.IndexOf(startStr, StringComparison.Ordinal) + startStr.Length;
        var endIndex = str.IndexOf(endStr, startIndex, StringComparison.Ordinal);
        return endIndex == -1 ? "" : str.Substring(startIndex, endIndex - startIndex);
    }

    /// <summary>
    ///     获取 JsonElement 实际的值
    /// </summary>
    /// <param name="value">对象值</param>
    /// <returns>
    ///     <see cref="object" />
    /// </returns>
    public static object? GetJsonElementValue(this object? value)
    {
        if (value == null || value is not JsonElement ele) return value;

        // 处理 Array 类型的值
        if (ele.ValueKind == JsonValueKind.Array)
        {
            var arrEle = ele.EnumerateArray();
            var length = ele.GetArrayLength();
            var arr = new object[length];

            var i = 0;
            foreach (var item in arrEle)
            {
                // 递归处理
                arr[i] = GetJsonElementValue(item);
                i++;
            }

            return arr;
        }

        // 处理单个值
        object? actValue = ele.ValueKind switch
        {
            JsonValueKind.String => ele.GetString(),
            JsonValueKind.True => true,
            JsonValueKind.False => false,
            JsonValueKind.Null => default,
            JsonValueKind.Number => ele.TryGetInt32(out var num) ? num : ele.GetInt64(),
            _ => throw new ArgumentException("Only int, long, string, boolean and null types or array types constructed by them are supported.")
        };

        // 处理 long 类型问题
        if (actValue is long longValue and >= int.MinValue and <= int.MaxValue)
        {
            actValue = (int)longValue;
        }

        return actValue;
    }
    
    /// <summary>
    ///     处理泛型类型转字符串打印问题
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public static string HandleGenericType(Type type)
    {
        if (type == null) return string.Empty;

        // var typeName = type.FullName ?? (!string.IsNullOrEmpty(type.Namespace) ? type.Namespace + "." : string.Empty) + type.Name;
        var typeName = type.Name;
        // 处理泛型类型问题
        if (type.IsConstructedGenericType)
        {
            var prefix = type.GetGenericArguments()
                .Select(genericArg => HandleGenericType(genericArg))
                .Aggregate((previous, current) => previous + ", " + current);

            typeName = typeName.Split('`').First() + "<" + prefix + ">";
        }

        return typeName;
    }

    /// <summary>
    /// Aes解密
    /// </summary>
    /// <param name="cipherText"></param>
    /// <param name="Key"></param>
    /// <returns></returns>
    public static string DecryptStringFromBytes_Aes(byte[] cipherText, byte[] Key)
    {
        using (var aesAlg = Aes.Create())
        {
            aesAlg.Key = Key;
            aesAlg.IV = Key;
            aesAlg.Mode = CipherMode.ECB;
            aesAlg.Padding = PaddingMode.PKCS7;

            var decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

            using (var ms = new MemoryStream(cipherText))
            {
                using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                {
                    using (var sr = new StreamReader(cs))
                    {
                        return sr.ReadToEnd();
                    }
                }
            }
        }
    }
    /// <summary>
    /// 转换成支持的cron表达式
    /// </summary>
    /// <param name="cron"></param>
    /// <returns></returns>
    public static string ConvertCron(string cron)
    {
        var sp = cron.Split(' ');
        var newCron = "";
        for (var i = 0; i < 6; i++) newCron += " " + sp[i];
        return newCron.TrimStart().TrimEnd();
    }
    
    /// <summary>
    ///  根据子网掩码获取CIDR后缀长度
    /// </summary>
    /// <param name="subnetMask"></param>
    /// <returns></returns>
    public static int GetSubnetSize(string subnetMask)
    {
        IPAddress mask = IPAddress.Parse(subnetMask);
        byte[] bytes = mask.GetAddressBytes();
        int size = 0;

        for (int i = 0; i < bytes.Length; i++)
        {
            for (int j = 7; j >= 0; j--)
            {
                if ((bytes[i] & (1 << j)) != 0)
                {
                    size++;
                }
                else
                {
                    return size;
                }
            }
        }

        return size;
    }
    
    /// <summary>
    /// 根据CIDR后缀长度反向生成子网掩码
    /// </summary>
    /// <param name="cidrPrefixLength"></param>
    /// <returns></returns>
    public  static string GenerateSubnetMask(int cidrPrefixLength)
    {
        byte[] bytes = new byte[4];

        for (int i = 0; i < 4; i++)
        {
            if (cidrPrefixLength >= 8)
            {
                bytes[i] = 255;
                cidrPrefixLength -= 8;
            }
            else if (cidrPrefixLength > 0)
            {
                bytes[i] = (byte)(255 - (Math.Pow(2, 8 - cidrPrefixLength) - 1));
                cidrPrefixLength = 0;
            }
        }

        return new IPAddress(bytes).ToString();
    }
    
    /// <summary>
    /// 根据ip获取CIDR后缀长度
    /// </summary>
    /// <param name="ipAddressWithCidr"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public static int GetCidrPrefixLength(string ipAddressWithCidr)
    {
        string[] parts = ipAddressWithCidr.Split('/');
        if (parts.Length == 2)
        {
            if (int.TryParse(parts[1], out var cidrPrefixLength))
            {
                return cidrPrefixLength;
            }
        }

        throw new ArgumentException("Invalid IP Address with CIDR.");
    }
}