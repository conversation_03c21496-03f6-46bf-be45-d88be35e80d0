using Common.Enums;
using Common.Extension;
using Feng.Common.Util;
using Furion.Templates;
using DateTime = Common.Extension.DateTime;

namespace Feng.Common.Extension;

/// <summary>
///     重写console
/// </summary>
public static class Console
{
    private static readonly List<ConsoleColor> Colors = new() {ConsoleColor.Yellow, ConsoleColor.DarkGreen, ConsoleColor.Blue, ConsoleColor.DarkCyan, ConsoleColor.White, ConsoleColor.Magenta};
    private static readonly Random Random = new();

    public static void WriteLine(string msg)
    {
        System.Console.ResetColor();
        var number = Random.Next(0, Colors.Count - 1);
        System.Console.ForegroundColor = Colors[number];
        System.Console.WriteLine(msg);
    }

    /// <summary>
    /// </summary>
    /// <param name="msg"></param>
    /// <param name="color"></param>
    public static void WriteLine(string msg, ConsoleColor color)
    {
        System.Console.ResetColor();
        System.Console.ForegroundColor = color;
        System.Console.WriteLine(msg);
    }

    /// <summary>
    ///     推送消息模板
    /// </summary>
    /// <param name="identifier">标识符</param>
    /// <param name="registerAddress">地址</param>
    /// <param name="variableStatus">状态</param>
    /// <param name="errorCode">错误码</param>
    /// <param name="errorMessage">错误信息</param>
    /// <param name="title">抬头</param>
    public static string Read(string identifier, string registerAddress, VariableStatusTypeEnum variableStatus, int errorCode, string errorMessage = "", string title = "实时读取")
    {
        return TP.Wrapper(title,
            $"##时间## 【{DateTime.NowString()}】",
            $"##标识符## 【{identifier}】",
            $"{(registerAddress.IsNotNull() ? "##地址##" + "【" + registerAddress + "】" : "")}",
            $"##读取状态## 【{EnumUtil.GetEnumDesc(variableStatus)}】",
            $"##状态码## 【{errorCode}】",
            $"{(errorMessage.IsNotNull() ? "##错误信息##" + "【" + errorMessage + "】" : "")}");
    }
}