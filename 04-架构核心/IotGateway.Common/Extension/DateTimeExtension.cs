using System.Globalization;
using System.Runtime.InteropServices;

namespace Common.Extension;

/// <summary>
///     时间拓展类
/// </summary>
public static class DateTime
{
    public static bool UtcTime = true;

    public static System.DateTime Now()
    {
        return UtcTime ? System.DateTime.UtcNow : System.DateTime.Now;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public static System.DateTime ShangHai()
    {
        try
        {
            // 设置时区为 Asia/Shanghai
            var timeZone = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
                : TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai");
            // 获取当前时间的 UTC 时间
            var utcTime = System.DateTime.UtcNow;
            // 将 UTC 时间转换为指定时区的本地时间
            return TimeZoneInfo.ConvertTimeFromUtc(utcTime, timeZone);
        }
        catch
        {
            return System.DateTime.Now;
        }
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public static string ShangHaiString()
    {
        try
        {
            // 设置时区为 Asia/Shanghai
            var timeZone = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
                : TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai");
            // 获取当前时间的 UTC 时间
            var utcTime = System.DateTime.UtcNow;
            // 将 UTC 时间转换为指定时区的本地时间
            return TimeZoneInfo.ConvertTimeFromUtc(utcTime, timeZone).ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch
        {
            return System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public static string NowString(string format = "yyyy-MM-dd HH:mm:ss")
    {
        var time = UtcTime ? System.DateTime.UtcNow : System.DateTime.Now;
        return time.ToString(format);
    }

    /// <summary>
    ///     时间戳转为C#格式时间
    /// </summary>
    /// <returns>C#格式时间</returns>
    public static System.DateTime ToTime(double timeStamp)
    {
        var dtDateTime = new System.DateTime(1970, 1, 1);
        dtDateTime = dtDateTime.AddMilliseconds(timeStamp).ToLocalTime();
        return dtDateTime;
    }

    /// <summary>
    ///     时间戳转为C#格式时间
    /// </summary>
    /// <returns>C#格式时间</returns>
    public static System.DateTime ToTime(object timeStamp)
    {
        var corTimeStamp = (long) timeStamp;
        var dtDateTime = new System.DateTime(1970, 1, 1);
        dtDateTime = dtDateTime.AddMilliseconds(corTimeStamp).ToLocalTime();
        return dtDateTime;
    }

    /// <summary>
    ///     时间戳转为C#格式时间
    /// </summary>
    /// <returns>C#格式时间</returns>
    public static System.DateTime ToTime(long timeStamp)
    {
        var douUnixTimeStamp = Convert.ToDouble(timeStamp);
        var dtDateTime = new System.DateTime(1970, 1, 1);
        dtDateTime = dtDateTime.AddMilliseconds(douUnixTimeStamp).ToLocalTime();
        return dtDateTime;
    }

    // /// <summary>
    // ///     Unix时间戳转为C#格式时间
    // /// </summary>
    // /// <returns>C#格式时间</returns>
    // public static SystemServices.DateTime ToUtc(double unixTimeStamp)
    // {
    //     var dtDateTime = new SystemServices.DateTime(1970, 1, 1);
    //     dtDateTime = dtDateTime.AddMilliseconds(unixTimeStamp).ToLocalTime();
    //     return dtDateTime;
    // }

    /// <summary>
    ///     毫秒级时间戳
    ///     返回字符串类型
    /// </summary>
    /// <param name="time"> DateTime时间格式</param>
    /// <returns>Unix时间戳格式</returns>
    public static string ToTsStr(System.DateTime time)
    {
        var delta = time - new System.DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds).ToString();
    }

    /// <summary>
    ///     毫秒级时间戳
    ///     返回字符串类型
    /// </summary>
    /// <returns>Unix时间戳格式</returns>
    public static string ToTsStr()
    {
        var time = Now();
        var delta = time - new System.DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds).ToString();
    }

    /// <summary>
    ///     将时间戳转换成北京时间
    /// </summary>
    /// <param name="timestamp"></param>
    /// <param name="type">返回格式,1:datetime；2string</param>
    /// <returns></returns>
    public static object ToShanghai(long timestamp, int type = 1)
    {
        try
        {
            // 将毫秒时间戳转换为DateTimeOffset对象
            var dateTimeOffset = DateTimeOffset.FromUnixTimeMilliseconds(timestamp);
            // 获取北京时区的TimeZoneInfo对象
            var beijingTimeZone = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
                : TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai");
            // 将DateTimeOffset对象转换为北京时间
            var beijingTime = TimeZoneInfo.ConvertTime(dateTimeOffset, beijingTimeZone);
            return type == 1 ? beijingTime : beijingTime.ToString("yyyy-MM-dd HH:mm:ss:fff");
        }
        catch
        {
            return type == 1 ? System.DateTime.Now : System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff");
        }
    }

    /// <summary>
    ///     毫秒级时间戳
    ///     返回字符串类型
    /// </summary>
    /// <param name="time"> DateTime时间格式</param>
    /// <returns>Unix时间戳格式</returns>
    public static long ToLong(System.DateTime time)
    {
        var delta = time - new System.DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds);
    }

    /// <summary>
    ///     毫秒级时间戳
    ///     返回字符串类型
    /// </summary>
    /// <returns>Unix时间戳格式</returns>
    public static long ToLong()
    {
        var delta = Now() - new System.DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds);
    }

    /// <summary>
    ///     毫秒级时间戳
    /// </summary>
    /// <returns></returns>
    public static long TimeStamp()
    {
        var delta = Now() - new System.DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds);
    }

    /// <summary>
    ///     毫秒转天时分秒
    /// </summary>
    /// <param name="ms"></param>
    /// <returns></returns>
    public static string FormatTime(long ms)
    {
        var ss = 1000;
        var mi = ss * 60;
        var hh = mi * 60;
        var dd = hh * 24;

        var day = ms / dd;
        var hour = (ms - day * dd) / hh;
        var minute = (ms - day * dd - hour * hh) / mi;
        var second = (ms - day * dd - hour * hh - minute * mi) / ss;
        var milliSecond = ms - day * dd - hour * hh - minute * mi - second * ss;

        var sDay = day < 10 ? "0" + day : "" + day; //天
        var sHour = hour < 10 ? "0" + hour : "" + hour; //小时
        var sMinute = minute < 10 ? "0" + minute : "" + minute; //分钟
        var sSecond = second < 10 ? "0" + second : "" + second; //秒
        var sMilliSecond = milliSecond < 10 ? "0" + milliSecond : "" + milliSecond; //毫秒
        sMilliSecond = milliSecond < 100 ? "0" + sMilliSecond : "" + sMilliSecond;

        return string.Format("{0} 天 {1} 小时 {2} 分 {3} 秒", sDay, sHour, sMinute, sSecond);
    }

    /// <summary>
    ///     将string转换为DateTime，若失败则返回日期最小值
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static System.DateTime ParseToDateTime(this string str)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(str)) return System.DateTime.MinValue;
            if (str.Contains("-") || str.Contains("/")) return System.DateTime.Parse(str);

            var length = str.Length;
            switch (length)
            {
                case 4:
                    return System.DateTime.ParseExact(str, "yyyy", CultureInfo.CurrentCulture);

                case 6:
                    return System.DateTime.ParseExact(str, "yyyyMM", CultureInfo.CurrentCulture);

                case 8:
                    return System.DateTime.ParseExact(str, "yyyyMMdd", CultureInfo.CurrentCulture);

                case 10:
                    return System.DateTime.ParseExact(str, "yyyyMMddHH", CultureInfo.CurrentCulture);

                case 12:
                    return System.DateTime.ParseExact(str, "yyyyMMddHHmm", CultureInfo.CurrentCulture);

                case 14:
                    return System.DateTime.ParseExact(str, "yyyyMMddHHmmss", CultureInfo.CurrentCulture);

                default:
                    return System.DateTime.ParseExact(str, "yyyyMMddHHmmss", CultureInfo.CurrentCulture);
            }
        }
        catch
        {
            return System.DateTime.MinValue;
        }
    }

    /// <summary>
    ///     将object转换为long，若失败则返回0
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static long ParseToLong(this object obj)
    {
        try
        {
            return long.Parse(obj.ToString() ?? string.Empty);
        }
        catch
        {
            return 0L;
        }
    }
}