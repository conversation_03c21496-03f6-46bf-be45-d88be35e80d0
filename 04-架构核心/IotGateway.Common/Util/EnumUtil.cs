namespace Feng.Common.Util;

/// <summary>
///     枚举扩展
/// </summary>
public static class EnumUtil
{
    // 枚举显示字典缓存
    private static readonly ConcurrentDictionary<Type, Dictionary<int, string>> EnumDisplayValueDict = new();

    // 枚举值字典缓存
    private static readonly ConcurrentDictionary<Type, Dictionary<int, string>> EnumNameValueDict = new();

    // 枚举类型缓存
    private static readonly ConcurrentDictionary<string, Type> _enumTypeDict = null!;

    /// <summary>
    ///     获取枚举对象Key与名称的字典（缓存）
    /// </summary>
    /// <param name="enumType"></param>
    /// <returns></returns>
    public static Dictionary<int, string> GetEnumDictionary(Type enumType)
    {
        if (!enumType.IsEnum)
            throw Oops.Oh("非法数据");

        // 查询缓存
        var enumDic = EnumNameValueDict.ContainsKey(enumType)
            ? EnumNameValueDict[enumType]
            : new Dictionary<int, string>();
        if (enumDic.Count != 0) return enumDic;
        // 取枚举类型的Key/Value字典集合
        enumDic = GetEnumDictionaryItems(enumType);

        // 缓存
        EnumNameValueDict[enumType] = enumDic;
        return enumDic;
    }

    /// <summary>
    ///     获取枚举对象Key与名称的字典
    /// </summary>
    /// <param name="enumType"></param>
    /// <returns></returns>
    private static Dictionary<int, string> GetEnumDictionaryItems(IReflect enumType)
    {
        // 获取类型的字段,初始化一个有限长度的字典
        var enumFields = enumType.GetFields(BindingFlags.Public | BindingFlags.Static);
        Dictionary<int, string> enumDic = new(enumFields.Length);

        // 遍历字段数组获取key和name
        foreach (var enumField in enumFields)
        {
#pragma warning disable CS8605 // 取消装箱可能为 null 的值。
            var intValue = (int) enumField.GetValue(enumType);
#pragma warning restore CS8605 // 取消装箱可能为 null 的值。
            enumDic[intValue] = enumField.Name;
        }

        return enumDic;
    }

    /// <summary>
    ///     获取枚举类型key与描述的字典（缓存）
    /// </summary>
    /// <param name="enumType"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public static Dictionary<int, string> GetEnumDescDictionary(Type enumType)
    {
        if (!enumType.IsEnum)
            throw Oops.Oh("非法数据");

        // 查询缓存
        var enumDic = EnumDisplayValueDict.ContainsKey(enumType)
            ? EnumDisplayValueDict[enumType]
            : new Dictionary<int, string>();
        if (enumDic.Count != 0) return enumDic;
        // 取枚举类型的Key/Value字典集合
        enumDic = GetEnumDescDictionaryItems(enumType);

        // 缓存
        EnumDisplayValueDict[enumType] = enumDic;
        return enumDic;
    }

    /// <summary>
    ///     获取枚举类型key与描述的字典（没有描述则获取name）
    /// </summary>
    /// <param name="enumType"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private static Dictionary<int, string> GetEnumDescDictionaryItems(Type enumType)
    {
        // 获取类型的字段,初始化一个有限长度的字典
        var enumFields = enumType.GetFields(BindingFlags.Public | BindingFlags.Static);
        Dictionary<int, string> enumDic = new(enumFields.Length);

        // 遍历字段数组获取key和name
        foreach (var enumField in enumFields)
        {
#pragma warning disable CS8605 // 取消装箱可能为 null 的值。
            var intValue = (int) enumField.GetValue(enumType);
#pragma warning restore CS8605 // 取消装箱可能为 null 的值。
            var desc = enumField.GetDescriptionValue<DescriptionAttribute>();
            enumDic[intValue] = desc != null && !string.IsNullOrEmpty(desc.Description)
                ? desc.Description
                : enumField.Name;
        }

        return enumDic;
    }

    /// <summary>
    ///     从程序集中查找指定枚举类型
    /// </summary>
    /// <param name="typeName"></param>
    /// <returns></returns>
    public static Type TryToGetEnumType(string typeName)
    {
        // 按名称查找
        return (_enumTypeDict.ContainsKey(typeName) ? _enumTypeDict[typeName] : null)!;
    }

    /// <summary>
    ///     从程序集中加载所有枚举类型
    /// </summary>
    /// <param name="assembly"></param>
    /// <returns></returns>
    private static ConcurrentDictionary<string, Type> LoadEnumTypeDict(Assembly assembly)
    {
        // 取程序集中所有类型
        var typeArray = assembly.GetTypes();

        // 过滤非枚举类型,转成字典格式并返回
        var dict = typeArray.Where(o => o.IsEnum).ToDictionary(o => o.Name, o => o);
        ConcurrentDictionary<string, Type> enumTypeDict = new(dict);
        return enumTypeDict;
    }

    /// <summary>
    /// </summary>
    /// <param name="tField"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static string GetEnumDesc<T>(T tField)
    {
        try
        {
            if (tField == null)
                return "";
            if (!Enum.IsDefined(typeof(T), tField))
                return "";
            var description = string.Empty; //结果
            var inputType = tField?.GetType(); //输入的类型
            var descType = typeof(DescriptionAttribute); //目标查找的描述类型

            var fieldStr = tField?.ToString(); //输入的字段字符串
            var field = inputType?.GetField(fieldStr!); //目标字段

            Debug.Assert(field != null, nameof(field) + " != null");
            var isDefined = field.IsDefined(descType, false); //判断描述是否在字段的特性
            if (!isDefined) return description;
            var enumAttributes = (DescriptionAttribute[]) field //得到特性信息
                .GetCustomAttributes(descType, false);
            description = enumAttributes.FirstOrDefault()?.Description ?? string.Empty;
            return description;
        }
        catch (Exception)
        {
            return "";
        }
    }


    /// <summary>
    ///     根据属性描述获取枚举值
    /// </summary>
    /// <typeparam name="T">类型</typeparam>
    /// <param name="des">属性说明</param>
    /// <returns>枚举值</returns>
    public static T GetEnum<T>(string des) where T : struct, IConvertible
    {
        var type = typeof(T);
        if (!type.IsEnum) return default;

        var enums = (T[]) Enum.GetValues(type);
        if (!Enum.TryParse(des, out T temp)) temp = default;

        foreach (var t in enums)
        {
            var name = t.ToString();
            var field = type.GetField(name);
            var objs = field?.GetCustomAttributes(typeof(DescriptionAttribute), false);
            if (objs == null || objs.Length == 0) continue;

            var descriptionAttribute = (DescriptionAttribute) objs[0];
            var edes = descriptionAttribute.Description;
            if (des != edes) continue;
            temp = t;
            break;
        }

        return temp;
    }
}