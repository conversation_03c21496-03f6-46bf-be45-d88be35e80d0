namespace Common.Enums;

/// <summary>
///     串口号
/// </summary>
public enum SerialNumberEnum
{
    [Description("/dev/ttyS1")] [Display(Name = "/dev/ttyS1")]
    ttyS1 = 1,

    [Description("/dev/ttyS3")] [Display(Name = "/dev/ttyS3")]
    ttyS3 = 2,

    [Description("/dev/ttyS5")] [Display(Name = "/dev/ttyS5")]
    ttyS5 = 3,

    [Description("/dev/ttyS0")] [Display(Name = "/dev/ttyS0")]
    ttyS0 = 4,
    
    [Description("/dev/ttyS2")] [Display(Name = "/dev/ttyS2")]
    ttyS2 = 5,
    
    [Description("/dev/ttyS4")] [Display(Name = "/dev/ttyS4")]
    ttyS4 = 6,
    
    [Description("/dev/ttyS9")] [Display(Name = "/dev/ttyS9")]
    ttyS9 = 7,
    
    [Description("/dev/ttyS7")] [Display(Name = "/dev/ttyS7")]
    ttyS7 = 8,
    
    [Description("/dev/ttyS8")] [Display(Name = "/dev/ttyS8")]
    ttyS8 = 9
}