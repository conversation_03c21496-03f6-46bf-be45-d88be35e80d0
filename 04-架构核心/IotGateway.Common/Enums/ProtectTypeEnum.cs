namespace Common.Enums;

/// <summary>
///     读写方式
/// </summary>
public enum ProtectTypeEnum
{
    
    /// <summary>
    ///     只读
    /// </summary>
    [Description("只读")] [Display(Name = "只读")]
    ReadOnly = 1,

    /// <summary>
    ///     读写
    /// </summary>
    [Description("读写")] [Display(Name = "读写")]
    ReadWrite = 2,
    
    [Description("只写")] [Display(Name = "只写")]
    WriteOnly = 3
}