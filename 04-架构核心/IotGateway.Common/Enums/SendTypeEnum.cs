namespace Common.Enums;

/// <summary>
///     数据上报方式
/// </summary>
public enum SendTypeEnum
{
    /// <summary>
    ///     总是上报
    /// </summary>
    [Description("总是上报")] [Display(Name = "总是上报")]
    Always = 1,

    /// <summary>
    ///     从不上报
    /// </summary>
    [Description("从不上报")] [Display(Name = "从不上报")]
    Never = 2,

    /// <summary>
    ///     变化上报
    /// </summary>
    [Description("变化上报")] [Display(Name = "变化上报")]
    Changed = 3
}