namespace Common.Enums;

/// <summary>
///     状态查询
/// </summary>
public enum VariableStatusTypeEnum
{
    [Description("Good")] [Display(Name = "Good")]
    Good = 1,

    [Description("读取失败")] [Display(Name = "读取失败")]
    Error = 2,

    [Description("方法错误")] [Display(Name = "方法错误")]
    MethodError = 3,

    [Description("表达式错误")] [Display(Name = "表达式错误")]
    ExpressionError = 4,

    [Description("脚本错误")] [Display(Name = "脚本错误")]
    ScriptError = 5,

    [Description("Bad")] [Display(Name = "Bad")]
    Bad = 6,

    [Description("未知的")] [Display(Name = "未知的")]
    UnKnow = 7,

    [Description("转换失败")] [Display(Name = "转换失败")]
    Convert = 8
}