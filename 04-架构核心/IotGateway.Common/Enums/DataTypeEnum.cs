namespace Common.Enums;

/// <summary>
///     读取采集类型
/// </summary>
public enum DataTypeEnum
{
    [Description("bit")] [Display(Name = "bit")]
    Bit = 1,

    [Description("bool")] [Display(Name = "bool")]
    Bool = 2,

    [Description("uint16")] [Display(Name = "uint16")]
    Uint16 = 3,

    [Description("int16")] [Display(Name = "int16")]
    Int16 = 4,

    [Description("uint32")] [Display(Name = "uint32")]
    Uint32 = 5,

    [Description("int32")] [Display(Name = "int32")]
    Int32 = 6,

    [Description("float")] [Display(Name = "float")]
    Float = 7,

    [Description("uint64")] [Display(Name = "uint64")]
    Uint64 = 8,

    [Description("int64")] [Display(Name = "int64")]
    Int64 = 9,

    [Description("double")] [Display(Name = "double")]
    Double = 10,

    [Description("string")] [Display(Name = "string")]
    String = 11, 
    
    [Description("bcd")] [Display(Name = "bcd")]
    Bcd = 14,
    
    [Description("byte")] [Display(Name = "byte")]
    Byte = 12,
    
    [Description("bcd-32")] [Display(Name = "bcd-32")]
    Bcd_32 = 15,
}

/// <summary>
/// 字符串编码枚举
/// </summary>
public enum StringEnum
{
    [Description("utf8")] [Display(Name = "utf8")]
    Utf8 = 1,
    [Description("unicode")] [Display(Name = "unicode")]
    Unicode = 2,
    [Description("ascii")] [Display(Name = "ascii")]
    Ascii = 3,
    [Description("unicode-big")] [Display(Name = "unicode-big")]
    UnicodeBig = 4,
    [Description("utf32")] [Display(Name = "utf32")]
    Utf32 = 5,
    [Description("gb2312")] [Display(Name = "gb2312")]
    Gb2312 = 6,
}