namespace Common.Enums;

/// <summary>
///     转发属性
/// </summary>
public enum TransPondDataTypeEnum
{
    [Description("Bool")] [Display(Name = "bool")]
    Bool = 1,

    [Description("Int")] [Display(Name = "int")]
    Int = 2,

    [Description("Double")] [Display(Name = "double")]
    Double = 3,

    [Description("String")] [Display(Name = "string")]
    String = 4,

    [Description("Bytes")] [Display(Name = "bytes")]
    Bytes = 5
}

/// <summary>
///     数据来源 1:直接取值; 2:计算赋值；3：手动写值
/// </summary>
public enum ValueSourceEnum
{
    /// <summary>
    ///     直接取值
    /// </summary>
    [Description("直接取值")] [Display(Name = "直接取值")]
    Get = 1,

    /// <summary>
    ///     计算赋值
    /// </summary>
    [Description("计算赋值")] [Display(Name = "计算赋值")]
    Calculate = 2,

    /// <summary>
    ///     手动写值
    /// </summary>
    [Description("手动写值")] [Display(Name = "手动写值")]
    Static = 3
}