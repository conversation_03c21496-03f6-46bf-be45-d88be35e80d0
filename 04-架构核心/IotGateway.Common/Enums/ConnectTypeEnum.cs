namespace Common.Enums;

/// <summary>
/// 连接方式：1：网口；2：串口；3：udp
/// </summary>
public enum ConnectTypeEnum
{
    [Description("网口")] [Display(Name = "网口")]
    NetWork = 1,
    
    [Description("串口")] [Display(Name = "串口")]
    SerialPort = 2,

    [Description("Udp")] [Display(Name = "Udp")]
    Udp = 3
}

/// <summary>
/// 注塑机设备连接方式
/// </summary>
public enum DriverConnectTypeEnum
{
    [Description("网口")] [Display(Name = "网口")]
    NetWork = 1,
    
    [Description("串口")] [Display(Name = "串口")]
    SerialPort = 2,
}