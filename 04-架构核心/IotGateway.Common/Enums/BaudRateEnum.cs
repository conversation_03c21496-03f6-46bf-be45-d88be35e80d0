namespace Common.Enums;

/// <summary>
///     波特率
/// </summary>
public enum BaudRateEnum
{
    [Description("1200")] [Display(Name = "1200")]
    Bps1200 = 0,

    [Description("2400")] [Display(Name = "2400")]
    Bps2400 = 1,

    [Description("4800")] [Display(Name = "4800")]
    Bps4800 = 2,

    [Description("9600")] [Display(Name = "9600")]
    Bps9600 = 3,

    [Description("19200")] [Display(Name = "19200")]
    Bps19200 = 4,
    
    [Description("38400")] [Display(Name = "38400")]
    Bps38400 = 6,
    
    [Description("57600")] [Display(Name = "57600")]
    Bps57600 = 7,

    [Description("115200")] [Display(Name = "115200")]
    Bps115200 = 8,
    
    [Description("187500")] [Display(Name = "187500")]
    Bps187500 = 5,
}