using System.Text.Json.Serialization;
using Common.Enums;
using DateTime = Common.Extension.DateTime;

namespace Common.Models;

/// <summary>
/// </summary>
public class PayLoad
{
    /// <summary>
    ///     设备Id
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 当前时间的毫秒时间戳
    /// </summary>
    public long Ts { get; set; } = DateTime.ToLong();

    /// <summary>
    ///     设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// </summary>
    public DeviceStatusTypeEnum DeviceStatus { get; set; } = DeviceStatusTypeEnum.Good;

    /// <summary>
    /// </summary>
    public Dictionary<string, ParamValue> Values { get; set; } = new();

    /// <summary>
    ///     驱动名
    /// </summary>
    public string DriverName { get; set; }

}