using System.Text.Json.Serialization;
using Common.Enums;
using Feng.Common.Util;

namespace Common.Models;

/// <summary>
///     数据
/// </summary>
public class ParamValue
{
    /// <summary>
    ///     上一次的值
    /// </summary>
    public object? CookieValue { get; set; }
    
    /// <summary>
    ///     上一次上报时间
    /// </summary>
    [JsonIgnore]
    public long CookieTime { get; set; }
    
    /// <summary>
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 属性名称
    /// </summary>
    public string DeviceVariableName { get; set; }
    
    /// <summary>
    ///     值
    /// </summary>
    public string? Value { get; set; }

    /// <summary>
    /// 数据来源
    /// </summary>
    public ValueSourceEnum ValueSource { get; set; }
    
    /// <summary>
    ///     数据类型
    /// </summary>
    public DataTypeEnum DataType { get; set; }

    /// <summary>
    ///     转换数据类型
    /// </summary>
    public TransPondDataTypeEnum TransitionType { get; set; }

    /// <summary>
    ///     毫秒时间戳
    /// </summary>
    public long ReadTime { get; set; }

    /// <summary>
    /// </summary>
    public VariableStatusTypeEnum VariableStatus { get; set; }

    /// <summary>
    /// </summary>
    public string VariableStatusName => EnumUtil.GetEnumDesc(VariableStatus);

    /// <summary>
    ///     错误消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    ///     上报方式
    /// </summary>
    // [JsonIgnore]
    public SendTypeEnum SendType { get; set; }
    
    /// <summary>
    /// 为属性值添加自定义
    /// </summary>
    [JsonIgnore]
    public string? Custom { get; set; }
    
    /// <summary>
    /// 为属性值添加自定义-映射别名
    /// </summary>
    public string MappingAlias { get; set; }

    /// <summary>
    /// 是否发布
    /// </summary>
    public bool Release { get; set; }
}