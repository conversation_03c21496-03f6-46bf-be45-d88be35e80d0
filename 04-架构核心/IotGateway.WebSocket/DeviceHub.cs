using Common.Extension;
using Feng.Common.Extension;

namespace Feng.IotGateway.WebSocket;

/// <summary>
/// </summary>
public class DeviceHub : Hub
{
    private readonly SendMessageService _message;

    public DeviceHub(SendMessageService message)
    {
        _message = message;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public override Task OnConnectedAsync()
    {
        try
        {
            _message.Init(Clients);
            return base.OnConnectedAsync();
        }
        catch (Exception ex)
        {
            Log.Error("OnConnectedAsync:" + ex.Message);
            return base.OnConnectedAsync();
        }
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public override Task OnDisconnectedAsync(Exception? exception)
    {
        _message.Init(Clients);
        return base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// </summary>
    /// <param name="groupName"></param>
    public async Task AddGroup(string groupName)
    {
        Console.WriteLine($"\n\n\n\n用户连接:{Context.ConnectionId},加入到组:{groupName} \n\n\n\n");
        if (groupName.IsNotNull())
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
    }

    /// <summary>
    /// </summary>
    /// <param name="groupName"></param>
    public async Task RemoveGroup(string groupName)
    {
        Console.WriteLine($"\n\n\n\n用户断开:{Context.ConnectionId},退出组:{groupName} \n\n\n\n");
        if (groupName.IsNotNull())
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
    }
}