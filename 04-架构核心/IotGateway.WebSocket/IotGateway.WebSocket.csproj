<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>Feng.IotGateway.WebSocket</RootNamespace>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <Reference Include="Microsoft.AspNetCore.SignalR.Core, Version=6.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60">
            <HintPath>C:\Program Files\dotnet\shared\Microsoft.AspNetCore.App\6.0.5\Microsoft.AspNetCore.SignalR.Core.dll</HintPath>
        </Reference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\IotGateway.Common\IotGateway.Common.csproj"/>
    </ItemGroup>

</Project>
