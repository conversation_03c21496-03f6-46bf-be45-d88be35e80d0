namespace Feng.IotGateway.WebSocket.Const;

/// <summary>
///     Socket推送方法
/// </summary>
public static class ConstMethod
{
    /// <summary>
    ///     设备事件
    /// </summary>
    public const string DeviceEvent = "{0}_Event";
    /// <summary>
    ///     设备运行状态
    /// </summary>
    public const string DeviceStatus = "DeviceStatus";

    /// <summary>
    ///     设备连接状态
    /// </summary>
    public const string DeviceConnectStatus = "DeviceConnectStatus";
    
    /// <summary>
    ///     设备采集到的实时数据
    /// </summary>
    public const string OnLineData = "OnLineData";
    
    /// <summary>
    ///     设备采集
    /// </summary>
    public const string DeviceThread = "{0}";

    /// <summary>
    ///     设备采集控制台
    /// </summary>
    public const string DeviceConsole = "{0}_Console";

    /// <summary>
    ///     设备采集上行输出
    /// </summary>
    public const string DeviceResp = "{0}_Resp";

    /// <summary>
    ///     设备属性写入
    /// </summary>
    public const string DeviceWrite = "{0}_Write";

    /// <summary>
    ///     MQTT配置中心
    /// </summary>
    public const string MqttManage = "Mqtt_Manage";

    /// <summary>
    ///     站内信
    /// </summary>
    public const string Message = "Message";

    /// <summary>
    ///     系统时间
    /// </summary>
    public const string DateTime = "DateTime";

    /// <summary>
    ///     系统运行状态
    /// </summary>
    public const string SysRunStatus = "SysRunStatus";

    /// <summary>
    ///     首页统计数据
    /// </summary>
    public const string IndexData = "IndexData";

    /// <summary>
    ///     Mqtt调试
    /// </summary>
    public const string MqttDebug = "MqttDebug";

    /// <summary>
    ///     串口调试
    /// </summary>
    public const string SerialDebug = "SerialDebug";

    /// <summary>
    ///     TcpClient调试
    /// </summary>
    public const string TcpClientDebug = "TcpClientDebug";

    /// <summary>
    ///     UdpClientDebug 调试
    /// </summary>
    public const string UdpClientDebug = "UdpClientDebug";

    /// <summary>
    ///     TcpServerDebug 调试
    /// </summary>
    public const string TcpServerDebug = "TcpServerDebug";

    /// <summary>
    ///     TcpServerConnectList 连接用户集合
    /// </summary>
    public const string TcpServerConnectList = "TcpServerConnectList";
    
    /// <summary>
    ///     云端要求刷新设备实时数据
    /// </summary>
    public const string CloudRequestRefreshRealTimeDeviceData = "{0}_CloudRequestRefreshRealTimeDeviceData";
    
    /// <summary>
    ///     FLink要求刷新设备实时数据
    /// </summary>
    public const string FLinkRequestRefreshRealTimeDeviceData = "{0}_FLinkRequestRefreshRealTimeDeviceData";
}