using Feng.IotGateway.WebSocket.Const;
using DateTime = Common.Extension.DateTime;

namespace Feng.IotGateway.WebSocket;

/// <summary>
///     socket推送消息
/// </summary>
public class SendMessageService : ISingleton
{
    private IHubCallerClients _clients = null!;

    /// <summary>
    ///     初始化
    /// </summary>
    /// <param name="clients"></param>
    public void Init(IHubCallerClients clients)
    {
        _clients = clients;
    }

    /// <summary>
    ///     转发上行消息发送
    /// </summary>
    /// <param name="context"></param>
    /// <param name="identifier"></param>
    /// <param name="info"></param>
    public async Task DeviceResp(string context, string identifier, bool info = false)
    {
        await Send($"当前时间:【{DateTime.ShangHaiString()}】 " + context, string.Format(ConstMethod.DeviceResp, identifier), info);
    }

    /// <summary>
    ///     设备输出消息
    /// </summary>
    /// <param name="context"></param>
    /// <param name="id"></param>
    /// <param name="info"></param>
    public async Task DeviceConsole(string context, long id, bool info = false)
    {
        await Send($"当前时间:【{DateTime.ShangHaiString()}】 " + context, string.Format(ConstMethod.DeviceConsole, id), info);
    }

    /// <summary>
    ///     消息发送
    /// </summary>
    /// <param name="context"></param>
    /// <param name="topic"></param>
    /// <param name="info"></param>
    /// <returns></returns>
    public async Task Send(string context, string topic = "DeviceResult", bool info = false)
    {
        try
        {
            if (info)
                Log.Information(context);
            // #if DEBUG
            //             Console.WriteLine($"推送数据:{context},Topic:{topic}");
            // #endif
            if (_clients == null)
                return;
            await _clients.Group(topic).SendAsync(topic, context);
        }
        catch (Exception ex)
        {
            Log.Error($"消息推送失败,错误:{ex.Message}！");
        }
    }

    /// <summary>
    ///     错误消息发送
    /// </summary>
    /// <param name="context"></param>
    /// <param name="topic"></param>
    public async Task Error(string context, string topic = "Error")
    {
        try
        {
            if (_clients == null) return;
            await _clients.Group(topic).SendAsync(topic, context);
            // _clients.All.SendAsync(topic, context);
        }
        catch (Exception ex)
        {
            Log.Error($"Error消息推送失败,错误:{ex.Message}！");
        }
    }
}