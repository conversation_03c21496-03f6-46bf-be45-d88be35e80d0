using TDengineDriver;

namespace TDengIne;

/// <summary>
///     TDengIne的连接服务
/// </summary>
public class ConnectService : ISingleton
{
    /// <summary>
    ///     连接
    /// </summary>
    /// <returns></returns>
    public IntPtr Connection()
    {
        try
        {
            var ip = "127.0.0.1";
#if DEBUG
            ip = "************";
#endif
            var connect = TDengine.Connect(ip, "root", "taosdata", "iotgateway", 6030);
            if (connect == IntPtr.Zero)
                throw new Exception("Connect to TDengine failed");
            return connect;
        }
        catch
        {
            return IntPtr.Zero;
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="res"></param>
    /// <param name="errorMsg"></param>
    public void CheckRes(IntPtr res, string errorMsg)
    {
        if (TDengine.ErrorNo(res) == 0) return;
        throw Oops.Oh(errorMsg + " since: " + TDengine.Error(res));
    }
}