<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>TDengIne</RootNamespace>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="TDengine.Connector" Version="3.0.2" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\04-架构核心\IotGateway.Common\IotGateway.Common.csproj" />
      <ProjectReference Include="..\..\04-架构核心\IotGateway.Core\IotGateway.Core.csproj" />
    </ItemGroup>

</Project>
