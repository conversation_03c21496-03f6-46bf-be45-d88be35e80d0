using Common.Enums;
using Common.Models;
using Furion.TaskQueue;
using Microsoft.Extensions.Logging;
using TDengineDriver;

namespace TDengIne;

/// <summary>
///     向TDengIne写入数据
/// </summary>
public class WriteService : ISingleton
{
    /// <summary>
    ///     TDengIne的连接
    /// </summary>
    private readonly ConnectService _connectService;

    /// <summary>
    /// </summary>
    private readonly ILogger<WriteService> _logger;

    private IntPtr _connect;

    public WriteService(ConnectService connectService, ILogger<WriteService> logger)
    {
        _connectService = connectService;
        _logger = logger;
        _connect = _connectService.Connection();
    }

    /// <summary>
    ///     采集数据上报
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task Write(PayLoad input)
    {
        await TaskQueued.EnqueueAsync((_, _) =>
        {
            try
            {
                var sql = $"INSERT INTO {input.DeviceName}  (ts";
                var valueSql = $"VALUES({input.Ts}";
                foreach (var (key, value) in input.Values.Where(w =>  w.Value is {Release: true, VariableStatus: VariableStatusTypeEnum.Good}))
                {
                    var val = value.Value;
                    if (val == null)
                        continue;
                    try
                    {
                        switch (value.TransitionType)
                        {
                            case TransPondDataTypeEnum.Bool:
                            {
                                sql += $",`{key}`";
                                valueSql += $",{val}";
                                break;
                            }
                            case TransPondDataTypeEnum.Int:
                            {
                                var intValue = Convert.ToInt64(val.Trim());
                                sql += $",`{key}`";
                                valueSql += $",{intValue}";
                                break;
                            }
                            case TransPondDataTypeEnum.Double:
                            {
                                var doubleValue = Convert.ToDouble(val.Trim());
                                sql += $",`{key}`";
                                valueSql += $",{doubleValue}";
                                break;
                            }
                            case TransPondDataTypeEnum.String:
                            case TransPondDataTypeEnum.Bytes:
                            default:
                            {
                                sql += $",`{key}`";
                                valueSql += $",'{val}'";
                                break;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"【TDengIne】 【{input.DeviceName}】 【{key}】 数据转换错误:{ex.Message}");
                    }
                }

                if (sql.TrimEnd().EndsWith("(ts"))
                    return ValueTask.CompletedTask;
                sql += ") " + valueSql + ");";
                try
                {
                    if (_connect == IntPtr.Zero)
                        _connect = _connectService.Connection();
                    // Console.WriteLine(sql);
                    var res = TDengine.Query(_connect, sql);
                    _connectService.CheckRes(res, $"【TDengIne】 failed to insert data,Sql:【{sql}】,");
                    //查看受影响的行数
                    // var affectedRows = TDengine.AffectRows(res);
                    // Console.WriteLine("【affectedRows】：" + affectedRows);
                    TDengine.FreeResult(res);
                    // GC.Collect();
                }
                catch (Exception e)
                {
                    _logger.LogError($"【TDengIne】 写入出现异常:{e.Message}");
                }
            }
            catch (Exception e)
            {
                _logger.LogError($"【TDengIne】 写入数据服务Error:{e.Message}");
            }

            return ValueTask.CompletedTask;
        });
    }
}