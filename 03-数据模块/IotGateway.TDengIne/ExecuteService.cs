using System.Dynamic;
using Common.Extension;
using Feng.Common.Extension;
using Furion.JsonSerialization;
using IotGateway.TDengIne;
using Microsoft.Extensions.Logging;
using TDengineDriver;
using TDengineDriver.Impl;
using DateTime = Common.Extension.DateTime;

namespace TDengIne;

/// <summary>
///     执行TDengIne的命令
/// </summary>
public class ExecuteService : ISingleton
{
    /// <summary>
    ///     TDengIne的连接
    /// </summary>
    private readonly ConnectService _connectService;

    private readonly ILogger<ExecuteService> _logger;

    public ExecuteService(ConnectService connectService, ILogger<ExecuteService> logger)
    {
        _connectService = connectService;
        _logger = logger;
    }

    /// <summary>
    ///     执行插入sql
    /// </summary>
    /// <param name="sql"></param>
    public bool Insert(string sql)
    {
        try
        {
            if (!sql.ToLower().StartsWith("insert"))
                throw Oops.Oh("禁止使用非insert语法！");
            var res = TDengine.Query(_connectService.Connection(), sql);
            _connectService.CheckRes(res, $"【TDengIne】 failed to insert data SQL:【{sql}】");
            // var affectedRows = TDengine.AffectRows(res);
            // Debug.WriteLine("【affectedRows自定义】: " + affectedRows);
            TDengine.FreeResult(res);
            return true;
        }
        catch (Exception e)
        {
            _logger.LogError($"【TDengIne】 insert Error:{e.Message}");
            throw Oops.Oh($"【TDengIne】 insert Error:{e.Message}");
        }
    }

    // /// <summary>
    // /// 批量插入
    // /// </summary>
    // /// <param name="objColumns"></param>
    // /// <param name="objValues"></param>
    // /// <returns></returns>
    // public dynamic BatchInsert(object objColumns,object objValues)
    // {
    //     
    // }

    /// <summary>
    ///     执行SQL语句
    /// </summary>
    /// <param name="sql"></param>
    public bool ExecuteCommand(string sql)
    {
        try
        {
            var res = TDengine.Query(_connectService.Connection(), sql);
            _connectService.CheckRes(res, $"【TDengine】 出错SQL:【{sql}】");
            TDengine.FreeResult(res);
            return true;
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            throw Oops.Oh(e.Message);
        }
    }

    /// <summary>
    ///     执行读取SQL语句,返回一条数据
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public Dictionary<string, object> First(string query)
    {
        try
        {
            var output = new Dictionary<string, object>();

            if (!query.ToUpper().StartsWith("SELECT"))
                throw Oops.Oh("仅允许查询数据,SQL必须是SELECT开始！");
            //从指定库中查询数据
            var conn = _connectService.Connection();
            var res = TDengine.Query(conn, query);
            if (TDengine.ErrorNo(res) != 0)
            {
                TDengine.FreeResult(res);
                TDengine.Close(conn);
                return output;
            }

            var metas = LibTaos.GetMeta(res);
            var resData = LibTaos.GetData(res);
            TDengine.FreeResult(res);
            for (var i = 0; i < resData.Count;)
            {
                for (var j = 0; j < metas.Count; j++)
                    output.Add(metas[j].name, resData[i + j]);
                TDengine.Close(conn);
                return output;
            }

            TDengine.Close(conn);
            return output;
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     执行读取SQL语句
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public dynamic Columns(string query)
    {
        try
        {
            dynamic obj = new ExpandoObject();
            query = query.TrimStart();
            if (!query.ToUpper().StartsWith("SELECT"))
                throw Oops.Oh("仅允许查询数据,SQL必须是SELECT开始！");
            //从指定库中查询数据
            var conn = _connectService.Connection();
            var res = TDengine.Query(conn, query);
            if (TDengine.ErrorNo(res) != 0)
            {
                TDengine.FreeResult(res);
                TDengine.Close(conn);

                obj.Columns = Array.Empty<string>();
                obj.Rows = Array.Empty<string>();
                return obj;
            }

            var metas = LibTaos.GetMeta(res);
            var resData = LibTaos.GetData(res);
            TDengine.FreeResult(res);

            var resultData = new List<List<dynamic>>();
            for (var i = 0; i < resData.Count; i += metas.Count)
            {
                var rows = new List<dynamic>();
                for (var j = 0; j < metas.Count; j++)
                    if (resData[i + j].GetJsonElementValue().GetType().ToString() == "SystemServices.String")
                    {
                        rows.Add(resData[i + j].ToString() == "NULL" ? null : resData[i + j]); // 10 代表nchar
                    }
                    else if (metas[j].name == "ts" && metas[j].type == 9) // 9 TimeStamp
                    {
                        var timestamp = Convert.ToInt64(resData[i + j]);
                        rows.Add(DateTime.ToShanghai(timestamp, 2));
                    }
                    else
                    {
                        rows.Add(resData[i + j] == null ? null : resData[i + j]);
                    }

                resultData.Add(rows);
            }

            TDengine.Close(conn);
            obj.Columns = metas.Select(s => s.name).ToArray();
            obj.Rows = resultData;
            return obj;
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     执行读取SQL语句
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public List<Dictionary<string, object?>> Select(string query)
    {
        try
        {
            query = query.TrimStart();
            if (!query.ToUpper().StartsWith("SELECT"))
                throw Oops.Oh("仅允许查询数据,SQL必须是SELECT开始！");
            var resultData = new List<Dictionary<string, object?>>();
            //从指定库中查询数据
            var conn = _connectService.Connection();
            var res = TDengine.Query(conn, query);
            if (TDengine.ErrorNo(res) != 0)
            {
                TDengine.FreeResult(res);
                TDengine.Close(conn);
                return resultData;
            }

            var metas = LibTaos.GetMeta(res);
            var resData = LibTaos.GetData(res);
            TDengine.FreeResult(res);
            for (var i = 0; i < resData.Count; i += metas.Count)
            {
                var dicValue = new Dictionary<string, object?>(StringComparer.OrdinalIgnoreCase);
                for (var j = 0; j < metas.Count; j++)
                    dicValue.Add(metas[j].name, resData[i + j] == null ? null : resData[i + j]);
                resultData.Add(dicValue);
            }

            TDengine.Close(conn);
            return resultData;
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     返回设备最近一条数据
    /// </summary>
    /// <param name="tableNameListObj">表名称集合</param>
    /// <param name="properListObj">属性集合</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public dynamic Last(object tableNameListObj, object? properListObj = null)
    {
        var tableNameList = ObjConvertToList(tableNameListObj);
        var properList = ObjConvertToListByNull(properListObj);
        var output = new Dictionary<string, Dictionary<string, object>>();
        foreach (var tableName in tableNameList)
        {
            var sql = "SELECT last_row(";
            if (properList == null)
            {
                sql += $"*) FROM {tableName};";
            }
            else
            {
                sql += "ts";

                sql = properList.Aggregate(sql, (current, propertie) => current + $",{propertie} ");
                sql += $") FROM {tableName};";
            }

            var data = First(sql);
            var newData = new Dictionary<string, object>();
            foreach (var (key, val) in data)
                newData.Add(key.Contains("last_row") ? key.Replace("last_row(", "").Replace(")", "") : key, val);
            output.Add(tableName, newData);
        }

        return output;
    }

    /// <summary>
    ///     返回设备最近一条数据
    /// </summary>
    /// <param name="tableName"></param>
    /// <param name="properListObj"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public dynamic Last(string tableName, object? properListObj = null)
    {
        var properList = ObjConvertToListByNull(properListObj);
        var output = new Dictionary<string, Dictionary<string, object>>();
        var sql = "SELECT last_row(";
        if (properList == null)
        {
            sql += $"*) FROM {tableName} ;";
        }
        else
        {
            sql += "ts";
            sql = properList.Aggregate(sql, (current, propertie) => current + $",{propertie} ");
            sql += $") FROM {tableName} ;";
        }

        var data = First(sql);
        var newData = new Dictionary<string, object>();
        foreach (var (key, val) in data)
            newData.Add(key.Contains("last_row") ? key.Replace("last_row(", "").Replace(")", "") : key, val);
        output.Add(tableName, newData);
        return output;
    }

    /// <summary>
    ///     查询一组设备的历史数据
    /// </summary>
    /// <param name="tableNameListObj">表名称</param>
    /// <param name="startTime">开始时间戳</param>
    /// <param name="endTime">结束时间戳</param>
    /// <param name="limit">分页数据</param>
    /// <param name="properListObj">返回字段，不填默认返回全部</param>
    /// <returns></returns>
    public dynamic Historical(object tableNameListObj, long startTime, long endTime, int limit = 20, object? properListObj = null)
    {
        var tableNameList = ObjConvertToList(tableNameListObj);
        var properList = ObjConvertToListByNull(properListObj);
        var output = new Dictionary<string, List<Dictionary<string, object>>>();
        foreach (var tableName in tableNameList)
        {
            var sql = "SELECT ";
            if (properList == null)
            {
                sql +=
                    $"* FROM {tableName} WHERE ts>={startTime} and ts<={endTime}  limit {limit};";
            }
            else
            {
                sql += "ts";
                sql = properList.Aggregate(sql, (current, propertie) => current + $",{propertie}");
                sql +=
                    $" FROM {tableName} WHERE ts>={startTime} and ts<={endTime} limit {limit};";
            }

            output.Add(tableName, Select(sql));
        }

        return output;
    }

    /// <summary>
    ///     查询指定设备的历史数据
    /// </summary>
    /// <param name="tableName">表名称</param>
    /// <param name="startTime">开始时间戳</param>
    /// <param name="endTime">结束时间戳</param>
    /// <param name="limit">分页数据</param>
    /// <param name="properListObj">返回字段，不填默认返回全部</param>
    /// <returns></returns>
    public dynamic Historical(string tableName, long startTime, long endTime, int limit = 20, object? properListObj = null)
    {
        var properList = ObjConvertToListByNull(properListObj);
        var sql = "SELECT ";
        if (properList == null)
        {
            sql +=
                $"* FROM {tableName} WHERE ts>={startTime} and ts<={endTime}  limit {limit};";
        }
        else
        {
            sql += "ts";
            sql = properList.Aggregate(sql, (current, propertie) => current + $",{propertie}");
            sql +=
                $" FROM {tableName} WHERE ts>={startTime} and ts<={endTime}  limit {limit};";
        }

        var output = Select(sql);
        return output;
    }

    /// <summary>
    ///     查询设备的时间窗口数据
    /// </summary>
    /// <param name="tableNameListObj">表名称</param>
    /// <param name="properListObj">字段名称</param>
    /// <param name="startTime">开始时间戳</param>
    /// <param name="endTime">结束时间戳</param>
    /// <returns></returns>
    public dynamic Window(object tableNameListObj, object properListObj, long startTime, long endTime)
    {
        var tableNameList = ObjConvertToList(tableNameListObj);
        var properList = ObjConvertToList(properListObj);
        var output = new Dictionary<string, object>();
        foreach (var tableName in tableNameList)
        {
            var propertiesDic = new Dictionary<string, List<Dictionary<string, object>>>();
            foreach (var properties in properList)
            {
                var sql =
                    $"SELECT * FROM (SELECT _wstart as fst,_WEND as ent,_WDURATION as totalTime, COUNT(*) AS cnt,  {properties} FROM {tableName} where ts >= {startTime} and ts <= {endTime}  STATE_WINDOW({properties})) t ; ";
                propertiesDic.Add(properties, Select(sql));
            }

            output.Add(tableName, propertiesDic);
        }

        return output;
    }

    /// <summary>
    ///     查询指定设备的时间聚集数据
    /// </summary>
    /// <param name="tableNameListObj">表名称</param>
    /// <param name="properListObj">字段名称</param>
    /// <param name="aggFunc">聚合函数</param>
    /// <param name="interval">采样间隔</param>
    /// <param name="startTime">开始时间戳</param>
    /// <param name="endTime">结束时间戳</param>
    /// <param name="limit">分页数据</param>
    /// <returns></returns>
    public dynamic Aggregated(object tableNameListObj, object properListObj, string aggFunc, string interval, long startTime, long endTime, int limit)
    {
        var tableNameList = ObjConvertToList(tableNameListObj);
        var properList = ObjConvertToList(properListObj);
        var output = new Dictionary<string, List<Dictionary<string, object>>>();
        foreach (var tableName in tableNameList)
        foreach (var properties in properList)
        {
            var sql =
                $"SELECT _wstart as time,{aggFunc}({properties}) FROM {tableName} WHERE ts>={startTime} and ts<={endTime}  ";
            sql += $"INTERVAL({interval})";
            sql += $" limit {limit};";

            var dataList = Select(sql);
            foreach (var data in dataList)
            {
                var newData = new Dictionary<string, object>();
                foreach (var (key, val) in data)
                    newData.Add(
                        key.Contains($"{aggFunc.ToLower()}")
                            ? key.Replace($"{aggFunc.ToLower()}(", "").Replace(")", "")
                            : key, val);

                if (!output.ContainsKey(tableName))
                {
                    output.TryAdd(tableName, new List<Dictionary<string, object>> {newData});
                }
                else
                {
                    var thingDic = output[tableName];
                    thingDic.Add(newData);
                    output[tableName] = thingDic;
                }
            }
        }

        return output;
    }

    /// <summary>
    ///     object转List/<string />
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    private List<string> ObjConvertToList(object obj)
    {
        return JSON.Deserialize<List<string>>(JSON.Serialize(obj));
    }

    /// <summary>
    ///     object转List/<string />
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    private List<string>? ObjConvertToListByNull(object? obj)
    {
        if (obj == null)
            return null;
        return JSON.Deserialize<List<string>>(JSON.Serialize(obj));
    }
}