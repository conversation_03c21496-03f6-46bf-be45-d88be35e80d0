using DateTime = Common.Extension.DateTime;

namespace IotGateway.TDengIne.Dto;

/// <summary>
/// </summary>
public class ReadDataCountInput
{
    /// <summary>
    ///     搜索开始时间
    /// </summary>
    [Required]
    public long StartTime { get; set; } = DateTime.ToLong(DateTime.Now().AddMinutes(-5));

    /// <summary>
    ///     搜索结束时间
    /// </summary>
    [Required]
    public long EndTime { get; set; } = DateTime.ToLong(DateTime.Now());

    /// <summary>
    ///     属性列表
    /// </summary>
    [Required]
    public List<string> Properties { get; set; } = new();

    /// <summary>
    ///     设备名称
    /// </summary>
    [Required(ErrorMessage = "设备名称不能是空！")]
    public string DeviceName { get; set; }
}