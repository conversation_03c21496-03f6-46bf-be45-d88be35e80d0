namespace IotGateway.TDengIne.Dto;

/// <summary>
///     查询指定设备的时间聚集数据
/// </summary>
public class GetAggregatedDataOfInstanceInput
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [Description("设备名称")]
    [Required]
    public string DeviceName { get; set; }

    /// <summary>
    ///     属性列表
    /// </summary>
    [Description("属性列表")]
    [Required]
    public List<string> Properties { get; set; } = new();

    /// <summary>
    ///     开始时间(毫秒时间戳)
    /// </summary>
    [Description("开始时间(毫秒时间戳)")]
    [Required]
    public long StartTime { get; set; }

    /// <summary>
    ///     结束时间(毫秒时间戳)
    /// </summary>
    [Description("结束时间(毫秒时间戳)")]
    [Required]
    public long EndTime { get; set; }

    /// <summary>
    ///     支持函数 FIRST LAST COUNT MODE MAX MIN AVG SUM STDDEV SPREAD  字段举例: avg, 表示平均数; 属性为String、Json、Binary类型仅支持FIRST、LAST和COUNT聚集函数
    /// </summary>
    [Description("支持函数 FIRST LAST COUNT MODE MAX MIN AVG SUM STDDEV SPREAD  字段举例: avg, 表示平均数; 属性为String、Json、Binary类型仅支持FIRST、LAST和COUNT聚集函数")]
    [Required]
    public string AggFunc { get; set; }

    /// <summary>
    ///     聚集的时间间隔
    /// </summary>
    [Description("聚集的时间间隔")]
    public string TimeInterval { get; set; }

    /// <summary>
    ///     查询返回的数据最大条数
    /// </summary>
    [Description("查询返回的数据最大条数(默认是1000，默认配置下limit不能超过100000)")]
    public int Limit { get; set; } = 1000;
}