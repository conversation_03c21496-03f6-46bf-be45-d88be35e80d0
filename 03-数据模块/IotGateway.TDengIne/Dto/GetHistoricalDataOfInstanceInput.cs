namespace TDengIne.Dto;

/// <summary>
///     设备历史数据查询
/// </summary>
public class GetHistoricalDataOfInstanceInput
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [Required]
    public string DeviceName { get; set; }

    /// <summary>
    ///     属性列表
    /// </summary>
    [Required]
    public List<string> Properties { get; set; } = new();

    /// <summary>
    ///     开始时间(毫秒时间戳)
    /// </summary>
    [Required]
    public long StartTime { get; set; }

    /// <summary>
    ///     结束时间(毫秒时间戳)
    /// </summary>
    [Required]
    public long EndTime { get; set; }

    /// <summary>
    ///     查询返回的数据页码
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    ///     查询返回的数据最大条数
    /// </summary>
    public int PageNo { get; set; } = 1;

    /// <summary>
    ///     默认倒序
    /// </summary>
    public string Sort { get; set; } = "DESC";
}

/// <summary>
///     设备历史数据导出
/// </summary>
public class HistorianExportInput
{
    /// <summary>
    ///     设备Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     属性列表
    /// </summary>
    [Required]
    public List<string> Properties { get; set; } = new();

    /// <summary>
    ///     开始时间(毫秒时间戳)
    /// </summary>
    [Required]
    public long StartTime { get; set; }

    /// <summary>
    ///     结束时间(毫秒时间戳)
    /// </summary>
    [Required]
    public long EndTime { get; set; }

    /// <summary>
    ///     默认倒序
    /// </summary>
    public string Sort { get; set; } = "DESC";
}

/// <summary>
/// 获取单个属性最后x条数据
/// </summary>
public class GetHistoricalDataByLimitInput
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [Required]
    public string DeviceName { get; set; }

    /// <summary>
    ///     属性标识
    /// </summary>
    [Required]
    public string Property { get; set; }

    /// <summary>
    /// 返回条数，默认10
    /// </summary>
    public int Number { get; set; } = 10;
}