namespace IotGateway.TDengIne.Dto;

/// <summary>
///     查询设备的时间窗口数据
/// </summary>
public class GetTimeWindowDataOfModelInstancesInput
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [Description("设备名称")]
    [Required]
    public string DeviceName { get; set; }

    /// <summary>
    ///     属性列表
    /// </summary>
    [Description("属性列表")]
    public List<string> Properties { get; set; } = new();

    /// <summary>
    ///     开始时间(毫秒时间戳)
    /// </summary>
    [Description("开始时间(毫秒时间戳)")]
    [Required]
    public long StartTime { get; set; }

    /// <summary>
    ///     结束时间(毫秒时间戳)
    /// </summary>
    [Description("结束时间(毫秒时间戳)")]
    [Required]
    public long EndTime { get; set; }
}