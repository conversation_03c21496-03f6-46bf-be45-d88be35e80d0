namespace IotGateway.TDengIne.Dto;

/// <summary>
///     TDengIne 读取数据请求参数
/// </summary>
public class ReadDataInput
{
    /// <summary>
    ///     页码容量
    /// </summary>
    public int Limit { get; set; } = 100000;

    /// <summary>
    ///     开始时间
    /// </summary>
    [Required]
    public long StartTime { get; set; }

    /// <summary>
    ///     结束时间
    /// </summary>
    [Required]
    public long EndTime { get; set; }

    /// <summary>
    ///     设备-属性
    /// </summary>
    [Required]
    public string Properties { get; set; }

    /// <summary>
    ///     设备名称
    /// </summary>
    [Required(ErrorMessage = "设备名称不能是空！")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     数据间隔,不填默认1s
    /// </summary>
    public string TimeInterval { get; set; }

    /// <summary>
    /// 聚合函数:最旧值(FIRST),最新值(LAST),均值(AVG),最大值(MAX),最小值(MIN),众数(),中位数(APERCENTILE)
    /// </summary>
    [Required]
    public string AggFunc { get; set; }

    /// <summary>
    /// 数据填充规则,不补值(NONE),补前值(PREV),线性补值(LINEAR)，补后值(NEXT)
    /// </summary>
    [Required]
    public string Fill { get; set; }

    /// <summary>
    /// where条件,例如 status>1 and status<3
    /// </summary>
    public string Where { get; set; }
}