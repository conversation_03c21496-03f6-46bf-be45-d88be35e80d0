namespace IotGateway.TDengIne.Dto;

/// <summary>
///     查询指定设备的实时数据
/// </summary>
public class GetRealtimeDataOfInstanceInput
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [Description("设备名称")]
    [Required]
    public string DeviceName { get; set; }

    /// <summary>
    ///     属性列表
    /// </summary>
    [Description("属性列表")]
    public List<string> Properties { get; set; } = new();
}