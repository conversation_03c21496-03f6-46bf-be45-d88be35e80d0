using IotGateway.TDengIne.Dto;
using TDengIne.Dto;
using TDengineDriver;
using TDengineDriver.Impl;
using DateTime = Common.Extension.DateTime;

namespace TDengIne;

/// <summary>
///     读取数据
/// </summary>
public class ReadService : ISingleton
{
    /// <summary>
    ///     TDengIne的连接
    /// </summary>
    private readonly ConnectService _connectService;

    /// <summary>
    ///     TDengIne 封装的方法
    /// </summary>
    private readonly ExecuteService _executeService;

    public ReadService(ConnectService connectService, ExecuteService executeService)
    {
        _connectService = connectService;
        _executeService = executeService;
    }

    /// <summary>
    ///     趋势图数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public ReadDataListOutput ReadDataList(ReadDataInput input)
    {
        var result = new ReadDataListOutput
        {
            Columns = new List<string>(),
            Rows = new List<List<object>>()
        };
        var interval = !string.IsNullOrEmpty(input.TimeInterval) ? input.TimeInterval : "1s";
        input.AggFunc = input.AggFunc == "APERCENTILE" ? $"{input.AggFunc}(`{input.Properties}`, 50) as {input.Properties}" : $"{input.AggFunc}(`{input.Properties}`) as {input.Properties}";

        var sql = $"SELECT _wstart,{input.AggFunc}";
        sql +=
            $" FROM {input.DeviceName} where ts >= {input.StartTime} and ts <= {input.EndTime} interval({interval}) fill({input.Fill}) limit {input.Limit} ;"; // //从指定库中查询数据
        var conn = _connectService.Connection();
        Console.WriteLine($"【趋势图】：{sql}");
        var res = TDengine.Query(conn, sql);
        if (TDengine.ErrorNo(res) != 0)
        {
            TDengine.FreeResult(res);
            TDengine.Close(conn);
            return result;
        }

        var metas = LibTaos.GetMeta(res);
        var resData = LibTaos.GetData(res);

        foreach (var t in metas) result.Columns.Add(t.name);

        for (var i = 0; i < resData.Count; i += metas.Count)
        {
            var data = metas.Select((t, j) => t.name == "_wstart" ? DateTime.ToShanghai(Convert.ToInt64(resData[i + j]), 2) : resData[i + j]).ToList();
            result.Rows.Add(data);
        }

        TDengine.FreeResult(res);
        TDengine.Close(conn);
        return result;
    }

    /// <summary>
    ///     查询时间段数据总数
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public int ReadDataCount(ReadDataCountInput input)
    {
        if (!input.Properties.Any()) throw Oops.Oh("Properties Is Null");
        var count = new List<int>();
        var sql = "SELECT ";
        foreach (var propertie in input.Properties) sql += "count(`" + propertie + "`),";
        sql = sql.TrimEnd(',');
        sql +=
            $" FROM {input.DeviceName} where ts >= {input.StartTime} and ts <= {input.EndTime};"; // //从指定库中查询数据
        var conn = _connectService.Connection();
        var res = TDengine.Query(conn, sql);
        if (TDengine.ErrorNo(res) != 0)
        {
            TDengine.FreeResult(res);
            TDengine.Close(conn);
            return 0;
        }

        var metas = LibTaos.GetMeta(res);
        var resData = LibTaos.GetData(res);

        for (var i = 0; i < resData.Count; i += metas.Count)
        for (var j = 0; j < metas.Count; j++)
            count.Add(Convert.ToInt32(resData[i + j]));

        TDengine.FreeResult(res);
        TDengine.Close(conn);
        return count.Max();
    }

    /// <summary>
    ///     设备的历史数据导出
    /// </summary>
    /// <param name="input"></param>
    /// <param name="deviceName">设备唯一标识</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public Task<dynamic> HistorianExport(HistorianExportInput input, string deviceName)
    {
        var sql = "SELECT ";
        if (!input.Properties.Any())
            throw Oops.Oh("Properties Is Null");
        sql += "ts";
        sql = input.Properties.Aggregate(sql, (current, propertie) => current + $",`{propertie}`");
        sql +=
            $" FROM {deviceName} WHERE ts>={input.StartTime} and ts<={input.EndTime} order by ts {input.Sort};";

        return Task.FromResult<dynamic>(_executeService.Columns(sql));
    }

    /// <summary>
    ///     查询指定设备的实时数据
    /// </summary>
    /// <returns></returns>
    public Task<dynamic> GetRealtimeDataOfInstance(GetRealtimeDataOfInstanceInput input)
    {
        var sql = "SELECT last_row(";
        if (!input.Properties.Any())
        {
            sql += $"*) FROM {input.DeviceName} ;";
        }
        else
        {
            sql += "ts";
            sql = input.Properties.Aggregate(sql, (current, propertie) => current + $",`{propertie}`");
            sql += $") FROM {input.DeviceName} ;";
        }

        var data = _executeService.First(sql);
        var newData = new Dictionary<string, object>();
        foreach (var (key, val) in data)
            newData.Add(key.Contains("last_row") ? key.Replace("last_row(", "").Replace(")", "") : key, val);

        return Task.FromResult<dynamic>(newData);
    }


    /// <summary>
    ///     查询设备的时间窗口数据
    /// </summary>
    /// <returns></returns>
    public Task<dynamic> GetTimeWindowDataOfModelInstances(GetTimeWindowDataOfModelInstancesInput input)
    {
        var propertiesDic = new Dictionary<string, List<Dictionary<string, object>>>();
        foreach (var properties in input.Properties)
        {
            var sql =
                $"SELECT * FROM (SELECT _wstart as fst,_WEND as ent,_WDURATION as totalTime, COUNT(*) AS cnt,  `{properties}` FROM {input.DeviceName} where ts >= {input.StartTime} and ts <= {input.EndTime}  STATE_WINDOW(`{properties}`)) t ; ";
            propertiesDic.Add(properties, _executeService.Select(sql));
        }

        return Task.FromResult<dynamic>(propertiesDic);
    }

    /// <summary>
    ///     查询指定设备的时间聚集数据
    /// </summary>
    /// <returns></returns>
    public Task<dynamic> GetAggregatedDataOfInstance(GetAggregatedDataOfInstanceInput input)
    {
        var output = new List<Dictionary<string, object>>();

        foreach (var propertie in input.Properties)
        {
            var sql =
                $"SELECT _wstart as time,{input.AggFunc}(`{propertie}`) FROM {input.DeviceName} WHERE ts>={input.StartTime} and ts<={input.EndTime}";
            if (!string.IsNullOrEmpty(input.TimeInterval))
                sql += $"INTERVAL({input.TimeInterval})";
            sql += $" limit {input.Limit};";

            var dataList = _executeService.Select(sql);
            foreach (var data in dataList)
            {
                var rowData = new Dictionary<string, object>();
                foreach (var (key, val) in data)
                    rowData.Add(
                        key.Contains($"{input.AggFunc.ToLower()}")
                            ? key.Replace($"{input.AggFunc.ToLower()}(", "").Replace(")", "")
                            : key, val);

                output.Add(rowData);
            }
        }

        return Task.FromResult<dynamic>(output);
    }
    /// <summary>
    ///     查询设备的历史数据
    /// </summary>
    /// <returns></returns>
    public Task<dynamic> GetHistoricalDataOfInstance(GetHistoricalDataOfInstanceInput ofInstanceInput)
    {
        var sql = "SELECT ";
        if (!ofInstanceInput.Properties.Any())
            throw Oops.Oh("Properties Is Null");
        sql += "ts";
        sql = ofInstanceInput.Properties.Aggregate(sql, (current, propertie) => current + $",`{propertie}`");
        sql +=
            $" FROM {ofInstanceInput.DeviceName} WHERE ts>={ofInstanceInput.StartTime} and ts<={ofInstanceInput.EndTime} order by ts {ofInstanceInput.Sort} limit {ofInstanceInput.PageSize} offset {(ofInstanceInput.PageNo - 1) * ofInstanceInput.PageSize};";

        return Task.FromResult<dynamic>(_executeService.Columns(sql));
    }
    
    /// <summary>
    ///     查询设备的历史数据
    /// </summary>
    /// <returns></returns>
    public Task<dynamic> GetHistoricalDataByLimit(GetHistoricalDataByLimitInput ofInstanceInput)
    {
        var sql = "SELECT ";
        sql += $"ts,`{ofInstanceInput.Property}`";
        sql +=
            $" FROM {ofInstanceInput.DeviceName} order by ts DESC limit {ofInstanceInput.Number};";
        return Task.FromResult<dynamic>(_executeService.Columns(sql));
    }
    
    
}