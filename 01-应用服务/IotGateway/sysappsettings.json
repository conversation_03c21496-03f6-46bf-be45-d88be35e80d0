{"$schema": "https://gitee.com/dotnetchina/Furion/raw/net6/schemas/v3/furion-schema.json", "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System.Net.Http.HttpClient": "Warning", "System.Logging.ScheduleService": "Error"}, "File": {"FileName": "logs/info.log", "Append": true, "MinimumLevel": "Information", "FileSizeLimitBytes": 5120, "MaxRollingFiles": 5}, "Database": {"MinimumLevel": "Information"}, "Monitor": {"GlobalEnabled": true, "IncludeOfMethods": [], "ExcludeOfMethods": []}}, "SpecificationDocumentSettings": {"DocExpansionState": "None", "DocumentTitle": "Gateway | 标准化接口", "GroupOpenApiInfos": [{"Group": "Gateway", "Title": "Gateway 规范化接口", "Description": "Gateway", "Version": "3.0.0"}], "LoginInfo": {"Enabled": true, "CheckUrl": "/Swagger/CheckUrl", "SubmitUrl": "/Swagger/SubmitUrl", "Meta": false}}, "RefreshToken": {"ExpiredTime": 43200}, "JWTSettings": {"ValidateIssuerSigningKey": true, "IssuerSigningKey": "0a3852e18a1e9b06a9c4f2d3b5f9d7e8b2e0c8d4f6a2d3b5f9d7e8b2e0c8d4f", "ValidateIssuer": true, "ValidIssuer": "HangZhou.FengHui", "ValidateAudience": true, "ValidAudience": "HangZhou.FengHui", "ValidateLifetime": true, "ExpiredTime": 500, "ClockSkew": 5, "Algorithm": "HS256"}, "AppSettings": {"ExternalAssemblies": ["plugins/equipment/IotGateway.Px30.dll", "plugins/equipment/IotGateway.Wr610.dll", "plugins/equipment/IotGateway.Ubuntu.dll", "plugins/equipment/IotGateway.Debian.dll", "plugins/equipment/IotGateway.FengEdge2000.dll", "plugins/equipment/IotGateway.Gt675x.dll", "plugins/equipment/IotGateway.Hw356x.dll", "plugins/equipment/IotGateway.FengEdge200.dll", "plugins/equipment/IotGateway.FengEdge150.dll"], "InjectSpecificationDocument": true}}