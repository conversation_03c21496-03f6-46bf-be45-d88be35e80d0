# Feng-Edge 更新日志

## V3.6.0

- 🌈 重构 `网关配置放到数据库中允许前台修改` `网关版本:v3.6.0` ⏱️`2024-08-08`
-
- 🌟 新增 `Flink连接管理` `网关版本:v3.6.0` ⏱️`2024-08-01`
- 🌟 新增 `协议(McFxLinks)增加[和校验]字段开关` `网关版本:v3.6.0` ⏱️`2024-08-01`
-
- 🌟 调整 `数据库初始化增加标记字段，忽略更新某些数据` `网关版本:v3.6.0` ⏱️`2024-08-01`
- 🌟 调整 `底层依赖库升级` `网关版本:v3.6.0` ⏱️`2024-08-01`
-
- 🐞 修复 `设备属性事件新增数据格式错误问题`， `网关版本:v3.6.0` ⏱️`2024-08-01`

## V3.6.1

- 🐞 修复 `flink无法连接时，网关启动时间过长`， `网关版本:v3.6.1` ⏱️`2024-08-12`
-
- 🌟 调整 `优化欧姆龙协议批量读取，测试不够完善，还需要更多实际地址进行测试`， `网关版本:v3.6.1` ⏱️`2024-08-12`
- 🌟 调整 `升级底层依赖库，移除.net5的支持`， `网关版本:v3.6.1` ⏱️`2024-08-12`

- 🌟 新增 `全局变量-是否允许属性写入，限制写入，脚本中增加单独的写入方法` `网关版本:v3.6.1` ⏱️`2024-08-13`

## V3.6.2

- 🐞 修复 `转发离线数据存储时未将数据解密`， `网关版本:v3.6.2` ⏱️`2024-08-13`
-
- 🌟 新增 `脚本中增加ftp功能` `网关版本:v3.6.2` ⏱️`2024-08-16`

## V3.6.3

- 🐞 修复 `尝试修复数据库被锁问题`， `网关版本:v3.6.3` ⏱️`2024-08-21`
- 🐞 修复 `修复小网关Dns写入问题，增加定时写入解决`， `网关版本:v3.6.3` ⏱️`2024-08-21`

## V3.6.4

- 🐞 修复 `尝试修复数据库被锁问题`， `网关版本:v3.6.4` ⏱️`2024-08-23`
- 
- 🌟 调整 `属性列表Seach字段支持更多类型模糊查询`， ⏱️`2024-08-26`

## V3.6.5

- 🐞 修复 `数据列表404问题`，  ⏱️`2024-08-29`
- 

## V3.6.6

- 🐞 修复 `FLink下发转发没有topic问题`，  ⏱️`2024-09-05`
- 
- 🌟 调整 `FLink上报不使用实时上报方式，改为被动上报`， ⏱️`2024-08-26`

## V3.6.7

- 🌟 新增 `增加注塑机KeQiang_T6F3协议`  ⏱️`2024-09-27`

## V3.6.8

- 🌟 调整 `调整FengEdge2000调试ifconfig页面不显示mac地址问题`， ⏱️`2024-10-11`
- 
- 🐞 修复 `修复系统设置中错误的种子数据问题`  ⏱️`2024-09-28`

## V3.6.9

- 🐞 修复 `修复事件触发表达式执行一直成功问题`  ⏱️`2024-09-28`

## V3.6.91

- 🌟 新增 `脚本中增加ftp上传文件方法`  ⏱️`2024-11-06`

## V3.6.92

- 🌟 调整 `移除Excel协议`  ⏱️`2024-11-12`

## V3.6.93

- 🌟 调整 `欧姆龙相关协议连接增加其他配置属性`  ⏱️`2024-11-14`

## V3.6.94

- 🌟 调整 `opcda协议调整集成`  ⏱️`2024-11-27`
- 
- 🌟 新增 `增加opcda工具扫描地址导出简单模板导excel中`  ⏱️`2024-11-27`