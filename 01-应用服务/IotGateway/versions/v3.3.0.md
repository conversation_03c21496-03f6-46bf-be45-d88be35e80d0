# Feng-Edge 更新日志</a>

## V3.3.0

`2023.12.07`

⚡⚡⚡ 此版本为破环性更新，优化内容如下：（谨慎更新！谨慎更新！！谨慎更新！！！）。

- 🌟 更新 依赖更新最新版本库
- 🌟 更新 转发ELink支持系统属性上报
- 🌟 更新 调整项目结构,底层数据存储结构做了调整，方便支持事件触发
- 🌟 更新 移除ssh相关代码和组件
- 🌟 更新 马扎克smart协议更改个别点位地址
- 🌟 更新 上报supOs平台支持脚本解析实时数据-元数据上报功能
- 🌟 更新 采集上报静态属性关机也能上报
- 🌟 更新 modbusServer地址映射数据类型修正
-
- 🌟 新增 设备增加事件模式
- 🌟 新增 授权检查任务，用于检测授权是否到期，到期停止采集，12小时执行一次
- 🌟 新增 FengEdge2000网关支持Ntp配置，手动设置时间
- 🌟 新增 设备分组
- 🌟 新增 FengEdge2000网关支持wifi扫描
- 🌟 新增 支持平台远程控制设备重启，网关重启
- 🌟 新增 转发SQL优化，增加可视化解析生成SQL
- 🌟 新增 支持Gt675X网关硬件
-
-
- 🎯 优化 优化设备属性列表筛选,设备属性下写调整，支持下写静态变量
- 🎯 优化 缓存列表支持临时缓存信息操作
- 🎯 优化 实时日志打印增加颜色，突出错误信息
- 🎯 优化 移除mqtt中心跳检测功能，并且处理日志打印格式
- 🎯 优化 计算策略导入导出模板
- 🎯 优化 网关首页推送信息结构进行调整
- 🎯 优化 全协议写入string类型时，长度不足补齐算法
-
- 🐞 修复 设备点位为字符串类型时设置长度不生效
- 🐞 修复 设备属性批量新增识别地址问题
- 🐞 修复 脚本share中兼容老版本redis问题修正
- 🐞 修复 定时备份数据库路径出错
- 🐞 修复 设备事件日志过长导致redis大key
- 🐞 修复 地址映射Modbus服务地址数据类型改成转换后的类型
- 🐞 修复 cnc协议默认生成字符串长度为0问题
- 🐞 修复 台达协议批量读取[X,Y]八进制地址区域解析问题

-
- 🌈 重构 边缘计算中增加了属性触发，变量触发，http触发，消息触发..等功能

## V3.3.1

`2024.01.27`

- 🎯 优化 脚本中tcp.send调用说明调整

- 🐞 修复 FengEdge2000修改网络识别ip后/CIDR转换问题
- 🐞 修复 边缘计算任务首次触发不生效

## V3.3.2

`2024.01.29`

- 🌟 新增 脚本中增加udp发送消息功能
-
- 🎯 优化 脚本中tcp和串口发送消息支持追加字符
-
- 🐞 修复 Fanuc协议其他通道点位[程序号]类型问题
- 🐞 修复 设备列表兼容Dnc老版本配置参数
- 🐞 修复 ⚡⚡⚡ 严重Bug,设备启动采集时授权验证失败问题 ⚡⚡⚡

## V3.3.3

`2024.02.19`

- 🎯 优化 导出增加失败日志，方便查询失败原因
- 🎯 优化 升级底层依赖包
- 🎯 优化 `脚本持久化变量方法增加参数，用来控制是否触发任务` `网关版本:v3.3.3.2` ⏱️`2024-02-22`
-
- 🐞 修复 `注塑机[弘讯Ak]协议偶发性解析出错，占用资源过高`问题，`协议版本:v1.2.0` ⏱️`2024-02-22`
-
- 🌟 新增 `新代V4增加主轴负载点位、进给率设定点位、主轴速度设定点位.` `协议版本:v1.3.1` `网关版本:v3.3.3.3` ⏱️`2024-02-26`
- 🌟 调整 `系统状态上报策略忽略ubuntu系统` `网关版本:v3.3.3.4` ⏱️`2024-03-05`
-
- 🐞 修复 `Modbus映射调整地址`问题， `网关版本:v3.3.3.4`⏱️`2024-02-22`
- 🐞 修复 `信捷(XinJE) XinJEInternalNet协议,修复没有读取方法，重构不继承通用信捷base方法`， `协议版本:v1.2.0` ⏱️`2024-03-01`
- 🐞 修复 `px30修改网络wifi设置false修改出错`， `网关版本:v3.3.3.6` ⏱️`2024-03-14`
-
- 🌟 调整 `设备属性导入自定义映射出现空的情况处理` `网关版本:v3.3.3.5` ⏱️`2024-03-05`
- 🌟 调整 `串口协议增加更多的波特率` `网关版本:v3.3.3.5` ⏱️`2024-03-10`



