# Feng-Edge 更新日志

## V3.5.0

`2024.05.14`

- 🌈 重构 `整个网关类库目录结构重新归类` `网关版本:v3.5.0` ⏱️`2024-05-14`
-
- 🌟 新增 `基恩士(Keyence)协议McAsciiNet/McNet` `网关版本:v3.5.0` ⏱️`2024-05-15`
- 🌟 新增 `协议(Driver)表增加厂商字段,用于设备列表显示更加明确` `网关版本:v3.5.0` ⏱️`2024-05-16`
-
- 🌟 调整 `调整某些类的命名空间` `网关版本:v3.5.0` ⏱️`2024-05-15`
- 🌟 调整 `设备事件-属性触发改为设备关机将不在触发` `网关版本:v3.5.0` ⏱️`2024-05-16`
- 🌟 调整 `注塑机协议根据要求强制改为根据命名写入某个固定文件,调整默认为串口，设置默认串口号` `网关版本:v3.5.0` ⏱️`2024-05-21`
-
- 🐞 修复 `设备事件某些场景无法正常触发`， `网关版本:v3.5.0` ⏱️`2024-05-15`
- 🐞 修复 `串口协议在创建时默认串口号设置不生效问题，默认使用了硬件默认配置串口号`， `网关版本:v3.5.0` ⏱️`2024-05-21`
- 🐞 修复 `多转发从节点配置SupOs平台时，导致程序无法正常启动`， `网关版本:v3.5.0` ⏱️`2024-05-23`

## V3.5.1

`2024.05.24`

- 🌟 新增 `脚本方法中增加对文件操作` `网关版本:v3.5.1` ⏱️`2024-05-24`
-
- 🌟 调整 `多转发蓝卓-Slave支持部分SupOs平台的回调请求` `网关版本:v3.5.1` ⏱️`2024-05-24`

## V3.5.2

`2024.05.27`

- 🌟 新增 `基恩士(Keyence) 新增NanoSerial,NanoSerialOverTcp 两种协议` `网关版本:v3.5.2` ⏱️`2024-05-27`
- 🌟 新增 `基恩士(Keyence) 网口协议增加[字符串反转]参数` `网关版本:v3.5.2` ⏱️`2024-05-27`
- 🌟 新增 `硬件Gtx675（lite）版本网关增加sn授权机制` `网关版本:v3.5.2` ⏱️`2024-05-27`
-
- 🌟 调整 `移除蓝卓管理平台远控相关功能` `网关版本:v3.5.2` ⏱️`2024-05-27`
- 🌟 调整 `注塑机协议做了特殊化定制，启动时保存当前` `网关版本:v3.5.2` ⏱️`2024-05-27`
-
- 🐞 修复 `修复设备扩展保存object问题` `网关版本:v3.5.2` ⏱️`2024-05-27`

## V3.5.3

`2024.05.28`

- 🌟 新增 `新增自由协议,串口协议` `网关版本:v3.5.3` ⏱️`2024-05-28`

- 🐞 修复 `西门子Cnc默认初始化plc协议增加大端` `协议版本:v3.4.1` ⏱️`2024-05-28`
- 🐞 修复 `底层方法GetJsonElementValue在极端情况下转number异常` `协议版本:v3.4.1` ⏱️`2024-05-31`

## V3.5.4

`2024.06.06`

- 🌟 调整 `调整转发研华平台默认数据格式,匹配最新格式` `网关版本:v3.5.4` ⏱️`2024-06-06`
- 🌟 调整 `脚本中打印日志格式调整，支持了对象返回` `网关版本:v3.5.4` ⏱️`2024-06-13`
- 🌟 调整 `优化App获取改成Services获取对象` `网关版本:v3.5.4` ⏱️`2024-06-13`
-
- 🐞 修复 `设备属性写入，当字符串中文时候编码格式不生效,和原填充写入冲突` `协议版本:v3.5.4` ⏱️`2024-06-12`

## V3.5.5

`2024.06.21`

- 🐞 修复 `脚本中设备属性写入，字符串被强制解析了` `协议版本:v3.5.5` ⏱️`2024-06-21`
- 🐞 修复 `dnc开放api中无法正常下写` `协议版本:v3.5.5` ⏱️`2024-06-25`
- 🐞 修复 `自由协议:MQTTSERVICE 无法正常停止` `协议版本:v3.5.5` ⏱️`2024-06-25`

- 🌟 调整 `默认模板中文改成英文名称，防止在特定环境下造成乱码` `网关版本:v3.5.5` ⏱️`2024-06-24`
- 🌟 调整 `台达协议支持异步和同步读取` `网关版本:v3.5.5` ⏱️`2024-06-25`
- 🌟 调整 `读取属性增加只写类型` `网关版本:v3.5.5` ⏱️`2024-06-25`
- 🌟 调整 `计算策略-定时任务，增加cron表达示支持，增加并行串行触发约束` `网关版本:v3.5.5` ⏱️`2024-07-09`
-
- 🌟 新增 `PX30,浩聚FengEdge2000 增加动态网卡api` `网关版本:v3.5.5` ⏱️`2024-07-03`
- 🌟 新增 `Hw356x硬件适配，适配完整度有待测试` `网关版本:v3.5.5` ⏱️`2024-07-03`
- 🌟 新增 `modbus映射增加全选，导出功能` `网关版本:v3.5.5` ⏱️`2024-07-10`

## V3.5.6

`2024.07.15`

- 🐞 修复 `修复永宏(Fatek)  ProgramOverTcp协议未初始化方法问题` `网关版本:v3.5.6` ⏱️`2024-07-15`
- 🐞 修复 `修复批量读取时，修改点位地址不会立刻生效问题` `网关版本:v3.5.6` ⏱️`2024-07-16`