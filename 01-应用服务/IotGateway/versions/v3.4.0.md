# Feng-Edge 更新日志

## V3.4.0

`2024.03.13`

- 🌟 新增 `增加关于脚本调用MQTT相关示例方法` `网关版本:v3.4.0` ⏱️`2024-03-14`
-
- 🎯 优化 `调整MQTT模块，支持动态解析脚本topic` `网关版本:v3.4.0` ⏱️`2024-03-13`
-
- 🌟 调整 `移除MQTT单独类库，支持解析更多的脚本参数` `网关版本:v3.4.0` ⏱️`2024-03-14`
- 🌟 调整 `兼容.net6;.net8` `网关版本:v3.4.0` ⏱️`2024-03-14`

## V3.4.1

`2024.04.07`

- 🌟 调整 `调整Fanuc协议 伺服负载(AxisLoad) 数据类型double改为string` `协议版本:v1.3.1` ⏱️`2024-03-26`
- 🌟 调整 `调整Fanuc协议 程序号数据类型错误` `协议版本:v1.3.1` ⏱️`2024-03-26`
- 🌟 调整 `调整西门子Cnc协议，初始化默认创建西门子S7 Plc协议,默认为S300` `协议版本:v1.4.0` ⏱️`2024-04-01`
- 🌟 新增 `伺服负载Z轴,伺服负载X轴,伺服负载Y轴,伺服负载B轴，伺服负载C轴，报警状态，报警信息...点位`
- 🌟 新增 `Siemens-CNC协议中重写西门子PLC协议，自构报文`
- 🌟 新增 `支持WinForm发布,嵌套vue代码，无需配置nginx等，可直接运行`
- 🌟 新增 `支持Web-Windows发布,嵌套vue代码，无需配置nginx等，可直接运行`

- 🐞 修复 `设备属性导入失败`， `网关版本:v3.4.1` ⏱️`2024-04-07`
- 🐞 修复 `脚本中tcp连接多个无法正常连接`， `网关版本:v3.4.1` ⏱️`2024-04-08`

## V3.4.2

`2024.04.29`

- 🐞 修复 `上传更新文件压缩包超过30m无法上传问题`， `网关版本:v3.4.2` ⏱️`2024-04-29`

## V3.4.3

`2024.05.08`

- 🐞 修复 `转发ELink停用启用转发后网关属性不上报问题`， `网关版本:v3.4.3` ⏱️`2024-05-08`
- 🐞 修复 `Windows磁盘使用率显示修复`， `网关版本:v3.4.3` ⏱️`2024-05-08`
-
- 🌟 调整 `广数GskTcp协议，运行时间(CycSec)调整命名，改为(RunTime)` `协议版本:V1.3.1` ⏱️`2024-05-09`

## V3.4.4

`2024.05.13`

- 🌟 调整 `DeltaSerialAsciiOverTcp增加同步读取方法，支持同步/异步读取` `协议版本:V1.1.0` ⏱️`2024-05-13`
- 🌟 调整 `更新底层依赖库` `网关版本:v3.4.4` ⏱️`2024-05-13`
