{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/net6/schemas/v3/furion-schema.json",
  "DbConnection": {
    "EnableConsoleSql": true,
    "ConnectionConfigs": [
      {
        "ConfigId": "业务数据(配置中心)",
        "DbType": "Sqlite",
        "ConnectionString": "Data Source=./IotGateway.db",
        "EnableInitTable": true,
        "EnableInitSeed": true
      },
      {
        "ConfigId": "实时数据",
        "DbType": "Sqlite",
        "ConnectionString": "Data Source=./EdgeData.db",
        "EnableInitTable": true,
        "EnableInitSeed": true
      }
    ]
  },
  "Cache": {
    "CacheType": "Cache",
    //使用redis服务
    "RedisConnectionString": "server=192.168.3.34:6379;password=**********;db=9;"
  },
  "Event": {
    "Type": "Cache"
  }
}