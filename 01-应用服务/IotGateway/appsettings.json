{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/net6/schemas/v3/furion-schema.json",
  "DbConnection": {
    "EnableConsoleSql": false,
    // 启用控制台打印SQL
    "ConnectionConfigs": [
      {
        "ConfigId": "业务数据(配置中心)",
        "DbType": "Sqlite",
        "ConnectionString": "Data Source=/Edge/Data/IotGateway.db",
        "EnableInitTable": true,
        "EnableInitSeed": true
      },
      {
        "ConfigId": "实时数据",
        "DbType": "Sqlite",
        "ConnectionString": "Data Source=/Edge/Data/EdgeData.db",
        "EnableInitTable": true,
        "EnableInitSeed": false
      }
    ]
  },
  "Cache": {
    "CacheType": "RedisCache",
    //使用redis服务
    "RedisConnectionString": "server=127.0.0.1:6379;password=**********;db=6;"
  },
  "Event": {
    "Type": "Cache"
  }
}