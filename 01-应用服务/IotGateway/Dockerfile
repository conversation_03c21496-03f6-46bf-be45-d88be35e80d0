#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /app/iotgateway
RUN sed -i s@/deb.debian.org/@/mirrors.aliyun.com/@g /etc/apt/sources.list && apt-get clean && apt-get update 
RUN apt-get install procps -y
RUN apt install net-tools
RUN apt install -y netcat
RUN apt install -y zip
EXPOSE 80
EXPOSE 443
EXPOSE 8093

COPY . .

ENTRYPOINT ["dotnet", "IotGateway.dll"]