# Feng-Edge 更新问题</a>

## ⚡⚡⚡ SOlite Error 19 NOT NULL constraint failed: DbLink.Description

```sql
🌟🌟🌟 //迁移表
ALTER TABLE DbLink RENAME TO DbLink_old;

🌟🌟🌟 // 创建新表，Description字段允许为空
CREATE TABLE "main"."DbLink" (
                                 "Id" bigint NOT NULL,
                                 "FullName" varchar(255) NOT NULL,
                                 "DbType" integer NOT NULL,
                                 "Host" varchar(255) NOT NULL,
                                 "Port" integer NOT NULL,
                                 "UserName" varchar(255) NOT NULL,
                                 "Password" varchar(255) NOT NULL,
                                 "ServiceName" varchar(255) NOT NULL,
                                 "Description" varchar(255) NULL,
                                 "SortCode" bigint,
                                 "CreatedTime" datetime,
                                 "UpdatedTime" datetime,
                                 "CreatedUserId" bigint,
                                 "UpdatedUserId" bigint,
                                 "CreatedUserName" varchar(255),
                                 "UpdatedUserName" varchar(255),
                                 PRIMARY KEY ("Id")
);
🌟🌟🌟 //将表数据插入新表中
INSERT INTO DbLink (id,FullName,DbType,Host,Port,UserName,Password,ServiceName,Description,SortCode,CreatedTime,UpdatedTime,CreatedUserId,UpdatedUserId,CreatedUserName,UpdatedUserName) select id,FullName,DbType,Host,Port,UserName,Password,ServiceName,Description,SortCode,CreatedTime,UpdatedTime,CreatedUserId,UpdatedUserId,CreatedUserName,UpdatedUserName from DbLink_old;
🌟🌟🌟 // 删除老数据
drop table DbLink_old;
```
