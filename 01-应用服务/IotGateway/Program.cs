using System.Reflection;
using System.Diagnostics;
using Feng.IotGateway.Core.Const;
using Feng.IotGateway.Core.Util;
using Furion.DataEncryption;
using HslCommunication;
using System.Net.NetworkInformation;
using Furion.FriendlyException;

Console.WriteLine("┌──────────────────────────────────────────────────┐");
Console.WriteLine($"│            正在设置系统版本...                    │");

var version = Assembly.GetExecutingAssembly().GetName().Version;
MachineUtil.Version = version?.ToString() ?? "1.0.0";

Console.WriteLine($"│            系统版本设置完成: {MachineUtil.Version}     │");
Console.WriteLine("└──────────────────────────────────────────────────┘");

var sw = Stopwatch.StartNew();

Serve.Run(RunOptions.Default.ConfigureBuilder(builder =>
{
    Authorization.SetAuthorizationCode(DESEncryption.Decrypt(CommonConst.Authorization, "Feng"));
    builder.WebHost.UseUrls("http://*:8093");
    builder.Logging.AddConsole();
    builder.Services.AddLogging(logging =>
    {
        logging.AddConsole();
        logging.AddDebug();
    });
}));

sw.Stop();
Console.WriteLine($"应用程序总启动耗时: {sw.ElapsedMilliseconds}ms");

public partial class Program
{
}