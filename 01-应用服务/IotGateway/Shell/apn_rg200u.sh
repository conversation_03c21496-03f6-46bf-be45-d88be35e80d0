
#!/bin/sh
#
# apn_rg200u.sh dev apn [user] [passwd] [permission]
# example:  
#			sh apn_rg200u.sh /dev/ttyUSB2 ctstnbangpu.gd.iot
#			sh apn_rg200u.sh /dev/ttyUSB2 ctstnbangpu.gd.iot admin admin 0
#

#RG200U 5G
#AT+QCFG="autoapn",0   //取消自动apn
#AT+QICSGP=2,1,"ctstnbangpu.gd.iot"   //设置APN 无认证
#AT+QNETDEVCTL=2,3,1					 //激活PDP拨号
#AT+QNETDEVSTATUS=2					 //查看状态信息

#echo $1 $2 $3 $4 $5

dev=$1
apndest=$2
apnuser=$3
apnpasswd=$4
apnpermission=$5

if [[ -n "$dev" ]] && [[ -n "$apndest" ]];then
    echo "modem device interfaces:$dev,	apn:$apndest"
else
    echo "error."
    exit 0
fi
 
echo "disable the autoapn."
echo -e "AT+QCFG=\"autoapn\",0\r\n" > $dev
sleep 1

if [[ -n "$apnuser" ]] && [[ -n "$apnpasswd" ]] && [[ -n "$apnpermission" ]];then
    echo "set the permission autoapn."
    #echo "get the apnuser param : $apnuser"
    echo -e "AT+QICSGP=2,1,\"$apndest\",\"$apnuser\",\"$apnpasswd\",\"$apnpersmission\"\r\n" > $dev
else
    echo "set the no permission autoapn."
    echo  "apnuser param is necessary"
    echo -e "AT+QICSGP=2,1,\"$apndest\"\r\n" > $dev
fi
sleep 1

echo "enable the pdp about network."
echo -e "AT+QNETDEVCTL=2,3,1\r\n" > $dev
sleep 1

echo "set apn and enable network success."


