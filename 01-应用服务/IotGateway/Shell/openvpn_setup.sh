#!/bin/bash

# 获取外部传入参数
vpnuser="$1"
interface="$2"

# 切换目录
cd /etc/openvpn/easy-rsa

# 生成证书
result=$(./vpnuser add $vpnuser)
if [ "$result" = "false" ]; then
  echo "证书添加失败，请检查用户名和目录权限是否正确。"
  exit 1
fi
# 允许 OpenVPN 数据包通过 TAP 接口
sudo iptables -A FORWARD -i tap0 -j ACCEPT
sudo iptables -A FORWARD -o tap0 -j ACCEPT

# 允许 OpenVPN 数据包通过指定的接口
sudo iptables -A FORWARD -i tap0 -o $interface -j ACCEPT
sudo iptables -A FORWARD -i $interface -o tap0 -m state --state RELATED,ESTABLISHED -j ACCEPT

# 允许 OpenVPN 数据包通过 NAT 接口
sudo iptables -t nat -A POSTROUTING -o $interface -j MASQUERADE
echo "true"