#!/bin/bash

# DNS服务器配置
nameserver1="192.168.11.251"
nameserver2="192.168.11.250"
nameserver3="114.114.114.114"
nameserver4="114.114.114.114"

# 创建一个临时文件
temp_file=$(mktemp)

# 备份原始的resolv.conf文件
cp /etc/resolv.conf /etc/resolv.conf.bak

# 将原始的resolv.conf文件内容复制到临时文件
cp /etc/resolv.conf "$temp_file"

# 判断是否已存在相同的配置行，如果不存在则追加写入临时文件
if ! grep -q "nameserver $nameserver1" "$temp_file"; then
    echo "nameserver $nameserver1" >> "$temp_file"
fi

if ! grep -q "nameserver $nameserver2" "$temp_file"; then
    echo "nameserver $nameserver2" >> "$temp_file"
fi

if ! grep -q "nameserver $nameserver3" "$temp_file"; then
    echo "nameserver $nameserver3" >> "$temp_file"
fi

if ! grep -q "nameserver $nameserver4" "$temp_file"; then
    echo "nameserver $nameserver4" >> "$temp_file"
fi

# 将临时文件内容复制到resolv.conf文件
cp "$temp_file" /etc/resolv.conf

# 删除临时文件
rm "$temp_file"