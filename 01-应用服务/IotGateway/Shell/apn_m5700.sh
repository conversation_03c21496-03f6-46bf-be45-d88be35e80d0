
#!/bin/sh
#
# apn_m5700.sh dev apn [user] [passwd]
# example:  
#			sh apn_m5700.sh /dev/ttyUSB0 CMNET
#			sh apn_m5700.sh /dev/ttyUSB0 ctstnbangpu.gd.iot admin admin
#

#M5700 4G
#AT+CSTT="ctstnbangpu.gd.iot" 	

#echo $1 $2 $3 $4

dev=$1
apndest=$2
apnuser=$3
apnpasswd=$4

if [[ -n "$dev" ]] && [[ -n "$apndest" ]];then
    echo "modem device interfaces:$dev,	apn:$apndest"
else
    echo "error."
    exit 0
fi

if [[ -n "$apnuser" ]] && [[ -n "$apnpasswd" ]];then
    echo "set the permission autoapn."
    #echo "get the apnuser param : $apnuser"
    echo -e "AT+CSTT=\"$apndest\",\"$apnuser\",\"$apnpasswd\"\r\n" > $dev
else
    echo "set the no permission autoapn."
    echo  "apnuser param is necessary"
    echo -e "AT+CSTT=\"$apndest\"\r\n" > $dev
fi
sleep 1

echo "set apn and enable network success."


