<?xml version="1.0" encoding="utf-8"?>
<ApplicationConfiguration
        xmlns:ua="http://opcfoundation.org/UA/2008/02/Types.xsd"
        xmlns="http://opcfoundation.org/UA/SDK/Configuration.xsd"
>
    <ApplicationName>Quickstart Reference Server</ApplicationName>
    <ApplicationUri>urn:localhost:UA:Quickstarts:ReferenceServer</ApplicationUri>
    <ProductUri>uri:opcfoundation.org:Quickstarts:ReferenceServer</ProductUri>
    <ApplicationType>Server_0</ApplicationType>

    <SecurityConfiguration>

        <!-- Where the application instance certificate is stored (MachineDefault) -->
        <ApplicationCertificate>
            <StoreType>Directory</StoreType>
            <StorePath>%LocalApplicationData%/OPC Foundation/pki/own</StorePath>
            <SubjectName>CN=Quickstart Reference Server, C=US, S=Arizona, O=OPC Foundation, DC=localhost</SubjectName>
        </ApplicationCertificate>

        <!-- Where the issuer certificate are stored (certificate authorities) -->
        <TrustedIssuerCertificates>
            <StoreType>Directory</StoreType>
            <StorePath>%LocalApplicationData%/OPC Foundation/pki/issuer</StorePath>
        </TrustedIssuerCertificates>

        <!-- Where the trust list is stored -->
        <TrustedPeerCertificates>
            <StoreType>Directory</StoreType>
            <StorePath>%LocalApplicationData%/OPC Foundation/pki/trusted</StorePath>
        </TrustedPeerCertificates>

        <!-- The directory used to store invalid certficates for later review by the administrator. -->
        <RejectedCertificateStore>
            <StoreType>Directory</StoreType>
            <StorePath>%LocalApplicationData%/OPC Foundation/pki/rejected</StorePath>
        </RejectedCertificateStore>

        <!-- WARNING: The following setting (to automatically accept untrusted certificates) should be used
        for easy debugging purposes ONLY and turned off for production deployments! -->
        <AutoAcceptUntrustedCertificates>false</AutoAcceptUntrustedCertificates>

        <!-- WARNING: SHA1 signed certficates are by default rejected and should be phased out. 
        The setting below to allow them is only required for UACTT (1.02.336.244) which uses SHA-1 signed certs. -->
        <RejectSHA1SignedCertificates>false</RejectSHA1SignedCertificates>
        <RejectUnknownRevocationStatus>true</RejectUnknownRevocationStatus>
        <MinimumCertificateKeySize>2048</MinimumCertificateKeySize>
        <AddAppCertToTrustedStore>false</AddAppCertToTrustedStore>
        <SendCertificateChain>true</SendCertificateChain>

        <!-- Where the User issuer certificates are stored -->
        <UserIssuerCertificates>
            <StoreType>Directory</StoreType>
            <StorePath>%LocalApplicationData%/OPC Foundation/pki/issuerUser</StorePath>
        </UserIssuerCertificates>

        <!-- Where the User trust list is stored-->
        <TrustedUserCertificates>
            <StoreType>Directory</StoreType>
            <StorePath>%LocalApplicationData%/OPC Foundation/pki/trustedUser</StorePath>
        </TrustedUserCertificates>
    </SecurityConfiguration>

    <TransportConfigurations></TransportConfigurations>
    <TransportQuotas>
        <OperationTimeout>600000</OperationTimeout>
        <MaxStringLength>1048576</MaxStringLength>
        <MaxByteStringLength>1048576</MaxByteStringLength>
        <MaxArrayLength>65535</MaxArrayLength>
        <MaxMessageSize>4194304</MaxMessageSize>
        <MaxBufferSize>65535</MaxBufferSize>
        <ChannelLifetime>300000</ChannelLifetime>
        <SecurityTokenLifetime>3600000</SecurityTokenLifetime>
    </TransportQuotas>
    <ServerConfiguration>
        <BaseAddresses>
            <ua:String>https://localhost:4841/Quickstarts/ReferenceServer</ua:String>
            <ua:String>opc.tcp://localhost:4840/Quickstarts/ReferenceServer</ua:String>
        </BaseAddresses>
        <!-- 
        These list the alternate addresses (via firewalls, multiple NICs etc.) that can be
        used to communicate with the server. The URL used by the client when calling
        FindServers/GetEndpoints or CreateSession will be used to filter the list of
        endpoints returned by checking for alternate base addresses that have a domain
        that matches the domain in the url provided by the client.
        
        Note that any additional domains should be listed in the server's certificate. If they
        are left out the client make refuse to connect because it has no way to know if the 
        alternate domain was authorized by the server administrator.
        -->
        <!--
        <AlternateBaseAddresses>
          <ua:String>http://AlternateHostName/Quickstarts/ReferenceServer</ua:String>
          <ua:String>http://*************/Quickstarts/ReferenceServer</ua:String>
          <ua:String>http://[2a01::626d]/Quickstarts/ReferenceServer</ua:String>
        </AlternateBaseAddresses>
        -->
        <SecurityPolicies>
            <ServerSecurityPolicy>
                <SecurityMode>SignAndEncrypt_3</SecurityMode>
                <SecurityPolicyUri>http://opcfoundation.org/UA/SecurityPolicy#Basic256Sha256</SecurityPolicyUri>
            </ServerSecurityPolicy>
            <ServerSecurityPolicy>
                <SecurityMode>None_1</SecurityMode>
                <SecurityPolicyUri>http://opcfoundation.org/UA/SecurityPolicy#None</SecurityPolicyUri>
            </ServerSecurityPolicy>
            <ServerSecurityPolicy>
                <SecurityMode>Sign_2</SecurityMode>
                <SecurityPolicyUri></SecurityPolicyUri>
            </ServerSecurityPolicy>
            <ServerSecurityPolicy>
                <SecurityMode>SignAndEncrypt_3</SecurityMode>
                <SecurityPolicyUri></SecurityPolicyUri>
            </ServerSecurityPolicy>
            <!-- deprecated security policies for reference only
            <ServerSecurityPolicy>
              <SecurityMode>Sign_2</SecurityMode>
              <SecurityPolicyUri>http://opcfoundation.org/UA/SecurityPolicy#Basic256</SecurityPolicyUri>
            </ServerSecurityPolicy>
            <ServerSecurityPolicy>
              <SecurityMode>SignAndEncrypt_3</SecurityMode>
              <SecurityPolicyUri>http://opcfoundation.org/UA/SecurityPolicy#Basic256</SecurityPolicyUri>
            </ServerSecurityPolicy>
            <ServerSecurityPolicy>
              <SecurityMode>Sign_2</SecurityMode>
              <SecurityPolicyUri>http://opcfoundation.org/UA/SecurityPolicy#Basic128Rsa15</SecurityPolicyUri>
            </ServerSecurityPolicy>
            <ServerSecurityPolicy>
              <SecurityMode>SignAndEncrypt_3</SecurityMode>
              <SecurityPolicyUri>http://opcfoundation.org/UA/SecurityPolicy#Basic128Rsa15</SecurityPolicyUri>
            </ServerSecurityPolicy>
            -->
        </SecurityPolicies>

        <MinRequestThreadCount>5</MinRequestThreadCount>
        <MaxRequestThreadCount>100</MaxRequestThreadCount>
        <MaxQueuedRequestCount>2000</MaxQueuedRequestCount>

        <!-- The SDK expects the server to support the same set of user tokens for every endpoint. -->
        <UserTokenPolicies>
            <!-- Allows anonymous users -->
            <ua:UserTokenPolicy>
                <ua:TokenType>Anonymous_0</ua:TokenType>
                <ua:SecurityPolicyUri>http://opcfoundation.org/UA/SecurityPolicy#None</ua:SecurityPolicyUri>
            </ua:UserTokenPolicy>

            <!-- Allows username/password -->
            <ua:UserTokenPolicy>
                <ua:TokenType>UserName_1</ua:TokenType>
                <!-- passwords must be encrypted - this specifies what algorithm to use -->
                <ua:SecurityPolicyUri>http://opcfoundation.org/UA/SecurityPolicy#Basic256Sha256</ua:SecurityPolicyUri>
            </ua:UserTokenPolicy>

            <!-- Allows user certificates -->
            <ua:UserTokenPolicy>
                <ua:TokenType>Certificate_2</ua:TokenType>
                <!-- certificate possession must be proven with a digital signature - this specifies what algorithm to use -->
                <ua:SecurityPolicyUri>http://opcfoundation.org/UA/SecurityPolicy#Basic256Sha256</ua:SecurityPolicyUri>
            </ua:UserTokenPolicy>
        </UserTokenPolicies>
        <DiagnosticsEnabled>true</DiagnosticsEnabled>
        <MaxSessionCount>100</MaxSessionCount>
        <MinSessionTimeout>10000</MinSessionTimeout>
        <MaxSessionTimeout>3600000</MaxSessionTimeout>
        <MaxBrowseContinuationPoints>10</MaxBrowseContinuationPoints>
        <MaxQueryContinuationPoints>10</MaxQueryContinuationPoints>
        <MaxHistoryContinuationPoints>100</MaxHistoryContinuationPoints>
        <MaxRequestAge>600000</MaxRequestAge>
        <MinPublishingInterval>100</MinPublishingInterval>
        <MaxPublishingInterval>3600000</MaxPublishingInterval>
        <PublishingResolution>50</PublishingResolution>
        <MaxSubscriptionLifetime>3600000</MaxSubscriptionLifetime>
        <MaxMessageQueueSize>100</MaxMessageQueueSize>
        <MaxNotificationQueueSize>100</MaxNotificationQueueSize>
        <MaxNotificationsPerPublish>1000</MaxNotificationsPerPublish>
        <MinMetadataSamplingInterval>1000</MinMetadataSamplingInterval>
        <AvailableSamplingRates>
            <SamplingRateGroup>
                <Start>5</Start>
                <Increment>5</Increment>
                <Count>20</Count>
            </SamplingRateGroup>
            <SamplingRateGroup>
                <Start>100</Start>
                <Increment>100</Increment>
                <Count>4</Count>
            </SamplingRateGroup>
            <SamplingRateGroup>
                <Start>500</Start>
                <Increment>250</Increment>
                <Count>2</Count>
            </SamplingRateGroup>
            <SamplingRateGroup>
                <Start>1000</Start>
                <Increment>500</Increment>
                <Count>20</Count>
            </SamplingRateGroup>
        </AvailableSamplingRates>

        <RegistrationEndpoint>
            <ua:EndpointUrl>opc.tcp://localhost:4840</ua:EndpointUrl>
            <ua:Server>
                <ua:ApplicationUri>opc.tcp://localhost:4840</ua:ApplicationUri>
                <ua:ApplicationType>DiscoveryServer_3</ua:ApplicationType>
                <ua:DiscoveryUrls>
                    <ua:String>opc.tcp://localhost:4840</ua:String>
                </ua:DiscoveryUrls>
            </ua:Server>
            <ua:SecurityMode>SignAndEncrypt_3</ua:SecurityMode>
            <ua:SecurityPolicyUri/>
            <ua:UserIdentityTokens/>
        </RegistrationEndpoint>

        <MaxRegistrationInterval>0</MaxRegistrationInterval>
        <NodeManagerSaveFile>Quickstarts.ReferenceServer.nodes.xml</NodeManagerSaveFile>
        <MinSubscriptionLifetime>10000</MinSubscriptionLifetime>
        <MaxPublishRequestCount>20</MaxPublishRequestCount>
        <MaxSubscriptionCount>100</MaxSubscriptionCount>
        <MaxEventQueueSize>10000</MaxEventQueueSize>

        <!-- see https://opcfoundation-onlineapplications.org/profilereporting/ for list of available profiles -->
        <ServerProfileArray>
            <ua:String>http://opcfoundation.org/UA-Profile/Server/StandardUA2017</ua:String>
            <ua:String>http://opcfoundation.org/UA-Profile/Server/DataAccess</ua:String>
            <ua:String>http://opcfoundation.org/UA-Profile/Server/Methods</ua:String>
            <ua:String>http://opcfoundation.org/UA-Profile/Server/ReverseConnect</ua:String>
        </ServerProfileArray>
        <ShutdownDelay>5</ShutdownDelay>
        <ServerCapabilities>
            <ua:String>DA</ua:String>
        </ServerCapabilities>
        <SupportedPrivateKeyFormats>
            <ua:String>PFX</ua:String>
            <ua:String>PEM</ua:String>
        </SupportedPrivateKeyFormats>
        <MaxTrustListSize>0</MaxTrustListSize>
        <MultiCastDnsEnabled>false</MultiCastDnsEnabled>
        <!--  Reverse connection parameters for aggregation server sample -->
        <!--
        <ReverseConnect>
          <Clients>
            <ReverseConnectClient>
              <EndpointUrl>opc.tcp://localhost:65300</EndpointUrl>
              <MaxSessionCount>0</MaxSessionCount>
              <Enabled>true</Enabled>
            </ReverseConnectClient>
          </Clients>
          <ConnectInterval>15000</ConnectInterval>
          <ConnectTimeout>30000</ConnectTimeout>
          <RejectTimeout>60000</RejectTimeout>
        </ReverseConnect>
        -->
        <OperationLimits>
            <MaxNodesPerRead>1000</MaxNodesPerRead>
            <MaxNodesPerWrite>1000</MaxNodesPerWrite>
            <MaxNodesPerMethodCall>250</MaxNodesPerMethodCall>
            <MaxNodesPerBrowse>2500</MaxNodesPerBrowse>
            <MaxNodesPerTranslateBrowsePathsToNodeIds>1000</MaxNodesPerTranslateBrowsePathsToNodeIds>
            <MaxMonitoredItemsPerCall>1000</MaxMonitoredItemsPerCall>
        </OperationLimits>

    </ServerConfiguration>

    <Extensions>
        <ua:XmlElement>
            <MemoryBufferConfiguration xmlns="http://samples.org/UA/MemoryBuffer">
                <Buffers>
                    <MemoryBufferInstance>
                        <Name>UInt32</Name>
                        <TagCount>100</TagCount>
                        <DataType>UInt32</DataType>
                    </MemoryBufferInstance>
                    <MemoryBufferInstance>
                        <Name>Double</Name>
                        <TagCount>100</TagCount>
                        <DataType>Double</DataType>
                    </MemoryBufferInstance>
                </Buffers>
            </MemoryBufferConfiguration>
        </ua:XmlElement>
    </Extensions>

    <TraceConfiguration>
        <OutputFilePath>%LocalApplicationData%/OPC Foundation/Logs/Quickstarts.ReferenceServer.log.txt</OutputFilePath>
        <DeleteOnLoad>true</DeleteOnLoad>
        <!-- Show Only Errors -->
        <!-- <TraceMasks>1</TraceMasks> -->
        <!-- Show Only Security and Errors -->
        <!-- <TraceMasks>513</TraceMasks> -->
        <!-- Show Only Security, Errors and Trace -->
        <!-- <TraceMasks>515</TraceMasks> -->
        <!-- Show Only Security, COM Calls, Errors and Trace -->
        <!-- <TraceMasks>771</TraceMasks> -->
        <!-- Show Only Security, Service Calls, Errors and Trace -->
        <!-- <TraceMasks>523</TraceMasks> -->
        <!-- Show Only Security, ServiceResultExceptions, Errors and Trace -->
        <!-- <TraceMasks>519</TraceMasks> -->
    </TraceConfiguration>

</ApplicationConfiguration>
