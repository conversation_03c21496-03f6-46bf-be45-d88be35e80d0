<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <ImplicitUsings>enable</ImplicitUsings>
        <SatelliteResourceLanguages>en-US</SatelliteResourceLanguages>
        <Nullable>enable</Nullable>
        <DockerDefaultTargetOS>Windows</DockerDefaultTargetOS>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <Version>1.1.1</Version>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <PlatformTarget>AnyCPU</PlatformTarget>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <PlatformTarget>AnyCPU</PlatformTarget>
    </PropertyGroup>

    <ItemGroup>
        <Content Update="wwwroot\**\*">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Include="..\.dockerignore">
            <Link>.dockerignore</Link>
        </Content>
        <Content Update="appsettings.json">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>
        <Content Update="appsettings.Development.json">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>
        <Content Update="sysappsettings.json">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>
        <Content Update="wwwroot\favicon.ico">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Update="wwwroot\index.html">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Update="wwwroot\monacoeditorwork\ts.worker.bundle.js">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Update="wwwroot\monacoeditorwork\css.worker.bundle.js">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Update="wwwroot\monacoeditorwork\html.worker.bundle.js">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Update="wwwroot\monacoeditorwork\json.worker.bundle.js">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Update="wwwroot\monacoeditorwork\ts.worker.bundle.js.gz">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Update="wwwroot\monacoeditorwork\css.worker.bundle.js.gz">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Update="wwwroot\monacoeditorwork\editor.worker.bundle.js">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Update="wwwroot\monacoeditorwork\html.worker.bundle.js.gz">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Update="wwwroot\monacoeditorwork\json.worker.bundle.js.gz">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Content Update="wwwroot\monacoeditorwork\editor.worker.bundle.js.gz">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\04-架构核心\IotGateway.Web.Core\IotGateway.Web.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="System.Management" Version="8.0.0"/>
    </ItemGroup>
  
    <ItemGroup>
        <Folder Include="plugins\"/>
        <Folder Include="Services\" />
        <Folder Include="wwwroot\" />
    </ItemGroup>

    <ItemGroup>
        <None Update="Quickstarts.ReferenceServer.Config.xml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="RSA.Private">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="appsettings.Development.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="appsettings.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="sysappsettings.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Templates\DeviceVariableImport.xlsx">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Templates\DeviceVariableSimpleImport.xlsx">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Templates\EdgeComputingPolicyImport.xlsx">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Templates\EdgeComputingScriptImport.xlsx">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Remove="wwwroot\monacoeditorwork\**" />
        <None Remove="wwwroot\static\**" />
        <None Remove="wwwroot\favicon.ico" />
        <None Remove="wwwroot\index.html" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="wwwroot\monacoeditorwork\**" />
      <Compile Remove="wwwroot\static\**" />
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Remove="wwwroot\monacoeditorwork\**" />
      <EmbeddedResource Remove="wwwroot\static\**" />
    </ItemGroup>

</Project>
