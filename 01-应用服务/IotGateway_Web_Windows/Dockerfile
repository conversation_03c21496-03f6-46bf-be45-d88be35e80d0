FROM mcr.microsoft.com/dotnet/runtime:6.0 AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["IotGateway_Web_Windows/IotGateway_Web_Windows.csproj", "IotGateway_Web_Windows/"]
RUN dotnet restore "IotGateway_Web_Windows/IotGateway_Web_Windows.csproj"
COPY . .
WORKDIR "/src/IotGateway_Web_Windows"
RUN dotnet build "IotGateway_Web_Windows.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "IotGateway_Web_Windows.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "IotGateway_Web_Windows.dll"]
