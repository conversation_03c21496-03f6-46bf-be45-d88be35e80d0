{"$schema": "https://gitee.com/dotnetchina/Furion/raw/net6/schemas/v3/furion-schema.json", "DbConnection": {"EnableConsoleSql": true, "ConnectionConfigs": [{"ConfigId": "1300000000001", "DbType": "Sqlite", "ConnectionString": "Data Source=./IotGatewayTerminal.db", "EnableInitTable": true, "EnableInitSeed": true}, {"ConfigId": "实时数据", "DbType": "Sqlite", "ConnectionString": "Data Source=./EdgeData.db", "EnableInitTable": true, "EnableInitSeed": true}]}, "Cache": {"CacheType": "<PERSON><PERSON>", "RedisConnectionString": "server=192.168.3.34:6379;password=**********;db=7;"}, "Event": {"Type": "<PERSON><PERSON>"}}