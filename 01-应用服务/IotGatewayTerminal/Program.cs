using System.Reflection;
using Feng.IotGateway.Core.Const;
using Feng.IotGateway.Core.Util;
using Furion.DataEncryption;
using HslCommunication;

Serve.Run(RunOptions.Default.ConfigureBuilder(builder =>
{
    MachineUtil.Version = Assembly.GetExecutingAssembly().GetName().Version!.ToString();
    Authorization.SetAuthorizationCode(DESEncryption.Decrypt(CommonConst.Authorization, "Feng"));
    // 网关出厂日期如果是小于2020年 雪花ID生成会有问题.
    if (DateTime.Now.Year < 2020) _ = ShellUtil.Bash("date -s \"2022-10-01 00:00:00:0000\" && hwclock -w --systohc ");
}));