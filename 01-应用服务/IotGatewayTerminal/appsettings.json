{"$schema": "https://gitee.com/dotnetchina/Furion/raw/net6/schemas/v3/furion-schema.json", "DbConnection": {"EnableConsoleSql": false, "ConnectionConfigs": [{"ConfigId": "1300000000001", "DbType": "Sqlite", "ConnectionString": "Data Source=/media/app/Data/IotGatewayTerminal.db", "EnableInitTable": true, "EnableInitSeed": true}, {"ConfigId": "实时数据", "DbType": "Sqlite", "ConnectionString": "Data Source=/media/app/Data/EdgeData.db", "EnableInitTable": true, "EnableInitSeed": false}]}, "Cache": {"CacheType": "<PERSON><PERSON>", "RedisConnectionString": "server=127.0.0.1:6379;password=**********;db=7;"}, "Event": {"Type": "<PERSON><PERSON>"}}