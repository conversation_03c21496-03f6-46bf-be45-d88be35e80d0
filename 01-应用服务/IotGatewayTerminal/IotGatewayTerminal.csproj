<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <Nullable>enable</Nullable>
        <SatelliteResourceLanguages>en-US</SatelliteResourceLanguages>
        <ImplicitUsings>enable</ImplicitUsings>
        <Version>1.0.0</Version>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugSymbols>false</DebugSymbols>
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <Folder Include="plugins\" />
    </ItemGroup>

    <ItemGroup>
      <None Update="Templates\DeviceVariableImport.xlsx">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="Templates\DeviceVariableSimpleImport.xlsx">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="versions\v1.0.0.md">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <Content Update="sysappsettings.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\04-架构核心\IotGatewayTerminal.Web.Core\IotGatewayTerminal.Web.Core.csproj" />
    </ItemGroup>

</Project>
