{"$schema": "https://gitee.com/dotnetchina/Furion/raw/net6/schemas/v3/furion-schema.json", "Urls": "http://*:5005", "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System.Net.Http.HttpClient": "Warning", "System.Logging.ScheduleService": "Error"}, "File": {"FileName": "logs/info.log", "Append": true, "MinimumLevel": "Information", "FileSizeLimitBytes": 5120, "MaxRollingFiles": 5}, "Database": {"MinimumLevel": "Information"}, "Monitor": {"GlobalEnabled": true, "IncludeOfMethods": [], "ExcludeOfMethods": []}}, "SpecificationDocumentSettings": {"DocExpansionState": "None", "DocumentTitle": "Gateway.Terminal | 标准化接口", "GroupOpenApiInfos": [{"Group": "Gateway.Terminal", "Title": "Gateway.Terminal 规范化接口", "Description": "Gateway.Terminal", "Version": "1.0.0"}, {"Group": "All Groups", "Title": "所有接口", "Version": "1.0.0", "Order": 0}], "EnableAllGroups": true, "EnumToNumber": true, "LoginInfo": {"Enabled": true, "CheckUrl": "/Swagger/CheckUrl", "SubmitUrl": "/Swagger/SubmitUrl", "Meta": false}}, "RefreshToken": {"ExpiredTime": 43200}, "JWTSettings": {"ValidateIssuerSigningKey": true, "IssuerSigningKey": "0a3852e18a1e9b06a9c4f2d3b5f9d7e8b2e0c8d4f6a2d3b5f9d7e8b2e0c8d4f", "ValidateIssuer": true, "ValidIssuer": "HangZhou.FengHui", "ValidateAudience": true, "ValidAudience": "HangZhou.FengHui", "ValidateLifetime": true, "ExpiredTime": 500, "ClockSkew": 5, "Algorithm": "HS256"}, "AppSettings": {"ExternalAssemblies": ["plugins/IotGateway.TerUbuntu.dll"], "InjectSpecificationDocument": true}}