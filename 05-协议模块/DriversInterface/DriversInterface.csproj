<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
        <OutputPath>..\..\..\..\..\01-应用服务\IotGateway/bin/$(Configuration)/$(TargetFramework)/drivers/plc/</OutputPath>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Driver.Core\Driver.Core.csproj"/>
    </ItemGroup>

</Project>
