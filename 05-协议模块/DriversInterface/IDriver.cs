using Driver.Core;
using Driver.Core.Models;
using Driver.Core.Write.Dto;

namespace DriversInterface;

/// <summary>
/// </summary>
public interface IDriver : IDisposable
{
    /// <summary>
    ///     读取模式：1：轮询模式；2：订阅触发模式
    /// </summary>
    public ReadTypeEnum ReadType { get; set; }

    /// <summary>
    ///     反射给协议加载的实体对象
    /// </summary>
    public DriverInfoDto DriverInfo { get; set; }

    /// <summary>
    ///     设备连接状态
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    ///     轮询周期(s) 轮询设备的时间周期设备
    /// </summary>
    public int MinPeriod { get; }

    /// <summary>
    ///     等待时间
    /// </summary>
    public int WaitTime { get; set; }

    /// <summary>
    ///     设备重新连接时间  tcp连接中断后自动重连周期
    /// </summary>
    public int ReConnTime { get; set; }

    /// <summary>
    ///     超时次数 单站设备所有报文均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备
    /// </summary>
    public int TimeOutCount { get; set; }

    /// <summary>
    ///     退避时间(m) 进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询报文里
    /// </summary>
    public int BackoffTime { get; set; }

    /// <summary>
    ///     报文间隔(ms)
    /// </summary>
    public int MessageInterval { get; set; }

    /// <summary>
    ///     连接信息
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect();

    /// <summary>
    ///     是否关闭
    /// </summary>
    /// <returns></returns>
    public void Close();

    /// <summary>
    ///     Plc写入方法
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public Task<WriteResponse> WriteAsync(DriverAddressIoArgModel val);

    /// <summary>
    ///     监听等待方法
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public Task<WaitAsyncOutput> WaitAsync(WaitAsyncInput val);
}