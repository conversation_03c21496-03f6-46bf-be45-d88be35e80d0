using Common.Enums;
using Common.Extension;
using Console = Feng.Common.Extension.Console;
using System.Text;

namespace Driver.Core.ReadBase.Modbus;

/*
 * Modbus基类协议
 * Modbus几类地址:
 * S=1;1  带站号读
 * S=1;x=3;1  带站号带寄存区读
 * x=3;1 带寄存区读
 * 1   直接读地址
 * 1.0 bool地址读取
 * 
 * 修改记录:
 * 2025-06-12:
 * 1. 增强了BatchReadHolding方法中的索引边界检查，修复了数组越界问题
 * 2. 启用了批量读取失败后的单独读取功能，提高读取成功率
 */
public class ModBusBase : BasePlcProtocolCollector
{
    /// <summary>
    ///     modbus批量读取分类地址
    /// </summary>
    private readonly List<GroupAddress> _groupAddress = new();

    /// <summary>
    ///     单独读取地址
    /// </summary>
    private readonly List<DriverAddressIoArgModel> _otherAddress = new();

    /// <summary>
    ///     批量读取失败的地址
    /// </summary>
    private readonly List<DriverAddressIoArgModel> _batchReadFailed = new();

    /// <summary>
    ///     报文间隔(ms)
    /// </summary>
    [ConfigParameter("报文间隔(ms)", GroupName = "高级配置", Display = true)]
    public override int MessageInterval { get; set; }

    /// <summary>
    /// 单次查询最大寄存器数量，0表示使用不限制
    /// </summary>
    [ConfigParameter("最大查询长度", GroupName = "高级配置", Remark = "单次查询最大寄存器数量，0表示使用不限制")]
    public int MaxQueryLength { get; set; } = 100;

    /// <summary>
    /// 地址间隔超过此值时分为不同组
    /// </summary>
    [ConfigParameter("地址间隔阈值", GroupName = "高级配置", Remark = "地址间隔超过此值时分为不同组")]
    public int PacketInterval { get; set; } = 20;

    [ConfigParameter("站号")] public byte Station { get; set; } = 1;
    [ConfigParameter("解析类型")] public DataFormat DataFormat { get; set; } = DataFormat.ABCD;
    [ConfigParameter("大小端转换")] public BoolEnum ByteTransform { get; set; } = BoolEnum.False;
    [ConfigParameter("字符串颠倒")] public BoolEnum StringReverse { get; set; } = BoolEnum.False;

    /// <summary>
    ///     解析地址
    /// </summary>
    /// <param name="registerAddress"></param>
    /// <param name="dataType">数据类型</param>
    /// <returns></returns>
    [Method("Parse", description: "用于解析Modbus的标准地址", name: "Parse")]
    public string Parse(string registerAddress, DataTypeEnum dataType)
    {
        if (registerAddress.IsNull()) return "Read";

        // 直接是地址的
        if (registerAddress.IndexOf(";", StringComparison.Ordinal) < 0)
            switch (dataType)
            {
                //0x01;0x03
                case DataTypeEnum.Bit:
                case DataTypeEnum.Bool:
                    return "ReadCoil";
                default:
                    return "ReadHoldingRegisters";
            }

        switch (dataType)
        {
            // 0x02;0x04
            case DataTypeEnum.Bit:
            case DataTypeEnum.Bool:
                return "ReadInput";
            default:
                return "ReadInputRegisters";
        }
    }

    #region 地址初始化

    /// <summary>
    ///     地址分组,只初始化一次
    /// </summary>
    /// <param name="groupVal"></param>
    [Method("Init", description: "地址初始化", name: "Init")]
    public bool InitGroup(List<IGrouping<string, DriverAddressIoArgModel>> groupVal)
    {
        try
        {
            _groupAddress.Clear();
            _otherAddress.Clear();
            _batchReadFailed.Clear();

            // 记录总地址数量
            int totalAddressCount = groupVal.Sum(g => g.Count());
            _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 初始化地址，共{groupVal.Count}组，{totalAddressCount}个地址", DriverInfo.DeviceId);

            // 简化处理逻辑，不再按存储区域分类
            // 所有地址都放入_groupAddress中的一个组中
            List<DriverAddressIoArgModel> allAddresses = new List<DriverAddressIoArgModel>();

            foreach (var group in groupVal)
            {
                foreach (var addr in group)
                {
                    // 字符串和BCD类型单独处理
                    if (addr.DataType is DataTypeEnum.String or DataTypeEnum.Bcd)
                    {
                        _otherAddress.Add(addr);
                    }
                    else
                    {
                        allAddresses.Add(addr);
                    }
                }
            }

            // 记录分组结果
            _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 地址初始化完成，共{allAddresses.Count}个批量处理地址，{_otherAddress.Count}个单独处理地址", DriverInfo.DeviceId);

            // 将所有地址放入一个组中，实际分组会在BatchRead中进行
            if (allAddresses.Count > 0)
            {
                _groupAddress.Add(new GroupAddress
                {
                    Address = allAddresses,
                    StorageArea = "All", // 标记为统一处理
                    Prefix = ""
                });
            }
        }
        catch (Exception ex)
        {
            _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 解析地址失败,Error:{ex.Message}", DriverInfo.DeviceId);
            return false;
        }

        return true;
    }

    /// <summary>
    ///     采集地址切割分组
    /// </summary>
    /// <param name="storageArea"></param>
    /// <param name="address"></param>
    private void SplitAndGroupAddress(string storageArea, List<DriverAddressIoArgModel> address)
    {
        try
        {
            // 提取每个地址的实际数值部分
            var addressValues = new List<(int Value, DriverAddressIoArgModel Address)>();

            foreach (var addr in address)
            {
                try
                {
                    string addressValue;
                    string prefix = string.Empty;

                    // 处理带前缀的地址，如 "X=4;2027" 或 "S=1;1"
                    if (addr.Address.IndexOf(";", StringComparison.Ordinal) > 0)
                    {
                        var parts = addr.Address.Split(";");
                        addressValue = parts[^1]; // 取最后一部分作为实际地址值

                        // 构建前缀
                        for (int i = 0; i < parts.Length - 1; i++)
                        {
                            prefix += parts[i] + ";";
                        }
                    }
                    else
                    {
                        addressValue = addr.Address;
                    }

                    // 尝试转换为整数
                    if (int.TryParse(addressValue, out int numericValue))
                    {
                        addressValues.Add((numericValue, addr));
                    }
                    else
                    {
                        _otherAddress.Add(addr);
                        _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 地址解析失败:【{addr.Address}】无法提取数值部分", DriverInfo.DeviceId);
                    }
                }
                catch
                {
                    _otherAddress.Add(addr);
                    _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 地址解析失败:【{addr.Address}】", DriverInfo.DeviceId);
                }
            }

            if (addressValues.Count == 0)
                return;

            // 按前缀分组
            var prefixGroups = addressValues
                .GroupBy(a => GetAddressPrefix(a.Address.Address))
                .ToList();

            // 记录前缀分组情况
            _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 地址按前缀分组，共{prefixGroups.Count}个前缀组", DriverInfo.DeviceId);
            foreach (var group in prefixGroups)
            {
                _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 前缀组：'{group.Key}'，包含{group.Count()}个地址", DriverInfo.DeviceId);
            }

            foreach (var prefixGroup in prefixGroups)
            {
                var prefix = prefixGroup.Key;
                var groupAddresses = prefixGroup.ToList();

                if (groupAddresses.Count == 0)
                    continue;

                var minNumber = groupAddresses.Min(a => a.Value);
                var maxNumber = groupAddresses.Max(a => a.Value);
                var numberList = groupAddresses.Select(a => a.Value).ToList();
                var addressList = groupAddresses.Select(a => a.Address).ToList();

                // 创建分组
                _groupAddress.Add(new GroupAddress
                {
                    Max = maxNumber,
                    MinNumber = minNumber,
                    Address = addressList,
                    StorageArea = storageArea,
                    NumberList = numberList,
                    Prefix = prefix
                });

                _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 创建地址分组: 前缀={prefix}, 存储区={storageArea}, 地址数量={addressList.Count}, 范围={minNumber}-{maxNumber}", DriverInfo.DeviceId);
            }
        }
        catch (Exception ex)
        {
            throw Oops.Bah($"【Modbus】 解析地址失败,错误信息:{ex.Message}");
        }
    }

    /// <summary>
    /// 获取地址的前缀部分
    /// </summary>
    /// <param name="address">完整地址字符串</param>
    /// <returns>地址前缀，如 "S=1;X=3;" 或空字符串</returns>
    private string GetAddressPrefix(string address)
    {
        if (string.IsNullOrEmpty(address))
            return string.Empty;

        if (address.IndexOf(";", StringComparison.Ordinal) < 0)
            return string.Empty;

        var parts = address.Split(';');
        if (parts.Length <= 1)
            return string.Empty;

        // 构建前缀字符串，不包括最后一部分（实际地址值）
        var prefixBuilder = new StringBuilder();
        for (int i = 0; i < parts.Length - 1; i++)
        {
            prefixBuilder.Append(parts[i]);
            prefixBuilder.Append(';');
        }

        return prefixBuilder.ToString();
    }

    #endregion

    #region 批量读取

    /// <summary>
    /// 解析Modbus地址，提取站号、功能码和偏移量
    /// </summary>
    /// <param name="address">地址字符串，如"S=1;100"或"100"</param>
    /// <returns>包含站号、功能码和偏移量的元组</returns>
    private (byte Station, string FunctionCode, int Offset, int BitIndex) ParseModbusAddress(string address)
    {
        // 默认值
        byte station = Station; // 使用默认站号
        string functionCode = "03"; // 默认使用03功能码（读保持寄存器）
        int offset = 0;
        int bitIndex = -1;

        try
        {
            // 解析地址
            if (string.IsNullOrEmpty(address))
                return (station, functionCode, offset, bitIndex);

            // 处理带前缀的地址
            if (address.Contains(";"))
            {
                var parts = address.Split(';');

                // 最后一部分是实际地址值
                string lastPart = parts[^1];
                if (int.TryParse(lastPart, out offset))
                {
                    // 处理前面的部分
                    for (int i = 0; i < parts.Length - 1; i++)
                    {
                        string part = parts[i].Trim();

                        // 处理站号
                        if (part.StartsWith("S=", StringComparison.OrdinalIgnoreCase))
                        {
                            if (byte.TryParse(part.Substring(2), out byte s))
                                station = s;
                        }
                        // 处理功能码
                        else if (part.StartsWith("x=", StringComparison.OrdinalIgnoreCase) ||
                                 part.StartsWith("X=", StringComparison.OrdinalIgnoreCase))
                        {
                            if (byte.TryParse(part.Substring(2), out byte fc))
                            {
                                // 转换功能码
                                functionCode = fc.ToString();

                                // 根据功能码确定是读线圈还是读寄存器
                                if (fc == 1 || fc == 2)
                                    functionCode = fc == 1 ? "01" : "02"; // 读线圈或读离散输入
                                else if (fc == 3 || fc == 4)
                                    functionCode = fc == 3 ? "03" : "04"; // 读保持寄存器或读输入寄存器
                            }
                        }
                    }
                }
            }
            else
            {
                // 直接是地址值
                if (address.Contains("."))
                {
                    // 处理位地址，如 "100.0"
                    var parts = address.Split('.');
                    if (int.TryParse(parts[0], out offset) && parts.Length > 1)
                    {
                        if (int.TryParse(parts[1], out int bit))
                            bitIndex = bit;
                    }
                }
                else
                {
                    // 纯数字地址
                    int.TryParse(address, out offset);
                }
            }
        }
        catch (Exception ex)
        {
            _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【地址解析错误】{address}: {ex.Message}", DriverInfo.DeviceId);
        }

        return (station, functionCode, offset, bitIndex);
    }

    /// <summary>
    /// 获取数据类型的字节长度
    /// </summary>
    /// <param name="param">参数</param>
    /// <returns>字节长度</returns>
    private ushort GetDataTypeLength(DriverAddressIoArgModel param)
    {
        return param.DataType switch
        {
            DataTypeEnum.Bool or DataTypeEnum.Bit => 1,
            DataTypeEnum.Int32 or DataTypeEnum.Uint32 or DataTypeEnum.Float => 4,
            DataTypeEnum.Double or DataTypeEnum.Int64 or DataTypeEnum.Uint64 => 8,
            DataTypeEnum.Int16 or DataTypeEnum.Uint16 => 2,
            DataTypeEnum.String => (ushort)(param.Length == 0 ? 20 : param.Length), // 默认字符串长度为20
            DataTypeEnum.Bcd => (ushort)(param.Length == 0 ? 8 : param.Length),
            _ => 2
        };
    }

    /// <summary>
    /// 获取数据类型的寄存器长度
    /// </summary>
    /// <param name="dataType">数据类型</param>
    /// <param name="length">字符串或BCD类型的长度</param>
    /// <returns>寄存器长度</returns>
    private int GetRegisterLength(DataTypeEnum dataType, int length = 0)
    {
        return dataType switch
        {
            DataTypeEnum.Bool or DataTypeEnum.Bit => 1,
            DataTypeEnum.Int16 or DataTypeEnum.Uint16 => 1,
            DataTypeEnum.Int32 or DataTypeEnum.Uint32 or DataTypeEnum.Float => 2,
            DataTypeEnum.Int64 or DataTypeEnum.Uint64 or DataTypeEnum.Double => 4,
            DataTypeEnum.String => (length == 0 ? 10 : (length + 1) / 2), // 字符串按照每个字符1字节，每个寄存器2字节计算
            DataTypeEnum.Bcd => (length == 0 ? 4 : (length + 1) / 2),
            _ => 1
        };
    }

    /// <summary>
    /// 解析数据值
    /// </summary>
    /// <param name="param">参数</param>
    /// <param name="content">读取的内容</param>
    /// <param name="offset">偏移量</param>
    /// <returns>解析后的值</returns>
    private object ParseValue(DriverAddressIoArgModel param, byte[] content, int offset)
    {
        try
        {
            var addressInfo = ParseModbusAddress(param.Address);

            // 根据数据类型解析值
            if (param.DataType == DataTypeEnum.Bool || param.DataType == DataTypeEnum.Bit)
            {
                // 布尔类型特殊处理
                if (addressInfo.BitIndex >= 0)
                {
                    // 有位索引的情况
                    byte byteValue = content[offset];
                    bool value = ((byteValue >> addressInfo.BitIndex) & 0x01) == 0x01;
                    return param.DataType == DataTypeEnum.Bit ? (value ? 1 : 0) : value;
                }
                else if (addressInfo.FunctionCode == "01" || addressInfo.FunctionCode == "02")
                {
                    // 读线圈或离散输入的情况
                    bool value = content[offset] == 1;
                    return param.DataType == DataTypeEnum.Bit ? (value ? 1 : 0) : value;
                }
                else
                {
                    // 从寄存器中读取布尔值
                    bool value = Driver.ByteTransform.TransBool(content, offset);
                    return param.DataType == DataTypeEnum.Bit ? (value ? 1 : 0) : value;
                }
            }
            else if (param.DataType == DataTypeEnum.String)
            {
                // 字符串类型特殊处理
                int stringLength = param.Length == 0 ? 20 : param.Length;

                // 确保不会读取超出数据范围
                stringLength = Math.Min(stringLength, content.Length - offset);

                // 读取字符串并去除可能的空字符
                string value = Encoding.ASCII.GetString(content, offset, stringLength);

                // 如果配置了字符串颠倒，则反转字符串
                if (StringReverse == BoolEnum.True)
                {
                    char[] charArray = value.ToCharArray();
                    Array.Reverse(charArray);
                    value = new string(charArray);
                }

                return value.TrimEnd('\0');
            }
            else if (param.DataType == DataTypeEnum.Bcd)
            {
                // BCD类型特殊处理
                int bcdLength = param.Length == 0 ? 8 : param.Length;
                bcdLength = Math.Min(bcdLength, content.Length - offset);
                byte[] bcdBytes = new byte[bcdLength];
                Array.Copy(content, offset, bcdBytes, 0, bcdLength);
                return BitConverter.ToString(bcdBytes).Replace("-", "");
            }
            else
            {
                // 根据数据类型使用对应的转换方法
                object result = param.DataType switch
                {
                    DataTypeEnum.Int32 => Driver.ByteTransform.TransInt32(content, offset),
                    DataTypeEnum.Uint32 => Driver.ByteTransform.TransUInt32(content, offset),
                    DataTypeEnum.Float => Driver.ByteTransform.TransSingle(content, offset),
                    DataTypeEnum.Double => Driver.ByteTransform.TransDouble(content, offset),
                    DataTypeEnum.Int64 => Driver.ByteTransform.TransInt64(content, offset),
                    DataTypeEnum.Uint64 => Driver.ByteTransform.TransUInt64(content, offset),
                    DataTypeEnum.Int16 => Driver.ByteTransform.TransInt16(content, offset),
                    DataTypeEnum.Uint16 => Driver.ByteTransform.TransUInt16(content, offset),
                    _ => Driver.ByteTransform.TransInt16(content, offset)
                };

                return result;
            }
        }
        catch (Exception ex)
        {
            _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 解析值异常: {param.Address}, 类型:{param.DataType}, 错误:{ex.Message}", DriverInfo.DeviceId);
            throw;
        }
    }

    /// <summary>
    /// 根据地址连续性和最大查询长度对参数进行分组
    /// </summary>
    /// <param name="parameters">参数列表</param>
    /// <returns>分组后的参数列表</returns>
    private List<List<DriverAddressIoArgModel>> GroupParameters(List<DriverAddressIoArgModel> parameters)
    {
        var result = new List<List<DriverAddressIoArgModel>>();
        if (parameters.Count == 0) return result;

        // 按功能码和站号分组
        var functionGroups = parameters
            .GroupBy(p =>
            {
                var addr = ParseModbusAddress(p.Address);
                return $"{addr.Station}:{addr.FunctionCode}";
            })
            .ToList();

        _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 按站号和功能码分组，共 {functionGroups.Count} 个分组", DriverInfo.DeviceId);

        // 处理每个功能码分组
        foreach (var functionGroup in functionGroups)
        {
            var groupKey = functionGroup.Key;
            var groupParams = functionGroup.ToList();

            // 按地址排序
            var sortedParams = groupParams
                .Select(p => new
                {
                    Param = p,
                    Address = ParseModbusAddress(p.Address),
                    RegisterLength = GetRegisterLength(p.DataType, p.Length)
                })
                .OrderBy(p => p.Address.Offset)
                .ToList();

            // 当前分组
            var currentGroup = new List<DriverAddressIoArgModel>();
            int currentStartOffset = sortedParams[0].Address.Offset;
            int currentEndOffset = currentStartOffset;

            // 如果MaxQueryLength为0，则使用较大的值，但仍然保持PacketInterval分组逻辑
            int maxQueryLength = MaxQueryLength <= 0 ? int.MaxValue : MaxQueryLength;

            foreach (var item in sortedParams)
            {
                int itemOffset = item.Address.Offset;
                int itemLength = item.RegisterLength;
                int itemEndOffset = itemOffset + itemLength;

                // 如果是第一个地址，直接添加
                if (currentGroup.Count == 0)
                {
                    currentGroup.Add(item.Param);
                    currentEndOffset = itemEndOffset;
                    continue;
                }

                // 检查是否需要创建新分组
                bool createNewGroup = false;
                string reason = "";

                // 如果地址间隔过大 - 始终检查，即使MaxQueryLength=0
                if (itemOffset - currentEndOffset > PacketInterval)
                {
                    createNewGroup = true;
                    reason = $"间隔过大";
                    _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【分组信息】新建分组({reason}): {groupKey}.{currentStartOffset}:{currentEndOffset - currentStartOffset}, 寄存器间隔: {itemOffset - currentEndOffset}", DriverInfo.DeviceId);
                }
                // 如果读取长度超过最大查询长度
                else if (itemEndOffset - currentStartOffset > maxQueryLength)
                {
                    createNewGroup = true;
                    reason = $"长度超限";
                    _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【分组信息】新建分组({reason}): {groupKey}.{currentStartOffset}:{currentEndOffset - currentStartOffset}, 当前长度: {currentEndOffset - currentStartOffset}, 新增长度: {itemLength}", DriverInfo.DeviceId);
                }

                // 创建新分组
                if (createNewGroup)
                {
                    result.Add(new List<DriverAddressIoArgModel>(currentGroup));
                    currentGroup.Clear();
                    currentGroup.Add(item.Param);
                    currentStartOffset = itemOffset;
                    currentEndOffset = itemEndOffset;
                }
                else
                {
                    // 添加到当前分组
                    currentGroup.Add(item.Param);
                    if (itemEndOffset > currentEndOffset)
                        currentEndOffset = itemEndOffset;
                }
            }

            // 添加最后一个分组
            if (currentGroup.Count > 0)
            {
                result.Add(currentGroup);
                _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【分组信息】添加最后分组: {groupKey}.{currentStartOffset}:{currentEndOffset - currentStartOffset}, 寄存器范围: {currentStartOffset}-{currentEndOffset}", DriverInfo.DeviceId);
            }
        }

        // 输出分组结果
        _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【分组结果】共分为 {result.Count} 组", DriverInfo.DeviceId);

        // 只打印每组的基本信息，不再详细列出每个地址
        for (int i = 0; i < result.Count; i++)
        {
            var group = result[i];
            if (group.Count == 0) continue;

            var firstAddr = ParseModbusAddress(group[0].Address);
            var lastAddr = ParseModbusAddress(group[group.Count - 1].Address);
            int lastLength = GetRegisterLength(group[group.Count - 1].DataType, group[group.Count - 1].Length);
            int totalLength = (lastAddr.Offset + lastLength) - firstAddr.Offset;

            _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 分组 #{i + 1}: 站号={firstAddr.Station}, 功能码={firstAddr.FunctionCode}, 包含 {group.Count} 个地址, 寄存器范围: {firstAddr.Offset}-{lastAddr.Offset + lastLength}", DriverInfo.DeviceId);
        }

        return result;
    }

    /// <summary>
    /// 读取参数，统一处理所有类型
    /// </summary>
    /// <param name="parameters">参数列表</param>
    /// <returns>读取结果列表</returns>
    private async Task<List<DriverReturnValueModel>> ReadParameters(List<DriverAddressIoArgModel> parameters)
    {
        var output = new List<DriverReturnValueModel>();
        if (parameters.Count == 0) return output;

        // 对参数进行分组
        var groups = GroupParameters(parameters);
        _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【批量读取】参数共分为 {groups.Count} 组", DriverInfo.DeviceId);

        // 记录读取请求次数
        int readRequestCount = 0;

        // 处理每个分组
        for (int groupIndex = 0; groupIndex < groups.Count; groupIndex++)
        {
            var group = groups[groupIndex];
            if (group.Count == 0) continue;

            try
            {
                // 获取分组的第一个地址信息
                var firstParam = group[0];
                var firstAddr = ParseModbusAddress(firstParam.Address);

                // 获取分组的最后一个地址和其数据长度
                var lastParam = group[group.Count - 1];
                var lastAddr = ParseModbusAddress(lastParam.Address);
                int lastLength = GetRegisterLength(lastParam.DataType, lastParam.Length);

                // 计算总的寄存器长度
                int totalLength = (lastAddr.Offset - firstAddr.Offset) + lastLength;

                // 构建起始地址
                string startAddress = firstAddr.Offset.ToString();
                if (firstAddr.Station != Station)
                {
                    startAddress = $"S={firstAddr.Station};{startAddress}";
                }
                if (firstAddr.FunctionCode != "03")
                {
                    startAddress = $"x={firstAddr.FunctionCode};{startAddress}";
                }

                // 构建地址范围字符串用于日志
                string addressRange = $"{startAddress}:{totalLength}";

                // 执行单次读取
                var readStopwatch = System.Diagnostics.Stopwatch.StartNew();
                OperateResult<byte[]> read = new OperateResult<byte[]>();
                readRequestCount++;

                // 根据功能码选择读取方法
                if (firstAddr.FunctionCode == "01" || firstAddr.FunctionCode == "02")
                {
                    // 读线圈或离散输入
                    _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【发送请求 #{readRequestCount}】ReadCoil: {startAddress}, 长度: {totalLength}", DriverInfo.DeviceId);
                    var coilResult = await Driver.ReadCoilAsync(startAddress, (ushort)totalLength);

                    // 使用LogNet记录读取结果
                    if (Driver.LogNet != null)
                    {
                        string logMessage = coilResult.IsSuccess
                            ? $"{addressRange} 查询总耗时：{readStopwatch.ElapsedMilliseconds}ms"
                            : $"{addressRange} 查询失败，耗时{readStopwatch.ElapsedMilliseconds}ms，返回报错信息：{coilResult.Message}";

                        if (coilResult.IsSuccess)
                            Driver.LogNet.WriteDebug("Modbus", logMessage);
                        else
                            Driver.LogNet.WriteError("Modbus", logMessage);
                    }

                    if (coilResult.IsSuccess)
                    {
                        // 将布尔数组转换为字节数组
                        byte[] byteContent = new byte[coilResult.Content.Length];
                        for (int i = 0; i < coilResult.Content.Length; i++)
                        {
                            byteContent[i] = (byte)(coilResult.Content[i] ? 1 : 0);
                        }
                        read = OperateResult.CreateSuccessResult(byteContent);
                    }
                }
                else
                {
                    // 读寄存器
                    _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【发送请求 #{readRequestCount}】ReadAsync: {startAddress}, 长度: {totalLength}", DriverInfo.DeviceId);
                    read = await Driver.ReadAsync(startAddress, (ushort)totalLength);

                    // 使用LogNet记录读取结果
                    if (Driver.LogNet != null)
                    {
                        string logMessage = read.IsSuccess
                            ? $"{addressRange} 查询总耗时：{readStopwatch.ElapsedMilliseconds}ms"
                            : $"{addressRange} 查询失败，耗时{readStopwatch.ElapsedMilliseconds}ms，返回报错信息：{read.Message}";

                        if (read.IsSuccess)
                            Driver.LogNet.WriteDebug("Modbus", logMessage);
                        else
                            Driver.LogNet.WriteError("Modbus", logMessage);
                    }
                }

                readStopwatch.Stop();
                _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【性能】分组 #{groupIndex + 1} 读取耗时: {readStopwatch.ElapsedMilliseconds}ms, 成功: {read.IsSuccess}", DriverInfo.DeviceId);

                // 使用新的BatchSocketSend方法记录读取结果
                if (!read.IsSuccess)
                {
                    // 所有地址均标记为失败
                    output.AddRange(group.Select(param => new DriverReturnValueModel
                    {
                        Id = param.Id,
                        VariableStatus = VariableStatusTypeEnum.Error,
                        Message = read.Message,
                        DataType = param.DataType
                    }));
                }
                else
                {
                    foreach (var param in group)
                    {
                        try
                        {
                            // 计算该参数相对于起始地址的偏移量
                            var addr = ParseModbusAddress(param.Address);
                            int relativeRegisterOffset = addr.Offset - firstAddr.Offset;
                            int relativeByteOffset = relativeRegisterOffset * 2; // Modbus寄存器是2字节

                            // 获取参数的字节长度
                            var byteLength = GetDataTypeLength(param);

                            // 确保偏移量不超出读取的数据范围
                            if (relativeByteOffset < 0 || relativeByteOffset + byteLength > read.Content.Length)
                            {
                                // 对于字符串类型，可能需要调整长度
                                if (param.DataType == DataTypeEnum.String || param.DataType == DataTypeEnum.Bcd)
                                {
                                    // 计算可用的字节数
                                    int availableBytes = read.Content.Length - relativeByteOffset;
                                    if (availableBytes > 0)
                                    {
                                        // 使用可用的字节数解析字符串
                                        _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【字符串调整】地址={param.Address}, 请求长度={byteLength}, 可用长度={availableBytes}", DriverInfo.DeviceId);

                                        // 解析字符串数据
                                        string strValue = Encoding.ASCII.GetString(read.Content, relativeByteOffset, availableBytes);

                                        // 去除可能的空字符
                                        strValue = strValue.TrimEnd('\0');

                                        output.Add(new DriverReturnValueModel
                                        {
                                            Id = param.Id,
                                            VariableStatus = VariableStatusTypeEnum.Good,
                                            Message = "字符串长度已调整",
                                            DataType = param.DataType,
                                            Value = strValue
                                        });
                                        continue;
                                    }
                                }

                                output.Add(new DriverReturnValueModel
                                {
                                    Id = param.Id,
                                    VariableStatus = VariableStatusTypeEnum.MethodError,
                                    Message = $"偏移量超出范围: 相对字节偏移={relativeByteOffset}, 长度={byteLength}, 数据长度={read.Content.Length}",
                                    DataType = param.DataType,
                                    Value = null
                                });
                                continue;
                            }

                            // 解析数据
                            object value = ParseValue(param, read.Content, relativeByteOffset);

                            output.Add(new DriverReturnValueModel
                            {
                                Id = param.Id,
                                VariableStatus = VariableStatusTypeEnum.Good,
                                Message = read.Message,
                                DataType = param.DataType,
                                Value = value
                            });
                        }
                        catch (Exception ex)
                        {
                            output.Add(new DriverReturnValueModel
                            {
                                Id = param.Id,
                                VariableStatus = VariableStatusTypeEnum.MethodError,
                                Message = $"解析异常: {ex.Message}",
                                DataType = param.DataType,
                                Value = null
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 批量读取过程中发生异常
                _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【批量读取异常】分组 #{groupIndex + 1}: {ex.Message}", DriverInfo.DeviceId);

                // 构建简单的地址范围字符串用于日志
                string addressRange = $"分组 #{groupIndex + 1}";
                // 使用LogNet记录异常
                if (Driver.LogNet != null)
                {
                    Driver.LogNet.WriteError("Modbus", $"{addressRange} 查询失败，返回报错信息：{ex.Message}");
                }

                output.AddRange(group.Select(param => new DriverReturnValueModel
                {
                    Id = param.Id,
                    VariableStatus = VariableStatusTypeEnum.Error,
                    Message = $"读取异常: {ex.Message}",
                    DataType = param.DataType
                }));
            }
        }

        // 输出总请求次数
        _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【请求统计】共发送 {readRequestCount} 次读取请求", DriverInfo.DeviceId);

        return output;
    }

    /// <summary>
    /// 批量读取方法，统一处理所有类型的地址
    /// </summary>
    /// <returns>读取结果列表</returns>
    [Method("Batch", description: "批量读取", name: "BatchRead")]
    public async Task<List<DriverReturnValueModel>> BatchRead()
    {
        List<DriverReturnValueModel> resData = new();
        _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【开始批量读取】配置参数：最大查询长度={MaxQueryLength}，地址间隔阈值={PacketInterval}", DriverInfo.DeviceId);

        try
        {
            // 准备所有需要读取的地址
            List<DriverAddressIoArgModel> allAddresses = new List<DriverAddressIoArgModel>();

            // 合并所有分组的地址
            foreach (var group in _groupAddress)
            {
                allAddresses.AddRange(group.Address);
            }

            // 添加其他单独处理的地址
            allAddresses.AddRange(_otherAddress);

            _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【地址统计】总共需要读取 {allAddresses.Count} 个地址", DriverInfo.DeviceId);

            // 统一处理所有参数
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var results = await ReadParameters(allAddresses);
            stopwatch.Stop();

            // 统计成功和失败的数量
            int successCount = results.Count(r => r.VariableStatus == VariableStatusTypeEnum.Good);
            int errorCount = results.Count - successCount;

            _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【批量读取完成】总耗时: {stopwatch.ElapsedMilliseconds}ms, 成功: {successCount}, 失败: {errorCount}", DriverInfo.DeviceId);

            // 使用LogNet记录轮询完成消息
            if (Driver.LogNet != null)
            {
                Driver.LogNet.WriteInfo("Modbus", $"轮询结束，总耗时：{stopwatch.ElapsedMilliseconds}ms");
            }

            resData.AddRange(results);
        }
        catch (Exception ex)
        {
            _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 【批量读取异常】{ex.Message}", DriverInfo.DeviceId);

            // 使用LogNet记录异常
            if (Driver.LogNet != null)
            {
                Driver.LogNet.WriteError("Modbus", $"批量读取异常：{ex.Message}");
            }
        }

        return resData;
    }

    #endregion 批量读取

    #region Methods

    /// <summary>
    ///     功能码:01
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("0x01", description: "读取线圈", name: "01:ReadCoil")]
    public async Task<DriverReturnValueModel> ReadCoil(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     功能码:02
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("0x02", description: "读输入", name: "02:ReadInput")]
    public async Task<DriverReturnValueModel> ReadInput(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     功能码:03
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("0x03;", description: "读保持寄存器", name: "03:ReadHoldingRegisters")]
    public async Task<DriverReturnValueModel> ReadHoldingRegisters(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     功能码:04
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("0x04", description: "读输入寄存器", name: "04:ReadInputRegisters")]
    public async Task<DriverReturnValueModel> ReadInputRegisters(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    #endregion Methods
}

/// <summary>
///     Modbus网口读取基类
/// </summary>
public class ModBusNetworkBase : ModBusBase
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 502;

    #endregion
}

/// <summary>
///     Modbus串口基类
/// </summary>
public class ModBusSerialBase : ModBusBase
{
    /// <summary>
    ///     断开连接
    /// </summary>
    /// <returns></returns>
    public override void Close()
    {
        // Driver?.Close();
    }

    /// <summary>
    ///     释放对象
    /// </summary>
    public override void Dispose()
    {
        // Driver?.Dispose();
    }

    #region 配置参数

    [ConfigParameter("串口号", Remark = "")]
    public SerialNumberEnum SerialNumber { get; set; }

    [ConfigParameter("波特率")] public BaudRateEnum BaudRate { get; set; } = BaudRateEnum.Bps9600;
    [ConfigParameter("数据位")] public int DataBits { get; set; } = 8;
    [ConfigParameter("停止位")] public StopBits Stop { get; set; } = StopBits.One;
    [ConfigParameter("校验位")] public Parity Checkout { get; set; } = Parity.None;

    [ConfigParameter("异步读取", GroupName = "高级配置", Remark = "同串口接入多个设备，建议关闭异步读取")]
    public BoolEnum IsAsync { get; set; } = BoolEnum.True;

    #endregion
}

/// <summary>
///     modbus批量读取分类地址
/// </summary>
public class GroupAddress
{
    /// <summary>
    ///     地址列表
    /// </summary>
    public List<DriverAddressIoArgModel> Address { get; set; } = new();

    /// <summary>
    ///     存储区域
    /// </summary>
    public string StorageArea { get; set; }

    /// <summary>
    ///     最小地址
    /// </summary>
    public int MinNumber { get; set; }

    /// <summary>
    ///     最大地址
    /// </summary>
    public int Max { get; set; }

    /// <summary>
    ///     数字列表
    /// </summary>
    public List<int> NumberList { get; set; } = new();

    /// <summary>
    /// 地址前缀
    /// </summary>
    public string Prefix { get; set; } = string.Empty;
}