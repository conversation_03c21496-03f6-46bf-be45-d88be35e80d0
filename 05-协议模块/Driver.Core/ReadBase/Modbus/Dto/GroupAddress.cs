namespace Driver.Core.ReadBase.Modbus.Dto;

/// <summary>
///     批量读取地址基类
/// </summary>
public class GroupAddress
{
    /// <summary>
    ///     分类后的地址集合
    /// </summary>
    public List<DriverAddressIoArgModel> Address { get; set; } = new();

    /// <summary>
    ///     寄存区地址
    /// </summary>
    public string StorageArea { get; set; }

    /// <summary>
    ///     最大地址
    /// </summary>
    public int Max { get; set; }

    /// <summary>
    ///     最小地址
    /// </summary>
    public int MinNumber { get; set; }

    /// <summary>
    ///     地址解析成数字后集合
    /// </summary>
    public List<int> NumberList { get; set; } = new();

    /// <summary>
    ///     是否是读数字
    /// </summary>
    public bool ReadNumber { get; set; }

    /// <summary>
    ///     地址前缀，如 "S=1;" 或 "X=3;"
    /// </summary>
    public string Prefix { get; set; } = string.Empty;
}

public class GroupAddressEx
{
    /// <summary>
    ///     分类后的地址集合
    /// </summary>
    public List<DriverAddressIoArgModel> Address { get; set; } = new();

    /// <summary>
    ///     寄存区地址
    /// </summary>
    public string StorageArea { get; set; }

    /// <summary>
    ///     标准地址集合
    /// </summary>
    public NumberTagEx NumberTag { get; set; } = new();

    /// <summary>
    ///     非标地址集合
    /// </summary>
    public OtherNumberTagEx OtherNumberTag { get; set; } = new();
}


// 标准地址集合
public class NumberTagEx
{
    /// <summary>
    /// 最大值
    /// </summary>
    public int Max { get; set; }

    /// <summary>
    /// 最小值
    /// </summary>
    public int Min { get; set; }

    /// <summary>
    ///     标准地址集合
    /// </summary>
    public List<int> NumberList { get; set; } = new();
}

public class OtherNumberTagEx
{
    /// <summary>
    /// 最大值
    /// </summary>
    public decimal Max { get; set; }

    /// <summary>
    /// 最小值
    /// </summary>
    public decimal Min { get; set; }

    /// <summary>
    ///     非标地址集合
    /// </summary>
    public List<decimal> OtherNumberList { get; set; } = new();

}