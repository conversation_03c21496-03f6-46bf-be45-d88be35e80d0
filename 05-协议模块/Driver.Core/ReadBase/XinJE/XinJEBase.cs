using Common.Enums;
using Common.Extension;
using HslCommunication.Profinet.XINJE;

namespace Driver.Core.ReadBase.XinJE;

/// <summary>
///     信捷协议
/// </summary>
public class XinJeBase : BasePlcProtocolCollector
{
    [ConfigParameter("批量读取",GroupName = "高级配置",Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;
    
    [ConfigParameter("站号")] public byte Station { get; set; } = 1;
    [ConfigParameter("解析类型")] public DataFormat DataFormat { get; set; } = DataFormat.ABCD;
    [ConfigParameter("大小端转换")] public BoolEnum ByteTransform { get; set; } = BoolEnum.False;
    [ConfigParameter("字符串颠倒")] public BoolEnum StringReverse { get; set; } = BoolEnum.False;
    [ConfigParameter("系列")] public XinJESeries Series { get; set; } = XinJESeries.XC;
    
    // [ConfigParameter("系列型号")] public SeriesModelEnum SeriesModel { get; set; }
    //
    // public enum SeriesModelEnum
    // {
    //     //XC1/XC2/XC3/XC5/XCM/XCC
    //     Bps1200 = 0, 
    // }

    /// <summary>
    ///     解析地址
    /// </summary>
    /// <param name="registerAddress"></param>
    /// <param name="dataType">数据类型</param>
    /// <returns></returns>
    [Method("Parse", description: "用于解析XinJE的标准地址", name: "Parse")]
    public string Parse(string registerAddress, DataTypeEnum dataType)
    {
        if (registerAddress.IsNull()) return "Read";

        if (registerAddress.StartsWith("M"))
            return "M:内部继电器";

        return "Read";
    }

    #region Methods

    #region XC系列

    /// <summary>
    ///     M:内部继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("M", description: "	M0-M7999，M8000-M8511", name: "M:内部继电器",filter:true,filterField:"Series",filterValue:"0")]
    public async Task<DriverReturnValueModel> ReadM(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     S:流程继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("S", description: "S0-S1023", name: "S:流程继电器",filter:true,filterField:"Series",filterValue:"0")]
    public async Task<DriverReturnValueModel> ReadS(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     T:定时器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("T", description: "T0-T618", name: "T:定时器",filter:true,filterField:"Series",filterValue:"0")]
    public async Task<DriverReturnValueModel> ReadT(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     C:计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C", description: "C0-C634", name: "C:计数器",filter:true,filterField:"Series",filterValue:"0")]
    public async Task<DriverReturnValueModel> ReadC(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     X:输入
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("X", description: "X0-X1037 或者X0.0-X103.7", name: "X:输入",filter:true,filterField:"Series",filterValue:"0")]
    public async Task<DriverReturnValueModel> ReadX(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Y:输出
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Y", description: "Y0-Y1037 或者Y0.0-Y103.7", name: "Y:输出",filter:true,filterField:"Series",filterValue:"0")]
    public async Task<DriverReturnValueModel> ReadY(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     D:数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("D", description: "	D0-D8511", name: "D:数据寄存器",filter:true,filterField:"Series",filterValue:"0")]
    public async Task<DriverReturnValueModel> ReadXcD(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     F:Flash寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("F", description: "F0-F5000;F8000-F8511", name: "F:Flash寄存器",filter:true,filterField:"Series",filterValue:"0")]
    public async Task<DriverReturnValueModel> ReadXcF(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     E:扩展内部寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("E", description: "E0-E36863", name: "E:扩展内部寄存器",filter:true,filterField:"Series",filterValue:"0")]
    public async Task<DriverReturnValueModel> ReadXcE(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    /// <summary>
    ///     T:定时器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("T", description: "	T0-T618", name: "T:定时器",filter:true,filterField:"Series",filterValue:"0")]
    public async Task<DriverReturnValueModel> ReadXcT(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     C:计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C", description: "C0-C634", name: "C:计数器",filter:true,filterField:"Series",filterValue:"0")]
    public async Task<DriverReturnValueModel> ReadXcC(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    #endregion

    #region XD,XL

    /// <summary>
    ///     M:中间寄电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("M", description: "M0-M7999", name: "M:中间寄电器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdM(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     X:输入
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("X", description: "X0-X77,X10000-X11177,X20000-X20177,X30000-X30077 或者X0.0-X37.7", name: "X:输入",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdX(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    /// <summary>
    ///     Y:输出
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Y", description: "Y0-Y77,Y10000-Y11177,Y20000-Y20177,Y30000-Y30077 或者Y0.0-Y37.7", name: "Y:输出",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdY(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     S:输出
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("S", description: "S0-S1023", name: "S:输出",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdS(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     SM:输出
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SM", description: "SM0-SM2047", name: "SM:输出",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdSm(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     T:定时器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("T", description: "T0-T575", name: "T:定时器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdT(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     C计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C", description: "C0-C575", name: "C:计数器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdC(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     ET计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("ET", description: "ET0-ET31", name: "ET:计数器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdEt(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///    SEM计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SEM", description: "SEM0-SEM31", name: "SEM:计数器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdSem(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     HM:计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("HM", description: "HM0-HM959", name: "HM:计数器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdHm(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     HS 计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("HS", description: "HS0-HS127", name: "HS:计数器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdHs(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     HT 计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("HT", description: "HT0-HT95", name: "HT:计数器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdHt(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Hc 计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Hc", description: "HT0-HC95", name: "Hc:计数器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdHc(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     HSC 计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("HSC", description: "HST0-HSC31", name: "HSC:计数器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdHsc(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     D 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("D", description: "D0-D7999", name: "D:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdD(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     ID 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("ID", description: "ID0-ID99，ID10000-ID10999，ID20000-ID20199,ID30000-ID30099", name: "ID:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdId(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    /// <summary>
    ///     QD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("QD", description: "QD0-QD99，QD10000-QD10999，QD20000-QD20199,QD30000-QD30099", name: "QD:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdQd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    /// <summary>
    ///     SD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SD", description: "SD0-SD2047", name: "SD:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdSd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    /// <summary>
    ///     TD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TD", description: "TD0-TD575", name: "TD:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdTd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     CD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CD", description: "CD0-CD575", name: "CD:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdCd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    /// <summary>
    ///     ETD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("ETD", description: "ETD0-ETD31", name: "ETD:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdEtd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     HD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("HD", description: "HD0-HD999", name: "HD:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdHd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     HSD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("HSD", description: "HSD0-HSD499", name: "HSD:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdHsd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     HTD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("HTD", description: "HTD0-HTD95", name: "HTD:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdHtd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     HD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("HCD", description: "HCD0-HCD95", name: "HCD:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdHcd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     HSCD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("HSCD", description: "HSCD0-HSCD31", name: "HSCD:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdHscd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    /// <summary>
    ///     FD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("FD", description: "FD0-FD5119", name: "FD:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdFd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     SFD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SFD", description: "SFD0-SFD1999", name: "SFD:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdSfd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    /// <summary>
    ///     FSD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("FSD", description: "FS0-FS47", name: "FSD:数据寄存器",filter:true,filterField:"Series",filterValue:"1,2")]
    public async Task<DriverReturnValueModel> ReadXdFsd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    #endregion XD,XL
    
    //XD5、 XDM、 XDC、 XD5E、 XDME、 XL5、 XL5E、 XLME 
    
    // /// <summary>
    // ///     D:数据寄存器
    // /// </summary>
    // /// <param name="val"></param>
    // /// <returns></returns>
    // [Method("D", description: "D0-D7999", name: "D:数据寄存器")]
    // public async Task<DriverReturnValueModel> ReadXcD(DriverAddressIoArgModel val)
    // {
    //     return await Read(val);
    // }
    
    // /// <summary>
    // ///     HD:数据寄存器
    // /// </summary>
    // /// <param name="val"></param>
    // /// <returns></returns>
    // [Method("HD", description: "HD0-HD999", name: "HD:数据寄存器")]
    // public async Task<DriverReturnValueModel> ReadHd(DriverAddressIoArgModel val)
    // {
    //     return await Read(val);
    // }
    
   

    #endregion Methods
}

/// <summary>
///     信捷网口协议基类
/// </summary>
public class XinJeNetworkDeviceBase : XinJeBase
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 102;

    #endregion
}

/// <summary>
///     串口协信捷议基类
/// </summary>
public class XinJeSerialDeviceBase : XinJeBase
{
    public override void Close()
    {
        Driver?.Close();
    }

    public override void Dispose()
    {
        Driver?.Dispose();
    }

    #region 配置参数

    [ConfigParameter("串口号", Remark = "")]
    public SerialNumberEnum SerialNumber { get; set; } = SerialNumberEnum.ttyS1;
    [ConfigParameter("波特率")] public BaudRateEnum BaudRate { get; set; } = BaudRateEnum.Bps9600;
    [ConfigParameter("数据位")] public int DataBits { get; set; } = 8;
    [ConfigParameter("停止位")] public StopBits Stop { get; set; } = StopBits.Two;
    [ConfigParameter("校验位")] public Parity Checkout { get; set; } = Parity.Even;

    #endregion
}