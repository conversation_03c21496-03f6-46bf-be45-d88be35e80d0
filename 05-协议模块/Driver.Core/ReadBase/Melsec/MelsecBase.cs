using Common.Enums;
using Common.Extension;

namespace Driver.Core.ReadBase.Melsec;

/// <summary>
///     三菱
/// </summary>
public class MelsecBase : BasePlcProtocolCollector
{
    /// <summary>
    ///     字符串和无法解析地址单独读取
    /// </summary>
    private readonly List<DriverAddressIoArgModel> _otherAddress = new();

    /// <summary>
    ///     解析地址
    /// </summary>
    private readonly List<GroupAddressEx> _groupAddress = new();

    /// <summary>
    ///     批量读取失败的地址
    /// </summary>
    private readonly List<DriverAddressIoArgModel> _batchReadFailed = new();

    /// <summary>
    ///     解析地址
    /// </summary>
    /// <param name="registerAddress"></param>
    /// <param name="dataType">数据类型</param>
    /// <returns></returns>
    [Method("Parse", description: "用于解析Melsec的标准地址", name: "Parse")]
    public string Parse(string registerAddress, DataTypeEnum dataType)
    {
        if (registerAddress.IsNull()) return "Read";
        foreach (var mapping in _storageAreaPrefix.Where(mapping => registerAddress.StartsWith(mapping.Key))) return mapping.Value;
        return "Read";
    }

    /// <summary>
    ///     地址区域前缀标识
    /// </summary>
    private readonly Dictionary<string, string> _storageAreaPrefix = new()
    {
        {"SM", "SM"},
        {"SB", "SB"},
        {"DX", "DX"},
        {"DY", "DY"},
        {"TS", "TS"},
        {"TC", "TC"},
        {"SS", "SS"},
        {"SC", "SC"},
        {"CS", "CS"},
        {"CC", "CC"},
        {"SD", "SD"},
        {"SW", "SW"},
        {"ZR", "ZR"},
        {"TN", "TN"},
        {"SN", "SN"},
        {"CN", "CN"},
        {"M", "M"},
        {"X", "X"},
        {"Y", "Y"},
        {"S", "S"},
        {"L", "L"},
        {"F", "F"},
        {"V", "V"},
        {"B", "B"},
        {"D", "D"},
        {"W", "W"},
        {"R", "R"},
        {"Z", "Z"}
    };

    /// <summary>
    ///     地址分组,只初始化一次
    /// </summary>
    /// <param name="groupVal"></param>
    [Method("Init", description: "地址初始化", name: "Init")]
    public bool InitGroup(List<IGrouping<string, DriverAddressIoArgModel>> groupVal)
    {
        try
        {
            _groupAddress.Clear();
            _otherAddress.Clear();

            foreach (var group in groupVal)
            {
                //
                var groupAddresses = new GroupAddressEx();
                foreach (var tag in group)
                    try
                    {
                        #region 区域解析

                        var address = tag.Address.ToUpper();
                        foreach (var (key, val) in _storageAreaPrefix)
                            if (address.StartsWith(key, StringComparison.CurrentCultureIgnoreCase))
                            {
                                groupAddresses.StorageArea = val;
                                address = address.Replace(val, "");
                                break;
                            }

                        #endregion

                        // 特殊地址如：Y00,X00，CN开头的无法走批量
                        if (tag.DataType.HasFlag(DataTypeEnum.String) || (address.StartsWith("0") && address.Length > 1) || groupAddresses.StorageArea == "CN")
                        {
                            _otherAddress.Add(tag);
                        }
                        else
                        {
                            if (address.IndexOf(".", StringComparison.Ordinal) > 0)
                            {
                                // address = address.Split(".", StringSplitOptions.RemoveEmptyEntries)[1];
                                groupAddresses.Address.Add(tag);
                                groupAddresses.OtherNumberTag.OtherNumberList.Add(Convert.ToDecimal(address));
                            }
                            else
                            {
                                groupAddresses.Address.Add(tag);
                                groupAddresses.NumberTag.NumberList.Add(Convert.ToInt32(address));
                            }
                        }
                    }
                    catch
                    {
                        _otherAddress.Add(tag);
                        _ = DriverInfo.Socket.DeviceConsole($"【Melsec】 地址解析失败:【{tag.Address}】", DriverInfo.DeviceId);
                    }

                if (groupAddresses.NumberTag.NumberList.Any())
                {
                    groupAddresses.NumberTag.Max = groupAddresses.NumberTag.NumberList.Max();
                    groupAddresses.NumberTag.Min = groupAddresses.NumberTag.NumberList.Min();
                }

                if (groupAddresses.OtherNumberTag.OtherNumberList.Any())
                {
                    groupAddresses.OtherNumberTag.Max = groupAddresses.OtherNumberTag.OtherNumberList.Max();
                    groupAddresses.OtherNumberTag.Min = groupAddresses.OtherNumberTag.OtherNumberList.Min();
                }

                _groupAddress.Add(groupAddresses);
            }
        }
        catch (Exception ex)
        {
            _ = DriverInfo.Socket.DeviceConsole($"【Melsec】 批量读取解析地址失败:【{ex.Message}】", DriverInfo.DeviceId);
            return false;
        }

        return true;
    }

    #region 批量读取

    /// <summary>
    ///     批量读取数据的函数。
    ///     该方法首先尝试批量读取数字输入和 coil 状态，然后尝试读取其他地址的数据。
    /// </summary>
    /// <returns>
    ///     返回一个包含所有读取操作结果的列表<see cref="DriverReturnValueModel" />。
    ///     每个结果代表一个读取操作的成功或失败及其相关信息。
    /// </returns>
    /// <exception cref="ArgumentOutOfRangeException">
    ///     如果传入的地址超出有效范围，可能会抛出此异常。
    /// </exception>
    [Method("Batch", description: "批量读取", name: "BatchRead")]
    public async Task<List<DriverReturnValueModel>> BatchRead()
    {
        // 初始化结果数据列表
        var resultData = new List<DriverReturnValueModel>();

        // 遍历组地址，尝试批量读取数字输入和 coil 状态
        foreach (var groupAddress in _groupAddress)
            try
            {
                // 批量读取数字输入
                resultData.AddRange(await BatchReadNumber(groupAddress));
                // 批量读取 coil 状态
                resultData.AddRange(await BatchReadCoil(groupAddress));
            }
            catch (Exception ex)
            {
                // 处理批量读取过程中发生的异常
                await HandleBatchReadException(ex);
            }

        // 处理批量读取地址失败的情况
        await HandleFailedBatchReadAddresses();

        // 遍历其他地址，尝试读取数据
        foreach (var address in _otherAddress)
            try
            {
                // 读取单个地址的数据
                resultData.Add(await Read(address));
            }
            catch (Exception ex)
            {
                // 处理单个读取操作过程中发生的异常
                await HandleSingleReadException(ex);
            }

        // 返回读取操作的结果列表
        return resultData;
    }

    /// <summary>
    ///     批量读取过程中发生的异常
    /// </summary>
    /// <param name="ex"></param>
    private async Task HandleBatchReadException(Exception ex)
    {
        _ = DriverInfo.Socket.DeviceConsole($"【Melsec】 批量读取失败,Error:{ex.Message}", DriverInfo.DeviceId);
    }

    /// <summary>
    ///     批量读取失败的地址，放到单个读取
    /// </summary>
    private async Task HandleFailedBatchReadAddresses()
    {
        if (_batchReadFailed.Any())
        {
            foreach (var group in _groupAddress)
            {
                var failedIds = new HashSet<long>(_batchReadFailed.Select(item => item.Id));
                group.Address.RemoveAll(item => failedIds.Contains(item.Id));
            }

            _otherAddress.AddRange(_batchReadFailed);
            _batchReadFailed.Clear();
        }
    }

    private async Task HandleSingleReadException(Exception ex)
    {
        _ = DriverInfo.Socket.DeviceConsole($"【Melsec】 读取失败,Error:{ex.Message}", DriverInfo.DeviceId);
    }

    #region 读Number

    /// <summary>
    ///     读Number
    /// </summary>
    /// <param name="groupAddress"></param>
    protected virtual async Task<List<DriverReturnValueModel>> BatchReadNumber(GroupAddressEx groupAddress)
    {
        try
        {
            // 处理原始数据
            var addressDataMap = NumberGroup(groupAddress);
            List<DriverReturnValueModel> resultData = new();
            foreach (var (_, values) in addressDataMap)
            {
                // 当前组里最小的值
                var minNumberInGroup = values.Min();
                // 当前组里最大的值
                var maxNumberInGroup = values.Max();
                var readValue = await ReadGroupAddressValuesAsync(groupAddress, minNumberInGroup, maxNumberInGroup);
                if (readValue.IsSuccess)
                    resultData.AddRange(ProcessReadValues(groupAddress, values, minNumberInGroup, readValue.Content));
                else
                    resultData.AddRange(HandleReadFailure(groupAddress, readValue.Message));
            }

            return resultData;
        }
        catch (Exception ex)
        {
            throw Oops.Bah($"【Melsec】 批量读Number失败,错误信息:{ex.Message}");
        }
    }

    private async Task<dynamic> ReadGroupAddressValuesAsync(GroupAddressEx groupAddress, int minNumber, int maxNumber)
    {
        var startAddress = groupAddress.StorageArea + minNumber;
        var length = Convert.ToUInt16(maxNumber - minNumber + 3);
        return await Driver.ReadAsync(startAddress, length);
    }

    private List<DriverReturnValueModel> ProcessReadValues(GroupAddressEx groupAddress, List<int> values, int minNumberInGroup, byte[] readContent)
    {
        var result = new List<DriverReturnValueModel>();
        foreach (var address in groupAddress.Address)
        {
            var normalizedAddress = NormalizeAddress(address, groupAddress.StorageArea);
            if (!values.Contains((int) normalizedAddress)) continue;

            var ret = new DriverReturnValueModel
            {
                DataType = address.DataType,
                Id = address.Id
            };

            try
            {
                ret.Value = ConvertAddressValue(address.DataType, readContent, (int) (normalizedAddress - minNumberInGroup));
                result.Add(ret);
            }
            catch (Exception ex)
            {
                result.AddRange(HandleValueProcessingFailure(address, ex.Message));
            }
        }

        return result;
    }

    private decimal NormalizeAddress(DriverAddressIoArgModel address, string storageArea)
    {
        var addrStr = address.Address.ToUpper().Replace(storageArea, "").Trim();
        if (addrStr.IsNumber()) return Convert.ToDecimal(addrStr);
        return -1;
    }

    /// <summary>
    ///     number
    /// </summary>
    /// <param name="dataType"></param>
    /// <param name="readContent"></param>
    /// <param name="offset"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private object ConvertAddressValue(DataTypeEnum dataType, byte[] readContent, int offset)
    {
        if (Driver == null)
            throw new Exception("协议未初始化！");
        return dataType switch
        {
            DataTypeEnum.Bit => Driver.ByteTransform.TransBool(readContent, offset * 2) ? 1 : 0,
            DataTypeEnum.Bool => Driver.ByteTransform.TransBool(readContent, offset * 2),
            DataTypeEnum.Uint16 => Driver.ByteTransform.TransUInt16(readContent, offset * 2),
            DataTypeEnum.Int16 => Driver.ByteTransform.TransInt16(readContent, offset * 2),
            DataTypeEnum.Uint32 => Driver.ByteTransform.TransUInt32(readContent, offset * 2),
            DataTypeEnum.Int32 => Driver.ByteTransform.TransInt32(readContent, offset * 2),
            DataTypeEnum.Float => Driver.ByteTransform.TransSingle(readContent, offset * 2),
            DataTypeEnum.Uint64 => Driver.ByteTransform.TransUInt64(readContent, offset * 2),
            DataTypeEnum.Int64 => Driver.ByteTransform.TransInt32(readContent, offset * 2),
            DataTypeEnum.Double => Driver.ByteTransform.TransDouble(readContent, offset * 2),
            _ => throw new Exception("数据类型错误,无法解析！")
        };
    }

    #endregion

    #region 读BOOL

    /// <summary>
    ///     读取Coil
    /// </summary>
    /// <param name="groupAddress">组地址</param>
    protected virtual async Task<List<DriverReturnValueModel>> BatchReadCoil(GroupAddressEx groupAddress)
    {
        try
        {
            var addressDataMap = OtherNumberGroup(groupAddress);
            var resultData = new List<DriverReturnValueModel>();

            foreach (var (_, values) in addressDataMap)
            {
                var minNumberInGroup = values.Min();
                var maxNumberInGroup = values.Max();
                var readValue = await ReadCoilValuesAsync(groupAddress, minNumberInGroup, maxNumberInGroup);
                resultData.AddRange(readValue.IsSuccess
                    ? (IEnumerable<DriverReturnValueModel>) ProcessCoilValues(groupAddress, values, minNumberInGroup, readValue.Content)
                    : (IEnumerable<DriverReturnValueModel>) HandleReadFailure(groupAddress, readValue.Message));
            }

            return resultData;
        }
        catch (Exception ex)
        {
            throw Oops.Bah($"【Melsec】 批量读线圈地址失败,错误信息:{ex.Message}");
        }
    }

    private async Task<dynamic> ReadCoilValuesAsync(GroupAddressEx groupAddress, decimal minNumber, decimal maxNumber)
    {
        var startAddress = groupAddress.StorageArea + minNumber;
        ushort length = 0;
        if (minNumber.ToString().IsInt())
            length = Convert.ToUInt16(maxNumber - minNumber + 3);
        else
            length = (ushort) (Convert.ToUInt16(maxNumber - minNumber) * 16);
        return await Driver.ReadBoolAsync(startAddress, length);
    }

    private List<DriverReturnValueModel> ProcessCoilValues(GroupAddressEx groupAddress, List<decimal> values, decimal minNumberInGroup, bool[] readContent)
    {
        var result = new List<DriverReturnValueModel>();
        foreach (var address in groupAddress.Address)
        {
            var normalizedAddress = NormalizeAddress(address, groupAddress.StorageArea);
            if (!values.Contains(normalizedAddress)) continue;

            var ret = new DriverReturnValueModel {DataType = address.DataType, Id = address.Id};

            try
            {
                // 假设2014.0 开始-  读地址应该是 结束地址*16 - 开始地址*16
                var thisAddressNumber = 0;
                if (normalizedAddress.ToString().IsInt())
                {
                    thisAddressNumber = (int) (normalizedAddress - minNumberInGroup);
                }
                else
                {
                    thisAddressNumber = (int) (normalizedAddress - minNumberInGroup) * 16;
                    // 取尾数 *10 
                    var endNumber = (int) (Math.Abs(normalizedAddress - Math.Truncate(normalizedAddress)) * 10);
                    thisAddressNumber += endNumber;
                }

                ret.Value = ConvertCoilValue(address.DataType, readContent, thisAddressNumber);
                result.Add(ret);
            }
            catch (Exception ex)
            {
                result.AddRange(HandleValueProcessingFailure(address, ex.Message));
            }
        }

        return result;
    }

    private object ConvertCoilValue(DataTypeEnum dataType, bool[] readContent, int offset)
    {
        return dataType switch
        {
            DataTypeEnum.Bit => readContent[offset] ? 1 : 0,
            DataTypeEnum.Bool => readContent[offset],
            _ => throw new Exception("数据类型错误,无法解析！")
        };
    }

    #endregion

    #region 私有方法

    private List<DriverReturnValueModel> HandleValueProcessingFailure(DriverAddressIoArgModel address, string errorMessage)
    {
        _batchReadFailed.Add(address);
        var ret = new DriverReturnValueModel
        {
            VariableStatus = VariableStatusTypeEnum.MethodError,
            Message = $"【Melsec】 批量读取解析失败, 错误信息: {errorMessage}"
        };
        return new List<DriverReturnValueModel> {ret};
    }

    private List<DriverReturnValueModel> HandleReadFailure(GroupAddressEx groupAddress, string errorMessage)
    {
        return groupAddress.Address.Select(address => new DriverReturnValueModel
        {
            VariableStatus = VariableStatusTypeEnum.Error,
            DataType = address.DataType,
            Id = address.Id,
            Message = errorMessage,
            ErrorCode = -1
        }).ToList();
    }

    #endregion

    /// <summary>
    ///     切割原始数据
    /// </summary>
    /// <param name="groupAddress"></param>
    /// <returns></returns>
    private Dictionary<int, List<int>> NumberGroup(GroupAddressEx groupAddress)
    {
        // 最大分割数
        const int maximumSplitCount = 256;
        var dicNumber = new Dictionary<int, List<int>>();
        var max = groupAddress.NumberTag.Max;
        var min = groupAddress.NumberTag.Min;
        var numberList = groupAddress.NumberTag.NumberList;
        // 最大和最小地址在同一范围内,就不需要切割
        if (max - min <= maximumSplitCount)
        {
            dicNumber.Add(0, numberList);
            return dicNumber;
        }

        // (最大值-最小值) / 最大分割数 = 整体切割读取次数
        var splitCount = (int) Math.Ceiling((max - min) / (double) maximumSplitCount);
        for (var i = 0; i <= splitCount; i++)
        {
            // 起始地址小于最小值，就将循环次数+1
            if (i * maximumSplitCount < min)
                splitCount++;
            // 循环将范围内的数据丢到对应的数据组里
            var numbers = numberList.Where(w => w >= i * maximumSplitCount && w < (i + 1) * maximumSplitCount).ToList();
            if (numbers.Count != 0)
                dicNumber.Add(i, numbers);
        }

        return dicNumber;
    }


    /// <summary>
    ///     切割原始数据
    /// </summary>
    /// <param name="groupAddress"></param>
    /// <returns></returns>
    private Dictionary<int, List<decimal>> OtherNumberGroup(GroupAddressEx groupAddress)
    {
        //最大分割数
        const int maximumSplitCount = 256;
        var dicNumber = new Dictionary<int, List<decimal>>();
        var max = groupAddress.OtherNumberTag.Max;
        var min = groupAddress.OtherNumberTag.Min;
        var numberList = groupAddress.OtherNumberTag.OtherNumberList;
        // 最大和最小地址在同一范围内,就不需要切割
        if (max - min <= maximumSplitCount)
        {
            dicNumber.Add(0, numberList);
            return dicNumber;
        }

        // (最大值-最小值) / 最大分割数 = 整体切割读取次数
        var splitCount = (int) Math.Ceiling((max - min) / maximumSplitCount);
        for (var i = 0; i <= splitCount; i++)
        {
            // 起始地址小于最小值，就将循环次数+1
            if (i * maximumSplitCount < min)
                splitCount++;
            // 循环将范围内的数据丢到对应的数据组里
            var numbers = numberList.Where(w => w >= i * maximumSplitCount && w < (i + 1) * maximumSplitCount).ToList();
            if (numbers.Count != 0)
                dicNumber.Add(i, numbers);
        }

        return dicNumber;
    }

    #endregion 批量读取

    #region Methods

    /// <summary>
    ///     M:内部继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("M", description: "", name: "M:内部继电器")]
    public async Task<DriverReturnValueModel> M(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     X:输入继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("X", description: "默认16进制,8进制 X011", name: "X:输入继电器")]
    public async Task<DriverReturnValueModel> X(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Y:输出继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Y", description: "默认16进制,8进制 X011", name: "Y:输出继电器")]
    public async Task<DriverReturnValueModel> Y(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SM:SM特殊继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("I0", description: "", name: "SM:SM特殊继电器")]
    public async Task<DriverReturnValueModel> SM(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     S:步进继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("S", description: "", name: "S:步进继电器")]
    public async Task<DriverReturnValueModel> S(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     L:锁存继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("L", description: "", name: "L:锁存继电器")]
    public async Task<DriverReturnValueModel> L(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     F:报警器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("F", description: "", name: "F:报警器")]
    public async Task<DriverReturnValueModel> F(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     V:边沿继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("V", description: "", name: "V:边沿继电器")]
    public async Task<DriverReturnValueModel> V(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     B:链接继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("B", description: "16进制地址", name: "B:链接继电器")]
    public async Task<DriverReturnValueModel> B(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SB:特殊链接继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SB", description: "16进制地址", name: "SB:特殊链接继电器")]
    public async Task<DriverReturnValueModel> SB(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     DX:直接输入
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("DX", description: "", name: "DX:直接输入")]
    public async Task<DriverReturnValueModel> DX(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     DY:直接输出
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("DY", description: "", name: "DY:直接输出")]
    public async Task<DriverReturnValueModel> DY(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TS:定时器触点
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TS", description: "", name: "TS:定时器触点")]
    public async Task<DriverReturnValueModel> TS(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TC:定时器线圈
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TC", description: "", name: "TC:定时器线圈")]
    public async Task<DriverReturnValueModel> TC(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SS:累计定时器触点
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SS", description: "", name: "SS:累计定时器触点")]
    public async Task<DriverReturnValueModel> SS(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SC:累计定时器线圈
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SC", description: "", name: "SC:累计定时器线圈")]
    public async Task<DriverReturnValueModel> SC(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CS:计数器触点
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CS", description: "", name: "CS:计数器触点")]
    public async Task<DriverReturnValueModel> CS(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CC:计数器线圈
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CC", description: "", name: "CC:计数器线圈")]
    public async Task<DriverReturnValueModel> CC(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     D:数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("I0", description: "", name: "D:数据寄存器")]
    public async Task<DriverReturnValueModel> D(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SD:特殊数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SD", description: "", name: "SD:特殊数据寄存器")]
    public async Task<DriverReturnValueModel> SD(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     W:链接寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("W", description: "", name: "W:链接寄存器")]
    public async Task<DriverReturnValueModel> W(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SW:特殊链接寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SW", description: "", name: "SW:特殊链接寄存器")]
    public async Task<DriverReturnValueModel> SW(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     R:文件寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("R", description: "", name: "R:文件寄存器")]
    public async Task<DriverReturnValueModel> R(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Z:变址寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Z", description: "", name: "Z:变址寄存器")]
    public async Task<DriverReturnValueModel> Z(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     ZR:ZR文件寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("ZR", description: "", name: "ZR:ZR文件寄存器")]
    public async Task<DriverReturnValueModel> ZR(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TN:定时器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TN", description: "", name: "TN:定时器当前值")]
    public async Task<DriverReturnValueModel> TN(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SN:累计定时器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SN", description: "", name: "SN:累计定时器当前值")]
    public async Task<DriverReturnValueModel> SN(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CN:计数器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CN", description: "", name: "CN:计数器当前值")]
    public async Task<DriverReturnValueModel> CN(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    #endregion
}

/// <summary>
///     三菱网口协议基类
/// </summary>
public class MelsecNetworkDeviceBase : MelsecBase
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 6000;

    #endregion
}

/// <summary>
///     三菱串口协议基类
/// </summary>
public class MelseSerialDeviceBase : MelsecBase
{
    /// <summary>
    /// </summary>
    /// <returns></returns>
    public override void Close()
    {
        Driver?.Close();
    }

    /// <summary>
    /// </summary>
    public override void Dispose()
    {
        Driver?.Dispose();
    }

    #region 配置参数

    [ConfigParameter("串口号", Remark = "")] public SerialNumberEnum SerialNumber { get; set; } = SerialNumberEnum.ttyS1;

    [ConfigParameter("波特率")] public BaudRateEnum BaudRate { get; set; } = BaudRateEnum.Bps9600;
    [ConfigParameter("数据位")] public int DataBits { get; set; } = 8;

    [ConfigParameter("停止位")] public StopBits Stop { get; set; } = StopBits.Two;
    [ConfigParameter("校验位")] public Parity Checkout { get; set; } = Parity.None;

    #endregion
}

/// <summary>
///     三菱Udp协议基类
/// </summary>
public class MelseNetworkUdpDeviceBase : MelsecBase
{
    /// <summary>
    /// </summary>
    public override void Close()
    {
    }

    /// <summary>
    /// </summary>
    public override void Dispose()
    {
    }

    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 6000;

    #endregion
}