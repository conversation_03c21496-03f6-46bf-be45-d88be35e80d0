using System.Diagnostics;
using Common.Enums;
using Common.Extension;
using HslCommunication.BasicFramework;
using HslCommunication.Profinet.Omron;

namespace Driver.Core.ReadBase.Omron;

/// <summary>
///     欧姆龙协议
/// </summary>
public class OmronBase : BasePlcProtocolCollector
{
    /// <summary>
    ///     批量读取分类地址
    /// </summary>
    private readonly List<GroupAddressEx> _groupAddress = new();

    /// <summary>
    ///     字符串类地址单独读取
    /// </summary>
    private readonly List<DriverAddressIoArgModel> _otherAddress = new();

    /// <summary>
    ///     批量读取失败的地址
    /// </summary>
    private readonly List<DriverAddressIoArgModel> _batchReadFailed = new();

    /// <summary>
    /// 单次查询最大寄存器数量，0表示不限制
    /// </summary>
    [ConfigParameter("最大查询长度", GroupName = "高级配置", Remark = "单次查询最大寄存器数量，0表示不限制")]
    public int MaxQueryLength { get; set; } = 256;

    /// <summary>
    /// 地址间隔超过此值时分为不同组
    /// </summary>
    [ConfigParameter("地址间隔阈值", GroupName = "高级配置", Remark = "地址间隔超过此值时分为不同组")]
    public int PacketInterval { get; set; } = 0;


    /// <summary>
    ///     解析地址
    /// </summary>
    /// <param name="registerAddress"></param>
    /// <param name="dataType">数据类型</param>
    /// <returns></returns>
    [Method("Parse", description: "用于解析Omron的标准地址", name: "Parse")]
    public string Parse(string registerAddress, DataTypeEnum dataType)
    {
        if (registerAddress.IsNull()) return "Read";
        if (registerAddress.StartsWith("TIM"))
            return "TIM";
        if (registerAddress.StartsWith("CNT"))
            return "CNT";
        if (registerAddress.StartsWith("IR"))
            return "IR";
        if (registerAddress.StartsWith("DR"))
            return "DR";
        if (registerAddress.StartsWith("CF"))
            return "CF";
        if (registerAddress.StartsWith("D"))
            return "D";
        if (registerAddress.StartsWith("C"))
            return "C";
        if (registerAddress.StartsWith("W"))
            return "W";
        if (registerAddress.StartsWith("H"))
            return "H";
        if (registerAddress.StartsWith("A"))
            return "A";
        if (registerAddress.StartsWith("E"))
            return "E";

        return "Read";
    }

    /// <summary>
    /// 解析Omron地址，提取存储区、字地址和位索引
    /// </summary>
    /// <param name="address">地址字符串，如"C10.5"或"D100"</param>
    /// <returns>包含存储区、字地址和位索引的元组</returns>
    private (string StorageArea, int WordAddress, int BitIndex) ParseOmronAddress(string address)
    {
        try
        {
            if (string.IsNullOrEmpty(address))
                return ("", 0, -1);

            // 地址预处理
            var processedAddress = address.ToUpper().Trim();

            // EF替换
            if (processedAddress.StartsWith("EF"))
                processedAddress = processedAddress.Replace("EF", "E15");
            // CIO替换
            if (processedAddress.StartsWith("CIO"))
                processedAddress = processedAddress.Replace("CIO", "C");

            // 提取存储区域
            string storageArea = "";
            if (processedAddress.StartsWith("TIM"))
                storageArea = "TIM";
            else if (processedAddress.StartsWith("CNT"))
                storageArea = "CNT";
            else if (processedAddress.StartsWith("DR"))
                storageArea = "DR";
            else if (processedAddress.StartsWith("IR"))
                storageArea = "IR";
            else if (processedAddress.StartsWith("CF"))
                storageArea = "CF";
            else if (processedAddress.StartsWith("D"))
                storageArea = "D";
            else if (processedAddress.StartsWith("C"))
                storageArea = "C";
            else if (processedAddress.StartsWith("W"))
                storageArea = "W";
            else if (processedAddress.StartsWith("H"))
                storageArea = "H";
            else if (processedAddress.StartsWith("A"))
                storageArea = "A";
            else if (processedAddress.StartsWith("E"))
                storageArea = "E";

            // 提取地址部分
            var addressPart = processedAddress.Replace(storageArea, "").Trim();

            // 检查是否有位索引
            if (addressPart.Contains('.'))
            {
                var parts = addressPart.Split('.');
                if (parts.Length == 2 && int.TryParse(parts[0], out int wordAddr) && int.TryParse(parts[1], out int bitIndex))
                {
                    return (storageArea, wordAddr, bitIndex);
                }
            }
            else
            {
                if (int.TryParse(addressPart, out int wordAddr))
                {
                    return (storageArea, wordAddr, -1);
                }
            }

            return (storageArea, 0, -1);
        }
        catch (Exception ex)
        {
            _ = DriverInfo.Socket.DeviceConsole($"【Omron】 地址解析异常: {address}, 错误: {ex.Message}", DriverInfo.DeviceId);
            return ("", 0, -1);
        }
    }

    #region 地址初始化

    /// <summary>
    ///     地址分组,只初始化一次
    /// </summary>
    /// <param name="groupVal"></param>
    [Method("Init", description: "地址初始化", name: "Init")]
    public virtual bool InitGroup(List<IGrouping<string, DriverAddressIoArgModel>> groupVal)
    {
        try
        {
            _groupAddress.Clear();
            _otherAddress.Clear();
            _batchReadFailed.Clear();

            // 记录总地址数量
            int totalAddressCount = groupVal.Sum(g => g.Count());
            _ = DriverInfo.Socket.DeviceConsole($"【Omron】 初始化地址，共{groupVal.Count}组，{totalAddressCount}个地址", DriverInfo.DeviceId);

            // 按存储区域分组处理
            foreach (var group in groupVal)
            {
                var groupAddresses = new GroupAddressEx();
                string currentStorageArea = "";

                foreach (var tag in group)
                {
                    try
                    {
                        // 字符串类型单独处理，BCD类型可以批量处理
                        if (tag.DataType.HasFlag(DataTypeEnum.String))
                        {
                            _otherAddress.Add(tag);
                            continue;
                        }

                        // 解析地址
                        var (storageArea, wordAddress, bitIndex) = ParseOmronAddress(tag.Address);

                        if (string.IsNullOrEmpty(storageArea))
                        {
                            _otherAddress.Add(tag);
                            _ = DriverInfo.Socket.DeviceConsole($"【Omron】 地址解析失败:【{tag.Address}】", DriverInfo.DeviceId);
                            continue;
                        }

                        // 设置存储区域
                        if (string.IsNullOrEmpty(currentStorageArea))
                        {
                            currentStorageArea = storageArea;
                            groupAddresses.StorageArea = storageArea;
                        }
                        else if (currentStorageArea != storageArea)
                        {
                            // 存储区域不同，需要创建新的分组
                            if (groupAddresses.Address.Any())
                            {
                                FinalizeGroupAddress(groupAddresses);
                                _groupAddress.Add(groupAddresses);
                            }

                            groupAddresses = new GroupAddressEx { StorageArea = storageArea };
                            currentStorageArea = storageArea;
                        }

                        // 添加到对应的分组
                        groupAddresses.Address.Add(tag);

                        if (bitIndex >= 0)
                        {
                            // 位地址：对于位地址，我们需要按字地址分组，因为要从字边界读取
                            groupAddresses.OtherNumberTag.OtherNumberList.Add(wordAddress + (decimal)bitIndex / 100);
                        }
                        else
                        {
                            // 字地址
                            groupAddresses.NumberTag.NumberList.Add(wordAddress);
                        }
                    }
                    catch (Exception ex)
                    {
                        _otherAddress.Add(tag);
                        _ = DriverInfo.Socket.DeviceConsole($"【Omron】 地址解析失败:【{tag.Address}】, 错误: {ex.Message}", DriverInfo.DeviceId);
                    }
                }

                // 完成当前分组
                if (groupAddresses.Address.Any())
                {
                    FinalizeGroupAddress(groupAddresses);
                    _groupAddress.Add(groupAddresses);
                }
            }

            // 记录分组结果
            int batchAddressCount = _groupAddress.Sum(g => g.Address.Count);
            _ = DriverInfo.Socket.DeviceConsole($"【Omron】 地址初始化完成，共{_groupAddress.Count}个批量处理组，{batchAddressCount}个批量处理地址，{_otherAddress.Count}个单独处理地址", DriverInfo.DeviceId);
        }
        catch (Exception ex)
        {
            _ = DriverInfo.Socket.DeviceConsole($"【Omron】 解析地址失败,Error:{ex.Message}", DriverInfo.DeviceId);
            return false;
        }

        return true;
    }

    /// <summary>
    /// 完成分组地址的最大最小值设置
    /// </summary>
    /// <param name="groupAddress">分组地址</param>
    private void FinalizeGroupAddress(GroupAddressEx groupAddress)
    {
        if (groupAddress.NumberTag.NumberList.Any())
        {
            groupAddress.NumberTag.Max = groupAddress.NumberTag.NumberList.Max();
            groupAddress.NumberTag.Min = groupAddress.NumberTag.NumberList.Min();
        }

        if (groupAddress.OtherNumberTag.OtherNumberList.Any())
        {
            groupAddress.OtherNumberTag.Max = groupAddress.OtherNumberTag.OtherNumberList.Max();
            groupAddress.OtherNumberTag.Min = groupAddress.OtherNumberTag.OtherNumberList.Min();
        }
    }

    #endregion

    #region 批量读取

    /// <summary>
    /// 批量读取方法，统一处理所有类型的地址
    /// </summary>
    /// <returns></returns>
    [Method("Batch", description: "批量读取", name: "BatchRead")]
    public virtual async Task<List<DriverReturnValueModel>> BatchRead()
    {
        List<DriverReturnValueModel> resData = new();
        _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【开始批量读取】配置参数：最大查询长度={MaxQueryLength}，地址间隔阈值={PacketInterval}", DriverInfo.DeviceId);

        try
        {
            // 准备所有需要读取的地址
            List<DriverAddressIoArgModel> allAddresses = new List<DriverAddressIoArgModel>();

            // 合并所有分组的地址
            foreach (var group in _groupAddress)
            {
                allAddresses.AddRange(group.Address);
            }

            // 添加其他单独处理的地址
            allAddresses.AddRange(_otherAddress);

            _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【地址统计】总共需要读取 {allAddresses.Count} 个地址", DriverInfo.DeviceId);

            // 统一处理所有参数
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var results = await ReadParameters(allAddresses);
            stopwatch.Stop();

            // 统计成功和失败的数量
            int successCount = results.Count(r => r.VariableStatus == VariableStatusTypeEnum.Good);
            int errorCount = results.Count - successCount;

            _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【批量读取完成】总耗时: {stopwatch.ElapsedMilliseconds}ms, 成功: {successCount}, 失败: {errorCount}", DriverInfo.DeviceId);

            // 使用LogNet记录轮询完成消息
            if (Driver.LogNet != null)
            {
                Driver.LogNet.WriteInfo("Omron", $"轮询结束，总耗时：{stopwatch.ElapsedMilliseconds}ms");
            }

            resData.AddRange(results);
        }
        catch (Exception ex)
        {
            _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【批量读取异常】{ex.Message}", DriverInfo.DeviceId);

            // 使用LogNet记录异常
            if (Driver.LogNet != null)
            {
                Driver.LogNet.WriteError("Omron", $"批量读取异常：{ex.Message}");
            }
        }

        return resData;
    }

    /// <summary>
    /// 根据地址连续性和最大查询长度对参数进行智能分组
    /// </summary>
    /// <param name="parameters">参数列表</param>
    /// <returns>分组后的参数列表</returns>
    private List<List<DriverAddressIoArgModel>> GroupParametersByConnectivity(List<DriverAddressIoArgModel> parameters)
    {
        var result = new List<List<DriverAddressIoArgModel>>();
        if (parameters.Count == 0) return result;

        // 按存储区域和数据类型分组
        var storageGroups = parameters
            .GroupBy(p =>
            {
                var (storageArea, _, bitIndex) = ParseOmronAddress(p.Address);
                var dataTypeGroup = (p.DataType == DataTypeEnum.Bool || p.DataType == DataTypeEnum.Bit) ? "Bit" : "Word";
                return $"{storageArea}:{dataTypeGroup}";
            })
            .ToList();

        _ = DriverInfo.Socket.DeviceConsole($"【Omron】 按存储区域和数据类型分组，共 {storageGroups.Count} 个分组", DriverInfo.DeviceId);

        // 处理每个存储区域分组
        foreach (var storageGroup in storageGroups)
        {
            var groupKey = storageGroup.Key;
            var groupParams = storageGroup.ToList();

            // 按地址排序
            var sortedParams = groupParams
                .Select(p => new
                {
                    Param = p,
                    Address = ParseOmronAddress(p.Address)
                })
                .OrderBy(p => p.Address.WordAddress)
                .ThenBy(p => p.Address.BitIndex)
                .ToList();

            // 当前分组
            var currentGroup = new List<DriverAddressIoArgModel>();
            int currentStartAddress = sortedParams[0].Address.WordAddress;
            int currentEndAddress = currentStartAddress;

            // 如果MaxQueryLength为0，则使用较大的值，但仍然保持PacketInterval分组逻辑
            int maxQueryLength = MaxQueryLength <= 0 ? int.MaxValue : MaxQueryLength;

            foreach (var item in sortedParams)
            {
                int itemAddress = item.Address.WordAddress;

                // 如果是第一个地址，直接添加
                if (currentGroup.Count == 0)
                {
                    currentGroup.Add(item.Param);
                    currentEndAddress = itemAddress;
                    continue;
                }

                // 检查是否需要创建新分组
                bool createNewGroup = false;
                string reason = "";

                // 如果读取长度超过最大查询长度（优先检查）
                if (maxQueryLength > 0 && itemAddress - currentStartAddress > maxQueryLength)
                {
                    createNewGroup = true;
                    reason = $"长度超限";
                    _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【分组信息】新建分组({reason}): {groupKey}.{currentStartAddress}:{currentEndAddress - currentStartAddress}, 当前长度: {itemAddress - currentStartAddress}", DriverInfo.DeviceId);
                }
                // 如果PacketInterval > 0 且地址间隔过大
                else if (PacketInterval > 0 && itemAddress - currentEndAddress > PacketInterval)
                {
                    createNewGroup = true;
                    reason = $"间隔过大";
                    _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【分组信息】新建分组({reason}): {groupKey}.{currentStartAddress}:{currentEndAddress - currentStartAddress}, 地址间隔: {itemAddress - currentEndAddress}, 间隔阈值: {PacketInterval}", DriverInfo.DeviceId);
                }

                // 创建新分组
                if (createNewGroup)
                {
                    result.Add(new List<DriverAddressIoArgModel>(currentGroup));
                    currentGroup.Clear();
                    currentGroup.Add(item.Param);
                    currentStartAddress = itemAddress;
                    currentEndAddress = itemAddress;
                }
                else
                {
                    // 添加到当前分组
                    currentGroup.Add(item.Param);
                    if (itemAddress > currentEndAddress)
                        currentEndAddress = itemAddress;
                }
            }

            // 添加最后一个分组
            if (currentGroup.Count > 0)
            {
                result.Add(currentGroup);
                _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【分组信息】添加最后分组: {groupKey}.{currentStartAddress}:{currentEndAddress - currentStartAddress}, 地址范围: {currentStartAddress}-{currentEndAddress}", DriverInfo.DeviceId);
            }
        }

        // 输出分组结果
        _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【分组结果】共分为 {result.Count} 组", DriverInfo.DeviceId);

        return result;
    }

    /// <summary>
    /// 读取参数，统一处理所有类型
    /// </summary>
    /// <param name="parameters">参数列表</param>
    /// <returns>读取结果列表</returns>
    private async Task<List<DriverReturnValueModel>> ReadParameters(List<DriverAddressIoArgModel> parameters)
    {
        var output = new List<DriverReturnValueModel>();
        if (parameters.Count == 0) return output;

        // 对参数进行分组
        var groups = GroupParametersByConnectivity(parameters);
        _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【批量读取】参数共分为 {groups.Count} 组", DriverInfo.DeviceId);

        // 记录读取请求次数
        int readRequestCount = 0;

        // 处理每个分组
        for (int groupIndex = 0; groupIndex < groups.Count; groupIndex++)
        {
            var group = groups[groupIndex];
            if (group.Count == 0) continue;

            try
            {
                // 判断是位读取还是字读取
                var firstParam = group[0];
                var (storageArea, firstWordAddress, firstBitIndex) = ParseOmronAddress(firstParam.Address);

                bool isBitRead = firstParam.DataType == DataTypeEnum.Bool || firstParam.DataType == DataTypeEnum.Bit;

                if (isBitRead)
                {
                    // 位读取
                    var bitResult = await ReadBitGroup(group, groupIndex, readRequestCount);
                    output.AddRange(bitResult.Results);
                    readRequestCount = bitResult.RequestCount;
                }
                else
                {
                    // 字读取
                    var wordResult = await ReadWordGroup(group, groupIndex, readRequestCount);
                    output.AddRange(wordResult.Results);
                    readRequestCount = wordResult.RequestCount;
                }
            }
            catch (Exception ex)
            {
                // 批量读取过程中发生异常
                _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【批量读取异常】分组 #{groupIndex + 1}: {ex.Message}", DriverInfo.DeviceId);

                // 使用LogNet记录异常
                if (Driver.LogNet != null)
                {
                    Driver.LogNet.WriteError("Omron", $"分组 #{groupIndex + 1} 查询失败，返回报错信息：{ex.Message}");
                }

                output.AddRange(group.Select(param => new DriverReturnValueModel
                {
                    Id = param.Id,
                    VariableStatus = VariableStatusTypeEnum.Error,
                    Message = $"读取异常: {ex.Message}",
                    DataType = param.DataType
                }));
            }
        }

        // 输出总请求次数
        _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【请求统计】共发送 {readRequestCount} 次读取请求", DriverInfo.DeviceId);

        return output;
    }

    /// <summary>
    /// 读取结果包装类
    /// </summary>
    private class ReadGroupResult
    {
        public List<DriverReturnValueModel> Results { get; set; } = new();
        public int RequestCount { get; set; }
    }

    /// <summary>
    /// 读取位地址组，支持字节边界优化和连续地址批量读取
    /// </summary>
    /// <param name="group">位地址组</param>
    /// <param name="groupIndex">分组索引</param>
    /// <param name="readRequestCount">读取请求计数</param>
    /// <returns>读取结果</returns>
    private async Task<ReadGroupResult> ReadBitGroup(List<DriverAddressIoArgModel> group, int groupIndex, int readRequestCount)
    {
        var output = new List<DriverReturnValueModel>();

        if (group.Count == 0)
        {
            return new ReadGroupResult { Results = output, RequestCount = readRequestCount };
        }

        // 解析所有地址并按字地址排序
        var addressInfos = group
            .Select(param => new
            {
                Param = param,
                Address = ParseOmronAddress(param.Address)
            })
            .OrderBy(x => x.Address.WordAddress)
            .ThenBy(x => x.Address.BitIndex)
            .ToList();

        // 获取地址范围
        var firstAddress = addressInfos[0].Address;
        var lastAddress = addressInfos[addressInfos.Count - 1].Address;

        // 计算需要读取的字数量
        int startWordAddress = firstAddress.WordAddress;
        int endWordAddress = lastAddress.WordAddress;
        int wordCount = endWordAddress - startWordAddress + 1;

        // 构建读取地址
        string readAddress = $"{firstAddress.StorageArea}{startWordAddress}";
        int totalBits = wordCount * 16;

        // 执行批量读取
        var readStopwatch = System.Diagnostics.Stopwatch.StartNew();
        readRequestCount++;

        _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【发送请求 #{readRequestCount}】ReadBoolAsync: {readAddress}, 位数: {totalBits} (字数: {wordCount})", DriverInfo.DeviceId);
        var readResult = await Driver.ReadBoolAsync(readAddress, (ushort)totalBits);
        readStopwatch.Stop();

        // 使用LogNet记录读取结果
        if (Driver.LogNet != null)
        {
            string logMessage = readResult.IsSuccess
                ? $"{readAddress}:{totalBits} 查询总耗时：{readStopwatch.ElapsedMilliseconds}ms"
                : $"{readAddress}:{totalBits} 查询失败，耗时{readStopwatch.ElapsedMilliseconds}ms，返回报错信息：{readResult.Message}";

            if (readResult.IsSuccess)
                Driver.LogNet.WriteDebug("Omron", logMessage);
            else
                Driver.LogNet.WriteError("Omron", logMessage);
        }

        _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【性能】分组 #{groupIndex + 1} 位读取耗时: {readStopwatch.ElapsedMilliseconds}ms, 成功: {readResult.IsSuccess}", DriverInfo.DeviceId);

        if (readResult.IsSuccess)
        {
            // 解析每个位地址
            foreach (var addressInfo in addressInfos)
            {
                try
                {
                    var param = addressInfo.Param;
                    var address = addressInfo.Address;

                    // 计算在读取数据中的位索引
                    int wordOffset = address.WordAddress - startWordAddress;
                    int bitIndexInData = wordOffset * 16 + address.BitIndex;

                    if (bitIndexInData >= 0 && bitIndexInData < readResult.Content.Length)
                    {
                        bool bitValue = readResult.Content[bitIndexInData];
                        object value = param.DataType == DataTypeEnum.Bit ? (bitValue ? 1 : 0) : bitValue;

                        output.Add(new DriverReturnValueModel
                        {
                            Id = param.Id,
                            VariableStatus = VariableStatusTypeEnum.Good,
                            Message = readResult.Message,
                            DataType = param.DataType,
                            Value = value
                        });
                    }
                    else
                    {
                        output.Add(new DriverReturnValueModel
                        {
                            Id = param.Id,
                            VariableStatus = VariableStatusTypeEnum.MethodError,
                            Message = $"位索引超出范围: 计算索引={bitIndexInData}, 数据长度={readResult.Content.Length}",
                            DataType = param.DataType,
                            Value = null
                        });
                    }
                }
                catch (Exception ex)
                {
                    output.Add(new DriverReturnValueModel
                    {
                        Id = addressInfo.Param.Id,
                        VariableStatus = VariableStatusTypeEnum.MethodError,
                        Message = $"位解析异常: {ex.Message}",
                        DataType = addressInfo.Param.DataType,
                        Value = null
                    });
                }
            }
        }
        else
        {
            // 读取失败，所有地址均标记为失败
            output.AddRange(group.Select(param => new DriverReturnValueModel
            {
                Id = param.Id,
                VariableStatus = VariableStatusTypeEnum.Error,
                Message = readResult.Message,
                DataType = param.DataType
            }));
        }

        return new ReadGroupResult
        {
            Results = output,
            RequestCount = readRequestCount
        };
    }

    /// <summary>
    /// 读取字地址组
    /// </summary>
    /// <param name="group">字地址组</param>
    /// <param name="groupIndex">分组索引</param>
    /// <param name="readRequestCount">读取请求计数</param>
    /// <returns>读取结果</returns>
    private async Task<ReadGroupResult> ReadWordGroup(List<DriverAddressIoArgModel> group, int groupIndex, int readRequestCount)
    {
        var output = new List<DriverReturnValueModel>();

        // 获取分组的第一个和最后一个地址
        var firstParam = group[0];
        var lastParam = group[group.Count - 1];

        var (storageArea, firstWordAddress, _) = ParseOmronAddress(firstParam.Address);
        var (_, lastWordAddress, _) = ParseOmronAddress(lastParam.Address);

        // 计算读取长度
        int readLength = lastWordAddress - firstWordAddress + GetDataTypeWordLength(lastParam.DataType);

        // 构建起始地址
        string startAddress = $"{storageArea}{firstWordAddress}";

        // 执行读取
        var readStopwatch = Stopwatch.StartNew();
        readRequestCount++;

        _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【发送请求 #{readRequestCount}】ReadAsync: {startAddress}, 长度: {readLength}", DriverInfo.DeviceId);
        var readResult = await Driver.ReadAsync(startAddress, (ushort)readLength);
        readStopwatch.Stop();

        // 使用LogNet记录读取结果
        if (Driver.LogNet != null)
        {
            string logMessage = readResult.IsSuccess
                ? $"{startAddress}:{readLength} 查询总耗时：{readStopwatch.ElapsedMilliseconds}ms"
                : $"{startAddress}:{readLength} 查询失败，耗时{readStopwatch.ElapsedMilliseconds}ms，返回报错信息：{readResult.Message}";

            if (readResult.IsSuccess)
                Driver.LogNet.WriteDebug("Omron", logMessage);
            else
                Driver.LogNet.WriteError("Omron", logMessage);
        }

        _ = DriverInfo.Socket.DeviceConsole($"【Omron】 【性能】分组 #{groupIndex + 1} 字读取耗时: {readStopwatch.ElapsedMilliseconds}ms, 成功: {readResult.IsSuccess}", DriverInfo.DeviceId);

        if (readResult.IsSuccess)
        {
            // 解析每个字地址
            foreach (var param in group)
            {
                try
                {
                    var (_, wordAddress, _) = ParseOmronAddress(param.Address);
                    int relativeOffset = (wordAddress - firstWordAddress) * 2; // 每个字2字节

                    // 确保偏移量不超出读取的数据范围
                    var dataTypeLength = GetDataTypeByteLength(param.DataType);
                    if (relativeOffset < 0 || relativeOffset + dataTypeLength > readResult.Content.Length)
                    {
                        output.Add(new DriverReturnValueModel
                        {
                            Id = param.Id,
                            VariableStatus = VariableStatusTypeEnum.MethodError,
                            Message = $"偏移量超出范围: 相对偏移={relativeOffset}, 长度={dataTypeLength}, 数据长度={readResult.Content.Length}",
                            DataType = param.DataType,
                            Value = null
                        });
                        continue;
                    }

                    // 解析数据
                    object value = ParseWordValue(param, readResult.Content, relativeOffset);

                    output.Add(new DriverReturnValueModel
                    {
                        Id = param.Id,
                        VariableStatus = VariableStatusTypeEnum.Good,
                        Message = readResult.Message,
                        DataType = param.DataType,
                        Value = value
                    });
                }
                catch (Exception ex)
                {
                    output.Add(new DriverReturnValueModel
                    {
                        Id = param.Id,
                        VariableStatus = VariableStatusTypeEnum.MethodError,
                        Message = $"字解析异常: {ex.Message}",
                        DataType = param.DataType,
                        Value = null
                    });
                }
            }
        }
        else
        {
            // 读取失败，所有地址均标记为失败
            output.AddRange(group.Select(param => new DriverReturnValueModel
            {
                Id = param.Id,
                VariableStatus = VariableStatusTypeEnum.Error,
                Message = readResult.Message,
                DataType = param.DataType
            }));
        }

        return new ReadGroupResult
        {
            Results = output,
            RequestCount = readRequestCount
        };
    }

    /// <summary>
    /// 获取数据类型的字节长度
    /// </summary>
    /// <param name="dataType">数据类型</param>
    /// <returns>字节长度</returns>
    private int GetDataTypeByteLength(DataTypeEnum dataType)
    {
        return dataType switch
        {
            DataTypeEnum.Bool or DataTypeEnum.Bit => 1,
            DataTypeEnum.Int16 or DataTypeEnum.Uint16 => 2,
            DataTypeEnum.Int32 or DataTypeEnum.Uint32 or DataTypeEnum.Float => 4,
            DataTypeEnum.Int64 or DataTypeEnum.Uint64 or DataTypeEnum.Double => 8,
            DataTypeEnum.Bcd => 2,      // BCD占用2字节
            DataTypeEnum.Bcd_32 => 4,   // BCD32占用4字节
            _ => 2
        };
    }

    /// <summary>
    /// 获取数据类型的字长度
    /// </summary>
    /// <param name="dataType">数据类型</param>
    /// <returns>字长度</returns>
    private int GetDataTypeWordLength(DataTypeEnum dataType)
    {
        return dataType switch
        {
            DataTypeEnum.Bool or DataTypeEnum.Bit => 1,
            DataTypeEnum.Int16 or DataTypeEnum.Uint16 => 1,
            DataTypeEnum.Int32 or DataTypeEnum.Uint32 or DataTypeEnum.Float => 2,
            DataTypeEnum.Int64 or DataTypeEnum.Uint64 or DataTypeEnum.Double => 4,
            DataTypeEnum.Bcd => 1,      // BCD占用1个字
            DataTypeEnum.Bcd_32 => 2,   // BCD32占用2个字
            _ => 1
        };
    }

    /// <summary>
    /// 解析字数据值
    /// </summary>
    /// <param name="param">参数</param>
    /// <param name="content">读取的内容</param>
    /// <param name="offset">偏移量</param>
    /// <returns>解析后的值</returns>
    private object ParseWordValue(DriverAddressIoArgModel param, byte[] content, int offset)
    {
        try
        {
            // 根据数据类型解析值
            return param.DataType switch
            {
                DataTypeEnum.Uint16 => Driver.ByteTransform.TransUInt16(content, offset),
                DataTypeEnum.Int16 => Driver.ByteTransform.TransInt16(content, offset),
                DataTypeEnum.Uint32 => Driver.ByteTransform.TransUInt32(content, offset),
                DataTypeEnum.Int32 => Driver.ByteTransform.TransInt32(content, offset),
                DataTypeEnum.Float => Driver.ByteTransform.TransSingle(content, offset),
                DataTypeEnum.Uint64 => Driver.ByteTransform.TransUInt64(content, offset),
                DataTypeEnum.Int64 => Driver.ByteTransform.TransInt64(content, offset),
                DataTypeEnum.Double => Driver.ByteTransform.TransDouble(content, offset),
                DataTypeEnum.Bcd => ParseBcdValue(content, offset, 2),
                DataTypeEnum.Bcd_32 => ParseBcd32Value(content, offset),
                _ => Driver.ByteTransform.TransInt16(content, offset)
            };
        }
        catch (Exception ex)
        {
            _ = DriverInfo.Socket.DeviceConsole($"【Omron】 解析值异常: {param.Address}, 类型:{param.DataType}, 错误:{ex.Message}", DriverInfo.DeviceId);
            throw;
        }
    }

    /// <summary>
    /// 解析BCD值
    /// </summary>
    /// <param name="content">字节数组</param>
    /// <param name="offset">偏移量</param>
    /// <param name="length">长度</param>
    /// <returns>BCD解析后的值</returns>
    private object ParseBcdValue(byte[] content, int offset, int length)
    {
        try
        {
            // 提取BCD字节
            byte[] bcdBytes = new byte[length];
            Array.Copy(content, offset, bcdBytes, 0, length);

            // 转换为十六进制字符串
            string hexString = SoftBasic.ByteToHexString(bcdBytes).Replace(" ", "");

            // 验证是否为有效的BCD格式
            if (IsValidBcdString(hexString))
            {
                // 对于有效的BCD，直接作为十进制数字解析
                return Convert.ToInt32(hexString);
            }
            else
            {
                // 对于包含A-F的十六进制，按十六进制解析或返回字符串
                if (int.TryParse(hexString, System.Globalization.NumberStyles.HexNumber, null, out int hexValue))
                {
                    return hexValue;
                }
                else
                {
                    return hexString;
                }
            }
        }
        catch (Exception ex)
        {
            _ = DriverInfo.Socket.DeviceConsole($"【Omron】 BCD解析异常: 偏移={offset}, 长度={length}, 错误:{ex.Message}", DriverInfo.DeviceId);
            return 0;
        }
    }

    /// <summary>
    /// 解析BCD32值
    /// </summary>
    /// <param name="content">字节数组</param>
    /// <param name="offset">偏移量</param>
    /// <returns>BCD32解析后的值</returns>
    private object ParseBcd32Value(byte[] content, int offset)
    {
        try
        {
            // 提取4字节BCD数据
            byte[] bcdBytes = new byte[4];
            Array.Copy(content, offset, bcdBytes, 0, 4);

            // 执行ABCD到CDAB的转换（参考BaseConfig.cs中的实现）
            byte[] sortedContent = ConvertAbBcdToCDab(bcdBytes);

            // 转换为十六进制字符串
            string hexString = SoftBasic.ByteToHexString(sortedContent).Replace(" ", "");

            // 验证是否为有效的BCD格式（每个半字节应该是0-9）
            if (IsValidBcdString(hexString))
            {
                // 对于有效的BCD，直接作为十进制数字解析
                return Convert.ToInt32(hexString);
            }
            else
            {
                // 对于包含A-F的十六进制，按十六进制解析
                return Convert.ToInt32(hexString, 16);
            }
        }
        catch (Exception ex)
        {
            _ = DriverInfo.Socket.DeviceConsole($"【Omron】 BCD32解析异常: 偏移={offset}, 错误:{ex.Message}", DriverInfo.DeviceId);
            return 0;
        }
    }

    /// <summary>
    /// 验证字符串是否为有效的BCD格式
    /// </summary>
    /// <param name="hexString">十六进制字符串</param>
    /// <returns>是否为有效BCD</returns>
    private bool IsValidBcdString(string hexString)
    {
        foreach (char c in hexString)
        {
            if (c < '0' || c > '9')
            {
                return false;
            }
        }
        return true;
    }

    /// <summary>
    /// ABCD转CDAB（BCD32专用）
    /// </summary>
    /// <param name="input">输入字节数组</param>
    /// <returns>转换后的字节数组</returns>
    private static new byte[] ConvertAbBcdToCDab(byte[] input)
    {
        // 检查输入数组长度是否为 4
        if (input == null || input.Length != 4)
        {
            throw new ArgumentException("输入数组长度必须为 4");
        }

        // 创建新的数组并重新排列元素
        byte[] result = new byte[4];
        result[0] = input[2]; // c
        result[1] = input[3]; // d
        result[2] = input[0]; // a
        result[3] = input[1]; // b

        return result;
    }
    
    #endregion 批量读取

    #region Methods

    /// <summary>
    ///     D0:DM Area
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("D0", description: "读取位使用D10.11", name: "D:DM Area")]
    public async Task<DriverReturnValueModel> D(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     C0:DI0 Area
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C0", description: "读取位使用C10.11", name: "C:CI0 Area")]
    public async Task<DriverReturnValueModel> C(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     WO:Work Area
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("W0", description: "读取位使用W10.11", name: "W:Work Area")]
    public async Task<DriverReturnValueModel> W(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     H0:Holding Bit Area
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("H0", description: "读取位使用H10.11", name: "H:Holding Bit Area")]
    public async Task<DriverReturnValueModel> H(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     A0:Auxiliary Bit Area
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("A0", description: "读取位使用A10.11", name: "A:Auxiliary Bit Area")]
    public async Task<DriverReturnValueModel> A(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     E0.0:EM Area
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("E0.0", description: "E0.0-EF.0", name: "E:EM Area")]
    public async Task<DriverReturnValueModel> E(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TIM:Timer Area
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TIM", description: "TIM100,TIM200", name: "TIM:Timer Area")]
    public async Task<DriverReturnValueModel> Tim(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CNT:Counter Area
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CNT", description: "CNT100,CNT200", name: "CNT:Counter Area")]
    public async Task<DriverReturnValueModel> Cnt(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     IR:Index Register
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("IR", description: "IR0,IR15", name: "IR:Index Register")]
    public async Task<DriverReturnValueModel> Ir(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     DR:Data Register
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("DR", description: "DR0,DR15", name: "DR:Data Register")]
    public async Task<DriverReturnValueModel> Dr(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CF:Condition Flags
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CF", description: "CF1.2,CF1001.0", name: "CF:Condition Flags")]
    public async Task<DriverReturnValueModel> Cf(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    #endregion Methods
}

/// <summary>
///     欧姆龙网口协议基类
/// </summary>
public class OmronNetworkDeviceBase : OmronBase
{
    #region 配置参数

    [ConfigParameter("IP地址",order:1)] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号",order:2)] public int Port { get; set; } = 9600;
    [ConfigParameter("PLC单元号",order:3)] public byte Da2 { get; set; } = 0;

    [ConfigParameter("解析类型")] public DataFormat DataFormat { get; set; } = DataFormat.ABCD;
    #endregion
}

/// <summary>
///     串口欧姆龙协议基类
/// </summary>
public class OmronSerialDeviceBase : OmronBase
{
    /// <summary>
    /// </summary>
    /// <returns></returns>
    public override void Close()
    {
        Driver?.Close();
    }

    /// <summary>
    /// </summary>
    public override void Dispose()
    {
        Driver?.Dispose();
    }

    #region 配置参数

    [ConfigParameter("串口号", Remark = "")]
    public SerialNumberEnum SerialNumber { get; set; } = SerialNumberEnum.ttyS1;

    [ConfigParameter("波特率")] public BaudRateEnum BaudRate { get; set; } = BaudRateEnum.Bps9600;
    [ConfigParameter("数据位")] public int DataBits { get; set; } = 8;
    [ConfigParameter("停止位")] public StopBits Stop { get; set; } = StopBits.Two;
    [ConfigParameter("校验位")] public Parity Checkout { get; set; } = Parity.None;
    
    [ConfigParameter("解析类型")] public DataFormat DataFormat { get; set; } = DataFormat.ABCD;

    #endregion
}

/// <summary>
///     欧姆龙udp协议基类
/// </summary>
public class OmronNetworkUdpDeviceBase : OmronBase
{
    public override void Close()
    {
    }

    public override void Dispose()
    {
    }

    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 9600;

    [ConfigParameter("本机网络号(SA1)")] public byte Sa1 { get; set; } = 192;

    #endregion
}