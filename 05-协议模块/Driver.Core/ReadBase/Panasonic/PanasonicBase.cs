using Common.Enums;
using Common.Extension;

namespace Driver.Core.ReadBase.Panasonic;

/// <summary>
///     松下协议
/// </summary>
public class PanasonicBase : BasePlcProtocolCollector
{
    [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    /// <summary>
    ///     解析地址
    /// </summary>
    /// <param name="registerAddress"></param>
    /// <param name="dataType">数据类型</param>
    /// <returns></returns>
    [Method("Parse", description: "用于解析Omron的标准地址", name: "Parse")]
    public string Parse(string registerAddress, DataTypeEnum dataType)
    {
        if (registerAddress.IsNull()) return "Read";

        if (registerAddress.StartsWith("X"))
            return "ReadX0";
        if (registerAddress.StartsWith("Y"))
            return "ReadY0";
        if (registerAddress.StartsWith("R"))
            return "ReadR21";
        if (registerAddress.StartsWith("T"))
            return "ReadT0";
        if (registerAddress.StartsWith("LD"))
            return "ReadLd";
        if (registerAddress.StartsWith("L"))
            return "ReadL2";
        if (registerAddress.StartsWith("D"))
            return "ReadD0";
        if (registerAddress.StartsWith("F"))
            return "ReadF";
        if (registerAddress.StartsWith("S"))
            return "ReadS";
        if (registerAddress.StartsWith("K"))
            return "ReadK";
        if (registerAddress.StartsWith("IX"))
            return "ReadIx";
        if (registerAddress.StartsWith("IY"))
            return "ReadIy";

        return "Read";
    }

    #region Methods

    /// <summary>
    ///     X0:外部输入继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("X0", description: "X33等同于X3.3", name: "X:外部输入继电器")]
    public async Task<DriverReturnValueModel> ReadX0(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Y0:外部输出继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Y0", description: "Y33等同于Y3.3", name: "Y:外部输出继电器")]
    public async Task<DriverReturnValueModel> ReadY0(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     R2.1:内部继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("R2.1", description: "R21等同于R2.1", name: "R2.1:内部继电器")]
    public async Task<DriverReturnValueModel> ReadR21(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     T0:定时器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("T0", description: "", name: "T:定时器")]
    public async Task<DriverReturnValueModel> ReadT0(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     L2.1:链接继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("L2.1", description: "", name: "L2.1:链接继电器")]
    public async Task<DriverReturnValueModel> ReadL2(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     D0:数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C0", description: "", name: "D:数据寄存器")]
    public async Task<DriverReturnValueModel> ReadD0(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     LD:链接寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C0", description: "", name: "LD:链接寄存器")]
    public async Task<DriverReturnValueModel> ReadLd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     F0:文件寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("F0", description: "", name: "F:文件寄存器")]
    public async Task<DriverReturnValueModel> ReadF(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     S0:目标值 SV
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("S0", description: "", name: "S:目标值 SV")]
    public async Task<DriverReturnValueModel> ReadS(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     K0:经过值 EV
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("S0", description: "", name: "K:经过值 EV")]
    public async Task<DriverReturnValueModel> ReadK(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     IX:索引寄存器 IX
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("IX", description: "", name: "IX:索引寄存器 IX")]
    public async Task<DriverReturnValueModel> ReadIx(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     IY:索引寄存器 IY
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("IY", description: "", name: "IY:索引寄存器 IY")]
    public async Task<DriverReturnValueModel> ReadIy(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    #endregion Methods
}

/// <summary>
///     松下网口协议基类
/// </summary>
/// <typeparam name="T"></typeparam>
public class PanasonicNetworkDeviceBase : PanasonicBase
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 2000;
    [ConfigParameter("站号")] public byte StationNo { get; set; } = 238;

    #endregion
}