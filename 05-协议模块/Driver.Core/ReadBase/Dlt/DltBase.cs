using Common.Enums;

namespace Driver.Core.ReadBase.Dlt;

/// <summary>
/// </summary>
public class DltBase : BasePlcProtocolCollector
{
    [ConfigParameter("地址域")] public string Station { get; set; } = "";

    /// <summary>
    ///     解析地址
    /// </summary>
    /// <param name="registerAddress"></param>
    /// <param name="dataType">数据类型</param>
    /// <returns></returns>
    [Method("Parse", name: "Parse")]
    public string Parse(string registerAddress, DataTypeEnum dataType)
    {
        return "Read";
    }

    /// <summary>
    ///     ReadDouble
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("ReadDouble", description: "", name: "ReadDouble")]
    public async Task<DriverReturnValueModel> ReadDouble(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     ReadString
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("ReadString", description: "", name: "ReadString")]
    public async Task<DriverReturnValueModel> ReadString(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
}

/// <summary>
///     Dlt网口读取基类
/// </summary>
public class DltNetworkBase : DltBase
{
    #region 配置参数

    [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 502;

    #endregion
}

/// <summary>
///     Dlt串口基类
/// </summary>
public class DltSerialBase : DltBase
{
    /// <summary>
    ///     断开连接
    /// </summary>
    /// <returns></returns>
    public override void Close()
    {
        Driver?.Close();
    }

    /// <summary>
    ///     释放对象
    /// </summary>
    public override void Dispose()
    {
        Driver?.Dispose();
    }

    #region 配置参数

    [ConfigParameter("串口号", Remark = "")]
    public SerialNumberEnum SerialNumber { get; set; } = SerialNumberEnum.ttyS1;

    [ConfigParameter("波特率")] public BaudRateEnum BaudRate { get; set; } = BaudRateEnum.Bps9600;
    [ConfigParameter("数据位")] public int DataBits { get; set; } = 8;
    [ConfigParameter("停止位")] public StopBits Stop { get; set; } = StopBits.One;
    [ConfigParameter("校验位")] public Parity Checkout { get; set; } = Parity.None;

    #endregion
}