using Common.Enums;
using Common.Extension;

namespace Driver.Core.ReadBase.Fatek;

/// <summary>
///     永宏
/// </summary>
public class FatekBase : BasePlcProtocolCollector
{
    [ConfigParameter("批量读取",GroupName = "高级配置",Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;
    
    /// <summary>
    ///     解析地址
    /// </summary>
    /// <param name="registerAddress"></param>
    /// <param name="dataType">数据类型</param>
    /// <returns></returns>
    [Method("Parse", description: "用于解析Fatek的标准地址", name: "Parse")]
    public string Parse(string registerAddress, DataTypeEnum dataType)
    {
        if (registerAddress.IsNull()) return "Read";

        if (registerAddress.StartsWith("SM"))
            return "SM:SM特殊继电器";
        return "Read";
    }

    #region Methods

    /// <summary>
    ///     M:内部继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("M", description: "", name: "M:内部继电器")]
    public async Task<DriverReturnValueModel> ReadM(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     X:输入继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("X", description: "默认16进制,8进制 X011", name: "X:输入继电器")]
    public async Task<DriverReturnValueModel> ReadX(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Y:输出继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Y", description: "默认16进制,8进制 X011", name: "Y:输出继电器")]
    public async Task<DriverReturnValueModel> ReadY(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SM:SM特殊继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("I0", description: "", name: "SM:SM特殊继电器")]
    public async Task<DriverReturnValueModel> ReadSm(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     S:步进继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("S", description: "", name: "S:步进继电器")]
    public async Task<DriverReturnValueModel> ReadS(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     L:锁存继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("L", description: "", name: "L:锁存继电器")]
    public async Task<DriverReturnValueModel> ReadL(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     F:报警器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("F", description: "", name: "F:报警器")]
    public async Task<DriverReturnValueModel> ReadF(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     V:边沿继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("V", description: "", name: "V:边沿继电器")]
    public async Task<DriverReturnValueModel> ReadV(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     B:链接继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("B", description: "16进制地址", name: "B:链接继电器")]
    public async Task<DriverReturnValueModel> ReadB(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SB:特殊链接继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SB", description: "16进制地址", name: "SB:特殊链接继电器")]
    public async Task<DriverReturnValueModel> ReadSb(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     DX:直接输入
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("DX", description: "", name: "DX:直接输入")]
    public async Task<DriverReturnValueModel> ReadDx(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     DY:直接输出
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("DY", description: "", name: "DY:直接输出")]
    public async Task<DriverReturnValueModel> ReadDy(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TS:定时器触点
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TS", description: "", name: "TS:定时器触点")]
    public async Task<DriverReturnValueModel> ReadTs(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TC:定时器线圈
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TC", description: "", name: "TC:定时器线圈")]
    public async Task<DriverReturnValueModel> ReadTc(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SS:累计定时器触点
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SS", description: "", name: "SS:累计定时器触点")]
    public async Task<DriverReturnValueModel> ReadSs(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SC:累计定时器线圈
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SC", description: "", name: "SC:累计定时器线圈")]
    public async Task<DriverReturnValueModel> ReadSc(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CS:计数器触点
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CS", description: "", name: "CS:计数器触点")]
    public async Task<DriverReturnValueModel> ReadCs(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CC:计数器线圈
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CC", description: "", name: "CC:计数器线圈")]
    public async Task<DriverReturnValueModel> ReadCc(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     D:数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("I0", description: "", name: "D:数据寄存器")]
    public async Task<DriverReturnValueModel> ReadD(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SD:特殊数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SD", description: "", name: "SD:特殊数据寄存器")]
    public async Task<DriverReturnValueModel> ReadSd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     W:链接寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("W", description: "", name: "W:链接寄存器")]
    public async Task<DriverReturnValueModel> ReadW(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SW:特殊链接寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SW", description: "", name: "SW:特殊链接寄存器")]
    public async Task<DriverReturnValueModel> ReadSw(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }


    /// <summary>
    ///     R:文件寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("R", description: "", name: "R:文件寄存器")]
    public async Task<DriverReturnValueModel> ReadR(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }


    /// <summary>
    ///     Z:变址寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Z", description: "", name: "Z:变址寄存器")]
    public async Task<DriverReturnValueModel> ReadZ(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }


    /// <summary>
    ///     ZR:ZR文件寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("ZR", description: "", name: "ZR:ZR文件寄存器")]
    public async Task<DriverReturnValueModel> ReadZr(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TN:定时器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TN", description: "", name: "TN:定时器当前值")]
    public async Task<DriverReturnValueModel> ReadTn(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SN:累计定时器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SN", description: "", name: "SN:累计定时器当前值")]
    public async Task<DriverReturnValueModel> ReadSn(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CN:计数器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CN", description: "", name: "CN:计数器当前值")]
    public async Task<DriverReturnValueModel> ReadCn(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    #endregion
}

/// <summary>
///     永宏网口协议基类
/// </summary>
public class FatekNetworkDeviceBase : FatekBase
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 2000;

    #endregion
}