using Common.Enums;
using Common.Extension;
using HslCommunication.Profinet.Delta;

namespace Driver.Core.ReadBase.Delta;

/// <summary>
///     台达
/// </summary>
public class DeltaBase : BasePlcProtocolCollector
{
    /// <summary>
    ///     字符串和无法解析地址单独读取
    /// </summary>
    internal readonly List<DriverAddressIoArgModel> _otherAddress = new();

    /// <summary>
    ///     解析地址
    /// </summary>
    internal readonly List<GroupAddress> _groupAddress = new();

    /// <summary>
    ///     批量读取失败的地址
    /// </summary>
    internal readonly List<DriverAddressIoArgModel> _batchReadFailed = new();

    [ConfigParameter("批量读取", GroupName = "高级配置")]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    [ConfigParameter("站号")] public byte Station { get; set; } = 1;

    [ConfigParameter("系列")] public DeltaSeries Series { get; set; } = DeltaSeries.Dvp;
    [ConfigParameter("异步读取")] public BoolEnum ReadAsync { get; set; } = BoolEnum.True;

    /// <summary>
    ///     解析地址
    /// </summary>
    /// <param name="registerAddress"></param>
    /// <param name="dataType">数据类型</param>
    /// <returns></returns>
    [Method("Parse", description: "用于解析Delta的标准地址", name: "Parse")]
    public string Parse(string registerAddress, DataTypeEnum dataType)
    {
        if (registerAddress.IsNull()) return "Read";

        if (registerAddress.StartsWith("S0-S1023"))
            return "S0-S1023";
        if (registerAddress.StartsWith("X0-X177"))
            return "X0-X177:输入继电器";
        if (registerAddress.StartsWith("Y0-Y177"))
            return "Y0-Y177:输出继电器";
        if (registerAddress.StartsWith("T0-T127"))
            return "T0-T127:定时器";
        if (registerAddress.StartsWith("C0-C127 C232-C255"))
            return "C0-C127 C232-C255:计数器";
        if (registerAddress.StartsWith("M0-M1279"))
            return "M0-M1279:内部继电器";
        if (registerAddress.StartsWith("D0-D1311"))
            return "D0-D1311:数据寄存器";
        if (registerAddress.StartsWith("S0-S1023"))
            return "S0-S1023";
        if (registerAddress.StartsWith("T0-T255"))
            return "T0-T255:定时器";
        if (registerAddress.StartsWith("C0-C199 C200-C255"))
            return "C0-C199 C200-C255:计数器";
        if (registerAddress.StartsWith("M0-M4095"))
            return "M0-M4095:内部继电器";
        if (registerAddress.StartsWith("D0-D4999"))
            return "D0-D4999:数据寄存器";
        if (registerAddress.StartsWith("X0-X377"))
            return "X0-X377:输入继电器";
        if (registerAddress.StartsWith("Y0-Y377"))
            return "Y0-Y377:输出继电器";
        if (registerAddress.StartsWith("D0-D9999"))
            return "D0-D9999:数据寄存器";
        return "Read";
    }

    /// <summary>
    ///     地址分组,只初始化一次
    /// </summary>
    /// <param name="groupVal"></param>
    [Method("Init", description: "地址初始化", name: "Init")]
    public bool InitGroup(List<IGrouping<string, DriverAddressIoArgModel>> groupVal)
    {
        try
        {
            _groupAddress.Clear();
            _otherAddress.Clear();
            var storageAreaMappings = new Dictionary<string, string>
            {
                {"S", "S"},
                {"X", "X"},
                {"Y", "Y"},
                {"T", "T"},
                {"C", "C"},
                {"M", "M"},
                {"D", "D"}
            };
            foreach (var group in groupVal)
            {
                //
                var groupAddresses = new GroupAddress
                {
                    NumberList = new List<int>(),
                    Address = new List<DriverAddressIoArgModel>()
                };

                foreach (var tag in group)
                    try
                    {
                        #region 区域解析

                        var address = tag.Address.ToUpper();
                        foreach (var (key, val) in storageAreaMappings)
                            if (address.ToUpper().StartsWith(key))
                            {
                                groupAddresses.StorageArea = val;
                                address = address.Replace(val, "");
                                break;
                            }

                        #endregion

                        if (tag.DataType.HasFlag(DataTypeEnum.String))
                        {
                            _otherAddress.Add(tag);
                            continue;
                        }

                        if (tag.DataType.HasFlag(DataTypeEnum.Uint16) || tag.DataType.HasFlag(DataTypeEnum.Int16) || tag.DataType.HasFlag(DataTypeEnum.Uint32) ||
                            tag.DataType.HasFlag(DataTypeEnum.Int32) || tag.DataType.HasFlag(DataTypeEnum.Float) || tag.DataType.HasFlag(DataTypeEnum.Uint64) ||
                            tag.DataType.HasFlag(DataTypeEnum.Int64) || tag.DataType.HasFlag(DataTypeEnum.Double))
                            groupAddresses.ReadNumber = true;

                        if (address.IndexOf(".", StringComparison.Ordinal) > 0)
                            address = address.Split(".", StringSplitOptions.RemoveEmptyEntries)[1];
                        if ((address.StartsWith("0") && address.Length > 1) || !address.IsInt())
                        {
                            _otherAddress.Add(tag);
                        }
                        else
                        {
                            groupAddresses.Address.Add(tag);
                            groupAddresses.NumberList.Add(Convert.ToInt32(address));
                        }
                    }
                    catch
                    {
                        _otherAddress.Add(tag);
                        _ = DriverInfo.Socket.DeviceConsole($"【Delta】 地址解析失败:【{tag.Address}】", DriverInfo.DeviceId);
                    }

                if (groupAddresses.NumberList.Any())
                {
                    groupAddresses.Max = (ushort) groupAddresses.NumberList.Max();
                    groupAddresses.MinNumber = (ushort) groupAddresses.NumberList.Min();
                    _groupAddress.Add(groupAddresses);
                }
            }
        }
        catch (Exception ex)
        {
            _ = DriverInfo.Socket.DeviceConsole($"【Delta】 批量读取解析地址失败:【{ex.Message}】", DriverInfo.DeviceId);
            return false;
        }

        return true;
    }

    #region 批量读取

    /// <summary>
    /// </summary>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    [Method("Batch", description: "批量读取", name: "BatchRead")]
    public async Task<List<DriverReturnValueModel>> BatchRead()
    {
        List<DriverReturnValueModel> resData = new();

        foreach (var groupAddress in _groupAddress)
            try
            {
                if (groupAddress.ReadNumber)
                    resData.AddRange(await BatchReadNumber(groupAddress));
                else
                    resData.AddRange(await BatchReadCoil(groupAddress));
            }
            catch (Exception ex)
            {
                _ = DriverInfo.Socket.DeviceConsole($"【Delta】 批量读取失败,Error:{ex.Message}", DriverInfo.DeviceId);
            }

        // 批量读取失败的地址，将他丢到单个读取中去
        if (_batchReadFailed.Any())
        {
            // 批量读取失败被标记的地址就从批量读取中移除
            foreach (var group in _groupAddress)
            {
                var ids = new HashSet<long>(_batchReadFailed.Select(item => item.Id));
                for (var i = group.Address.Count - 1; i >= 0; i--)
                    if (ids.Contains(group.Address[i].Id))
                        group.Address.RemoveAll(item => item.Id == group.Address[i].Id);
            }

            _otherAddress.AddRange(_batchReadFailed);
            _batchReadFailed.Clear();
        }

        foreach (var address in _otherAddress)
            try
            {
                resData.Add(await Read(address));
            }
            catch (Exception ex)
            {
                _ = DriverInfo.Socket.DeviceConsole($"【Delta】 读取失败,Error:{ex.Message}", DriverInfo.DeviceId);
            }

        return resData;
    }

    /// <summary>
    ///     读Number
    /// </summary>
    /// <param name="groupAddress"></param>
    protected virtual async Task<List<DriverReturnValueModel>> BatchReadNumber(GroupAddress groupAddress)
    {
        try
        {
            //处理原始数据
            var dicNumber = Group(groupAddress);
            List<DriverReturnValueModel> resData = new();
            foreach (var (_, values) in dicNumber)
            {
                // 当前组里最小的值
                var thisMinNumber = values.Min();
                // 当前组里最大的值
                var thisMaxNumber = values.Max();
                // var minAddr = groupAddress.StorageArea + thisMinNumber;
                OperateResult<byte[]> readValue;
                if (ReadAsync == BoolEnum.True)
                    readValue = await Driver.ReadAsync(groupAddress.StorageArea + thisMinNumber, Convert.ToUInt16(thisMaxNumber - thisMinNumber + 3));
                else
                    readValue = Driver.Read(groupAddress.StorageArea + thisMinNumber, Convert.ToUInt16(thisMaxNumber - thisMinNumber + 3));
                
                if (readValue.IsSuccess)
                    // 1个地址占用2个字节,全int
                    foreach (var address in groupAddress.Address)
                    {
                        var addrStr = address.Address;
                        addrStr = addrStr.ToUpper().Replace(groupAddress.StorageArea, "").Trim();
                        if (!addrStr.IsInt())
                            continue;
                        // 没有这个地址就过滤掉
                        if (!values.Contains(Convert.ToInt32(addrStr)))
                            continue;

                        DriverReturnValueModel ret = new()
                        {
                            DataType = address.DataType,
                            Id = address.Id
                        };

                        var addr = Convert.ToInt32(addrStr) - thisMinNumber;
                        try
                        {
                            switch (address.DataType)
                            {
                                case DataTypeEnum.Bit:
                                    ret.Value = Driver.ByteTransform.TransBool(readValue.Content, addr * 2) ? 1 : 0;
                                    break;
                                case DataTypeEnum.Bool:
                                    ret.Value = Driver.ByteTransform.TransBool(readValue.Content, addr * 2);
                                    break;
                                case DataTypeEnum.Uint16:
                                    ret.Value = Driver.ByteTransform.TransUInt16(readValue.Content, addr * 2);
                                    break;
                                case DataTypeEnum.Int16:
                                    ret.Value = Driver.ByteTransform.TransInt16(readValue.Content, addr * 2);
                                    break;
                                case DataTypeEnum.Uint32:
                                    ret.Value = Driver.ByteTransform.TransUInt32(readValue.Content, addr * 2);
                                    break;
                                case DataTypeEnum.Int32:
                                    ret.Value = Driver.ByteTransform.TransInt32(readValue.Content, addr * 2);
                                    break;
                                case DataTypeEnum.Float:
                                    ret.Value = Driver.ByteTransform.TransSingle(readValue.Content, addr * 2);
                                    break;
                                case DataTypeEnum.Uint64:
                                    ret.Value = Driver.ByteTransform.TransUInt64(readValue.Content, addr * 2);
                                    break;
                                case DataTypeEnum.Int64:
                                    ret.Value = Driver.ByteTransform.TransInt32(readValue.Content, addr * 2);
                                    break;
                                case DataTypeEnum.Double:
                                    ret.Value = Driver.ByteTransform.TransDouble(readValue.Content, addr * 2);
                                    break;
                                default:
                                    _batchReadFailed.Add(address);
                                    ret.VariableStatus = VariableStatusTypeEnum.MethodError;
                                    ret.Message = "数据类型错误,无法解析！";
                                    break;
                            }

                            resData.Add(ret);
                        }
                        catch (Exception ex)
                        {
                            _batchReadFailed.Add(address);
                            ret.VariableStatus = VariableStatusTypeEnum.MethodError;
                            ret.Message = $"【Delta】 批量读取解析失败,,错误信息:{ex.Message}";
                            resData.Add(ret);
                        }
                    }
                else
                    resData.AddRange(groupAddress.Address.Select(propertied => new DriverReturnValueModel
                    {
                        VariableStatus = VariableStatusTypeEnum.Error,
                        DataType = propertied.DataType,
                        Id = propertied.Id,
                        Message = readValue.Message,
                        ErrorCode = -1
                    }));
            }

            return resData;
        }
        catch (Exception ex)
        {
            throw Oops.Bah($"【Delta】 批量读Number失败,错误信息:{ex.Message}");
        }
    }

    /// <summary>
    ///     读Bool地址
    /// </summary>
    /// <param name="groupAddress"></param>
    protected virtual async Task<List<DriverReturnValueModel>> BatchReadCoil(GroupAddress groupAddress)
    {
        try
        {
            // 处理原始数据
            var dicNumber = Group(groupAddress);

            List<DriverReturnValueModel> resData = new();
            foreach (var (_, values) in dicNumber)
            {
                var thisMinNumber = values.Min();
                var thisMaxNumber = values.Max();
                // var minAddr = groupAddress.StorageArea + thisMinNumber;
                OperateResult<bool[]> readValue;
                if (ReadAsync == BoolEnum.True)
                    readValue = await Driver.ReadBoolAsync(groupAddress.StorageArea + thisMinNumber, Convert.ToUInt16(thisMaxNumber - thisMinNumber + 3));
                else
                    readValue =  Driver.ReadBool(groupAddress.StorageArea + thisMinNumber, Convert.ToUInt16(thisMaxNumber - thisMinNumber + 3));
                if (readValue.IsSuccess)
                    foreach (var address in groupAddress.Address)
                    {
                        var addrStr = address.Address;
                        addrStr = addrStr.ToUpper().Replace(groupAddress.StorageArea, "").Trim();
                        if (!addrStr.IsInt())
                            continue;
                        // 没有这个地址就过滤掉
                        if (!values.Contains(Convert.ToInt32(addrStr)))
                            continue;

                        DriverReturnValueModel ret = new()
                        {
                            DataType = address.DataType,
                            Id = address.Id
                        };

                        var addr = Convert.ToInt32(addrStr) - thisMinNumber;
                        if (groupAddress.StorageArea is "X" or "Y")
                        {
                            var convertAddr = Convert.ToInt32(addrStr, 8);
                            var thisMinNumberStr = Convert.ToInt32(thisMinNumber.ToString(), 8);
                            addr = convertAddr - thisMinNumberStr;
                        }

                        try
                        {
                            switch (address.DataType)
                            {
                                case DataTypeEnum.Bit:
                                    ret.Value = readValue.Content[addr] ? 1 : 0;
                                    break;
                                case DataTypeEnum.Bool:
                                    ret.Value = readValue.Content[addr];
                                    break;
                                default:
                                    _batchReadFailed.Add(address);
                                    ret.VariableStatus = VariableStatusTypeEnum.MethodError;
                                    ret.Message = "数据类型错误,无法解析！";
                                    break;
                            }

                            resData.Add(ret);
                        }
                        catch (Exception ex)
                        {
                            _batchReadFailed.Add(address);
                            ret.VariableStatus = VariableStatusTypeEnum.MethodError;
                            ret.Message = $"【Delta】 批量读线圈地址解析失败,错误信息:{ex.Message}";
                            resData.Add(ret);
                        }
                    }
                else
                    resData.AddRange(groupAddress.Address.Select(propertied => new DriverReturnValueModel
                    {
                        VariableStatus = VariableStatusTypeEnum.Error,
                        DataType = propertied.DataType,
                        Id = propertied.Id,
                        Message = readValue.Message,
                        ErrorCode = -1
                    }));
            }

            return resData;
        }
        catch (Exception ex)
        {
            throw Oops.Bah($"【Delta】 批量读线圈地址失败,错误信息:{ex.Message}");
        }
    }

    /// <summary>
    ///     切割原始数据
    /// </summary>
    /// <param name="groupAddress"></param>
    /// <returns></returns>
    internal Dictionary<int, List<int>> Group(GroupAddress groupAddress)
    {
        // 最大分割数
        const int maximumSplitCount = 116;
        var dicNumber = new Dictionary<int, List<int>>();
        // 最大和最小地址在同一范围内,就不需要切割
        if (groupAddress.Max - groupAddress.MinNumber <= maximumSplitCount)
        {
            dicNumber.Add(0, groupAddress.NumberList);
            return dicNumber;
        }

        // (最大值-最小值) / 最大分割数 = 整体切割读取次数
        var splitCount = (int) Math.Ceiling((groupAddress.Max - groupAddress.MinNumber) / (double) maximumSplitCount);
        for (var i = 0; i <= splitCount; i++)
        {
            // 起始地址小于最小值，就将循环次数+1
            if (i * maximumSplitCount < groupAddress.MinNumber)
                splitCount++;
            // 循环将范围内的数据丢到对应的数据组里
            var numbers = groupAddress.NumberList.Where(w => w >= i * maximumSplitCount && w < (i + 1) * maximumSplitCount).ToList();
            if (numbers.Any())
                dicNumber.Add(i, numbers);
        }

        return dicNumber;
    }

    #endregion 批量读取

    #region Methods

    /// <summary>
    ///     S0-S127
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("S", description: "", name: "S0-S127")]
    public async Task<DriverReturnValueModel> ReadS0S127(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     X0-X177:输入继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("X", description: "", name: "X0-X177:输入继电器")]
    public async Task<DriverReturnValueModel> ReadX0X177(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Y0-Y177:输出继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Y", description: "地址8进制", name: "Y0-Y177:输出继电器")]
    public async Task<DriverReturnValueModel> ReadY0Y177(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     T0-T127:定时器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("T", description: "如果是读位,就是通断继电器,如果是读字,就是当前值", name: "T0-T127:定时器")]
    public async Task<DriverReturnValueModel> ReadT0T127(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }


    /// <summary>
    ///     C0-C127 C232-C255:计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C", description: "如果是读位,就是通断继电器,如果是读字,就是当前值", name: "C0-C127 C232-C255:计数器")]
    public async Task<DriverReturnValueModel> ReadC0C127C232C255(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     M0-M1279:内部继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("M", description: "", name: "M0-M1279:内部继电器")]
    public async Task<DriverReturnValueModel> ReadM0M1279(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     D0-D1311:数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("D", description: "", name: "D0-D1311:数据寄存器")]
    public async Task<DriverReturnValueModel> ReadD0D1311(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     S0-S1023
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("S", description: "", name: "S0-S1023")]
    public async Task<DriverReturnValueModel> ReadS0S1023(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     T0-T255:定时器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("T", description: "", name: "T0-T255:定时器")]
    public async Task<DriverReturnValueModel> ReadT0T255(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     C0-C199 C200-C255:计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C", description: "如果是读位,就是通断继电器,如果是读字,就是当前值", name: "C0-C199 C200-C255:计数器")]
    public async Task<DriverReturnValueModel> ReadC0C199C200C255(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     M0-M4095:内部继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("M", description: "", name: "M0-M4095:内部继电器")]
    public async Task<DriverReturnValueModel> ReadM0M4095(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     D0-D4999:数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("D", description: "", name: "D0-D4999:数据寄存器")]
    public async Task<DriverReturnValueModel> ReadD0D4999(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     X0-X377:输入继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("X", description: "只读操作,地址8进制", name: "X0-X377:输入继电器")]
    public async Task<DriverReturnValueModel> ReadX0X377(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Y0-Y377:输出继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Y", description: "地址8进制", name: "Y0-Y377:输出继电器")]
    public async Task<DriverReturnValueModel> ReadY0Y377(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     D0-D9999:数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("D", description: "", name: "D0-D9999:数据寄存器")]
    public async Task<DriverReturnValueModel> ReadD0D9999(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    #endregion Methods
}