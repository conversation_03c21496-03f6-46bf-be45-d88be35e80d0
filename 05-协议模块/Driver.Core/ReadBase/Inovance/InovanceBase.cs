using Common.Enums;
using HslCommunication.Profinet.Inovance;

namespace Driver.Core.ReadBase.Inovance;

/// <summary>
///     汇川协议基类
/// </summary>
public class InovanceBase : BasePlcProtocolCollector
{
    [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    [ConfigParameter("站号")] public byte Station { get; set; } = 1;
    [ConfigParameter("解析类型")] public DataFormat DataFormat { get; set; } = DataFormat.ABCD;
    [ConfigParameter("系列")] public InovanceSeries Series { get; set; } = InovanceSeries.AM;
    [ConfigParameter("大小端转换")] public BoolEnum ByteTransform { get; set; } = BoolEnum.False;
    [ConfigParameter("字符串颠倒")] public BoolEnum StringReverse { get; set; } = BoolEnum.False;

    /// <summary>
    ///     解析地址
    /// </summary>
    /// <param name="registerAddress"></param>
    /// <param name="dataType">数据类型</param>
    /// <returns></returns>
    [Method("Parse", name: "Parse")]
    public string Parse(string registerAddress, DataTypeEnum dataType)
    {
        return "Read";
    }

    #region Inovance

    /// <summary>
    ///     Q:输出
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Q", description: "Q0.0-Q8191.7 或是 Q0-Q65535", name: "Q:输出", filter: true, filterField: "Series", filterValue: "0")]
    public async Task<DriverReturnValueModel> ReadAmQ(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }


    /// <summary>
    ///     I:输入
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("I", description: "IX0.0-IX8191.7 或是 I0-I65535", name: "I:输入", filter: true, filterField: "Series", filterValue: "0")]
    public async Task<DriverReturnValueModel> ReadAmI(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }


    /// <summary>
    ///     M:M寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("M", description: "MW0-MW65535", name: "M:M寄存器(字访问)", filter: true, filterField: "Series", filterValue: "0")]
    public async Task<DriverReturnValueModel> ReadAmM(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     M:M寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("M", description: "MX0.0-MX1000.10", name: "M:M寄存器(位访问)", filter: true, filterField: "Series", filterValue: "0")]
    public async Task<DriverReturnValueModel> ReadAmMb(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SM
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SM", description: "SM0.0-SM8191.7 或是 SM0-SM65535", name: "SM", filter: true, filterField: "Series", filterValue: "0")]
    public async Task<DriverReturnValueModel> ReadAmSm(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SD
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SD", description: "SDW0-SDW65535", name: "SD", filter: true, filterField: "Series", filterValue: "0")]
    public async Task<DriverReturnValueModel> ReadAmSd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }


    /// <summary>
    ///     M:中间寄电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("M", description: "M0-M7679，M8000-M8511", name: "M:中间寄电器", filter: true, filterField: "Series", filterValue: "1")]
    public async Task<DriverReturnValueModel> ReadH3Um(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SM
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SM", description: "SM0-SM1023", name: "SM", filter: true, filterField: "Series", filterValue: "1")]
    public async Task<DriverReturnValueModel> ReadH3USm(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     S
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("S", description: "S0-S4095", name: "S", filter: true, filterField: "Series", filterValue: "1")]
    public async Task<DriverReturnValueModel> ReadH3Us(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     T:定时器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("T", description: "T0-T511", name: "T:定时器", filter: true, filterField: "Series", filterValue: "1")]
    public async Task<DriverReturnValueModel> ReadH3Ut(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     C:计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C", description: "C0-C199,C200-C255", name: "C:计数器", filter: true, filterField: "Series", filterValue: "1")]
    public async Task<DriverReturnValueModel> ReadH3Uc(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     X:输入
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("X", description: "X0-X377 或者X0.0-X37.7", name: "X:输入", filter: true, filterField: "Series", filterValue: "1")]
    public async Task<DriverReturnValueModel> ReadH3Ux(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Y:输出
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Y", description: "Y0-Y377 或者Y0.0-Y37.7", name: "Y:输出", filter: true, filterField: "Series", filterValue: "1")]
    public async Task<DriverReturnValueModel> ReadH3Uy(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     D:数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("D", description: "D0-D8511", name: "D:数据寄存器", filter: true, filterField: "Series", filterValue: "1")]
    public async Task<DriverReturnValueModel> ReadH3Ud(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SD:数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("SD", description: "SD0-SD1023", name: "SD:数据寄存器", filter: true, filterField: "Series", filterValue: "1")]
    public async Task<DriverReturnValueModel> ReadH3USd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     R
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("R", description: "R0-R32767", name: "R", filter: true, filterField: "Series", filterValue: "H3U")]
    public async Task<DriverReturnValueModel> ReadH3Ur(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     M:中间寄电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("M", description: "M0-M3071，M8000-M8511", name: "M:中间寄电器", filter: true, filterField: "Series", filterValue: "2")]
    public async Task<DriverReturnValueModel> ReadH5Um(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     S
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("S", description: "S0-S999", name: "S", filter: true, filterField: "Series", filterValue: "2")]
    public async Task<DriverReturnValueModel> ReadH5Us(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     T:定时器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("T", description: "T0-T255", name: "T:定时器", filter: true, filterField: "Series", filterValue: "2")]
    public async Task<DriverReturnValueModel> ReadH5Ut(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     X:输入
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("X", description: "X0-X377 或者X0.0-X37.7", name: "X:输入", filter: true, filterField: "Series", filterValue: "2")]
    public async Task<DriverReturnValueModel> ReadH5Ux(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Y:输出
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Y", description: "Y0-Y377 或者Y0.0-Y37.7", name: "Y:输出", filter: true, filterField: "Series", filterValue: "2")]
    public async Task<DriverReturnValueModel> ReadH5Uy(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     C:计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C", description: "C0-C255", name: "C:计数器", filter: true, filterField: "Series", filterValue: "2")]
    public async Task<DriverReturnValueModel> ReadH5Uc(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
    
    /// <summary>
    ///     D:数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("D", description: "D0-D8511", name: "D:数据寄存器", filter: true, filterField: "Series", filterValue: "H5U")]
    public async Task<DriverReturnValueModel> ReadH5Ud(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    #endregion Inovance
}

/// <summary>
///     Modbus网口读取基类
/// </summary>
public class InovanceNetworkBase : InovanceBase
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 502;

    #endregion
}