using Common.Enums;

namespace Driver.Core.ReadBase.Keyence;

/// <summary>
///     基恩士基类
/// </summary>
public class KeyenceBase : BasePlcProtocolCollector
{
    #region 方法

    /// <summary>
    ///     BO:链接继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("BO", description: "地址16进制，范围:B0~B7FFF", name: "BO:链接继电器")]
    public async Task<DriverReturnValueModel> BO(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     MROOO/MRO.0:内部辅助继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("MROOO/MRO.0", description: "字+位组合(16)，范围: MR00000~MR99915", name: "MROOO/MRO.0:内部辅助继电器")]
    public async Task<DriverReturnValueModel> Mr(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     LRO15/LRO.15:锁存继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("LRO15/LRO.15", description: "字+位组合(16)，范围: LR00000~LR99915", name: "LRO15/LRO.15:锁存继电器")]
    public async Task<DriverReturnValueModel> Lr(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CRO15/CR0.15:控制继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CRO15/CR0.15", description: "字+位组合(16)，范围:CR0000~CR7915", name: "CRO15/CR0.15:控制继电器")]
    public async Task<DriverReturnValueModel> Cr(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CMO:控制寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CMO", description: "范围:CM0000~CM5999", name: "CMO:控制寄存器")]
    public async Task<DriverReturnValueModel> CmO(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     DMO:数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("DMO", description: "范围: DM00000~DM65534", name: "DMO:数据寄存器")]
    public async Task<DriverReturnValueModel> Dm(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     EMO:扩展数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("EMO", description: "范围:EM00000~EM65534", name: "EMO:扩展数据寄存器")]
    public async Task<DriverReturnValueModel> Em(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     FMO:文件寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("FMO", description: "FMO0000~FM32767", name: "FMO:文件寄存器")]
    public async Task<DriverReturnValueModel> FmO(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     ZFO:文件寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("ZFO", description: "ZF000000~ZF524287", name: "ZFO:文件寄存器")]
    public async Task<DriverReturnValueModel> ZfO(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     WO:链路寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("WO", description: "16进制地址，范围:W0000~W7FFF", name: "WO:链路寄存器")]
    public async Task<DriverReturnValueModel> Wo(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    #endregion
}

/// <summary>
///     基恩士网口基类
/// </summary>
public class KeyenceNetWorkBase : KeyenceBase
{
    #region 配置参数

    [ConfigParameter("字符串颠倒")] public BoolEnum StringReverse { get; set; } = BoolEnum.False;
    
    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 6000;

    [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    #endregion

    /// <summary>
    ///     R015/R0.15 (XF,X1A0):输入继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("RO15/RO.15 (XF,X1A0)", description: "字+位组合(16)，或是三菱的X地址，范围: RO0000~R99915", name: "R015/R0.15 (XF,X1A0):输入继电器")]
    public async Task<DriverReturnValueModel> Ro15X(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     RO15/RO.15 (YF,Y1AO):输出继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("RO15/RO.15 (YF,Y1AO)", description: "字+位组合(16)，或是三菱的Y地址，范围:RO0000~R99915", name: "RO15/RO.15 (YF,Y1AO):输出继电器")]
    public async Task<DriverReturnValueModel> Ro15Y(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TNO:定时器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TNO", description: "范围:TN0000~TN3999", name: "TNO:定时器当前值")]
    public async Task<DriverReturnValueModel> TnO(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TCO:定时器线圈
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TCO", description: "范围:TC0000~TC3999", name: "TCO:定时器线圈")]
    public async Task<DriverReturnValueModel> TcO(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TSO:定时器触点
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TSO", description: "范围: TS0000~TS3999", name: "TSO:定时器触点")]
    public async Task<DriverReturnValueModel> TsO(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CNO:计数器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CNO", description: "范围: TN0000~TN3999", name: "CNO:计数器当前值")]
    public async Task<DriverReturnValueModel> CnO(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CS0:计数器触点
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CS0", description: "范围:CS0000~Cs3999", name: "CS0:计数器触点")]
    public async Task<DriverReturnValueModel> Cs0(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CC0:计数器线圈
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CC0", description: "范围:CC0000~CC3999", name: "CC0:计数器线圈")]
    public async Task<DriverReturnValueModel> Cc0(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
}

/// <summary>
///     基恩士串口基类
/// </summary>
public class KeyenceSerialBase : KeyenceBase
{
    #region 配置参数

    [ConfigParameter("站号")] public byte Station { get; set; } = 0;

    [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    #endregion

    /// <summary>
    ///     RO15/RO.15:R继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("RO15/RO.15", description: "字+位组合(16)，范围:RO0000~R99915", name: "RO15/RO.15:R继电器")]
    public async Task<DriverReturnValueModel> Ro15(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     T0:定时器线圈
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("T0", description: "范围:T0000~T3999", name: "T0:定时器线圈")]
    public async Task<DriverReturnValueModel> T0(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CTCO:高速计数器线圈
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CTCO", description: "范围: CTC0 ~ CTC3", name: "CTCO:高速计数器线圈")]
    public async Task<DriverReturnValueModel> Ctco(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CTHO:高速计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CTHO", description: "范围: CTHO ~ CTH1", name: "CTHO:高速计数器")]
    public async Task<DriverReturnValueModel> Ctho(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     VBO:字线圈
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("VBO", description: "16进制，范围:VB0000~VB3FFF", name: "VBO:字线圈")]
    public async Task<DriverReturnValueModel> Vbo(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TMO:临时数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TMO", description: "范围: TMO00~TM511", name: "TMO:临时数据寄存器")]
    public async Task<DriverReturnValueModel> Tmo(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Z0:变址寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Z0", description: "范围: Z1~Z12", name: "Z0:变址寄存器")]
    public async Task<DriverReturnValueModel> Z0(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TCO:定时器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TCO", description: "范围:TC0000~TC3999", name: "TCO:定时器当前值")]
    public async Task<DriverReturnValueModel> Tco(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TSO:定时器设定值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("TSO", description: "范围: TS0000~TS3999", name: "TSO:定时器设定值")]
    public async Task<DriverReturnValueModel> Tso(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CC0:计数器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CC0", description: "范围:CC0000~CC3999", name: "CC0:计数器当前值")]
    public async Task<DriverReturnValueModel> Cc0(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CS0:计数器设定值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CS0", description: "范围:CS0000~CS3999", name: "CS0:计数器设定值")]
    public async Task<DriverReturnValueModel> Cs0(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CTC0:高速计数器设定值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("CTC0", description: "范围: CTC0~CTC3", name: "CTC0:高速计数器设定值")]
    public async Task<DriverReturnValueModel> Ctc0(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     ATO:数字微调器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("ATO", description: "范围: ATO~AT7", name: "ATO:数字微调器")]
    public async Task<DriverReturnValueModel> At0(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     VMM:字存储器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("VMM", description: "范围: VMO~VM59999", name: "VMM:字存储器")]
    public async Task<DriverReturnValueModel> Vmm(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     unit=0;100:扩展存储器模块
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("unit=0;100", description: "读取扩展模块的数据", name: "unit=0;100:扩展存储器模块")]
    public async Task<DriverReturnValueModel> Unit(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }
}