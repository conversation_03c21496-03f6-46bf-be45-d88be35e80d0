using Common.Enums;

namespace Driver.Core.ReadBase.AllenBradley;

/// <summary>
///     罗克韦尔协议基类
/// </summary>
public class AllenBradleyBase : BasePlcProtocolCollector
{
    [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    /// <summary>
    ///     解析地址
    /// </summary>
    /// <param name="registerAddress"></param>
    /// <param name="dataType">数据类型</param>
    /// <returns></returns>
    [Method("Parse", description: "用于解析AllenBradley的标准地址", name: "Parse")]
    public string Parse(string registerAddress, DataTypeEnum dataType)
    {
        return "Read";
    }

    #region Methods

    /// <summary>
    ///     A
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("A", description: "A9:0/1 或 A9:0.1", name: "A")]
    public async Task<DriverReturnValueModel> A(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     B
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("B", description: "B9:0/1 或 B9:0.1", name: "B")]
    public async Task<DriverReturnValueModel> B(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     N
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("N", description: "N9:0/1 或 N9:0.1", name: "N")]
    public async Task<DriverReturnValueModel> N(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     F
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("F", description: "N9:0/1 或 N9:0.1", name: "F")]
    public async Task<DriverReturnValueModel> F(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     S
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("S", description: "S:0/1 或 S:0.1", name: "S")]
    public async Task<DriverReturnValueModel> S(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     ST
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("ST", description: "", name: "ST")]
    public async Task<DriverReturnValueModel> St(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     C
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C", description: "C9:0/1 或 C9:0.1", name: "C")]
    public async Task<DriverReturnValueModel> C(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     I
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("I", description: "I:0/1 或 I9:0.1", name: "I")]
    public async Task<DriverReturnValueModel> I(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     O
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("O", description: "O:0/1 或 O9:0.1", name: "O")]
    public async Task<DriverReturnValueModel> O(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     R
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("R", description: "R9:0/1 或 R9:0.1", name: "R")]
    public async Task<DriverReturnValueModel> R(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     T
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("T", description: "T9:0/1 或 T9:0.1", name: "T")]
    public async Task<DriverReturnValueModel> T(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     L
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("L", description: "L9:0/1 或 L9:0.1", name: "L")]
    public async Task<DriverReturnValueModel> L(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    #endregion Methods
}

/// <summary>
///     罗克韦尔网口协议
/// </summary>
public class AllenBradleyNetworkDeviceBase : AllenBradleyBase
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 44818;

    #endregion
}