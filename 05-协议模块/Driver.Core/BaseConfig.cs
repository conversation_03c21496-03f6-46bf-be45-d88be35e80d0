using Common.Enums;
using Furion.JsonSerialization;
using HslCommunication.LogNet;

namespace Driver.Core;

/// <summary>
///     cnc协议基类
/// </summary>
public class BaseCncProtocolCollector : BaseDeviceProtocolCollector
{
    [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置", Display = false)]
    public override int WriteInterval { get; set; } = 120;

    [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    /// <summary>
    ///     路径信息
    /// </summary>
    /// <returns></returns>
    [Method("FileRouteInfo", description: "路径信息", name: "FileRouteInfo")]
#pragma warning disable CS1998
    public virtual async Task<object> FileRouteInfo(string path)
#pragma warning restore CS1998
    {
        throw Oops.Oh("暂不支持");
    }

    /// <summary>
    ///     读取程序内容
    /// </summary>
    /// <returns></returns>
    [Method("FileRead", description: "读取程序内容", name: "FileRead")]
#pragma warning disable CS1998
    public virtual async Task<string> FileRead(FileReadInput input)
#pragma warning restore CS1998
    {
        throw Oops.Oh("暂不支持");
    }

    /// <summary>
    ///     删除程序内容
    /// </summary>
    /// <returns></returns>
    [Method("FileDel", description: "删除程序内容", name: "FileDel")]
#pragma warning disable CS1998
    public virtual async Task<bool> FileDel(FileDelInput input)
#pragma warning restore CS1998
    {
        throw Oops.Oh("暂不支持");
    }

    /// <summary>
    ///     设置为主程序
    /// </summary>
    /// <returns></returns>
    [Method("SetCurrentProgram", description: "设置为主程序", name: "SetCurrentProgram")]
#pragma warning disable CS1998
    public virtual async Task<bool> SetCurrentProgram(SetCurrentProgramInput input)
#pragma warning restore CS1998
    {
        throw Oops.Oh("暂不支持");
    }

    /// <summary>
    ///     写入程序内容
    /// </summary>
    /// <returns></returns>
    [Method("FileWrite", description: "写入程序内容", name: "FileWrite")]
#pragma warning disable CS1998
    public virtual async Task<bool> FileWrite(FileWriteInput input)
#pragma warning restore CS1998
    {
        throw Oops.Oh("暂不支持");
    }
}

/// <summary>
///     Plc协议基类
/// </summary>
public class BasePlcProtocolCollector : BaseDeviceProtocolCollector
{
    /// <summary>
    ///     监听属性
    /// </summary>
    [ConfigParameter("监听属性", "高级配置", "等待指定地址的值为设定指定的值,再触发读取设备地址", Display = false, DisplayExpress = "{\"ReadType\": [\"2\"]}", Order = 1, Type = "combine")]
    public string VariableIdentifier { get; set; }

    /// <summary>
    ///     指定的值
    /// </summary>
    [ConfigParameter("指定的值", "高级配置", "设置指定的值,满足后会触发读取设备地址", Display = false, DisplayExpress = "{\"ReadType\": [\"2\"]}", Order = 2)]
    public string VariableValue { get; set; }

    /// <summary>
    ///     刷新数据频率(ms)
    /// </summary>
    [ConfigParameter("刷新数据频率(ms)", "高级配置", "监听属性地址的读取频率,单位(毫秒)", Display = false, DisplayExpress = "{\"ReadType\": [\"2\"]}", Order = 3)]
    public int ReadInterval { get; set; } = 100;

    /// <summary>
    ///     监听超时时间(ms)
    /// </summary>
    [ConfigParameter("监听超时时间(ms)", "高级配置", "等待的超时时间，如果超时时间为-1的话，则是无期限等待", Display = false, DisplayExpress = "{\"ReadType\": [\"2\"]}", Order = 4)]
    public int WaitTimeout { get; set; } = 30_000;

    /// <summary>
    ///     监听等待方法
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public override async Task<WaitAsyncOutput> WaitAsync(WaitAsyncInput val)
    {
        WaitAsyncOutput response = new() { IsSuccess = false };
        try
        {
            if (!IsConnected)
            {
                response.Description = "设备连接已断开";
                return response;
            }

            var wait = new OperateResult<TimeSpan>();
            switch (val.DataType)
            {
                case DataTypeEnum.Bool:
                    wait = await Driver.WaitAsync(val.Address, Convert.ToBoolean(VariableValue), ReadInterval, WaitTimeout);
                    if (!wait.IsSuccess)
                        response.Description = wait.Message;
                    break;
                case DataTypeEnum.Bit:
                    {
                        var value = Convert.ToInt32(VariableValue) == 1;
                        wait = await Driver.WaitAsync(val.Address, Convert.ToBoolean(value), ReadInterval, WaitTimeout);
                        if (!wait.IsSuccess)
                            response.Description = wait.Message;
                        break;
                    }
                case DataTypeEnum.Uint16:
                    wait = await Driver.WaitAsync(val.Address, Convert.ToUInt16(VariableValue), ReadInterval, WaitTimeout);
                    if (!wait.IsSuccess)
                        response.Description = wait.Message;
                    break;
                case DataTypeEnum.Int16:
                    wait = await Driver.WaitAsync(val.Address, Convert.ToInt16(VariableValue), ReadInterval, WaitTimeout);
                    if (!wait.IsSuccess)
                        response.Description = wait.Message;
                    break;
                case DataTypeEnum.Uint32:
                    wait = await Driver.WaitAsync(val.Address, Convert.ToUInt32(VariableValue), ReadInterval, WaitTimeout);
                    if (!wait.IsSuccess)
                        response.Description = wait.Message;
                    break;
                case DataTypeEnum.Int32:
                case DataTypeEnum.Bcd:
                    wait = await Driver.WaitAsync(val.Address, Convert.ToInt32(VariableValue), ReadInterval, WaitTimeout);
                    if (!wait.IsSuccess)
                        response.Description = wait.Message;
                    break;
                case DataTypeEnum.Float:
                case DataTypeEnum.Double:
                case DataTypeEnum.String:
                    wait.IsSuccess = false;
                    response.Description = "暂不支持数据类型";
                    break;
                case DataTypeEnum.Uint64:
                    wait = await Driver.WaitAsync(val.Address, Convert.ToUInt64(VariableValue), ReadInterval, WaitTimeout);
                    if (!wait.IsSuccess)
                        response.Description = wait.Message;
                    break;
                case DataTypeEnum.Int64:
                    wait = await Driver.WaitAsync(val.Address, Convert.ToInt64(VariableValue), ReadInterval, WaitTimeout);
                    if (!wait.IsSuccess)
                        response.Description = wait.Message;
                    break;
                default:
                    response.Description = "暂不支持数据类型";
                    break;
            }

            if (wait.IsSuccess)
            {
                response.Description = "";
                response.IsSuccess = true;
                response.WaitTime = wait.Content;
            }
        }
        catch (Exception ex)
        {
            response.Description = $"订阅失败:{ex.Message},请求数据:{val}";
        }

        return response;
    }
}

/// <summary>
///     公共基类
/// </summary>
public class BaseDeviceProtocolCollector
{
    #region 标准参数

    /// <summary>
    ///     读取模式：1：轮询模式；2：订阅触发模式
    /// </summary>
    [ConfigParameter("读取模式", GroupName = "高级配置", Remark = "轮询模式：主动遍历轮询读取配置采集点\n订阅触发模式：订阅某个地址，达到设置值后在进行读取", Order = 0)]
    public ReadTypeEnum ReadType { get; set; } = ReadTypeEnum.Poll;

    /// <summary>
    ///     反射给协议加载的实体对象
    /// </summary>
    public DriverInfoDto DriverInfo { get; set; }

    /// <summary>
    ///     超时次数
    /// </summary>
    [ConfigParameter("超时次数", GroupName = "高级配置", Remark = "设备所有属性均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备")]
    public virtual int TimeOutCount { get; set; } = 0;

    /// <summary>
    ///     退避时间(m)
    /// </summary>
    [ConfigParameter("退避时间(m)", GroupName = "高级配置", Remark = "当设备连续超时达到设置超时次数时，进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询里")]
    public virtual int BackoffTime { get; set; } = 10;

    /// <summary>
    ///     重连周期
    /// </summary>
    [ConfigParameter("重连周期(s)", GroupName = "高级配置", Remark = "当设备关机后下一轮进行重新连接的时间")]
    public virtual int ReConnTime { get; set; } = 60;

    /// <summary>
    ///     超时时间
    /// </summary>
    [ConfigParameter("超时时间(ms)", GroupName = "高级配置", Remark = "发报文收到响应报文的时间，超出算超时")]
    public virtual int Timeout { get; set; } = 3000;

    /// <summary>
    ///     等待时间
    /// </summary>
    [ConfigParameter("等待时间(s)", GroupName = "高级配置", Remark = "设备每次进行连接前等待的时间，通常针对慢设备")]
    public virtual int WaitTime { get; set; } = 0;

    /// <summary>
    ///     批量读取
    /// </summary>
    [ConfigParameter("批量读取", GroupName = "高级配置", Remark = "不连续报文组成合并包，减少查询包次数，缩短轮询周期")]
    public virtual BoolEnum BulkRead { get; set; } = BoolEnum.True;

    /// <summary>
    ///     写入间隔周期
    /// </summary>
    [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置", Remark = "每个报文的间隔时间，避免数据写入不成功（针对低速设备）")]
    public virtual int WriteInterval { get; set; } = 120;

    #region Modbus...等慢速设备设置

    /// <summary>
    ///     报文间隔(ms)
    /// </summary>
    [ConfigParameter("报文间隔(ms)", GroupName = "高级配置", Remark = "发要查询报文后延时设定时间再发下一条报文（针对低速设备）", Display = false)]
    public virtual int MessageInterval { get; set; }

    #endregion

    #region 轮询模式

    /// <summary>
    ///     轮询周期(ms)
    /// </summary>
    [ConfigParameter("轮询周期(ms)", GroupName = "高级配置", Display = false, DisplayExpress = "{\"ReadType\": [\"1\"]}", Order = 1)]
    public virtual int MinPeriod { get; set; } = 1000;

    #endregion 轮询模式

    #endregion 标准参数

    /// <summary>
    ///     操作是否成功
    /// </summary>
    public OperateResult OperateResult = new();

    /// <summary>
    ///     设备连接状态
    ///     cnc类协议需要重写该方法
    /// </summary>
    public virtual bool IsConnected { get; set; }

    /// <summary>
    ///     采集协议
    /// </summary>
    public virtual dynamic? Driver { get; set; }

    /// <summary>
    ///     关闭连接,串口协议需要自行重写该方法
    /// </summary>
    public virtual void Close()
    {
        OperateResult.Message = "连接已经关闭";
        if (Driver != null)
            Driver.ConnectClose();
    }

    /// <summary>
    ///     释放连接,串口协议需要自行重写该方法
    /// </summary>
    public virtual void Dispose()
    {
        if (Driver != null)
            Driver.Dispose();
    }

    /// <summary>
    ///     读取设备属性,循环单个读取
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public virtual async Task<DriverReturnValueModel> Read(DriverAddressIoArgModel val)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = val.DataType,
            Id = val.Id
        };
        try
        {
            if (IsConnected || DriverInfo.ConnectType is ConnectTypeEnum.SerialPort or ConnectTypeEnum.Udp)
            {
                switch (val.DataType)
                {
                    case DataTypeEnum.Bool:
                        {
                            var boolRead = await Driver.ReadBoolAsync(val.Address);
                            if (!boolRead.IsSuccess)
                            {
                                ret.VariableStatus = VariableStatusTypeEnum.Error;
                                ret.Message = boolRead.Message;
                                ret.ErrorCode = boolRead.ErrorCode;
                            }

                            ret.Value = boolRead.Content;
                            break;
                        }
                    case DataTypeEnum.Bit:
                        {
                            var boolRead = await Driver.ReadBoolAsync(val.Address);
                            if (!boolRead.IsSuccess)
                            {
                                ret.VariableStatus = VariableStatusTypeEnum.Error;
                                ret.Message = boolRead.Message;
                                ret.ErrorCode = boolRead.ErrorCode;
                            }

                            ret.Value = boolRead.Content ? 1 : 0;
                            break;
                        }
                    case DataTypeEnum.Uint16:
                        var uint16Read = await Driver.ReadUInt16Async(val.Address);
                        if (!uint16Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = uint16Read.Message;
                            ret.ErrorCode = uint16Read.ErrorCode;
                        }

                        ret.Value = uint16Read.Content;
                        break;
                    case DataTypeEnum.Int16:
                        var int16Read = await Driver.ReadInt16Async(val.Address);
                        if (!int16Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = int16Read.Message;
                            ret.ErrorCode = int16Read.ErrorCode;
                        }

                        ret.Value = int16Read.Content;
                        break;
                    case DataTypeEnum.Uint32:
                        var uint32Read = await Driver.ReadUInt32Async(val.Address);
                        if (!uint32Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = uint32Read.Message;
                            ret.ErrorCode = uint32Read.ErrorCode;
                        }

                        ret.Value = uint32Read.Content;
                        break;
                    case DataTypeEnum.Int32:
                        var int32Read = await Driver.ReadInt32Async(val.Address);
                        if (!int32Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = int32Read.Message;
                            ret.ErrorCode = int32Read.ErrorCode;
                        }

                        ret.Value = int32Read.Content;
                        break;
                    case DataTypeEnum.Float:
                        var floatRead = await Driver.ReadFloatAsync(val.Address);
                        if (!floatRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = floatRead.Message;
                            ret.ErrorCode = floatRead.ErrorCode;
                        }

                        if (float.IsNaN(floatRead.Content))
                            floatRead.Content = 0;
                        ret.Value = floatRead.Content;
                        break;
                    case DataTypeEnum.Uint64:
                        var uint64Read = await Driver.ReadUInt64Async(val.Address);
                        if (!uint64Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = uint64Read.Message;
                            ret.ErrorCode = uint64Read.ErrorCode;
                        }

                        ret.Value = uint64Read.Content;
                        break;
                    case DataTypeEnum.Int64:
                        var int64Read = await Driver.ReadInt64Async(val.Address);
                        if (!int64Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = int64Read.Message;
                            ret.ErrorCode = int64Read.ErrorCode;
                        }

                        ret.Value = int64Read.Content;
                        break;
                    case DataTypeEnum.Double:
                        var doubleRead = await Driver.ReadDoubleAsync(val.Address);
                        if (!doubleRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = doubleRead.Message;
                            ret.ErrorCode = doubleRead.ErrorCode;
                        }

                        if (double.IsNaN(doubleRead.Content))
                            doubleRead.Content = 0;
                        ret.Value = doubleRead.Content;
                        break;
                    case DataTypeEnum.String:
                        {
                            OperateResult<string> stringRead;
                            // 字符串默认编码
                            val.Length = val.Length == 0 ? val.Length = 10 : val.Length;
                            switch (val.Encoding)
                            {
                                case StringEnum.Unicode:
                                    stringRead = await Driver.ReadStringAsync(val.Address, val.Length, Encoding.Unicode);
                                    break;
                                case StringEnum.Ascii:
                                    stringRead = await Driver.ReadStringAsync(val.Address, val.Length, Encoding.ASCII);
                                    break;
                                case StringEnum.UnicodeBig:
                                    stringRead = await Driver.ReadStringAsync(val.Address, val.Length,
                                        Encoding.BigEndianUnicode);
                                    break;
                                case StringEnum.Utf32:
                                    stringRead = await Driver.ReadStringAsync(val.Address, val.Length, Encoding.UTF32);
                                    break;
                                case StringEnum.Gb2312:
                                    stringRead = await Driver.ReadStringAsync(val.Address, val.Length,
                                        Encoding.GetEncoding("gb2312"));
                                    break;
                                case StringEnum.Utf8:
                                default:
                                    stringRead = await Driver.ReadStringAsync(val.Address, val.Length, Encoding.UTF8);
                                    break;
                            }

                            if (!stringRead.IsSuccess)
                            {
                                ret.VariableStatus = VariableStatusTypeEnum.Error;
                                ret.Message = stringRead.Message;
                                ret.ErrorCode = stringRead.ErrorCode;
                            }

                            ret.Value = stringRead.Content;
                            break;
                        }
                    case DataTypeEnum.Bcd:
                        {
                            OperateResult<byte[]> bcdRead = await Driver.ReadAsync(val.Address, 1);
                            if (!bcdRead.IsSuccess)
                            {
                                ret.VariableStatus = VariableStatusTypeEnum.Error;
                                ret.Message = bcdRead.Message;
                                ret.ErrorCode = bcdRead.ErrorCode;
                            }

                            try
                            {
                                ret.Value = Convert.ToInt32(bcdRead.Content.ToHexString());
                            }
                            catch
                            {
                                ret.Value = bcdRead.Content.ToHexString();
                            }

                            break;
                        }
                    case DataTypeEnum.Bcd_32:
                        {
                            OperateResult<byte[]> bcdRead = await Driver.ReadAsync(val.Address, 2);
                            if (!bcdRead.IsSuccess)
                            {
                                ret.VariableStatus = VariableStatusTypeEnum.Error;
                                ret.Message = bcdRead.Message;
                                ret.ErrorCode = bcdRead.ErrorCode;
                            }
                            var sortContent = ConvertAbBcdToCDab(bcdRead.Content);
                            ret.Value = Convert.ToInt32(sortContent.ToHexString());

                            break;
                        }
                    default:
                        throw new ArgumentOutOfRangeException();
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "设备未连接";
                ret.ErrorCode = -1;
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }


    /// <summary>
    /// ABCD转CDAB
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public static byte[] ConvertAbBcdToCDab(byte[] input)
    {
        // 检查输入数组长度是否为 4
        if (input == null || input.Length != 4)
        {
            throw new ArgumentException("输入数组长度必须为 4");
        }

        // 创建新的数组并重新排列元素
        byte[] result = new byte[4];
        result[0] = input[2]; // c
        result[1] = input[3]; // d
        result[2] = input[0]; // a
        result[3] = input[1]; // b

        return result;
    }

    /// <summary>
    ///     实时调试
    /// </summary>
    /// <param name="addr">地址</param>
    /// <param name="dataType">数据类型</param>
    /// <param name="length">长度</param>
    /// <param name="encoding">字符串编码</param>
    /// <returns></returns>
    [Method("Debug", description: "实时调试读取", name: "Debug")]
    public virtual async Task<object?> Debug(string addr, DataTypeEnum dataType, byte length = 10, StringEnum encoding = StringEnum.Utf8)
    {
        var reqModel = new DriverAddressIoArgModel(DriverInfo.DeviceId, addr, length, dataType, encoding: encoding);
        var read = await Read(reqModel);
        if (read.ErrorCode > 0)
            throw Oops.Oh(read.Message);
        return read.Value;
    }

    /// <summary>
    ///     写入设备属性
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("WriteAsync", description: "调试写入", name: "WriteAsync")]
    public virtual async Task<WriteResponse> WriteAsync(DriverAddressIoArgModel val)
    {
        WriteResponse response = new() { IsSuccess = false };
        try
        {
            if (!IsConnected)
            {
                response.Description = "设备连接已断开";
                return response;
            }

            var write = new OperateResult();
            switch (val.DataType)
            {
                case DataTypeEnum.Bool:
                    write = await Driver!.WriteAsync(val.Address, Convert.ToBoolean(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Bit:
                    {
                        var value = Convert.ToInt32(val.Value) == 1;
                        write = await Driver!.WriteAsync(val.Address, value);
                        if (!write.IsSuccess)
                            response.Description = write.Message;
                        break;
                    }
                case DataTypeEnum.Uint16:
                    write = await Driver!.WriteAsync(val.Address, Convert.ToUInt16(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Int16:
                    write = await Driver!.WriteAsync(val.Address, Convert.ToInt16(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Uint32:
                    write = await Driver!.WriteAsync(val.Address, Convert.ToUInt32(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Int32:
                case DataTypeEnum.Bcd:
                    write = await Driver!.WriteAsync(val.Address, Convert.ToInt32(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Float:
                    write = await Driver!.WriteAsync(val.Address, Convert.ToSingle(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Uint64:
                    write = await Driver!.WriteAsync(val.Address, Convert.ToUInt64(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Int64:
                    write = await Driver!.WriteAsync(val.Address, Convert.ToInt64(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Double:
                    write = await Driver!.WriteAsync(val.Address, Convert.ToDouble(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.String:
                    {
                        var strValue = val.Value ?? "";
                        // 将字符串转换为字节数组
                        var byteArray = Encoding.UTF8.GetBytes(strValue);
                        // 如果字节数组长度小于固定长度，进行填充操作
                        if (byteArray.Length < val.Length * 2)
                        {
                            Array.Resize(ref byteArray, val.Length * 2);
                            write = await Driver!.WriteAsync(val.Address, byteArray);
                        }

                        // 注意: 该处写入和上面填充写入不冲突
                        switch (val.Encoding)
                        {
                            case StringEnum.Unicode:
                                write = await Driver!.WriteAsync(val.Address, strValue, val.Length, Encoding.Unicode);
                                break;
                            case StringEnum.Ascii:
                                write = await Driver!.WriteAsync(val.Address, strValue, val.Length, Encoding.ASCII);
                                break;
                            case StringEnum.UnicodeBig:
                                write = await Driver!.WriteAsync(val.Address, strValue, val.Length, Encoding.BigEndianUnicode);
                                break;
                            case StringEnum.Utf32:
                                write = await Driver!.WriteAsync(val.Address, strValue, val.Length, Encoding.UTF32);
                                break;
                            case StringEnum.Gb2312:
                                write = await Driver!.WriteAsync(val.Address, strValue, val.Length, Encoding.GetEncoding("gb2312"));
                                break;
                            case StringEnum.Utf8:
                            default:
                                write = await Driver!.WriteAsync(val.Address, strValue, val.Length, Encoding.UTF8);
                                break;
                        }

                        if (!write.IsSuccess)
                            response.Description = write.Message;
                        break;
                    }
                default:
                    response.Description = "数据类型不支持写入";
                    break;
            }

            // Log.Information($"【写入设备】 地址:【{val.Address}】,值:【{Convert.ToString(val.Value)}】,结果:【{JSON.Serialize(write)}】");
            if (write.IsSuccess)
            {
                response.Description = "写入成功！";
                response.IsSuccess = true;
            }
        }
        catch (Exception ex)
        {
            response.Description = $"写入失败:{ex.Message},请求数据:{val}";
        }

        return response;
    }

    /// <summary>
    ///     监听等待方法
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public virtual async Task<WaitAsyncOutput> WaitAsync(WaitAsyncInput val)
    {
        throw Oops.Oh("暂不支持");
    }

    /// <summary>
    ///     推送Socket消息
    /// </summary>
    /// <param name="e"> hsl报文回调</param>
    /// <returns></returns>
    protected async Task SocketSend(HslEventArgs e)
    {
        // 提取时间戳、协议类型和IP地址信息
        string messageContent = e.HslMessage.ToString();
        await DriverInfo.Socket.Send(messageContent, DriverInfo.DeviceId + "_Logs");
    }

    /// <summary>
    ///     返回连接结果
    /// </summary>
    /// <returns></returns>
    protected DeviceConnectDto ResConnect()
    {
        try
        {
            return OperateResult.IsSuccess
                ? new DeviceConnectDto { Message = $"连接成功:【{OperateResult.Message}】" }
                : new DeviceConnectDto { Message = $"连接失败:【{OperateResult.Message}】" };
        }
        catch (Exception e)
        {
            return new DeviceConnectDto { Message = $"连接失败:【{OperateResult.Message}】,Error:【{e.Message}】" };
        }
    }
}

/// <summary>
///     读取模式：1：轮询模式；2：订阅触发模式
/// </summary>
public enum ReadTypeEnum
{
    /// <summary>
    ///     轮询模式
    /// </summary>
    [Description("轮询模式")] Poll = 1,

    /// <summary>
    ///     订阅触发模式
    /// </summary>
    [Description("订阅触发模式")] Subscription = 2
}