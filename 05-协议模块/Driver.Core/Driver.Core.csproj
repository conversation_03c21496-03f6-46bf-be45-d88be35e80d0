<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
        <OutputPath>..\..\..\..\..\01-应用服务\IotGateway/bin/$(Configuration)/$(TargetFramework)/drivers/plc/</OutputPath>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="HslCommunication" Version="12.3.2" />
        <PackageReference Include="SharpPcap" Version="6.3.3" />
        <PackageReference Include="SimpleTCP.Core" Version="1.0.4"/>
        <PackageReference Include="System.IO.Ports" Version="7.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\04-架构核心\IotGateway.Common\IotGateway.Common.csproj"/>
        <ProjectReference Include="..\..\04-架构核心\IotGateway.WebSocket\IotGateway.WebSocket.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Enum"/>
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="ReadBase\Modbus\ModbusAlgorithmTest.cs" />
      <Compile Remove="ReadBase\Modbus\ModbusTestAddresses.cs" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="ReadBase\Modbus\README_ModbusOptimization.md" />
    </ItemGroup>

</Project>
