namespace Driver.Core.Attributes;

/// <summary>
/// </summary>
[AttributeUsage(AttributeTargets.Property)]
public class ConfigParameterAttribute : Attribute
{
    public ConfigParameterAttribute(string description, string groupName = "连接配置", string remark = "", bool display = true,
        bool required = true, string displayExpress = "", short order = 99,string type="text")
    {
        Description = description;
        Remark = remark;
        GroupName = groupName;
        Display = display;
        Required = required;
        DisplayExpress = displayExpress;
        Order = order;
        Type = type;
    }


    public string Description { get; }

    /// <summary>
    ///     分组名称
    /// </summary>
    public string GroupName { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    ///     是否显示
    /// </summary>
    public bool Display { get; set; }

    /// <summary>
    ///     字段显示条件表达式
    /// </summary>
    public string DisplayExpress { get; set; }

    /// <summary>
    ///     是否必填
    /// </summary>
    public bool Required { get; set; }
    
    /// <summary>
    ///     排序,越小越在前
    /// </summary>
    public short Order { get; set; }
    
    /// <summary>
    /// 输入框类型
    /// </summary>
    public string Type { get; set; }
}