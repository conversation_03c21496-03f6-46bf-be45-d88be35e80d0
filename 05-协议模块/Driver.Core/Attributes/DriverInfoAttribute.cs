namespace Driver.Core.Attributes;

/// <summary>
/// </summary>
[AttributeUsage(AttributeTargets.Class, Inherited = false)]
public class DriverInfoAttribute : Attribute
{
    public DriverInfoAttribute(string name, string version, string typeName = "公共协议")
    {
        Name = name;
        Version = version;
        TypeName = typeName;
    }

    /// <summary>
    ///     协议名称
    /// </summary>
    public string Name { get; }

    /// <summary>
    ///     版本号
    /// </summary>
    public string Version { get; }

    /// <summary>
    ///     分类名称
    /// </summary>
    public string TypeName { get; }
}