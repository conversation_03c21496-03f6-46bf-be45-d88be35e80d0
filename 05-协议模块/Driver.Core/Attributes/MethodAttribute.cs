using Common.Enums;

namespace Driver.Core.Attributes;

/// <summary>
/// </summary>
[AttributeUsage(AttributeTargets.Method, Inherited = false)]
public class MethodAttribute : Attribute
{
    /// <summary>
    /// </summary>
    /// <param name="identifier">标识</param>
    /// <param name="dataType">数据类型</param>
    /// <param name="protectType">读写类型</param>
    /// <param name="description">描述</param>
    /// <param name="name">方法名称</param>
    /// <param name="filter">是否过滤方法</param>
    /// <param name="filterField">根据什么字段过滤方法</param>
    /// <param name="filterValue">根据值过滤方法</param>
    public MethodAttribute(string identifier = "Read", TransPondDataTypeEnum dataType = TransPondDataTypeEnum.Int, ProtectTypeEnum protectType = ProtectTypeEnum.ReadOnly,
        string description = "", string name = "读地址", bool filter = false, string filterField = "", string filterValue = "")
    {
        Identifier = identifier;
        ProtectType = protectType;
        Description = description;
        DataType = dataType;
        Name = name;
        Filter = filter;
        FilterField = filterField;
        FilterValue = filterValue;
    }

    /// <summary>
    /// 标识
    /// </summary>
    public string Identifier { get; }
    
    /// <summary>
    /// 
    /// </summary>
    public ProtectTypeEnum ProtectType { get; }

    public string Name { get; }

    public TransPondDataTypeEnum DataType { get; }

    public string Description { get; }

    /// <summary>
    ///     方法过滤
    /// </summary>
    public bool Filter { get; set; }

    /// <summary>
    ///     过滤字段
    /// </summary>
    public string FilterField { get; set; }

    /// <summary>
    ///     过滤值
    /// </summary>
    public string FilterValue { get; set; }
}