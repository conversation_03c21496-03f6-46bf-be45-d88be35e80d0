global using System.ComponentModel;
global using Furion.FriendlyException;
global using HslCommunication;
global using Newtonsoft.Json;
global using Newtonsoft.Json.Converters;
global using Driver.Core.Write.Dto;
global using System;
global using Feng.Common.Extension;
global using System.IO.Ports;
global using System.Text;
global using Driver.Core.Attributes;
global using Driver.Core.Models;
global using Driver.Core.ReadBase.Modbus.Dto;
global using Furion.Logging;
global using HslCommunication.Core;
global using Feng.IotGateway.WebSocket;