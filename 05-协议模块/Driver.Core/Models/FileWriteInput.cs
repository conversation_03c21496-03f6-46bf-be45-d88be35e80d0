using System.ComponentModel.DataAnnotations;
using DateTime = System.DateTime;

namespace Driver.Core.Models;

/// <summary>
///     向设备写入程序内容
/// </summary>
public class FileWriteInput
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [Required]
    [Description("设备名称")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     写入内容
    /// </summary>
    [Required]
    [Description("写入内容")]
    public string Content { get; set; }

    /// <summary>
    ///     路径
    /// </summary>
    [Required]
    [Description("文件路径")]
    public string Path { get; set; }

    /// <summary>
    /// 文件名称
    /// </summary>
    [Required]
    [Description("程序号/文件名称")]
    public string FileName { get; set; }
}

/// <summary>
///     向设备写入程序-来源网关本地
/// </summary>
public class FileWriteByFileInput
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [Required]
    [Description("设备名称")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     文件名称
    /// </summary>
    [Required]
    [Description("文件名称")]
    public string FileName { get; set; }

    /// <summary>
    ///     路径
    /// </summary>
    [Required]
    [Description("文件路径")]
    public string Path { get; set; }
}

/// <summary>
/// 开放Api-文件下发到机台
/// </summary>
public class OpenApiFileWriteInput 
{
    /// <summary>
    ///     应用Id
    /// </summary>
    [Required]
    [Description("应用Id")]
    public long AppId { get; set; }
    
    /// <summary>
    ///     设备名称
    /// </summary>
    [Required]
    [Description("设备名称")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     文件名称
    /// </summary>
    [Required]
    [Description("文件名称")]
    public string FileName { get; set; }

    /// <summary>
    ///     路径
    /// </summary>
    [Required]
    [Description("文件路径")]
    public string Path { get; set; }
}

/// <summary>
///     读取设备程序内容
/// </summary>
public class FileReadInput
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [Required]
    [Description("设备名称")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     程序路径
    /// </summary>
    [Required]
    [Description("程序路径")]
    public string Path { get; set; }

    /// <summary>
    ///     程序号/文件名称
    /// </summary>
    [Required]
    [Description("程序号/文件名称")]
    public string FileName { get; set; }
}

public class OpenApiFileReadInput : FileReadInput
{
    /// <summary>
    ///     应用Id
    /// </summary>
    [Required]
    [Description("应用Id")]
    public long AppId { get; set; }
}

/// <summary>
/// </summary>
public class FileRouteInfoOutput
{
    /// <summary>
    ///     名称
    /// </summary>
    [Description("文件名称")]
    public string Name { get; set; }

    /// <summary>
    ///     是否是文件夹
    /// </summary>
    [Description("是否是文件夹")]
    public bool IsDirectory { get; set; }

    /// <summary>
    ///     最后一次更新时间，当为文件的时候有效
    /// </summary>
    [Description("最后一次更新时间")]
    public DateTime LastModified { get; set; }

    /// <summary>
    ///     路径
    /// </summary>
    [Description("文件路径")]
    public string Path { get; set; }

    /// <summary>
    ///     文件的大小，当为文件的时候有效
    /// </summary>
    [Description("文件的大小")]
    public int Size { get; set; }

    /// <summary>
    ///     子项文件
    /// </summary>
    [Description("子项文件")]
    public List<FileRouteInfoOutput> Children { get; set; }
}

/// <summary>
///     程序删除
/// </summary>
public class FileDelInput
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [Required]
    [Description("设备名称")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     文件路径
    /// </summary>
    [Required]
    [Description("程序路径")]
    public string Path { get; set; }
    
    /// <summary>
    /// 文件名称
    /// </summary>
    [Required]
    [Description("程序号/文件名称")]
    public string FileName { get; set; }
}

public class OpenApiFileDelInput : FileDelInput
{
    /// <summary>
    ///     应用Id
    /// </summary>
    [Required]
    [Description("应用Id")]
    public long AppId { get; set; }
}

/// <summary>
///     设置为主程序
/// </summary>
public class SetCurrentProgramInput
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [Required]
    [Description("设备名称")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     程序号/文件名称
    /// </summary>
    [Required]
    [Description("程序号/文件名称")]
    public string Name { get; set; }
}

public class OpenApiSetCurrentProgramInput : SetCurrentProgramInput
{
    /// <summary>
    ///     应用Id
    /// </summary>
    [Required]
    [Description("应用Id")]
    public long AppId { get; set; }
}

/// <summary>
///     读取设备程序底层路径信息请求参数
/// </summary>
public class DeviceFileRouteInfoInput
{
    /// <summary>
    ///     设备名称
    /// </summary>
    [Required]
    [Description("设备名称")]
    public string DeviceName { get; set; }
}

/// <summary>
///     读取设备程序底层路径信息请求参数
/// </summary>
public class OpenApiDeviceFileRouteInfoInput : DeviceFileRouteInfoInput
{
    /// <summary>
    ///     应用Id
    /// </summary>
    [Required]
    [Description("应用Id")]
    public long AppId { get; set; }
}