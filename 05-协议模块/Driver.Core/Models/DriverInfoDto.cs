using Common.Enums;

namespace Driver.Core.Models;

/// <summary>
///     反射给协议加载的实体对象
/// </summary>
public class DriverInfoDto
{
    /// <summary>
    /// </summary>
    public SendMessageService Socket { get; set; }

    /// <summary>
    ///     设备Id
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    ///     设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    ///     协议类型
    /// </summary>
    public ConnectTypeEnum ConnectType { get; set; }
}