using System.Collections.Concurrent;
using Furion.LinqBuilder;

namespace Driver.Core.Models;

// 当value发生改变时触发事件
public delegate void ValueChangedEventHandler(string key, object? newValue, object? oldValue);

// 当时间发生改变时候触发事件
public delegate void TimeChangedEventHandler(string key, long newTime, long oldTime);

// 订阅值发生改变时候且满足条件触发事件
public delegate void CheckSubscriptionConditionEventHandler(string key, object? newValue, long newTime);

// 当调用UpdateData时候触发事件
public delegate void DataUpdatedEventHandler(string key, object? newValue, long newTime);

/// <summary>
///     实时数据存储
/// </summary>
public class DataStorage
{
    private readonly ConcurrentDictionary<string, PropertyStorage> _data = new();
    private readonly ConcurrentDictionary<string, List<ValueChangedEventHandler>> _valueSubscribers = new();
    private readonly ConcurrentDictionary<string, List<TimeChangedEventHandler>> _timeSubscribers = new();
    private readonly ConcurrentDictionary<string, List<CheckSubscriptionConditionEventHandler>> _checkConditionSubscribers = new();
    public event DataUpdatedEventHandler DataUpdated;

    private static readonly Lazy<DataStorage> instance = new(() => new DataStorage());
    public static DataStorage Instance => instance.Value;

    /// <summary>
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public PropertyStorage? GetData(string key)
    {
        _data.TryGetValue(key, out var property);
        return property;
    }

    /// <summary>
    /// </summary>
    /// <param name="deviceName"></param>
    /// <returns></returns>
    public PropertyStorage? Get(string deviceName)
    {
        return _data.TryGetValue(deviceName, out var value) ? value : null;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public ConcurrentDictionary<string, PropertyStorage?> All()
    {
        return _data;
    }

    /// <summary>
    /// </summary>
    /// <param name="key"></param>
    /// <param name="input"></param>
    public void Set(string key, DriverReturnValueModel input)
    {
        if (!_data.ContainsKey(key))
        {
            _data.TryAdd(key, new PropertyStorage
            {
                Key = key,
                Identifier = input.Identifier,
                DataType = input.DataType,
                Name = input.Name,
                Message = input.Message,
                ErrorCode = input.ErrorCode,
                TransitionType = input.TransitionType,
                VariableStatus = input.VariableStatus,
                Value = input.Value,
                ReadTime = input.ReadTime
            });
        }
        else
        {
            if (_data.TryGetValue(key, out var property))
            {
                property.Identifier = input.Identifier;
                property.DataType = input.DataType;
                property.Name = input.Name;
                property.Message = input.Message;
                property.ErrorCode = input.ErrorCode;
                property.TransitionType = input.TransitionType;
                property.VariableStatus = input.VariableStatus;
                property.Value = input.Value;
                property.ReadTime = input.ReadTime;
                property.CookieTime = input.CookieTime;
                property.CookieValue = input.CookieValue;
            }
        }

        OnDataUpdated(key, input.Value, input.ReadTime);
    }

    public void Remove(string key)
    {
        _data.TryRemove(key, out _);
        _valueSubscribers.TryRemove(key, out _);
    }

    public bool Contains(string key)
    {
        return _data.ContainsKey(key);
    }

    public void SubscribeValueChangedEvent(string key, ValueChangedEventHandler handler)
    {
        if (!_valueSubscribers.ContainsKey(key)) _valueSubscribers.TryAdd(key, new List<ValueChangedEventHandler>());

        _valueSubscribers[key].Add(handler);
    }

    public void UnsubscribeValueChangedEvent(string key, ValueChangedEventHandler handler)
    {
        if (_valueSubscribers.TryGetValue(key, out var subscriber))
            subscriber.Remove(handler);
    }

    public void SubscribeTimeChangedEvent(string key, TimeChangedEventHandler handler)
    {
        if (!_timeSubscribers.ContainsKey(key)) _timeSubscribers.TryAdd(key, new List<TimeChangedEventHandler>());

        _timeSubscribers[key].Add(handler);
    }

    public void UnsubscribeTimeChangedEvent(string key, TimeChangedEventHandler handler)
    {
        if (_timeSubscribers.TryGetValue(key, out var subscriber)) subscriber.Remove(handler);
    }

    public void SubscribeCheckSubscriptionConditionEvent(string key, CheckSubscriptionConditionEventHandler handler)
    {
        if (!_checkConditionSubscribers.ContainsKey(key)) _checkConditionSubscribers.TryAdd(key, new List<CheckSubscriptionConditionEventHandler>());

        _checkConditionSubscribers[key].Add(handler);
    }

    public void UnsubscribeCheckSubscriptionConditionEvent(string key, CheckSubscriptionConditionEventHandler handler)
    {
        if (_checkConditionSubscribers.TryGetValue(key, out var subscriber)) subscriber.Remove(handler);
    }

    /// <summary>
    ///     调用UpdateData触发事件
    /// </summary>
    /// <param name="key"></param>
    /// <param name="newValue"></param>
    /// <param name="newTime"></param>
    private void OnDataUpdated(string key, object? newValue, long newTime)
    {
        DataUpdated?.Invoke(key, newValue, newTime);
    }

    /// <summary>
    ///     实际存储数据
    /// </summary>
    public class PropertyStorage : DriverReturnValueModel
    {
        private object? _value;

        private long _readTime;

        /// <summary>
        /// </summary>
        [JsonIgnore]
        public string Key { get; set; }

        public new object? Value
        {
            get => _value;
            set
            {
                if (value?.ToString() != _value?.ToString())
                {
                    var oldValue = _value;
                    _value = value;
                    NotifyValueChanged(Key, value, oldValue);
                }

                CheckSubscriptionCondition(Key, value, _readTime);
            }
        }

        private void NotifyValueChanged(string key, object? newValue, object? oldValue)
        {
            if (key.IsNullOrEmpty())
                return;
            List<ValueChangedEventHandler> handlers;
            if (Instance._valueSubscribers.TryGetValue(key, out handlers))
                foreach (var handler in handlers)
                    handler.Invoke(key, newValue, oldValue);
        }

        private void NotifyTimeChanged(string key, long newTime, long oldTime)
        {
            if (key.IsNullOrEmpty())
                return;
            List<TimeChangedEventHandler> handlers;
            if (Instance._timeSubscribers.TryGetValue(key, out handlers))
                foreach (var handler in handlers)
                    handler.Invoke(key, newTime, oldTime);
        }

        private void CheckSubscriptionCondition(string key, object? newValue, long newTime)
        {
            if (key.IsNullOrEmpty())
                return;
            List<CheckSubscriptionConditionEventHandler> handlers;
            if (Instance._checkConditionSubscribers.TryGetValue(key, out handlers))
                foreach (var handler in handlers)
                    handler.Invoke(key, newValue, newTime);
        }

        /// <summary>
        ///     上一次的值
        /// </summary>
        public object? CookieValue { get; set; }

        public new long ReadTime
        {
            get => _readTime;
            set
            {
                if (value != _readTime)
                {
                    var oldTime = _readTime;
                    _readTime = value;
                    NotifyTimeChanged(Key, value, oldTime);
                }
            }
        }
    }
}