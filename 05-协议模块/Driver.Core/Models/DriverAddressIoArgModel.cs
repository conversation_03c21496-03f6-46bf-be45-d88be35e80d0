using Common.Enums;

namespace Driver.Core.Models;

/// <summary>
/// </summary>
public class DriverAddressIoArgModel
{
    /// <summary>
    /// </summary>
    /// <param name="id"></param>
    /// <param name="address"></param>
    /// <param name="length"></param>
    /// <param name="dataType"></param>
    /// <param name="value"></param>
    /// <param name="method">读取方法</param>
    /// <param name="encoding"></param>
    public DriverAddressIoArgModel(long id, string address, ushort length, DataTypeEnum dataType, string value = "", string method = "read", StringEnum encoding = StringEnum.Utf8)
    {
        Id = id;
        Address = address;
        Length = length;
        DataType = dataType;
        Value = value;
        Method = method;
        Encoding = encoding;
    }

    /// <summary>
    ///     下写值
    /// </summary>
    public string? Value { get; set; }

    /// <summary>
    ///     点位Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    ///     长度
    /// </summary>
    public ushort Length { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    public DataTypeEnum DataType { get; set; }

    /// <summary>
    ///     读取方法
    /// </summary>
    public string Method { get; set; }

    /// <summary>
    ///     字符串编码
    /// </summary>
    public StringEnum Encoding { get; set; }

    public override string ToString()
    {
        return $"变量ID:{Id},Address:{Address},Value:{Value},ValueType:{DataType},Length:{Length}";
    }
}


/// <summary>
///  等待监听方法-请求参数
/// </summary>
public class WaitAsyncInput
{
    /// <summary>
    ///     数据类型
    /// </summary>
    public DataTypeEnum DataType { get; set; }
    
    /// <summary>
    ///     地址
    /// </summary>
    public string Address { get; set; }
}

/// <summary>
/// 等待监听方法-返回参数
/// </summary>
public class WaitAsyncOutput
{
    /// <summary>
    ///     操作是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    ///     返回消息
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    ///     等待了多长时间
    /// </summary>
    public TimeSpan WaitTime { get; set; }
}