using Furion.JsonSerialization;

namespace Driver.Core.Models;

/// <summary>
///     设备操作下写
/// </summary>
public class DeviceWriteRequest
{
    /// <summary>
    ///     设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// </summary>
    public Dictionary<string, string> Params { get; set; } = new();

    /// <summary>
    ///     表达式处理，默认支持
    /// </summary>
    public bool Eval { get; set; } = true;

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public override string ToString()
    {
        return $"DeviceName:{DeviceName},Params:{JSON.Serialize(Params)}";
    }
}