using System.Collections.Concurrent;

namespace Driver.Core.Models;

/// <summary>
///     自定义字典
/// </summary>
/// <typeparam name="TK<PERSON>"></typeparam>
/// <typeparam name="TValue"></typeparam>
public class ObservableDictionary<TKey, TValue> : ConcurrentDictionary<TKey, TValue>, IDictionary<TKey, TValue>
{
    public event Action<TKey, TValue> ItemAdded;
    public event Action<TKey, TValue> ItemUpdated;
    public event Action<TKey> ItemRemoved;
    public event Action ClearAll;

    public new TValue this[TKey key]
    {
        get => base[key];
        set
        {
            base[key] = value;
            OnItemUpdated(key, value);
        }
    }

    public new void TryAdd(TKey key, TValue value)
    {
        base.TryAdd(key, value);
        OnItemAdded(key, value);
    }

    public new void TryRemove(TKey key, out TValue? value)
    {
        base.TryRemove(key, out value);
        OnItemRemoved(key);
    }

    public new void Clear()
    {
        base.Clear();
        OnClearAll();
    }

    protected virtual void OnItemAdded(TK<PERSON> key, TValue value)
    {
        ItemAdded?.Invoke(key, value);
    }

    protected virtual void OnItemUpdated(TKey key, TValue value)
    {
        ItemUpdated?.Invoke(key, value);
    }

    protected virtual void OnItemRemoved(TKey key)
    {
        ItemRemoved?.Invoke(key);
    }

    protected virtual void OnClearAll()
    {
        ClearAll?.Invoke();
    }
}