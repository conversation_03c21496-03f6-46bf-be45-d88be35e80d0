using Common.Enums;
using DateTime = Common.Extension.DateTime;

namespace Driver.Core.Models;

/// <summary>
/// </summary>
public class DriverReturnValueModel
{
    /// <summary>
    /// </summary>
    public  object? Value { get; set; }

    /// <summary>
    ///     上一次的值
    /// </summary>
    public object? CookieValue { get; set; }

    /// <summary>
    ///     上一次上报时间
    /// </summary>
    public long CookieTime { get; set; }
    
    /// <summary>
    ///     信息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// code小于0就是设备关机
    /// </summary>
    public int ErrorCode { get; set; }

    /// <summary>
    ///     读取结果类型
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public VariableStatusTypeEnum VariableStatus { get; set; } = VariableStatusTypeEnum.Good;

    /// <summary>
    ///     数据类型
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public DataTypeEnum DataType { get; set; } = DataTypeEnum.String;

    /// <summary>
    ///     属性Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     属性标识符
    /// </summary>
    public string Identifier { get; set; }

    /// <summary>
    ///     属性名称
    /// </summary>
    public string Name { get; set; }
    
    /// <summary>
    /// 读取时间
    /// </summary>
    public  long ReadTime { get; set; } = DateTime.ToLong(DateTime.Now());
    
    /// <summary>
    ///     转换数据类型
    /// </summary>
    public TransPondDataTypeEnum TransitionType { get; set; }
}