namespace Driver.Core.Enum;

/// <summary>
///     报警类型
/// </summary>
public enum SysAlarmType
{
    /// <summary>
    ///     SW
    /// </summary>
    [Description("SW")] Sw = 0,

    /// <summary>
    ///     PW
    /// </summary>
    [Description("PW")] Pw = 1,

    /// <summary>
    ///     IO
    /// </summary>
    [Description("IO")] Io = 2,

    /// <summary>
    ///     PS
    /// </summary>
    [Description("PS")] Ps = 3,

    /// <summary>
    ///     OT
    /// </summary>
    [Description("OT")] Ot = 4,

    /// <summary>
    ///     OH
    /// </summary>
    [Description("OH")] Oh = 5,

    /// <summary>
    ///     SV
    /// </summary>
    [Description("SV")] Sv = 6,

    /// <summary>
    ///     SR
    /// </summary>
    [Description("SR")] Sr = 7,

    /// <summary>
    ///     MC
    /// </summary>
    [Description("MC")] Mc = 8,

    /// <summary>
    ///     SP
    /// </summary>
    [Description("SP")] Sp = 9,

    /// <summary>
    ///     DS
    /// </summary>
    [Description("DS")] Ds = 10,

    /// <summary>
    ///     IE
    /// </summary>
    [Description("IE")] Ie = 11,

    /// <summary>
    ///     BG
    /// </summary>
    [Description("BG")] Bg = 12,

    /// <summary>
    ///     SN
    /// </summary>
    [Description("SN")] Sn = 13,

    /// <summary>
    ///     EX
    /// </summary>
    [Description("EX")] Ex = 15
}