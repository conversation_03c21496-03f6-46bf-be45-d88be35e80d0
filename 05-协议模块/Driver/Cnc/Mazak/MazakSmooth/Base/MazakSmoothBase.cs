using System;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace MazakSmooth.Base;

/// <summary>
///     一个马扎克CNC的机床通讯库，目前仅限于测试smooth
/// </summary>
public class MazakSmoothBase : NetworkDoubleBase
{
    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }

    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public MazakSmoothBase(string ipAddress, int port = 50100)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    /// <inheritdoc />
    // protected override INetMessage GetNewNetMessage() => new CNCFanucSeriesMessage();
    protected override OperateResult InitializationOnConnect(Socket socket)
    {
        var read1 = Receive(socket, 12, ReceiveTimeOut);
        if (!read1.IsSuccess) return read1;

        return OperateResult.CreateSuccessResult();
    }

    public override OperateResult<byte[]> ReadFromCoreServer(Socket socket, byte[] send, bool hasResponseData = true, bool usePackHeader = true)
    {
        // send
        var sendResult = Send(socket, send);
        if (!sendResult.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(sendResult);

        if (ReceiveTimeOut < 0) return OperateResult.CreateSuccessResult(new byte[0]);


        OperateResult<byte[]> resultReceive;

        switch (send[0])
        {
            case 0x2a: //主轴信息（坐标、负载）
                resultReceive = Receive(socket, 3296, ReceiveTimeOut);
                if (!resultReceive.IsSuccess) return resultReceive;
                break;
            case 0x2b: //程序信息
                resultReceive = Receive(socket, 2696, ReceiveTimeOut);
                if (!resultReceive.IsSuccess) return resultReceive;
                break;
            case 0x29: //进给轴信息（坐标、负载）
                resultReceive = Receive(socket, 5568, ReceiveTimeOut);
                if (!resultReceive.IsSuccess) return resultReceive;
                break;
            case 0x2c: //运行信息（倍率、时间、计数）
                resultReceive = Receive(socket, 2312, ReceiveTimeOut);
                if (!resultReceive.IsSuccess) return resultReceive;
                break;
            default:
                resultReceive = Receive(socket, -1);
                if (!resultReceive.IsSuccess) return resultReceive;
                break;
        }


        // Success
        return OperateResult.CreateSuccessResult(resultReceive.Content);
    }

    private double[] GetmazakDouble(byte[] content, int index, int length)
    {
        var buffer = new double[length];
        for (var i = 0; i < length; i++)
        {
            var data = ByteTransform.TransInt32(content, index + 8 * i);
            int decs = ByteTransform.TransInt16(content, index + 8 * i + 6);
            buffer[i] = Math.Round(data * Math.Pow(0.1d, decs), decs);
        }

        return buffer;
    }

    #endregion

    #region Read Write Support

    /// <summary>
    ///     读取当前系统状态
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSysStatus()
    {
        var read = ReadFromCoreServer(BuildReadArray(0x11));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(256);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取当前工作模式
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSysMode()
    {
        var read = ReadFromCoreServer(BuildReadArray(0x07));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(256);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取运行信息，刀具号，主轴倍率、进给倍率、快速倍率、目标工件、工件计、自动运行时间、切削时间、总时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<short, short, short, short, int, int, int, int, int> ReadRunInfo()
    {
        var read = ReadFromCoreServer(BuildReadArray(0x2c));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<short, short, short, short, int, int, int, int, int>(read);
        var result = read.Content.RemoveBegin(1292);
        var toolnum = ByteTransform.TransInt16(result, 0); //刀具号
        var spindlerate = ByteTransform.TransInt16(result, 16); //主轴倍率
        var feedrate = ByteTransform.TransInt16(result, 18); //进给倍率
        var quickrate = ByteTransform.TransInt16(result, 20); //快速倍率
        var tagproduce = ByteTransform.TransInt32(result, 28); //目标工件
        var curproduce = ByteTransform.TransInt32(result, 32); //工件计数
        var runtime = ByteTransform.TransInt32(result, 36); //自动运行时间
        var cuttime = ByteTransform.TransInt32(result, 40); //切削时间
        var totaltime = ByteTransform.TransInt32(result, 44); //总时间
        return OperateResult.CreateSuccessResult(toolnum, spindlerate, feedrate, quickrate, tagproduce, curproduce, runtime, cuttime, totaltime);
    }

    public OperateResult<byte[]> ReadRunInfobyte()
    {
        var read = ReadFromCoreServer(BuildReadArray(0x2c));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(read);
        var result = read.Content;

        return OperateResult.CreateSuccessResult(result);
    }


    /// <summary>
    ///     读取主轴信息主轴速度、主轴负载、主轴温度
    /// </summary>
    /// <returns></returns>
    public OperateResult<double, double, double> ReadSpindInfo()
    {
        var read = ReadFromCoreServer(BuildReadArray(0x2a));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double, double, double>(read);
        var result = read.Content.RemoveBegin(1288);
        var actspeed = ByteTransform.TransDouble(result, 0); //主轴速度
        var sload = ByteTransform.TransDouble(result, 8); //主轴负载
        var stemper = ByteTransform.TransDouble(result, 16); //主轴温度

        return OperateResult.CreateSuccessResult(actspeed, sload, stemper);
    }

    /// <summary>
    ///     读取程序信息包括程序号、块号、顺序号、程序名、注释
    /// </summary>
    /// <returns></returns>
    public OperateResult<int, int, int, string, string> ReadProgramInfo()
    {
        var read = ReadFromCoreServer(BuildReadArray(0x2b));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, int, int, string, string>(read);
        var result = read.Content.RemoveBegin(1292);
        var wno = ByteTransform.TransInt32(result, 0); //程序号
        var blockno = ByteTransform.TransInt32(result, 4); //块号
        var seqno = ByteTransform.TransInt32(result, 8); //顺序号
        var name = Encoding.Default.GetString(result, 16, 64).TrimEnd('\0'); //程序名
        var annotation = Encoding.Default.GetString(result, 82, 64).TrimEnd('\0'); //程序注释
        return OperateResult.CreateSuccessResult(wno, blockno, seqno, name, annotation);
    }

    ///// <summary>
    ///// 读取进给轴信息 包括坐标 负载 倍率，暂时未开发完成
    ///// </summary>
    ///// <returns></returns>
    //public OperateResult<int, int, int, string, string> ReadFeedInfo()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(0x29));
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, int, int, string, string>(read);
    //    byte[] result = read.Content.RemoveBegin(1292);
    //    int wno = ByteTransform.TransInt32(result, 0);//程序号
    //    int blockno = ByteTransform.TransInt32(result, 4);//块号
    //    int seqno = ByteTransform.TransInt32(result, 8);//顺序号
    //    string name = Encoding.Default.GetString(result, 16, 64).TrimEnd('\0'); //程序名
    //    string annotation = Encoding.Default.GetString(result, 82, 64).TrimEnd('\0'); //程序注释
    //    return OperateResult.CreateSuccessResult(wno, blockno, seqno, name, annotation);
    //}

    #endregion


    #region Build Command

    private byte[] BuildReadArray(ushort code)
    {
        var buffer = new byte[1280];
        ByteTransform.TransByte(code).CopyTo(buffer, 0);
        return buffer;
    }

    #endregion
}