using System;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace MazakSmart.Base;

/// <summary>
///     一个马扎克CNC的机床通讯库，目前仅限于测试smart
/// </summary>
public class MazakSmartBase : NetworkDoubleBase
{
    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }
    
    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public MazakSmartBase(string ipAddress, int port = 57400)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    /// <inheritdoc />
    // protected override INetMessage GetNewNetMessage() => new CNCFanucSeriesMessage();
    protected override OperateResult InitializationOnConnect(Socket socket)
    {
        var read1 = Receive(socket, 6, ReceiveTimeOut);
        if (!read1.IsSuccess) return read1;

        return OperateResult.CreateSuccessResult();
    }

    public override OperateResult<byte[]> ReadFromCoreServer(Socket socket, byte[] send, bool hasResponseData = true, bool usePackHeader = true)
    {
        // send
        var sendResult = Send(socket, send);
        if (!sendResult.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(sendResult);

        if (ReceiveTimeOut < 0) return OperateResult.CreateSuccessResult(new byte[0]);


        OperateResult<byte[]> resultReceive;
        // if (send[9] == 0x4e || send[9] == 0x4d) resultReceive = Receive(socket, -1);
        // if (send[9] == 0x52 && send[16] == 0x53) resultReceive = Receive(socket, -1);

        switch (send[9])
        {
            case 0x53://主轴信息（速度、负载、温度）
                resultReceive = Receive(socket, 2033, this.ReceiveTimeOut);
                if (!resultReceive.IsSuccess) return resultReceive;
                break;
            case 0x50: //程序信息
                resultReceive = Receive(socket, 1433, this.ReceiveTimeOut);
                if (!resultReceive.IsSuccess) return resultReceive;
                break;
            case 0x41://进给轴信息（坐标、负载）
                resultReceive = Receive(socket, 4305, this.ReceiveTimeOut); 
                if (!resultReceive.IsSuccess) return resultReceive;
                break;
            case 0x52:
                if (send[16] == 0x53) //状态
                {
                    resultReceive = Receive(socket, 19, this.ReceiveTimeOut);
                }
                else //运行信息（倍率、时间、计数）
                { 
                    resultReceive = Receive(socket, 1049, this.ReceiveTimeOut); 
                }
                if (!resultReceive.IsSuccess) return resultReceive;
                break;          
            case 0x0a://报警信息
                resultReceive = Receive(socket, 4433, this.ReceiveTimeOut);
                if (!resultReceive.IsSuccess) return resultReceive;
                break;
            case 0x4d://模式
                resultReceive = Receive(socket, 19, this.ReceiveTimeOut);
                if (!resultReceive.IsSuccess) return resultReceive;
                break;
            default:
                resultReceive = Receive(socket, -1);
                if (!resultReceive.IsSuccess) return resultReceive;
                break;
        }


        // Success
        return OperateResult.CreateSuccessResult(resultReceive.Content);
    }

    private double[] GetmazakDouble(byte[] content, int index, int length)
    {
        var buffer = new double[length];
        for (var i = 0; i < length; i++)
        {
            var data = ByteTransform.TransInt32(content, index + 8 * i);
            int decs = ByteTransform.TransInt16(content, index + 8 * i + 6);
            buffer[i] = Math.Round(data * Math.Pow(0.1d, decs), decs);
        }

        return buffer;
    }

    #endregion

    #region Read Write Support

    /// <summary>
    ///     读取当前系统状态
    /// </summary>
    /// <returns></returns>
    public OperateResult<short> ReadSysStatus()
    {
        //OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(0x12));
        //if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);

        var read1 = ReadFromCoreServer(BuildReadSysStatus());
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<short>(read1);

        var result = read1.Content.RemoveBegin(17);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt16(result, 0));
    }

    /// <summary>
    ///     读取当前工作模式
    /// </summary>
    /// <returns></returns>
    public OperateResult<short> ReadSysMode()
    {
        //OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(0x0c));
        //if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);

        var read1 = ReadFromCoreServer(BuildReadSysMode());
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<short>(read1);

        var result = read1.Content.RemoveBegin(17);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt16(result, 0));
    }
    
    /// <summary>
    ///     读取运行信息，刀具号，主轴倍率、进给倍率、快速倍率、目标工件、工件计、自动运行时间、切削时间、总时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<short, short, short, short, int, int, int, int, int> ReadRunInfo()
    {
        //OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(0x12));
        //if (!read.IsSuccess) return OperateResult.CreateFailedResult<short, short, short, short, int, int, int, int, int>(read);

        var read1 = ReadFromCoreServer(BuildReadRunInfo());
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<short, short, short, short, int, int, int, int, int>(read1);
        var result = read1.Content.RemoveBegin(17);
        var toolnum = ByteTransform.TransInt16(result, 12); //刀具号  
        var spindlerate = ByteTransform.TransInt16(result, 28); //主轴倍率
        var feedrate = ByteTransform.TransInt16(result, 30); //进给倍率
        var quickrate = ByteTransform.TransInt16(result, 32); //快速倍率
        int tagproduce = ByteTransform.TransInt16(result, 46); //目标工件
        int curproduce = ByteTransform.TransInt16(result, 44); //工件计数
        var runtime = ByteTransform.TransInt32(result, 48); //自动运行时间
        var cuttime = ByteTransform.TransInt32(result, 52); //切削时间
        var totaltime = ByteTransform.TransInt32(result, 56); //总时间
        return OperateResult.CreateSuccessResult(toolnum, spindlerate, feedrate, quickrate, tagproduce, curproduce, runtime, cuttime, totaltime);
    }
    
    public OperateResult<byte[]> ReadRunInfobyte()
    {
        var read = ReadFromCoreServer(BuildReadArray(0x2c));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(read);
        var result = read.Content;

        return OperateResult.CreateSuccessResult(result);
    }


    /// <summary>
    ///     读取主轴信息主轴速度、主轴负载、主轴温度
    /// </summary>
    /// <returns></returns>
    public OperateResult<double, double, double> ReadSpindInfo()
    {
        //OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(0x12));
        //if (!read.IsSuccess) return OperateResult.CreateFailedResult<double, double, double>(read);

        var read1 = ReadFromCoreServer(BuildReadSpindInfo());
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<double, double, double>(read1);
        var result = read1.Content.RemoveBegin(25);
        var actspeed = ByteTransform.TransDouble(result, 0); //主轴速度
        var sload = ByteTransform.TransDouble(result, 8); //主轴负载
        var stemper = ByteTransform.TransDouble(result, 16); //主轴温度

        return OperateResult.CreateSuccessResult(actspeed, sload, stemper);
    }

    public OperateResult<byte[]> ReadSpindInfoByte()
    {
        //OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(0x12));
        //if (!read.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(read);

        var read1 = ReadFromCoreServer(BuildReadSpindInfo());
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(read1);
        var result = read1.Content.RemoveBegin(25);
        var actspeed = ByteTransform.TransDouble(result, 0); //主轴速度
        var sload = ByteTransform.TransDouble(result, 8); //主轴负载
        var stemper = ByteTransform.TransDouble(result, 16); //主轴温度


        return OperateResult.CreateSuccessResult(result);
    }

    /// <summary>
    ///     读取程序信息包括程序号、块号、顺序号、程序名、注释
    /// </summary>
    /// <returns></returns>
    public OperateResult<int, int, int, string, string> ReadProgramInfo()
    {
        var read = ReadFromCoreServer(BuildReadProgramInfo());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, int, int, string, string>(read);
        var result = read.Content.RemoveBegin(25);
        var wno = ByteTransform.TransInt32(result, 0); //程序号
        var blockno = ByteTransform.TransInt32(result, 4); //块号
        var seqno = ByteTransform.TransInt32(result, 8); //顺序号
        var name = Encoding.Default.GetString(result, 16, 64).TrimEnd('\0'); //程序名
        var annotation = Encoding.Default.GetString(result, 82, 64).TrimEnd('\0'); //程序注释
        return OperateResult.CreateSuccessResult(wno, blockno, seqno, name, annotation);
    }

    ///// <summary>
    ///// 读取进给轴信息 包括坐标 负载 倍率，暂时未开发完成
    ///// </summary>
    ///// <returns></returns>
    //public OperateResult<int, int, int, string, string> ReadFeedInfo()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(0x29));
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, int, int, string, string>(read);
    //    byte[] result = read.Content.RemoveBegin(1292);
    //    int wno = ByteTransform.TransInt32(result, 0);//程序号
    //    int blockno = ByteTransform.TransInt32(result, 4);//块号
    //    int seqno = ByteTransform.TransInt32(result, 8);//顺序号
    //    string name = Encoding.Default.GetString(result, 16, 64).TrimEnd('\0'); //程序名
    //    string annotation = Encoding.Default.GetString(result, 82, 64).TrimEnd('\0'); //程序注释
    //    return OperateResult.CreateSuccessResult(wno, blockno, seqno, name, annotation);
    //}

    #endregion


    #region Build Command

    private byte[] BuildReadArray(ushort code)
    {
        var buffer = new byte[4];
        ByteTransform.TransByte(code).CopyTo(buffer, 0);
        return buffer;
    }

    #endregion

    /// <summary>
    ///     系统状态
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysStatus()
    {
        var buffer = @"0x12,0x00,0x00,0x00,
                            0x47, 0x45, 0x54, 0x0d, 0x0a, 0x52, 0x55, 0x4e,
                            0x4e, 0x49, 0x4e, 0x47, 0x53, 0x54, 0x53, 0x0d,
                            0x0a, 0x30
                            ".ToHexBytes();
        return buffer;
    }


    /// <summary>
    ///     系统模式
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysMode()
    {
        var buffer = @"0x0c,0x00,0x00,0x00,
                            0x47, 0x45, 0x54, 0x0d, 0x0a, 0x4d, 0x4f, 0x44,
                            0x45, 0x0d, 0x0a, 0x30
                            ".ToHexBytes();
        return buffer;
    }


    /// <summary>
    ///     主轴信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSpindInfo()
    {
        var buffer = @"0x12,0x00,0x00,0x00,
                            0x47, 0x45, 0x54, 0x0d, 0x0a, 0x53, 0x50, 0x49,
                            0x4e, 0x44, 0x4c, 0x45, 0x49, 0x4e, 0x46, 0x4f,
                            0x0d, 0x0a
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     运行信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadRunInfo()
    {
        var buffer = @"0x12,0x00,0x00,0x00,
                            0x47, 0x45, 0x54, 0x0d, 0x0a, 0x52, 0x55, 0x4e,
                            0x4e, 0x49, 0x4e, 0x47, 0x49, 0x4e, 0x46, 0x4f,
                            0x0d, 0x0a
                            ".ToHexBytes();
        return buffer;
    }


    /// <summary>
    ///     程序信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadProgramInfo()
    {
        var buffer = @"0x12,0x00,0x00,0x00,
                            0x47, 0x45, 0x54, 0x0d, 0x0a, 0x50, 0x52, 0x4f,
                            0x47, 0x52, 0x41, 0x4d, 0x49, 0x4e, 0x46, 0x4f,
                            0x0d, 0x0a
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAlarmInfo()
    {
        var buffer = @"
                            0x47, 0x45, 0x54, 0x0d, 0x0a, 0x41, 0x4c, 0x41,
                            0x52, 0x4d, 0x0d, 0x0a
                            ".ToHexBytes();
        return buffer;
    }
}