using System;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using HslCommunication;
using MazakSmart.Base;
// ReSharper disable All
#pragma warning disable CS1998

namespace MazakSmart;

/// <summary>
/// 注意：该协议不支持异步调用
/// </summary>
[DriverSupported("MazakSmart")]
[DriverInfo("MazakSmart", "V1.3.0", "马扎克(Mazak)")]
public class MazakSmart : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 57400;

    #endregion

    public MazakSmart(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    /// </summary>
    public override bool IsConnected => Driver != null && Driver.GetConnectStatus();

    /// <summary>
    ///     读取运行信息，刀具号，主轴倍率、进给倍率、快速倍率、目标工件、工件计、自动运行时间、切削时间、总时间
    /// </summary>
    private OperateResult<short, short, short, short, int, int, int, int, int> _runInfo;

    /// <summary>
    ///     读取主轴信息主轴速度、主轴负载、主轴温度
    /// </summary>
    private OperateResult<double, double, double> _spindInfo;

    /// <summary>
    ///     读取程序信息包括程序号、块号、顺序号、程序名、注释
    /// </summary>
    private OperateResult<int, int, int, string, string> _programInfo;

    /// <summary>
    ///     清空数据
    /// </summary>
    /// <returns></returns>
    [Method("Clear", name: "Clear")]
#pragma warning disable CS1998
    public async Task Clear()
#pragma warning restore CS1998
    {
        _runInfo = null;
        _spindInfo = null;
        _programInfo = null;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            Driver ??= new MazakSmartBase(IpAddress, Port)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout
            };
            OperateResult = Driver.ConnectServer();
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接失败:【{ex.Message}】"};
        }

        return ResConnect();
    }

    /// <summary>
    ///     读取运行信息，刀具号，主轴倍率、进给倍率、快速倍率、目标工件、工件计、自动运行时间、切削时间、总时间
    /// </summary>
    /// <param name="paramName"></param>
    private async Task<DriverReturnValueModel> ReadRunInfo(string paramName)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Double
        };
        try
        {
            if (IsConnected)
            {
                _runInfo ??=  Driver.ReadRunInfo();
                if (!_runInfo.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _runInfo.Message;
                }

                switch (paramName)
                {
                    case "ToolNum": //刀具号
                    {
                        ret.Value = Convert.ToInt16(_runInfo.Content1);
                        ret.DataType = DataTypeEnum.Int16;
                        break;
                    }
                    case "OvSpin": // 主轴倍率
                    {
                        ret.Value = Convert.ToInt16(_runInfo.Content2);
                        ret.DataType = DataTypeEnum.Int16;
                        break;
                    }
                    case "OvFeed": // 进给倍率
                    {
                        ret.Value = Convert.ToInt16(_runInfo.Content3);
                        ret.DataType = DataTypeEnum.Int16;
                        break;
                    }
                    case "QuickRate": //快速倍率
                    {
                        ret.Value = Convert.ToInt16(_runInfo.Content4);
                        ret.DataType = DataTypeEnum.Int16;
                        break;
                    }
                    case "TagProduce": //目标工件
                        ret.Value = Convert.ToInt32(_runInfo.Content5);
                        break;
                    case "CurProduce": //工件计数
                        ret.Value = Convert.ToInt32(_runInfo.Content6);
                        break;
                    case "Runtime": //自动运行时间
                        ret.Value = Convert.ToInt32(_runInfo.Content7);
                        break;
                    case "CutTime": //切削时间
                        ret.Value = Convert.ToInt32(_runInfo.Content8);
                        break;
                    case "TotalTime": //总时间
                        ret.Value = Convert.ToInt32(_runInfo.Content9);
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取主轴信息主轴速度、主轴负载、主轴温度
    /// </summary>
    /// <param name="paramName">参数名称</param>
    private async Task<DriverReturnValueModel> ReadSpindInfo(string paramName)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Double
        };
        try
        {
            if (IsConnected)
            {
                _spindInfo ??= Driver.ReadSpindInfo();
                if (!_spindInfo.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _spindInfo.Message;
                }

                switch (paramName)
                {
                    case "ActSpeed": //主轴速度
                        ret.Value = Convert.ToDouble(_spindInfo.Content1);
                        break;
                    case "SLoad": //主轴负载
                        ret.Value = Convert.ToDouble(_spindInfo.Content2);
                        break;
                    case "SpinTemp": //主轴温度
                        ret.Value = Convert.ToDouble(_spindInfo.Content3);
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取程序信息包括程序号、块号、顺序号、程序名、注释
    /// </summary>
    /// <param name="paramName">参数名称</param>
    private async Task<DriverReturnValueModel> ReadProgramInfo(string paramName)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _programInfo ??= Driver.ReadProgramInfo();
                if (!_programInfo.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _programInfo.Message;
                }

                switch (paramName)
                {
                    case "ProgramNo": //程序号
                        ret.Value = Convert.ToInt32(_programInfo.Content1);
                        break;
                    case "BlockNo": //块号
                        ret.Value = Convert.ToInt32(_programInfo.Content2);
                        break;
                    case "SeqNo": //顺序号
                        ret.Value = Convert.ToInt32(_programInfo.Content3);
                        break;
                    case "Name": //程序名
                    {
                        ret.Value = _programInfo.Content4;
                        ret.DataType = DataTypeEnum.String;
                    }
                        break;
                    case "Annotation": //程序注释
                    {
                        ret.Value = _programInfo.Content5;
                        ret.DataType = DataTypeEnum.String;
                    }
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     工作模式
    /// </summary>
    /// <returns></returns>
    [Method("WorkMode", name: "工作模式")]
    public async Task<DriverReturnValueModel> ReadWorkMode()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = Driver.ReadSysMode();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = Convert.ToUInt32(read.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     系统状态
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", name: "系统状态")]
    public async Task<DriverReturnValueModel> ReadRunStatus()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read =  Driver.ReadSysStatus();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = Convert.ToUInt32(read.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #region RunInfo

    /// <summary>
    ///     刀具号
    /// </summary>
    /// <returns></returns>
    [Method("ToolNum", name: "刀具号")]
    public async Task<DriverReturnValueModel> ReadToolNum()
    {
        return await ReadRunInfo("ToolNum");
    }

    /// <summary>
    ///     切削时间
    /// </summary>
    /// <returns></returns>
    [Method("CutTime", name: "切削时间")]
    public async Task<DriverReturnValueModel> ReadCutTime()
    {
        return await ReadRunInfo("CutTime");
    }

    /// <summary>
    ///     目标工件
    /// </summary>
    /// <returns></returns>
    [Method("TagProduce", name: "目标工件")]
    public async Task<DriverReturnValueModel> ReadtagProduce()
    {
        return await ReadRunInfo("TagProduce");
    }

    /// <summary>
    ///     工件计数
    /// </summary>
    /// <returns></returns>
    [Method("CurProduce", name: "工件计数")]
    public async Task<DriverReturnValueModel> ReadCurProduce()
    {
        return await ReadRunInfo("CurProduce");
    }

    /// <summary>
    ///     快速倍率
    /// </summary>
    /// <returns></returns>
    [Method("QuickRate", name: "快速倍率")]
    public async Task<DriverReturnValueModel> ReadQuickRate()
    {
        return await ReadRunInfo("QuickRate");
    }

    /// <summary>
    ///     总时间
    /// </summary>
    /// <returns></returns>
    [Method("TotalTime", name: "总时间")]
    public async Task<DriverReturnValueModel> ReadTotalTime()
    {
        return await ReadRunInfo("TotalTime");
    }

    /// <summary>
    ///     自动运行时间
    /// </summary>
    /// <returns></returns>
    [Method("Runtime", name: "自动运行时间")]
    public async Task<DriverReturnValueModel> ReadRunTime()
    {
        return await ReadRunInfo("Runtime");
    }

    /// <summary>
    ///     主轴倍率
    /// </summary>
    /// <returns></returns>
    [Method("OvSpin", name: "主轴倍率")]
    public async Task<DriverReturnValueModel> ReadOvSpin()
    {
        return await ReadRunInfo("OvSpin");
    }

    /// <summary>
    ///     进给倍率
    /// </summary>
    /// <returns></returns>
    [Method("OvFeed", name: "进给倍率")]
    public async Task<DriverReturnValueModel> ReadOvFeed()
    {
        return await ReadRunInfo("OvFeed");
    }

    #endregion

    #region SpindInfo

    /// <summary>
    ///     主轴温度
    /// </summary>
    /// <returns></returns>
    [Method("SpinTemp", TransPondDataTypeEnum.Double, name: "主轴温度")]
    public async Task<DriverReturnValueModel> ReadSpinTemp()
    {
        return await ReadSpindInfo("SpinTemp");
    }

    /// <summary>
    ///     主轴负载
    /// </summary>
    /// <returns></returns>
    [Method("SLoad", TransPondDataTypeEnum.Double, name: "主轴负载")]
    public async Task<DriverReturnValueModel> ReadSLoad()
    {
        return await ReadSpindInfo("SLoad");
    }

    /// <summary>
    ///     主轴速度
    /// </summary>
    /// <returns></returns>
    [Method("ActSpeed", TransPondDataTypeEnum.Double, name: "主轴速度")]
    public async Task<DriverReturnValueModel> ReadActSpeed()
    {
        return await ReadSpindInfo("ActSpeed");
    }

    #endregion

    #region ProgramInfo

    /// <summary>
    ///     程序号
    /// </summary>
    /// <returns></returns>
    [Method("ProgramNo", name: "程序号")]
    public async Task<DriverReturnValueModel> ReadProgramNo()
    {
        return await ReadProgramInfo("ProgramNo");
    }

    /// <summary>
    ///     块号
    /// </summary>
    /// <returns></returns>
    [Method("BlockNo", name: "块号")]
    public async Task<DriverReturnValueModel> ReadBlockNo()
    {
        return await ReadProgramInfo("BlockNo");
    }

    /// <summary>
    ///     顺序号
    /// </summary>
    /// <returns></returns>
    [Method("SeqNo", name: "顺序号")]
    public async Task<DriverReturnValueModel> ReadSeqNo()
    {
        return await ReadProgramInfo("SeqNo");
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    [Method("Name", TransPondDataTypeEnum.String, name: "程序名")]
    public async Task<DriverReturnValueModel> ReadName()
    {
        return await ReadProgramInfo("Name");
    }

    /// <summary>
    ///     程序注释
    /// </summary>
    /// <returns></returns>
    [Method("Annotation", TransPondDataTypeEnum.String, name: "程序注释")]
    public async Task<DriverReturnValueModel> ReadAnnotation()
    {
        return await ReadProgramInfo("Annotation");
    }

    #endregion
}