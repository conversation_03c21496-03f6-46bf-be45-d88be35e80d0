using System;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using HslCommunication;
using HslCommunication.BasicFramework;

namespace MazakMatrix;

[DriverSupported("MazakMatrix")]
[DriverInfo("MazakMatrix", "V1.1.0", "马扎克(Mazak)")]
public class MazakMatrix : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    private CancellationTokenSource _tokenSource = new();
    [ConfigParameter("端口号")] public int Port { get; set; } = 51001;

    #endregion

    public MazakMatrix(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    /// </summary>
    public override bool IsConnected => Driver != null;

    public override void Close()
    {
        _tokenSource.Cancel();
        if (Driver != null)
            Driver.Close();
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            //不能注释下面语句,close方法里有关闭
            _tokenSource = new CancellationTokenSource();
            if (Driver == null)
            {
                Driver = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
                Driver.Bind(new IPEndPoint(IPAddress.Any, Port));
                Recive();
            }

            OperateResult = new OperateResult {IsSuccess = true, Message = $"【0.0.0.0:{Port}】服务已开启"};
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接失败:【{ex.Message}】"};
        }

        return ResConnect();
    }

    private string _type = ""; //  类型
    private string _ip = ""; //  Ip
    private string _mode = ""; // 模式
    private string _proName = ""; // 程序名
    private string _proComment = ""; //程序内容
    private string _toolNum = ""; //刀具号
    private int _rapidFeed; // 快速进给
    private string _sFeed = ""; // 主轴倍率
    private string _fFeed = ""; //进给倍率
    private string _alarm = ""; //报警信息
    private string _millingLoad = ""; //铣削负载
    private string _turningLoad = ""; //车削负载
    private string _millingSpeed = ""; //铣削转速
    private string _turningSpeed = ""; //车削转速
    private int _parts; // 产量
    private int _runStatus; // 运行状态
    private string _alarmNo = ""; //报警号
    private DateTime _lastTime = DateTime.MinValue;

    /// <summary>
    /// </summary>
    private void Recive()
    {
        Task.Factory.StartNew(() =>
        {
            while (!_tokenSource.IsCancellationRequested)
                try
                {
                    EndPoint point = new IPEndPoint(IPAddress.Any, 0);
                    var buffer = new byte[1024];
                    var length = Driver.ReceiveFrom(buffer, ref point);
                    var macStr = Encoding.UTF8.GetString(buffer, 0, length);
                    var newData = buffer.Skip(0).Take(512).ToArray();
                    _ = DriverInfo.Socket.Send($"报文:【{SoftBasic.ByteToHexString(newData)}】", DriverInfo.DeviceId + "_Logs");
                    _lastTime = Common.Extension.DateTime.Now();
                    _type = Encoding.GetEncoding("UTF-8").GetString(newData.Skip(2).Take(9).ToArray());
                    _ip = Encoding.GetEncoding("UTF-8").GetString(newData.Skip(33).Take(13).ToArray());
                    _mode = Encoding.GetEncoding("UTF-8").GetString(newData.Skip(86).Take(2).ToArray());
                    _runStatus = Convert.ToInt32(Encoding.GetEncoding("UTF-8").GetString(newData.Skip(88).Take(1).ToArray()));
                    _proName = Encoding.GetEncoding("UTF-8").GetString(newData.Skip(89).Take(2).ToArray());
                    _proComment = Encoding.GetEncoding("UTF-8").GetString(newData.Skip(122).Take(11).ToArray());
                    _toolNum = Encoding.GetEncoding("UTF-8").GetString(newData.Skip(274).Take(2).ToArray());
                    _parts = Convert.ToInt32(Encoding.GetEncoding("UTF-8").GetString(newData.Skip(368 - 42 + 12).Take(2).ToArray()));
                    _alarmNo = Encoding.GetEncoding("UTF-8").GetString(newData.Skip(55).Take(3).ToArray());
                    for (var j = 0; j < macStr.Length - 3; j++)
                        if (macStr.Substring(j, 2) == "FF")
                        {
                            _rapidFeed = Convert.ToInt32(macStr.Substring(j + 2, 3));
                            _sFeed = macStr.Substring(j + 6, 3);
                            _fFeed = macStr.Substring(j + 10, 3);
                            _alarm = Encoding.GetEncoding("gb2312").GetString(newData.Skip(364).Take(20).ToArray());

                            _millingLoad = macStr.Substring(433, 1);
                            _turningLoad = macStr.Substring(437, 1);
                            _millingSpeed = macStr.Substring(454, 4);
                            _turningSpeed = macStr.Substring(472, 4);
                            for (var i = 0; i < 100; i++)
                            {
                                var temp2 = macStr.Substring(j + i, 50);
                                if (temp2.Trim() != "") continue;
                                temp2 = macStr.Substring(j + i + 51, 1);
                                if (temp2.Trim() == "") continue;
                                _millingLoad = macStr.Substring(j + i + 51, 2);
                                _turningLoad = macStr.Substring(j + i + 55, 2);
                                _millingSpeed = macStr.Substring(j + i + 67, 2);
                                _turningSpeed = macStr.Substring(j + i + 76, 2);
                            }
                        }
                }
                catch (Exception ex)
                {
                    _ = DriverInfo.Socket.Send($"Recive Error:【{ex.Message}】", DriverInfo.DeviceId + "_Console");
                }
        }, TaskCreationOptions.LongRunning);
    }

    /// <summary>
    ///     最后活动时间
    /// </summary>
    /// <returns></returns>
    [Method("LastTime", TransPondDataTypeEnum.String, name: "最后活动时间")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadLastTime()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                ret.Value = _lastTime;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     产量
    /// </summary>
    /// <returns></returns>
    [Method("Product", TransPondDataTypeEnum.String, name: "产量")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadParts()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                ret.Value = _parts;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     运行状态
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", TransPondDataTypeEnum.String, name: "运行状态")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadRunStatus()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                ret.Value = _runStatus;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     报警号
    /// </summary>
    /// <returns></returns>
    [Method("AlarmNo", TransPondDataTypeEnum.String, name: "报警号")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadAlarmNo()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                if (_alarmNo == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _alarmNo;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     类型
    /// </summary>
    /// <returns></returns>
    [Method("Type", TransPondDataTypeEnum.String, name: "类型")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadType()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                if (_type == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _type;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     Ip
    /// </summary>
    /// <returns></returns>
    [Method("IP", TransPondDataTypeEnum.String, name: "Ip")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadIp()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                if (_ip == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _ip;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     模式
    /// </summary>
    /// <returns></returns>
    [Method("Mode", name: "模式")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadMode()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int16
        };
        try
        {
            if (IsConnected)
            {
                if (_mode == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _mode;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    [Method("ProName", TransPondDataTypeEnum.String, name: "程序名")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadProName()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                if (_proName == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _proName;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     程序内容
    /// </summary>
    /// <returns></returns>
    [Method("ProComment", TransPondDataTypeEnum.String, name: "程序内容")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadProComment()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                if (_proComment == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _proComment;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     刀具号
    /// </summary>
    /// <returns></returns>
    [Method("ToolNum", name: "刀具号")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadToolNum()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int16
        };
        try
        {
            if (IsConnected)
            {
                if (_toolNum == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _toolNum;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     快速进给
    /// </summary>
    /// <returns></returns>
    [Method("RapidFeed", TransPondDataTypeEnum.String, name: "快速进给")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadRapidFeed()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                ret.Value = _rapidFeed;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴倍率
    /// </summary>
    /// <returns></returns>
    [Method("SFeed", TransPondDataTypeEnum.String, name: "主轴倍率")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadSFeed()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                if (_sFeed == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _sFeed;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给倍率
    /// </summary>
    /// <returns></returns>
    [Method("FFeed", TransPondDataTypeEnum.String, name: "进给倍率")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadFFeed()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                if (_fFeed == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _fFeed;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     报警信息
    /// </summary>
    /// <returns></returns>
    [Method("Alarm", TransPondDataTypeEnum.String, name: "报警信息")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadAlarm()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                if (_alarm == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _alarm;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     铣削负载
    /// </summary>
    /// <returns></returns>
    [Method("MillingLoad", TransPondDataTypeEnum.String, name: "铣削负载")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadMillingLoad()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                if (_millingLoad == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _millingLoad;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     车削负载
    /// </summary>
    /// <returns></returns>
    [Method("TurningLoad", TransPondDataTypeEnum.String, name: "车削负载")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadTurningLoad()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                if (_turningLoad == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _turningLoad;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     铣削倍率
    /// </summary>
    /// <returns></returns>
    [Method("MillingSpeed", TransPondDataTypeEnum.String, name: "铣削倍率")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadMillingSpeed()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                if (_millingSpeed == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _millingSpeed;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     车削转速
    /// </summary>
    /// <returns></returns>
    [Method("TurningSpeed", TransPondDataTypeEnum.String, name: "车削转速")]
#pragma warning disable CS1998
    public async Task<DriverReturnValueModel> ReadTurningSpeed()
#pragma warning restore CS1998
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                if (_turningSpeed == "")
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "未收到数据";
                }

                ret.Value = _turningSpeed;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "服务未开启";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
}