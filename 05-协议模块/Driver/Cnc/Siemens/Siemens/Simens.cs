using System;
 using System.Linq;
 using System.Text;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.Write.Dto;
using DriversInterface;
using Furion.JsonSerialization;
using Furion.Logging;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Siemens;
using Newtonsoft.Json;
using Siemens.Base;
using Siemens.Simens.Base;

namespace Siemens;

/* 更新日志
 *版本:v1.4.0
 *  1.新增`兼容西门子PLC协议`，初始化同时初始化西门子PLC地址，默认为S300协议
 *  2.新增`伺服负载Z轴,伺服负载X轴,伺服负载Y轴,伺服负载B轴，伺服负载C轴，报警状态，报警信息`点位
 *  3.`Siemens-CNC协议中`重写西门子PLC协议，自构报文
 *
 * 版本:v1.4.1
 * 1.初始化plc协议默认初始化大小端转换
 *
  * 版本:v1.4.2 - 25-06-30
 * 1.报警信息过滤了空值
 * 2.报警状态增加了plc的报警数量判断，两个同时报警数 >0 才报警
 * 3.负载轴x.y.z...改成第一轴.第二轴...
 * 4.第三轴底层报文调整
 */
[DriverSupported("Siemens")]
[DriverInfo("Siemens", "V1.4.2", "西门子(Siemens)")]
public class Siemens : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 102;
    [ConfigParameter("轴数量")] public int AxisCount { get; set; } = 5;

    #endregion

    public Siemens(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public override bool IsConnected => Driver != null && Driver.GetConnectStatus();

    public bool S7IsConnected => _siemensS7Net != null; 

    private SimensPLC _siemensS7Net;

    public DeviceConnectDto Connect()
    {
        try
        {
            Driver ??= new SimensBase(IpAddress, Port)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout
            };
            OperateResult = Driver.ConnectServer();

            // 内部默认初始化S300 PLC
            if (_siemensS7Net == null)
            {
                _siemensS7Net = new SimensPLC(SiemensPLCS.S300, IpAddress, Port)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout,
                    ByteTransform = new RegularByteTransform() // 在cnc中默认初始化该参数
                };
                _siemensS7Net.LogNet = new LogNetSingle("");
                _siemensS7Net.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接失败:【{ex.Message}】" };
        }

        return ResConnect();
    }

    /// <summary>
    ///     断开连接
    /// </summary>
    public override void Close()
    {
        OperateResult.Message = "连接已经关闭";
        Driver?.ConnectClose();
        _siemensS7Net?.ConnectClose();
    }

    /// <summary>
    ///     释放
    /// </summary>
    public override void Dispose()
    {
        Driver?.Dispose();
        _siemensS7Net?.Dispose();
    }

    /// <summary>
    ///     读取设备属性,循环单个读取
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    [Method("ReadPlc", name: "读PLC地址")]
    public async Task<DriverReturnValueModel> ReadPlc(DriverAddressIoArgModel val)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = val.DataType,
            Id = val.Id
        };
        try
        {
            if (S7IsConnected)
            {
                switch (val.DataType)
                {
                    case DataTypeEnum.Bool:
                        {
                            var boolRead = await _siemensS7Net.ReadBoolAsync(val.Address);
                            if (!boolRead.IsSuccess)
                            {
                                ret.VariableStatus = VariableStatusTypeEnum.Error;
                                ret.Message = boolRead.Message;
                                ret.ErrorCode = boolRead.ErrorCode;
                            }

                            ret.Value = boolRead.Content;
                            break;
                        }
                    case DataTypeEnum.Bit:
                        {
                            var boolRead = await _siemensS7Net.ReadBoolAsync(val.Address);
                            if (!boolRead.IsSuccess)
                            {
                                ret.VariableStatus = VariableStatusTypeEnum.Error;
                                ret.Message = boolRead.Message;
                                ret.ErrorCode = boolRead.ErrorCode;
                            }

                            ret.Value = boolRead.Content ? 1 : 0;
                            break;
                        }
                    case DataTypeEnum.Uint16:
                        var uint16Read = await _siemensS7Net.ReadUInt16Async(val.Address);
                        if (!uint16Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = uint16Read.Message;
                            ret.ErrorCode = uint16Read.ErrorCode;
                        }

                        ret.Value = uint16Read.Content;
                        break;
                    case DataTypeEnum.Int16:
                        var int16Read = await _siemensS7Net.ReadInt16Async(val.Address);
                        if (!int16Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = int16Read.Message;
                            ret.ErrorCode = int16Read.ErrorCode;
                        }

                        ret.Value = int16Read.Content;
                        break;
                    case DataTypeEnum.Uint32:
                        var uint32Read = await _siemensS7Net.ReadUInt32Async(val.Address);
                        if (!uint32Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = uint32Read.Message;
                            ret.ErrorCode = uint32Read.ErrorCode;
                        }

                        ret.Value = uint32Read.Content;
                        break;
                    case DataTypeEnum.Int32:
                        var int32Read = await _siemensS7Net.ReadInt32Async(val.Address);
                        if (!int32Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = int32Read.Message;
                            ret.ErrorCode = int32Read.ErrorCode;
                        }

                        ret.Value = int32Read.Content;
                        break;
                    case DataTypeEnum.Float:
                        var floatRead = await _siemensS7Net.ReadFloatAsync(val.Address);
                        if (!floatRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = floatRead.Message;
                            ret.ErrorCode = floatRead.ErrorCode;
                        }

                        if (float.IsNaN(floatRead.Content))
                            floatRead.Content = 0;
                        ret.Value = floatRead.Content;
                        break;
                    case DataTypeEnum.Uint64:
                        var uint64Read = await _siemensS7Net.ReadUInt64Async(val.Address);
                        if (!uint64Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = uint64Read.Message;
                            ret.ErrorCode = uint64Read.ErrorCode;
                        }

                        ret.Value = uint64Read.Content;
                        break;
                    case DataTypeEnum.Int64:
                        var int64Read = await _siemensS7Net.ReadInt64Async(val.Address);
                        if (!int64Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = int64Read.Message;
                            ret.ErrorCode = int64Read.ErrorCode;
                        }

                        ret.Value = int64Read.Content;
                        break;
                    case DataTypeEnum.Double:
                        var doubleRead = await _siemensS7Net.ReadDoubleAsync(val.Address);
                        if (!doubleRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = doubleRead.Message;
                            ret.ErrorCode = doubleRead.ErrorCode;
                        }

                        if (double.IsNaN(doubleRead.Content))
                            doubleRead.Content = 0;
                        ret.Value = doubleRead.Content;
                        break;
                    case DataTypeEnum.String:
                        {
                            OperateResult<string> stringRead;
                            //字符串默认编码
                            val.Length = val.Length == 0 ? val.Length = 10 : val.Length;
                            switch (val.Encoding)
                            {
                                case StringEnum.Unicode:
                                    stringRead = await _siemensS7Net.ReadStringAsync(val.Address, val.Length, Encoding.Unicode);
                                    break;
                                case StringEnum.Ascii:
                                    stringRead = await _siemensS7Net.ReadStringAsync(val.Address, val.Length, Encoding.ASCII);
                                    break;
                                case StringEnum.UnicodeBig:
                                    stringRead = await _siemensS7Net.ReadStringAsync(val.Address, val.Length,
                                        Encoding.BigEndianUnicode);
                                    break;
                                case StringEnum.Utf32:
                                    stringRead = await _siemensS7Net.ReadStringAsync(val.Address, val.Length, Encoding.UTF32);
                                    break;
                                case StringEnum.Gb2312:
                                    stringRead = await _siemensS7Net.ReadStringAsync(val.Address, val.Length,
                                        Encoding.GetEncoding("gb2312"));
                                    break;
                                case StringEnum.Utf8:
                                default:
                                    stringRead = await _siemensS7Net.ReadStringAsync(val.Address, val.Length, Encoding.UTF8);
                                    break;
                            }

                            if (!stringRead.IsSuccess)
                            {
                                ret.VariableStatus = VariableStatusTypeEnum.Error;
                                ret.Message = stringRead.Message;
                                ret.ErrorCode = stringRead.ErrorCode;
                            }

                            ret.Value = stringRead.Content;
                            break;
                        }
                    case DataTypeEnum.Bcd:
                        {
                            var bcdRead = await _siemensS7Net.ReadAsync(val.Address, 1);
                            if (!bcdRead.IsSuccess)
                            {
                                ret.VariableStatus = VariableStatusTypeEnum.Error;
                                ret.Message = bcdRead.Message;
                                ret.ErrorCode = bcdRead.ErrorCode;
                            }

                            try
                            {
                                ret.Value = Convert.ToInt32(bcdRead.Content.ToHexString());
                            }
                            catch
                            {
                                ret.Value = bcdRead.Content.ToHexString();
                            }

                            break;
                        }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "设备未连接";
                ret.ErrorCode = -1;
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     写设备属性
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public override async Task<WriteResponse> WriteAsync(DriverAddressIoArgModel val)
    {
        WriteResponse response = new() { IsSuccess = false };
        try
        {
            if (!S7IsConnected)
            {
                response.Description = "设备连接已断开";
                return response;
            }

            var write = new OperateResult();
            switch (val.DataType)
            {
                case DataTypeEnum.Bool:
                    write = await _siemensS7Net!.WriteAsync(val.Address, Convert.ToBoolean(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Bit:
                    {
                        var value = Convert.ToInt32(val.Value) == 1;
                        write = await _siemensS7Net!.WriteAsync(val.Address, value);
                        if (!write.IsSuccess)
                            response.Description = write.Message;
                        break;
                    }
                case DataTypeEnum.Uint16:
                    write = await _siemensS7Net!.WriteAsync(val.Address, Convert.ToUInt16(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Int16:
                    write = await _siemensS7Net!.WriteAsync(val.Address, Convert.ToInt16(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Uint32:
                    write = await _siemensS7Net!.WriteAsync(val.Address, Convert.ToUInt32(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Int32:
                case DataTypeEnum.Bcd:
                    write = await _siemensS7Net!.WriteAsync(val.Address, Convert.ToInt32(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Float:
                    write = await _siemensS7Net!.WriteAsync(val.Address, Convert.ToSingle(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Uint64:
                    write = await _siemensS7Net!.WriteAsync(val.Address, Convert.ToUInt64(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Int64:
                    write = await _siemensS7Net!.WriteAsync(val.Address, Convert.ToInt64(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.Double:
                    write = await _siemensS7Net!.WriteAsync(val.Address, Convert.ToDouble(val.Value));
                    if (!write.IsSuccess)
                        response.Description = write.Message;
                    break;
                case DataTypeEnum.String:
                    {
                        var strValue = val.Value ?? "";
                        // 将字符串转换为字节数组
                        var byteArray = Encoding.UTF8.GetBytes(strValue);
                        // 如果字节数组长度小于固定长度，进行填充操作
                        if (byteArray.Length < val.Length * 2)
                        {
                            Array.Resize(ref byteArray, val.Length * 2);
                            write = await _siemensS7Net!.WriteAsync(val.Address, byteArray);
                        }
                        else
                        {
                            switch (val.Encoding)
                            {
                                case StringEnum.Unicode:
                                    write = await _siemensS7Net!.WriteAsync(val.Address, strValue, val.Length, Encoding.Unicode);
                                    break;
                                case StringEnum.Ascii:
                                    write = await _siemensS7Net!.WriteAsync(val.Address, strValue, val.Length, Encoding.ASCII);
                                    break;
                                case StringEnum.UnicodeBig:
                                    write = await _siemensS7Net!.WriteAsync(val.Address, strValue, val.Length, Encoding.BigEndianUnicode);
                                    break;
                                case StringEnum.Utf32:
                                    write = await _siemensS7Net!.WriteAsync(val.Address, strValue, val.Length, Encoding.UTF32);
                                    break;
                                case StringEnum.Gb2312:
                                    write = await _siemensS7Net!.WriteAsync(val.Address, strValue, val.Length, Encoding.GetEncoding("gb2312"));
                                    break;
                                case StringEnum.Utf8:
                                default:
                                    write = await _siemensS7Net!.WriteAsync(val.Address, strValue, val.Length, Encoding.UTF8);
                                    break;
                            }
                        }

                        if (!write.IsSuccess)
                            response.Description = write.Message;
                        break;
                    }
                default:
                    response.Description = "数据类型不支持写入";
                    break;
            }

            // Log.Information($"【写入设备】 地址:【{val.Address}】,值:【{Convert.ToString(val.Value)}】,结果:【{JSON.Serialize(write)}】");
            if (write.IsSuccess)
            {
                response.Description = "写入成功！";
                response.IsSuccess = true;
            }
        }
        catch (Exception ex)
        {
            response.Description = $"写入失败:{ex.Message},请求数据:{val}";
        }

        return response;
    }

    /// <summary>
    ///     0:手动模式; 1:子运行模式示教 ;2:mdi 3:自动运行模式 ;4:再定位,重新逼近轮廓; 5:返回参考点 ;6:以可变增量运行; 7:增量进给 8:未知
    /// </summary>
    /// <returns></returns>
    [Method("WorkMode", description: "0:手动模式; 1:子运行模式示教 ;2:mdi 3:自动运行模式 ;4:再定位,重新逼近轮廓; 5:返回参考点 ;6:以可变增量运行; 7:增量进给 8:未知", name: "模式")]
    public async Task<DriverReturnValueModel> ReadWorkMode()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSysModeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     RESET = 0:复位 STOP = 1:程序块结束 HOLD = 2:进给保持; START = 3:程序运行 SPENDLE_CW_CCW = 4:主轴正反转 OTHER =
    ///     5 一般是报警
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", description: "RESET = 0:复位 STOP = 1:程序块结束 HOLD = 2:进给保持; START = 3:程序运行 SPENDLE_CW_CCW = 4:主轴正反转 OTHER = 5 一般是报警", name: "状态")]
    public async Task<DriverReturnValueModel> ReadRunStatus()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSysStatusAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     R变量
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("R", name: "R变量")]
    public async Task<DriverReturnValueModel> R(DriverAddressIoArgModel val)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Double,
            Id = val.Id
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadRVariableAsync(val.Address);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     报警号
    /// </summary>
    /// <returns></returns>
    [Method("AlarmInfo", name: "报警号")]
    public async Task<DriverReturnValueModel> ReadAlarmInfo()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSystemAlarmInfoAsync(1);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    [Method("CycleTime", TransPondDataTypeEnum.Double, name: "循环时间")]
    public async Task<DriverReturnValueModel> ReadCycleTime()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCycleTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     工件数量
    /// </summary>
    /// <returns></returns>
    [Method("CurrentNum", TransPondDataTypeEnum.Double, name: "工件数量")]
    public async Task<DriverReturnValueModel> ReadCurrentNum()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCurrentProduceCountAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     剩余加工时间
    /// </summary>
    /// <returns></returns>
    [Method("RemainTime", TransPondDataTypeEnum.Double, name: "剩余加工时间")]
    public async Task<DriverReturnValueModel> ReadRemainTime()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadRemainTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     设定主轴速度
    /// </summary>
    /// <returns></returns>
    [Method("SetSpeed", TransPondDataTypeEnum.Double, name: "设定主轴速度")]
    public async Task<DriverReturnValueModel> ReadSetSpeed()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSetSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    [Method("ActSpeed", TransPondDataTypeEnum.Double, name: "主轴转速")]
    public async Task<DriverReturnValueModel> ReadActSpeed()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadActSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴倍率
    /// </summary>
    /// <returns></returns>
    [Method("SpindleRate", TransPondDataTypeEnum.Double, name: "主轴倍率")]
    public async Task<DriverReturnValueModel> ReadSpindleRate()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSpindleRateAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     设定进给速度
    /// </summary>
    /// <returns></returns>
    [Method("SetFSpeed", TransPondDataTypeEnum.Double, name: "设定进给速度")]
    public async Task<DriverReturnValueModel> ReadSetFSpeed()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSetFspeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给速度
    /// </summary>
    /// <returns></returns>
    [Method("ActFSpeed", TransPondDataTypeEnum.Double, name: "进给速度")]
    public async Task<DriverReturnValueModel> ReadActFSpeed()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadActFspeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给轴倍率
    /// </summary>
    /// <returns></returns>
    [Method("FeedRate", TransPondDataTypeEnum.Double, name: "进给轴倍率")]
    public async Task<DriverReturnValueModel> ReadFeedRate()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadFeedRateAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    [Method("MainName", TransPondDataTypeEnum.String, name: "程序名")]
    public async Task<DriverReturnValueModel> ReadMainName()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSystemProgramCurrentAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     刀具号
    /// </summary>
    /// <returns></returns>
    [Method("ToolNum", name: "刀具号")]
    public async Task<DriverReturnValueModel> ReadToolNum()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int16
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadToolNumAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     报警个数
    /// </summary>
    /// <returns></returns>
    [Method("AlarmCount", name: "报警个数")]
    public async Task<DriverReturnValueModel> ReadAlarmNum()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int16
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadAlarmNumAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }
                int alarmNum = read.Content;
                // 读取CNC系统类型 
                var readCncType = await Driver.ReadCncTypeAsync();
                if (readCncType.IsSuccess)
                {
                    // 检查是否是802D的类型
                    if (!readCncType.Content.Contains("802D") && _siemensS7Net != null)
                    {
                        // 不是802D类型累加
                        var readPlc = await _siemensS7Net.ReadPlcAlarmAsync();
                        if (readPlc.IsSuccess)
                            alarmNum += readPlc.Content1;
                    }
                }
                ret.Value = alarmNum;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     剩余坐标
    /// </summary>
    /// <returns></returns>
    [Method("RemainPos", TransPondDataTypeEnum.String, name: "剩余坐标")]
    public async Task<DriverReturnValueModel> ReadRemainPos()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadRemainPosAsync(AxisCount);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = JSON.Serialize(read.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     机械坐标
    /// </summary>
    /// <returns></returns>
    [Method("MachPos", TransPondDataTypeEnum.String, name: "机械坐标")]
    public async Task<DriverReturnValueModel> ReadMachPos()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadMachinePosAsync(AxisCount);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = JSON.Serialize(read.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     相对坐标
    /// </summary>
    /// <returns></returns>
    [Method("RelPos", TransPondDataTypeEnum.String, name: "相对坐标")]
    public async Task<DriverReturnValueModel> ReadRelPos()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadRelativePosAsync(AxisCount);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = JSON.Serialize(read.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     第一轴负载
    /// </summary>
    /// <returns></returns>
    [Method("FeedLoadX", TransPondDataTypeEnum.Double, name: "第一轴负载")]
    public async Task<DriverReturnValueModel> ReadFeedLoadX()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadFeedLoadXAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     第二轴负载
    /// </summary>
    /// <returns></returns>
    [Method("FeedLoadY", TransPondDataTypeEnum.Double, name: "第二轴负载")]
    public async Task<DriverReturnValueModel> ReadFeedLoadY()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadFeedLoadYAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     第三轴负载
    /// </summary>
    /// <returns></returns>
    [Method("FeedLoadZ", TransPondDataTypeEnum.Double, name: "第三轴负载")]
    public async Task<DriverReturnValueModel> ReadFeedLoadZ()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadFeedLoadZAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     第四轴负载
    /// </summary>
    /// <returns></returns>
    [Method("FeedLoadB", TransPondDataTypeEnum.Double, name: "第四轴负载")]
    public async Task<DriverReturnValueModel> ReadFeedLoadB()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadFeedLoadBAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     第五轴负载
    /// </summary>
    /// <returns></returns>
    [Method("FeedLoadC", TransPondDataTypeEnum.Double, name: "第五轴负载")]
    public async Task<DriverReturnValueModel> ReadFeedLoadC()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadFeedLoadCAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    [Method("Alarm", TransPondDataTypeEnum.Bool, name: "报警状态")]
    public async Task<DriverReturnValueModel> ReadAlarm()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Bool
        };
        try
        {
            if (IsConnected)
            {
                // 读cnc报警数量
                var read = await Driver.ReadAlarmNumAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                var allNum = read.Content;
                // 读取CNC系统类型 
                var readCncType = await Driver.ReadCncTypeAsync();
                if (readCncType.IsSuccess)
                {
                    // 检查是否是802D的类型
                    if (!readCncType.Content.Contains("802D") && _siemensS7Net != null)
                    {
                        // 不是802D类型累加
                        var readPlc = await _siemensS7Net.ReadPlcAlarmAsync();
                        if (readPlc.IsSuccess)
                        {
                            // 2025-06-30 改动 - 逻辑改为 cnc报警数量或者plc报警数量都不是0的才认为报警
                            allNum = (short)(read.Content + readPlc.Content1);
                        }
                    }
                }
                ret.Value = allNum > 0;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     PLC报警信息
    /// </summary>
    /// <returns></returns>
    [Method("AlarmMessage", TransPondDataTypeEnum.String, name: "PLC报警信息")]
    public async Task<DriverReturnValueModel> ReadAlarmMessage()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected && _siemensS7Net != null)
            {
                // 读取CNC系统类型 
                var readCncType = await Driver.ReadCncTypeAsync();
                if (readCncType.IsSuccess)
                {
                    // 检查是否是802D的类型
                    if (!readCncType.Content.Contains("802D") && _siemensS7Net != null)
                    { 
                        // 读取plc报警状态
                        var read = await _siemensS7Net.ReadPlcAlarmAsync();
                        if (!read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = read.Message;
                        }
                        else
                        {   // 非802D正常返回
                            ret.Value = read.Content2?.Any() == true ? read.Content2.Where(w => w != null).ToJsonString(Formatting.None) : "";
                        }
                    }
                    else
                    {
                        // 802D类型固定返回空值
                        ret.Value = "";
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = _siemensS7Net == null ? "PLC连接未初始化" : "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取报警信息
    /// </summary>
    /// <param name="val">包含报警编号的参数</param>
    /// <returns></returns>
    [Method("SystemAlarmInfo", description: "读取报警信息", name: "系统报警详情")]
    public async Task<DriverReturnValueModel> ReadSystemAlarmInfo(DriverAddressIoArgModel val)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32,
            Id = val.Id
        };
        try
        {
            if (IsConnected)
            {
                // 从地址中解析报警编号，如果地址为空或无效，默认读取第0个报警
                int alarmNum = 0;
                if (!string.IsNullOrEmpty(val.Address) && int.TryParse(val.Address, out int parsedNum))
                {
                    alarmNum = parsedNum;
                }

                var read = await Driver.ReadSystemAlarmInfoAsync(alarmNum);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }
                else
                {
                    ret.Value = read.Content;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取CNC系统类型
    /// </summary>
    /// <returns></returns>
    [Method("CncType", TransPondDataTypeEnum.String, name: "系统类型")]
    public async Task<DriverReturnValueModel> ReadCncType()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCncTypeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }
                else
                {
                    ret.Value = read.Content;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取CNC序列号
    /// </summary>
    /// <returns></returns>
    [Method("CncSerialNum", TransPondDataTypeEnum.String, name: "序列号")]
    public async Task<DriverReturnValueModel> ReadCncSerialNum()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCncSerialNumAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }
                else
                {
                    ret.Value = read.Content;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取CNC版本号
    /// </summary>
    /// <returns></returns>
    [Method("CncVersion", TransPondDataTypeEnum.String, name: "版本号")]
    public async Task<DriverReturnValueModel> ReadCncVersion()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCncVersionAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }
                else
                {
                    ret.Value = read.Content;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取轴名称
    /// </summary>
    /// <returns></returns>
    [Method("AxisNames", TransPondDataTypeEnum.String, name: "轴名称")]
    public async Task<DriverReturnValueModel> ReadAxisNames()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadAxisNamesAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }
                else
                {
                    ret.Value = JsonConvert.SerializeObject(read.Content);
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取温度
    /// </summary>
    /// <returns></returns>
    [Method("Temperature", TransPondDataTypeEnum.Double, name: "主轴温度")]
    public async Task<DriverReturnValueModel> ReadTemperature()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Double
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadTemperAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }
                else
                {
                    ret.Value = read.Content;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
    /// <summary>
    ///     程序行号
    /// </summary>
    /// <returns></returns>
    [Method("ProgramNumber", name: "程序行号")]
    public async Task<DriverReturnValueModel> ReadProgramNumber()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Int32 };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadProgramNumberAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
}