using System;
using System.Linq;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Profinet.Siemens;

namespace Siemens.Base;

/// <summary>
///     一个Simens的机床通信类对象里PLC部分
/// </summary>
public class SimensPLC : SiemensS7Net
{
    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="siemensPlc">西门子型号</param>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public SimensPLC(SiemensPLCS siemensPlc, string ipAddress, int port = 102) : base(siemensPlc, ipAddress)
    {
        IpAddress = ipAddress;
        Port = port;
        ByteTransform = new RegularByteTransform();
    }

    /// <inheritdoc />
    // protected override INetMessage GetNewNetMessage() => new CNCFanucSeriesMessage();
    protected override OperateResult InitializationOnConnect()
    {
        var read1 = ReadFromCoreServer(BuildFirstHandShark);
        if (!read1.IsSuccess) return read1;
        var read2 = ReadFromCoreServer(BuildSencondHandShark);
        if (!read2.IsSuccess) return read2;

        return OperateResult.CreateSuccessResult();
    }

    protected override async Task<OperateResult> InitializationOnConnectAsync()
    {
        var read1 = await ReadFromCoreServerAsync(BuildFirstHandShark);
        if (!read1.IsSuccess) return read1;
        var read2 = await ReadFromCoreServerAsync(BuildSencondHandShark);
        if (!read2.IsSuccess) return read2;

        return OperateResult.CreateSuccessResult();
    }

    #endregion

    #region Read Write Support

    /// <summary>
    ///     读取plc 报警个数/报警信息
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int, SysAlarm[]>> ReadPlcAlarmAsync()
    {
        var alarms = new SysAlarm[32];
        short num = 0;
        for (short i = 0; i < 31; i++)
        {
            var read = await ReadFromCoreServerAsync(BuildReadByte((byte)(i * 8)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, SysAlarm[]>(read);
            var result = read.Content;
            var alarm = BitConverter.ToInt16(result.Skip(result.Length - 2).Take(2).ToArray(), 0);
            if (alarm > 0)
            {
                alarms[num] = new SysAlarm
                {
                    AlarmId = num + 1,
                    AlarmNum = "" // 初始化为空字符串
                };

                // 根据位标志设置报警号，每个位对应一个报警
                if ((alarm & 0x01) == 1) alarms[num].AlarmNum = (700000 + i * 8).ToString();
                else if ((alarm & 0x02) == 0x02) alarms[num].AlarmNum = (700001 + i * 8).ToString();
                else if ((alarm & 0x04) == 0x04) alarms[num].AlarmNum = (700002 + i * 8).ToString();
                else if ((alarm & 0x08) == 0x08) alarms[num].AlarmNum = (700003 + i * 8).ToString();
                else if ((alarm & 0x10) == 0x10) alarms[num].AlarmNum = (700004 + i * 8).ToString();
                else if ((alarm & 0x20) == 0x20) alarms[num].AlarmNum = (700005 + i * 8).ToString();
                else if ((alarm & 0x40) == 0x40) alarms[num].AlarmNum = (700006 + i * 8).ToString();
                else if ((alarm & 0x80) == 0x80) alarms[num].AlarmNum = (700007 + i * 8).ToString();

                num++;
            }
        }

        // 过滤有效的报警信息
        var validAlarms = alarms.Where(a => a != null).ToArray();
        return OperateResult.CreateSuccessResult(validAlarms.Length, validAlarms);
    }

    #endregion

    #region Build Command

    /// <summary>
    ///     第一次握手
    /// </summary>
    public byte[] BuildFirstHandShark = @" 
                                             0x03, 0x00, 0x00, 0x16, 0x11, 0xE0, 0x00, 0x00, 
                                             0x00, 0x01, 0x00, 0xC0, 0x01, 0x0A, 0xC1, 0x02, 
                                             0x01, 0x02, 0xC2, 0x02, 0x01, 0x02
                                          ".ToHexBytes();

    /// <summary>
    ///     第二次握手
    /// </summary>
    public byte[] BuildSencondHandShark = @" 
                                             0x03, 0x00, 0x00, 0x19, 0x02, 0xF0, 0x80, 0x32, 
                                             0x01, 0x00, 0x00, 0x04, 0x00, 0x00, 0x08, 0x00, 
                                             0x00, 0xF0, 0x00, 0x00, 0x01, 0x00, 0x01, 0x01,
                                             0xE0
                                          ".ToHexBytes();

    /// <summary>
    ///     读取DB1600寄存器的字节类型:，最后0x00为DB1600.0,依次按字节累计
    /// </summary>
    public byte[] BuildReadByte(byte addr)
    {
        var Buffer = @"0x03, 0x00, 0x00, 0x1F, 0x02, 0xF0, 0x80, 0x32, 
                           0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x0E, 0x00, 
                           0x00, 0x04, 0x01, 0x12, 0x0A, 0x10, 0x02, 0x00, 
                           0x01, 0x06, 0x40, 0x84, 0x00, 0x00, 0x00".ToHexBytes();

        Buffer[Buffer.Length - 1] = addr;
        //  this.ByteTransform.TransByte(addr).CopyTo(Buffer, 30);

        return Buffer;
    }

    #endregion
}