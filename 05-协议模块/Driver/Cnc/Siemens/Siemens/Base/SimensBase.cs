using System;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace Siemens.Simens.Base;

/// <summary>
///     一个Simens的机床通信类对象，支持828D、840D
/// </summary>
public class SimensBase : NetworkDoubleBase
{
    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }

    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public SimensBase(string ipAddress, int port = 102)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    /// <inheritdoc />
    // protected override INetMessage GetNewNetMessage() => new CNCFanucSeriesMessage();
    protected override OperateResult InitializationOnConnect(Socket socket)
    {
        var read1 = ReadFromCoreServer(socket, BuildFirstHandShark);
        if (!read1.IsSuccess) return read1;
        var read2 = ReadFromCoreServer(socket, BuildSencondHandShark);
        if (!read2.IsSuccess) return read2;
        var read3 = ReadFromCoreServer(socket, BuildThreeHandShark);
        if (!read2.IsSuccess) return read3;

        return OperateResult.CreateSuccessResult();
    }


    protected override async Task<OperateResult> InitializationOnConnectAsync(Socket socket)
    {
        var read1 = await ReadFromCoreServerAsync(socket, BuildFirstHandShark);
        if (!read1.IsSuccess) return read1;
        var read2 = await ReadFromCoreServerAsync(socket, BuildSencondHandShark);
        if (!read2.IsSuccess) return read2;
        var read3 = await ReadFromCoreServerAsync(socket, BuildThreeHandShark);
        if (!read2.IsSuccess) return read3;

        return OperateResult.CreateSuccessResult();
    }

    #endregion

    #region Read Write Support

    /// <summary>
    ///     读取系统类型
    /// </summary>
    /// <returns></returns>
    public OperateResult<string> ReadCncType()
    {
        var read = ReadFromCoreServer(BuildCncType());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        var result = read.Content.RemoveBegin(25);
        var type = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(type);
    }


    /// <summary>
    ///     读取序列号
    /// </summary>
    /// <returns></returns>
    public OperateResult<string> ReadCncSerialNum()
    {
        var read = ReadFromCoreServer(BuildCncSerialNum());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        var result = read.Content.RemoveBegin(25);
        var serialnum = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(serialnum);
    }

    /// <summary>
    ///     版本号
    /// </summary>
    /// <returns></returns>
    public OperateResult<string> ReadCncVersion()
    {
        var read = ReadFromCoreServer(BuildCncVersion());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        var result = read.Content.RemoveBegin(25);
        var version = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(version);
    }


    /// <summary>
    ///     读取当前系统状态RESET = 0：复位 STOP = 1：程序块结束 HOLD = 2：进给保持
    ///     START = 3：程序运行 SPENDLE_CW_CCW = 4：主轴正反转 OTHER = 5 一般是报警
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSysStatus()
    {
        var read = ReadFromCoreServer(BuildReadSysStatus());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = 5;

        if (read.Content[24] == 0x02)
        {
            if (read.Content[25] == 0x00 && read.Content[31] == 0x05) result = 0;
            else if (read.Content[25] == 0x02 && read.Content[31] == 0x02) result = 1;
            else if (read.Content[25] == 0x01 && read.Content[31] == 0x03) result = 3;
            else if (read.Content[25] == 0x01 && read.Content[31] == 0x05) result = 4;
            else result = 5;
        }

        return OperateResult.CreateSuccessResult(result);
    }

    /// <summary>
    ///     读取报警个数 通过报警个数判定报警状态
    /// </summary>
    /// <returns></returns>
    public OperateResult<short> ReadAlarmNum()
    {
        var read = ReadFromCoreServer(BuildReadAlarmNum());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);
        var result = read.Content.RemoveBegin(25);
        //short num = ByteTransform.TransInt16(result, 0);
        //if (num > 0) num = 1; else num = 0;
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt16(result, 0));
    }

    /// <summary>
    ///     读取当前工作模式
    ///     0:手动模式; 1:子运行模式示教 ;2:mdi 3:自动运行模式 ;4:再定位，重新逼近轮廓;
    ///     5:返回参考点 ;6:以可变增量运行; 7:增量进给 8:未知
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSysMode()
    {
        var read = ReadFromCoreServer(BuildReadSysMode());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = 5;
        if (read.Content[24] == 0x02)
        {
            if (read.Content[31] == 0x00)
            {
                if (read.Content[25] == 0x00)
                    result = 0;
                else if (read.Content[25] == 0x01)
                    result = 2;
                else if (read.Content[25] == 0x02) result = 3;
            }
            else if (read.Content[31] == 0x03)
            {
                result = 5;
            }
        }

        return OperateResult.CreateSuccessResult(result);
    }

    /// <summary>
    /// 读取当前工件数量
    /// </summary>
    /// <returns></returns>

    //public OperateResult<byte[]> ReadCurrentProduceCount()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadProduct());
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(read);

    //    byte[] result = read.Content;


    //    return OperateResult.CreateSuccessResult(result);
    //}

    /// <summary>
    ///     读取当前工件数量
    /// </summary>
    /// <returns></returns>
    public OperateResult<double> ReadCurrentProduceCount()
    {
        var read = ReadFromCoreServer(BuildReadProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取程序名
    /// </summary>
    /// <returns>程序名</returns>
    public OperateResult<string> ReadSystemProgramCurrent()
    {
        var read = ReadFromCoreServer(BuildReadSystemProgram());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(25);

        var name = Encoding.Default.GetString(result).Replace("\u0000", "");
        if (name == "") name = "未加工";
        return OperateResult.CreateSuccessResult(name);
    }


    public OperateResult<string> ReadProgramBlock()
    {
        var read = ReadFromCoreServer(BuildProgramBlock());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        var result = read.Content.RemoveBegin(25);

        var block = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(block);
    }

    /// <summary>
    ///     轴名称
    /// </summary>
    /// <returns></returns>
    public OperateResult<string[]> ReadAxisNames()
    {
        var read = ReadFromCoreServer(BuildAxisNames());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string[]>(read);
        var result = read.Content.RemoveBegin(25);

        // 读取所有5个轴名称
        var allNames = new string[5];
        for (var i = 0; i < 5; i++) allNames[i] = Encoding.ASCII.GetString(result, i * 16, 16).Replace("\u0000", "");

        // 过滤出有效的轴名称（非空字符串）
        var validNames = allNames.Where(name => !string.IsNullOrEmpty(name.Trim())).ToArray();

        return OperateResult.CreateSuccessResult(validNames);
    }


    /// <summary>
    ///     读取剩余加工时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<double> ReadRemainTime()
    {
        var read = ReadFromCoreServer(BuildReadRemainTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);
        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取循环时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<double> ReadCycleTime()
    {
        var read = ReadFromCoreServer(BuildReadCycleTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);
        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取主轴设定速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<double> ReadSetSpeed()
    {
        var read = ReadFromCoreServer(BuildReadSetSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);
        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取主轴速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<double> ReadActSpeed()
    {
        var read = ReadFromCoreServer(BuildReadActSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);
        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取进给轴设定速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<double> ReadSetFspeed()
    {
        var read = ReadFromCoreServer(BuildReadSetFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);
        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取进给速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<double> ReadActFspeed()
    {
        var read = ReadFromCoreServer(BuildReadActFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);
        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取主轴倍率
    /// </summary>
    /// <returns></returns>
    public OperateResult<double> ReadSpindleRate()
    {
        var read = ReadFromCoreServer(BuildReadSpindleRate());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);
        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取进给倍率
    /// </summary>
    /// <returns></returns>
    public OperateResult<double> ReadFeedRate()
    {
        var read = ReadFromCoreServer(BuildReadFeedRate());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);
        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取刀具号
    /// </summary>
    /// <returns></returns>
    public OperateResult<short> ReadToolNum()
    {
        var read = ReadFromCoreServer(BuildReadToolNum());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);
        var result = read.Content.RemoveBegin(25);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt16(result, 0));
    }

    /// <summary>
    ///     读取机械坐标
    /// </summary>
    /// <returns></returns>
    public OperateResult<double[]> ReadMachinePos()
    {
        // 先读取轴名称以确定有效轴数量
        var axisNamesResult = ReadAxisNames();
        if (!axisNamesResult.IsSuccess)
            return OperateResult.CreateFailedResult<double[]>(axisNamesResult);

        var axisCount = axisNamesResult.Content.Length;
        if (axisCount == 0)
            return new OperateResult<double[]>();

        // 使用通用方法读取坐标
        return ReadCoordinatesByAxisCount(BuildReadMachinePos, axisCount);
    }

    /// <summary>
    ///     读取相对坐标
    /// </summary>
    /// <returns></returns>
    public OperateResult<double[]> ReadRelativePos()
    {
        // 先读取轴名称以确定有效轴数量
        var axisNamesResult = ReadAxisNames();
        if (!axisNamesResult.IsSuccess)
            return OperateResult.CreateFailedResult<double[]>(axisNamesResult);

        var axisCount = axisNamesResult.Content.Length;
        if (axisCount == 0)
            return new OperateResult<double[]>();

        // 使用通用方法读取坐标
        return ReadCoordinatesByAxisCount(BuildRelativePos, axisCount);
    }

    /// <summary>
    ///     剩余坐标
    /// </summary>
    /// <returns></returns>
    public OperateResult<double[]> ReadRemainPos()
    {
        // 先读取轴名称以确定有效轴数量
        var axisNamesResult = ReadAxisNames();
        if (!axisNamesResult.IsSuccess)
            return OperateResult.CreateFailedResult<double[]>(axisNamesResult);

        var axisCount = axisNamesResult.Content.Length;
        if (axisCount == 0)
            return new OperateResult<double[]>();

        // 使用通用方法读取坐标
        return ReadCoordinatesByAxisCount(BuildRemainPos, axisCount);
    }

    /// <summary>
    ///     读取报警号
    /// </summary>
    /// <param name="num">第几个报警</param>
    /// <returns></returns>
    public OperateResult<int> ReadSystemAlarmInfo(int num)
    {
        var intBuff = BitConverter.GetBytes(num);
        var buildreadalarm = BuildReadAlarmInfo();
        buildreadalarm[26] = intBuff[0];
        var read = ReadFromCoreServer(buildreadalarm);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(25);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取R变量
    /// </summary>
    /// <returns></returns>
    public OperateResult<double> ReadRVariable(string address)
    {
        var buildr = BuildReadR();
        var intBuff = BitConverter.GetBytes(Convert.ToInt32(address.Substring(1)) + 1);
        buildr[26] = intBuff[0];
        buildr[25] = intBuff[1];
        var read = ReadFromCoreServer(buildr);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(0).ToArray(), 0));
    }

    /// <summary>
    ///     读取第一轴负载
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadFeedLoadX()
    {
        var read = ReadFromCoreServer(BuildReadFloadX());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content;
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result.Skip(25).Reverse().ToArray(), 0));
    }

    /// <summary>
    ///     读取第二轴负载
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadFeedLoadY()
    {
        var read = ReadFromCoreServer(BuildReadFloadY());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content;
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result.Skip(25).Reverse().ToArray(), 0));
    }

    /// <summary>
    ///     读取第三轴负载
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadFeedLoadZ()
    {
        var read = ReadFromCoreServer(BuildReadFloadZ());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content;
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result.Skip(25).Reverse().ToArray(), 0));
    }

    /// <summary>
    ///     读取第四轴负载
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadFeedLoadB()
    {
        var read = ReadFromCoreServer(BuildReadFloadB());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content;
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result.Skip(25).Reverse().ToArray(), 0));
    }


    /// <summary>
    ///     读取第五轴负载
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadFeedLoadC()
    {
        var read = ReadFromCoreServer(BuildReadFloadC());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content;
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result.Skip(25).Reverse().ToArray(), 0));
    }

    /// <summary>
    ///     电机温度
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadTemper()
    {
        var read = ReadFromCoreServer(BuildTemper());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content;
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result.Skip(25).Reverse().ToArray(), 0));
    }
    
    /// <summary>
    /// 读取程序行号
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadProgramNumber()
    {
        OperateResult<byte[]> read = ReadFromCoreServer(BuildProgramNumber());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        byte[] result = read.Content.RemoveBegin(25);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    #endregion

    #region Build Command

    /// <summary>
    ///     第一次握手
    /// </summary>
    public byte[] BuildFirstHandShark = @" 
                                          0x03, 0x00, 0x00, 0x16, 0x11, 0xe0, 0x00, 0x00, 
                                          0x00, 0x48, 0x00, 0xc1, 0x02, 0x04, 0x00, 0xc2,
                                          0x02, 0x0d, 0x04, 0xc0, 0x01, 0x0a
                                          ".ToHexBytes();

    /// <summary>
    ///     第二次握手
    /// </summary>
    public byte[] BuildSencondHandShark = @" 
                                          0x03, 0x00, 0x00, 0x19, 0x02, 0xf0, 0x80, 0x32, 
                                          0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x08, 0x00, 
                                          0x00, 0xf0, 0x00, 0x00, 0x64, 0x00, 0x64, 0x03, 
                                          0xc0
                                          ".ToHexBytes();

    /// <summary>
    ///     第三次握手
    /// </summary>
    public byte[] BuildThreeHandShark = @" 
                                          0x03, 0x00, 0x00, 0x1d, 0x02, 0xf0, 0x80, 0x32, 
                                          0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x0c, 0x00, 
                                          0x00, 0x04, 0x01, 0x12, 0x08, 0x82, 0x01, 0x00, 
                                          0x14, 0x00, 0x01, 0x3b, 0x01, 0x03, 0x00, 0x00, 
                                          0x07, 0x02, 0xf0, 0x00
                                          ".ToHexBytes();


    /// <summary>
    ///     系统状态
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysStatus()
    {
        var buffer = @"
                            0x03, 0x00, 0x00, 0x27, 0x02, 0xf0, 0x80, 0x32, 
                            0x01, 0x00, 0x00, 0x00, 0x14, 0x00, 0x16, 0x00,
                            0x00, 0x04, 0x02, 0x12, 0x08, 0x82, 0x41, 0x00,
                            0x0b, 0x00, 0x01, 0x7f, 0x01, 0x12, 0x08, 0x82,
                            0x41, 0x00, 0x0d, 0x00, 0x01, 0x7f, 0x01, 0x03,
                            0x00, 0x00, 0x07, 0x02, 0xf0, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警个数
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAlarmNum()
    {
        var buffer = @"
                            0x03,0x00,0x00, 0x1d,                   
		                    0x02,0xf0,0x80,0x32,
                            0x01,
                            0x00,0x00,0x00,0x0c,
                            0x00,0x0c,                
		                    0x00,0x00,
                            0x04,
                            0x01,
                            0x12,0x08,0x82,0x01,0x00,0x07,0x00,0x01,0x7f,0x01,
                            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     系统模式
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysMode()
    {
        var buffer = @"
                            0x03, 0x00, 0x00, 0x27,   
                            0x02, 0xf0, 0x80, 0x32, 0x01,
                            0x00, 0x00, 0x00, 0x14,
                            0x00, 0x16, 
                            0x00, 0x00,
                            0x04,
                            0x02,
                            0x12, 0x08, 0x82, 0x21, 0x00, 0x03, 0x00, 0x01, 0x7f, 0x01,
                            0x12, 0x08, 0x82, 0x41, 0x00, 0x0c, 0x00, 0x01, 0x7f, 0x01,
                            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     工件当前产量
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadProduct()
    {
        var buffer = @"
                            0x03, 0x00, 0x00, 0x1d, 0x02, 0xf0, 0x80, 0x32, 
                            0x01, 0x00, 0x00, 0x00, 0x11, 0x00, 0x0c, 0x00, 
                            0x00, 0x04, 0x01, 0x12, 0x08, 0x82, 0x41, 0x00, 
                            0x79, 0x00, 0x01, 0x7f, 0x01, 0x03, 0x00, 0x00, 
                            0x07, 0x02, 0xf0, 0x00
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSystemProgram()
    {
        var buffer = @"
                            0x03, 0x00, 0x00, 0x1d,    
                            0x02, 0xf0, 0x80, 0x32, 0x01,
                            0x00, 0x00, 0x00, 0x14,
                            0x00, 0x0c, 
                            0x00, 0x00,
                            0x04,
                            0x01,
                            0x12, 0x08, 0x82, 0x41, 0x00, 0x0c, 0x00, 0x01, 0x7a, 0x01,
                            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     剩余时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadRemainTime()
    {
        var buffer = @"
                           0x03, 0x00, 0x00, 0x1d,
                           0x02, 0xf0, 0x80, 0x32, 0x01,
                           0x00, 0x00, 0x00, 0x14,
                           0x00, 0x0c, 
                           0x00, 0x00,
                           0x04,
                           0x01,
                           0x12, 0x08, 0x82, 0x41, 0x01, 0x2a, 0x00, 0x01, 0x7f, 0x01,
                           0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCycleTime()
    {
        var buffer = @"
                            0x03, 0x00, 0x00, 0x1d,   
                            0x02, 0xf0, 0x80, 0x32, 0x01,
                            0x00, 0x00, 0x00, 0x14,
                            0x00, 0x0c, 
                            0x00, 0x00,
                            0x04,
                            0x01,
                            0x12, 0x08, 0x82, 0x41, 0x01, 0x29, 0x00, 0x01, 0x7f, 0x01,
                            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     主轴设定速度
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSetSpeed()
    {
        var buffer = @"
                            0x03, 0x00, 0x00, 0x1d,   
                            0x02, 0xf0, 0x80, 0x32, 0x01,
                            0x00, 0x00, 0x00, 0x14,
                            0x00, 0x0c, 
                            0x00, 0x00,
                            0x04,
                            0x01,
                            0x12, 0x08, 0x82, 0x01, 0x00, 0x03, 0x00, 0x04, 0x72, 0x01,
                            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     主轴速度
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActSpeed()
    {
        var buffer = @"
                            0x03, 0x00, 0x00, 0x1d,   
                            0x02, 0xf0, 0x80, 0x32, 0x01,
                            0x00, 0x00, 0x00, 0x14,
                            0x00, 0x0c, 
                            0x00, 0x00,
                            0x04,
                            0x01,
                            0x12, 0x08, 0x82, 0x41, 0x00, 0x02, 0x00, 0x01, 0x72, 0x01,
                            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     进给设定转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSetFspeed()
    {
        var buffer = @"
                             0x03, 0x00, 0x00, 0x1d,   
                             0x02, 0xf0, 0x80, 0x32, 0x01,
                             0x00, 0x00, 0x00, 0x12,
                             0x00, 0x0c, 
                             0x00, 0x00,
                             0x04,
                             0x01,
                             0x12, 0x08, 0x82, 0x41, 0x00, 0x02, 0x00, 0x01, 0x7f, 0x01,
                             0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     进给速度
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActFspeed()
    {
        var buffer = @"
                            0x03, 0x00, 0x00, 0x1d,   
                            0x02, 0xf0, 0x80, 0x32, 0x01,
                            0x00, 0x00, 0x00, 0x12,
                            0x00, 0x0c, 
                            0x00, 0x00,
                            0x04,
                            0x01,
                            0x12,0x08,0x82,0x41,0x00,0x01,0x00,0x01,0x7f,0x01,
                            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     主轴倍率报文
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSpindleRate()
    {
        var buffer = @"
                            0x03, 0x00, 0x00, 0x1d,   
                            0x02, 0xf0, 0x80, 0x32, 0x01,
                            0x00, 0x00, 0x00, 0x14,
                            0x00, 0x0c,  
                            0x00, 0x00,
                            0x04,
                            0x01,
                            0x12, 0x08, 0x82, 0x41, 0x00, 0x04, 0x00, 0x01, 0x72, 0x01,
                            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     进给倍率报文
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadFeedRate()
    {
        var buffer = @"
                            0x03, 0x00, 0x00, 0x1d,   
                            0x02, 0xf0, 0x80, 0x32, 0x01,
                            0x00, 0x00, 0x00, 0x13,
                            0x00, 0x0c, 
                            0x00, 0x00,
                            0x04,
                            0x01,
                            0x12, 0x08, 0x82, 0x41, 0x00, 0x03, 0x00, 0x01, 0x7f, 0x01,
                            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00
                           ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     刀具号报文
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadToolNum()
    {
        var buffer = @"
                            0x03, 0x00, 0x00, 0x1d,   
                            0x02, 0xf0, 0x80, 0x32, 0x01,
                            0x00, 0x00, 0x00, 0x14,
                            0x00, 0x0c,  
                            0x00, 0x00,
                            0x04,
                            0x01,
                            0x12, 0x08, 0x82, 0x41, 0x00, 0x17, 0x00, 0x01, 0x7f, 0x01,
                            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00};
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAlarmInfo()
    {
        var buffer = @"
                            0x03,0x00,0x00,
                            0x27,                    
		                    0x02,0xf0,0x80,0x32,
                            0x01,
                            0x00,0x00,0x00,0x0d,
                            0x00,0x16,                
		                    0x00,0x00,
                            0x04,
                            0x02,
                            0x12,0x08,0x82,0x01,
                            0x00,0x01,
                            0x00,0x01,    
		                    0x77,0x01,   
	                        0x12,0x08,0x82,0x01,0x00,0x04,0x00,0x01,0x77,0x01
                            ".ToHexBytes();
        return buffer;
    }


    //机械坐标MECPOS
    public byte[] BuildReadMachinePos()
    {
        var Buffer = @" 0x03, 0x00, 0x00, 0x1d,   
            0x02, 0xf0, 0x80, 0x32, 0x01,
            0x00, 0x00, 0x00, 0x14,
            0x00, 0x0c, 
            0x00, 0x00,
            0x04,
            0x01,
            0x12, 0x08, 0x82, 0x41, 0x00, 0x02, 0x00,  0x01, 0x74, 0x01,
            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     相对坐标、工件坐标
    /// </summary>
    /// <returns></returns>
    public byte[] BuildRelativePos()
    {
        var Buffer = @" 0x03, 0x00, 0x00, 0x1d,   
            0x02, 0xf0, 0x80, 0x32, 0x01,
            0x00, 0x00, 0x00, 0x14,
            0x00, 0x0c,  
            0x00, 0x00,
            0x04,
            0x01,
            0x12, 0x08, 0x82, 0x41, 0x00, 0x19, 0x00, 0x01, 0x70, 0x01,
            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     剩余坐标
    /// </summary>
    /// <returns></returns>
    public byte[] BuildRemainPos()
    {
        var Buffer = @"0x03, 0x00, 0x00, 0x1d,  
            0x02, 0xf0, 0x80, 0x32, 0x01,
            0x00, 0x00, 0x00, 0x14,
            0x00, 0x0c,  
            0x00, 0x00,
            0x04,
            0x01,
            0x12, 0x08, 0x82, 0x41, 0x00, 0x03, 0x00, 0x01, 0x74, 0x01, 
            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     读取R变量
    ///     后[25][26]=b-1=10
    /// </summary>
    public byte[] BuildReadR()
    {
        var Buffer = @"0x03, 0x00, 0x00, 0x1d,   
          0x02, 0xf0, 0x80, 0x32, 0x01,
          0x00, 0x00, 0x00, 0x10,
          0x00, 0x0c,
          0x00, 0x00,
          0x04,
          0x01,
          0x12,0x08,0x82,0x41, 0x00,0x01,0x01,0x2c,      
		  0x15,0x01,
          0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     写R变量
    /// </summary>
    public byte[] BuildWriteR()
    {
        var Buffer = @"0x03,0x00,0x00,0x29,
               0x02,0xf0,0x80,0x32,0x01,
               0x00,0x00,0x00,0x12,
               0x00,0x0c,0x00,0x0c,0x05,0x01,
               0x12,0x08,0x82,0x41,0x00,0x01,
               0x00,0x01,    
	           0x15,0x01,
               0x00,0x09,
               0x00,0x08,
               0x00,0x00,0x00,0x00,0x44,0xf4,0x20,0x41".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     第三轴负载   28
    /// </summary>
    public byte[] BuildReadFloadZ()
    {
        var Buffer = @" 0x03, 0x00, 0x00, 0x1d,
     0x02, 0xf0, 0x80, 0x32, 0x01,
     0x00, 0x00, 0x00, 0x14,
     0x00, 0x0c,
     0x00, 0x00,
     0x04,
     0x01,
     0x12, 0x08, 0x82, 0xa3, 0x00, 0x51, 0x00, 0x01, 0x82, 0x01,
     0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     第一轴负载  R81  倒数第三位R驱动器下标，如R37[1],0X03=R37[2];倒数第五位为地址，如25为十进制37，第七位为轴，依次递增切换轴
    /// </summary>
    public byte[] BuildReadFloadX()
    {
        var Buffer = @"0x03, 0x00, 0x00, 0x1d,
            0x02, 0xf0, 0x80, 0x32, 0x01,
            0x00, 0x00, 0x00, 0x14,
            0x00, 0x0c,
            0x00, 0x00,
            0x04,
            0x01,
            0x12, 0x08, 0x82, 0xa1, 0x00, 0x51, 0x00, 0x01, 0x82, 0x01,
            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     第二轴负载   28
    /// </summary>
    public byte[] BuildReadFloadY()
    {
        var Buffer = @"0x03, 0x00, 0x00, 0x1d,
            0x02, 0xf0, 0x80, 0x32, 0x01,
            0x00, 0x00, 0x00, 0x14,
            0x00, 0x0c,
            0x00, 0x00,
            0x04,
            0x01,
            0x12, 0x08, 0x82, 0xa2, 0x00, 0x51, 0x00, 0x01, 0x82, 0x01,
            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     第四轴负载   28
    /// </summary>
    public byte[] BuildReadFloadB()
    {
        var Buffer = @"0x03, 0x00, 0x00, 0x1d,
            0x02, 0xf0, 0x80, 0x32, 0x01,
            0x00, 0x00, 0x00, 0x14,
            0x00, 0x0c,
            0x00, 0x00,
            0x04,
            0x01,
            0x12, 0x08, 0x82, 0xa4, 0x00, 0x51, 0x00, 0x01, 0x82, 0x01,
            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     第五轴负载   28
    /// </summary>
    public byte[] BuildReadFloadC()
    {
        var Buffer = @"0x03, 0x00, 0x00, 0x1d,
            0x02, 0xf0, 0x80, 0x32, 0x01,
            0x00, 0x00, 0x00, 0x14,
            0x00, 0x0c,
            0x00, 0x00,
            0x04,
            0x01,
            0x12, 0x08, 0x82, 0xa5, 0x00, 0x51, 0x00, 0x01, 0x82, 0x01,
            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     轴名称
    /// </summary>
    /// <returns></returns>
    public byte[] BuildAxisNames()
    {
        var Buffer = @"0x03,0x00,0x00,0x1d,
             0x02,0xf0,0x80,0x32,0x01,
             0x00,0x00,0x00,0x30,
             0x00,0x0c,
             0x00, 0x00,
             0x04,
             0x01,
             0x12,0x08,0x82,0x41, 0x4e,0x70, 0x00,0x01, 0x1a,0x0a,
             0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     电机温度
    /// </summary>
    /// <returns></returns>
    public byte[] BuildTemper()
    {
        var Buffer = @"0x03, 0x00, 0x00, 0x1d, 0x02, 0xf0, 0x80, 0x32,
                           0x01, 
                           0x00, 0x00, 0x00, 0x11, 0x00, 0x0c, 0x00,
                           0x00, 0x04, 0x01, 0x12, 0x08, 0x82, 0xa1, 0x00,
                           0x23, 0x00, 0x01, 0x82, 0x01, 0x03, 0x00, 0x00,
                           0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     当前程序块
    /// </summary>
    /// <returns></returns>
    public byte[] BuildProgramBlock()
    {
        var Buffer = @"0x03,0x00,0x00,0x1d,0x02,0xf0,0x80,0x32,
                              0x01,0x00,0x00,0x00,0x50,0x00,0x0c,0x00,
                              0x00,0x04,0x01,0x12,0x08,0x82,0x41,0x00,
                              0x1f,0x00,0x01,0x7d,0x01,0x03, 0x00,0x00,
                              0x07, 0x02,0xf0,0x00".ToHexBytes();
        return Buffer;
    }


    /// <summary>
    ///     系统版本
    /// </summary>
    /// <returns></returns>
    public byte[] BuildCncVersion()
    {
        var Buffer = @"0x03, 0x00, 0x00, 0x1d, 
                   0x02, 0xf0, 0x80, 0x32, 0x01,
                   0x00, 0x00, 0x00, 0x14,
                   0x00, 0x0c, 
                   0x00, 0x00,
                   0x04,
                   0x01,
                   0x12, 0x08, 0x82, 0x01, 0x46, 0x78, 0x00, 0x01, 0x1a, 0x01,
                   0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     序列号
    /// </summary>
    /// <returns></returns>
    public byte[] BuildCncSerialNum()
    {
        var Buffer = @"0x03, 0x00, 0x00, 0x1d,    
            0x02, 0xf0, 0x80, 0x32, 0x01,
            0x00, 0x00, 0x00, 0x14,
            0x00, 0x0c,  
            0x00, 0x00,
            0x04,
            0x01,
            0x12, 0x08, 0x82, 0x01, 0x46, 0x6e, 0x00, 0x01, 0x1a, 0x01,
            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     CNC类型
    /// </summary>
    /// <returns></returns>
    public byte[] BuildCncType()
    {
        var Buffer = @"0x03, 0x00, 0x00, 0x1d,    
            0x02, 0xf0, 0x80, 0x32, 0x01,
            0x00, 0x00, 0x00, 0x14,
            0x00, 0x0c,  
            0x00, 0x00,
            0x04,
            0x01,
            0x12, 0x08, 0x82, 0x01, 0x46, 0x78, 0x00, 0x04, 0x1a, 0x01,
            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }
    
    /// <summary>
    /// 程序行号
    /// </summary>
    /// <returns></returns>
    public byte[] BuildProgramNumber()
    {
        byte[] Buffer = @"0x03, 0x00,0x00, 0x1d,
            0x02, 0xf0, 0x80,0x32,0x01,              
            0x00, 0x00,0x00, 0x01,			 
            0x00, 0x0c,       
            0x00, 0x00,        
            0x04,            
            0x01,                
            0x12, 0x08, 0x82, 0x41, 0x00, 0x09, 0x00, 0x01, 0x7d, 0x01,
            0x03, 0x00, 0x00, 0x07, 0x02, 0xf0, 0x00".ToHexBytes();
        return Buffer;
    }

    /// <summary>
    ///     根据轴数量动态读取坐标的通用方法 - 同步版本
    /// </summary>
    /// <param name="buildCommand">构建命令的方法</param>
    /// <param name="axisCount">轴数量</param>
    /// <returns></returns>
    private OperateResult<double[]> ReadCoordinatesByAxisCount(Func<byte[]> buildCommand, int axisCount)
    {
        var coordinates = new double[axisCount];
        var command = buildCommand();

        for (var i = 0; i < axisCount; i++)
        {
            command[26] = (byte)(i + 1); // 轴索引从1开始

            var read = ReadFromCoreServer(command);
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);

            var result = read.Content;
            if (read.Content[3] == 33)
                coordinates[i] = ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0);
            else
                coordinates[i] = ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0);
        }

        return OperateResult.CreateSuccessResult(coordinates);
    }

    #endregion

    #region Async Methods

    /// <summary>
    ///     读取系统类型 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<string>> ReadCncTypeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildCncType());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        var result = read.Content.RemoveBegin(25);
        var type = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(type);
    }

    /// <summary>
    ///     读取序列号 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<string>> ReadCncSerialNumAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildCncSerialNum());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        var result = read.Content.RemoveBegin(25);
        var serialnum = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(serialnum);
    }

    /// <summary>
    ///     版本号 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<string>> ReadCncVersionAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildCncVersion());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        var result = read.Content.RemoveBegin(25);
        var version = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(version);
    }

    /// <summary>
    ///     读取当前系统状态 - 异步版本
    ///     RESET = 0：复位 STOP = 1：程序块结束 HOLD = 2：进给保持
    ///     START = 3：程序运行 SPENDLE_CW_CCW = 4：主轴正反转 OTHER = 5 一般是报警
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysStatusAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysStatus());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = 5;

        if (read.Content[24] == 0x02)
        {
            if (read.Content[25] == 0x00 && read.Content[31] == 0x05) result = 0;
            else if (read.Content[25] == 0x02 && read.Content[31] == 0x02) result = 1;
            else if (read.Content[25] == 0x01 && read.Content[31] == 0x03) result = 3;
            else if (read.Content[25] == 0x01 && read.Content[31] == 0x05) result = 4;
            else result = 5;
        }

        return OperateResult.CreateSuccessResult(result);
    }

    /// <summary>
    ///     读取报警个数 通过报警个数判定报警状态 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<short>> ReadAlarmNumAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadAlarmNum());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);
        var result = read.Content.RemoveBegin(25);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt16(result, 0));
    }

    /// <summary>
    ///     读取当前工作模式 - 异步版本
    ///     0:手动模式; 1:子运行模式示教 ;2:mdi 3:自动运行模式 ;4:再定位，重新逼近轮廓;
    ///     5:返回参考点 ;6:以可变增量运行; 7:增量进给 8:未知
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysModeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysMode());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = 5;
        if (read.Content[24] == 0x02)
        {
            if (read.Content[31] == 0x00)
            {
                if (read.Content[25] == 0x00)
                    result = 0;
                else if (read.Content[25] == 0x01)
                    result = 2;
                else if (read.Content[25] == 0x02) result = 3;
            }
            else if (read.Content[31] == 0x03)
            {
                result = 5;
            }
        }

        return OperateResult.CreateSuccessResult(result);
    }

    /// <summary>
    ///     读取当前工件数量 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<double>> ReadCurrentProduceCountAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取程序名 - 异步版本
    /// </summary>
    /// <returns>程序名</returns>
    public async Task<OperateResult<string>> ReadSystemProgramCurrentAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSystemProgram());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(25);

        var name = Encoding.Default.GetString(result).Replace("\u0000", "");
        if (name == "") name = "未加工";
        return OperateResult.CreateSuccessResult(name);
    }

    /// <summary>
    ///     读取程序块 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<string>> ReadProgramBlockAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildProgramBlock());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        var result = read.Content.RemoveBegin(25);

        var block = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(block);
    }

    /// <summary>
    ///     读取轴名称 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<string[]>> ReadAxisNamesAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildAxisNames());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string[]>(read);
        var result = read.Content.RemoveBegin(25);

        // 读取所有5个轴名称
        var allNames = new string[5];
        for (var i = 0; i < 5; i++) allNames[i] = Encoding.ASCII.GetString(result, i * 16, 16).Replace("\u0000", "");

        // 过滤出有效的轴名称（非空字符串）
        var validNames = allNames.Where(name => !string.IsNullOrEmpty(name.Trim())).ToArray();

        return OperateResult.CreateSuccessResult(validNames);
    }

    /// <summary>
    ///     读取剩余时间 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<double>> ReadRemainTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadRemainTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     根据轴数量动态读取坐标的通用方法
    /// </summary>
    /// <param name="buildCommand">构建命令的方法</param>
    /// <param name="axisCount">轴数量</param>
    /// <returns></returns>
    private async Task<OperateResult<double[]>> ReadCoordinatesByAxisCountAsync(Func<byte[]> buildCommand, int axisCount)
    {
        var coordinates = new double[axisCount];
        var command = buildCommand();

        for (var i = 0; i < axisCount; i++)
        {
            command[26] = (byte)(i + 1); // 轴索引从1开始

            var read = await ReadFromCoreServerAsync(command);
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);

            var result = read.Content;
            if (read.Content[3] == 33)
                coordinates[i] = ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0);
            else
                coordinates[i] = ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0);
        }

        return OperateResult.CreateSuccessResult(coordinates);
    }

    /// <summary>
    ///     读取循环时间 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<double>> ReadCycleTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCycleTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取设定主轴转速 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<double>> ReadSetSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSetSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取主轴转速 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<double>> ReadActSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取设定进给速度 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<double>> ReadSetFspeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSetFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取进给速度 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<double>> ReadActFspeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取主轴倍率 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<double>> ReadSpindleRateAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSpindleRate());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取进给倍率 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<double>> ReadFeedRateAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadFeedRate());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.SelectLast(8).ToArray(), 0));
    }

    /// <summary>
    ///     读取刀具号 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<short>> ReadToolNumAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadToolNum());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);
        var result = read.Content.RemoveBegin(25);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt16(result, 0));
    }

    /// <summary>
    ///     读取机械坐标 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<double[]>> ReadMachinePosAsync(int axisCount)
    {
        // 使用通用方法读取坐标
        return await ReadCoordinatesByAxisCountAsync(BuildReadMachinePos, axisCount);
    }

    /// <summary>
    ///     读取相对坐标 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<double[]>> ReadRelativePosAsync(int axisCount)
    {
        // 使用通用方法读取坐标
        return await ReadCoordinatesByAxisCountAsync(BuildRelativePos, axisCount);
    }

    /// <summary>
    ///     读取剩余坐标 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<double[]>> ReadRemainPosAsync(int axisCount)
    {
        // 使用通用方法读取坐标
        return await ReadCoordinatesByAxisCountAsync(BuildRemainPos, axisCount);
    }

    /// <summary>
    ///     读取报警信息 - 异步版本
    /// </summary>
    /// <param name="num"></param>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSystemAlarmInfoAsync(int num)
    {
        var read = await ReadFromCoreServerAsync(BuildReadAlarmInfo());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(25);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取R变量 - 异步版本
    /// </summary>
    /// <param name="address"></param>
    /// <returns></returns>
    public async Task<OperateResult<double>> ReadRVariableAsync(string address)
    {
        var buildr = BuildReadR();
        var intBuff = BitConverter.GetBytes(Convert.ToInt32(address.Substring(1)) + 1);
        buildr[26] = intBuff[0];
        buildr[25] = intBuff[1];
        var read = await ReadFromCoreServerAsync(buildr);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

        var result = read.Content;
        if (read.Content[3] == 33) return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(25).Take(8).ToArray(), 0));

        return OperateResult.CreateSuccessResult(ByteTransform.TransDouble(result.Skip(0).ToArray(), 0));
    }

    /// <summary>
    ///     读取第一轴负载 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadFeedLoadXAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadFloadX());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        byte[] result = read.Content;
        var value = ByteTransform.TransSingle(result.Skip(25).Reverse().ToArray(), 0) ;
        // Console.WriteLine($"读取第一轴负载: {result.ToHexString()},解析值：{value}");
        return OperateResult.CreateSuccessResult(value);
    }

    /// <summary>
    ///     读取第二轴负载 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadFeedLoadYAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadFloadY());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        byte[] result = read.Content;
        var value = ByteTransform.TransSingle(result.Skip(25).Reverse().ToArray(), 0) ;
        // Console.WriteLine($"读取第二轴负载: {result.ToHexString()},解析值：{value}");
        return OperateResult.CreateSuccessResult(value);
    }

    /// <summary>
    ///     读取第三轴负载 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadFeedLoadZAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadFloadZ());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        byte[] result = read.Content;
        var value = ByteTransform.TransSingle(result.Skip(25).Reverse().ToArray(), 0) ;
        // Console.WriteLine($"读取第三轴负载: {result.ToHexString()},解析值：{value}");
        return OperateResult.CreateSuccessResult(value);
        
    }
    
    /// <summary>
    ///     读取第四轴负载 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadFeedLoadBAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadFloadB());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        byte[] result = read.Content;
        var value = ByteTransform.TransSingle(result.Skip(25).Reverse().ToArray(), 0) ;
        // Console.WriteLine($"读取第四轴负载: {result.ToHexString()},解析值：{value}");
        return OperateResult.CreateSuccessResult(value);
    }

    /// <summary>
    ///     读取第五轴负载 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadFeedLoadCAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadFloadC());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        byte[] result = read.Content;
        var value = ByteTransform.TransSingle(result.Skip(25).Reverse().ToArray(), 0) ;
        // Console.WriteLine($"读取第五轴负载: {result.ToHexString()},解析值：{value}");
        return OperateResult.CreateSuccessResult(value);
    }

    /// <summary>
    ///     读取电机温度 - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadTemperAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildTemper());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content;
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result.Skip(25).Reverse().ToArray(), 0));
    }
    
    
    /// <summary>
    /// 读取程序行号  - 异步版本
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadProgramNumberAsync()
    {
        OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildProgramNumber());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        byte[] result = read.Content.RemoveBegin(25);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    #endregion Async Methods
}