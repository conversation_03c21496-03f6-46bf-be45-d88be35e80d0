using System.Collections.Generic;

namespace Kede.Base;

public class SpindleInfo
{
    public int Nmb { get; set; } // 主轴编号
    public int Max { get; set; } // 最大转速
    public List<SpindleGroup> Groups { get; set; } = new();
}

public class SpindleGroup
{
    public int GroupId { get; set; }
    public int Act { get; set; } // 实际值
    public float Position { get; set; } // 位置
    public int Over { get; set; } // 倍率
}