using System.Linq;
using System.Text;
using System.Xml.Linq;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace Kede.Base;

/// <summary>
///     一个科德CNC的机床通讯库
/// </summary>
public class KedeBase : NetworkDoubleBase
{
    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }

    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public KedeBase(string ipAddress, int port = 62937)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    /// <inheritdoc/>
    // protected override INetMessage GetNewNetMessage() => new CNCFanucSeriesMessage();

    #endregion

    #region Read Write Support

    /// <summary>
    ///     读取当前系统数据包括状态、模式、程序名、程序块   Mode:0:自动模式 1:MI 模式 2:手动模式 3:调试模式
    ///     状态:0:机床准备好1:程多运行，2:进给停止 3:报警 4:暂停
    /// </summary>
    /// <returns></returns>
    public OperateResult<int, int, string, string> ReadNcData()
    {
        var read = ReadFromCoreServer(BuildReadNcData());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, int, string, string>(read);
        var xmldata = Encoding.ASCII.GetString(read.Content);
        var doc = XDocument.Parse(xmldata);
        var ncda = doc.Root; // 获取根元素 <ncda>
        var mode = (int)ncda.Element("mode");
        var ncstate = (int)ncda.Element("ncstate");
        var program = (string)ncda.Element("prg");
        var block = (string)ncda.Element("block");
        return OperateResult.CreateSuccessResult(mode, ncstate, program, block);
    }

    /// <summary>
    ///     读取进给轴信息
    ///     act 实际 G01 进给速度   actf0 实际 G00 进给速度   over G01进给倍率 overf0  G00进给倍率
    /// </summary>
    /// <returns></returns>
    public OperateResult<int, int, int, int> ReadFeedInfo()
    {
        var read = ReadFromCoreServer(BuildReadFeedInfo());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, int, int, int>(read);
        var xmldata = Encoding.ASCII.GetString(read.Content);
        var doc = XDocument.Parse(xmldata);
        var feed = doc.Root; // 获取根元素 <ncda>
        var over = (int)feed.Element("over");
        var overf0 = (int)feed.Element("overf0");
        var act = (int)feed.Element("act");
        var actf0 = (int)feed.Element("actf0");
        return OperateResult.CreateSuccessResult(over, overf0, act, actf0);
    }

    /// <summary>
    ///     主轴信息  act 主轴实际速度  Over主轴倍率
    /// </summary>
    /// <returns></returns>
    public OperateResult<SpindleInfo> ReadSpindInfo()
    {
        var read = ReadFromCoreServer(BuildReadSpindleInfo());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<SpindleInfo>(read);
        var xmldata = Encoding.ASCII.GetString(read.Content);
        var doc = XDocument.Parse(xmldata);
        var spindle = doc.Root; // 获取根元素 <ncda>
        var spindleinfo = new SpindleInfo
        {
            Nmb = (int)spindle.Element("nmb"),
            Max = (int)spindle.Element("max")
        };
        ///动态解析带数字后缀的组
        var groupCount = 1;
        while (true)
        {
            var presetElement = $"preset{groupCount}";
            if (spindle.Element(presetElement) == null) break;

            spindleinfo.Groups.Add(new SpindleGroup
            {
                GroupId = groupCount,
                Act = (int)spindle.Element($"act{groupCount}"),
                Position = (float)spindle.Element($"position{groupCount}"),
                Over = (int)spindle.Element($"over{groupCount}")
            });
            groupCount++;
        }

        return OperateResult.CreateSuccessResult(spindleinfo);
    }

    /// <summary>
    ///     MT信息
    /// </summary>
    /// <returns></returns>
    public OperateResult<string> ReadMTInfo()
    {
        var read = ReadFromCoreServer(BuildReadMTInfo());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        var xmldata = Encoding.ASCII.GetString(read.Content);
        var doc = XDocument.Parse(xmldata);
        var mt = doc.Root; // 获取根元素 <ncda>
        var c4 = (string)mt.Element("c4");
        return OperateResult.CreateSuccessResult(c4);
    }

    /// <summary>
    ///     读取宏变量
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSystemMacroValue(int number)
    {
        var read = ReadFromCoreServer(BuildSystemMacroValue(number));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var xmldata = Encoding.ASCII.GetString(read.Content);
        var sanitizedInput = xmldata.Replace($"<{number}>", $"<_{number}>").Replace($"</{number}>", $"</_{number}>");
        var doc = XDocument.Parse(sanitizedInput);
        var Macro = doc.Root; // 获取根元素 <ncda>
        var value = (int)Macro.Element("value");
        return OperateResult.CreateSuccessResult(value);
    }

    /// <summary>
    ///     机械坐标（有问题会不停的发包）
    /// </summary>
    /// <returns></returns>
    public OperateResult<double[]> ReadMachinePos()
    {
        var read = ReadFromCoreServer(BuildReadMachinePos());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);
        var xmldata = Encoding.ASCII.GetString(read.Content);

        var axes = XDocument.Parse(xmldata).Root;
        var pos = new double[6];
        // 解析轴坐标值 (转换为float)
        for (var i = 0; i < 6; i++)
        {
            var axValue = (string)axes.Element($"ax{i + 1}");
            if (double.TryParse(axValue, out var position))
                pos[i] = position;
            else
                // 处理解析失败的情况
                pos[i] = 0f;
        }

        return OperateResult.CreateSuccessResult(pos);
    }

    /// <summary>
    ///     绝对坐标
    /// </summary>
    /// <returns></returns>
    public OperateResult<double[]> ReadAbsolutePos()
    {
        var read = ReadFromCoreServer(BuildReadAbsolutePos());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);
        var xmldata = Encoding.ASCII.GetString(read.Content);

        var axes = XDocument.Parse(xmldata).Root;
        var pos = new double[6];
        // 解析轴坐标值 (转换为float)
        for (var i = 0; i < 6; i++)
        {
            var axValue = (string)axes.Element($"ax{i + 1}");
            if (double.TryParse(axValue, out var position))
                pos[i] = position;
            else
                // 处理解析失败的情况
                pos[i] = 0f;
        }

        return OperateResult.CreateSuccessResult(pos);
    }

    /// <summary>
    ///     剩余坐标
    /// </summary>
    /// <returns></returns>
    public OperateResult<double[]> ReadRemainingPos()
    {
        var read = ReadFromCoreServer(BuildReadRemainingPos());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);
        var xmldata = Encoding.ASCII.GetString(read.Content);

        var axes = XDocument.Parse(xmldata).Root;
        var pos = new double[6];
        // 解析轴坐标值 (转换为float)
        for (var i = 0; i < 6; i++)
        {
            var axValue = (string)axes.Element($"ax{i + 1}");
            if (double.TryParse(axValue, out var position))
                pos[i] = position;
            else
                // 处理解析失败的情况
                pos[i] = 0f;
        }

        return OperateResult.CreateSuccessResult(pos);
    }

    /// <summary>
    ///     工件产量
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadCurrentProduceCount()
    {
        var read = ReadFromCoreServer(BuildReadProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var xmldata = Encoding.ASCII.GetString(read.Content);
        var product = XDocument.Parse(xmldata).Root;
        var value = (int?)product.Element("v1") ?? 0;
        return OperateResult.CreateSuccessResult(value);
    }

    public OperateResult<SysAlarm> ReadSystemAlarmInfo()
    {
        var read = ReadFromCoreServer(BuildReadRemainingPos());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysAlarm>(read);
        var xmldata = Encoding.ASCII.GetString(read.Content);

        var sysalarm = new SysAlarm();
        var alarm = XDocument.Parse(xmldata).Root;
        if (!alarm.HasElements) return OperateResult.CreateSuccessResult(sysalarm);
        sysalarm.ALarmTime = (string)alarm.Element("localtime");
        sysalarm.AlarmId = ParseIntElement(alarm, "no", 0);
        sysalarm.Priority = ParseIntElement(alarm, "prio", 0);
        ParseMessages(alarm, sysalarm);
        return OperateResult.CreateSuccessResult(sysalarm);
    }

    private static void ParseMessages(XElement element, SysAlarm alarm)
    {
        // 查找所有消息元素 (v1, v2, v3...)
        var messageElements = element.Elements()
            .Where(e => e.Name.LocalName.Length > 1 &&
                        e.Name.LocalName.StartsWith("v") &&
                        char.IsDigit(e.Name.LocalName[1]))
            .OrderBy(e =>
            {
                var numPart = e.Name.LocalName.Substring(1);
                return int.TryParse(numPart, out var num) ? num : int.MaxValue;
            });

        foreach (var msgElement in messageElements)
            if (!string.IsNullOrWhiteSpace(msgElement.Value))
                alarm.Messages.Add(CleanMessage(msgElement.Value));
    }

    private static string CleanMessage(string message)
    {
        return message
            .Replace("\n", " ")
            .Replace("\r", "")
            .Replace("  ", " ")
            .Trim();
    }

    private static int ParseIntElement(XElement parent, string elementName, int defaultValue)
    {
        return int.TryParse(parent.Element(elementName)?.Value, out var result) ? result : defaultValue;
    }

    #endregion


    #region Build Command

    /// <summary>
    ///     NC信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadNcData()
    {
        var buffer = @"
                             0x3c, 0x6e, 0x63, 0x64, 0x61, 0x3e, 0x3c, 0x72,
                             0x65, 0x71, 0x3e, 0x79, 0x65, 0x73, 0x3c, 0x2f,
                             0x72, 0x65, 0x71, 0x3e, 0x3c, 0x76, 0x61, 0x72,
                             0x3e, 0x6d, 0x6f, 0x64, 0x65, 0x2c, 0x70, 0x72,
                             0x67, 0x2c, 0x70, 0x72, 0x6f, 0x63, 0x74, 0x69,
                             0x6d, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x2c,
                             0x70, 0x72, 0x6f, 0x63, 0x74, 0x69, 0x6d, 0x65,
                             0x70, 0x72, 0x6f, 0x63, 0x2c, 0x72, 0x65, 0x6d,
                             0x61, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x74, 0x69,
                             0x6d, 0x65, 0x2c, 0x6e, 0x63, 0x73, 0x74, 0x61,
                             0x74, 0x65, 0x3c, 0x2f, 0x76, 0x61, 0x72, 0x3e,
                             0x3c, 0x2f, 0x6e, 0x63, 0x64, 0x61, 0x3e, 0x0a
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     进给轴信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadFeedInfo()
    {
        var buffer = @"
                            0x3c, 0x66, 0x65, 0x65, 0x64, 0x3e, 0x3c, 0x72,
                            0x65, 0x71, 0x3e, 0x79, 0x65, 0x73, 0x3c, 0x2f,
                            0x72, 0x65, 0x71, 0x3e, 0x3c, 0x2f, 0x66, 0x65,
                            0x65, 0x64, 0x3e, 0x0a

                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     主轴信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSpindleInfo()
    {
        var buffer = @"
                            0x3c, 0x73, 0x70, 0x69, 0x6e, 0x64, 0x6c, 0x65,
                            0x3e, 0x3c, 0x72, 0x65, 0x71, 0x3e, 0x79, 0x65,
                            0x73, 0x3c, 0x2f, 0x72, 0x65, 0x71, 0x3e, 0x3c,
                            0x2f, 0x73, 0x70, 0x69, 0x6e, 0x64, 0x6c, 0x65,
                            0x3e, 0x0a
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     MT信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadMTInfo()
    {
        var buffer = @"
                            0x3c, 0x62, 0x63, 0x64, 0x3e, 0x3c, 0x72, 0x65,
                            0x71, 0x3e, 0x79, 0x65, 0x73, 0x3c, 0x2f, 0x72,
                            0x65, 0x71, 0x3e, 0x3c, 0x2f, 0x62, 0x63, 0x64,
                            0x3e, 0x0a
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSystemAlarm()
    {
        var buffer = @"
                            0x3c, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x3e, 0x3c,
                            0x72, 0x65, 0x71, 0x3e, 0x79, 0x65, 0x73, 0x3c,
                            0x2f, 0x72, 0x65, 0x71, 0x3e, 0x3c, 0x61, 0x75,
                            0x74, 0x6f, 0x3e, 0x79, 0x65, 0x73, 0x3c, 0x2f,
                            0x61, 0x75, 0x74, 0x6f, 0x3e, 0x3c, 0x2f, 0x61,
                            0x6c, 0x61, 0x72, 0x6d, 0x3e, 0x0a
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     读取宏变量
    /// </summary>
    /// <param name="idValue"></param>
    /// <returns></returns>
    public byte[] BuildSystemMacroValue(int number)
    {
        // 创建 XML 文档
        var xml = new XElement("sharpvar",
            new XElement("req", "yes"),
            new XElement("auto", "yes"),
            new XElement("id", number) // 使用变量值
        );

        // 转换为字符串
        var xmlString = xml.ToString(SaveOptions.DisableFormatting);
        var buffer = Encoding.UTF8.GetBytes(xmlString).Concat(new byte[] { 0x0A }).ToArray();
        return buffer;
    }

    public byte[] BuildReadProduct()
    {
        // 转换为字符串
        var xmlString = "<appl><req>yes</req><sub>get</sub><paratype>1</paratype><id>{Channel}1_{WorkpieceCount}_{ACTUAL}</id></appl>";
        var buffer = Encoding.UTF8.GetBytes(xmlString).Concat(new byte[] { 0x0A }).ToArray();
        return buffer;
    }


    /// <summary>
    ///     机械坐标
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadMachinePos()
    {
        var buffer = @"
                            0x3c, 0x61, 0x78, 0x65, 0x73, 0x3e, 0x3c, 0x72,
                            0x65, 0x71, 0x3e, 0x79, 0x65, 0x73, 0x3c, 0x2f,
                            0x72, 0x65, 0x71, 0x3e, 0x3c, 0x61, 0x75, 0x74,
                            0x6f, 0x3e, 0x79, 0x65, 0x73, 0x3c, 0x2f, 0x61,
                            0x75, 0x74, 0x6f, 0x3e, 0x3c, 0x73, 0x75, 0x62,
                            0x3e, 0x70, 0x6f, 0x73, 0x3c, 0x2f, 0x73, 0x75,
                            0x62, 0x3e, 0x3c, 0x2f, 0x61, 0x78, 0x65, 0x73,
                            0x3e, 0x0a
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     绝对坐标
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAbsolutePos()
    {
        var buffer = @"
                            0x3c, 0x61, 0x78, 0x65, 0x73, 0x3e, 0x3c, 0x72,
                            0x65, 0x71, 0x3e, 0x79, 0x65, 0x73, 0x3c, 0x2f,
                            0x72, 0x65, 0x71, 0x3e, 0x3c, 0x61, 0x75, 0x74,
                            0x6f, 0x3e, 0x79, 0x65, 0x73, 0x3c, 0x2f, 0x61,
                            0x75, 0x74, 0x6f, 0x3e, 0x3c, 0x73, 0x75, 0x62,
                            0x3e, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x3c,
                            0x2f, 0x73, 0x75, 0x62, 0x3e, 0x3c, 0x2f, 0x61,
                            0x78, 0x65, 0x73, 0x3e, 0x0a
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     剩余坐标
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadRemainingPos()
    {
        var buffer = @"
                            0x3c, 0x61, 0x78, 0x65, 0x73, 0x3e, 0x3c, 0x72,
                            0x65, 0x71, 0x3e, 0x79, 0x65, 0x73, 0x3c, 0x2f,
                            0x72, 0x65, 0x71, 0x3e, 0x3c, 0x61, 0x75, 0x74,
                            0x6f, 0x3e, 0x79, 0x65, 0x73, 0x3c, 0x2f, 0x61,
                            0x75, 0x74, 0x6f, 0x3e, 0x3c, 0x73, 0x75, 0x62,
                            0x3e, 0x72, 0x65, 0x6d, 0x3c, 0x2f, 0x73, 0x75,
                            0x62, 0x3e, 0x3c, 0x2f, 0x61, 0x78, 0x65, 0x73,
                            0x3e, 0x0a
                            ".ToHexBytes();
        return buffer;
    }

    #endregion
}