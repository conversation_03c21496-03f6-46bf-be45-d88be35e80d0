using System;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Furion.FriendlyException;
using Furion.JsonSerialization;
using HslCommunication;
using Kede.Base;
using SysAlarm = Kede.Base.SysAlarm;

namespace Kede;

/* 更新日志
 *版本:v1.0.0
 *  1.初始版本，支持科德CNC基础功能
 *  2.添加底层方法反射调用支持
 */
[DriverSupported("Kede")]
[DriverInfo("Kede", "V1.0.0", "科德(Kede)")]
public class Kede : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 62937;

    #endregion

    public Kede(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    /// </summary>
    public override bool IsConnected => Driver != null && _driver.GetConnectStatus();

    private KedeBase _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (KedeBase)value;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            _driver ??= new KedeBase(IpAddress, Port)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout
            };
            OperateResult = _driver.ConnectServer();
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接失败:【{ex.Message}】" };
        }

        return ResConnect();
    }

    /// <summary>
    ///     清空数据
    /// </summary>
    /// <returns></returns>
    [Method("Clear", name: "Clear")]
    public Task Clear()
    {
        _cachedNcData = null;
        _cachedFeedInfo = null;
        _cachedSpindleInfo = null;
        _cachedAlarmInfo = null;
        return Task.CompletedTask;
    }

    /// <summary>
    ///     读取程序内容
    /// </summary>
    /// <returns></returns>
    [Method("FileRead", description: "读取程序内容", name: "FileRead")]
    public override async Task<string> FileRead(FileReadInput input)
    {
        // 科德CNC暂不支持程序读取功能
        throw Oops.Oh("科德CNC暂不支持程序读取功能");
    }

    #region NC数据读取

    /// <summary>
    ///     缓存的NC数据
    /// </summary>
    private OperateResult<int, int, string, string> _cachedNcData;

    /// <summary>
    ///     读取NC数据的通用方法
    /// </summary>
    /// <returns></returns>
    private async Task<OperateResult<int, int, string, string>> ReadNcDataInternal()
    {
        if (_cachedNcData == null) _cachedNcData = _driver.ReadNcData();
        return _cachedNcData;
    }

    /// <summary>
    ///     读取NC模式
    /// </summary>
    /// <returns></returns>
    [Method("Mode", name: "NC模式")]
    public async Task<DriverReturnValueModel> ReadMode()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Int32 };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadNcDataInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取NC模式失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content1;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取NC模式异常：{ex.Message}");
        }

        return ret;
    }

    /// <summary>
    ///     读取NC状态
    /// </summary>
    /// <returns></returns>
    [Method("State", name: "NC状态")]
    public async Task<DriverReturnValueModel> ReadState()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Int32 };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadNcDataInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取NC状态失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content2;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取NC状态异常：{ex.Message}");
        }

        return ret;
    }

    /// <summary>
    ///     读取当前程序名
    /// </summary>
    /// <returns></returns>
    [Method("Program", TransPondDataTypeEnum.String, name: "当前程序名")]
    public async Task<DriverReturnValueModel> ReadProgram()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.String };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadNcDataInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取当前程序名失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content3;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取当前程序名异常：{ex.Message}");
        }

        return ret;
    }

    /// <summary>
    ///     读取当前程序块
    /// </summary>
    /// <returns></returns>
    [Method("Block", TransPondDataTypeEnum.String, name: "当前程序块")]
    public async Task<DriverReturnValueModel> ReadBlock()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.String };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadNcDataInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取当前程序块失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content4;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取当前程序块异常：{ex.Message}");
        }

        return ret;
    }

    #endregion

    #region 进给信息读取

    /// <summary>
    ///     缓存的进给信息
    /// </summary>
    private OperateResult<int, int, int, int> _cachedFeedInfo;

    /// <summary>
    ///     读取进给信息的通用方法
    /// </summary>
    /// <returns></returns>
    private async Task<OperateResult<int, int, int, int>> ReadFeedInfoInternal()
    {
        if (_cachedFeedInfo == null) _cachedFeedInfo = _driver.ReadFeedInfo();
        return _cachedFeedInfo;
    }

    /// <summary>
    ///     读取G01进给倍率
    /// </summary>
    /// <returns></returns>
    [Method("FeedOver", name: "G01进给倍率")]
    public async Task<DriverReturnValueModel> ReadFeedOver()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Int32 };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadFeedInfoInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取G01进给倍率失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content3;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取G01进给倍率异常：{ex.Message}");
        }

        return ret;
    }

    /// <summary>
    ///     读取G00进给倍率
    /// </summary>
    /// <returns></returns>
    [Method("FeedOverF0", name: "G00进给倍率")]
    public async Task<DriverReturnValueModel> ReadFeedOverF0()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Int32 };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadFeedInfoInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取G00进给倍率失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content4;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取G00进给倍率异常：{ex.Message}");
        }

        return ret;
    }

    /// <summary>
    ///     读取实际G01进给速度
    /// </summary>
    /// <returns></returns>
    [Method("FeedAct", name: "实际G01进给速度")]
    public async Task<DriverReturnValueModel> ReadFeedAct()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Int32 };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadFeedInfoInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取实际G01进给速度失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content1;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取实际G01进给速度异常：{ex.Message}");
        }

        return ret;
    }

    /// <summary>
    ///     读取实际G00进给速度
    /// </summary>
    /// <returns></returns>
    [Method("FeedActF0", name: "实际G00进给速度")]
    public async Task<DriverReturnValueModel> ReadFeedActF0()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Int32 };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadFeedInfoInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取实际G00进给速度失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content2;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取实际G00进给速度异常：{ex.Message}");
        }

        return ret;
    }

    #endregion

    #region 主轴信息读取

    /// <summary>
    ///     缓存的主轴信息
    /// </summary>
    private OperateResult<SpindleInfo> _cachedSpindleInfo;

    /// <summary>
    ///     读取主轴信息的通用方法
    /// </summary>
    /// <returns></returns>
    private async Task<OperateResult<SpindleInfo>> ReadSpindleInfoInternal()
    {
        if (_cachedSpindleInfo == null) _cachedSpindleInfo = _driver.ReadSpindInfo();
        return _cachedSpindleInfo;
    }

    /// <summary>
    ///     读取主轴编号
    /// </summary>
    /// <returns></returns>
    [Method("SpindleNmb", name: "主轴编号")]
    public async Task<DriverReturnValueModel> ReadSpindleNmb()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Int32 };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadSpindleInfoInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取主轴编号失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content.Nmb;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取主轴编号异常：{ex.Message}");
        }

        return ret;
    }

    /// <summary>
    ///     读取主轴最大转速
    /// </summary>
    /// <returns></returns>
    [Method("SpindleMax", name: "主轴最大转速")]
    public async Task<DriverReturnValueModel> ReadSpindleMax()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Int32 };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadSpindleInfoInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取主轴最大转速失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content.Max;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取主轴最大转速异常：{ex.Message}");
        }

        return ret;
    }

    /// <summary>
    ///     读取主轴组数量
    /// </summary>
    /// <returns></returns>
    [Method("SpindleGroupCount", name: "主轴组数量")]
    public async Task<DriverReturnValueModel> ReadSpindleGroupCount()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Int32 };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadSpindleInfoInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取主轴组数量失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content.Groups.Count;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取主轴组数量异常：{ex.Message}");
        }

        return ret;
    }

    /// <summary>
    ///     读取MT信息
    /// </summary>
    /// <returns></returns>
    [Method("MTInfo", TransPondDataTypeEnum.String, name: "MT信息")]
    public async Task<DriverReturnValueModel> ReadMTInfo()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.String };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = _driver.ReadMTInfo();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取MT信息失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取MT信息异常：{ex.Message}");
        }

        return ret;
    }

    #endregion

    #region 坐标读取

    /// <summary>
    ///     读取机械坐标
    /// </summary>
    /// <returns></returns>
    [Method("MachPos", TransPondDataTypeEnum.String, name: "机械坐标")]
    public async Task<DriverReturnValueModel> ReadMachPos()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.String };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = _driver.ReadMachinePos();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;
                return ret;
            }

            ret.Value = JSON.Serialize(read.Content);
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取绝对坐标
    /// </summary>
    /// <returns></returns>
    [Method("AbsPos", TransPondDataTypeEnum.String, name: "绝对坐标")]
    public async Task<DriverReturnValueModel> ReadAbsPos()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.String };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = _driver.ReadAbsolutePos();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;
                return ret;
            }

            ret.Value = JSON.Serialize(read.Content);
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取剩余坐标
    /// </summary>
    /// <returns></returns>
    [Method("Remaining", TransPondDataTypeEnum.String, name: "剩余坐标")]
    public async Task<DriverReturnValueModel> ReadRemaining()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.String };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = _driver.ReadRemainingPos();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;
                return ret;
            }

            ret.Value = JSON.Serialize(read.Content);
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion

    #region 宏变量和产量

    /// <summary>
    ///     读取宏变量
    /// </summary>
    /// <returns></returns>
    [Method("Macro", name: "读宏变量")]
    public async Task<DriverReturnValueModel> Macro(DriverAddressIoArgModel val)
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Int32 };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var readResult = _driver.ReadSystemMacroValue(Convert.ToInt32(val.Address));
            if (!readResult.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = readResult.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取宏变量失败，地址：{val.Address}，错误：{readResult.Message}");

                return ret;
            }

            ret.Value = readResult.Content;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取宏变量异常，地址：{val.Address}，异常：{ex.Message}");
        }

        return ret;
    }

    /// <summary>
    ///     读取工件产量
    /// </summary>
    /// <returns></returns>
    [Method("ProduceCount", name: "工件产量")]
    public async Task<DriverReturnValueModel> ReadProduceCount()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Int32 };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = _driver.ReadCurrentProduceCount();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;
                return ret;
            }

            ret.Value = read.Content;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion

    #region 报警信息

    /// <summary>
    ///     缓存的报警信息
    /// </summary>
    private OperateResult<SysAlarm> _cachedAlarmInfo;

    /// <summary>
    ///     读取报警信息的通用方法
    /// </summary>
    /// <returns></returns>
    private async Task<OperateResult<SysAlarm>> ReadAlarmInfoInternal()
    {
        if (_cachedAlarmInfo == null) _cachedAlarmInfo = _driver.ReadSystemAlarmInfo();
        return _cachedAlarmInfo;
    }

    /// <summary>
    ///     读取报警时间
    /// </summary>
    /// <returns></returns>
    [Method("AlarmTime", TransPondDataTypeEnum.String, name: "报警时间")]
    public async Task<DriverReturnValueModel> ReadAlarmTime()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.String };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadAlarmInfoInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取报警时间失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content.ALarmTime ?? "";
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取报警时间异常：{ex.Message}");
        }

        return ret;
    }

    /// <summary>
    ///     读取报警ID
    /// </summary>
    /// <returns></returns>
    [Method("AlarmId", name: "报警ID")]
    public async Task<DriverReturnValueModel> ReadAlarmId()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Int32 };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadAlarmInfoInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取报警ID失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content.AlarmId;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取报警ID异常：{ex.Message}");
        }

        return ret;
    }

    /// <summary>
    ///     读取报警优先级
    /// </summary>
    /// <returns></returns>
    [Method("AlarmPriority", name: "报警优先级")]
    public async Task<DriverReturnValueModel> ReadAlarmPriority()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Int32 };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadAlarmInfoInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取报警优先级失败：{read.Message}");

                return ret;
            }

            ret.Value = read.Content.Priority;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取报警优先级异常：{ex.Message}");
        }

        return ret;
    }

    /// <summary>
    ///     读取报警消息
    /// </summary>
    /// <returns></returns>
    [Method("AlarmMessages", TransPondDataTypeEnum.String, name: "报警消息")]
    public async Task<DriverReturnValueModel> ReadAlarmMessages()
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.String };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var read = await ReadAlarmInfoInternal();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;

                // 使用LogNet记录错误
                if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取报警消息失败：{read.Message}");

                return ret;
            }

            ret.Value = JSON.Serialize(read.Content.Messages);
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;

            // 使用LogNet记录异常
            if (_driver.LogNet != null) _driver.LogNet.WriteError("Kede", $"读取报警消息异常：{ex.Message}");
        }

        return ret;
    }

    #endregion
}