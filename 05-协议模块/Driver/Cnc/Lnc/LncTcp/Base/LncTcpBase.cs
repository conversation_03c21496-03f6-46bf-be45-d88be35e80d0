using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace LncTcp.Base
{
    /// <summary>
    ///     一个宝元机床的机床通信类对象
    /// </summary>
    public class LncTcpBase : NetworkDoubleBase
    {
        /// <summary>
        ///     获取设备连接状态
        /// </summary>
        /// <returns></returns>
        public bool GetConnectStatus()
        {
            return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
        }
        #region Constructor

        /// <summary>
        ///     根据IP及端口来实例化一个对象内容
        /// </summary>
        /// <param name="ipAddress">Ip地址信息</param>
        /// <param name="port">端口号</param>
        public LncTcpBase(string ipAddress, int port = 1888)
        {
            IpAddress = ipAddress;
            Port = port;
            //this.ByteTransform = new ReverseBytesTransform();
            ByteTransform = new RegularByteTransform();
            //this.ByteTransform.DataFormat = DataFormat.CDAB;
        }

        /// <inheritdoc />
        // protected override INetMessage GetNewNetMessage() => new CNCFanucSeriesMessage();
        protected override OperateResult InitializationOnConnect(Socket socket)
        {
            // OperateResult<byte[]> read = ReadFromCoreServer(BuildInit());
            var read = ReadFromCoreServer(socket, @"
                               0x04, 0x02, 0x00, 0x01, 0x00, 0x02, 0x00, 0x00,
  0x10, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes());
            if (!read.IsSuccess) return read;
            return OperateResult.CreateSuccessResult();
        }

        #endregion


        #region Read Write Support

        /// <summary>
        ///     读取当前系统状态和mode
        /// </summary>
        /// <returns></returns>
        public OperateResult<short, short> ReadSysStatusAndMode()
        {
            var read = ReadFromCoreServer(BuildReadSysStatusAndMode());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<short, short>(read);
            var result = read.Content.RemoveBegin(12);
            var mode = ByteTransform.TransInt16(result, 0);
            var status = ByteTransform.TransInt16(result, 2);
            return OperateResult.CreateSuccessResult(mode, status);
        }
        
        /// <summary>
        ///     读取当前系统状态和mode
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<short, short>> ReadSysStatusAndModeAsync()
        {
            var read = await ReadFromCoreServerAsync(BuildReadSysStatusAndMode());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<short, short>(read);
            var result = read.Content.RemoveBegin(12);
            var mode = ByteTransform.TransInt16(result, 0);
            var status = ByteTransform.TransInt16(result, 2);
            return OperateResult.CreateSuccessResult(mode, status);
        }
        
        ///// <summary>
        ///// 读取报警状态
        ///// </summary>
        ///// <returns></returns>
        //public OperateResult<int> ReadSysAlarm()
        //{
        //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadSysAlarm());
        //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        //    if (read.Content.Length <= 6) return OperateResult.CreateFailedResult<int>(read);
        //    byte[] result = read.Content.RemoveBegin(6);
        //    //获取错误个数
        //    int errornum = ByteTransform.TransInt32(result, 0);
        //    //获取警告个数
        //    int warnnum = ByteTransform.TransInt32(result, 4);

        //    return OperateResult.CreateSuccessResult(errornum+ warnnum);
        //}


        /// <summary>
        ///     读取主程序名8，12及当前程序名20，12，工件数量36，4，最大数量32，4
        /// </summary>
        /// <returns>程序名及程序号</returns>
        public OperateResult<string, string, int, int> ReadSystemProgramAndCount()
        {
            var read = ReadFromCoreServer(BuildReadProgramAndCount());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, string, int, int>(read);
            var result = read.Content.RemoveBegin(8);
            var mainname = Encoding.Default.GetString(result.SelectMiddle(8, 13)).TrimEnd('\0');
            var currentname = Encoding.Default.GetString(result.SelectMiddle(21, 13)).Replace("\u0000", "");
            var maxcount = ByteTransform.TransInt32(result, 34);
            var count = ByteTransform.TransInt32(result, 38);

            return OperateResult.CreateSuccessResult(mainname, currentname, maxcount, count);
        }
        
        /// <summary>
        ///     读取主程序名8，12及当前程序名20，12，工件数量36，4，最大数量32，4
        /// </summary>
        /// <returns>程序名及程序号</returns>
        public async Task<OperateResult<string, string, int, int>> ReadSystemProgramAndCountAsync()
        {
            var read = await ReadFromCoreServerAsync(BuildReadProgramAndCount());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, string, int, int>(read);
            var result = read.Content.RemoveBegin(8);
            var mainname = Encoding.Default.GetString(result.SelectMiddle(8, 13)).TrimEnd('\0');
            var currentname = Encoding.Default.GetString(result.SelectMiddle(21, 13)).Replace("\u0000", "");
            var maxcount = ByteTransform.TransInt32(result, 34);
            var count = ByteTransform.TransInt32(result, 38);

            return OperateResult.CreateSuccessResult(mainname, currentname, maxcount, count);
        }


        ///// <summary>
        ///// 读取运行时间
        ///// </summary>
        ///// <returns></returns>
        //public OperateResult<int> ReadRunTime()
        //{
        //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadRunTime());
        //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        //    byte[] result = read.Content.RemoveBegin(15);
        //    return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
        //}

        ///// <summary>
        ///// 读取切削时间
        ///// </summary>
        ///// <returns></returns>
        //public OperateResult<int> ReadCutTime()
        //{
        //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadCutTime());
        //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        //    byte[] result = read.Content.RemoveBegin(15);
        //    // Array.Reverse(result, 0, 4);
        //    return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
        //}
        
        // //// 读取主轴倍率
        ///// / </summary>
        // /// <returns></returns>
        // public OperateResult<float> ReadSpindleRate()
        // {
        //     OperateResult<byte[]> read = ReadFromCoreServer(BuildSpindleRate());
        //     if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        //     byte[] result = read.Content.RemoveBegin(15);
        //     return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
        // }
        // /// <summary>
        // //// 读取进给倍率
        // /// / </summary>
        // /// <returns></returns>
        // public OperateResult<float> ReadFeedRate()
        // {
        //     OperateResult<byte[]> read = ReadFromCoreServer(BuildReadFeedRate());
        //     if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        //     byte[] result = read.Content.RemoveBegin(15);
        //     return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
        // }
        
        /// <summary>
        /// 读取轴速度信息 进给设定速度要除以10000,进给实际速度，主轴设定速度，主轴实际速度
        /// </summary>
        /// <returns></returns>
        public OperateResult<int, int, int, int> ReadSpeedInfo()
        {
            var read = ReadFromCoreServer(BuildReadSpeedInfo());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, int, int, int>(read);
            var result = read.Content.RemoveBegin(8);
            var setfspeed = ByteTransform.TransInt32(result, 8);
            var actfspeed = ByteTransform.TransInt32(result, 12);
            var setspeed = ByteTransform.TransInt32(result, 24);
            var actspeed = ByteTransform.TransInt32(result, 28);
            return OperateResult.CreateSuccessResult(setfspeed, actfspeed, setspeed, actspeed);
        }
        
        /// <summary>
        /// 读取轴速度信息 进给设定速度要除以10000,进给实际速度，主轴设定速度，主轴实际速度
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<int, int, int, int>> ReadSpeedInfoAsync()
        {
            var read = await ReadFromCoreServerAsync(BuildReadSpeedInfo());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, int, int, int>(read);
            var result = read.Content.RemoveBegin(8);
            var setfspeed = ByteTransform.TransInt32(result, 8);
            var actfspeed = ByteTransform.TransInt32(result, 12);
            var setspeed = ByteTransform.TransInt32(result, 24);
            var actspeed = ByteTransform.TransInt32(result, 28);
            return OperateResult.CreateSuccessResult(setfspeed, actfspeed, setspeed, actspeed);
        }


        ///// <summary>
        ///// 读取报警信息
        ///// </summary>
        ///// <returns>读取报警信息</returns>
        //public OperateResult<string> ReadSystemAlarmInfo()
        //{
        //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadAlarmInfo());
        //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        //    byte[] result = read.Content.RemoveBegin(28);

        //    //string name = Encoding.Default.GetString(result).Replace("\u0000", "");
        //    string name = Encoding.Default.GetString(result).TrimEnd('\0');
        //    return OperateResult.CreateSuccessResult(name);
        //}

        #endregion


        #region Build Command

        public byte[] BuildInit()
        {
            var buffer = @"
                               0x04, 0x02, 0x00, 0x01, 0x00, 0x02, 0x00, 0x00,
  0x10, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
            return buffer;
        }

        /// <summary>
        ///     系统状态和模式
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadSysStatusAndMode()
        {
            var buffer = @"
                            0x04, 0x02, 0x02, 0x01, 0x10, 0x00, 0x33, 0x91,
                            0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
            return buffer;
        }

        /// <summary>
        ///     程序和工件，6-7是可变的
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadProgramAndCount()
        {
            var buffer = @"
                            0x04, 0x02, 0x06, 0x01, 0x10, 0x00, 0x3d, 0x00,
                            0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();

            return buffer;
        }

        /// <summary>
        /// 运行时间
        /// </summary>
        ///// <returns></returns>
        //public byte[] BuildReadRunTime()
        //{
        //    byte[] buffer = @"
        //                    0x93, 0x00, 0x0a, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
        //                    0x1e, 0x17, 0x10, 0x17, 0x18, 0x00, 0x55, 0xaa
        //                    ".ToHexBytes();
        //    return buffer;
        //}
        ///// <summary>
        ///// 切削时间
        ///// </summary>
        ///// <returns></returns>
        //public byte[] BuildReadCutTime()
        //{
        //    byte[] buffer = @"
        //                   0x93, 0x00, 0x0a, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
        //                   0x1e, 0x17, 0x10, 0x17, 0x19, 0x00, 0x55, 0xaa
        //                    ".ToHexBytes();
        //    return buffer;
        //}

        /// <summary>
        ///     轴转速信息
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadSpeedInfo()
        {
            var buffer = @"
                            0x04, 0x02, 0x03, 0x01, 0x10, 0x00, 0x71, 0x00,
                            0x8c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
            return buffer;
        }


        ///// <summary>
        ///// 报警信息
        ///// </summary>
        ///// <returns></returns>
        //public byte[] BuildReadAlarmInfo()
        //{
        //    byte[] buffer = @"
        //                    0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x29, 0x77,
        //                    0xc8, 0x00, 0x04, 0x09, 0xc8, 0x00, 0x00, 0x00,
        //                    0x28, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
        //                    0x78, 0x1c, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00,
        //                    0x73, 0x00, 0x67, 0x00
        //                    ".ToHexBytes();
        //    return buffer;
        //}

        #endregion
    }
}