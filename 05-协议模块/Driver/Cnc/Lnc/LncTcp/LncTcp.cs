using System;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using HslCommunication;
using LncTcp.Base;
// ReSharper disable All
#pragma warning disable CS1998

namespace LncTcp;

[DriverSupported("LncTcp")]
[DriverInfo("LncTcp", "V1.1.0", "宝元(Lnc)")]
public class LncTcp : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 1888;

    #endregion

    public LncTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    /// </summary>
    public override bool IsConnected => Driver != null && Driver.GetConnectStatus();

    /// <summary>
    ///     读取轴速度信息 进给设定速度要除以10000,进给实际速度，主轴设定速度，主轴实际速度
    /// </summary>
    private OperateResult<int, int, int, int> _readSpeedInfo;

    /// <summary>
    ///     主程序名,当前程序名，工件数量,最大数量
    /// </summary>
    private OperateResult<string, string, int, int> _readSystemProgramAndCount;

    /// <summary>
    ///     系统状态,模式
    /// </summary>
    private OperateResult<short, short> _readSysStatusAndMode;

    /// <summary>
    ///     清空数据
    /// </summary>
    /// <returns></returns>
    [Method("Clear", name: "Clear")]
    public async Task Clear()
    {
        _readSpeedInfo = null;
        _readSystemProgramAndCount = null;
        _readSysStatusAndMode = null;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver == null)
                Driver = new LncTcpBase(IpAddress, Port)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
            OperateResult = Driver.ConnectServer();
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接失败:【{ex.Message}】"};
        }

        return ResConnect();
    }

    #region 进给设定速度要除以10000,进给实际速度要除以10000，主轴设定速度，主轴实际速度

    /// <summary>
    ///     进给设定速度要除以10000,进给实际速度，主轴设定速度，主轴实际速度
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSpeedInfo(string identifier)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _readSpeedInfo ??= await Driver.ReadSpeedInfoAsync();
                if (!_readSpeedInfo.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _readSpeedInfo.Message;
                }
                else
                {
                    ret.Value = identifier switch
                    {
                        "SetFSpeed" => _readSpeedInfo.Content1 / 10000,
                        "ActFSpeed" => _readSpeedInfo.Content2 / 10000,
                        "SetsSpeed" => _readSpeedInfo.Content3,
                        "ActsPeed" => _readSpeedInfo.Content4,
                        _ => ret.Value
                    };
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给实际速度
    /// </summary>
    /// <returns></returns>
    [Method("ActFSpeed", name: "进给实际速度")]
    public async Task<DriverReturnValueModel> ReadActFSpeed()
    {
        return await ReadSpeedInfo("ActFSpeed");
    }

    /// <summary>
    ///     主轴实际速度
    /// </summary>
    /// <returns></returns>
    [Method("ActsPeed",  name: "主轴实际速度")]
    public async Task<DriverReturnValueModel> ReadActsPeed()
    {
        return await ReadSpeedInfo("ActsPeed");
    }

    /// <summary>
    ///     主轴设定速度
    /// </summary>
    /// <returns></returns>
    [Method("SetsSpeed",  name: "主轴设定速度")]
    public async Task<DriverReturnValueModel> ReadSetsSpeed()
    {
        return await ReadSpeedInfo("SetsSpeed");
    }

    /// <summary>
    ///     进给设定速度
    /// </summary>
    /// <returns></returns>
    [Method("SetFSpeed", name: "进给设定速度")]
    public async Task<DriverReturnValueModel> ReadSetFSpeed()
    {
        return await ReadSpeedInfo("SetFSpeed");
    }

    #endregion

    #region 主程序名,当前程序名，工件数量,最大数量

    /// <summary>
    ///     主程序名,当前程序名，工件数量,最大数量
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSystemProgramAndCount(string identifier)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String,
        };
        try
        {
            if (IsConnected)
            {
                _readSystemProgramAndCount ??= await Driver.ReadSystemProgramAndCountAsync();
                if (!_readSystemProgramAndCount.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _readSystemProgramAndCount.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "MainName":
                            ret.Value = _readSystemProgramAndCount.Content1;
                            break;
                        case "CurrentName":
                            ret.Value = _readSystemProgramAndCount.Content2;
                            break;
                        case "CurrentNum":
                            ret.Value = _readSystemProgramAndCount.Content4;
                            ret.DataType = DataTypeEnum.Int32;
                            break;
                        case "MaxCount":
                            ret.Value = _readSystemProgramAndCount.Content3;
                            ret.DataType = DataTypeEnum.Int32;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主程序名
    /// </summary>
    /// <returns></returns>
    [Method("MainName", TransPondDataTypeEnum.String, name: "主程序名")]
    public async Task<DriverReturnValueModel> ReadMainName()
    {
        return await ReadSystemProgramAndCount("MainName");
    }

    /// <summary>
    ///     当前程序名
    /// </summary>
    /// <returns></returns>
    [Method("CurrentName", name: "当前程序名")]
    public async Task<DriverReturnValueModel> ReadCurrentName()
    {
        return await ReadSystemProgramAndCount("CurrentName");
    }

    /// <summary>
    ///     工件数量
    /// </summary>
    /// <returns></returns>
    [Method("CurrentNum", name: "工件数量")]
    public async Task<DriverReturnValueModel> ReadCurrentNum()
    {
        return await ReadSystemProgramAndCount("CurrentNum");
    }

    /// <summary>
    ///     最大数量
    /// </summary>
    /// <returns></returns>
    [Method("MaxCount", name: "最大数量")]
    public async Task<DriverReturnValueModel> ReadMaxCount()
    {
        return await ReadSystemProgramAndCount("MaxCount");
    }

    #endregion

    #region 系统状态,模式

    /// <summary>
    ///     系统状态,模式
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSysStatusAndMode(string identifier)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int16,
        };
        try
        {
            if (IsConnected)
            {
                _readSysStatusAndMode ??= await Driver.ReadSysStatusAndModeAsync();
                if (!_readSystemProgramAndCount.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _readSystemProgramAndCount.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "RunStatus":
                            ret.Value = _readSysStatusAndMode.Content2;
                            break;
                        case "Mode":
                            ret.Value = _readSysStatusAndMode.Content1;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     系统状态
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", name: "系统状态",description:"状态:1:准备就绪;2:自动运行;4:连续寸动")]
    public async Task<DriverReturnValueModel> ReadRunStatus()
    {
        return await ReadSysStatusAndMode("RunStatus");
    }

    /// <summary>
    ///     模式
    /// </summary>
    /// <returns></returns>
    [Method("Mode", name: "模式")]
    public async Task<DriverReturnValueModel> ReadMode()
    {
        return await ReadSysStatusAndMode("Mode");
    }

    #endregion 系统状态,模式

    // /// <summary>
    // ///     报警状态
    // /// </summary>
    // /// <returns></returns>
    // [Method("AlarmStatus", name: "报警状态")]
    // public async Task<DriverReturnValueModel> ReadAlarmStatus()
    // {
    //     DriverReturnValueModel ret = new()
    //     {
    //         VariableStatus = VariableStatusTypeEnum.Good,
    //         DataType = DataTypeEnum.Int32,
    //         ReadTime = DateTime.ToTimeStampLong(DateTime.Now())
    //     };
    //     try
    //     {
    //         if (IsConnected)
    //         {
    //             var read = Driver.ReadSysAlarm();
    //             if (!read.IsSuccess)
    //             {
    //                 ret.VariableStatus = VariableStatusTypeEnum.Error;
    //                 ret.Message = read.Message;
    //             }
    //
    //             ret.Value = read.Content;
    //         }
    //         else
    //         {
    //             ret.VariableStatus = VariableStatusTypeEnum.Bad;
    //             ret.Message = "TCP连接异常";
    //         }
    //     }
    //     catch (Exception ex)
    //     {
    //         ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
    //         ret.Message = ex.Message;
    //     }
    //
    //     return ret;
    // }
    //
    // /// <summary>
    // ///     工作模式
    // /// </summary>
    // /// <returns></returns>
    // [Method("WorkMode", description: "0:EDIT,1:MEM 2:MDI 3:DNC 4:JOG,5：HANDLE,6:REF", name: "工作模式")]
    // public async Task<DriverReturnValueModel> ReadSysMode()
    // {
    //     DriverReturnValueModel ret = new()
    //     {
    //         VariableStatus = VariableStatusTypeEnum.Good,
    //         DataType = DataTypeEnum.Int32,
    //         ReadTime = DateTime.ToTimeStampLong(DateTime.Now())
    //     };
    //     try
    //     {
    //         if (IsConnected)
    //         {
    //             var read = Driver.ReadSysMode();
    //             if (!read.IsSuccess)
    //             {
    //                 ret.VariableStatus = VariableStatusTypeEnum.Error;
    //                 ret.Message = read.Message;
    //             }
    //
    //             ret.Value = read.Content;
    //         }
    //         else
    //         {
    //             ret.VariableStatus = VariableStatusTypeEnum.Bad;
    //             ret.Message = "TCP连接异常";
    //         }
    //     }
    //     catch (Exception ex)
    //     {
    //         ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
    //         ret.Message = ex.Message;
    //     }
    //
    //     return ret;
    // }
    //
    // /// <summary>
    // ///     运行时间
    // /// </summary>
    // /// <returns></returns>
    // [Method("RunTime", name: "运行时间")]
    // public async Task<DriverReturnValueModel> ReadRunTime()
    // {
    //     DriverReturnValueModel ret = new()
    //     {
    //         VariableStatus = VariableStatusTypeEnum.Good,
    //         DataType = DataTypeEnum.Int32,
    //         ReadTime = DateTime.ToTimeStampLong(DateTime.Now())
    //     };
    //     try
    //     {
    //         if (IsConnected)
    //         {
    //             var read = Driver.ReadRunTime();
    //             if (!read.IsSuccess)
    //             {
    //                 ret.VariableStatus = VariableStatusTypeEnum.Error;
    //                 ret.Message = read.Message;
    //             }
    //
    //             ret.Value = read.Content;
    //         }
    //         else
    //         {
    //             ret.VariableStatus = VariableStatusTypeEnum.Bad;
    //             ret.Message = "TCP连接异常";
    //         }
    //     }
    //     catch (Exception ex)
    //     {
    //         ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
    //         ret.Message = ex.Message;
    //     }
    //
    //     return ret;
    // }
    //
    // /// <summary>
    // ///     切削时间
    // /// </summary>
    // /// <returns></returns>
    // [Method("CutTime", name: "切削时间")]
    // public async Task<DriverReturnValueModel> ReadCutTime()
    // {
    //     DriverReturnValueModel ret = new()
    //     {
    //         VariableStatus = VariableStatusTypeEnum.Good,
    //         DataType = DataTypeEnum.Int32,
    //         ReadTime = DateTime.ToTimeStampLong(DateTime.Now())
    //     };
    //     try
    //     {
    //         if (IsConnected)
    //         {
    //             var read = Driver.ReadCutTime();
    //             if (!read.IsSuccess)
    //             {
    //                 ret.VariableStatus = VariableStatusTypeEnum.Error;
    //                 ret.Message = read.Message;
    //             }
    //
    //             ret.Value = read.Content;
    //         }
    //         else
    //         {
    //             ret.VariableStatus = VariableStatusTypeEnum.Bad;
    //             ret.Message = "TCP连接异常";
    //         }
    //     }
    //     catch (Exception ex)
    //     {
    //         ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
    //         ret.Message = ex.Message;
    //     }
    //
    //     return ret;
    // }
    //
    // /// <summary>
    // ///     主轴倍率
    // /// </summary>
    // /// <returns></returns>
    // [Method("SpindleRate", name: "主轴倍率")]
    // public async Task<DriverReturnValueModel> ReadSpindleRate()
    // {
    //     DriverReturnValueModel ret = new()
    //     {
    //         VariableStatus = VariableStatusTypeEnum.Good,
    //         DataType = DataTypeEnum.Double,
    //         ReadTime = DateTime.ToTimeStampLong(DateTime.Now())
    //     };
    //     try
    //     {
    //         if (IsConnected)
    //         {
    //             var read = Driver.ReadSpindleRate();
    //             if (!read.IsSuccess)
    //             {
    //                 ret.VariableStatus = VariableStatusTypeEnum.Error;
    //                 ret.Message = read.Message;
    //             }
    //
    //             ret.Value = read.Content;
    //         }
    //         else
    //         {
    //             ret.VariableStatus = VariableStatusTypeEnum.Bad;
    //             ret.Message = "TCP连接异常";
    //         }
    //     }
    //     catch (Exception ex)
    //     {
    //         ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
    //         ret.Message = ex.Message;
    //     }
    //
    //     return ret;
    // }
    //
    // /// <summary>
    // ///     进给倍率
    // /// </summary>
    // /// <returns></returns>
    // [Method("FeedRate", name: "进给倍率")]
    // public async Task<DriverReturnValueModel> ReadFeedRate()
    // {
    //     DriverReturnValueModel ret = new()
    //     {
    //         VariableStatus = VariableStatusTypeEnum.Good,
    //         DataType = DataTypeEnum.Double,
    //         ReadTime = DateTime.ToTimeStampLong(DateTime.Now())
    //     };
    //     try
    //     {
    //         if (IsConnected)
    //         {
    //             var read = Driver.ReadFeedRate();
    //             if (!read.IsSuccess)
    //             {
    //                 ret.VariableStatus = VariableStatusTypeEnum.Error;
    //                 ret.Message = read.Message;
    //             }
    //
    //             ret.Value = read.Content;
    //         }
    //         else
    //         {
    //             ret.VariableStatus = VariableStatusTypeEnum.Bad;
    //             ret.Message = "TCP连接异常";
    //         }
    //     }
    //     catch (Exception ex)
    //     {
    //         ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
    //         ret.Message = ex.Message;
    //     }
    //
    //     return ret;
    // }
    //
    // /// <summary>
    // ///     报警信息
    // /// </summary>
    // /// <returns></returns>
    // [Method("AlarmInfo", name: "报警信息")]
    // public async Task<DriverReturnValueModel> ReadSystemAlarmInfo()
    // {
    //     DriverReturnValueModel ret = new()
    //     {
    //         VariableStatus = VariableStatusTypeEnum.Good,
    //         DataType = DataTypeEnum.String,
    //         ReadTime = DateTime.ToTimeStampLong(DateTime.Now())
    //     };
    //     try
    //     {
    //         if (IsConnected)
    //         {
    //             var read = Driver.ReadSystemAlarmInfo();
    //             if (!read.IsSuccess)
    //             {
    //                 ret.VariableStatus = VariableStatusTypeEnum.Error;
    //                 ret.Message = read.Message;
    //             }
    //
    //             ret.Value = read.Content;
    //         }
    //         else
    //         {
    //             ret.VariableStatus = VariableStatusTypeEnum.Bad;
    //             ret.Message = "TCP连接异常";
    //         }
    //     }
    //     catch (Exception ex)
    //     {
    //         ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
    //         ret.Message = ex.Message;
    //     }
    //
    //     return ret;
    // }
}