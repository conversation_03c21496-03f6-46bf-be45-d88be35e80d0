using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace GskUdp.Base;

/// <summary>
/// </summary>
public sealed class GskUdpBase : NetworkDoubleBase
{
    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public GskUdpBase(string ipAddress, int port = 2000)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    #endregion

    #region Read Write Support

    /// <summary>
    ///     读取当前系统状态  0:RESET复位,1:STOP停止 2:RUN 3:HOLD 进给保持
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSysStatus()
    {
        var read = ReadFromCoreServer(BuildReadSysStatus());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取当前系统状态  0:RESET复位,1:STOP停止 2:RUN 3:HOLD 进给保持
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysStatusAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysStatus());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取报警状态
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSysAlarm()
    {
        var read = ReadFromCoreServer(BuildReadSysAlarm());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(6);
        //获取错误个数
        var errornum = ByteTransform.TransInt32(result, 0);
        //获取警告个数
        var warnnum = ByteTransform.TransInt32(result, 4);

        return OperateResult.CreateSuccessResult(errornum + warnnum);
    }
    
    /// <summary>
    ///     读取报警状态
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysAlarmAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysAlarm());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(6);
        //获取错误个数
        var errornum = ByteTransform.TransInt32(result, 0);
        //获取警告个数
        var warnnum = ByteTransform.TransInt32(result, 4);

        return OperateResult.CreateSuccessResult(errornum + warnnum);
    }

    /// <summary>
    ///     读取当前工作模式
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSysMode()
    {
        var read = ReadFromCoreServer(BuildReadSysMode());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(6);

        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取当前工作模式
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysModeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysMode());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(6);

        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取当前工件数量
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadCurrentProduceCount()
    {
        var read = ReadFromCoreServer(BuildReadProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取当前工件数量
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCurrentProduceCountAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取程序名及程序号
    /// </summary>
    /// <returns>程序名及程序号</returns>
    public OperateResult<string> ReadSystemProgramCurrent()
    {
        var read = ReadFromCoreServer(BuildReadSystemProgram());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        var result = read.Content.RemoveBegin(6);
        var length = ByteTransform.TransInt16(result, 0, 2);
        var name = Encoding.Default.GetString(result.SelectMiddle(2, length[0])).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(name);
    }
    
    /// <summary>
    ///     读取程序名及程序号
    /// </summary>
    /// <returns>程序名及程序号</returns>
    public async Task<OperateResult<string>> ReadSystemProgramCurrentAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSystemProgram());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        var result = read.Content.RemoveBegin(6);
        var length = ByteTransform.TransInt16(result, 0, 2);
        var name = Encoding.Default.GetString(result.SelectMiddle(2, length[0])).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(name);
    }

    /// <summary>
    ///     读取上电时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadAliveTime()
    {
        var read = ReadFromCoreServer(BuildReadAliveTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取上电时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadAliveTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadAliveTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取运行时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadRunTime()
    {
        var read = ReadFromCoreServer(BuildReadRunTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取运行时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadRunTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadRunTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取切削时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadCutTime()
    {
        var read = ReadFromCoreServer(BuildReadCutTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取切削时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCutTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCutTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取主轴倍率
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadSpindleRate()
    {
        var read = ReadFromCoreServer(BuildSpindleRate());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    }
    
    /// <summary>
    ///     读取主轴倍率
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadSpindleRateAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildSpindleRate());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    }

    /// <summary>
    ///     读取进给倍率
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadFeedRate()
    {
        var read = ReadFromCoreServer(BuildReadFeedRate());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(6);
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    }
    
    /// <summary>
    ///     读取进给倍率
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadFeedRateAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadFeedRate());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(6);
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    }

    /// <summary>
    ///     读取主轴速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadActSpeed()
    {
        var read = ReadFromCoreServer(BuildReadActSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    }
    
    /// <summary>
    ///     读取主轴速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadActSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    }

    /// <summary>
    ///     读取主轴编程速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadSetSpeed()
    {
        var read = ReadFromCoreServer(BuildReadSetSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    }

    /// <summary>
    ///     读取主轴编程速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadSetSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSetSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    }
    
    /// <summary>
    ///     读取进给速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadActFSpeed()
    {
        var read = ReadFromCoreServer(BuildReadActFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    }
    
    /// <summary>
    ///     读取进给速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadActFSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    }

    /// <summary>
    ///     读取进给编程速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadSetFspeed()
    {
        var read = ReadFromCoreServer(BuildReadSetFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    }
    
    /// <summary>
    ///     读取进给编程速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadSetFspeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSetFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(6);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    }

    /// <summary>
    ///     读取报警信息
    /// </summary>
    /// <returns>读取报警信息</returns>
    public OperateResult<string> ReadSystemAlarmInfo()
    {
        var read = ReadFromCoreServer(BuildReadAlarmInfo());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(28);

        //string name = Encoding.Default.GetString(result).Replace("\u0000", "");
        var name = Encoding.Default.GetString(result).TrimEnd('\0');
        return OperateResult.CreateSuccessResult(name);
    }
    
    /// <summary>
    ///     读取报警信息
    /// </summary>
    /// <returns>读取报警信息</returns>
    public async Task<OperateResult<string>> ReadSystemAlarmInfoAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadAlarmInfo());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(28);

        //string name = Encoding.Default.GetString(result).Replace("\u0000", "");
        var name = Encoding.Default.GetString(result).TrimEnd('\0');
        return OperateResult.CreateSuccessResult(name);
    }

    #endregion

    #region Build Command

    /// <summary>
    ///     系统状态
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysStatus()
    {
        var buffer = @"
                            0x17, 0x10, 0x00, 0x11, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysAlarm()
    {
        var buffer = @"
                           0x17, 0x10, 0x00, 0x81
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     系统模式
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysMode()
    {
        var buffer = @"
                            0x17, 0x10, 0x00, 0x10, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     工件产量
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadProduct()
    {
        var buffer = @"
                           0x17, 0x10, 0x00, 0x16, 0x00
                         ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSystemProgram()
    {
        var buffer = @"
                             0x17, 0x10, 0x00, 0x12, 0x00
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     上电时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAliveTime()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x6f, 0x69,
                            0xc8, 0x00, 0x1d, 0x09, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xf4, 0x03, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     运行时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadRunTime()
    {
        var buffer = @"
                             0x17, 0x10, 0x00, 0x18, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     切削时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCutTime()
    {
        var buffer = @"
                            0x17, 0x10, 0x00, 0x19, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     读取主轴倍率报文
    /// </summary>
    /// <returns></returns>
    public byte[] BuildSpindleRate()
    {
        var buffer = @"
                           0x17, 0x10, 0x00, 0x1a, 0x05, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     读取进给倍率报文
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadFeedRate()
    {
        var buffer = @"
                             0x17, 0x10, 0x00, 0x1a, 0x02, 0x00, 0x00
                           ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActSpeed()
    {
        var buffer = @"
                             0x17, 0x10, 0x00, 0x1a, 0x04, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     主轴编程转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSetSpeed()
    {
        var buffer = @"
                             0x17, 0x10, 0x00, 0x1a, 0x03, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     进给转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActFspeed()
    {
        var buffer = @"
                             0x17, 0x10, 0x00, 0x1a, 0x01, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     进给编程转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSetFspeed()
    {
        var buffer = @"
                             0x17, 0x10, 0x00, 0x1a, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAlarmInfo()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x29, 0x77,
                            0xc8, 0x00, 0x04, 0x09, 0xc8, 0x00, 0x00, 0x00,
                            0x28, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x78, 0x1c, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00,
                            0x73, 0x00, 0x67, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    #endregion
}