using System.Text;
using System.Threading.Tasks;
using Gsk25im.Base.Models;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace Gsk25im.Base;

/// <summary>
///     一个广数25im的机床通信类对象
/// </summary>
public sealed class Gsk25ImBase : NetworkDoubleBase
{
    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public Gsk25ImBase(string ipAddress, int port = 5000)
    {
        IpAddress = ipAddress;
        Port = port;
        // ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    #endregion

    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }

    #region Read Write Support

    /// <summary>
    ///     读取当前系统运行数据
    /// </summary>
    /// <returns></returns>
    public OperateResult<GskRunData> ReadRunData()
    {
        var read = ReadFromCoreServer(BuildReadRunData());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<GskRunData>(read);
        var result = read.Content.RemoveBegin(read.Content.Length > 192 ? 72 : 64);
        var gskRunData = new GskRunData {RunStatus = result[0], WorkMode = result[1], Products = ByteTransform.TransUInt16(result, 6)};
        var result1 = result.RemoveBegin(62);
        gskRunData.Mainproname = Encoding.Default.GetString(result1.SelectBegin(8)).Replace("\u0000", "");
        var result2 = result1.RemoveBegin(34);
        gskRunData.Runtime = ByteTransform.TransInt32(result2, 0);
        gskRunData.CycleTime = ByteTransform.TransInt32(result2, 4);
        gskRunData.Seq = ByteTransform.TransInt32(result2, 12);
        return OperateResult.CreateSuccessResult(gskRunData);
    }
    
    /// <summary>
    ///     读取当前系统运行数据
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<GskRunData>> ReadRunDataAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadRunData());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<GskRunData>(read);
        var result = read.Content.RemoveBegin(read.Content.Length > 192 ? 72 : 64);
        var gskRunData = new GskRunData {RunStatus = result[0], WorkMode = result[1], Products = ByteTransform.TransUInt16(result, 6)};
        var result1 = result.RemoveBegin(62);
        gskRunData.Mainproname = Encoding.Default.GetString(result1.SelectBegin(8)).Replace("\u0000", "");
        var result2 = result1.RemoveBegin(34);
        gskRunData.Runtime = ByteTransform.TransInt32(result2, 0);
        gskRunData.CycleTime = ByteTransform.TransInt32(result2, 4);
        gskRunData.Seq = ByteTransform.TransInt32(result2, 12);
        return OperateResult.CreateSuccessResult(gskRunData);
    }

    public OperateResult<byte[]> ReadRunDataByte()
    {
        var read = ReadFromCoreServer(BuildReadRunData());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(read);
        var result = read.Content.RemoveBegin(72);
        return OperateResult.CreateSuccessResult(result);
    }

    public OperateResult<GskAxisParam> ReadAxisParam()
    {
        var read = ReadFromCoreServer(BuildReadAxisParam());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<GskAxisParam>(read);
        if (read.Content.Length < 328)
            return OperateResult.CreateFailedResult<GskAxisParam>(read);
        var result = read.Content.RemoveBegin(328);
        var gskAxisParam = new GskAxisParam();
        gskAxisParam.ActSpeed = ByteTransform.TransInt32(result, 0);
        gskAxisParam.Srate = ByteTransform.TransInt32(result, 4);
        gskAxisParam.Spindlerate = ByteTransform.TransInt32(result, 8);
        gskAxisParam.RapidFeed = ByteTransform.TransInt32(result, 12);
        return OperateResult.CreateSuccessResult(gskAxisParam);
    }
    
    public async Task<OperateResult<GskAxisParam>> ReadAxisParamAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadAxisParam());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<GskAxisParam>(read);
        if (read.Content.Length < 328)
            return OperateResult.CreateFailedResult<GskAxisParam>(read);
        var result = read.Content.RemoveBegin(328);
        var gskAxisParam = new GskAxisParam();
        gskAxisParam.ActSpeed = ByteTransform.TransInt32(result, 0);
        gskAxisParam.Srate = ByteTransform.TransInt32(result, 4);
        gskAxisParam.Spindlerate = ByteTransform.TransInt32(result, 8);
        gskAxisParam.RapidFeed = ByteTransform.TransInt32(result, 12);
        return OperateResult.CreateSuccessResult(gskAxisParam);
    }

    #endregion


    #region Build Command

    /// <summary>
    ///     采样信息1
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadRunData()
    {
        var buffer = @"
                           0x47, 0x45, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     轴倍率、转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAxisParam()
    {
        var buffer = @"
                           0x47, 0x45, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    #endregion
}