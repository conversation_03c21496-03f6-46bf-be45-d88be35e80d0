namespace Gsk25im.Base.Models;

/// <summary>
/// </summary>
public class GskRunData
{
    /// <summary>
    ///     运行状态 0:停止  3:运行 5复位
    /// </summary>
    public byte RunStatus { get; set; }

    /// <summary>
    ///     运行模式  1 自动
    /// </summary>
    public byte WorkMode { get; set; }

    /// <summary>
    ///     产量
    /// </summary>
    public ushort Products { get; set; }

    /// <summary>
    ///     程序号
    /// </summary>
    public string Mainproname { get; set; }

    /// <summary>
    ///     程序行号
    /// </summary>
    public int Seq { get; set; }

    /// <summary>
    ///     运行时间
    /// </summary>
    public int Runtime { get; set; }

    /// <summary>
    ///     循环时间
    /// </summary>
    public int CycleTime { get; set; }
}