using System;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Gsk25im.Base;
using Gsk25im.Base.Models;
using HslCommunication;

namespace Gsk25im;

[DriverSupported("Gsk25im")]
[DriverInfo("Gsk25im", "V1.2.0", "广州数控(GSK)")]
public class Gsk25im : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 5000;

    #endregion

    public Gsk25im(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     Gsk25ImBase
    /// </summary>
    private Gsk25ImBase _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (Gsk25ImBase)value;
    }

    public override bool IsConnected => _driver != null && _driver.GetConnectStatus();

    public DeviceConnectDto Connect()
    {
        try
        {
            _driver ??= new Gsk25ImBase(IpAddress, Port)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout
            };
            OperateResult = _driver.ConnectServer();
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接失败:【{ex.Message}】"};
        }

        return ResConnect();
    }

    /// <summary>
    ///     清空数据
    /// </summary>
    /// <returns></returns>
    [Method("Clear", name: "Clear")]
    public Task Clear()
    {
        _gskRunData = null;
        _gskAxisParam = null;
        return Task.CompletedTask;
    }

    #region 读取模式,运行状态,产量,程序号,程序行号,运行时间,循环时间

    /// <summary>
    ///     读取模式,运行状态,产量,程序号,程序行号,运行时间,循环时间
    /// </summary>
    private OperateResult<GskRunData> _gskRunData;

    /// <summary>
    ///     读取模式,运行状态,产量,程序号,程序行号,运行时间,循环时间
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadRunDataAsync(string identifier)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _gskRunData ??= await Driver.ReadRunDataAsync();
                if (!_gskRunData.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _gskRunData.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "WorkMode":
                            ret.Value = _gskRunData.Content.WorkMode;
                            break;
                        case "RunStatus":
                            ret.Value = _gskRunData.Content.RunStatus;
                            break;
                        case "CurrentNum":
                            ret.Value = _gskRunData.Content.Products;
                            break;
                        case "CurPgm":
                            ret.Value = _gskRunData.Content.Seq;
                            break;
                        case "MainName":
                        {
                            ret.DataType = DataTypeEnum.String;
                            ret.Value = _gskRunData.Content.Mainproname;
                            break;
                        }
                        case "CycleTime":
                            ret.Value = _gskRunData.Content.CycleTime;
                            break;
                        case "CycSec":
                            ret.Value = _gskRunData.Content.Runtime;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     模式
    /// </summary>
    /// <returns></returns>
    [Method("WorkMode", description: "运行模式  1 自动", name: "模式")]
    public async Task<DriverReturnValueModel> ReadWorkMode()
    {
        return await ReadRunDataAsync("WorkMode");
    }

    /// <summary>
    ///     运行状态
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", description: " 0:停止  3:运行 5复位", name: "状态")]
    public async Task<DriverReturnValueModel> ReadRunStatus()
    {
        return await ReadRunDataAsync("RunStatus");
    }

    /// <summary>
    ///     产量
    /// </summary>
    /// <returns></returns>
    [Method("CurrentNum", name: "产量")]
    public async Task<DriverReturnValueModel> ReadCurrentNum()
    {
        return await ReadRunDataAsync("CurrentNum");
    }

    /// <summary>
    ///     程序行号
    /// </summary>
    /// <returns></returns>
    [Method("CurPgm", name: "程序行号")]
    public async Task<DriverReturnValueModel> ReadCurPgm()
    {
        return await ReadRunDataAsync("CurPgm");
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    [Method("MainName", TransPondDataTypeEnum.String, name: "程序名")]
    public async Task<DriverReturnValueModel> ReadMainName()
    {
        return await ReadRunDataAsync("MainName");
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    [Method("CycleTime", name: "循环时间")]
    public async Task<DriverReturnValueModel> ReadCycleTime()
    {
        return await ReadRunDataAsync("CycleTime");
    }

    /// <summary>
    ///     运行时间
    /// </summary>
    /// <returns></returns>
    [Method("RunTime", name: "运行时间")]
    public async Task<DriverReturnValueModel> ReadCycSec()
    {
        return await ReadRunDataAsync("RunTime");
    }

    #endregion

    #region 读取主轴速度，进给倍率，主轴倍率，快速倍率

    /// <summary>
    ///     读取主轴速度，进给倍率，主轴倍率，快速倍率
    /// </summary>
    private OperateResult<GskAxisParam> _gskAxisParam;

    /// <summary>
    ///     读取主轴速度，进给倍率，主轴倍率，快速倍率
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadAxisParamAsync(string identifier)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _gskAxisParam ??= await Driver.ReadAxisParamAsync();
                if (!_gskAxisParam.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _gskAxisParam.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "OvSpin":
                            ret.Value = _gskAxisParam.Content.Spindlerate;
                            break;
                        case "OvFeed":
                            ret.Value = _gskAxisParam.Content.Srate;
                            break;
                        case "sSpeed":
                            ret.Value = _gskAxisParam.Content.ActSpeed;
                            break;
                        case "RapidFeed":
                            ret.Value = _gskAxisParam.Content.RapidFeed;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴倍率
    /// </summary>
    /// <returns></returns>
    [Method("OvSpin", name: "主轴倍率")]
    public async Task<DriverReturnValueModel> ReadOvSpin()
    {
        return await ReadAxisParamAsync("OvSpin");
    }

    /// <summary>
    ///     进给倍率
    /// </summary>
    /// <returns></returns>
    [Method("OvFeed", name: "进给倍率")]
    public async Task<DriverReturnValueModel> ReadOvFeed()
    {
        return await ReadAxisParamAsync("OvFeed");
    }

    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    [Method("sSpeed", name: "主轴转速")]
    public async Task<DriverReturnValueModel> ReadsSpeed()
    {
        return await ReadAxisParamAsync("sSpeed");
    }

    /// <summary>
    ///     快速倍率
    /// </summary>
    /// <returns></returns>
    [Method("RapidFeed", name: "快速倍率")]
    public async Task<DriverReturnValueModel> ReadRapidFeed()
    {
        return await ReadAxisParamAsync("RapidFeed");
    }

    #endregion
}