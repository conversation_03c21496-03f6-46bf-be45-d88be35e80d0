using System;
using System.Threading.Tasks;
using Brother.Base;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Furion.JsonSerialization;

namespace Brother;

/// <summary>
/// 兄弟(Brother)
/// </summary>
[DriverSupported("Brother")]
[DriverInfo("Brother", "V1.0.0", "兄弟(Brother)")]
public class Brother : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 10000;

    #endregion

    public Brother(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     BrotherBase
    /// </summary>
    private BrotherBase _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (BrotherBase)value;
    }

    /// <summary>
    /// 是否连接
    /// </summary>
    public override bool IsConnected => _driver != null && _driver.GetConnectStatus();

    /// <summary>
    /// 连接
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            _driver ??= new BrotherBase(IpAddress, Port)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout
            };
            OperateResult = _driver.ConnectServer();
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接失败:【{ex.Message}】" };
        }

        return ResConnect();
    }

    /// <summary>
    ///     当前系统状态
    /// </summary>
    /// <returns></returns>
    [Method("SysStatus", description: "0: 不操作 1:工作 2:暂停 3: 块停止", name: "系统状态")]
    public async Task<DriverReturnValueModel> ReadSysStatus()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int16
        };
        try
        {
            if (IsConnected)
            {
                var _statusInfo = Driver.ReadSysStatus();
                if (!_statusInfo.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _statusInfo.Message;
                }
                ret.Value = Convert.ToInt32(_statusInfo.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    /// 读取当前工作模式
    /// </summary>
    /// <returns></returns>
    [Method("SysMode", description: "0: MANU 1:MDI 2:MEM 3:EDIT 4: MANU+MDI 5: MEM+EDIT", name: "工作模式")]
    public async Task<DriverReturnValueModel> ReadSysMode()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int16
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadSysMode();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = Convert.ToInt32(result.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取当前程序名
    /// </summary>
    /// <returns></returns>
    [Method("ProgramName", TransPondDataTypeEnum.String, name: "程序名")]
    public async Task<DriverReturnValueModel> ReadSystemProgramCurrent()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadSystemProgramCurrent();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取报警状态
    /// </summary>
    /// <returns></returns>
    [Method("AlarmStatus", TransPondDataTypeEnum.Bool, name: "报警状态")]
    public async Task<DriverReturnValueModel> ReadSystemAlarmStatus()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Bool
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadSystemAlarm();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content1;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取报警号
    /// </summary>
    /// <returns></returns>
    [Method("AlarmNumbers", TransPondDataTypeEnum.String, name: "报警号")]
    public async Task<DriverReturnValueModel> ReadSystemAlarmNumbers()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadSystemAlarm();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = JSON.Serialize(result.Content2);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取系统版本
    /// </summary>
    [Method("SystemVersion", TransPondDataTypeEnum.String, name: "系统版本")]
    public async Task<DriverReturnValueModel> ReadSystemVersion()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadSystemVer();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取进给倍率
    /// </summary>
    [Method("FeedRate", name: "进给倍率")]
    public async Task<DriverReturnValueModel> ReadFeedRate()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadFeedRate();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取主轴倍率
    /// </summary>
    [Method("SpindleRate", name: "主轴倍率")]
    public async Task<DriverReturnValueModel> ReadSpindleRate()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadSpindleRate();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取是否急停
    /// </summary>
    [Method("IsEmergency", TransPondDataTypeEnum.Bool, name: "急停状态")]
    public async Task<DriverReturnValueModel> ReadIsEmergency()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Bool
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadIsEmerGency();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取上电时间(秒)
    /// </summary>
    [Method("KeepAliveTime", name: "上电时间")]
    public async Task<DriverReturnValueModel> ReadKeepAliveTime()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadKeepAliveTime();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取运行时间(秒)
    /// </summary>
    [Method("RunTime", name: "运行时间")]
    public async Task<DriverReturnValueModel> ReadRunTime()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadRunTime();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    ///   工件当前计数
    /// </summary>
    [Method("CurrentProduceCount", name: "工件当前计数")]
    public async Task<DriverReturnValueModel> ReadCurrentProduceCount()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadCurrentProduceCount();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    ///   工件计数
    /// </summary>
    [Method("ProduceCount", name: "工件计数")]
    public async Task<DriverReturnValueModel> ReadProduceCount()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadProduceCount();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    
    /// <summary>
    /// 读取进给速度
    /// </summary>
    [Method("ActualFeedSpeed", name: "进给速度")]
    public async Task<DriverReturnValueModel> ReadActualFeedSpeed()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadActfSpeed();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取主轴速度
    /// </summary>
    [Method("ActualSpindleSpeed", name: "主轴速度")]
    public async Task<DriverReturnValueModel> ReadActualSpindleSpeed()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadActsSpeed();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取机器坐标
    /// </summary>
    [Method("MachinePosition", TransPondDataTypeEnum.String, name: "机器坐标")]
    public async Task<DriverReturnValueModel> ReadMachinePosition()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadMachinePos();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = JSON.Serialize(result.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取相对坐标
    /// </summary>
    [Method("RelativePosition", TransPondDataTypeEnum.String, name: "相对坐标")]
    public async Task<DriverReturnValueModel> ReadRelativePosition()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadRelativePos();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = JSON.Serialize(result.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取绝对坐标
    /// </summary>
    [Method("AbsolutePosition", TransPondDataTypeEnum.String, name: "绝对坐标")]
    public async Task<DriverReturnValueModel> ReadAbsolutePosition()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadAbsolutePos();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = JSON.Serialize(result.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取剩余坐标
    /// </summary>
    [Method("RemainingPosition", TransPondDataTypeEnum.String, name: "剩余坐标")]
    public async Task<DriverReturnValueModel> ReadRemainingPosition()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadRemainPos();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = JSON.Serialize(result.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取当前刀具号
    /// </summary>
    [Method("ToolNumber", name: "刀具号")]
    public async Task<DriverReturnValueModel> ReadToolNumber()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int16
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadToolNum();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }

    /// <summary>
    /// 读取系统型号
    /// </summary>
    [Method("SystemType", TransPondDataTypeEnum.String, name: "系统型号")]
    public async Task<DriverReturnValueModel> ReadSystemType()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var result = Driver.ReadSystemType();
                if (!result.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = result.Message;
                }
                ret.Value = result.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }
        return ret;
    }
}