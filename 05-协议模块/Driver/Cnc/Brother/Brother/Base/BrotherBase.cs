using System;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace Brother.Base;

/// <summary>
/// 一个Brother的机床通信类对象
/// </summary>
public class BrotherBase : NetworkDoubleBase
{
    /// <summary>
    /// 根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public BrotherBase(string ipAddress, int port = 10000)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
        ReceiveTimeOut = 30_000;
    }
    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }

    #region Read Write Support

    #region MEM命令
    /// <summary>
    /// 读取当前系统状态0: 不操作 1:工作 2:暂停 3: 块停止
    /// </summary>
    /// <returns></returns>
    public OperateResult<short> ReadSysStatus()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherMEM);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);

        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);

        string[] result = arr[1].Split(',');
        // 根据型号判断  状态：系统型号包含TC字样的时候，取[2],不然取[3]
        var systemType = ReadSystemType();
        if (systemType.Content.ToLower().Contains("tc"))
        {
            short status = short.Parse(result[2]);
            return OperateResult.CreateSuccessResult(status);    
        }
        else
        {
            short status = short.Parse(result[3]);
            return OperateResult.CreateSuccessResult(status);
        }
    }


    /// <summary>
    /// 读取当前工作模式 模式0: MANU 1:MDI 2:MEM 3:EDIT 4: MANU+MDI 5: MEM+EDIT
    /// </summary>
    /// <returns></returns>
    public OperateResult<short> ReadSysMode()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherMEM);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);

        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);

        string[] result = arr[1].Split(',');
        // 根据型号判断  模式：系统型号包含TC字样的时候，取[5],不然取[6]
        var systemType = ReadSystemType();
        if (systemType.Content.ToLower().Contains("tc"))
        {
            short mode = short.Parse(result[5]);
            return OperateResult.CreateSuccessResult(mode);    
        }
        else
        {
            short mode = short.Parse(result[6]);
            return OperateResult.CreateSuccessResult(mode);
        }
    }

    /// <summary>
    /// 读取程序名
    /// </summary>
    /// <returns>程序名</returns>
    public OperateResult<string> ReadSystemProgramCurrent()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherMEM);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);

        string[] result = arr[1].Split(',');
        string program = result[2];   //版本号是TC开头的这么取

        return OperateResult.CreateSuccessResult(program);
    }

    /// <summary>
    /// 读取报警状态、报警号
    /// </summary>
    /// <returns></returns>
    public OperateResult<bool, string[]> ReadSystemAlarm()
    {
        int j = 0;
        string[] alarmnum = new string[10];
        bool aralm = false;
        byte[] commed = Encoding.ASCII.GetBytes(BrotherMEM);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<bool, string[]>(read);

        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);

        string[] result = arr[2].Split(',');

        for (int i = 1; i < result.Count<string>(); i++)
        {
            if (result[i] != "    ")
            {
                alarmnum[j] = result[i];
                j++;
            }
        }
        if (j > 0)
        {
            aralm = true;
        }
        return OperateResult.CreateSuccessResult(aralm, alarmnum);
    }

    #endregion

    /// <summary>
    /// 获取系统版本
    /// </summary>
    /// <returns></returns>
    public OperateResult<string> ReadSystemVer()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherVER);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[2].Split(',');
        string ver = result[1];
        return OperateResult.CreateSuccessResult(ver);
    }

    /// <summary>
    /// 获取系统型号
    /// </summary>
    /// <returns></returns>
    public OperateResult<string> ReadSystemType()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherVER);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[1].Split(',');
        string type = Regex.Replace(result[1].Trim(), "\\s", "");
        return OperateResult.CreateSuccessResult(type);
    }


    /// <summary>
    /// 读取进给倍率
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadFeedRate()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherPANEL);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[3].Split(',');
        int feedrate = int.Parse(result[2]);
        return OperateResult.CreateSuccessResult(feedrate);
    }


    /// <summary>
    /// 读取主轴倍率
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSpindleRate()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherPANEL);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[3].Split(',');
        int spindlerate = int.Parse(result[3]);
        return OperateResult.CreateSuccessResult(spindlerate);
    }


    /// <summary>
    /// 读取是否急停
    /// </summary>
    /// <returns></returns>
    public OperateResult<bool> ReadIsEmerGency()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherPANEL);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<bool>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[3].Split(',');
        bool isemergency = Convert.ToBoolean(int.Parse(result[4]));
        return OperateResult.CreateSuccessResult(isemergency);
    }

    /// <summary>
    /// 上电时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadKeepAliveTime()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherMONTR);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[2].Split(',');
        int keepalive = int.Parse(result[2]);
        return OperateResult.CreateSuccessResult(keepalive);
    }

    /// <summary>
    /// 运行时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadRunTime()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherMONTR);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[2].Split(',');
        int readruntime = int.Parse(result[3]);
        return OperateResult.CreateSuccessResult(readruntime);
    }

    /// <summary>
    /// 工件当前计数
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadCurrentProduceCount()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherMONTR);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[3].Split(',');
        int product = int.Parse(result[2]);
        return OperateResult.CreateSuccessResult(product);
    }
    
    /// <summary>
    /// 工件计数
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadProduceCount()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherMONTR);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[3].Split(',');
        int product = int.Parse(result[1]);
        return OperateResult.CreateSuccessResult(product);
    }

    /// <summary>
    /// 读取进给速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadActfSpeed()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherPDSP);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[8].Split(',');
        int keepalive = int.Parse(result[1]);
        return OperateResult.CreateSuccessResult(keepalive);
    }
    /// <summary>
    /// 读取主轴速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadActsSpeed()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherPDSP);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[8].Split(',');
        int keepalive = int.Parse(result[2]);
        return OperateResult.CreateSuccessResult(keepalive);
    }
    /// <summary>
    /// 读取机器坐标
    /// </summary>
    /// <returns></returns>
    public OperateResult<double[]> ReadMachinePos()
    {
        double[] machine = new double[3];
        byte[] commed = Encoding.ASCII.GetBytes(BrotherPDSP);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[4].Split(',');
        machine[0] = double.Parse(result[1]);
        machine[1] = double.Parse(result[2]);
        machine[2] = double.Parse(result[3]);
        return OperateResult.CreateSuccessResult(machine);
    }

    /// <summary>
    /// 读取相对坐标
    /// </summary>
    /// <returns></returns>
    public OperateResult<double[]> ReadRelativePos()
    {
        double[] relative = new double[3];
        byte[] commed = Encoding.ASCII.GetBytes(BrotherPDSP);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[5].Split(',');
        relative[0] = double.Parse(result[1]);
        relative[1] = double.Parse(result[2]);
        relative[2] = double.Parse(result[3]);
        return OperateResult.CreateSuccessResult(relative);
    }
    /// <summary>
    /// 读取绝对坐标
    /// </summary>
    /// <returns></returns>
    public OperateResult<double[]> ReadAbsolutePos()
    {
        double[] absolute = new double[3];
        byte[] commed = Encoding.ASCII.GetBytes(BrotherPDSP);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[6].Split(',');
        absolute[0] = double.Parse(result[1]);
        absolute[1] = double.Parse(result[2]);
        absolute[2] = double.Parse(result[3]);
        return OperateResult.CreateSuccessResult(absolute);
    }

    /// <summary>
    /// 读取剩余坐标
    /// </summary>
    /// <returns></returns>
    public OperateResult<double[]> ReadRemainPos()
    {
        double[] remain = new double[3];
        byte[] commed = Encoding.ASCII.GetBytes(BrotherPDSP);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[7].Split(',');
        remain[0] = double.Parse(result[1]);
        remain[1] = double.Parse(result[2]);
        remain[2] = double.Parse(result[3]);
        return OperateResult.CreateSuccessResult(remain);
    }

    /// <summary>
    /// 当前刀具号
    /// </summary>
    /// <returns></returns>
    public OperateResult<short> ReadToolNum()
    {
        byte[] commed = Encoding.ASCII.GetBytes(BrotherATCTL);
        OperateResult<byte[]> read = ReadFromCoreServer(commed);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);
        string[] arr = Encoding.UTF8.GetString(read.Content).Split(new string[] { "\r\n" }, StringSplitOptions.None);
        string[] result = arr[1].Split(',');
        short toolnum = short.Parse(result[1]);
        return OperateResult.CreateSuccessResult(toolnum);
    }
    #endregion


    #region Build Command

    /// <summary>
    /// 状态相关
    /// </summary>
    public string BrotherMEM = "%CLOD    MEM     00\r\n%";
    /// <summary>
    /// 版本相关
    /// </summary>
    public string BrotherVER = "%CLOD    VER     00\r\n%";
    /*%RLOD    VER     00
      M01,'TC-S2C    '
      V01,'MB1-11-15 ','LB2-11-15 ','SB1-11-09 ','AB1-11-02 ','EB1-11-01 ','PB1-11-04'
      S01,     0
      07% */
    /// <summary>
    /// 倍率相关
    /// </summary>
    public string BrotherPANEL = "%CLOD    PANEL   00\r\n%";
    /// <summary>
    /// 时间相关
    /// </summary>
    public string BrotherMONTR = "%CLOD    MONTR   00\r\n%";
    /// <summary>
    /// 速度、坐标相关
    /// </summary>
    public string BrotherPDSP = "%CLOD    PDSP    00\r\n%";
    /// <summary>
    /// 刀具相关
    /// </summary>
    public string BrotherATCTL = "%CLOD    ATCTL   00\r\n%";
    #endregion

}