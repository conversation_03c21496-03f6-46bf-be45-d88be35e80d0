using System;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Furion.JsonSerialization;
using Heidenhain.Base;
using HslCommunication;

namespace Heidenhain;

[DriverSupported("Heidenhain")]
[DriverInfo("Heidenhain", "V1.2.0", "海德汉(Heidenhain)")]
public class Heidenhain : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 19000;

    #endregion

    public Heidenhain(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    /// </summary>
    public override bool IsConnected => Driver != null && Driver.GetConnectStatus();

    /// <summary>
    ///     读取进给倍率、主轴倍率、快速倍率
    /// </summary>
    private OperateResult<int, int, int> _rate;

    /// <summary>
    ///     读取刀具信息，刀具号、轴刀号、长度、直径
    /// </summary>
    private OperateResult<int, int, double, double> _toolInfo;

    /// <summary>
    ///     读取报警信息,报警id，报警组，报警类型
    /// </summary>
    private OperateResult<SysAlarm> _sysAlarm;

    /// <summary>
    ///     清空数据
    /// </summary>
    /// <returns></returns>
    [Method("Clear", name: "Clear")]
#pragma warning disable CS1998
    public async Task Clear()
#pragma warning restore CS1998
    {
        _rate = null;
        _toolInfo = null;
        _sysAlarm = null;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            Driver ??= new HeidenhainBase(IpAddress, Port)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout
            };
            OperateResult = Driver.ConnectServer();
            //该协议需要初始化
            if (OperateResult.IsSuccess)
                OperateResult = Driver.Initializatio();
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接失败:【{ex.Message}】"};
        }

        return ResConnect();
    }

    /// <summary>
    ///     读取当前程序运行状态
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", name: "运行状态", description: "STARTED = 0,STOPPED = 1,FINISHED = 2,CANCELED = 3,INTERRUPTED = 4,ERROR = 5,ERROR_CLEARED = 6,IDLE = 7")]
    public async Task<DriverReturnValueModel> ReadSysStatus()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int16
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSysStatusAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取报警状态
    /// </summary>
    /// <returns></returns>
    [Method("AlarmStatus", name: "报警状态")]
    public async Task<DriverReturnValueModel> ReadSysAlarm()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSysAlarmAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取当前工作模式
    /// </summary>
    /// <returns></returns>
    [Method("WorkMode", name: "工作模式", description: "模式MANUAL = 0,MDI = 1,RPF = 2,SINGLESTEP = 3,AUTOMATIC = 4,OTHER = 5")]
    public async Task<DriverReturnValueModel> ReadSysMode()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int16
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSysModeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     机械坐标
    /// </summary>
    /// <returns></returns>
    [Method("MachPos", TransPondDataTypeEnum.String, name: "机械坐标")]
    public async Task<DriverReturnValueModel> ReadMachinePos()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadMachinePosAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = JSON.Serialize(read.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     切削坐标
    /// </summary>
    /// <returns></returns>
    [Method("CurrentPos", TransPondDataTypeEnum.String, name: "切削坐标")]
    public async Task<DriverReturnValueModel> ReadCurrentPos()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCurrentPosAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = JSON.Serialize(read.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取当前工件数量
    /// </summary>
    /// <returns></returns>
    [Method("CurrentNum", name: "工件数量")]
    public async Task<DriverReturnValueModel> ReadCurrentProduceCount()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int16
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCurrentProduceCountAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取程序名
    /// </summary>
    /// <returns></returns>
    [Method("MainName", TransPondDataTypeEnum.String, name: "程序名")]
    public async Task<DriverReturnValueModel> ReadSystemProgramCurrent()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSystemProgramCurrentAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取运行时间
    /// </summary>
    /// <returns></returns>
    [Method("RunTime", name: "运行时间")]
    public async Task<DriverReturnValueModel> ReadRunTime()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadRunTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取机器上电时间
    /// </summary>
    /// <returns></returns>
    [Method("MachineAliveTime", name: "机器上电时间")]
    public async Task<DriverReturnValueModel> ReadMachineAliveTime()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadMachineAliveTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取程序上电时间
    /// </summary>
    /// <returns></returns>
    [Method("NcAliveTime", name: "程序上电时间")]
    public async Task<DriverReturnValueModel> ReadNcAliveTime()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadNcAliveTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取进给倍率、主轴倍率、快速倍率
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadRate(string identifier)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _rate ??= await Driver.ReadRateAsync();
                if (!_rate.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _rate.Message;
                }
                else
                {
                    ret.Value = identifier switch
                    {
                        "FeedRate" => _rate.Content1,
                        "SpindleRate" => _rate.Content2,
                        "QuickRate" => _rate.Content3,
                        _ => ret.Value
                    };
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给倍率
    /// </summary>
    /// <returns></returns>
    [Method("FeedRate", name: "进给倍率")]
    public async Task<DriverReturnValueModel> ReadFeedRate()
    {
        return await ReadRate("FeedRate");
    }

    /// <summary>
    ///     主轴倍率
    /// </summary>
    /// <returns></returns>
    [Method("SpindleRate", name: "主轴倍率")]
    public async Task<DriverReturnValueModel> ReadSpindleRate()
    {
        return await ReadRate("SpindleRate");
    }

    /// <summary>
    ///     快速倍率
    /// </summary>
    /// <returns></returns>
    [Method("QuickRate", name: "快速倍率")]
    public async Task<DriverReturnValueModel> ReadQuickRate()
    {
        return await ReadRate("QuickRate");
    }

    /// <summary>
    ///     读取刀具信息，刀具号、轴刀号、长度、直径
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadToolInfo(string identifier)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _toolInfo ??= await Driver.ReadToolInfoAsync();
                if (!_toolInfo.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _toolInfo.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "ToolNum":
                            ret.Value = _toolInfo.Content1;
                            break;
                        case "ToolAxis":
                            ret.Value = _toolInfo.Content2;
                            break;
                        case "ToolLen":
                            ret.Value = _toolInfo.Content3;
                            ret.DataType = DataTypeEnum.Double;
                            break;
                        case "ToolRad":
                            ret.Value = _toolInfo.Content4;
                            ret.DataType = DataTypeEnum.Double;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     刀具号
    /// </summary>
    /// <returns></returns>
    [Method("ToolNum", name: "刀具号")]
    public async Task<DriverReturnValueModel> ReadToolNum()
    {
        return await ReadToolInfo("ToolNum");
    }

    /// <summary>
    ///     轴刀号
    /// </summary>
    /// <returns></returns>
    [Method("ToolAxis", name: "轴刀号")]
    public async Task<DriverReturnValueModel> ReadToolAxis()
    {
        return await ReadToolInfo("ToolAxis");
    }

    /// <summary>
    ///     长度
    /// </summary>
    /// <returns></returns>
    [Method("ToolLen", TransPondDataTypeEnum.Double, name: "长度")]
    public async Task<DriverReturnValueModel> ReadToolLen()
    {
        return await ReadToolInfo("ToolLen");
    }

    /// <summary>
    ///     直径
    /// </summary>
    /// <returns></returns>
    [Method("ToolRad", TransPondDataTypeEnum.Double, name: "直径")]
    public async Task<DriverReturnValueModel> ReadToolRad()
    {
        return await ReadToolInfo("ToolRad");
    }

    /// <summary>
    ///     读取报警信息,报警id，报警组，报警类型
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSystemAlarmInfo(string identifier)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int16
        };
        try
        {
            if (IsConnected)
            {
                _sysAlarm ??= await Driver.ReadSystemAlarmInfoAsync();
                if (!_sysAlarm.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _sysAlarm.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "AlarmId":
                            ret.Value = _sysAlarm.Content.AlarmId;
                            ret.DataType = DataTypeEnum.Int32;
                            break;
                        case "AlarmGroup":
                            ret.Value = _sysAlarm.Content.Group;
                            break;
                        case "AlarmType":
                            ret.Value = _sysAlarm.Content.Type;
                            break;
                        case "AlarmMessage":
                            ret.Value = _sysAlarm.Content.Message;
                            ret.DataType = DataTypeEnum.String;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读取报警信息
    /// </summary>
    /// <returns></returns>
    [Method("AlarmMessage", TransPondDataTypeEnum.String, name: "报警信息")]
    public async Task<DriverReturnValueModel> ReadAlarmMessage()
    {
        return await ReadSystemAlarmInfo("AlarmMessage");
    }

    /// <summary>
    ///     报警组
    /// </summary>
    /// <returns></returns>
    [Method("AlarmGroup", name: "报警组")]
    public async Task<DriverReturnValueModel> ReadAlarmGroup()
    {
        return await ReadSystemAlarmInfo("AlarmGroup");
    }

    /// <summary>
    ///     当前报警的ID信息
    /// </summary>
    /// <returns></returns>
    [Method("AlarmId", name: "报警ID")]
    public async Task<DriverReturnValueModel> ReadAlarmId()
    {
        return await ReadSystemAlarmInfo("AlarmId");
    }

    /// <summary>
    ///     报警类型
    /// </summary>
    /// <returns></returns>
    [Method("AlarmType", name: "报警类型")]
    public async Task<DriverReturnValueModel> ReadSystemAlarmInfo()
    {
        return await ReadSystemAlarmInfo("AlarmType");
    }
}