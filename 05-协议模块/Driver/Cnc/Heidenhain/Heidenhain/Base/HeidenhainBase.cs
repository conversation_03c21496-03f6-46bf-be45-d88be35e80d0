using System;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace Heidenhain.Base
{
    /// <summary>
    /// 一个海德汉的机床通信类对象，已知的支持530，640部分点位不全
    /// </summary>
    public class HeidenhainBase : NetworkDoubleBase
    {
        #region Constructor

        /// <summary>
        ///     获取设备连接状态
        /// </summary>
        /// <returns></returns>
        public bool GetConnectStatus()
        {
            return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
        }

        /// <summary>
        /// 根据IP及端口来实例化一个对象内容
        /// </summary>
        /// <param name="ipAddress">Ip地址信息</param>
        /// <param name="port">端口号</param>
        public HeidenhainBase(string ipAddress, int port =19000)
        {
            IpAddress = ipAddress;
            Port = port;
            ByteTransform = new ReverseBytesTransform();

            //this.ByteTransform = new RegularByteTransform();

        }

        //protected override OperateResult InitializationOnConnect(Socket socket)
        //{
        //    OperateResult<byte[]> read1 = ReadFromCoreServer(socket, "0x00, 0x00, 0x00, 0x08, 0x41, 0x5f, 0x4c, 0x47,0x49, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x54, 0x00".ToHexBytes()); //前置报文
        //    if (!read1.IsSuccess) return read1;

        //    OperateResult<byte[]> read2 = ReadFromCoreServer(socket, "0x00, 0x00, 0x00, 0x00, 0x52, 0x5f, 0x50, 0x52".ToHexBytes());
        //    if (!read2.IsSuccess) return read2;

        //    OperateResult<byte[]> read3 = ReadFromCoreServer(socket, "0x00, 0x00, 0x00, 0x05, 0x41, 0x5f, 0x4c, 0x47,0x46, 0x49, 0x4c, 0x45, 0x00".ToHexBytes());
        //    if (!read3.IsSuccess) return read3;

        //    OperateResult<byte[]> read4 = ReadFromCoreServer(socket, "0x00, 0x00, 0x00, 0x00, 0x52, 0x5f, 0x56, 0x52".ToHexBytes());
        //    if (!read4.IsSuccess) return read4;

        //    OperateResult<byte[]> read5 = ReadFromCoreServer(socket, "0x00, 0x00, 0x00, 0x02, 0x43, 0x5f, 0x43, 0x43,0x00, 0x03".ToHexBytes());
        //    if (!read5.IsSuccess) return read5;
        //    OperateResult<byte[]> read6 = ReadFromCoreServer(socket, "0x00, 0x00, 0x00, 0x02, 0x43, 0x5f, 0x43, 0x43,0x00, 0x06".ToHexBytes());
        //    if (!read6.IsSuccess) return read6;
        //    OperateResult<byte[]> read7 = ReadFromCoreServer(socket, "0x00, 0x00, 0x00, 0x02, 0x43, 0x5f, 0x43, 0x43,0x00, 0x13".ToHexBytes());
        //    if (!read7.IsSuccess) return read7;
        //    OperateResult<byte[]> read8 = ReadFromCoreServer(socket, "0x00, 0x00, 0x00, 0x00, 0x52, 0x5f, 0x44, 0x49".ToHexBytes());
        //    if (!read8.IsSuccess) return read8;

        //    OperateResult<byte[]> read9 = ReadFromCoreServer(socket, "  0x00, 0x00, 0x00, 0x04, 0x41, 0x5f, 0x4c, 0x47,0x44, 0x4e, 0x43, 0x00".ToHexBytes());
        //    if (!read9.IsSuccess) return read9;

        //    return OperateResult.CreateSuccessResult();
        //}
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public OperateResult Initializatio()
        {
            OperateResult<byte[]> read1 = ReadFromCoreServer("0x00, 0x00, 0x00, 0x08, 0x41, 0x5f, 0x4c, 0x47,0x49, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x54, 0x00".ToHexBytes()); //前置报文
            if (!read1.IsSuccess) return read1;

            OperateResult<byte[]> read2 = ReadFromCoreServer("0x00, 0x00, 0x00, 0x00, 0x52, 0x5f, 0x50, 0x52".ToHexBytes());
            if (!read2.IsSuccess) return read2;

            OperateResult<byte[]> read3 = ReadFromCoreServer( "0x00, 0x00, 0x00, 0x05, 0x41, 0x5f, 0x4c, 0x47,0x46, 0x49, 0x4c, 0x45, 0x00".ToHexBytes());
            if (!read3.IsSuccess) return read3;

            OperateResult<byte[]> read4 = ReadFromCoreServer("0x00, 0x00, 0x00, 0x00, 0x52, 0x5f, 0x56, 0x52".ToHexBytes());
            if (!read4.IsSuccess) return read4;

            OperateResult<byte[]> read5 = ReadFromCoreServer("0x00, 0x00, 0x00, 0x02, 0x43, 0x5f, 0x43, 0x43,0x00, 0x03".ToHexBytes());
            if (!read5.IsSuccess) return read5;
            OperateResult<byte[]> read6 = ReadFromCoreServer("0x00, 0x00, 0x00, 0x02, 0x43, 0x5f, 0x43, 0x43,0x00, 0x06".ToHexBytes());
            if (!read6.IsSuccess) return read6;
            OperateResult<byte[]> read7 = ReadFromCoreServer("0x00, 0x00, 0x00, 0x02, 0x43, 0x5f, 0x43, 0x43,0x00, 0x13".ToHexBytes());
            if (!read7.IsSuccess) return read7;
            OperateResult<byte[]> read8 = ReadFromCoreServer("0x00, 0x00, 0x00, 0x00, 0x52, 0x5f, 0x44, 0x49".ToHexBytes());
            if (!read8.IsSuccess) return read8;

            OperateResult<byte[]> read9 = ReadFromCoreServer("0x00, 0x00, 0x00, 0x04, 0x41, 0x5f, 0x4c, 0x47,0x44, 0x4e, 0x43, 0x00".ToHexBytes());
            if (!read9.IsSuccess) return read9;

                                                            
            return OperateResult.CreateSuccessResult();
        }
        
        // protected override INetMessage GetNewNetMessage() => new CNCFanucSeriesMessage();

        #endregion

        #region Read Write Support

        /// <summary>
        /// 读取当前程序运行状态  STARTED = 0,STOPPED = 1,FINISHED = 2,CANCELED = 3,INTERRUPTED = 4,ERROR = 5,ERROR_CLEARED = 6,IDLE = 7
        /// </summary>
        /// <returns></returns>
        public OperateResult<short> ReadSysStatus()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadSysStatus());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read); 
            byte[] result = read.Content.RemoveBegin(8);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt16(result, 0));
        }
        
        /// <summary>
        /// 读取当前程序运行状态  STARTED = 0,STOPPED = 1,FINISHED = 2,CANCELED = 3,INTERRUPTED = 4,ERROR = 5,ERROR_CLEARED = 6,IDLE = 7
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<short>> ReadSysStatusAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadSysStatus());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read); 
            byte[] result = read.Content.RemoveBegin(8);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt16(result, 0));
        }
        
        /// <summary>
        /// 读取报警状态
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadSysAlarm()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadAlarmInfo());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            int length = read.Content.Length;
            var alarmstatus = 0;
            if (length > 14)//有报警
            {
                alarmstatus = 1;
            }
            return OperateResult.CreateSuccessResult(alarmstatus);
        }
        
        /// <summary>
        /// 读取报警状态
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<int>> ReadSysAlarmAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadAlarmInfo());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            int length = read.Content.Length;
            var alarmstatus = 0;
            if (length > 14)//有报警
            {
                alarmstatus = 1;
            }
            return OperateResult.CreateSuccessResult(alarmstatus);
        }

        /// <summary>
        /// 读取当前工作模式 模式MANUAL = 0,MDI = 1,RPF = 2,SINGLESTEP = 3,AUTOMATIC = 4,OTHER = 5
        /// </summary>
        /// <returns></returns>
        public OperateResult<short> ReadSysMode()
        {
           //Initializatio();
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadSysMode());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);
            byte[] result = read.Content.RemoveBegin(8);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt16(result, 0));
        }
        
        /// <summary>
        /// 读取当前工作模式 模式MANUAL = 0,MDI = 1,RPF = 2,SINGLESTEP = 3,AUTOMATIC = 4,OTHER = 5
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<short>> ReadSysModeAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadSysMode());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);
            byte[] result = read.Content.RemoveBegin(8);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt16(result, 0));
        }
        
        /// <summary>
        /// 当前位置就是机械位置
        /// </summary>
        /// <returns></returns>
        public OperateResult<double[]> ReadMachinePos()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadMachinePos());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);
            byte[] result = read.Content.RemoveBegin(8);

            int axisnum = ByteTransform.TransInt16(result, 0);//取轴个数

            byte[] pos = result.RemoveBegin(axisnum * 2 + 2);

            string posstr = Encoding.Default.GetString(pos) ;
            string[] sArray = posstr.Split('\u0000');
            double[] axispos = new double[axisnum];
            for (int i = 0; i < axisnum; i++)
            {
                axispos[i] = double.Parse(sArray[i]);
            }
            return OperateResult.CreateSuccessResult(axispos);
        }
        
        /// <summary>
        /// 当前位置就是机械位置
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<double[]>> ReadMachinePosAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadMachinePos());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);
            byte[] result = read.Content.RemoveBegin(8);

            int axisnum = ByteTransform.TransInt16(result, 0);//取轴个数

            byte[] pos = result.RemoveBegin(axisnum * 2 + 2);

            string posstr = Encoding.Default.GetString(pos) ;
            string[] sArray = posstr.Split('\u0000');
            double[] axispos = new double[axisnum];
            for (int i = 0; i < axisnum; i++)
            {
                axispos[i] = double.Parse(sArray[i]);
            }
            return OperateResult.CreateSuccessResult(axispos);
        }

        /// <summary>
        /// 当前位置就是机械位置byte
        /// </summary>
        /// <returns></returns>
        public OperateResult<byte[]> ReadMachinePosByte()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadMachinePos());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(read);
           // byte[] result = read.Content.RemoveBegin(8);

            //int axisnum = ByteTransform.TransInt16(result, 0);//取轴个数

            //byte[] pos = result.RemoveBegin(axisnum * 2 + 2);

            //string posstr = Encoding.Default.GetString(pos);
            //string[] sArray = posstr.Split('\u0000');
            //double[] axispos = new double[axisnum];
            //for (int i = 0; i < axisnum; i++)
            //{
            //    axispos[i] = double.Parse(sArray[i]);
            //}
            return OperateResult.CreateSuccessResult(read.Content);
        }
        /// <summary>
        /// 切削位置就是当前位置
        /// </summary>
        /// <returns></returns>
        public OperateResult<double[]> ReadCurrentPos()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadCurrentPos());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);
            byte[] result = read.Content.RemoveBegin(8);

            int axisnum = ByteTransform.TransInt16(result, 0);//取轴个数

            byte[] pos = result.RemoveBegin(2);

            string posstr = Encoding.Default.GetString(pos);
            string[] sArray = posstr.Split('\u0000');
            double[] axispos = new double[axisnum];
            for (int i = 0; i < axisnum; i++)
            {
                axispos[i] = double.Parse(sArray[i]);
            }
            return OperateResult.CreateSuccessResult(axispos);
        }
        
        /// <summary>
        /// 切削位置就是当前位置
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<double[]>> ReadCurrentPosAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadCurrentPos());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);
            byte[] result = read.Content.RemoveBegin(8);

            int axisnum = ByteTransform.TransInt16(result, 0);//取轴个数

            byte[] pos = result.RemoveBegin(2);

            string posstr = Encoding.Default.GetString(pos);
            string[] sArray = posstr.Split('\u0000');
            double[] axispos = new double[axisnum];
            for (int i = 0; i < axisnum; i++)
            {
                axispos[i] = double.Parse(sArray[i]);
            }
            return OperateResult.CreateSuccessResult(axispos);
        }

        /// <summary>
        /// 读取当前工件数量
        /// </summary>
        /// <returns></returns>
        public OperateResult<short> ReadCurrentProduceCount()
        {
            OperateResult<byte[]> read1 = ReadFromCoreServer("0x00, 0x00, 0x00, 0x09, 0x41, 0x5f, 0x4c, 0x47,0x50, 0x4c, 0x43, 0x44, 0x45, 0x42, 0x55, 0x47,0x00".ToHexBytes()); //前置报文
            if (!read1.IsSuccess) OperateResult.CreateFailedResult<short>(read1);
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadProduct());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);
            byte[] result = read.Content.RemoveBegin(8);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt16(result, 0));
        }
        
        /// <summary>
        /// 读取当前工件数量
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<short>> ReadCurrentProduceCountAsync()
        {
            OperateResult<byte[]> read1 = await ReadFromCoreServerAsync("0x00, 0x00, 0x00, 0x09, 0x41, 0x5f, 0x4c, 0x47,0x50, 0x4c, 0x43, 0x44, 0x45, 0x42, 0x55, 0x47,0x00".ToHexBytes()); //前置报文
            if (!read1.IsSuccess) OperateResult.CreateFailedResult<short>(read1);
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadProduct());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);
            byte[] result = read.Content.RemoveBegin(8);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt16(result, 0));
        }

        /// <summary>
        /// 读取程序名
        /// </summary>
        /// <returns>程序名</returns>
        public OperateResult<string> ReadSystemProgramCurrent()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadSystemProgram());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
            byte[] result = read.Content.RemoveBegin(12);
            string name = Encoding.Default.GetString(result).Replace("\u0000", "");
            return OperateResult.CreateSuccessResult(name);
        }
        
        /// <summary>
        /// 读取程序名
        /// </summary>
        /// <returns>程序名</returns>
        public async Task<OperateResult<string>> ReadSystemProgramCurrentAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadSystemProgram());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
            byte[] result = read.Content.RemoveBegin(12);
            string name = Encoding.Default.GetString(result).Replace("\u0000", "");
            return OperateResult.CreateSuccessResult(name);
        }

        /// <summary>
        /// 读取运行时间
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadRunTime()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadRunTime());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(8);
            var rumTime = result.Length == 2 ? 0 : ByteTransform.TransInt32(result, 0);
            return OperateResult.CreateSuccessResult(rumTime);
        }
        
        /// <summary>
        /// 读取运行时间
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<int>> ReadRunTimeAsync()
        {
            var read = await ReadFromCoreServerAsync(BuildReadRunTime());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            var result = read.Content.RemoveBegin(8);
            var rumTime = result.Length == 2 ? 0 : ByteTransform.TransInt32(result, 0);
            return OperateResult.CreateSuccessResult(rumTime);
        }

        /// <summary>
        /// 读取运行时间
        /// </summary>
        /// <returns></returns>
        public OperateResult<byte[]> ReadRunTimeByte()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadRunTime());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(read);
            return OperateResult.CreateSuccessResult(read.Content);
        }
        
        /// <summary>
        /// 读取机器上电时间
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadMachineAliveTime()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadMachineAliveTime());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(8);
            var aliveTime = result.Length==2 ? 0 : ByteTransform.TransInt32(result, 0);
            return OperateResult.CreateSuccessResult(aliveTime);
        }
        
        /// <summary>
        /// 读取机器上电时间
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<int>> ReadMachineAliveTimeAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadMachineAliveTime());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(8);
            var aliveTime = result.Length==2 ? 0 : ByteTransform.TransInt32(result, 0);
            return OperateResult.CreateSuccessResult(aliveTime);
        }
        
        /// <summary>
        /// 读取程序上电时间
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadNcAliveTime()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadNcAliveTime());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(8);
            var ncTime = result.Length == 2 ? 0 : ByteTransform.TransInt32(result, 0);
            return OperateResult.CreateSuccessResult(ncTime);
        }
        
        /// <summary>
        /// 读取程序上电时间
        /// </summary>
        /// <returns></returns>
        public async  Task<OperateResult<int>> ReadNcAliveTimeAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadNcAliveTime());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(8);
            var ncTime = result.Length == 2 ? 0 : ByteTransform.TransInt32(result, 0);
            return OperateResult.CreateSuccessResult(ncTime);
        }
      
        /// <summary>
        /// 读取进给倍率、主轴倍率、快速倍率
        /// </summary>
        /// <returns></returns>
        public OperateResult<int,int,int> ReadRate()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildRate());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int,int,int>(read);
            byte[] result = read.Content.RemoveBegin(8);
            int feedrate = ByteTransform.TransInt32(result, 0)/100;//进给倍率
            int spindlerate = ByteTransform.TransInt32(result, 4)/100;//主轴倍率
            int quickrate = ByteTransform.TransInt32(result, 8)/100;//快速倍率
            return OperateResult.CreateSuccessResult(feedrate, spindlerate, quickrate);
        }
        
        /// <summary>
        /// 读取进给倍率、主轴倍率、快速倍率
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<int,int,int>> ReadRateAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildRate());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int,int,int>(read);
            byte[] result = read.Content.RemoveBegin(8);
            int feedrate = ByteTransform.TransInt32(result, 0)/100;//进给倍率
            int spindlerate = ByteTransform.TransInt32(result, 4)/100;//主轴倍率
            int quickrate = ByteTransform.TransInt32(result, 8)/100;//快速倍率
            return OperateResult.CreateSuccessResult(feedrate, spindlerate, quickrate);
        }
        
        /// <summary>
        /// 读取刀具信息，刀具号、轴刀号、长度、直径
        /// </summary>
        /// <returns></returns>
        public OperateResult<int,int,double,double> ReadToolInfo()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadToolInfo());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, int, double, double>(read);
            int length = read.Content.Length;
            if (length > 14)
            {
                byte[] result = read.Content.RemoveBegin(8);
                int toolnum = ByteTransform.TransInt32(result, 0);//刀具号
                int toolaxis = ByteTransform.TransInt32(result, 4);//轴刀号
                double toollen = BitConverter.ToDouble(result, 8);//长度 
                double toolrad = BitConverter.ToDouble(result, 16);//直径
                //double toollen = ByteTransform.TransDouble(result, 8);//长度 
                // double toolrad = ByteTransform.TransDouble(result, 16);//直径
                return OperateResult.CreateSuccessResult(toolnum, toolaxis, toollen, toolrad);
            }
            return OperateResult.CreateSuccessResult(0, 0, 0.0, 0.0);
        }
        
        /// <summary>
        /// 读取刀具信息，刀具号、轴刀号、长度、直径
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<int,int,double,double>> ReadToolInfoAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadToolInfo());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, int, double, double>(read);
            int length = read.Content.Length;
            if (length > 14)
            {
                byte[] result = read.Content.RemoveBegin(8);
                int toolnum = ByteTransform.TransInt32(result, 0);//刀具号
                int toolaxis = ByteTransform.TransInt32(result, 4);//轴刀号
                double toollen = BitConverter.ToDouble(result, 8);//长度 
                double toolrad = BitConverter.ToDouble(result, 16);//直径
                //double toollen = ByteTransform.TransDouble(result, 8);//长度 
                // double toolrad = ByteTransform.TransDouble(result, 16);//直径
                return OperateResult.CreateSuccessResult(toolnum, toolaxis, toollen, toolrad);
            }
            return OperateResult.CreateSuccessResult(0, 0, 0.0, 0.0);
        }

        ///// <summary>
        ///// / 读取主轴速度
        ////// </summary>
        ////// <returns></returns>
        //public OperateResult<float> ReadActSpeed()
        //{
        //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadActSpeed());
        //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        //    byte[] result = read.Content.RemoveBegin(15);
        //    return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
        //}
        ///// <summary>
        ///// / 读取主轴编程速度
        ////// </summary>
        ////// <returns></returns>
        //public OperateResult<float> ReadSetSpeed()
        //{
        //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadSetSpeed());
        //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        //    byte[] result = read.Content.RemoveBegin(15);
        //    return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
        //}

        ///// <summary>
        ///// 读取进给速度
        ///// </summary>
        ///// <returns></returns>
        //public OperateResult<float> ReadActFspeed()
        //{
        //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadActFspeed());
        //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        //    byte[] result = read.Content.RemoveBegin(15);
        //    return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
        //}

        ///// <summary>
        ///// 读取进给编程速度
        ///// </summary>
        ///// <returns></returns>
        //public OperateResult<float> ReadSetFspeed()
        //{
        //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadSetFspeed());
        //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        //    byte[] result = read.Content.RemoveBegin(15);
        //    return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
        //}

        /// <summary>
        /// 读取报警信息
        /// </summary>
        /// <returns>读取报警号、报警内容</returns>
        public OperateResult<SysAlarm> ReadSystemAlarmInfo()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadAlarmInfo());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysAlarm>(read);
            int length = read.Content.Length;
            if (length > 14) {
                SysAlarm alarms =new SysAlarm();
                byte[] result = read.Content.RemoveBegin(8);
                alarms.Type= ByteTransform.TransInt16(result, 0);
                alarms.Group= ByteTransform.TransInt16(result, 2);
                alarms.AlarmId = ByteTransform.TransInt32(result, 4);
                byte[] result1 = result.RemoveBegin(8);
                alarms.Message = Encoding.Default.GetString(result1).TrimEnd('\0');
                return OperateResult.CreateSuccessResult(alarms);
            }
            return OperateResult.CreateSuccessResult(new SysAlarm());
        }
        
        /// <summary>
        /// 读取报警信息
        /// </summary>
        /// <returns>读取报警号、报警内容</returns>
        public async Task<OperateResult<SysAlarm>> ReadSystemAlarmInfoAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadAlarmInfo());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysAlarm>(read);
            int length = read.Content.Length;
            if (length > 14) {
                SysAlarm alarms =new SysAlarm();
                byte[] result = read.Content.RemoveBegin(8);
                alarms.Type= ByteTransform.TransInt16(result, 0);
                alarms.Group= ByteTransform.TransInt16(result, 2);
                alarms.AlarmId = ByteTransform.TransInt32(result, 4);
                byte[] result1 = result.RemoveBegin(8);
                alarms.Message = Encoding.Default.GetString(result1).TrimEnd('\0');
                return OperateResult.CreateSuccessResult(alarms);
            }
            return OperateResult.CreateSuccessResult(new SysAlarm());
        }
        #endregion

        #region Build Command

        /// <summary>
        /// 系统状态
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadSysStatus()
        {
            byte[] buffer = @"
                           0x00, 0x00, 0x00, 0x02, 0x52, 0x5f, 0x52, 0x49,
                           0x00, 0x1a
                            ".ToHexBytes();
            return buffer;
        }
        /// <summary>
        /// 报警状态
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadSysAlarm()
        {
            byte[] buffer = @"
                           0x17, 0x10, 0x00, 0x81
                            ".ToHexBytes();
            return buffer;
        }

        /// <summary>
        /// 系统模式
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadSysMode()
        {
            byte[] buffer = @"
                             0x00, 0x00, 0x00, 0x02, 0x52, 0x5f, 0x52, 0x49,
                             0x00, 0x17
                            ".ToHexBytes();
            return buffer;
        }
        /// <summary>
        /// 机械位置
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadMachinePos()
        {
            byte[] buffer = @"
                           0x00, 0x00, 0x00, 0x02, 0x52, 0x5f, 0x52, 0x49,
                           0x00, 0x15
                            ".ToHexBytes();
            return buffer;
        }
        //  
        /// <summary>
        /// 当前位置
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadCurrentPos()
        {
            byte[] buffer = @"
                           0x00, 0x00, 0x00, 0x02, 0x52, 0x5f, 0x52, 0x49,
                           0x00, 0x16
                            ".ToHexBytes();
            return buffer;
        }
        /// <summary>
        /// 工件产量
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadProduct()
        {
            byte[] buffer = @"
                           0x00, 0x00, 0x00, 0x05, 0x52, 0x5f, 0x4d, 0x42,
                           0x00, 0x00, 0x00, 0x4a, 0x02
                         ".ToHexBytes();

            return buffer;

        }
        /// <summary>
        /// 程序名
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadSystemProgram()
        {
            byte[] buffer = @"
                            0x00, 0x00, 0x00, 0x02, 0x52, 0x5f, 0x52, 0x49,
                            0x00, 0x18
                            ".ToHexBytes();

            return buffer;
        }

        /// <summary>
        /// 运行时间
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadRunTime()
        {
            byte[] buffer = @"
                            0x00, 0x00, 0x00, 0x02, 0x52, 0x5f, 0x52, 0x49,
                            0x00, 0x20
                            ".ToHexBytes();
            return buffer;
        }
        /// <summary>
        /// 机器上电时间
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadMachineAliveTime()
        {
            byte[] buffer = @"
                            0x00, 0x00, 0x00, 0x02, 0x52, 0x5f, 0x52, 0x49,
                            0x00, 0x1f
                            ".ToHexBytes();
            return buffer;
        }
        /// <summary>
        /// 程序上电时间
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadNcAliveTime()
        {
            byte[] buffer = @"
                            0x00, 0x00, 0x00, 0x02, 0x52, 0x5f, 0x52, 0x49,
                            0x00, 0x1e
                            ".ToHexBytes();
            return buffer;
        }
        /// <summary>
        /// 读取主轴倍率报文
        /// </summary>
        /// <returns></returns>
        public byte[] BuildRate()
        {
            byte[] buffer = @"
                            0x00, 0x00, 0x00, 0x02, 0x52, 0x5f, 0x52, 0x49,
                            0x00, 0x19
                            ".ToHexBytes();
            return buffer;
        }
        /// <summary>
        /// 读取刀具信息报文
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadToolInfo()
        {
            byte[] buffer = @"
                            0x00, 0x00, 0x00, 0x02, 0x52, 0x5f, 0x52, 0x49,
                            0x00, 0x33
                            ".ToHexBytes();
            return buffer;
        }
        ///// <summary>
        ///// 主轴转速
        ///// </summary>
        ///// <returns></returns>
        //public byte[] BuildReadActSpeed()
        //{
        //    byte[] buffer = @"
        //                     0x93, 0x00, 0x0c, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
        //                     0x1e, 0x17, 0x10, 0x17, 0x1a, 0x04, 0x00, 0x00,
        //                     0x55, 0xaa
        //                    ".ToHexBytes();
        //    return buffer;
        //}
        ///// <summary>
        ///// 主轴编程转速
        ///// </summary>
        ///// <returns></returns>
        //public byte[] BuildReadSetSpeed()
        //{
        //    byte[] buffer = @"
        //                     0x93, 0x00, 0x0c, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
        //                     0x1e, 0x17, 0x10, 0x17, 0x1a, 0x00, 0x00, 0x00,
        //                     0x55, 0xaa
        //                    ".ToHexBytes();
        //    return buffer;
        //}

        ///// <summary>
        ///// 进给转速
        ///// </summary>
        ///// <returns></returns>
        //public byte[] BuildReadActFspeed()
        //{
        //    byte[] buffer = @"
        //                     0x93, 0x00, 0x0c, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
        //                     0x1e, 0x17, 0x10, 0x17, 0x1a, 0x01, 0x00, 0x00,
        //                     0x55, 0xaa
        //                    ".ToHexBytes();
        //    return buffer;
        //}
        ///// <summary>
        ///// 进给编程转速
        ///// </summary>
        ///// <returns></returns>
        //public byte[] BuildReadSetFspeed()
        //{
        //    byte[] buffer = @"
        //                     0x93, 0x00, 0x0c, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
        //                     0x1e, 0x17, 0x10, 0x17, 0x1a, 0x00, 0x00, 0x00,
        //                     0x55, 0xaa
        //                    ".ToHexBytes();
        //    return buffer;
        //}
        /// <summary>
        /// 报警信息
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadAlarmInfo()
        {
            byte[] buffer = @"
                            0x00, 0x00, 0x00, 0x02, 0x52, 0x5f, 0x52, 0x49,
                            0x00, 0x1b
                            ".ToHexBytes();
            return buffer;
        }


        #endregion

    }
}
