namespace Heidenhain.Base
{
    /// <summary>
    /// 当前机床的报警信息
    /// </summary>
    public class SysAlarm
    {
        /// <summary>
        /// 当前报警的ID信息
        /// </summary>
        public int AlarmId { get; set; }

        /// <summary>
        /// 当前的报警类型
        ///   LSV2_EC_NONE = 0,LSV2_EC_WARNING = 1,LSV2_EC_FEEDHOLD = 2,LSV2_EC_PROGRAMHOLD = 3,LSV2_EC_PROGRAMABORT = 4,
        ///   LSV2_EC_EMERGENCYSTOP = 5,LSV2_EC_RESET = 6, LSV2_EC_ERROR = 7, LSV2_EC_INFO = 8
        /// </summary>
        public short Type { get; set; }

        /// <summary>
        /// 报警的组  LSV2_EG_NONE = 0,LSV2_EG_OPERATING = 1,LSV2_EG_PROGRAMMING = 2,LSV2_EG_PLC = 3,LSV2_EG_GENERAL = 4
        /// </summary>
        public short Group { get; set; }

        /// <summary>
        /// 报警的消息
        /// </summary>
        public string Message { get; set; }
    }
}