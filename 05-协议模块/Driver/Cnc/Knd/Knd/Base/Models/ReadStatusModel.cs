using System.Text.Json.Serialization;

namespace Knd.Base.Models;

/// <summary>
/// </summary>
public class ReadStatusModel
{
    /// <summary>
    ///     当前运行状态（运行、停止等）
    ///     - 0:CNC 处于停止状态
    ///     - 1:CNC 处于暂停（进给保持）状态
    ///     - 2:CNC 处于运行状态
    /// </summary>
    [JsonPropertyName("run-status")]
    public int Status { get; set; }

    /// <summary>
    ///     当前工作模式（录入,自动等）
    ///     - 0:录入方式
    ///     - 1:自动方式
    ///     - 3:编辑方式
    ///     - 4:单步方式
    ///     - 5:手动方式
    ///     - 6:手动编辑（示教）方式
    ///     - 7:手轮编辑（示教）方式
    ///     - 8:手轮方式
    ///     - 9:（机械）回零方式
    ///     - 10:程序回零方式
    /// </summary>
    [JsonPropertyName("opr-mode")]
    public int WorkMode { get; set; }

    /// <summary>
    ///     是否准备就绪
    /// </summary>
    [JsonPropertyName("ready")]
    public bool Ready { get; set; }

    /// <summary>
    ///     准备未绪的原因掩码值,可为以下值的 or:
    ///     - 0x1:急停信号有效
    ///     - 0x2:伺服准备未绪
    ///     - 0x4:IO 准备未绪（远程 IO 设备等）
    /// </summary>
    [JsonPropertyName("not-ready-reason")]
    public int NotReadyReason { get; set; }

    /// <summary>
    ///     当前报警类型的列表,如果当前没有报警,则列表为空
    /// </summary>
    [JsonPropertyName("alarms")]
    public string[] Alarms { get; set; }
}