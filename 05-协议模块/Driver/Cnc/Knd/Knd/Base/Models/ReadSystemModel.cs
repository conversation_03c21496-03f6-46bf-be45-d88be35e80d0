using System.Text.Json.Serialization;

namespace Knd.Base.Models;

/// <summary>
///     系统信息和版本返回结果
/// </summary>
public class ReadSystemModel
{
    public ReadSystemModel(string[] ncAxes)
    {
        NcAxes = ncAxes;
    }

    /// <summary>
    ///     唯一 ID 的 64 位十进制表示
    /// </summary>
    [JsonPropertyName("id")]
    public long Id { get; set; }

    /// <summary>
    ///     系统类型
    /// </summary>
    [JsonPropertyName("type")]
    public string Type { get; set; }

    /// <summary>
    ///     系统类型
    /// </summary>
    [JsonPropertyName("manufacturer")]
    public string Manufacturer { get; set; }

    /// <summary>
    ///     生产时间
    /// </summary>
    [JsonPropertyName("manufacture-time")]
    public string ManufactureTime { get; set; }

    /// <summary>
    ///     车铣类型
    /// </summary>
    [JsonPropertyName("cnc-type")]
    public string CncType { get; set; }

    /// <summary>
    ///     网络参数中的机床名称
    /// </summary>
    [JsonPropertyName("cnc-name")]
    public string CncName { get; set; }

    /// <summary>
    ///     系统软件版本号
    /// </summary>
    [JsonPropertyName("soft-version")]
    public string SoftVersion { get; set; }

    /// <summary>
    ///     FPGA 版本号
    /// </summary>
    [JsonPropertyName("fpga-version")]
    public string FpgaVersion { get; set; }

    /// <summary>
    ///     梯图版本号
    /// </summary>
    [JsonPropertyName("ladder-version")]
    public string LadderVersion { get; set; }

    /// <summary>
    ///     用户 NC 轴列表
    /// </summary>
    [JsonPropertyName("nc-axes")]
    public string[] NcAxes { get; set; }

    /// <summary>
    ///     用户 NC 轴相对坐标地址列表
    /// </summary>
    [JsonPropertyName("nc-relative-axes")]
    public string[] NcRelativeAxes { get; set; }

    /// <summary>
    ///     用户轴列表
    /// </summary>
    [JsonPropertyName("axes")]
    public string[] Axes { get; set; }

    /// <summary>
    ///     用户轴相对坐标地址列表
    /// </summary>
    [JsonPropertyName("relative-axes")]
    public string[] RelativeAxes { get; set; }
}