using System.Text.Json.Serialization;

namespace Knd.Base.Models;

/// <summary>
///     系统坐标
/// </summary>
public class ReadCoorsModel
{
    /// <summary>
    ///     绝对
    /// </summary>
    [JsonPropertyName("absolute")]
    public XyzModel Absolute { get; set; }

    /// <summary>
    ///     机床
    /// </summary>
    [JsonPropertyName("machine")]
    public XyzModel Machine { get; set; }

    /// <summary>
    ///     相对
    /// </summary>
    [JsonPropertyName("relative")]
    public XyzModel Relative { get; set; }

    /// <summary>
    ///     余移动量
    /// </summary>
    [JsonPropertyName("dist-to-go")]
    public XyzModel DistToGo { get; set; }
}

/// <summary>
///     xyz轴对象
/// </summary>
public class XyzModel
{
    /// <summary>
    ///     X轴
    /// </summary>
    public double X { get; set; }

    /// <summary>
    ///     Y轴
    /// </summary>
    public double Y { get; set; }

    /// <summary>
    ///     Z轴
    /// </summary>
    public double Z { get; set; }
}