using System.ComponentModel;

namespace Knd.Base.Enums;

/// <summary>
///     当前运行状态（运行、停止等）
/// </summary>
public enum KndRunStatus
{
    /// <summary>
    ///     CNC 处于停止状态
    /// </summary>
    [Description("CNC 处于停止状态")] Stop = 0,

    /// <summary>
    ///     CNC 处于暂停（进给保持）状态
    /// </summary>
    [Description("CNC 处于暂停（进给保持）状态")] Suspend = 1,

    /// <summary>
    ///     CNC 处于运行状态
    /// </summary>
    [Description("CNC 处于运行状态")] Run = 2
}

/// <summary>
///     当前工作模式（录入,自动等）
/// </summary>
public enum OprMode
{
    /// <summary>
    ///     录入方式
    /// </summary>
    [Description("录入方式")] In = 0,

    /// <summary>
    ///     自动方式
    /// </summary>
    [Description("自动方式")] Auto = 1,

    /// <summary>
    ///     编辑方式
    /// </summary>
    [Description("编辑方式")] Edit = 3,

    /// <summary>
    ///     单步方式
    /// </summary>
    [Description("单步方式")] One = 4,

    /// <summary>
    ///     手动方式
    /// </summary>
    [Description("手动方式")] Hand = 5,

    /// <summary>
    ///     手动编辑（示教）方式
    /// </summary>
    [Description("手动编辑（示教）方式")] HandEdit = 6,

    /// <summary>
    ///     手轮编辑（示教）方式
    /// </summary>
    [Description("手轮编辑（示教）方式")] HandWheel = 7,

    /// <summary>
    ///     手轮方式
    /// </summary>
    [Description("手轮方式")] Wheel = 8,

    /// <summary>
    ///     （机械）回零方式
    /// </summary>
    [Description("（机械）回零方式")] Mechanical = 9,

    /// <summary>
    ///     程序回零方式
    /// </summary>
    [Description("程序回零方式")] Clear = 10
}