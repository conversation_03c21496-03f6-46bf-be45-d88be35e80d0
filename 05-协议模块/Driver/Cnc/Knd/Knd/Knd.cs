using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Furion.JsonSerialization;
using Furion.RemoteRequest.Extensions;
using HslCommunication;
using Knd.Base.Models;
using DateTime = Common.Extension.DateTime;

namespace Knd;

[DriverSupported("Knd")]
[DriverInfo("Knd", "V1.1.0", "凯恩帝(Knd)")]
public class Knd : BaseCncProtocolCollector, IDriver
{
    public Knd(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    #region 配置参数

    [ConfigParameter("Url地址")] public string Url { get; set; } = "http://127.0.0.1:8088/v1/";

    #endregion

    public DeviceConnectDto Connect()
    {
        try
        {
            _readSystemModel ??= Url.SetHttpMethod(HttpMethod.Get).GetAsAsync<ReadSystemModel>().Result;
            if (_readSystemModel != null)
            {
                IsConnected = true;
                OperateResult = new OperateResult {IsSuccess = true, Message = ""};
            }
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接失败:【{ex.Message}】"};
        }

        return ResConnect();
    }

    /// <summary>
    ///     模式,运行状态,报警信息,报警状态,未就绪原因
    /// </summary>
    private ReadStatusModel _readStatusModel;

    /// <summary>
    ///     系统软件版本号,FPGA版本号,梯图版本号
    /// </summary>
    private ReadSystemModel _readSystemModel;

    /// <summary>
    ///     循环时间,加工时间
    /// </summary>
    private ReadCycleTimeModel _readCycleTimeModel;

    /// <summary>
    ///     清空数据
    /// </summary>
    /// <returns></returns>
    [Method("Clear", name: "Clear")]
    public Task Clear()
    {
        _readStatusModel = null;
        _readSystemModel = null;
        _readCycleTimeModel = null;
        return Task.CompletedTask;
    }

    #region 模式,运行状态,报警信息,报警状态,未就绪原因

    /// <summary>
    ///     模式
    /// </summary>
    /// <returns></returns>
    [Method("WorkMode", description: "0:录入方式；1:自动方式；3:编辑方式;4:单步方式； 5:手动方式； 6:手动编辑（示教）方式；7:手轮编辑（示教）方式; 8:手轮方式； 9:（机械）回零方式; 10:程序回零方式", name: "模式")]
    public async Task<DriverReturnValueModel> ReadWorkMode()
    {
        return await ReadStatusModel("WorkMode");
    }

    /// <summary>
    ///     运行状态
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", description: "0:CNC 处于停止状态;1:CNC 处于暂停（进给保持）状态;2:CNC 处于运行状态", name: "状态")]
    public async Task<DriverReturnValueModel> ReadRunStatus()
    {
        return await ReadStatusModel("RunStatus");
    }

    /// <summary>
    ///     报警信息
    /// </summary>
    /// <returns></returns>
    [Method("Alarm", TransPondDataTypeEnum.String, name: "报警信息")]
    public async Task<DriverReturnValueModel> ReadAlarm()
    {
        return await ReadStatusModel("Alarm");
    }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    [Method("AlarmStatus", TransPondDataTypeEnum.Bool, description: "1:报警；0:未报警", name: "报警状态")]
    public async Task<DriverReturnValueModel> ReadAlarmStatus()
    {
        return await ReadStatusModel("AlarmStatus");
    }

    /// <summary>
    ///     未就绪原因
    /// </summary>
    /// <returns></returns>
    [Method("NoReadyReason", description: " 0x1:急停信号有效;0x2:伺服准备未绪;0x4:IO 准备未绪（远程 IO 设备等）", name: "未就绪原因")]
    public async Task<DriverReturnValueModel> ReadNoReadyReason()
    {
        return await ReadStatusModel("NoReadyReason");
    }

    /// <summary>
    ///     模式,运行状态,报警信息,报警状态,未就绪原因
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadStatusModel(string identifier)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _readStatusModel ??= await (Url + "status").SetHttpMethod(HttpMethod.Get).GetAsAsync<ReadStatusModel>();
                if (_readStatusModel == null)
                {
                    IsConnected = false;
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "读取失败";
                }
                else
                {
                    switch (identifier)
                    {
                        case "WorkMode":
                            ret.Value = _readStatusModel.WorkMode;
                            break;
                        case "RunStatus":
                            ret.Value = _readStatusModel.Status;
                            break;
                        case "Alarm":
                            ret.DataType = DataTypeEnum.String;
                            ret.Value = _readStatusModel.Alarms.Any() ? JSON.Serialize(_readStatusModel.Alarms) : "";
                            break;
                        case "AlarmStatus":
                            ret.DataType = DataTypeEnum.Bool;
                            ret.Value = _readStatusModel.Alarms.Any();
                            break;
                        case "NoReadyReason":
                            ret.Value = _readStatusModel.NotReadyReason;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion

    #region 系统软件版本号,FPGA版本号,梯图版本号

    /// <summary>
    ///     系统软件版本号,FPGA版本号,梯图版本号
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSystemModel(string identifier)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                _readSystemModel ??= await Url.SetHttpMethod(HttpMethod.Get).GetAsAsync<ReadSystemModel>();
                if (_readSystemModel == null)
                {
                    IsConnected = false;
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "读取失败";
                }
                else
                {
                    switch (identifier)
                    {
                        case "SoftVersion":
                            ret.Value = _readSystemModel.SoftVersion;
                            break;
                        case "FpgaVersion":
                            ret.Value = _readSystemModel.FpgaVersion;
                            break;
                        case "LadderVersion":
                            ret.Value = _readSystemModel.LadderVersion;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     系统软件版本号
    /// </summary>
    /// <returns></returns>
    [Method("SoftVersion", TransPondDataTypeEnum.String, description: "", name: "系统软件版本号")]
    public async Task<DriverReturnValueModel> ReadSoftVersion()
    {
        return await ReadSystemModel("SoftVersion");
    }

    /// <summary>
    ///     FPGA版本号
    /// </summary>
    /// <returns></returns>
    [Method("FpgaVersion", TransPondDataTypeEnum.String, description: "", name: "FPGA版本号")]
    public async Task<DriverReturnValueModel> ReadFpgaVersion()
    {
        return await ReadSystemModel("FpgaVersion");
    }


    /// <summary>
    ///     梯图版本号
    /// </summary>
    /// <returns></returns>
    [Method("LadderVersion", TransPondDataTypeEnum.String, description: "", name: "梯图版本号")]
    public async Task<DriverReturnValueModel> ReadLadderVersion()
    {
        return await ReadSystemModel("LadderVersion");
    }

    #endregion

    #region 循环时间,加工时间

    /// <summary>
    ///     循环时间,加工时间
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadCycleTimeModel(string identifier)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _readCycleTimeModel ??= await (Url + "cycletime").SetHttpMethod(HttpMethod.Get).GetAsAsync<ReadCycleTimeModel>();
                if (_readCycleTimeModel == null)
                {
                    IsConnected = false;
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "读取失败";
                }
                else
                {
                    switch (identifier)
                    {
                        case "CycleTime":
                            ret.Value = _readCycleTimeModel.Cur;
                            break;
                        case "CutTime":
                            ret.Value = _readCycleTimeModel.Total;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    [Method("CycleTime", description: "", name: "循环时间")]
    public async Task<DriverReturnValueModel> ReadCycleTime()
    {
        return await ReadCycleTimeModel("CycleTime");
    }

    /// <summary>
    ///     加工时间
    /// </summary>
    /// <returns></returns>
    [Method("CutTime", description: "", name: "加工时间")]
    public async Task<DriverReturnValueModel> ReadCutTime()
    {
        return await ReadCycleTimeModel("CutTime");
    }

    #endregion

    /// <summary>
    ///     产量
    /// </summary>
    /// <returns></returns>
    [Method("CurrentNum", name: "产量")]
    public async Task<DriverReturnValueModel> ReadCurrentNum()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var result = await (Url + "workcounts/").SetHttpMethod(HttpMethod.Get).GetAsAsync<ReadWorkCountsModel>();
                if (result == null)
                {
                    IsConnected = false;
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "失败";
                    return ret;
                }

                ret.Value = result.Total;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            IsConnected = false;
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴1倍率
    /// </summary>
    /// <returns></returns>
    [Method("OverRidesSp1", TransPondDataTypeEnum.Double, name: "主轴1倍率")]
    public async Task<DriverReturnValueModel> ReadOverRidesSp1()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Double
        };
        try
        {
            if (IsConnected)
            {
                var result = await (Url + "overrides/sp/1").SetHttpMethod(HttpMethod.Get).GetAsAsync<ReadOverRidesModels>();
                if (result == null)
                {
                    IsConnected = false;
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "失败";
                    return ret;
                }

                ret.Value = result.Ov;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            IsConnected = false;
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     手动倍率
    /// </summary>
    /// <returns></returns>
    [Method("OverRidesJog", TransPondDataTypeEnum.Double, name: "手动倍率")]
    public async Task<DriverReturnValueModel> ReadOverRidesJog()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Double
        };
        try
        {
            if (IsConnected)
            {
                var result = await (Url + "overrides/jog").SetHttpMethod(HttpMethod.Get).GetAsAsync<ReadOverRidesModels>();
                if (result == null)
                {
                    IsConnected = false;
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "失败";
                    return ret;
                }

                ret.Value = result.Ov;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            IsConnected = false;
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     快速倍率
    /// </summary>
    /// <returns></returns>
    [Method("OverRidesRapid", TransPondDataTypeEnum.Double, name: "快速倍率")]
    public async Task<DriverReturnValueModel> ReadOverRidesRapid()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Double
        };
        try
        {
            if (IsConnected)
            {
                var result = await (Url + "overrides/rapid").SetHttpMethod(HttpMethod.Get).GetAsAsync<ReadOverRidesModels>();
                if (result == null)
                {
                    IsConnected = false;
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "失败";
                    return ret;
                }

                ret.Value = result.Ov;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            IsConnected = false;
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     工件坐标系
    /// </summary>
    /// <returns></returns>
    [Method("WorkCoorsCur", TransPondDataTypeEnum.String, name: "工件坐标系")]
    public async Task<DriverReturnValueModel> ReadWorkCoorsCur()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var result = await (Url + "workcoors/cur").SetHttpMethod(HttpMethod.Get).GetAsAsync<Dictionary<string, XyzModel>>();
                if (result == null)
                {
                    IsConnected = false;
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "失败";
                    return ret;
                }

                ret.Value = JSON.Serialize(result.Values);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            IsConnected = false;
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     程序号
    /// </summary>
    /// <returns></returns>
    [Method("CurPgm", name: "程序号")]
    public async Task<DriverReturnValueModel> ReadCurPgm()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var result = await (Url + "progs/cur").SetHttpMethod(HttpMethod.Get).GetAsAsync<ReadProgsCurModel>();
                if (result == null)
                {
                    IsConnected = false;
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = "失败";
                    return ret;
                }

                ret.Value = result.Number;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            IsConnected = false;
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    public override void Close()
    {
        IsConnected = false;
    }

    public override void Dispose()
    {
    }
}