using System;
using System.Linq;
using System.Threading.Tasks;
using Common.Enums;
using Common.Extension;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Enum;
using Driver.Core.Models;
using DriversInterface;
using Fanuc18i.Base;
using Feng.Common.Extension;
using Feng.Common.Util;
using Furion.JsonSerialization;
using HslCommunication;
using HslCommunication.CNC.Fanuc;
using StackExchange.Profiling.Internal;
using Console = System.Console;
using SysAllCoors = Fanuc18i.Base.SysAllCoors;

namespace Fanuc18i;

/* 更新日志
 *版本:v1.2.0
 *  1.完善采集点位功能，包含以下采集点位：
 *    - 基础状态：WorkMode(模式), RunStatus(运行状态), AlarmStatus(报警状态)
 *    - 坐标信息：AbsPos(绝对坐标), MachPos(机械坐标), RelPos(相对坐标)
 *    - 报警信息：Alarm(报警信息), AlarmId(报警Id), AlarmType(报警类型Id)
 *    - 程序信息：MainName(程序名), CurPgm(程序号), CurrentNum(产量)
 *    - 速度倍率：OvSpin(主轴倍率), ActFeed(进给速度), SetFSpeed(设定进给速度)
 *    - 主轴信息：SetsSpeed(设定主轴速度), OvFeed(进给倍率), sSpeed(主轴转速)
 *    - 温度监控：SpinTemp(主轴温度), SvTemp(伺服温度)
 *    - 系统信息：Series(系列信息), Version(版本号信息), ToolNum(刀具号)
 *    - 负载监控：AxisLoad(伺服负载), SpindleLoad(主轴负载)
 *    - 时间统计：AliveTime(上电时间), CycleTime(循环时间), CutTime(切削时间), RunTime(运行时间)
 *  2.所有采集点位都使用异步方法实现，提高性能
 *  3.采用批量读取策略，减少通信次数，提高效率
 *  4.完善错误处理和连接状态检查
 *
 *版本:v1.1.3
 *  1.基础功能实现
 *
 *
 */
[DriverSupported("Fanuc18i")]
[DriverInfo("Fanuc18i", "V1.2.0", "发那科(Fanuc)")]
public class Fanuc18i : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 8193;

    #endregion

    public Fanuc18i(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public override bool IsConnected => Driver != null && Driver.GetConnectStatus();

    // private FanucSeries18i Driver;

    public DeviceConnectDto Connect()
    {
        try
        {
            Driver ??= new FanucSeries18i(IpAddress, Port)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout
            };
            OperateResult = Driver.ConnectServer();
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接失败:【{ex.Message}】"};
        }

        return ResConnect();
    }

    /// <summary>
    ///     读取模式,运行状态,报警状态
    /// </summary>
    private OperateResult<SysStatusInfo> _statusInfo;

    /// <summary>
    ///  读取绝对坐标,机械坐标,相对坐标，剩余坐标
    /// </summary>
    private OperateResult<SysAllCoors> _sysAllCoors;
    /// <summary>
    ///     读取报警信息,报警Id,报警类型Id
    /// </summary>
    private OperateResult<SysAlarm[]> _systemAlarm;

    /// <summary>
    ///     读取系统信息
    /// </summary>
    private OperateResult<FanucSysInfo> _sysInfo;
    
    /// <summary>
    ///     清空数据
    /// </summary>
    /// <returns></returns>
    [Method("Clear", name: "Clear")]
    public Task Clear()
    {
        _statusInfo = null;
        _sysAllCoors = null;
        _systemAlarm = null;
        _sysInfo = null;
        return Task.CompletedTask;
    }

    #region 读取模式,运行状态,报警状态

    /// <summary>
    ///     读取模式,运行状态,报警状态
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> SysStatusInfo(string identifier)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _statusInfo ??= await Driver.ReadSysStatusInfoAsync();
                if (!_statusInfo.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _statusInfo.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "WorkMode":
                            ret.Value = Convert.ToInt32(_statusInfo.Content.WorkMode);
                            break;
                        case "RunStatus":
                            ret.Value = Convert.ToInt32(_statusInfo.Content.RunStatus);
                            break;
                        case "AlarmStatus":
                            ret.Value = _statusInfo.Content.Alarm;
                            break;
                        case "Emergency":
                            ret.Value = _statusInfo.Content.Emergency;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     紧急停止状态
    /// </summary>
    /// <returns></returns>
    [Method("Emergency", name: "紧急停止状态", description: "0:正常；1:急停")]
    public async Task<DriverReturnValueModel> ReadEmergency()
    {
        return await SysStatusInfo("Emergency");
    }
    
    /// <summary>
    ///     模式
    /// </summary>
    /// <returns></returns>
    [Method("WorkMode", description: "0:手动输入；1:自动循环；3:程序编辑；4:×100;5:连续进给；6:TeachInJog；7:TeachInHandle；8:InCfeed；9:ReFerence；10:ReMoTe", name: "模式")]
    public async Task<DriverReturnValueModel> ReadWorkMode()
    {
        return await SysStatusInfo("WorkMode");
    }

    /// <summary>
    ///     运行状态
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", description: "0:Reset；1:Stop；2:Hold；3:Start；4:Mstr；", name: "状态")]
    public async Task<DriverReturnValueModel> ReadRunStatus()
    {
        return await SysStatusInfo("RunStatus");
    }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    [Method("AlarmStatus", name: "报警状态")]
    public async Task<DriverReturnValueModel> ReadAlarmStatus()
    {
        return await SysStatusInfo("AlarmStatus");
    }

    #endregion

    #region 读取绝对坐标,机械坐标,相对坐标

    /// <summary>
    ///     读取绝对坐标,机械坐标,相对坐标
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSysAllCoorsAsync(string identifier)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                _sysAllCoors ??= await Driver.ReadSysAllCoorsAsync();
                if (!_sysAllCoors.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _sysAllCoors.Message;
                }
                else
                {
                    ret.Value = identifier switch
                    {
                        "AbsPos" => JSON.Serialize(_sysAllCoors.Content.Absolute),
                        "MachPos" => JSON.Serialize(_sysAllCoors.Content.Machine),
                        "RelPos" => JSON.Serialize(_sysAllCoors.Content.Relative),
                        "Remaining" => JSON.Serialize(_sysAllCoors.Content.Remaining),
                        _ => ret.Value
                    };
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     剩余坐标
    /// </summary>
    /// <returns></returns>
    [Method("Remaining", TransPondDataTypeEnum.String, name: "剩余坐标")]
    public async Task<DriverReturnValueModel> ReadRemaining()
    {
        return await ReadSysAllCoorsAsync("Remaining");
    }
    
    /// <summary>
    ///     绝对坐标
    /// </summary>
    /// <returns></returns>
    [Method("AbsPos", TransPondDataTypeEnum.String, name: "绝对坐标")]
    public async Task<DriverReturnValueModel> ReadAbsPos()
    {
        return await ReadSysAllCoorsAsync("AbsPos");
    }

    /// <summary>
    ///     机械坐标
    /// </summary>
    /// <returns></returns>
    [Method("MachPos", TransPondDataTypeEnum.String, name: "机械坐标")]
    public async Task<DriverReturnValueModel> ReadMachPos()
    {
        return await ReadSysAllCoorsAsync("MachPos");
    }

    /// <summary>
    ///     相对坐标
    /// </summary>
    /// <returns></returns>
    [Method("RelPos", TransPondDataTypeEnum.String, name: "相对坐标")]
    public async Task<DriverReturnValueModel> ReadRelPos()
    {
        return await ReadSysAllCoorsAsync("MachPos");
    }

    #endregion

    #region 读取报警信息,报警Id,报警类型Id

    /// <summary>
    ///     读取报警信息,报警Id,报警类型Id
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSystemAlarmAsync(string identifier)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                _systemAlarm ??= await Driver.ReadSystemAlarmAsync();
                if (!_systemAlarm.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _systemAlarm.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "Alarm":
                            ret.Value = JSON.Serialize(_systemAlarm.Content);
                            break;
                        case "AlarmId":
                        {
                            var content = _systemAlarm.Content.FirstOrDefault();
                            ret.Value = content != null ? content.AlarmId.ToString() : "";
                            break;
                        }
                        case "AlarmType":
                        {
                            var typeVal = "";
                            foreach (var item in _systemAlarm.Content)
                                if (typeVal.IsNotNull())
                                    typeVal = typeVal + "," + EnumUtil.GetEnumDesc((SysAlarmType) item.Type) + item.AlarmId;
                                else
                                    typeVal = EnumUtil.GetEnumDesc((SysAlarmType) item.Type) + item.AlarmId;
                            ret.Value = typeVal;
                            break;
                        }
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     报警信息
    /// </summary>
    /// <returns></returns>
    [Method("Alarm", TransPondDataTypeEnum.String, name: "报警信息")]
    public async Task<DriverReturnValueModel> ReadAlarm()
    {
        return await ReadSystemAlarmAsync("Alarm");
    }

    /// <summary>
    ///     报警Id
    /// </summary>
    /// <returns></returns>
    [Method("AlarmId", name: "报警Id")]
    public async Task<DriverReturnValueModel> ReadAlarmId()
    {
        return await ReadSystemAlarmAsync("AlarmId");
    }

    /// <summary>
    ///     报警类型Id
    /// </summary>
    /// <returns></returns>
    [Method("AlarmType", TransPondDataTypeEnum.String,name: "报警类型Id")]
    public async Task<DriverReturnValueModel> ReadAlarmType()
    {
        return await ReadSystemAlarmAsync("AlarmType");
    }

    #endregion

    /// <summary>
    /// 读取行号
    /// </summary>
    /// <returns></returns>
    [Method("ProgramNumber", name: "程序行号")]
    public async Task<DriverReturnValueModel> ReadProgramNumber()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadProgramNumberAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
    
    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    [Method("MainName", TransPondDataTypeEnum.String, name: "程序名")]
    public async Task<DriverReturnValueModel> ReadMainName()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSystemProgramCurrentAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content1.ToString();
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     程序号
    /// </summary>
    /// <returns></returns>
    [Method("CurPgm",TransPondDataTypeEnum.String, name: "程序号")]
    public async Task<DriverReturnValueModel> ReadCurPgm()
    {
        DriverReturnValueModel ret = new(){DataType = DataTypeEnum.String};
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSystemProgramCurrentAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content2.ToString();
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     产量
    /// </summary>
    /// <returns></returns>
    [Method("CurrentNum", name: "产量")]
    public async Task<DriverReturnValueModel> ReadCurrentNum()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCurrentProduceCountAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴倍率
    /// </summary>
    /// <returns></returns>
    [Method("OvSpin", name: "主轴倍率")]
    public async Task<DriverReturnValueModel> ReadOvSpin()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSpindleRateAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给速度
    /// </summary>
    /// <returns></returns>
    [Method("ActFeed", name: "进给速度")]
    public async Task<DriverReturnValueModel> ReadActFeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadActfSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴温度
    /// </summary>
    /// <returns></returns>
    [Method("SpinTemp", name: "主轴温度")]
    public async Task<DriverReturnValueModel> ReadSpinTemp()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadStemperAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     伺服温度
    /// </summary>
    /// <returns></returns>
    [Method("SvTemp", name: "伺服温度")]
    public async Task<DriverReturnValueModel> ReadSvTemp()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadFtemperAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     刀具号
    /// </summary>
    /// <returns></returns>
    [Method("ToolNum", name: "刀具号")]
    public async Task<DriverReturnValueModel> ReadToolNum()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadToolnumAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     设定进给速度
    /// </summary>
    /// <returns></returns>
    [Method("SetFSpeed", name: "设定进给速度")]
    public async Task<DriverReturnValueModel> ReadSetFSpeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSetfSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     设定主轴速度
    /// </summary>
    /// <returns></returns>
    [Method("SetsSpeed", name: "设定主轴速度")]
    public async Task<DriverReturnValueModel> ReadSetsSpeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSetsSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给倍率
    /// </summary>
    /// <returns></returns>
    [Method("OvFeed", name: "进给倍率")]
    public async Task<DriverReturnValueModel> ReadOvFeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadFeedRateAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
    
    /// <summary>
    ///     读取系统信息
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSysInfo(string identifier)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _sysInfo ??= Driver.ReadSysInfo();
                if (!_sysInfo.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _sysInfo.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "Series":
                            ret.Value = _sysInfo.Content.Series;
                            break;
                        case "Version":
                            ret.Value = _sysInfo.Content.Version;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     系列信息
    /// </summary>
    /// <returns></returns>
    [Method("Series", TransPondDataTypeEnum.String, name: "系列信息")]
    public async Task<DriverReturnValueModel> ReadSeries()
    {
        var ret = await ReadSysInfo("Series");
        ret.DataType = DataTypeEnum.String;
        return ret;
    }

    /// <summary>
    ///     版本号信息
    /// </summary>
    /// <returns></returns>
    [Method("Version", TransPondDataTypeEnum.String, name: "版本号信息")]
    public async Task<DriverReturnValueModel> ReadVersion()
    {
        var ret = await ReadSysInfo("Version");
        ret.DataType = DataTypeEnum.String;
        return ret;
    }
    /// <summary>
    ///     伺服负载
    /// </summary>
    /// <returns></returns>
    [Method("AxisLoad", TransPondDataTypeEnum.String, name: "伺服负载")]
    public async Task<DriverReturnValueModel> ReadAxisLoad()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadFanucAxisLoadAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }
                ret.Value =JSON.Serialize(read.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    [Method("sSpeed", TransPondDataTypeEnum.Double, name: "主轴转速")]
    public async Task<DriverReturnValueModel> ReadsSpeed()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Double
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSpindleSpeedAndFeedRateAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content1;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     上电时间
    /// </summary>
    /// <returns></returns>
    [Method("AliveTime", name: "上电时间")]
    public async Task<DriverReturnValueModel> ReadAliveTime()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int64
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadTimeDataAsync(6750);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    [Method("CycleTime", name: "循环时间")]
    public async Task<DriverReturnValueModel> ReadCycleTime()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int64
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCycleTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     切削时间
    /// </summary>
    /// <returns></returns>
    [Method("CutTime", name: "切削时间")]
    public async Task<DriverReturnValueModel> ReadCutTime()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int64
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadTimeDataAsync(6754);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     运行时间
    /// </summary>
    /// <returns></returns>
    [Method("RunTime", name: "运行时间")]
    public async Task<DriverReturnValueModel> ReadRunTime()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int64
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadTimeDataAsync(6752);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴负载
    /// </summary>
    /// <returns></returns>
    [Method("SpindleLoad", name: "主轴负载")]
    public async Task<DriverReturnValueModel> ReadSpindleLoad()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSpindleLoadAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
    // private IDriver _driverImplementation;
}