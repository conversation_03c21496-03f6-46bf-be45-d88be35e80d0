using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.CNC.Fanuc;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;

namespace Fanuc18i.Base
{
    /// <summary>
    /// 一个FANUC的机床通信类对象
    /// </summary>
    public class FanucSeries18i : NetworkDoubleBase
    {
        #region Constructor

        /// <summary>
        ///     获取设备连接状态
        /// </summary>
        /// <returns></returns>
        public bool GetConnectStatus()
        {
            return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
        }
        
        /// <summary>
        /// 根据IP及端口来实例化一个对象内容
        /// </summary>
        /// <param name="ipAddress">Ip地址信息</param>
        /// <param name="port">端口号</param>
        public FanucSeries18i(string ipAddress, int port = 8193)
        {
            this.IpAddress = ipAddress;
            this.Port = port;
            this.ByteTransform = new ReverseBytesTransform();
            this.encoding = Encoding.Default;
            this.ReceiveTimeOut = 30_000;
        }

        /// <inheritdoc/>
        protected override INetMessage GetNewNetMessage() => new CNCFanucSeriesMessage();


        /// <summary>
        /// 获取或设置当前的文本的字符编码信息，如果你不清楚，可以调用<see cref="ReadLanguage"/>方法来自动匹配。<br />
        /// Get or set the character encoding information of the current text. 
        /// If you are not sure, you can call the <see cref="ReadLanguage"/> method to automatically match.
        /// </summary>
        public Encoding TextEncoding
        {
            get => this.encoding;
            set => this.encoding = value;
        }


        /// <summary>
        /// 获取或设置当前操作的路径信息，默认为1，如果机床支持多路径的，可以设置为其他值。<br />
        /// Gets or sets the path information for the current operation, the default is 1, if the machine supports multipathing, it can be set to other values.
        /// </summary>
        public short OperatePath { get => this.opPath; set => this.opPath = value; }
        #endregion

        #region NetworkDoubleBase Override

        /// <inheritdoc/>
        protected override OperateResult InitializationOnConnect(Socket socket)
        {
            OperateResult<byte[]> read1 = ReadFromCoreServer(socket, "a0 a0 a0 a0 00 01 01 01 00 02 00 02".ToHexBytes());
            if (!read1.IsSuccess) return read1;

            // 读取系统信息
            OperateResult<byte[]> read2 = ReadFromCoreServer(socket, BuildReadArray(BuildReadSingle(0x18, 0, 0, 0, 0, 0)));
            if (!read2.IsSuccess) return read2;

            try
            {
                this.fanucSysInfo = new FanucSysInfo(read2.Content);
            }
            catch
            {

            }

            return OperateResult.CreateSuccessResult();
        }
        /// <inheritdoc/>
        protected override OperateResult ExtraOnDisconnect(Socket socket)
        {
            return ReadFromCoreServer(socket, "a0 a0 a0 a0 00 01 02 01 00 00".ToHexBytes());
        }
#if !NET35
        /// <inheritdoc/>
        protected async override Task<OperateResult> InitializationOnConnectAsync(Socket socket)
        {
            OperateResult<byte[]> read1 = await ReadFromCoreServerAsync(socket, "a0 a0 a0 a0 00 01 01 01 00 02 00 02".ToHexBytes());
            if (!read1.IsSuccess) return read1;

            // 读取系统信息
            OperateResult<byte[]> read2 = await ReadFromCoreServerAsync(socket, BuildReadArray(BuildReadSingle(0x18, 0, 0, 0, 0, 0)));
            if (!read2.IsSuccess) return read2;

            try
            {
                this.fanucSysInfo = new FanucSysInfo(read2.Content);
            }
            catch
            {

            }

            return OperateResult.CreateSuccessResult();
        }

        /// <inheritdoc/>
        protected async override Task<OperateResult> ExtraOnDisconnectAsync(Socket socket)
        {
            return await ReadFromCoreServerAsync(socket, "a0 a0 a0 a0 00 01 02 01 00 00".ToHexBytes());
        }
#endif
        #endregion

        private double GetFanucDouble(byte[] content, int index)
        {
            return GetFanucDouble(content, index, 1)[0];
        }

        private double[] GetFanucDouble(byte[] content, int index, int length)
        {
            double[] buffer = new double[length];
            for (int i = 0; i < length; i++)
            {
                int data = ByteTransform.TransInt32(content, index + 8 * i);
                int decs = ByteTransform.TransInt16(content, index + 8 * i + 6);
                buffer[i] = Math.Round(data * Math.Pow(0.1d, decs), decs);
            }
            return buffer;
        }

        private byte[] CreateFromFanucDouble(double value)
        {
            byte[] buffer = new byte[8];
            int interge = (int)(value * 1000);
            ByteTransform.TransByte(interge).CopyTo(buffer, 0);
            buffer[5] = 0x0A;
            buffer[7] = 0x03;
            return buffer;
        }

        private void ChangeTextEncoding(ushort code)
        {
            switch (code)
            {
                case 0x00: this.encoding = Encoding.Default; break;
                case 0x01:
                case 0x04: this.encoding = Encoding.GetEncoding("shift_jis", EncoderFallback.ReplacementFallback, new DecoderReplacementFallback()); break;
                case 0x06: this.encoding = Encoding.GetEncoding("ks_c_5601-1987"); break;
                case 0x0F: this.encoding = Encoding.GetEncoding("gb2312"); break;
                case 0x10: this.encoding = Encoding.GetEncoding("windows-1251"); break;
                case 0x11: this.encoding = Encoding.GetEncoding("windows-1254"); break;
            }
        }

        #region Read Write Support



        /// <summary>
        /// 获取fanuc机床设备的基本信息，型号，轴数量等等。<br />
        /// Get basic information about fanuc machines, models, number of axes and much more
        /// </summary>
        /// <returns>机床信息</returns>
  
        public OperateResult<FanucSysInfo> ReadSysInfo()
        {
            return this.fanucSysInfo == null ? new OperateResult<FanucSysInfo>("Must connect device first!") : OperateResult.CreateSuccessResult(this.fanucSysInfo);
        }
      
        /// <summary>
        /// 主轴转速
        /// </summary>
        /// <returns>主轴转速及进给倍率</returns>
        public OperateResult<double, double> ReadSpindleSpeedAndFeedRate()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0xA4, 3, 0, 0, 0, 0),
                BuildReadSingle(0x8A, 1, 0, 0, 0, 0),
                BuildReadSingle(0x88, 3, 0, 0, 0, 0),
                BuildReadSingle(0x88, 4, 0, 0, 0, 0),
                BuildReadSingle(0x24, 0, 0, 0, 0, 0),
                BuildReadSingle(0x25, 0, 0, 0, 0, 0),
                BuildReadSingle(0xA4, 3, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double, double>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            return OperateResult.CreateSuccessResult(GetFanucDouble(result[5], 14), GetFanucDouble(result[4], 14));
        }

        /// <summary>
        /// 读取主程序号和当前程序号
        /// </summary>
        /// <returns>主程序号和当前程序号</returns>
        public OperateResult<int,int> ReadSystemProgramCurrent()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0x1C, 0, 0, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int,int>(read);
            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            OperateResult check = CheckSingleResultLeagle(result);
            if (!check.IsSuccess) return OperateResult.CreateFailedResult<int,int>(check);
            int mainpro = ByteTransform.TransInt32(result, 14);
            int currpro = ByteTransform.TransInt32(result, 18);
            return OperateResult.CreateSuccessResult(mainpro, currpro);

        }
        /// <summary>
        /// 程序行号
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadProgramNumber()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x1d, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(28);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
        }
        /// <summary>
        /// 读取机床的语言设定信息，具体值的含义参照API文档说明<br />
        /// Read the language setting information of the machine tool, refer to the API documentation for the meaning of the specific values
        /// </summary>
        /// <remarks>此处举几个常用值 0: 英语 1: 日语 2: 德语 3: 法语 4: 中文繁体 6: 韩语 15: 中文简体 16: 俄语 17: 土耳其语</remarks>
        /// <returns>返回的语言代号</returns>
        public OperateResult<ushort> ReadLanguage()
        {
            var read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x8D, 0x0CD1, 0x0CD1, 0, 0, 0)
            ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<ushort>(read);
            var result = ExtraContentArray(read.Content.RemoveBegin(10));
            var code = result[0].Length >= 24 ? ByteTransform.TransUInt16(result[0], 24) : (ushort)0;
            ChangeTextEncoding(code);
            return OperateResult.CreateSuccessResult(code);
        }

        /// <summary>
        /// 读取宏变量，可以用来读取刀具号
        /// </summary>
        /// <param name="number"></param>
        /// <returns>读宏变量信息</returns>
        public OperateResult<double> ReadSystemMacroValue(int number)
        {
            return ByteTransformHelper.GetResultFromArray(ReadSystemMacroValue(number, 1));
        }

        /// <summary>
        /// 读取宏变量，可以用来读取刀具号
        /// </summary>
        /// <param name="number"></param>
        /// <param name="length">读取的长度信息</param>
        /// <returns>是否成功</returns>
        public OperateResult<double[]> ReadSystemMacroValue(int number, int length)
        {
            // 拆分5个5个读
            int[] lenArray = SoftBasic.SplitIntegerToArray(length, 5);
            int index = number;
            List<byte> result = new List<byte>();

            for (int i = 0; i < lenArray.Length; i++)
            {
                OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                    BuildReadSingle(0x15, index, index + lenArray[i] - 1, 0, 0, 0)));
                if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);

                result.AddRange(ExtraContentArray(read.Content.RemoveBegin(10))[0].RemoveBegin(14));
                index += lenArray[i];
            }

            try
            {
                return OperateResult.CreateSuccessResult(GetFanucDouble(result.ToArray(), 0, length));
            }
            catch (Exception ex)
            {
                return new OperateResult<double[]>(ex.Message + " Source:" + result.ToArray().ToHexString(' '));
            };
        }

        /// <summary>
        /// 写宏变量
        /// </summary>
        /// <param name="number">地址</param>
        /// <param name="values">数据值</param>
        /// <returns>是否成功</returns>
        public OperateResult WriteSystemMacroValue(int number, double[] values)
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildWriteSingle(0x16, number, number + values.Length - 1, 0, 0, values)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            if (ByteTransform.TransUInt16(result[0], 6) == 0)
            {
                return OperateResult.CreateSuccessResult();
            }
            else
            {
                return new OperateResult(ByteTransform.TransUInt16(result[0], 6), "Unknown Error");
            }
        }

        /// <summary>
        /// 写PMC
        /// </summary>
        /// <param name="addr">地址</param>
        /// <param name="values">值</param>
        /// <returns></returns>
        public OperateResult WritePmcValue(int addr, byte[] values)
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildWriteByte(0x8002, addr, addr + values.Length - 1, 0x09, 0, values)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            if (ByteTransform.TransUInt16(result[0], 6) == 0)
            {
                return OperateResult.CreateSuccessResult();
            }
            else
            {
                return new OperateResult(ByteTransform.TransUInt16(result[0], 6), "Unknown Error");
            }
        }

        public OperateResult WriteProductReset()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildProductZero());
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            if (ByteTransform.TransUInt16(result[0], 6) == 0)
            {
                return OperateResult.CreateSuccessResult();
            }
            else
            {
                return new OperateResult(ByteTransform.TransUInt16(result[0], 6), "Unknown Error");
            }
        }

        public OperateResult WritePmcByte(int addr, byte value) => WritePmcValue(addr, new byte[] { value });
        /// <summary>
        /// 根据刀具号写入长度形状补偿，刀具号为1-24
        /// </summary>
        /// <param name="cutter">刀具号，范围为1-24</param>
        /// <param name="offset">补偿值</param>
        /// <returns>是否写入成功</returns>
        public OperateResult WriteCutterLengthSharpOffset(int cutter, double offset) => WriteSystemMacroValue(11000 + cutter, new double[] { offset });

        /// <summary>
        /// 根据刀具号写入长度磨损补偿，刀具号为1-24
        /// </summary>
        /// <param name="cutter">刀具号，范围为1-24</param>
        /// <param name="offset">补偿值</param>
        /// <returns>是否写入成功</returns>
        public OperateResult WriteCutterLengthWearOffset(int cutter, double offset) => WriteSystemMacroValue(10000 + cutter, new double[] { offset });

        /// <summary>
        /// 根据刀具号写入半径形状补偿，刀具号为1-24
        /// </summary>
        /// <param name="cutter">刀具号，范围为1-24</param>
        /// <param name="offset">补偿值</param>
        /// <returns>是否写入成功</returns>
        public OperateResult WriteCutterRadiusSharpOffset(int cutter, double offset) => WriteSystemMacroValue(13000 + cutter, new double[] { offset });

        /// <summary>
        /// 根据刀具号写入半径磨损补偿，刀具号为1-24
        /// </summary>
        /// <param name="cutter">刀具号，范围为1-24</param>
        /// <param name="offset">补偿值</param>
        /// <returns>是否写入成功</returns>
        public OperateResult WriteCutterRadiusWearOffset(int cutter, double offset) => WriteSystemMacroValue(12000 + cutter, new double[] { offset });

        /// <summary>
        /// 读取伺服负载
        /// </summary>
        /// <returns>轴负载</returns>
        public OperateResult<double[]> ReadFanucAxisLoad()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0xA4, 2, 0, 0, 0, 0),
                BuildReadSingle(0x89, 0, 0, 0, 0, 0),
                BuildReadSingle(0x56, 1, 0, 0, 0, 0),
                BuildReadSingle(0xA4, 2, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            int length = -1;
            if (result[0].Length >= 16) length = ByteTransform.TransUInt16(result[0], 14);
            if (length < 0 || length * 8 + 14 > result[2].Length) length = (result[2].Length - 14) / 8;
            return OperateResult.CreateSuccessResult(GetFanucDouble(result[2], 14, length));
        }

        /// <summary>
        /// 读取主轴负载
        /// </summary>
        /// <returns>主轴负载</returns>
        public OperateResult<double> ReadSpindleLoad()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x40, 4, -1, 0, 0, 0),
                BuildReadSingle(0x40, 5, -1, 0, 0, 0),
                BuildReadSingle(0x8A, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            if (result[0].Length >= 18) return OperateResult.CreateSuccessResult(GetFanucDouble(result[0], 14));
            return new OperateResult<double>("Read failed, data is too short: " + result[0].ToHexString(' '));
        }
        /// <summary>
        /// 读取机床的坐标
        /// </summary>
        /// <returns></returns>
        public OperateResult<SysAllCoors> ReadSysAllCoors()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x19, 0, 0, 0, 0, 0),
                BuildReadSingle(0x26, 0, -1, 0, 0, 0),   //4个轴  轴0 X  轴1 Y  轴2 Z 轴3 B
                BuildReadSingle(0x26, 1, -1, 0, 0, 0),
                BuildReadSingle(0x26, 2, -1, 0, 0, 0),
                BuildReadSingle(0x26, 3, -1, 0, 0, 0),
                BuildReadSingle(0x89, -1, 0, 0, 0, 0),
                BuildReadSingle(0x0e, 0x0c2b, 0x0c2b, -1, 0, 0),
                BuildReadSingle(0x88, 2, 0, 0, 0, 0),
                BuildReadSingle(0x19, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysAllCoors>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            int length = fanucSysInfo.Axes; //ByteTransform.TransUInt16(result[0], 14);

            SysAllCoors allCoors = new SysAllCoors();

            allCoors.Absolute = GetFanucDouble(result[1], 14, length);
            allCoors.Machine = GetFanucDouble(result[2], 14, length);
            allCoors.Relative = GetFanucDouble(result[3], 14, length);
            allCoors.Remaining = GetFanucDouble(result[4], 14, length);
            
            // 先确保系统信息已读取
            if (fanucSysInfo != null)
            {
                // 根据系统信息中的轴数量来截取相应数量的坐标
                int axesCount = fanucSysInfo.Axes;
                allCoors.Absolute = allCoors.Absolute.Take(axesCount).ToArray();
                allCoors.Machine = allCoors.Machine.Take(axesCount).ToArray();
                allCoors.Relative = allCoors.Relative.Take(axesCount).ToArray();
                allCoors.Remaining = allCoors.Remaining.Take(axesCount).ToArray();
            }
            
            return OperateResult.CreateSuccessResult(allCoors);
        }

        /// <summary>
        /// 读取报警信息
        /// </summary>
        /// <returns></returns>
        public OperateResult<SysAlarm[]> ReadSystemAlarm()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x23, -1, 10, 2, 64, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysAlarm[]>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            if (ByteTransform.TransUInt16(result[0], 12) > 0)
            {
                int length = ByteTransform.TransUInt16(result[0], 12) / 80;
                SysAlarm[] alarms = new SysAlarm[length];
                for (int i = 0; i < alarms.Length; i++)
                {
                    alarms[i] = new SysAlarm();
                    alarms[i].AlarmId = ByteTransform.TransInt32(result[0], 14 + 80 * i);
                    alarms[i].Type = ByteTransform.TransInt16(result[0], 20 + 80 * i);
                    alarms[i].Axis = ByteTransform.TransInt16(result[0], 24 + 80 * i);

                    ushort msgLength = ByteTransform.TransUInt16(result[0], 28 + 80 * i);
                    alarms[i].Message = Encoding.Default.GetString(result[0], 30 + 80 * i, msgLength);

                   // alarms[i].Message = Encoding.GetEncoding("gb2312").GetString(result[0], 30 + 80 * i, msgLength);//中文系统默认编码格式GB2312

                }
                return OperateResult.CreateSuccessResult(alarms);
            }
            else
                return OperateResult.CreateSuccessResult(new SysAlarm[0]);
        }

        /// <summary>
        /// 读取fanuc机床的时间，6750是开机时间，6752是运行时间，6754是切割时间， 返回分为单位的信息
        /// </summary>
        /// <param name="timeType">读取的时间类型</param>
        /// <returns>秒为单位的结果</returns>
        public OperateResult<long> ReadTimeData(int timeType)
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x0e, timeType, timeType, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<long>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            OperateResult check = CheckSingleResultLeagle(result);
            if (!check.IsSuccess) return OperateResult.CreateFailedResult<long>(check);
            long time = ByteTransform.TransInt32(result, 22);
            return OperateResult.CreateSuccessResult(time);
        }


        /// <summary>
        /// 循环时间
        /// </summary>
        /// <returns></returns>
        public  OperateResult<long> ReadCycleTime()
        {
            OperateResult<long> read = ReadTimeData(6758);
            long munite = read.Content;
            OperateResult<long> read1 =  ReadTimeData(6757);
            long second = read1.Content/1000;
            return OperateResult.CreateSuccessResult(munite * 60 + second);
        }

        /// <summary>
        /// 读取报警状态信息
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadAlarmStatus()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x1A, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            return OperateResult.CreateSuccessResult((int)ByteTransform.TransUInt16(result[0], 16));
        }


        /// <summary>
        /// 读取进给倍率,Fanuc进给倍率需要255-读取值
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadFeedRate()
        {
            //OperateResult<byte[]> read = ReadFromCoreServer(BuildReadFeedRate());
            //if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            //byte[] result = read.Content.RemoveBegin(28);
            //return OperateResult.CreateSuccessResult(255 - (int)(result[0]));
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
               BuildReadMulti(mode: 0x02, code: 0x8001, 12, 13, 0, 1, 0)
    ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            OperateResult check = CheckSingleResultLeagle(result);
            if (!check.IsSuccess) return OperateResult.CreateFailedResult<int>(check);

            return OperateResult.CreateSuccessResult(100 - (this.ByteTransform.TransUInt16(result, 14) - 155));
        }
        /// <summary>
        /// 读取主轴倍率
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadSpindleRate()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadMulti(mode: 0x02, code: 0x8001, 30, 31, 0, 1, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            OperateResult check = CheckSingleResultLeagle(result);
            if (!check.IsSuccess) return OperateResult.CreateFailedResult<int>(check);

            return OperateResult.CreateSuccessResult((int)this.ByteTransform.TransUInt16(result, 14));
        }
        /// <summary>
        /// 读取进给速度
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadActfSpeed()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                                  BuildReadSingle(0x24, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(28);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
        }

        /// <summary>
        /// 读取进给速度设定
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadSetfSpeed()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x15, 0x10d5, 0x10d5, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(28);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000);
        }
        /// <summary>
        /// 读取主轴速度设定
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<int>> ReadSetsSpeedAsync()
        {            
            OperateResult<byte[]> read =await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x15, 0x10df, 0x10df, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(28);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000);
        }

        /// <summary>
        /// 读取主轴速度设定
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadSetsSpeed()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x15, 0x10df, 0x10df, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(28);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000);
        }

        /// <summary>
        /// 读取主轴温度
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadStemper()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x30, 0x0193, 0x0193, 1, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            byte[] result = read.Content.RemoveBegin(36);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
        }

        /// <summary>
        /// 读取伺服温度
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadFtemper()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x30, 0x0134, 0x0134, 1, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            byte[] result = read.Content.RemoveBegin(36);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
        }

        /// <summary>
        /// 读取当前刀具号
        /// </summary>
        /// <returns></returns>
        public OperateResult<int> ReadToolnum()
        {
            //OperateResult<byte[]> read = ReadFromCoreServer(BuildReadToolnum());
            //if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            //byte[] result = read.Content.RemoveBegin(28);
            //return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
            OperateResult<double[]> read = ReadSystemMacroValue(4120, 1);
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            return OperateResult.CreateSuccessResult(Convert.ToInt32(read.Content[0]));
        }


        /// <summary>
        /// 读取系统的基本信息状态，工作模式，运行状态，是否急停等等操作
        /// </summary>
        /// <returns>结果信息数据</returns>
        public OperateResult<SysStatusInfo> ReadSysStatusInfo()
        {

            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x19, 0, 0, 0, 0, 0),
                BuildReadSingle(0xE1, 0, 0, 0, 0, 0),
                BuildReadSingle(0x98, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysStatusInfo>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            SysStatusInfo statusInfo = new SysStatusInfo();
            statusInfo.Dummy = result[2].Length >= 16 ? ByteTransform.TransInt16(result[1], 14) : (short)0; 
            statusInfo.TMMode = result[2].Length >= 16 ? ByteTransform.TransInt16(result[2], 14) : (short)0;
            statusInfo.WorkMode = (CNCWorkMode)ByteTransform.TransInt16(result[0], 14);
            statusInfo.RunStatus = (CNCRunStatus)ByteTransform.TransInt16(result[0], 16);
            statusInfo.Motion = ByteTransform.TransInt16(result[0], 18);
            statusInfo.MSTB = ByteTransform.TransInt16(result[0], 20);
            statusInfo.Emergency = ByteTransform.TransInt16(result[0], 22);
            statusInfo.Alarm = ByteTransform.TransInt16(result[0], 24);
            statusInfo.Edit = ByteTransform.TransInt16(result[0], 26);

            return OperateResult.CreateSuccessResult(statusInfo);
        }


        public OperateResult<byte[]> ReadSysStatusInfobyte()
        {

            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x19, 0, 0, 0, 0, 0),
                BuildReadSingle(0xE1, 0, 0, 0, 0, 0),
                BuildReadSingle(0x98, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            SysStatusInfo statusInfo = new SysStatusInfo();
           // statusInfo.Dummy = ByteTransform.TransInt16(result[1], 14);
            //if (result[2].Length > 14)
            //{
            //    statusInfo.TMMode = ByteTransform.TransInt16(result[2], 14);
            //}
           // statusInfo.TMMode = result[2].Length >= 16 ? ByteTransform.TransInt16(result[2], 14) : (short)0;
            statusInfo.WorkMode = (CNCWorkMode)ByteTransform.TransInt16(result[0], 14);

            return OperateResult.CreateSuccessResult(result[0]);
        }
  

        private OperateResult<string[]> ParseAxisNames(byte[] content)
        {
            byte[] result = ExtraContentArray(content.RemoveBegin(10))[0];
            OperateResult checkResult = CheckSingleResultLeagle(result);
            if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<string[]>(checkResult);

            int length = fanucSysInfo.Axes;//ByteTransform.TransInt32(result, 10);
            List<string> list = new List<string>();
            for (int i = 0; i < length; i += 4)
            {
                if (i < result.Length)
                    list.Add(Encoding.ASCII.GetString(result, 14 + i, 1));
            }
            return OperateResult.CreateSuccessResult(list.ToArray());
        }
        /// <summary>
        /// 获取系统的轴名称信息，数组的长度表示有几个轴<br />
        /// Gets the axis name information of the system, and the length of the array indicates how many axes there are
        /// </summary>
        /// <returns>机床的轴名称列表</returns>

        public OperateResult<string[]> ReadAxisNames()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x89, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string[]>(read);

            return ParseAxisNames(read.Content);
        }
        /// <summary>
        /// 读取设备的程序列表
        /// </summary>
        /// <returns>读取结果信息</returns>
        public OperateResult<int[]> ReadProgramList()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x06, 0x01, 0x13, 0, 0, 0)
                ));
            OperateResult<byte[]> check = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x06, 0x1A0B, 0x13, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int[]>(read);
            if (!check.IsSuccess) return OperateResult.CreateFailedResult<int[]>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];

            OperateResult checkResult = CheckSingleResultLeagle(result);
            if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<int[]>(checkResult);

            int length = (result.Length - 14) / 72;
            int[] programs = new int[length];
            for (int i = 0; i < length; i++)
            {
                programs[i] = ByteTransform.TransInt32(result, 14 + 72 * i);
            }
            return OperateResult.CreateSuccessResult(programs);
        }

        /// <summary>
        /// 读取当前的刀具补偿信息
        /// </summary>
        /// <param name="cutterNumber">刀具数量</param>
        /// <returns>结果内容</returns>
        public OperateResult<CutterInfo[]> ReadCutterInfos(int cutterNumber = 24)
        {
            OperateResult<byte[]> read1 = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 0, 0, 0)));
            if (!read1.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read1);

            OperateResult<byte[]> read2 = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 1, 0, 0)));
            if (!read2.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read2);

            OperateResult<byte[]> read3 = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 2, 0, 0)));
            if (!read3.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read3);

            OperateResult<byte[]> read4 = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 3, 0, 0)));
            if (!read4.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read4);

            return ExtraCutterInfos(read1.Content, read2.Content, read3.Content, read4.Content, cutterNumber);
        }



        /// <summary>
        /// 读取寄存器的数据信息，需要传入寄存器的代码，起始地址，结束地址信息<br />
        /// To read the data information of the register, you need to pass in the code of the register, the start address, and the end address information
        /// </summary>
        /// <param name="code">寄存器代码</param>
        /// <param name="start">起始的地址</param>
        /// <param name="end">结束的地址</param>
        /// <returns>包含原始字节信息的结果对象</returns>

        public OperateResult<byte[]> ReadData(int code, int start, int end)
        {
            OperateResult<byte[]> read1 = ReadFromCoreServer(BuildReadArray(BuildReadMulti(0x02, 0x8001, start, end, code, 0, 0)));
            if (!read1.IsSuccess) return read1;

            byte[] result = ExtraContentArray(read1.Content.RemoveBegin(10))[0];

            OperateResult checkResult = CheckSingleResultLeagle(result);
            if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(checkResult);

            int length = this.ByteTransform.TransUInt16(result, 12);
            return OperateResult.CreateSuccessResult(result.SelectMiddle(14, length));
        }

        /// <summary>
        /// 将原始字节的数据写入到指定的寄存器里，需要传入寄存器的代码，起始地址，原始的字节数据信息<br />
        /// To write the original byte data into the specified register, you need to pass in the code of the register, the starting address, and the original byte data information
        /// </summary>
        /// <param name="code">寄存器代码</param>
        /// <param name="start">起始的地址</param>
        /// <param name="data">等待写入的原始字节数据</param>
        /// <returns>是否写入成功</returns>
       
        public OperateResult WriteData(int code, int start, byte[] data)
        {
            if (data == null) data = new byte[0];
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(BuildWriteSingle(0x02, 0x8002, start, start + data.Length - 1, code, 0, data)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            return CheckSingleResultLeagle(result);
        }

        /// <summary>
        /// 读取PMC数据，需要传入起始地址和结束地址，返回byte[]数据信息<br />
        /// To read PMC data, you need to pass in the start address and length, and return byte[] data information
        /// </summary>
        /// <remarks>
        /// 地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5
        /// </remarks>
        /// <param name="address">起始地址，地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5</param>
        /// <param name="length">长度信息</param>
        /// <returns>读取结果</returns>
    
        public OperateResult<byte[]> ReadPMCData(string address, ushort length) => FanucPMCAddress.ParseFrom(address, length).Then(m => ReadData(m.DataCode, m.AddressStart, m.AddressEnd));

        /// <summary>
        /// 写入PMC数据，需要传入起始地址和，以及等待写入的byte[]数据信息<br />
        /// To write PMC data, you need to pass in the start address, as well as the byte[] data information waiting to be written
        /// </summary>
        /// <remarks>
        /// 地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5
        /// </remarks>
        /// <param name="address">起始地址，地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5</param>
        /// <param name="value">等待写入的原始字节数据</param>
        /// <returns>是否写入成功</returns>

        public OperateResult WritePMCData(string address, byte[] value) => FanucPMCAddress.ParseFrom(address, 1).Then(m => WriteData(m.DataCode, m.AddressStart, value));

        public OperateResult<int> ReadInt32(string address)
        {
            OperateResult<byte[]> read = ReadPMCData(address, 4);
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content;
            //   ByteTransform.DataFormat = DataFormat.ABCD;
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));

        }


        //public OperateResult<byte[]> ReadByte(string address)
        //{
        //    OperateResult<byte[]> read = ReadPMCData(address, 4);
        //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(read);
        //    byte[] result = read.Content;
        //    return OperateResult.CreateSuccessResult(result);

        //}


        public OperateResult<int> ReadInt16(string address)
        {
            OperateResult<byte[]> read = ReadPMCData(address, 2);
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content;
            ByteTransform.DataFormat = DataFormat.ABCD;
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
        }

        public OperateResult<bool> ReadBool(string address)
        {
            OperateResult<byte[]> read = ReadPMCData(address, 1);
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<bool>(read);
            byte[] result = read.Content;
            // ByteTransform.DataFormat = DataFormat.ABCD;
            return OperateResult.CreateSuccessResult(ByteTransform.TransBool(result, 0));
        }

        /// <summary>
        /// 读取工件尺寸
        /// </summary>
        /// <returns></returns>
        public OperateResult<double[]> ReadDeviceWorkPiecesSize() => ReadSystemMacroValue(601, 20);

        /// <summary>
        /// 读取当前的程序内容，只能读取程序的片段，返回程序内容。<br />
        /// Read the current program content, only read the program fragments, and return the program content.
        /// </summary>
        /// <returns>程序内容</returns>
        public OperateResult<string> ReadCurrentProgram()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(
                BuildReadArray(BuildReadSingle(0x20, 0x0594, 0x00, 0x00, 0x00, 0x00)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            OperateResult checkResult = CheckSingleResultLeagle(result);
            if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<string>(checkResult);
            return OperateResult.CreateSuccessResult(Encoding.ASCII.GetString(result, 18, result.Length - 18));
        }


        /// <summary>
        /// 设置指定的程序号为当前的主程序，如果程序号不存在，返回错误信息<br />
        /// Set the specified program number as the current main program, if the program number does not exist, an error message will be returned
        /// </summary>
        /// <param name="programNum">程序号信息</param>
        /// <returns>是否设置成功</returns>
        public OperateResult SetCurrentProgram(ushort programNum)
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0x03, programNum, 0, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, string>(read);
            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            return CheckSingleResultLeagle(result);
        }

        /// <summary>
        /// 启动加工程序<br />
        /// Start the processing program
        /// </summary>
        /// <returns>是否启动成功</returns>

        public OperateResult StartProcessing()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0x01, 0, 0, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, string>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            return CheckSingleResultLeagle(result);
        }


        /// <summary>
        ///  将指定文件的NC加工程序，下载到数控机床里，返回是否下载成功<br />
        /// <b>[Authorization]</b> Download the NC machining program of the specified file to the CNC machine tool, and return whether the download is successful
        /// </summary>
        /// <remarks>
        /// 程序文件的内容必须%开始，%结束，下面是一个非常简单的例子：<br />
        /// %<br />
        /// O0006<br />
        /// G90G10L2P1<br />
        /// M30<br />
        /// %
        /// </remarks>
        /// <param name="file">程序文件的路径</param>
        /// <returns>是否下载成功</returns>
        public OperateResult WriteProgramFile(string file)
        {
            string content = File.ReadAllText(file);
            return WriteProgramContent(content);
        }

        /// <summary>
        /// 将指定程序内容的NC加工程序，写入到数控机床里，返回是否下载成功<br />
        /// <b>[Authorization]</b> Download the NC machining program to the CNC machine tool, and return whether the download is successful
        /// </summary>
        /// <remarks>
        /// 程序文件的内容必须%开始，%结束，下面是一个非常简单的例子：<br />
        /// %<br />
        /// O0006<br />
        /// G90G10L2P1<br />
        /// M30<br />
        /// %
        /// </remarks>
        /// <param name="program">程序内容信息</param>
        /// <param name="everyWriteSize">每次写入的长度信息</param>
        /// <returns>是否下载成功</returns>
        public OperateResult WriteProgramContent(string program, int everyWriteSize = 512, string path = "")
        {

            OperateResult<Socket> socket = CreateSocketAndConnect(IpAddress, Port, ConnectTimeOut);
            if (!socket.IsSuccess) return socket.ConvertFailed<int>();

            OperateResult<byte[]> ini1 = ReadFromCoreServer(socket.Content, "a0 a0 a0 a0 00 01 01 01 00 02 00 01".ToHexBytes());
            if (!ini1.IsSuccess) return ini1;

            OperateResult<byte[]> read1 = ReadFromCoreServer(socket.Content, BulidWriteProgramFilePre(path));
            if (!read1.IsSuccess) return read1;

            List<byte[]> contents = BulidWriteProgram(Encoding.ASCII.GetBytes(program), everyWriteSize);
            for (int i = 0; i < contents.Count; i++)
            {
                OperateResult<byte[]> read2 = ReadFromCoreServer(socket.Content, contents[i], false);
                if (!read2.IsSuccess) return read2;
            }

            OperateResult<byte[]> read3 = ReadFromCoreServer(socket.Content, new byte[] { 0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x13, 0x01, 0x00, 0x00 });
            if (!read3.IsSuccess) return read3;

            socket.Content?.Close();
            if (read3.Content.Length >= 14)
            {
                int err = this.ByteTransform.TransInt16(read3.Content, 12);
                if (err != 0) return new OperateResult<string>(err, StringResources.Language.UnknownError);
            }

            return OperateResult.CreateSuccessResult();
        }

        /// <summary>
        /// 读取指定程序号的程序内容<br />
        ///  Read the program content of the specified program number
        /// </summary>
        /// <param name="program">程序号</param>
        /// <returns>程序内容</returns>
 		public OperateResult<string> ReadProgram(int program, string path = "")
        {

            OperateResult<Socket> socket = CreateSocketAndConnect(IpAddress, Port, ConnectTimeOut);
            if (!socket.IsSuccess) return socket.ConvertFailed<string>();

            OperateResult<byte[]> ini1 = ReadFromCoreServer(socket.Content, "a0 a0 a0 a0 00 01 01 01 00 02 00 01".ToHexBytes());
            if (!ini1.IsSuccess) return OperateResult.CreateFailedResult<string>(ini1);

            OperateResult<byte[]> read1 = ReadFromCoreServer(socket.Content, BuildReadProgramPre(program, path));
            if (!read1.IsSuccess) return OperateResult.CreateFailedResult<string>(read1);

            // 检测错误信息
            int err = read1.Content[12] * 256 + read1.Content[13];
            if (err != 0)
            {
                socket.Content?.Close();
                return new OperateResult<string>(err, StringResources.Language.UnknownError);
            }

            // 接收所有的程序内容
            StringBuilder sb = new StringBuilder();
            while (true)
            {
                OperateResult<byte[]> read2 = ReadFromCoreServer(socket.Content, null);
                if (!read2.IsSuccess) return OperateResult.CreateFailedResult<string>(read2);

                if (read2.Content[6] == 0x16)
                    sb.Append(Encoding.ASCII.GetString(read2.Content, 10, read2.Content.Length - 10));
                else if (read2.Content[6] == 0x17)
                    break;
            }

            //OperateResult<byte[]> read3 = ReadFromCoreServer( socket.Content, null );
            //if (!read3.IsSuccess) return OperateResult.CreateFailedResult<string>( read3 );

            OperateResult send = Send(socket.Content, new byte[] { 0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x17, 0x02, 0x00, 0x00 });
            //if (!send.IsSuccess) return OperateResult.CreateFailedResult<string>( send );

            socket.Content?.Close();
            return OperateResult.CreateSuccessResult(sb.ToString());
        }

        /// <summary>
        /// 根据指定的程序号信息，删除当前的程序信息<br />
        /// According to the designated program number information, delete the current program information
        /// </summary>
        /// <param name="program">程序号</param>
        /// <returns>是否删除成功</returns>
        public OperateResult DeleteProgram(int program)
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0x05, program, 0, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, string>(read);
            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            return CheckSingleResultLeagle(result);
        }
        /// <summary>
        /// 根据指定的文件名来删除文件，如果是路径，则必须 '/' 结尾，如果是文件，则需要输入完整的文件名，例如：//CNC_MEM/USER/PATH2/O12<br />
        /// </summary>
        /// <param name="fileName">文件名称，也可以是路径信息</param>
        /// <returns>是否删除成功</returns>
        public OperateResult DeleteFile(string fileName)
        {
            byte[] buffer = new byte[256];
            Encoding.ASCII.GetBytes(fileName).CopyTo(buffer, 0);

            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(BuildWriteSingle(0x01, 0xB6, 0, 0, 0, 0, buffer)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            return CheckSingleResultLeagle(result);
        }


        /// <summary>
        /// 读取当前程序的前台路径<br />
        /// Read the foreground path of the current program
        /// </summary>
        /// <returns>程序的路径信息</returns>
        public OperateResult<string> ReadCurrentForegroundDir()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0xB0, 1, 0, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];

            OperateResult checkResult = CheckSingleResultLeagle(result);
            if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<string>(checkResult);

            return OperateResult.CreateSuccessResult(result.GetStringOrEndChar(14, result.Length - 14, this.encoding));
        }

        /// <summary>
        /// 读取指定路径下的所有的子路径和文件的信息，路径信息，例如 "//CNC_MEM/USER/"
        /// </summary>
        /// <param name="path">路径信息，例如 "//CNC_MEM/USER/"</param>
        /// <returns>文件及路径信息</returns>
        public OperateResult<FileDirInfo[]> ReadAllDirectoryAndFile(string path)
        {
            if (!path.EndsWith("/")) path += "/";

            byte[] buffer = new byte[256];
            Encoding.ASCII.GetBytes(path).CopyTo(buffer, 0);

            // 先获取文件数量信息
            OperateResult<int> readCount = ReadAllDirectoryAndFileCount(path);
            if (!readCount.IsSuccess) return OperateResult.CreateFailedResult<FileDirInfo[]>(readCount);
            if (readCount.Content == 0) return OperateResult.CreateSuccessResult(new FileDirInfo[0]);

            // 分割成20的长度读取
            int[] splits = SoftBasic.SplitIntegerToArray(readCount.Content, 0x14);
            List<FileDirInfo> list = new List<FileDirInfo>();

            int already = 0;
            for (int j = 0; j < splits.Length; j++)
            {
                // a:req_num, b:num_prog, c:type, d:size_kind
                OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(BuildWriteSingle(0x01, 0xB3, already, splits[j], 1, 1, buffer)));
                if (!read.IsSuccess) return OperateResult.CreateFailedResult<FileDirInfo[]>(read);

                if (read.Content.Length == 0x12 || this.ByteTransform.TransInt16(read.Content, 10) == 0)
                {
                    read = ReadFromCoreServer(BuildReadArray(BuildWriteSingle(0x01, 0xB3, 0, 0x14, 1, 1, buffer)));
                    if (!read.IsSuccess) return OperateResult.CreateFailedResult<FileDirInfo[]>(read);
                }

                byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];

                OperateResult checkResult = CheckSingleResultLeagle(result);
                if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<FileDirInfo[]>(checkResult);

                int count = (result.Length - 14) / 128;
                for (int i = 0; i < count; i++)
                {
                    list.Add(new FileDirInfo(this.ByteTransform, result, 14 + 128 * i));
                }
                already += splits[j];
            }
            return OperateResult.CreateSuccessResult(list.ToArray());
        }

        /// <summary>
        /// 获取指定的路径里所有的文件夹数量和文件数量之和，路径示例：例如 "//CNC_MEM/USER/"， "//CNC_MEM/USER/PATH1/"
        /// </summary>
        /// <param name="path">路径信息，例如 "//CNC_MEM/USER/"</param>
        /// <returns>文件夹数量和文件数量之和</returns>
        public OperateResult<int> ReadAllDirectoryAndFileCount(string path)
        {
            if (!path.EndsWith("/")) path += "/";

            byte[] buffer = new byte[256];
            Encoding.ASCII.GetBytes(path).CopyTo(buffer, 0);

            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(BuildWriteSingle(0x01, 0xB4, 0, 0, 0, 0, buffer)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];

            OperateResult checkResult = CheckSingleResultLeagle(result);
            if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<int>(checkResult);

            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 14) + ByteTransform.TransInt32(result, 18));
        }

        /// <summary>
        /// 设置指定路径为当前路径<br />
        /// Set the specified path as the current path
        /// </summary>
        /// <param name="programName">程序名</param>
        /// <returns>结果信息</returns>
        public OperateResult SetDeviceProgsCurr(string programName)
        {
            OperateResult<string> path = ReadCurrentForegroundDir();
            if (!path.IsSuccess) return path;

            byte[] buffer = new byte[256];
            Encoding.ASCII.GetBytes(path.Content + programName).CopyTo(buffer, 0);

            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(BuildWriteSingle(0x01, 0xBA, 0, 0, 0, 0, buffer)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            return CheckSingleResultLeagle(result);
        }
        /// <summary>
        /// 读取机床的当前时间信息
        /// </summary>
        /// <returns>时间信息</returns>
        public OperateResult<DateTime> ReadCurrentDateTime()
        {
            OperateResult<double> read1 = ReadSystemMacroValue(3011);
            if (!read1.IsSuccess) return OperateResult.CreateFailedResult<DateTime>(read1);

            OperateResult<double> read2 = ReadSystemMacroValue(3012);
            if (!read2.IsSuccess) return OperateResult.CreateFailedResult<DateTime>(read2);

            string date = Convert.ToInt32(read1.Content).ToString();
            string time = Convert.ToInt32(read2.Content).ToString().PadLeft(6, '0');

            return OperateResult.CreateSuccessResult(new DateTime(
                int.Parse(date.Substring(0, 4)), int.Parse(date.Substring(4, 2)), int.Parse(date.Substring(6)),
                int.Parse(time.Substring(0, 2)), int.Parse(time.Substring(2, 2)), int.Parse(time.Substring(4))));
        }

        /// <summary>
        /// 读取当前的已加工的零件数量
        /// </summary>
        /// <returns>已经加工的零件数量</returns>
        public OperateResult<int> ReadCurrentProduceCount()
        {
            OperateResult<double> read = ReadSystemMacroValue(3901);
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            return OperateResult.CreateSuccessResult(Convert.ToInt32(read.Content));
        }

        /// <summary>
        /// 读取期望的加工的零件数量
        /// </summary>
        /// <returns>期望的加工的零件数量</returns>
        public OperateResult<int> ReadExpectProduceCount()
        {
            OperateResult<double> read = ReadSystemMacroValue(3902);
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            return OperateResult.CreateSuccessResult(Convert.ToInt32(read.Content));
        }


        /// <summary>
        /// 读取机床的操作信息<br />
        /// Read machine operation information
        /// </summary>
        /// <returns>操作信息列表</returns>
        public OperateResult<FanucOperatorMessage[]> ReadOperatorMessage()
        {
            OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0x34, 0, 0, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<FanucOperatorMessage[]>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];

            OperateResult checkResult = CheckSingleResultLeagle(result);
            if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<FanucOperatorMessage[]>(checkResult);

            List<FanucOperatorMessage> list = new List<FanucOperatorMessage>();
            int index = 12;
            while (true)
            {
                ushort len = this.ByteTransform.TransUInt16(result, index);
                list.Add(FanucOperatorMessage.CreateMessage(this.ByteTransform, result.SelectMiddle(index + 2, len), this.encoding));

                index += 2;
                index += len;
                if (index >= result.Length) break;
            }
            return OperateResult.CreateSuccessResult(list.ToArray());
        }

        #endregion


        #region Async Read Write Support
#if !NET20 && !NET35
        /// <inheritdoc cref="ReadSpindleSpeedAndFeedRate"/>
        public async Task<OperateResult<double, double>> ReadSpindleSpeedAndFeedRateAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0xA4, 3, 0, 0, 0, 0),
                BuildReadSingle(0x8A, 1, 0, 0, 0, 0),
                BuildReadSingle(0x88, 3, 0, 0, 0, 0),
                BuildReadSingle(0x88, 4, 0, 0, 0, 0),
                BuildReadSingle(0x24, 0, 0, 0, 0, 0),
                BuildReadSingle(0x25, 0, 0, 0, 0, 0),
                BuildReadSingle(0xA4, 3, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double, double>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            return OperateResult.CreateSuccessResult(GetFanucDouble(result[5], 14), GetFanucDouble(result[4], 14));
        }

        /// <inheritdoc cref="ReadSystemProgramCurrent"/>
        public async Task<OperateResult<int,int>> ReadSystemProgramCurrentAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(BuildReadSingle(0x1C, 0, 0, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, int>(read);
            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            OperateResult check = CheckSingleResultLeagle(result);
            if (!check.IsSuccess) return OperateResult.CreateFailedResult<int, int>(check);
            int mainpro = ByteTransform.TransInt32(result, 14);
            int currpro = ByteTransform.TransInt32(result, 18);
            return OperateResult.CreateSuccessResult(mainpro, currpro);
        }

        public async Task<OperateResult<int>> ReadProgramNumberAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x1d, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(28);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
        }

        /// <inheritdoc cref="ReadLanguage"/>
        public async Task<OperateResult<ushort>> ReadLanguageAsync()
        {
           OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
               BuildReadSingle(0x8D, 0x0CD1, 0x0CD1, 0, 0, 0)
           ));
                       if (!read.IsSuccess) return OperateResult.CreateFailedResult<ushort>(read);
                       List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
                       ushort code = result[0].Length >= 24 ? ByteTransform.TransUInt16(result[0], 24) : (ushort)0;
                       ChangeTextEncoding(code);
                       return OperateResult.CreateSuccessResult(code);
        }
        
        /// <inheritdoc cref="ReadSystemMacroValue(int)"/>
        public async Task<OperateResult<double>> ReadSystemMacroValueAsync(int number)
        {
            return ByteTransformHelper.GetResultFromArray(await ReadSystemMacroValueAsync(number, 1));
        }

        /// <inheritdoc cref="ReadSystemMacroValue(int, int)"/>
        public async Task<OperateResult<double[]>> ReadSystemMacroValueAsync(int number, int length)
        {
            // 拆分5个5个读
            int[] lenArray = SoftBasic.SplitIntegerToArray(length, 5);
            int index = number;
            List<byte> result = new List<byte>();

            for (int i = 0; i < lenArray.Length; i++)
            {
                OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                    BuildReadSingle(0x15, index, index + lenArray[i] - 1, 0, 0, 0)));
                if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);

                result.AddRange(ExtraContentArray(read.Content.RemoveBegin(10))[0].RemoveBegin(14));
                index += lenArray[i];
            }

            try
            {
                return OperateResult.CreateSuccessResult(GetFanucDouble(result.ToArray(), 0, length));
            }
            catch (Exception ex)
            {
                return new OperateResult<double[]>(ex.Message + " Source:" + result.ToArray().ToHexString(' '));
            }
        }

        /// <inheritdoc cref="ReadCutterNumber"/>
        public async Task<OperateResult<int>> ReadToolnumAsync()
        {
            OperateResult<double[]> read = await ReadSystemMacroValueAsync(4120, 1);
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            return OperateResult.CreateSuccessResult(Convert.ToInt32(read.Content[0]));
        }



        /// <summary>
        /// 读取进给倍率,Fanuc进给倍率需要255-读取值
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<int>> ReadFeedRateAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                   BuildReadMulti(mode: 0x02, code: 0x8001, 12, 13, 0, 1, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            OperateResult check = CheckSingleResultLeagle(result);
            if (!check.IsSuccess) return OperateResult.CreateFailedResult<int>(check);
            result[14] = 0x00;//特殊情况做下处理
            return OperateResult.CreateSuccessResult(255 - this.ByteTransform.TransUInt16(result, 14));
        }
        /// <summary>
        /// 读取主轴倍率
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<int>> ReadSpindleRateAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadMulti(mode: 0x02, code: 0x8001, 30, 31, 0, 1, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            OperateResult check = CheckSingleResultLeagle(result);
            if (!check.IsSuccess) return OperateResult.CreateFailedResult<int>(check);
            return OperateResult.CreateSuccessResult((int)this.ByteTransform.TransUInt16(result, 14));
        }
        /// <summary>
        /// 读取进给速度
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<int>> ReadActfSpeedAsync()
        {
            // OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadActfSpeed());

            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x24, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(28);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
        }
        /// <summary>
        /// 读取进给速度设定
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<int>> ReadSetfSpeedAsync()
        {
           
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x15, 0x10d5, 0x10d5, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(28);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0)/1000);
        }

        /// <summary>
        /// 读取主轴温度
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<int>> ReadStemperAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                       BuildReadSingle(0x30, 0x0193, 0x0193, 1, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(36);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
        }

        /// <summary>
        /// 异步读取伺服温度
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<int>> ReadFtemperAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                       BuildReadSingle(0x30, 0x0134, 0x0134, 1, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            byte[] result = read.Content.RemoveBegin(36);
            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
        }

        /// <summary>
        /// 读取主轴负载
        /// </summary>
        /// <returns>主轴负载</returns>
        public async Task<OperateResult<double>> ReadSpindleLoadAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x40, 4, -1, 0, 0, 0),
                BuildReadSingle(0x40, 5, -1, 0, 0, 0),
                BuildReadSingle(0x8A, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            if (result[0].Length >= 18) return OperateResult.CreateSuccessResult(GetFanucDouble(result[0], 14));
            return new OperateResult<double>("Read failed, data is too short: " + result[0].ToHexString(' '));
        }
        
        /// <summary>
        /// 读取进给轴负载
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<double[]>> ReadFanucAxisLoadAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0xA4, 2, 0, 0, 0, 0),
                BuildReadSingle(0x89, 0, 0, 0, 0, 0),
                BuildReadSingle(0x56, 1, 0, 0, 0, 0),
                BuildReadSingle(0xA4, 2, 0, 0, 0, 0)
            ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            int length = -1;
            if (result[0].Length >= 16) length = ByteTransform.TransUInt16(result[0], 14);
            if (length < 0 || length * 8 + 14 > result[2].Length) length = (result[2].Length - 14) / 8;

            double[] array = GetFanucDouble(result[2], 14, length); 
            for (int i = 0; i < fanucSysInfo.Axes; i++)
            { 
                if (array[i] < 0) array[i] =-array[i]; 
            }

            return OperateResult.CreateSuccessResult(array.ToArray());
        }
        
        /// <inheritdoc cref="WriteSystemMacroValue(int, double[])"/>
        public async Task<OperateResult> WriteSystemMacroValueAsync(int number, double[] values)
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildWriteSingle(0x16, number, number + values.Length - 1, 0, 0, values)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            if (ByteTransform.TransUInt16(result[0], 6) == 0)
            {
                return OperateResult.CreateSuccessResult();
            }
            else
            {
                return new OperateResult(ByteTransform.TransUInt16(result[0], 6), "Unknown Error");
            }
        }

        /// <inheritdoc cref="WriteCutterLengthShapeOffset(int, double)"/>
        public async Task<OperateResult> WriteCutterLengthSharpOffsetAsync(int cutter, double offset) => await WriteSystemMacroValueAsync(11000 + cutter, new double[] { offset });

        /// <inheritdoc cref="WriteCutterLengthWearOffset(int, double)"/>
        public async Task<OperateResult> WriteCutterLengthWearOffsetAsync(int cutter, double offset) => await WriteSystemMacroValueAsync(10000 + cutter, new double[] { offset });

        /// <inheritdoc cref="WriteCutterRadiusShapeOffset(int, double)"/>
        public async Task<OperateResult> WriteCutterRadiusSharpOffsetAsync(int cutter, double offset) => await WriteSystemMacroValueAsync(13000 + cutter, new double[] { offset });

        /// <inheritdoc cref="WriteCutterRadiusWearOffset(int, double)"/>
        public async Task<OperateResult> WriteCutterRadiusWearOffsetAsync(int cutter, double offset) => await WriteSystemMacroValueAsync(12000 + cutter, new double[] { offset });

      


        /// <inheritdoc cref="ReadSysAllCoors"/>
        public async Task<OperateResult<SysAllCoors>> ReadSysAllCoorsAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x19, 0, 0, 0, 0, 0),
                BuildReadSingle(0x26, 0, -1, 0, 0, 0),   //4个轴  轴0 X  轴1 Y  轴2 Z 轴3 B
                BuildReadSingle(0x26, 1, -1, 0, 0, 0),
                BuildReadSingle(0x26, 2, -1, 0, 0, 0),
                BuildReadSingle(0x26, 3, -1, 0, 0, 0),
                BuildReadSingle(0x89, -1, 0, 0, 0, 0),
                BuildReadSingle(0x0e, 0x0c2b, 0x0c2b, -1, 0, 0),
                BuildReadSingle(0x88, 2, 0, 0, 0, 0),
                BuildReadSingle(0x19, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysAllCoors>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            int length = fanucSysInfo.Axes; //ByteTransform.TransUInt16(result[0], 14);

            SysAllCoors allCoors = new SysAllCoors();

            allCoors.Absolute = GetFanucDouble(result[1], 14, length);
            allCoors.Machine = GetFanucDouble(result[2], 14, length);
            allCoors.Relative = GetFanucDouble(result[3], 14, length);
            allCoors.Remaining = GetFanucDouble(result[4], 14, length);
            
            // 先确保系统信息已读取
            if (fanucSysInfo != null)
            {
                // 根据系统信息中的轴数量来截取相应数量的坐标
                int axesCount = fanucSysInfo.Axes;
                allCoors.Absolute = allCoors.Absolute.Take(axesCount).ToArray();
                allCoors.Machine = allCoors.Machine.Take(axesCount).ToArray();
                allCoors.Relative = allCoors.Relative.Take(axesCount).ToArray();
                allCoors.Remaining = allCoors.Remaining.Take(axesCount).ToArray();
            }
            return OperateResult.CreateSuccessResult(allCoors);
        }


        /// <inheritdoc cref="ReadSystemAlarm"/>
        public async Task<OperateResult<SysAlarm[]>> ReadSystemAlarmAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x23, -1, 10, 2, 64, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysAlarm[]>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            if (ByteTransform.TransUInt16(result[0], 12) > 0)
            {
                int length = ByteTransform.TransUInt16(result[0], 12) / 80;
                SysAlarm[] alarms = new SysAlarm[length];
                for (int i = 0; i < alarms.Length; i++)
                {
                    alarms[i] = new SysAlarm();
                    alarms[i].AlarmId = ByteTransform.TransInt32(result[0], 14 + 80 * i);
                    alarms[i].Type = ByteTransform.TransInt16(result[0], 20 + 80 * i);
                    alarms[i].Axis = ByteTransform.TransInt16(result[0], 24 + 80 * i);

                    ushort msgLength = ByteTransform.TransUInt16(result[0], 28 + 80 * i);
                    alarms[i].Message = this.encoding.GetString(result[0], 30 + 80 * i, msgLength);
                }
                return OperateResult.CreateSuccessResult(alarms);
            }
            else
                return OperateResult.CreateSuccessResult(new SysAlarm[0]);
        }

        /// <inheritdoc cref="ReadTimeData(int)"/>
        public async Task<OperateResult<long>> ReadTimeDataAsync(int timeType)
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x0e, timeType, timeType, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<long>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            OperateResult check = CheckSingleResultLeagle(result);
            if (!check.IsSuccess) return OperateResult.CreateFailedResult<long>(check);
            long time = ByteTransform.TransInt32(result, 22);
            return OperateResult.CreateSuccessResult(time);
        }

        /// <summary>
        /// 循环时间
        /// </summary>
        /// <returns></returns>
        public async Task<OperateResult<long>> ReadCycleTimeAsync()
        {
            OperateResult<long> read = await ReadTimeDataAsync(6758);
            long munite = read.Content;
            OperateResult<long> read1 = await ReadTimeDataAsync(6757);
            long second = read1.Content/1000;
            return OperateResult.CreateSuccessResult(munite*60+second);
        }

        /// <inheritdoc cref="ReadAlarmStatus"/>
        public async Task<OperateResult<int>> ReadAlarmStatusAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x1A, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            return OperateResult.CreateSuccessResult((int)ByteTransform.TransUInt16(result[0], 16));
        }

        /// <inheritdoc cref="ReadSysStatusInfo"/>
        public async Task<OperateResult<SysStatusInfo>> ReadSysStatusInfoAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x19, 0, 0, 0, 0, 0),
                BuildReadSingle(0xE1, 0, 0, 0, 0, 0),
                BuildReadSingle(0x98, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysStatusInfo>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            SysStatusInfo statusInfo = new SysStatusInfo();
            statusInfo.Dummy = result[2].Length >= 16 ? ByteTransform.TransInt16(result[1], 14) : (short)0;
            statusInfo.TMMode = result[2].Length >= 16 ? ByteTransform.TransInt16(result[2], 14) : (short)0;
            statusInfo.WorkMode = (CNCWorkMode)ByteTransform.TransInt16(result[0], 14);
            statusInfo.RunStatus = (CNCRunStatus)ByteTransform.TransInt16(result[0], 16);
            statusInfo.Motion = ByteTransform.TransInt16(result[0], 18);
            statusInfo.MSTB = ByteTransform.TransInt16(result[0], 20);
            statusInfo.Emergency = ByteTransform.TransInt16(result[0], 22);
            statusInfo.Alarm = ByteTransform.TransInt16(result[0], 24);
            statusInfo.Edit = ByteTransform.TransInt16(result[0], 26);

            return OperateResult.CreateSuccessResult(statusInfo);
        }

        public async Task<OperateResult<string[]>> ReadAxisNamesAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x89, 0, 0, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string[]>(read);

            return ParseAxisNames(read.Content);
        }

        /// <inheritdoc cref="ReadProgramList"/>
        public async Task<OperateResult<int[]>> ReadProgramListAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x06, 0x01, 0x13, 0, 0, 0)
                ));
            OperateResult<byte[]> check = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x06, 0x1A0B, 0x13, 0, 0, 0)
                ));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int[]>(read);
            if (!check.IsSuccess) return OperateResult.CreateFailedResult<int[]>(read);

            List<byte[]> result = ExtraContentArray(read.Content.RemoveBegin(10));
            int length = (result[0].Length - 14) / 72;
            int[] programs = new int[length];
            for (int i = 0; i < length; i++)
            {
                programs[i] = ByteTransform.TransInt32(result[0], 14 + 72 * i);
            }
            return OperateResult.CreateSuccessResult(programs);
        }

        /// <inheritdoc cref="ReadCutterInfos(int)"/>
        public async Task<OperateResult<CutterInfo[]>> ReadCutterInfosAsync(int cutterNumber = 24)
        {
            OperateResult<byte[]> read1 = await ReadFromCoreServerAsync(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 0, 0, 0)));
            if (!read1.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read1);

            OperateResult<byte[]> read2 = await ReadFromCoreServerAsync(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 1, 0, 0)));
            if (!read2.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read2);

            OperateResult<byte[]> read3 = await ReadFromCoreServerAsync(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 2, 0, 0)));
            if (!read3.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read3);

            OperateResult<byte[]> read4 = await ReadFromCoreServerAsync(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 3, 0, 0)));
            if (!read4.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read4);

            return ExtraCutterInfos(read1.Content, read2.Content, read3.Content, read4.Content, cutterNumber);
        }
        /// <inheritdoc cref="ReadData(int, int, int)"/>
        public async Task<OperateResult<byte[]>> ReadDataAsync(int code, int start, int end)
        {
            OperateResult<byte[]> read1 = await ReadFromCoreServerAsync(BuildReadArray(BuildReadMulti(0x02, 0x8001, start, end, code, 0, 0)));
            if (!read1.IsSuccess) return read1;

            byte[] result = ExtraContentArray(read1.Content.RemoveBegin(10))[0];

            OperateResult checkResult = CheckSingleResultLeagle(result);
            if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(checkResult);

            int length = this.ByteTransform.TransUInt16(result, 12);
            return OperateResult.CreateSuccessResult(result.SelectMiddle(14, length));
        }

        /// <inheritdoc cref="WriteData(int, int, byte[])"/>
        public async Task<OperateResult> WriteDataAsync(int code, int start, byte[] data)
        {
            if (data == null) data = new byte[0];
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(BuildWriteSingle(0x02, 0x8002, start, start + data.Length - 1, code, 0, data)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            return CheckSingleResultLeagle(result);
        }

        /// <inheritdoc cref="ReadPMCData(string, ushort)"/>
        public async Task<OperateResult<byte[]>> ReadPMCDataAsync(string address, ushort length)
        {
            OperateResult<FanucPMCAddress> analysis = FanucPMCAddress.ParseFrom(address, length);
            if (!analysis.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(analysis);

            return await ReadDataAsync(analysis.Content.DataCode, analysis.Content.AddressStart, analysis.Content.AddressEnd);
        }

        /// <inheritdoc cref="WritePMCData(string, byte[])"/>
        public async Task<OperateResult> WritePMCDataAsync(string address, byte[] value)
        {
            OperateResult<FanucPMCAddress> analysis = FanucPMCAddress.ParseFrom(address, 1);
            if (!analysis.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(analysis);

            return await WriteDataAsync(analysis.Content.DataCode, analysis.Content.AddressStart, value);
        }

        /// <inheritdoc cref="ReadDeviceWorkPiecesSize"/>
        public async Task<OperateResult<double[]>> ReadDeviceWorkPiecesSizeAsync() => await ReadSystemMacroValueAsync(601, 20);


        /// <inheritdoc cref="ReadCurrentForegroundDir"/>
        public async Task<OperateResult<string>> ReadCurrentForegroundDirAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(BuildReadSingle(0xB0, 1, 0, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];

            OperateResult checkResult = CheckSingleResultLeagle(result);
            if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<string>(checkResult);

            return OperateResult.CreateSuccessResult(result.GetStringOrEndChar(14, result.Length - 14, this.encoding));
        }

        /// <inheritdoc cref="ReadAllDirectoryAndFile(string)"/>
        public async Task<OperateResult<FileDirInfo[]>> ReadAllDirectoryAndFileAsync(string path)
        {
            if (!path.EndsWith("/")) path += "/";

            byte[] buffer = new byte[256];
            Encoding.ASCII.GetBytes(path).CopyTo(buffer, 0);

            // 先获取文件数量信息
            OperateResult<int> readCount = await ReadAllDirectoryAndFileCountAsync(path);
            if (!readCount.IsSuccess) return OperateResult.CreateFailedResult<FileDirInfo[]>(readCount);
            if (readCount.Content == 0) return OperateResult.CreateSuccessResult(new FileDirInfo[0]);

            // 分割成20的长度读取
            int[] splits = SoftBasic.SplitIntegerToArray(readCount.Content, 0x14);
            List<FileDirInfo> list = new List<FileDirInfo>();

            int already = 0;
            for (int j = 0; j < splits.Length; j++)
            {
                // a:req_num, b:num_prog, c:type, d:size_kind
                OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(BuildWriteSingle(0x01, 0xB3, already, splits[j], 1, 1, buffer)));
                if (!read.IsSuccess) return OperateResult.CreateFailedResult<FileDirInfo[]>(read);

                if (read.Content.Length == 0x12 || this.ByteTransform.TransInt16(read.Content, 10) == 0)
                {
                    read = ReadFromCoreServer(BuildReadArray(BuildWriteSingle(0x01, 0xB3, 0, 0x14, 1, 1, buffer)));
                    if (!read.IsSuccess) return OperateResult.CreateFailedResult<FileDirInfo[]>(read);
                }

                byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];

                OperateResult checkResult = CheckSingleResultLeagle(result);
                if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<FileDirInfo[]>(checkResult);

                int count = (result.Length - 14) / 128;
                for (int i = 0; i < count; i++)
                {
                    list.Add(new FileDirInfo(this.ByteTransform, result, 14 + 128 * i));
                }
                already += splits[j];
            }
            return OperateResult.CreateSuccessResult(list.ToArray());
        }

        /// <inheritdoc cref="ReadAllDirectoryAndFileCount(string)"/>
        public async Task<OperateResult<int>> ReadAllDirectoryAndFileCountAsync(string path)
        {
            if (!path.EndsWith("/")) path += "/";

            byte[] buffer = new byte[256];
            Encoding.ASCII.GetBytes(path).CopyTo(buffer, 0);

            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(BuildWriteSingle(0x01, 0xB4, 0, 0, 0, 0, buffer)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];

            OperateResult checkResult = CheckSingleResultLeagle(result);
            if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<int>(checkResult);

            return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 14) + ByteTransform.TransInt32(result, 18));
        }

        /// <inheritdoc cref="SetDeviceProgsCurr(string)"/>
        public async Task<OperateResult> SetDeviceProgsCurrAsync(string programName)
        {
            OperateResult<string> path = await ReadCurrentForegroundDirAsync();
            if (!path.IsSuccess) return path;

            byte[] buffer = new byte[256];
            Encoding.ASCII.GetBytes(path.Content + programName).CopyTo(buffer, 0);

            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(BuildWriteSingle(0x01, 0xBA, 0, 0, 0, 0, buffer)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            return CheckSingleResultLeagle(result);
        }

        /// <inheritdoc cref="ReadCurrentDateTime"/>
        public async Task<OperateResult<DateTime>> ReadCurrentDateTimeAsync()
        {
            OperateResult<double> read1 = await ReadSystemMacroValueAsync(3011);
            if (!read1.IsSuccess) return OperateResult.CreateFailedResult<DateTime>(read1);

            OperateResult<double> read2 = await ReadSystemMacroValueAsync(3012);
            if (!read2.IsSuccess) return OperateResult.CreateFailedResult<DateTime>(read2);

            string date = Convert.ToInt32(read1.Content).ToString();
            string time = Convert.ToInt32(read2.Content).ToString().PadLeft(6, '0');

            return OperateResult.CreateSuccessResult(new DateTime(
                int.Parse(date.Substring(0, 4)), int.Parse(date.Substring(4, 2)), int.Parse(date.Substring(6)),
                int.Parse(time.Substring(0, 2)), int.Parse(time.Substring(2, 2)), int.Parse(time.Substring(4))));
        }

        /// <inheritdoc cref="ReadCurrentProduceCount"/>
        public async Task<OperateResult<int>> ReadCurrentProduceCountAsync()
        {
            OperateResult<double> read = await ReadSystemMacroValueAsync(3901);
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            return OperateResult.CreateSuccessResult(Convert.ToInt32(read.Content));
        }

        /// <inheritdoc cref="ReadExpectProduceCount"/>
        public async Task<OperateResult<int>> ReadExpectProduceCountAsync()
        {
            OperateResult<double> read = await ReadSystemMacroValueAsync(3902);
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

            return OperateResult.CreateSuccessResult(Convert.ToInt32(read.Content));
        }

        /// <inheritdoc cref="ReadCurrentProgram"/>
        public async Task<OperateResult<string>> ReadCurrentProgramAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(
                BuildReadArray(BuildReadSingle(0x20, 0x0594, 0x00, 0x00, 0x00, 0x00)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            OperateResult checkResult = CheckSingleResultLeagle(result);
            if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<string>(checkResult);

            return OperateResult.CreateSuccessResult(Encoding.ASCII.GetString(result, 18, result.Length - 18));
        }

        /// <inheritdoc cref="SetCurrentProgram(ushort)"/>
        public async Task<OperateResult> SetCurrentProgramAsync(ushort programNum)
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(
                BuildReadArray(BuildReadSingle(0x03, programNum, 0, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, string>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            return CheckSingleResultLeagle(result);
        }

        /// <inheritdoc cref="StartProcessing"/>
        public async Task<OperateResult> StartProcessingAsync()
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadArray(BuildReadSingle(0x01, 0, 0, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, string>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            return CheckSingleResultLeagle(result);
        }

        /// <inheritdoc cref="WriteProgramFile(string)"/>
        public async Task<OperateResult> WriteProgramFileAsync(string file)
        {
            string content = File.ReadAllText(file);
            return await WriteProgramContentAsync(content);
        }

        /// <inheritdoc cref="WriteProgramContent(string, int, string)"/>
        public async Task<OperateResult> WriteProgramContentAsync(string program, int everyWriteSize = 512, string path = "")
        {

            OperateResult<Socket> socket = await CreateSocketAndConnectAsync(IpAddress, Port, ConnectTimeOut);
            if (!socket.IsSuccess) return socket.ConvertFailed<int>();

            OperateResult<byte[]> ini1 = await ReadFromCoreServerAsync(socket.Content, "a0 a0 a0 a0 00 01 01 01 00 02 00 01".ToHexBytes());
            if (!ini1.IsSuccess) return ini1;

            OperateResult<byte[]> read1 = await ReadFromCoreServerAsync(socket.Content, BulidWriteProgramFilePre(path));
            if (!read1.IsSuccess) return read1;

            List<byte[]> contents = BulidWriteProgram(Encoding.ASCII.GetBytes(program), everyWriteSize);
            for (int i = 0; i < contents.Count; i++)
            {
                OperateResult<byte[]> read2 = await ReadFromCoreServerAsync(socket.Content, contents[i], false);
                if (!read2.IsSuccess) return read2;
            }

            OperateResult<byte[]> read3 = await ReadFromCoreServerAsync(socket.Content, new byte[] { 0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x13, 0x01, 0x00, 0x00 });
            if (!read3.IsSuccess) return read3;

            socket.Content?.Close();
            if (read3.Content.Length >= 14)
            {
                int err = this.ByteTransform.TransInt16(read3.Content, 12);
                if (err != 0) return new OperateResult<string>(err, StringResources.Language.UnknownError);
            }

            return OperateResult.CreateSuccessResult();
        }

        /// <inheritdoc cref="ReadProgram(int, string)"/>
        public async Task<OperateResult<string>> ReadProgramAsync(int program, string path = "")
        {

            OperateResult<Socket> socket = await CreateSocketAndConnectAsync(IpAddress, Port, ConnectTimeOut);
            if (!socket.IsSuccess) return socket.ConvertFailed<string>();

            OperateResult<byte[]> ini1 = await ReadFromCoreServerAsync(socket.Content, "a0 a0 a0 a0 00 01 01 01 00 02 00 01".ToHexBytes());
            if (!ini1.IsSuccess) return OperateResult.CreateFailedResult<string>(ini1);

            OperateResult<byte[]> read1 = await ReadFromCoreServerAsync(socket.Content, BuildReadProgramPre(program, path));
            if (!read1.IsSuccess) return OperateResult.CreateFailedResult<string>(read1);

            // 检测错误信息
            int err = read1.Content[12] * 256 + read1.Content[13];
            if (err != 0)
            {
                socket.Content?.Close();
                return new OperateResult<string>(err, StringResources.Language.UnknownError);
            }

            StringBuilder sb = new StringBuilder();
            while (true)
            {
                OperateResult<byte[]> read2 = await ReadFromCoreServerAsync(socket.Content, null);
                if (!read2.IsSuccess) return OperateResult.CreateFailedResult<string>(read2);

                if (read2.Content[6] == 0x16)
                    sb.Append(Encoding.ASCII.GetString(read2.Content, 10, read2.Content.Length - 10));
                else if (read2.Content[6] == 0x17)
                    break;
            }

            //OperateResult<byte[]> read3 = await ReadFromCoreServerAsync( socket.Content, null );
            //if (!read3.IsSuccess) return OperateResult.CreateFailedResult<string>( read3 );

            OperateResult send = await SendAsync(socket.Content, new byte[] { 0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x17, 0x02, 0x00, 0x00 });
            //if (!send.IsSuccess) return OperateResult.CreateFailedResult<string>( send );

            socket.Content?.Close();
            return OperateResult.CreateSuccessResult(sb.ToString());
        }

        /// <inheritdoc cref="DeleteProgram(int)"/>
        public async Task<OperateResult> DeleteProgramAsync(int program)
        {
            OperateResult<byte[]> read = await ReadFromCoreServerAsync(
                BuildReadArray(BuildReadSingle(0x05, program, 0, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, string>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            return CheckSingleResultLeagle(result);
        }

        /// <inheritdoc cref="DeleteFile(string)"/>
        public async Task<OperateResult> DeleteFileAsync(string fileName)
        {
            byte[] buffer = new byte[256];
            Encoding.ASCII.GetBytes(fileName).CopyTo(buffer, 0);

            OperateResult<byte[]> read = await ReadFromCoreServerAsync(
                BuildReadArray(BuildWriteSingle(0x01, 0xB6, 0, 0, 0, 0, buffer)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

            byte[] result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
            return CheckSingleResultLeagle(result);
        }

#endif
        #endregion



        #region Build Command

        /// <summary>
        /// 构建读取一个命令的数据内容
        /// </summary>
        /// <param name="code">命令码</param>
        /// <param name="a">第一个参数内容</param>
        /// <param name="b">第二个参数内容</param>
        /// <param name="c">第三个参数内容</param>
        /// <param name="d">第四个参数内容</param>
        /// <param name="e">第五个参数内容</param>
        /// <returns>总报文信息</returns>
        public byte[] BuildReadSingle(ushort code, int a, int b, int c, int d, int e)
        {
            byte[] buffer = new byte[28];
            buffer[1] = 0x1C;
            buffer[3] = 0x01;
            buffer[5] = 0x01; //通道号
            byte[] f = this.ByteTransform.TransByte(code);
            f.CopyTo(buffer, 6);
            this.ByteTransform.TransByte(a).CopyTo(buffer, 8);
            this.ByteTransform.TransByte(b).CopyTo(buffer, 12);
            this.ByteTransform.TransByte(c).CopyTo(buffer, 16);
            this.ByteTransform.TransByte(d).CopyTo(buffer, 20);
            this.ByteTransform.TransByte(e).CopyTo(buffer, 24);
            return buffer;
        }
        /// <summary>
        /// 构建读取多个命令的数据内容
        /// </summary>
        /// <param name="mode">模式</param>
        /// <param name="code">命令码</param>
        /// <param name="a">第一个参数内容</param>
        /// <param name="b">第二个参数内容</param>
        /// <param name="c">第三个参数内容</param>
        /// <param name="d">第四个参数内容</param>
        /// <param name="e">第五个参数内容</param>
        /// <returns>总报文信息</returns>
        private byte[] BuildReadMulti(ushort mode, ushort code, int a, int b, int c, int d, int e)
        {
            byte[] buffer = new byte[28];
            this.ByteTransform.TransByte((ushort)buffer.Length).CopyTo(buffer, 0);
            this.ByteTransform.TransByte(mode).CopyTo(buffer, 2);
            this.ByteTransform.TransByte(opPath).CopyTo(buffer, 4);
            this.ByteTransform.TransByte(code).CopyTo(buffer, 6);
            this.ByteTransform.TransByte(a).CopyTo(buffer, 8);
            this.ByteTransform.TransByte(b).CopyTo(buffer, 12);
            this.ByteTransform.TransByte(c).CopyTo(buffer, 16);
            this.ByteTransform.TransByte(d).CopyTo(buffer, 20);
            this.ByteTransform.TransByte(e).CopyTo(buffer, 24);
            return buffer;
        }

        /// <summary>
        /// 创建写入byte[]数组的报文信息
        /// </summary>
        /// <param name="mode">模式</param>
        /// <param name="code">命令码</param>
        /// <param name="a">第一个参数内容</param>
        /// <param name="b">第二个参数内容</param>
        /// <param name="c">第三个参数内容</param>
        /// <param name="d">第四个参数内容</param>
        /// <param name="data">等待写入的byte数组信息</param>
        /// <returns>总报文信息</returns>
        private byte[] BuildWriteSingle(ushort mode, ushort code, int a, int b, int c, int d, byte[] data)
        {
            byte[] buffer = new byte[28 + data.Length];
            this.ByteTransform.TransByte((ushort)buffer.Length).CopyTo(buffer, 0);
            this.ByteTransform.TransByte(mode).CopyTo(buffer, 2);
            buffer[5] = 0x01;
            this.ByteTransform.TransByte(code).CopyTo(buffer, 6);
            this.ByteTransform.TransByte(a).CopyTo(buffer, 8);
            this.ByteTransform.TransByte(b).CopyTo(buffer, 12);
            this.ByteTransform.TransByte(c).CopyTo(buffer, 16);
            this.ByteTransform.TransByte(d).CopyTo(buffer, 20);
            this.ByteTransform.TransByte(data.Length).CopyTo(buffer, 24);
            if (data.Length > 0) data.CopyTo(buffer, 28);
            return buffer;
        }
        public byte[] BuildWriteByte(ushort code, int a, int b, int c, int d, byte[] data)
        {
            byte[] buffer = new byte[28 + data.Length];
            this.ByteTransform.TransByte((ushort)buffer.Length).CopyTo(buffer, 0);
            buffer[3] = 0x02;
            buffer[5] = 0x01;
            this.ByteTransform.TransByte(code).CopyTo(buffer, 6);
            this.ByteTransform.TransByte(a).CopyTo(buffer, 8);
            this.ByteTransform.TransByte(b).CopyTo(buffer, 12);
            this.ByteTransform.TransByte(c).CopyTo(buffer, 16);
            this.ByteTransform.TransByte(d).CopyTo(buffer, 20);
            this.ByteTransform.TransByte(data.Length).CopyTo(buffer, 24);
            if (data.Length > 0) data.CopyTo(buffer, 28);
            return buffer;
        }

        /// <summary>
        /// 创建写入单个double数组的报文信息
        /// </summary>
        /// <param name="code">功能码</param>
        /// <param name="a">第一个参数内容</param>
        /// <param name="b">第二个参数内容</param>
        /// <param name="c">第三个参数内容</param>
        /// <param name="d">第四个参数内容</param>
        /// <param name="data">等待写入的double数组信息</param>
        /// <returns>总报文信息</returns>
        private byte[] BuildWriteSingle(ushort code, int a, int b, int c, int d, double[] data)
        {
            byte[] buffer = new byte[data.Length * 8];
            for (int i = 0; i < data.Length; i++)
            {
                CreateFromFanucDouble(data[i]).CopyTo(buffer, 0);
            }
            return BuildWriteSingle(0x01, code, a, b, c, d, buffer);
        }


        //private byte[] BulidWriteProgramFilePre()
        //{
        //    MemoryStream ms = new MemoryStream();
        //    ms.Write(new byte[] { 0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x11, 0x01, 0x02, 0x04 }, 0, 10);
        //    ms.Write(new byte[] { 0x00, 0x00, 0x00, 0x01 }, 0, 4);
        //    for (int i = 0; i < 512; i++)
        //    {
        //        ms.WriteByte(0x00);
        //    }
        //    return ms.ToArray();
        //}
        private byte[] BulidWriteProgramFilePre(string path)
        {
            if (!string.IsNullOrEmpty(path))
            {
                if (!path.EndsWith("/")) path += "/";
                if (!path.StartsWith("N:")) path = "N:" + path;
            }

            MemoryStream ms = new MemoryStream();
            ms.Write(new byte[] { 0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x11, 0x01, 0x02, 0x04 }, 0, 10);
            ms.Write(new byte[] { 0x00, 0x00, 0x00, 0x01 }, 0, 4);
            ms.Write(new byte[512], 0, 512);

            byte[] buffer = ms.ToArray();
            if (!string.IsNullOrEmpty(path))
            {
                Encoding.ASCII.GetBytes(path).CopyTo(buffer, 14);
            }

            return buffer;
        }

        /// <summary>
        /// 创建读取运行程序的报文信息
        /// </summary>
        /// <param name="program">程序号</param>
        /// <param name="path">程序路径信息</param>
        /// <returns>总报文</returns>
        private byte[] BuildReadProgramPre(int program, string path = "")
        {
            if (!string.IsNullOrEmpty(path))
            {
                if (!path.EndsWith("/")) path += "/";
                if (!path.StartsWith("N:")) path = "N:" + path;
            }

            MemoryStream ms = new MemoryStream();
            ms.Write(new byte[] { 0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x15, 0x01, 0x02, 0x04 }, 0, 10);
            ms.Write(new byte[] { 0x00, 0x00, 0x00, 0x01 }, 0, 4);
            ms.Write(new byte[512], 0, 512);
            byte[] buffer = ms.ToArray();
            string pro = string.IsNullOrEmpty(path) ? ("O" + program + "-" + "O" + program) : path + "O" + program;
            Encoding.ASCII.GetBytes(pro).CopyTo(buffer, 14);
            return buffer;
        }

        private List<byte[]> BulidWriteProgram(byte[] program, int everyWriteSize)
        {
            List<byte[]> list = new List<byte[]>();
            int[] lengths = SoftBasic.SplitIntegerToArray(program.Length, everyWriteSize);
            int index = 0;
            for (int i = 0; i < lengths.Length; i++)
            {
                MemoryStream ms = new MemoryStream();
                ms.Write(new byte[] { 0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x12, 0x04, 0x00, 0x00 }, 0, 10);
                ms.Write(program, index, lengths[i]);
                byte[] buffer = ms.ToArray();
                this.ByteTransform.TransByte((ushort)(buffer.Length - 10)).CopyTo(buffer, 8);

                list.Add(buffer);
                index += lengths[i];
            }
            return list;
        }



        /// <summary>
        /// 产量清零报文
        /// </summary>
        /// <returns></returns>
        public byte[] BuildProductZero()
        {
            byte[] buffer = @"
  0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01,
  0x01, 0x26, 0x00, 0x01, 0x01, 0x24, 0x00, 0x01,
  0x00, 0x01, 0x00, 0x8e, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08,
  0x00, 0x00, 0x1a, 0x37, 0x00, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff,
  0x00, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00,
  0x02, 0x00, 0x00, 0x00, 0xe8, 0xd9, 0xaf, 0x00,
  0x00, 0x01, 0x01, 0x01, 0x5c, 0xd9, 0xaf, 0x00,
  0x74, 0x09, 0xfe, 0x63, 0x28, 0xe2, 0x41, 0x05,
  0x02, 0x00, 0x00, 0x00, 0x18, 0xdb, 0xc8, 0x00,
  0x30, 0x09, 0xfe, 0x63, 0xe0, 0xd9, 0xaf, 0x00,
  0x84, 0x32, 0x09, 0x64, 0x28, 0xe2, 0x41, 0x05,
  0x02, 0x00, 0x00, 0x00, 0x80, 0xd9, 0xaf, 0x00,
  0x98, 0xd9, 0xaf, 0x00, 0x00, 0x09, 0xfe, 0x63,
  0xc8, 0xdc, 0xaf, 0x00, 0x00, 0xdc, 0xaf, 0x00,
  0xe0, 0xd9, 0xaf, 0x00, 0x68, 0xdc, 0xaf, 0x00,
  0x18, 0xdc, 0xaf, 0x00, 0xe0, 0xd9, 0xaf, 0x00,
  0x03, 0x0d, 0xfe, 0x63, 0x00, 0x00, 0x00, 0x00,
  0x02, 0x00, 0x00, 0x00, 0xa4, 0xd9, 0xaf, 0x00,
  0x97, 0xac, 0x07, 0x64, 0x00, 0xdc, 0xaf, 0x00,
  0x84, 0x00, 0x00, 0x00, 0xb8, 0xd9, 0xc8, 0x00,
  0x10, 0xfc, 0x38, 0x64, 0x1b, 0xfc, 0x38, 0x64,
  0xb8, 0xd9, 0xaf, 0x00, 0x5e, 0x06, 0x53, 0x64,
  0xe0, 0xd9, 0xaf, 0x00, 0xa8, 0x19, 0xca, 0x00,
  0xf4, 0x1c, 0xfd, 0x63, 0x01, 0x00, 0x00, 0x00,
  0x80, 0xdc, 0xaf, 0x00, 0xa8, 0x19, 0xca, 0x00,
  0xce, 0x07, 0xfe, 0x63, 0xb8, 0xd9, 0xc8, 0x00,
  0xe0, 0x07, 0xfe, 0x63, 0x00, 0x00, 0x00, 0x00,
  0xc7, 0xdc, 0xaf, 0x00, 0x20, 0x06, 0x53, 0x64,
  0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
  0x0d, 0x00, 0x00, 0x00, 0x60, 0x01, 0x00, 0x00,
  0x08, 0xda, 0xaf, 0x00, 0x1c, 0x00, 0x00, 0x00,
  0x98, 0xd9, 0xce, 0x00, 0x30, 0xb4, 0xfd, 0x00,
  0x5c, 0xda, 0xaf, 0x00, 0x38, 0x00, 0xee, 0x00,
  0x2c, 0xda, 0xaf, 0x00, 0xb7, 0x7e, 0x0b, 0x64,
  0x28, 0xe2, 0x41, 0x05, 0x02, 0x00, 0x00, 0x00
".ToHexBytes();

            return buffer;
        }

        /// <summary>
        /// 读取进给倍率报文
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadFeedRate()
        {
            byte[] buffer = @"
  0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01,
  0x00, 0x1e, 0x00, 0x01, 0x00, 0x1c, 0x00, 0x02,
  0x00, 0x01, 0x80, 0x01, 0x00, 0x00, 0x00, 0x0c,
  0x00, 0x00, 0x00, 0x0d, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00

".ToHexBytes();

            return buffer;
        }

        /// <summary>
        /// 读取主轴倍率报文
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadSpindleRate()
        {
            byte[] buffer = @"
                            0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01,
                            0x00, 0x1e, 0x00, 0x01, 0x00, 0x1c, 0x00, 0x02,
                            0x00, 0x01, 0x80, 0x01, 0x00, 0x00, 0x00, 0x1e,
                            0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
".ToHexBytes();

            return buffer;
        }
 
        /// <summary>
        /// 读取主轴电机温度报文
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadStemper()
        {
            byte[] buffer = @"
                            0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01,
                            0x00, 0x1e, 0x00, 0x01, 0x00, 0x1c, 0x00, 0x01,
                            0x00, 0x01, 0x00, 0x93, 0x00, 0x00, 0x01, 0x93,
                            0x00, 0x00, 0x01, 0x93, 0x00, 0x00, 0x00, 0x01,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00

                           ".ToHexBytes();

            return buffer;
        }
        /// <summary>
        /// 读取伺服电机温度报文
        /// </summary>
        /// <returns></returns>
        public byte[] BuildReadFtemper()
        {
            // 报文第4-8行从0x03->0x01
            byte[] buffer = @"
                             0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01,
                             0x00, 0x1e, 0x00, 0x01, 0x00, 0x1c, 0x00, 0x01,
                             0x00, 0x01, 0x00, 0x93, 0x00, 0x00, 0x01, 0x34,
                             0x00, 0x00, 0x01, 0x34, 0x00, 0x00, 0x00, 0x01,
                             0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
                           ".ToHexBytes();

            return buffer;
        }



        /// <summary>
        /// 创建多个命令报文的总报文信息
        /// </summary>
        /// <param name="commands">报文命令的数组</param>
        /// <returns>总报文信息</returns>
        public byte[] BuildReadArray(params byte[][] commands)
        {
            MemoryStream ms = new MemoryStream();
            ms.Write(new byte[] { 0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01, 0x00, 0x1e }, 0, 10);
            byte[] buffer2 = ms.ToArray();
            ms.Write(ByteTransform.TransByte((ushort)commands.Length), 0, 2);
            byte[] buffer1 = ms.ToArray();
            for (int i = 0; i < commands.Length; i++)
            {
                ms.Write(commands[i], 0, commands[i].Length);
            }
            byte[] buffer = ms.ToArray();
            this.ByteTransform.TransByte((ushort)(buffer.Length - 10)).CopyTo(buffer, 8);
            return buffer;
        }



        /// <summary>
        /// 从机床返回的数据里解析出实际的数据内容，去除了一些多余的信息报文。
        /// </summary>
        /// <param name="content">返回的报文信息</param>
        /// <returns>解析之后的报文信息</returns>
        public List<byte[]> ExtraContentArray(byte[] content)
        {
            List<byte[]> list = new List<byte[]>();
            int count = ByteTransform.TransUInt16(content, 0);
            int index = 2;

            for (int i = 0; i < count; i++)
            {
                ushort length = ByteTransform.TransUInt16(content, index);
                list.Add(content.SelectMiddle(index + 2, length - 2));
                index += length;
            }
            return list;
        }

        private OperateResult<CutterInfo[]> ExtraCutterInfos(byte[] content1, byte[] content2, byte[] content3, byte[] content4, int cutterNumber)
        {
            // 先提取出各个数据信息
            List<byte[]> result1 = ExtraContentArray(content1.RemoveBegin(10));
            List<byte[]> result2 = ExtraContentArray(content2.RemoveBegin(10));
            List<byte[]> result3 = ExtraContentArray(content3.RemoveBegin(10));
            List<byte[]> result4 = ExtraContentArray(content4.RemoveBegin(10));

            // 校验数据是否有效
            bool check1 = this.ByteTransform.TransInt16(result1[0], 6) == 0x00;
            bool check2 = this.ByteTransform.TransInt16(result2[0], 6) == 0x00;
            bool check3 = this.ByteTransform.TransInt16(result3[0], 6) == 0x00;
            bool check4 = this.ByteTransform.TransInt16(result4[0], 6) == 0x00;

            // 如果数据有效，则显示出来
            CutterInfo[] cutters = new CutterInfo[cutterNumber];
            for (int i = 0; i < cutters.Length; i++)
            {
                cutters[i] = new CutterInfo();
                cutters[i].LengthSharpOffset = check1 ? GetFanucDouble(result1[0], 14 + 8 * i) : double.NaN;
                cutters[i].LengthWearOffset = check2 ? GetFanucDouble(result2[0], 14 + 8 * i) : double.NaN;
                cutters[i].RadiusSharpOffset = check3 ? GetFanucDouble(result3[0], 14 + 8 * i) : double.NaN;
                cutters[i].RadiusWearOffset = check4 ? GetFanucDouble(result4[0], 14 + 8 * i) : double.NaN;
            }

            return OperateResult.CreateSuccessResult(cutters);
        }
        private OperateResult CheckSingleResultLeagle(byte[] result)
        {
            int status = result[6] * 256 + result[7];
            if (status != 0x00) return new OperateResult<int>(status, StringResources.Language.UnknownError);

            return OperateResult.CreateSuccessResult();
        }
        #endregion

        #region Private Member

        private Encoding encoding;
        private FanucSysInfo fanucSysInfo;
        private short opPath = 1;
        #endregion

    }
}
