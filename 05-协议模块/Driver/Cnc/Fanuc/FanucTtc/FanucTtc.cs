using System;
using System.Linq;
using System.Threading.Tasks;
using Common.Enums;
using Common.Extension;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Enum;
using Driver.Core.Models;
using DriversInterface;
using FanucTtc.Base;
using Feng.Common.Extension;
using Feng.Common.Util;
using Furion.JsonSerialization;
using HslCommunication;
using HslCommunication.CNC.Fanuc;

namespace FanucTtc;

[DriverSupported("FanucTtc")]
[DriverInfo("FanucTtc", "V1.1.0", "发那科(Fanuc)")]
public class FanucTtc : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 8193;

    #endregion

    public FanucTtc(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    /// </summary>
    public override bool IsConnected => Driver != null && Driver.GetConnectStatus();

    // private FanucTtcBase Driver;

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            Driver ??= new FanucTtcBase(IpAddress, Port)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout
            };
            OperateResult = Driver.ConnectServer();
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接失败:【{ex.Message}】"};
        }

        return ResConnect();
    }

    /// <summary>
    ///     读取模式,运行状态,报警状态
    /// </summary>
    private OperateResult<SysStatusInfo> _statusInfo;

    /// <summary>
    ///     读取程序名,程序号
    /// </summary>
    private OperateResult<string, int> _systemProgramCurrent;

    /// <summary>
    ///     读取绝对坐标,机械坐标,相对坐标
    /// </summary>
    private OperateResult<SysAllCoors> _sysAllCoors;

    /// <summary>
    ///     读取报警信息,报警Id,报警类型Id
    /// </summary>
    private OperateResult<SysAlarm[]> _systemAlarm;

    /// <summary>
    ///     清空数据
    /// </summary>
    /// <returns></returns>
    [Method("Clear", name: "Clear")]
    public Task Clear()
    {
        _statusInfo = null;
        _systemProgramCurrent = null;
        _sysAllCoors = null;
        _systemAlarm = null;
        return Task.CompletedTask;
    }

    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    [Method("sSpeed", TransPondDataTypeEnum.Double, name: "主轴转速")]
    public async Task<DriverReturnValueModel> ReadsSpeed()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Double
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSpindleSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #region 读取程序名,程序号

    /// <summary>
    ///     读取程序名,程序号
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSystemProgramCurrent(string identifier)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                _systemProgramCurrent ??= await Driver.ReadMainProgramAsync();
                if (!_systemProgramCurrent.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _systemProgramCurrent.Message;
                }
                else
                {
                    ret.Value = identifier switch
                    {
                        "MainName" => _systemProgramCurrent.Content1,
                        "CurPgm" => _systemProgramCurrent.Content2,
                        _ => ret.Value
                    };
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    [Method("MainName", TransPondDataTypeEnum.String, name: "程序名")]
    public async Task<DriverReturnValueModel> ReadMainName()
    {
        return await ReadSystemProgramCurrent("MainName");
    }

    /// <summary>
    ///     程序号
    /// </summary>
    /// <returns></returns>
    [Method("CurPgm", name: "程序号")]
    public async Task<DriverReturnValueModel> ReadCurPgm()
    {
        var output = await ReadSystemProgramCurrent("CurPgm");
        output.DataType = DataTypeEnum.Int32;
        return output;
    }

    #endregion

    #region 读取模式,运行状态,报警状态

    /// <summary>
    ///     读取模式,运行状态,报警状态
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> SysStatusInfo(string identifier)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _statusInfo ??= await Driver.ReadSysStatusInfoAsync();
                if (!_statusInfo.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _statusInfo.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "WorkMode":
                            ret.Value = Convert.ToInt32(_statusInfo.Content.WorkMode);
                            break;
                        case "RunStatus":
                            ret.Value = Convert.ToInt32(_statusInfo.Content.RunStatus);
                            break;
                        case "AlarmStatus":
                            ret.Value = _statusInfo.Content.Alarm;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     模式
    /// </summary>
    /// <returns></returns>
    [Method("WorkMode", description: "0:手动输入；1:自动循环；3:程序编辑；4:×100;5:连续进给；6:TeachInJog；7:TeachInHandle；8:InCfeed；9:ReFerence；10:ReMoTe", name: "模式")]
    public async Task<DriverReturnValueModel> ReadWorkMode()
    {
        return await SysStatusInfo("WorkMode");
    }

    /// <summary>
    ///     运行状态
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", description: "0:Reset；1:Stop；2:Hold；3:Start；4:Mstr；", name: "状态")]
    public async Task<DriverReturnValueModel> ReadRunStatus()
    {
        return await SysStatusInfo("RunStatus");
    }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    [Method("AlarmStatus", name: "报警状态")]
    public async Task<DriverReturnValueModel> ReadAlarmStatus()
    {
        return await SysStatusInfo("AlarmStatus");
    }

    #endregion

    #region 读取绝对坐标,机械坐标,相对坐标

    /// <summary>
    ///     读取绝对坐标,机械坐标,相对坐标
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSysAllCoorsAsync(string identifier)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                _sysAllCoors ??= await Driver.ReadSysAllCoorsAsync();
                if (!_sysAllCoors.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _sysAllCoors.Message;
                }
                else
                {
                    ret.Value = identifier switch
                    {
                        "AbsPos" => JSON.Serialize(_sysAllCoors.Content.Absolute),
                        "MachPos" => JSON.Serialize(_sysAllCoors.Content.Machine),
                        "RelPos" => JSON.Serialize(_sysAllCoors.Content.Relative),
                        _ => ret.Value
                    };
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     绝对坐标
    /// </summary>
    /// <returns></returns>
    [Method("AbsPos", TransPondDataTypeEnum.String, name: "绝对坐标")]
    public async Task<DriverReturnValueModel> ReadAbsPos()
    {
        return await ReadSysAllCoorsAsync("AbsPos");
    }

    /// <summary>
    ///     机械坐标
    /// </summary>
    /// <returns></returns>
    [Method("MachPos", TransPondDataTypeEnum.String, name: "机械坐标")]
    public async Task<DriverReturnValueModel> ReadMachPos()
    {
        return await ReadSysAllCoorsAsync("MachPos");
    }

    /// <summary>
    ///     相对坐标
    /// </summary>
    /// <returns></returns>
    [Method("RelPos", TransPondDataTypeEnum.String, name: "相对坐标")]
    public async Task<DriverReturnValueModel> ReadRelPos()
    {
        return await ReadSysAllCoorsAsync("MachPos");
    }

    #endregion

    #region 读取报警信息,报警Id,报警类型Id

    /// <summary>
    ///     读取报警信息,报警Id,报警类型Id
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSystemAlarmAsync(string identifier)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                _systemAlarm ??= await Driver.ReadSystemAlarmAsync();
                if (!_systemAlarm.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _systemAlarm.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "Alarm":
                            ret.Value = JSON.Serialize(_systemAlarm.Content);
                            break;
                        case "AlarmId":
                        {
                            var content = _systemAlarm.Content.FirstOrDefault();
                            ret.Value = content != null ? content.AlarmId.ToString() : "";
                            break;
                        }
                        case "AlarmType":
                        {
                            var typeVal = "";
                            foreach (var item in _systemAlarm.Content)
                                if (typeVal.IsNotNull())
                                    typeVal = typeVal + "," + EnumUtil.GetEnumDesc((SysAlarmType) item.Type) + item.AlarmId;
                                else
                                    typeVal = EnumUtil.GetEnumDesc((SysAlarmType) item.Type) + item.AlarmId;
                            ret.Value = typeVal;
                            break;
                        }
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     报警信息
    /// </summary>
    /// <returns></returns>
    [Method("Alarm", TransPondDataTypeEnum.String, name: "报警信息")]
    public async Task<DriverReturnValueModel> ReadAlarm()
    {
        return await ReadSystemAlarmAsync("Alarm");
    }

    /// <summary>
    ///     报警Id
    /// </summary>
    /// <returns></returns>
    [Method("AlarmId", name: "报警Id")]
    public async Task<DriverReturnValueModel> ReadAlarmId()
    {
        return await ReadSystemAlarmAsync("AlarmId");
    }

    /// <summary>
    ///     报警类型Id
    /// </summary>
    /// <returns></returns>
    [Method("AlarmType", name: "报警类型Id")]
    public async Task<DriverReturnValueModel> ReadAlarmType()
    {
        return await ReadSystemAlarmAsync("AlarmType");
    }

    #endregion

    /// <summary>
    ///     产量
    /// </summary>
    /// <returns></returns>
    [Method("CurrentNum", name: "产量")]
    public async Task<DriverReturnValueModel> ReadCurrentNum()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCurrentProduceCountAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴温度
    /// </summary>
    /// <returns></returns>
    [Method("SpinTemp", name: "主轴温度")]
    public async Task<DriverReturnValueModel> ReadSpinTemp()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadStemperAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     伺服温度
    /// </summary>
    /// <returns></returns>
    [Method("SvTemp", name: "伺服温度")]
    public async Task<DriverReturnValueModel> ReadSvTemp()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadFtemperAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     刀具号
    /// </summary>
    /// <returns></returns>
    [Method("ToolNum", name: "刀具号")]
    public async Task<DriverReturnValueModel> ReadToolNum()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadToolnumAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴倍率
    /// </summary>
    /// <returns></returns>
    [Method("OvSpin", name: "主轴倍率")]
    public async Task<DriverReturnValueModel> ReadOvSpin()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSpindleRateAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给速度
    /// </summary>
    /// <returns></returns>
    [Method("ActFeed", name: "进给速度")]
    public async Task<DriverReturnValueModel> ReadActFeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadActfSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     设定进给速度
    /// </summary>
    /// <returns></returns>
    [Method("SetFSpeed", name: "设定进给速度")]
    public async Task<DriverReturnValueModel> ReadSetFSpeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSetfSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     设定主轴速度
    /// </summary>
    /// <returns></returns>
    [Method("SetsSpeed", name: "设定主轴速度")]
    public async Task<DriverReturnValueModel> ReadSetsSpeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSetsSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给倍率
    /// </summary>
    /// <returns></returns>
    [Method("OvFeed", name: "进给倍率")]
    public async Task<DriverReturnValueModel> ReadOvFeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadFeedRateAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     伺服负载
    /// </summary>
    /// <returns></returns>
    [Method("AxisLoad", name: "伺服负载")]
    public async Task<DriverReturnValueModel> ReadAxisLoad()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadFanucAxisLoadAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     上电时间
    /// </summary>
    /// <returns></returns>
    [Method("AliveTime", name: "上电时间")]
    public async Task<DriverReturnValueModel> ReadAliveTime()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int64
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadAliveTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    [Method("CycleTime", name: "循环时间")]
    public async Task<DriverReturnValueModel> ReadCycleTime()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int64
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCycleTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     切削时间
    /// </summary>
    /// <returns></returns>
    [Method("CutTime", name: "切削时间")]
    public async Task<DriverReturnValueModel> ReadCutTime()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int64
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCutTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     运行时间
    /// </summary>
    /// <returns></returns>
    [Method("RunTime", name: "运行时间")]
    public async Task<DriverReturnValueModel> ReadCycSec()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int64
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadRunTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴负载
    /// </summary>
    /// <returns></returns>
    [Method("SpindleLoad", name: "主轴负载")]
    public async Task<DriverReturnValueModel> ReadSpindleLoad()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (!IsConnected)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
                return ret;
            }

            var read = await Driver.ReadSpindleLoadAsync();
            if (!read.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = read.Message;
                return ret;
            }

            ret.Value = read.Content;
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读R变量
    /// </summary>
    /// <returns></returns>
    [Method("R", name: "读R变量")]
    public async Task<DriverReturnValueModel> R(DriverAddressIoArgModel val)
    {
        var ret = new DriverReturnValueModel {DataType = val.DataType};

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            switch (val.DataType)
            {
                case DataTypeEnum.Bit or DataTypeEnum.Bool:
                {
                    var readResult = await Driver.ReadBoolAsync(val.Address);
                    if (!readResult.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = readResult.Message;
                        return ret;
                    }

                    ret.Value = readResult.Content;
                    break;
                }
                case DataTypeEnum.Uint16 or DataTypeEnum.Int16:
                {
                    var readResult = await Driver.ReadInt16Async(val.Address);
                    if (!readResult.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = readResult.Message;
                        return ret;
                    }

                    ret.Value = readResult.Content;
                    break;
                }
                case DataTypeEnum.Uint32 or DataTypeEnum.Int32:
                {
                    var readResult = await Driver.ReadInt32Async(val.Address);
                    if (!readResult.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = readResult.Message;
                        return ret;
                    }

                    ret.Value = readResult.Content;
                    break;
                }
                default:
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Bad;
                    ret.Message = "不支持的数据类型";
                    return ret;
                }
            }
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
}