using System;
using HslCommunication;
using HslCommunication.Core.Address;

namespace FanucTtc.Base
{
    /// <summary>
    /// Fanuc的PMC地址对象信息
    /// </summary>
    public class FanucPmcAddress : DeviceAddressDataBase
    {
        /// <summary>
        /// 地址代号信息
        /// </summary>
        public int DataCode { get; set; }

        /// <summary>
        /// 结束的地址值
        /// </summary>
        public int AddressEnd { get; set; }

        /// <summary>
        /// 根据实际的地址信息，解析出PMC地址信息
        /// </summary>
        /// <param name="address">地址信息，例如 R0, G5</param>
        /// <param name="length">读取的长度信息</param>
        /// <returns>PMC地址对象</returns>
        public static OperateResult<FanucPmcAddress> ParseFrom(string address, ushort length)
        {
            FanucPmcAddress fanucPmcAddress = new FanucPmcAddress();
            try
            {
                switch (address[0])
                {
                    case 'G':
                    case 'g': fanucPmcAddress.DataCode = 0x00; break;
                    case 'F':
                    case 'f': fanucPmcAddress.DataCode = 0x01; break;
                    case 'Y':
                    case 'y': fanucPmcAddress.DataCode = 0x02; break;
                    case 'X':
                    case 'x': fanucPmcAddress.DataCode = 0x03; break;
                    case 'A':
                    case 'a': fanucPmcAddress.DataCode = 0x04; break;
                    case 'R':
                    case 'r': fanucPmcAddress.DataCode = 0x05; break;
                    case 'T':
                    case 't': fanucPmcAddress.DataCode = 0x06; break;
                    case 'K':
                    case 'k': fanucPmcAddress.DataCode = 0x07; break;
                    case 'C':
                    case 'c': fanucPmcAddress.DataCode = 0x08; break;
                    case 'D':
                    case 'd': fanucPmcAddress.DataCode = 0x09; break;
                    case 'E':
                    case 'e': fanucPmcAddress.DataCode = 0x0c; break;
                    default: return new OperateResult<FanucPmcAddress>(StringResources.Language.NotSupportedDataType);
                }
                fanucPmcAddress.AddressStart = Convert.ToInt32(address.Substring(1));
                fanucPmcAddress.AddressEnd = fanucPmcAddress.AddressStart + length - 1;
                fanucPmcAddress.Length = length;

                if (fanucPmcAddress.AddressEnd < fanucPmcAddress.AddressStart) fanucPmcAddress.AddressEnd = fanucPmcAddress.AddressStart;
                return OperateResult.CreateSuccessResult(fanucPmcAddress);
            }
            catch (Exception ex)
            {
                return new OperateResult<FanucPmcAddress>(StringResources.Language.NotSupportedDataType + " : " + ex.Message);
            }
        }
    }
}
