using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.CNC.Fanuc;
using HslCommunication.Core;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;

namespace FanucTtc.Base;

/// <summary>
///     一个FANUC的机床通信类对象
/// </summary>
public class FanucTtcBase : NetworkDoubleBase
{
    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }

    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public FanucTtcBase(string ipAddress, int port = 8193)
    {
        IpAddress = ipAddress;
        Port = port;
        ByteTransform = new ReverseBytesTransform();
        TextEncoding = Encoding.Default;
        ReceiveTimeOut = 30_000;
    }

    /// <inheritdoc />
    protected override INetMessage GetNewNetMessage()
    {
        return new CNCFanucSeriesMessage();
    }


    /// <summary>
    ///     获取或设置当前的文本的字符编码信息，如果你不清楚，可以调用<see cref="ReadLanguage" />方法来自动匹配。<br />
    ///     Get or set the character encoding information of the current text.
    ///     If you are not sure, you can call the <see cref="ReadLanguage" /> method to automatically match.
    /// </summary>
    public Encoding TextEncoding { get; set; }

    #endregion

    #region NetworkDoubleBase Override

    /// <inheritdoc />
    protected override OperateResult InitializationOnConnect(Socket socket)
    {
        var read1 = ReadFromCoreServer(socket, "a0 a0 a0 a0 00 01 01 01 00 02 00 02".ToHexBytes());
        if (!read1.IsSuccess) return read1;

        var read2 = ReadFromCoreServer(socket, "a0 a0 a0 a0 00 01 21 01 00 1e 00 01 00 1c 00 01 00 01 00 18 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00".ToHexBytes());
        if (!read2.IsSuccess) return read2;

        return OperateResult.CreateSuccessResult();
    }

    /// <inheritdoc />
    protected override OperateResult ExtraOnDisconnect(Socket socket)
    {
        return ReadFromCoreServer(socket, "a0 a0 a0 a0 00 01 02 01 00 00".ToHexBytes());
    }
#if !NET35
    /// <inheritdoc />
    protected override async Task<OperateResult> InitializationOnConnectAsync(Socket socket)
    {
        var read1 = await ReadFromCoreServerAsync(socket, "a0 a0 a0 a0 00 01 01 01 00 02 00 02".ToHexBytes());
        if (!read1.IsSuccess) return read1;

        var read2 = await ReadFromCoreServerAsync(socket, "a0 a0 a0 a0 00 01 21 01 00 1e 00 01 00 1c 00 01 00 01 00 18 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00".ToHexBytes());
        if (!read2.IsSuccess) return read2;

        return OperateResult.CreateSuccessResult();
    }

    /// <inheritdoc />
    protected override async Task<OperateResult> ExtraOnDisconnectAsync(Socket socket)
    {
        return await ReadFromCoreServerAsync(socket, "a0 a0 a0 a0 00 01 02 01 00 00".ToHexBytes());
    }
#endif

    #endregion

    private double GetFanucDouble(byte[] content, int index)
    {
        return GetFanucDouble(content, index, 1)[0];
    }

    private double[] GetFanucDouble(byte[] content, int index, int length)
    {
        var buffer = new double[length];
        for (var i = 0; i < length; i++)
        {
            var data = ByteTransform.TransInt32(content, index + 8 * i);
            int decs = ByteTransform.TransInt16(content, index + 8 * i + 6);
            buffer[i] = Math.Round(data * Math.Pow(0.1d, decs), decs);
        }

        return buffer;
    }

    private byte[] CreateFromFanucDouble(double value)
    {
        var buffer = new byte[8];
        var interge = (int) (value * 1000);
        ByteTransform.TransByte(interge).CopyTo(buffer, 0);
        buffer[5] = 0x0A;
        buffer[7] = 0x03;
        return buffer;
    }

    private void ChangeTextEncoding(ushort code)
    {
        switch (code)
        {
            case 0x00:
                TextEncoding = Encoding.Default;
                break;
            case 0x01:
            case 0x04:
                TextEncoding = Encoding.GetEncoding("shift_jis", EncoderFallback.ReplacementFallback, new DecoderReplacementFallback());
                break;
            case 0x06:
                TextEncoding = Encoding.GetEncoding("ks_c_5601-1987");
                break;
            case 0x0F:
                TextEncoding = Encoding.Default;
                break;
            case 0x10:
                TextEncoding = Encoding.GetEncoding("windows-1251");
                break;
            case 0x11:
                TextEncoding = Encoding.GetEncoding("windows-1254");
                break;
        }
    }

    #region Read Write Support

    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns>主轴转速</returns>
    public OperateResult<int> ReadSpindleSpeed()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x25, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(28);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取程序名及程序号
    /// </summary>
    /// <returns>程序名及程序号</returns>
    public OperateResult<string, int> ReadMainProgram()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x1C, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        var number = ByteTransform.TransInt32(result[0], 14);
        var name = ByteTransform.TransInt32(result[0], 14).ToString();
        return OperateResult.CreateSuccessResult(name, number);
    }

    /// <summary>
    ///     读取当前程序号
    /// </summary>
    /// <returns>程序名</returns>
    public OperateResult<string> ReadSystemProgramCurrent()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x1d, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        var name = "N" + ByteTransform.TransInt32(result[0], 14);
        return OperateResult.CreateSuccessResult(name);
    }

    /// <summary>
    ///     读取机床的语言设定信息，具体值的含义参照API文档说明<br />
    ///     Read the language setting information of the machine tool, refer to the API documentation for the meaning of the specific values
    /// </summary>
    /// <remarks>此处举几个常用值 0: 英语 1: 日语 2: 德语 3: 法语 4: 中文繁体 6: 韩语 15: 中文简体 16: 俄语 17: 土耳其语</remarks>
    /// <returns>返回的语言代号</returns>
    public OperateResult<ushort> ReadLanguage()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x8D, 0x0CD1, 0x0CD1, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<ushort>(read);
        var result = ExtraContentArray(read.Content.RemoveBegin(10));

        var code = ByteTransform.TransUInt16(result[0], 24);
        ChangeTextEncoding(code);
        return OperateResult.CreateSuccessResult(code);
    }

    /// <summary>
    ///     读取宏变量，可以用来读取刀具号
    /// </summary>
    /// <param name="number"></param>
    /// <returns>读宏变量信息</returns>
    public OperateResult<double> ReadSystemMacroValue(int number)
    {
        return ByteTransformHelper.GetResultFromArray(ReadSystemMacroValue(number, 1));
    }

    /// <summary>
    ///     读取宏变量，可以用来读取刀具号
    /// </summary>
    /// <param name="number"></param>
    /// <param name="length">读取的长度信息</param>
    /// <returns>是否成功</returns>
    public OperateResult<double[]> ReadSystemMacroValue(int number, int length)
    {
        // 拆分5个5个读
        var lenArray = SoftBasic.SplitIntegerToArray(length, 5);
        var index = number;
        var result = new List<byte>();

        for (var i = 0; i < lenArray.Length; i++)
        {
            var read = ReadFromCoreServer(BuildReadArray(
                BuildReadSingle(0x15, index, index + lenArray[i] - 1, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);

            result.AddRange(ExtraContentArray(read.Content.RemoveBegin(10))[0].RemoveBegin(14));
            index += lenArray[i];
        }

        try
        {
            return OperateResult.CreateSuccessResult(GetFanucDouble(result.ToArray(), 0, length));
        }
        catch (Exception ex)
        {
            return new OperateResult<double[]>(ex.Message + " Source:" + result.ToArray().ToHexString(' '));
        }

        ;
    }

    /// <summary>
    ///     写宏变量
    /// </summary>
    /// <param name="number">地址</param>
    /// <param name="values">数据值</param>
    /// <returns>是否成功</returns>
    public OperateResult WriteSystemMacroValue(int number, double[] values)
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildWriteSingle(0x16, number, number + values.Length - 1, 0, 0, values)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        if (ByteTransform.TransUInt16(result[0], 6) == 0)
            return OperateResult.CreateSuccessResult();
        return new OperateResult(ByteTransform.TransUInt16(result[0], 6), "Unknown Error");
    }

    /// <summary>
    ///     写PMC
    /// </summary>
    /// <param name="addr">地址</param>
    /// <param name="values">值</param>
    /// <returns></returns>
    public OperateResult WritePmcValue(int addr, byte[] values)
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildWriteByte(0x8002, addr, addr + values.Length - 1, 0x09, 0, values)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        if (ByteTransform.TransUInt16(result[0], 6) == 0)
            return OperateResult.CreateSuccessResult();
        return new OperateResult(ByteTransform.TransUInt16(result[0], 6), "Unknown Error");
    }

    public OperateResult WriteProductReset()
    {
        var read = ReadFromCoreServer(BuildProductZero());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        if (ByteTransform.TransUInt16(result[0], 6) == 0)
            return OperateResult.CreateSuccessResult();
        return new OperateResult(ByteTransform.TransUInt16(result[0], 6), "Unknown Error");
    }

    public OperateResult WritePmcByte(int addr, byte value)
    {
        return WritePmcValue(addr, new[] {value});
    }

    /// <summary>
    ///     根据刀具号写入长度形状补偿，刀具号为1-24
    /// </summary>
    /// <param name="cutter">刀具号，范围为1-24</param>
    /// <param name="offset">补偿值</param>
    /// <returns>是否写入成功</returns>
    public OperateResult WriteCutterLengthSharpOffset(int cutter, double offset)
    {
        return WriteSystemMacroValue(11000 + cutter, new[] {offset});
    }

    /// <summary>
    ///     根据刀具号写入长度磨损补偿，刀具号为1-24
    /// </summary>
    /// <param name="cutter">刀具号，范围为1-24</param>
    /// <param name="offset">补偿值</param>
    /// <returns>是否写入成功</returns>
    public OperateResult WriteCutterLengthWearOffset(int cutter, double offset)
    {
        return WriteSystemMacroValue(10000 + cutter, new[] {offset});
    }

    /// <summary>
    ///     根据刀具号写入半径形状补偿，刀具号为1-24
    /// </summary>
    /// <param name="cutter">刀具号，范围为1-24</param>
    /// <param name="offset">补偿值</param>
    /// <returns>是否写入成功</returns>
    public OperateResult WriteCutterRadiusSharpOffset(int cutter, double offset)
    {
        return WriteSystemMacroValue(13000 + cutter, new[] {offset});
    }

    /// <summary>
    ///     根据刀具号写入半径磨损补偿，刀具号为1-24
    /// </summary>
    /// <param name="cutter">刀具号，范围为1-24</param>
    /// <param name="offset">补偿值</param>
    /// <returns>是否写入成功</returns>
    public OperateResult WriteCutterRadiusWearOffset(int cutter, double offset)
    {
        return WriteSystemMacroValue(12000 + cutter, new[] {offset});
    }

    /// <summary>
    ///     读取伺服负载
    /// </summary>
    /// <returns>轴负载</returns>
    public OperateResult<int> ReadFanucAxisLoad()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x56, 1, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);


        var result = read.Content.RemoveBegin(68);

        return OperateResult.CreateSuccessResult((int) ByteTransform.TransInt16(result, 0));
    }

    /// <summary>
    ///     读取主轴负载
    /// </summary>
    /// <returns>主轴负载</returns>
    public OperateResult<int> ReadSpindleLoad()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x40, 4, -1, 0, 0, 0),
            BuildReadSingle(0x40, 5, -1, 0, 0, 0),
            BuildReadSingle(0x8a, 1, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        var result = read.Content.RemoveBegin(68);

        return OperateResult.CreateSuccessResult((int) ByteTransform.TransInt16(result, 0));
    }

    /// <summary>
    ///     读取机床的坐标
    /// </summary>
    /// <returns></returns>
    public OperateResult<SysAllCoors> ReadSysAllCoors()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x19, 0, 0, 0, 0, 0),
            BuildReadSingle(0x26, 0, -1, 0, 0, 0),
            BuildReadSingle(0x26, 1, -1, 0, 0, 0),
            BuildReadSingle(0x26, 2, -1, 0, 0, 0),
            BuildReadSingle(0x26, 3, -1, 0, 0, 0),
            BuildReadSingle(0x89, -1, 0, 0, 0, 0),
            BuildReadSingle(0x0e, 0x0c2b, 0x0c2b, -1, 0, 0),
            BuildReadSingle(0x88, 2, 0, 0, 0, 0),
            BuildReadSingle(0x19, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysAllCoors>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        var length = 3; // ByteTransform.TransUInt16(result[0], 14);

        var allCoors = new SysAllCoors();

        allCoors.Absolute = GetFanucDouble(result[1], 14, length);
        allCoors.Machine = GetFanucDouble(result[2], 14, length);
        allCoors.Relative = GetFanucDouble(result[3], 14, length);

        return OperateResult.CreateSuccessResult(allCoors);
    }

    /// <summary>
    ///     读取报警信息
    /// </summary>
    /// <returns></returns>
    public OperateResult<SysAlarm[]> ReadSystemAlarm()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x23, -1, 10, 2, 64, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysAlarm[]>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        if (ByteTransform.TransUInt16(result[0], 12) > 0)
        {
            var length = ByteTransform.TransUInt16(result[0], 12) / 80;
            var alarms = new SysAlarm[length];
            for (var i = 0; i < alarms.Length; i++)
            {
                alarms[i] = new SysAlarm();
                alarms[i].AlarmId = ByteTransform.TransInt32(result[0], 14 + 80 * i);
                alarms[i].Type = ByteTransform.TransInt16(result[0], 20 + 80 * i);
                alarms[i].Axis = ByteTransform.TransInt16(result[0], 24 + 80 * i);

                var msgLength = ByteTransform.TransUInt16(result[0], 28 + 80 * i);
                //alarms[i].Message = Encoding.Default.GetString(result[0], 30 + 80 * i, msgLength);

                alarms[i].Message = Encoding.GetEncoding("gb2312").GetString(result[0], 30 + 80 * i, msgLength); //中文系统默认编码格式GB2312
            }

            return OperateResult.CreateSuccessResult(alarms);
        }

        return OperateResult.CreateSuccessResult(new SysAlarm[0]);
    }

    /// <summary>
    ///     读取fanuc机床的运行时间返回秒为单位的信息
    /// </summary>
    /// <returns>秒为单位的结果</returns>
    public OperateResult<long> ReadRunTime()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x0e, 0x1a60, 0x1a60, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<long>(read);
        var result = read.Content.RemoveBegin(36);
        return OperateResult.CreateSuccessResult((long) ByteTransform.TransInt32(result, 0) * 60);
    }


    /// <summary>
    ///     读取fanuc机床的开机时间返回秒为单位的信息
    /// </summary>
    /// <returns>秒为单位的结果</returns>
    public OperateResult<long> ReadAliveTime()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x0e, 0x1a5e, 0x1a5e, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<long>(read);
        var result = read.Content.RemoveBegin(36);
        return OperateResult.CreateSuccessResult((long) ByteTransform.TransInt32(result, 0) * 60);
    }


    /// <summary>
    ///     读取fanuc机床的切削时间返回秒为单位的信息
    /// </summary>
    /// <returns>秒为单位的结果</returns>
    public OperateResult<long> ReadCutTime()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x0e, 0x1a62, 0x1a62, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<long>(read);
        var result = read.Content.RemoveBegin(36);
        return OperateResult.CreateSuccessResult((long) ByteTransform.TransInt32(result, 0) * 60);
    }


    /// <summary>
    ///     读取fanuc机床的循环时间返回秒为单位的信息
    /// </summary>
    /// <returns>秒为单位的结果</returns>
    public OperateResult<long> ReadCycleTime()
    {
        //获取循环时间的分钟数
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x0e, 0x1a66, 0x1a66, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<long>(read);
        var result = read.Content.RemoveBegin(36);
        //获取循环时间的秒数
        var read1 = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x0e, 0x1a65, 0x1a65, 0, 0, 0)
        ));
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<long>(read1);
        var result1 = read1.Content.RemoveBegin(36);
        long cycletime = ByteTransform.TransInt32(result, 0) * 60 + ByteTransform.TransInt32(result1, 0) / 1000;
        return OperateResult.CreateSuccessResult(cycletime);
    }


    /// <summary>
    ///     读取报警状态信息
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadAlarmStatus()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x1A, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        return OperateResult.CreateSuccessResult((int) ByteTransform.TransUInt16(result[0], 16));
    }


    /// <summary>
    ///     读取进给倍率,Fanuc进给倍率需要255-读取值
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadFeedRate()
    {
        var read = ReadFromCoreServer(BuildReadFeedRate());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        var result = read.Content.RemoveBegin(28);
        return OperateResult.CreateSuccessResult(255 - result[0]);
    }

    /// <summary>
    ///     读取主轴倍率
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSpindleRate()
    {
        var read = ReadFromCoreServer(BuildReadSpindleRate());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        var result = read.Content.RemoveBegin(28);
        return OperateResult.CreateSuccessResult((int) result[0]);
    }

    /// <summary>
    ///     读取进给速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadActfSpeed()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x24, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(28);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取进给速度设定
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSetfSpeed()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x15, 0x10d5, 0x10d5, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(28);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000);
    }

    /// <summary>
    ///     读取主轴速度设定
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSetsSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x15, 0x10df, 0x10df, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(28);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000);
    }

    /// <summary>
    ///     读取主轴速度设定
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSetsSpeed()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x15, 0x10df, 0x10df, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(28);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000);
    }

    /// <summary>
    ///     读取主轴温度
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadStemper()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x30, 0x0193, 0x0193, 1, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        var result = read.Content.RemoveBegin(36);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取伺服温度
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadFtemper()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x30, 0x0134, 0x0134, 1, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        var result = read.Content.RemoveBegin(36);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取当前刀具号
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadToolnum()
    {
        //OperateResult<byte[]> read = ReadFromCoreServer(BuildReadToolnum());
        //if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        //byte[] result = read.Content.RemoveBegin(28);
        //return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
        var read = ReadSystemMacroValue(4120, 1);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        return OperateResult.CreateSuccessResult(Convert.ToInt32(read.Content[0]));
    }


    /// <summary>
    ///     读取系统的基本信息状态，工作模式，运行状态，是否急停等等操作
    /// </summary>
    /// <returns>结果信息数据</returns>
    public OperateResult<SysStatusInfo> ReadSysStatusInfo()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x19, 0, 0, 0, 0, 0),
            BuildReadSingle(0xE1, 0, 0, 0, 0, 0),
            BuildReadSingle(0x98, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysStatusInfo>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        var statusInfo = new SysStatusInfo();
        statusInfo.Dummy = result[1].Length >= 16 ? ByteTransform.TransInt16(result[1], 14) : (short) 0;
        //if (result[2].Length > 14)
        //{
        //    statusInfo.TMMode = ByteTransform.TransInt16(result[2], 14);
        //}
        statusInfo.TMMode = result[2].Length >= 16 ? ByteTransform.TransInt16(result[2], 14) : (short) 0;
        statusInfo.WorkMode = (CNCWorkMode) ByteTransform.TransInt16(result[0], 14);
        statusInfo.RunStatus = (CNCRunStatus) ByteTransform.TransInt16(result[0], 16);
        statusInfo.Motion = ByteTransform.TransInt16(result[0], 18);
        statusInfo.MSTB = ByteTransform.TransInt16(result[0], 20);
        statusInfo.Emergency = ByteTransform.TransInt16(result[0], 22);
        statusInfo.Alarm = ByteTransform.TransInt16(result[0], 24);
        statusInfo.Edit = ByteTransform.TransInt16(result[0], 26);

        return OperateResult.CreateSuccessResult(statusInfo);
    }

    /// <summary>
    ///     读取设备的程序列表
    /// </summary>
    /// <returns>读取结果信息</returns>
    public OperateResult<int[]> ReadProgramList()
    {
        var read = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x06, 0x01, 0x13, 0, 0, 0)
        ));
        var check = ReadFromCoreServer(BuildReadArray(
            BuildReadSingle(0x06, 0x1A0B, 0x13, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int[]>(read);
        if (!check.IsSuccess) return OperateResult.CreateFailedResult<int[]>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        var length = (result[0].Length - 14) / 72;
        var programs = new int[length];
        for (var i = 0; i < length; i++) programs[i] = ByteTransform.TransInt32(result[0], 14 + 72 * i);
        return OperateResult.CreateSuccessResult(programs);
    }

    /// <summary>
    ///     读取当前的刀具补偿信息
    /// </summary>
    /// <param name="cutterNumber">刀具数量</param>
    /// <returns>结果内容</returns>
    public OperateResult<CutterInfo[]> ReadCutterInfos(int cutterNumber = 24)
    {
        var read1 = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 0, 0, 0)));
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read1);

        var read2 = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 1, 0, 0)));
        if (!read2.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read2);

        var read3 = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 2, 0, 0)));
        if (!read3.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read3);

        var read4 = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 3, 0, 0)));
        if (!read4.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read4);

        return ExtraCutterInfos(read1.Content, read2.Content, read3.Content, read4.Content, cutterNumber);
    }


    /// <summary>
    ///     读取寄存器的数据信息，需要传入寄存器的代码，起始地址，结束地址信息<br />
    ///     To read the data information of the register, you need to pass in the code of the register, the start address, and the end address information
    /// </summary>
    /// <param name="code">寄存器代码</param>
    /// <param name="start">起始的地址</param>
    /// <param name="end">结束的地址</param>
    /// <returns>包含原始字节信息的结果对象</returns>
    public OperateResult<byte[]> ReadData(int code, int start, int end)
    {
        var read1 = ReadFromCoreServer(BuildReadArray(BuildReadMulti(0x02, 0x8001, start, end, code, 0, 0)));
        if (!read1.IsSuccess) return read1;

        var result = ExtraContentArray(read1.Content.RemoveBegin(10))[0];

        var checkResult = CheckSingleResultLeagle(result);
        if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(checkResult);

        int length = ByteTransform.TransUInt16(result, 12);
        return OperateResult.CreateSuccessResult(result.SelectMiddle(14, length));
    }

    /// <summary>
    ///     将原始字节的数据写入到指定的寄存器里，需要传入寄存器的代码，起始地址，原始的字节数据信息<br />
    ///     To write the original byte data into the specified register, you need to pass in the code of the register, the starting address, and the original byte data information
    /// </summary>
    /// <param name="code">寄存器代码</param>
    /// <param name="start">起始的地址</param>
    /// <param name="data">等待写入的原始字节数据</param>
    /// <returns>是否写入成功</returns>
    public OperateResult WriteData(int code, int start, byte[] data)
    {
        if (data == null) data = new byte[0];
        var read = ReadFromCoreServer(BuildReadArray(BuildWriteSingle(0x02, 0x8002, start, start + data.Length - 1, code, 0, data)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
        return CheckSingleResultLeagle(result);
    }

    /// <summary>
    ///     读取PMC数据，需要传入起始地址和结束地址，返回byte[]数据信息<br />
    ///     To read PMC data, you need to pass in the start address and length, and return byte[] data information
    /// </summary>
    /// <remarks>
    ///     地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5
    /// </remarks>
    /// <param name="address">起始地址，地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5</param>
    /// <param name="length">长度信息</param>
    /// <returns>读取结果</returns>
    public OperateResult<byte[]> ReadPMCData(string address, ushort length)
    {
        return FanucPmcAddress.ParseFrom(address, length).Then(m => ReadData(m.DataCode, m.AddressStart, m.AddressEnd));
    }

    /// <summary>
    ///     写入PMC数据，需要传入起始地址和，以及等待写入的byte[]数据信息<br />
    ///     To write PMC data, you need to pass in the start address, as well as the byte[] data information waiting to be written
    /// </summary>
    /// <remarks>
    ///     地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5
    /// </remarks>
    /// <param name="address">起始地址，地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5</param>
    /// <param name="value">等待写入的原始字节数据</param>
    /// <returns>是否写入成功</returns>
    public OperateResult WritePMCData(string address, byte[] value)
    {
        return FanucPmcAddress.ParseFrom(address, 1).Then(m => WriteData(m.DataCode, m.AddressStart, value));
    }


    public OperateResult<int> ReadInt32(string address)
    {
        var read = ReadPMCData(address, 4);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content;
        //ByteTransform.DataFormat = DataFormat.Abcd;
        Array.Reverse(result, 0, result.Length);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    public async Task<OperateResult<int>> ReadInt32Async(string address)
    {
        var read = await ReadPMCDataAsync(address, 4);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content;
        //ByteTransform.DataFormat = DataFormat.Abcd;
        Array.Reverse(result, 0, result.Length);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    //public OperateResult<byte[]> ReadByte(string address)
    //{
    //    OperateResult<byte[]> read = ReadPMCData(address, 4);
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(read);
    //    byte[] result = read.Content;
    //    return OperateResult.CreateSuccessResult(result);

    //}


    public OperateResult<int> ReadInt16(string address)
    {
        var read = ReadPMCData(address, 2);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content;
        //ByteTransform.DataFormat = DataFormat.Abcd;
        Array.Reverse(result, 0, result.Length);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    public async Task<OperateResult<int>> ReadInt16Async(string address)
    {
        var read = await ReadPMCDataAsync(address, 2);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content;
        //ByteTransform.DataFormat = DataFormat.Abcd;
        Array.Reverse(result, 0, result.Length);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    public async Task<OperateResult<bool>> ReadBoolAsync(string address)
    {
        var read = await ReadPMCDataAsync(address, 1);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<bool>(read);
        var result = read.Content;
        // ByteTransform.DataFormat = DataFormat.ABCD;
        return OperateResult.CreateSuccessResult(ByteTransform.TransBool(result, 0));
    }
    
    public OperateResult<bool> ReadBool(string address)
    {
        var read = ReadPMCData(address, 1);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<bool>(read);
        var result = read.Content;
        // ByteTransform.DataFormat = DataFormat.ABCD;
        return OperateResult.CreateSuccessResult(ByteTransform.TransBool(result, 0));
    }

    /// <summary>
    ///     读取工件尺寸
    /// </summary>
    /// <returns></returns>
    public OperateResult<double[]> ReadDeviceWorkPiecesSize()
    {
        return ReadSystemMacroValue(601, 20);
    }

    /// <summary>
    ///     读取当前的程序内容，只能读取程序的片段，返回程序内容。<br />
    ///     Read the current program content, only read the program fragments, and return the program content.
    /// </summary>
    /// <returns>程序内容</returns>
    public OperateResult<string> ReadCurrentProgram()
    {
        var read = ReadFromCoreServer(
            BuildReadArray(BuildReadSingle(0x20, 0x0594, 0x00, 0x00, 0x00, 0x00)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
        return OperateResult.CreateSuccessResult(Encoding.ASCII.GetString(result, 18, result.Length - 18));
    }


    /// <summary>
    ///     设置指定的程序号为当前的主程序，如果程序号不存在，返回错误信息<br />
    ///     Set the specified program number as the current main program, if the program number does not exist, an error message will be returned
    /// </summary>
    /// <param name="programNum">程序号信息</param>
    /// <returns>是否设置成功</returns>
    public OperateResult SetCurrentProgram(ushort programNum)
    {
        var read = ReadFromCoreServer(
            BuildReadArray(BuildReadSingle(0x03, programNum, 0, 0, 0, 0)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, string>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
        var err = ByteTransform.TransInt16(result, 6);

        if (err == 0) return OperateResult.CreateSuccessResult();
        return new OperateResult(err, StringResources.Language.UnknownError);
    }

    /// <summary>
    ///     启动加工程序<br />
    ///     Start the processing program
    /// </summary>
    /// <returns>是否启动成功</returns>
    public OperateResult StartProcessing()
    {
        var read = ReadFromCoreServer(
            BuildReadArray(BuildReadSingle(0x01, 0, 0, 0, 0, 0)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, string>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
        var err = ByteTransform.TransInt16(result, 6);

        if (err == 0) return OperateResult.CreateSuccessResult();
        return new OperateResult(err, StringResources.Language.UnknownError);
    }


    /// <summary>
    ///     将指定文件的NC加工程序，下载到数控机床里，返回是否下载成功<br />
    ///     <b>[Authorization]</b> Download the NC machining program of the specified file to the CNC machine tool, and return whether the download is successful
    /// </summary>
    /// <remarks>
    ///     程序文件的内容必须%开始，%结束，下面是一个非常简单的例子：<br />
    ///     %<br />
    ///     O0006<br />
    ///     G90G10L2P1<br />
    ///     M30<br />
    ///     %
    /// </remarks>
    /// <param name="file">程序文件的路径</param>
    /// <returns>是否下载成功</returns>
    public OperateResult WriteProgramFile(string file)
    {
        var content = File.ReadAllText(file);
        return WriteProgramContent(content);
    }

    /// <summary>
    ///     将指定程序内容的NC加工程序，写入到数控机床里，返回是否下载成功<br />
    ///     <b>[Authorization]</b> Download the NC machining program to the CNC machine tool, and return whether the download is successful
    /// </summary>
    /// <remarks>
    ///     程序文件的内容必须%开始，%结束，下面是一个非常简单的例子：<br />
    ///     %<br />
    ///     O0006<br />
    ///     G90G10L2P1<br />
    ///     M30<br />
    ///     %
    /// </remarks>
    /// <param name="program">程序内容信息</param>
    /// <param name="everyWriteSize">每次写入的长度信息</param>
    /// <returns>是否下载成功</returns>
    public OperateResult WriteProgramContent(string program, int everyWriteSize = 512)
    {
        var socket = CreateSocketAndConnect(IpAddress, Port, ConnectTimeOut);
        if (!socket.IsSuccess) return socket.ConvertFailed<int>();

        var ini1 = ReadFromCoreServer(socket.Content, "a0 a0 a0 a0 00 01 01 01 00 02 00 01".ToHexBytes());
        if (!ini1.IsSuccess) return ini1;

        var read1 = ReadFromCoreServer(socket.Content, BulidWriteProgramFilePre());
        if (!read1.IsSuccess) return read1;

        var contents = BulidWriteProgram(Encoding.ASCII.GetBytes(program), everyWriteSize);
        for (var i = 0; i < contents.Count; i++)
        {
            var read2 = ReadFromCoreServer(socket.Content, contents[i], false);
            if (!read2.IsSuccess) return read2;
        }

        var read3 = ReadFromCoreServer(socket.Content, new byte[] {0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x13, 0x01, 0x00, 0x00});
        if (!read3.IsSuccess) return read3;

        socket.Content?.Close();
        if (read3.Content.Length >= 14)
        {
            int err = ByteTransform.TransInt16(read3.Content, 12);
            if (err != 0) return new OperateResult<string>(err, StringResources.Language.UnknownError);
        }

        return OperateResult.CreateSuccessResult();
    }

    /// <summary>
    ///     读取指定程序号的程序内容<br />
    ///     Read the program content of the specified program number
    /// </summary>
    /// <param name="program">程序号</param>
    /// <returns>程序内容</returns>
    public OperateResult<string> ReadProgram(int program)
    {
        var socket = CreateSocketAndConnect(IpAddress, Port, ConnectTimeOut);
        if (!socket.IsSuccess) return socket.ConvertFailed<string>();

        var ini1 = ReadFromCoreServer(socket.Content, "a0 a0 a0 a0 00 01 01 01 00 02 00 01".ToHexBytes());
        if (!ini1.IsSuccess) return OperateResult.CreateFailedResult<string>(ini1);

        var read1 = ReadFromCoreServer(socket.Content, BuildReadProgramPre(program));
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<string>(read1);

        // 检测错误信息
        var err = read1.Content[12] * 256 + read1.Content[13];
        if (err != 0)
        {
            socket.Content?.Close();
            return new OperateResult<string>(err, StringResources.Language.UnknownError);
        }

        var sb = new StringBuilder();
        while (true)
        {
            var read2 = ReadFromCoreServer(socket.Content, null);
            if (!read2.IsSuccess) return OperateResult.CreateFailedResult<string>(read2);

            if (read2.Content[6] == 0x16)
                sb.Append(Encoding.ASCII.GetString(read2.Content, 10, read2.Content.Length - 10));
            else if (read2.Content[6] == 0x17)
                break;
        }

        var send = Send(socket.Content, new byte[] {0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x17, 0x02, 0x00, 0x00});
        if (!send.IsSuccess) return OperateResult.CreateFailedResult<string>(send);

        socket.Content?.Close();
        return OperateResult.CreateSuccessResult(sb.ToString());
    }

    /// <summary>
    ///     根据指定的程序号信息，删除当前的程序信息<br />
    ///     According to the designated program number information, delete the current program information
    /// </summary>
    /// <param name="program">程序号</param>
    /// <returns>是否删除成功</returns>
    public OperateResult DeleteProgram(int program)
    {
        var read = ReadFromCoreServer(
            BuildReadArray(BuildReadSingle(0x05, program, 0, 0, 0, 0)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, string>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
        var err = ByteTransform.TransInt16(result, 6);

        if (err == 0) return OperateResult.CreateSuccessResult();
        return new OperateResult(err, StringResources.Language.UnknownError);
    }

    /// <summary>
    ///     读取当前程序的前台路径<br />
    ///     Read the foreground path of the current program
    /// </summary>
    /// <returns>程序的路径信息</returns>
    public OperateResult<string> ReadCurrentForegroundDir()
    {
        var read = ReadFromCoreServer(BuildReadArray(BuildReadSingle(0xB0, 1, 0, 0, 0, 0)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        var index = 0;
        for (var i = 14; i < result[0].Length; i++)
            if (result[0][i] == 0x00)
            {
                index = i;
                break;
            }

        if (index == 0) index = result[0].Length;
        return OperateResult.CreateSuccessResult(Encoding.ASCII.GetString(result[0], 14, index - 14));
    }

    /// <summary>
    ///     设置指定路径为当前路径<br />
    ///     Set the specified path as the current path
    /// </summary>
    /// <param name="programName">程序名</param>
    /// <returns>结果信息</returns>
    public OperateResult SetDeviceProgsCurr(string programName)
    {
        var path = ReadCurrentForegroundDir();
        if (!path.IsSuccess) return path;

        var buffer = new byte[256];
        Encoding.ASCII.GetBytes(path.Content + programName).CopyTo(buffer, 0);

        var read = ReadFromCoreServer(BuildReadArray(BuildWriteSingle(0x01, 0xBA, 0, 0, 0, 0, buffer)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
        return CheckSingleResultLeagle(result);
    }

    /// <summary>
    ///     读取机床的当前时间信息
    /// </summary>
    /// <returns>时间信息</returns>
    public OperateResult<DateTime> ReadCurrentDateTime()
    {
        var read1 = ReadSystemMacroValue(3011);
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<DateTime>(read1);

        var read2 = ReadSystemMacroValue(3012);
        if (!read2.IsSuccess) return OperateResult.CreateFailedResult<DateTime>(read2);

        var date = Convert.ToInt32(read1.Content).ToString();
        var time = Convert.ToInt32(read2.Content).ToString().PadLeft(6, '0');

        return OperateResult.CreateSuccessResult(new DateTime(
            int.Parse(date.Substring(0, 4)), int.Parse(date.Substring(4, 2)), int.Parse(date.Substring(6)),
            int.Parse(time.Substring(0, 2)), int.Parse(time.Substring(2, 2)), int.Parse(time.Substring(4))));
    }

    /// <summary>
    ///     读取当前的已加工的零件数量
    /// </summary>
    /// <returns>已经加工的零件数量</returns>
    public OperateResult<int> ReadCurrentProduceCount()
    {
        var read = ReadSystemMacroValue(3901);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        return OperateResult.CreateSuccessResult(Convert.ToInt32(read.Content));
    }

    /// <summary>
    ///     读取期望的加工的零件数量
    /// </summary>
    /// <returns>期望的加工的零件数量</returns>
    public OperateResult<int> ReadExpectProduceCount()
    {
        var read = ReadSystemMacroValue(3902);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        return OperateResult.CreateSuccessResult(Convert.ToInt32(read.Content));
    }

    #endregion

    #region Async Read Write Support

#if !NET20 && !NET35
    /// <inheritdoc cref="ReadSpindleSpeedAndFeedRate" />
    public async Task<OperateResult<int>> ReadSpindleSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x25, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(28);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }


    public async Task<OperateResult<string, int>> ReadMainProgramAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x1C, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        var number = ByteTransform.TransInt32(result[0], 14);
        var name = ByteTransform.TransInt32(result[0], 14).ToString();
        return OperateResult.CreateSuccessResult(name, number);
    }

    /// <summary>
    ///     读取当前程序号
    /// </summary>
    /// <returns>程序名</returns>
    public async Task<OperateResult<string>> ReadSystemProgramCurrentAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x1d, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        var name = "N" + ByteTransform.TransInt32(result[0], 14);
        return OperateResult.CreateSuccessResult(name);
    }

    /// <inheritdoc cref="ReadLanguage" />
    public async Task<OperateResult<ushort>> ReadLanguageAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x8D, 0x0CD1, 0x0CD1, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<ushort>(read);
        var result = ExtraContentArray(read.Content.RemoveBegin(10));

        var code = ByteTransform.TransUInt16(result[0], 24);
        ChangeTextEncoding(code);
        return OperateResult.CreateSuccessResult(code);
    }

    /// <inheritdoc cref="ReadSystemMacroValue(int)" />
    public async Task<OperateResult<double>> ReadSystemMacroValueAsync(int number)
    {
        return ByteTransformHelper.GetResultFromArray(await ReadSystemMacroValueAsync(number, 1));
    }

    /// <inheritdoc cref="ReadSystemMacroValue(int, int)" />
    public async Task<OperateResult<double[]>> ReadSystemMacroValueAsync(int number, int length)
    {
        // 拆分5个5个读
        var lenArray = SoftBasic.SplitIntegerToArray(length, 5);
        var index = number;
        var result = new List<byte>();

        for (var i = 0; i < lenArray.Length; i++)
        {
            var read = await ReadFromCoreServerAsync(BuildReadArray(
                BuildReadSingle(0x15, index, index + lenArray[i] - 1, 0, 0, 0)));
            if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);

            result.AddRange(ExtraContentArray(read.Content.RemoveBegin(10))[0].RemoveBegin(14));
            index += lenArray[i];
        }

        try
        {
            return OperateResult.CreateSuccessResult(GetFanucDouble(result.ToArray(), 0, length));
        }
        catch (Exception ex)
        {
            return new OperateResult<double[]>(ex.Message + " Source:" + result.ToArray().ToHexString(' '));
        }
    }

    /// <inheritdoc cref="ReadCutterNumber" />
    public async Task<OperateResult<int>> ReadToolnumAsync()
    {
        var read = await ReadSystemMacroValueAsync(4120, 1);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        return OperateResult.CreateSuccessResult(Convert.ToInt32(read.Content[0]));
    }


    /// <summary>
    ///     读取进给倍率,Fanuc进给倍率需要255-读取值
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadFeedRateAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadFeedRate());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(28);
        return OperateResult.CreateSuccessResult(255 - result[0]);
    }

    /// <summary>
    ///     读取主轴倍率
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSpindleRateAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSpindleRate());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        var result = read.Content.RemoveBegin(28);
        return OperateResult.CreateSuccessResult((int) result[0]);
    }

    /// <summary>
    ///     读取进给速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadActfSpeedAsync()
    {
        // OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadActfSpeed());

        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x24, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(28);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取进给速度设定
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSetfSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x15, 0x10d5, 0x10d5, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(28);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000);
    }

    /// <summary>
    ///     读取主轴温度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadStemperAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x30, 0x0193, 0x0193, 1, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        var result = read.Content.RemoveBegin(36);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     异步读取伺服温度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadFtemperAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x30, 0x0134, 0x0134, 1, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        var result = read.Content.RemoveBegin(36);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取主轴负载
    /// </summary>
    /// <returns>主轴负载</returns>
    public async Task<OperateResult<int>> ReadSpindleLoadAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSload());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        var result = read.Content.RemoveBegin(68);

        return OperateResult.CreateSuccessResult((int) ByteTransform.TransInt16(result, 0));
    }


    /// <inheritdoc cref="WriteSystemMacroValue(int, double[])" />
    public async Task<OperateResult> WriteSystemMacroValueAsync(int number, double[] values)
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildWriteSingle(0x16, number, number + values.Length - 1, 0, 0, values)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        if (ByteTransform.TransUInt16(result[0], 6) == 0)
            return OperateResult.CreateSuccessResult();
        return new OperateResult(ByteTransform.TransUInt16(result[0], 6), "Unknown Error");
    }

    /// <inheritdoc cref="WriteCutterLengthShapeOffset(int, double)" />
    public async Task<OperateResult> WriteCutterLengthSharpOffsetAsync(int cutter, double offset)
    {
        return await WriteSystemMacroValueAsync(11000 + cutter, new[] {offset});
    }

    /// <inheritdoc cref="WriteCutterLengthWearOffset(int, double)" />
    public async Task<OperateResult> WriteCutterLengthWearOffsetAsync(int cutter, double offset)
    {
        return await WriteSystemMacroValueAsync(10000 + cutter, new[] {offset});
    }

    /// <inheritdoc cref="WriteCutterRadiusShapeOffset(int, double)" />
    public async Task<OperateResult> WriteCutterRadiusSharpOffsetAsync(int cutter, double offset)
    {
        return await WriteSystemMacroValueAsync(13000 + cutter, new[] {offset});
    }

    /// <inheritdoc cref="WriteCutterRadiusWearOffset(int, double)" />
    public async Task<OperateResult> WriteCutterRadiusWearOffsetAsync(int cutter, double offset)
    {
        return await WriteSystemMacroValueAsync(12000 + cutter, new[] {offset});
    }

    /// <summary>
    ///     读取伺服负载
    /// </summary>
    /// <returns>轴负载</returns>
    public async Task<OperateResult<int>> ReadFanucAxisLoadAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x56, 1, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);


        var result = read.Content.RemoveBegin(68);

        return OperateResult.CreateSuccessResult((int) ByteTransform.TransInt16(result, 0));
    }

    // /// <inheritdoc cref="ReadFanucAxisLoad" />
    // public async Task<OperateResult<double[]>> ReadFanucAxisLoadAsync()
    // {
    //     var read = await ReadFromCoreServerAsync(BuildReadArray(
    //         BuildReadSingle(0xA4, 2, 0, 0, 0, 0),
    //         BuildReadSingle(0x89, 0, 0, 0, 0, 0),
    //         BuildReadSingle(0x56, 1, 0, 0, 0, 0),
    //         BuildReadSingle(0xA4, 2, 0, 0, 0, 0)
    //     ));
    //     if (!read.IsSuccess) return OperateResult.CreateFailedResult<double[]>(read);
    //
    //     var result = ExtraContentArray(read.Content.RemoveBegin(10));
    //     int length = ByteTransform.TransUInt16(result[0], 14);
    //
    //     return OperateResult.CreateSuccessResult(GetFanucDouble(result[2], 14, length));
    // }

    /// <inheritdoc cref="ReadSysAllCoors" />
    public async Task<OperateResult<SysAllCoors>> ReadSysAllCoorsAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x19, 0, 0, 0, 0, 0),
            BuildReadSingle(0x26, 0, -1, 0, 0, 0),
            BuildReadSingle(0x26, 1, -1, 0, 0, 0),
            BuildReadSingle(0x26, 2, -1, 0, 0, 0),
            BuildReadSingle(0x26, 3, -1, 0, 0, 0),
            BuildReadSingle(0x89, -1, 0, 0, 0, 0),
            BuildReadSingle(0x0e, 0x0c2b, 0x0c2b, -1, 0, 0),
            BuildReadSingle(0x88, 2, 0, 0, 0, 0),
            BuildReadSingle(0x19, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysAllCoors>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        int length = ByteTransform.TransUInt16(result[0], 14);

        var allCoors = new SysAllCoors();

        allCoors.Absolute = GetFanucDouble(result[1], 14, length);
        allCoors.Machine = GetFanucDouble(result[2], 14, length);
        allCoors.Relative = GetFanucDouble(result[3], 14, length);

        return OperateResult.CreateSuccessResult(allCoors);
    }


    /// <inheritdoc cref="ReadSystemAlarm" />
    public async Task<OperateResult<SysAlarm[]>> ReadSystemAlarmAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x23, -1, 10, 2, 64, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysAlarm[]>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        if (ByteTransform.TransUInt16(result[0], 12) > 0)
        {
            var length = ByteTransform.TransUInt16(result[0], 12) / 80;
            var alarms = new SysAlarm[length];
            for (var i = 0; i < alarms.Length; i++)
            {
                alarms[i] = new SysAlarm();
                alarms[i].AlarmId = ByteTransform.TransInt32(result[0], 14 + 80 * i);
                alarms[i].Type = ByteTransform.TransInt16(result[0], 20 + 80 * i);
                alarms[i].Axis = ByteTransform.TransInt16(result[0], 24 + 80 * i);

                var msgLength = ByteTransform.TransUInt16(result[0], 28 + 80 * i);
                alarms[i].Message = TextEncoding.GetString(result[0], 30 + 80 * i, msgLength);
            }

            return OperateResult.CreateSuccessResult(alarms);
        }

        return OperateResult.CreateSuccessResult(new SysAlarm[0]);
    }


    /// <summary>
    ///     读取fanuc机床的运行时间返回秒为单位的信息
    /// </summary>
    /// <returns>秒为单位的结果</returns>
    public async Task<OperateResult<long>> ReadRunTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x0e, 0x1a60, 0x1a60, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<long>(read);
        var result = read.Content.RemoveBegin(36);
        return OperateResult.CreateSuccessResult((long) ByteTransform.TransInt32(result, 0) * 60);
    }


    /// <summary>
    ///     读取fanuc机床的开机时间返回秒为单位的信息
    /// </summary>
    /// <returns>秒为单位的结果</returns>
    public async Task<OperateResult<long>> ReadAliveTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x0e, 0x1a5e, 0x1a5e, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<long>(read);
        var result = read.Content.RemoveBegin(36);
        return OperateResult.CreateSuccessResult((long) ByteTransform.TransInt32(result, 0) * 60);
    }


    /// <summary>
    ///     读取fanuc机床的切削时间返回秒为单位的信息
    /// </summary>
    /// <returns>秒为单位的结果</returns>
    public async Task<OperateResult<long>> ReadCutTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x0e, 0x1a62, 0x1a62, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<long>(read);
        var result = read.Content.RemoveBegin(36);
        return OperateResult.CreateSuccessResult((long) ByteTransform.TransInt32(result, 0) * 60);
    }


    /// <summary>
    ///     读取fanuc机床的循环时间返回秒为单位的信息
    /// </summary>
    /// <returns>秒为单位的结果</returns>
    public async Task<OperateResult<long>> ReadCycleTimeAsync()
    {
        //获取循环时间的分钟数
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x0e, 0x1a66, 0x1a66, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<long>(read);
        var result = read.Content.RemoveBegin(36);
        //获取循环时间的秒数
        var read1 = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x0e, 0x1a65, 0x1a65, 0, 0, 0)
        ));
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<long>(read1);
        var result1 = read1.Content.RemoveBegin(36);
        long cycletime = ByteTransform.TransInt32(result, 0) * 60 + ByteTransform.TransInt32(result1, 0);
        return OperateResult.CreateSuccessResult(cycletime);
    }


    /// <inheritdoc cref="ReadAlarmStatus" />
    public async Task<OperateResult<int>> ReadAlarmStatusAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x1A, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        return OperateResult.CreateSuccessResult((int) ByteTransform.TransUInt16(result[0], 16));
    }

    /// <inheritdoc cref="ReadSysStatusInfo" />
    public async Task<OperateResult<SysStatusInfo>> ReadSysStatusInfoAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x19, 0, 0, 0, 0, 0),
            BuildReadSingle(0xE1, 0, 0, 0, 0, 0),
            BuildReadSingle(0x98, 0, 0, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<SysStatusInfo>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        var statusInfo = new SysStatusInfo();
        statusInfo.Dummy = result[1].Length >= 16 ? ByteTransform.TransInt16(result[1], 14) : (short) 0;
        //if (result[2].Length > 14)
        //{
        //    statusInfo.TMMode = ByteTransform.TransInt16(result[2], 14);
        //}
        statusInfo.TMMode = result[2].Length >= 16 ? ByteTransform.TransInt16(result[2], 14) : (short) 0;
        statusInfo.WorkMode = (CNCWorkMode) ByteTransform.TransInt16(result[0], 14);
        statusInfo.RunStatus = (CNCRunStatus) ByteTransform.TransInt16(result[0], 16);
        statusInfo.Motion = ByteTransform.TransInt16(result[0], 18);
        statusInfo.MSTB = ByteTransform.TransInt16(result[0], 20);
        statusInfo.Emergency = ByteTransform.TransInt16(result[0], 22);
        statusInfo.Alarm = ByteTransform.TransInt16(result[0], 24);
        statusInfo.Edit = ByteTransform.TransInt16(result[0], 26);

        return OperateResult.CreateSuccessResult(statusInfo);
    }

    /// <inheritdoc cref="ReadProgramList" />
    public async Task<OperateResult<int[]>> ReadProgramListAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x06, 0x01, 0x13, 0, 0, 0)
        ));
        var check = await ReadFromCoreServerAsync(BuildReadArray(
            BuildReadSingle(0x06, 0x1A0B, 0x13, 0, 0, 0)
        ));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int[]>(read);
        if (!check.IsSuccess) return OperateResult.CreateFailedResult<int[]>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        var length = (result[0].Length - 14) / 72;
        var programs = new int[length];
        for (var i = 0; i < length; i++) programs[i] = ByteTransform.TransInt32(result[0], 14 + 72 * i);
        return OperateResult.CreateSuccessResult(programs);
    }

    /// <inheritdoc cref="ReadCutterInfos(int)" />
    public async Task<OperateResult<CutterInfo[]>> ReadCutterInfosAsync(int cutterNumber = 24)
    {
        var read1 = await ReadFromCoreServerAsync(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 0, 0, 0)));
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read1);

        var read2 = await ReadFromCoreServerAsync(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 1, 0, 0)));
        if (!read2.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read2);

        var read3 = await ReadFromCoreServerAsync(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 2, 0, 0)));
        if (!read3.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read3);

        var read4 = await ReadFromCoreServerAsync(BuildReadArray(BuildReadSingle(0x08, 1, cutterNumber, 3, 0, 0)));
        if (!read4.IsSuccess) return OperateResult.CreateFailedResult<CutterInfo[]>(read4);

        return ExtraCutterInfos(read1.Content, read2.Content, read3.Content, read4.Content, cutterNumber);
    }

    /// <inheritdoc cref="ReadData(int, int, int)" />
    public async Task<OperateResult<byte[]>> ReadDataAsync(int code, int start, int end)
    {
        var read1 = await ReadFromCoreServerAsync(BuildReadArray(BuildReadMulti(0x02, 0x8001, start, end, code, 0, 0)));
        if (!read1.IsSuccess) return read1;

        var result = ExtraContentArray(read1.Content.RemoveBegin(10))[0];

        var checkResult = CheckSingleResultLeagle(result);
        if (!checkResult.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(checkResult);

        int length = ByteTransform.TransUInt16(result, 12);
        return OperateResult.CreateSuccessResult(result.SelectMiddle(14, length));
    }

    /// <inheritdoc cref="WriteData(int, int, byte[])" />
    public async Task<OperateResult> WriteDataAsync(int code, int start, byte[] data)
    {
        if (data == null) data = new byte[0];
        var read = await ReadFromCoreServerAsync(BuildReadArray(BuildWriteSingle(0x02, 0x8002, start, start + data.Length - 1, code, 0, data)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string, int>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
        return CheckSingleResultLeagle(result);
    }

    /// <inheritdoc cref="ReadPMCData(string, ushort)" />
    public async Task<OperateResult<byte[]>> ReadPMCDataAsync(string address, ushort length)
    {
        var analysis = FanucPmcAddress.ParseFrom(address, length);
        if (!analysis.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(analysis);

        return await ReadDataAsync(analysis.Content.DataCode, analysis.Content.AddressStart, analysis.Content.AddressEnd);
    }

    /// <inheritdoc cref="WritePMCData(string, byte[])" />
    public async Task<OperateResult> WritePMCDataAsync(string address, byte[] value)
    {
        var analysis = FanucPmcAddress.ParseFrom(address, 1);
        if (!analysis.IsSuccess) return OperateResult.CreateFailedResult<byte[]>(analysis);

        return await WriteDataAsync(analysis.Content.DataCode, analysis.Content.AddressStart, value);
    }

    /// <inheritdoc cref="ReadDeviceWorkPiecesSize" />
    public async Task<OperateResult<double[]>> ReadDeviceWorkPiecesSizeAsync()
    {
        return await ReadSystemMacroValueAsync(601, 20);
    }


    /// <inheritdoc cref="ReadCurrentForegroundDir" />
    public async Task<OperateResult<string>> ReadCurrentForegroundDirAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadArray(BuildReadSingle(0xB0, 1, 0, 0, 0, 0)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10));
        var index = 0;
        for (var i = 14; i < result[0].Length; i++)
            if (result[0][i] == 0x00)
            {
                index = i;
                break;
            }

        if (index == 0) index = result[0].Length;
        return OperateResult.CreateSuccessResult(Encoding.ASCII.GetString(result[0], 14, index - 14));
    }

    /// <inheritdoc cref="SetDeviceProgsCurr(string)" />
    /// <inheritdoc cref="SetDeviceProgsCurr(string)" />
    public async Task<OperateResult> SetDeviceProgsCurrAsync(string programName)
    {
        var path = await ReadCurrentForegroundDirAsync();
        if (!path.IsSuccess) return path;

        var buffer = new byte[256];
        Encoding.ASCII.GetBytes(path.Content + programName).CopyTo(buffer, 0);

        var read = await ReadFromCoreServerAsync(BuildReadArray(BuildWriteSingle(0x01, 0xBA, 0, 0, 0, 0, buffer)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
        return CheckSingleResultLeagle(result);
    }

    /// <inheritdoc cref="ReadCurrentDateTime" />
    public async Task<OperateResult<DateTime>> ReadCurrentDateTimeAsync()
    {
        var read1 = await ReadSystemMacroValueAsync(3011);
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<DateTime>(read1);

        var read2 = await ReadSystemMacroValueAsync(3012);
        if (!read2.IsSuccess) return OperateResult.CreateFailedResult<DateTime>(read2);

        var date = Convert.ToInt32(read1.Content).ToString();
        var time = Convert.ToInt32(read2.Content).ToString().PadLeft(6, '0');

        return OperateResult.CreateSuccessResult(new DateTime(
            int.Parse(date.Substring(0, 4)), int.Parse(date.Substring(4, 2)), int.Parse(date.Substring(6)),
            int.Parse(time.Substring(0, 2)), int.Parse(time.Substring(2, 2)), int.Parse(time.Substring(4))));
    }

    /// <inheritdoc cref="ReadCurrentProduceCount" />
    public async Task<OperateResult<int>> ReadCurrentProduceCountAsync()
    {
        var read = await ReadSystemMacroValueAsync(3901);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        return OperateResult.CreateSuccessResult(Convert.ToInt32(read.Content));
    }

    /// <inheritdoc cref="ReadExpectProduceCount" />
    public async Task<OperateResult<int>> ReadExpectProduceCountAsync()
    {
        var read = await ReadSystemMacroValueAsync(3902);
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

        return OperateResult.CreateSuccessResult(Convert.ToInt32(read.Content));
    }

    /// <inheritdoc cref="ReadCurrentProgram" />
    public async Task<OperateResult<string>> ReadCurrentProgramAsync()
    {
        var read = await ReadFromCoreServerAsync(
            BuildReadArray(BuildReadSingle(0x20, 0x0594, 0x00, 0x00, 0x00, 0x00)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
        return OperateResult.CreateSuccessResult(Encoding.ASCII.GetString(result, 18, result.Length - 18));
    }

    /// <inheritdoc cref="SetCurrentProgram(ushort)" />
    public async Task<OperateResult> SetCurrentProgramAsync(ushort programNum)
    {
        var read = await ReadFromCoreServerAsync(
            BuildReadArray(BuildReadSingle(0x03, programNum, 0, 0, 0, 0)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, string>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
        var err = ByteTransform.TransInt16(result, 6);

        if (err == 0) return OperateResult.CreateSuccessResult();
        return new OperateResult(err, StringResources.Language.UnknownError);
    }

    /// <inheritdoc cref="StartProcessing" />
    public async Task<OperateResult> StartProcessingAsync()
    {
        var read = await ReadFromCoreServerAsync(
            BuildReadArray(BuildReadSingle(0x01, 0, 0, 0, 0, 0)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, string>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
        var err = ByteTransform.TransInt16(result, 6);

        if (err == 0) return OperateResult.CreateSuccessResult();
        return new OperateResult(err, StringResources.Language.UnknownError);
    }

    /// <inheritdoc cref="WriteProgramFile(string)" />
    public async Task<OperateResult> WriteProgramFileAsync(string file)
    {
        var content = File.ReadAllText(file);
        return await WriteProgramContentAsync(content);
    }

    /// <inheritdoc cref="WriteProgramContent(string, int)" />
    public async Task<OperateResult> WriteProgramContentAsync(string program, int everyWriteSize = 512)
    {
        var socket = await CreateSocketAndConnectAsync(IpAddress, Port, ConnectTimeOut);
        if (!socket.IsSuccess) return socket.ConvertFailed<int>();

        var ini1 = await ReadFromCoreServerAsync(socket.Content, "a0 a0 a0 a0 00 01 01 01 00 02 00 01".ToHexBytes());
        if (!ini1.IsSuccess) return ini1;

        var read1 = await ReadFromCoreServerAsync(socket.Content, BulidWriteProgramFilePre());
        if (!read1.IsSuccess) return read1;

        var contents = BulidWriteProgram(Encoding.ASCII.GetBytes(program), everyWriteSize);
        for (var i = 0; i < contents.Count; i++)
        {
            var read2 = await ReadFromCoreServerAsync(socket.Content, contents[i], false);
            if (!read2.IsSuccess) return read2;
        }

        var read3 = await ReadFromCoreServerAsync(socket.Content, new byte[] {0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x13, 0x01, 0x00, 0x00});
        if (!read3.IsSuccess) return read3;

        socket.Content?.Close();
        if (read3.Content.Length >= 14)
        {
            int err = ByteTransform.TransInt16(read3.Content, 12);
            if (err != 0) return new OperateResult<string>(err, StringResources.Language.UnknownError);
        }

        return OperateResult.CreateSuccessResult();
    }

    /// <inheritdoc cref="ReadProgram(int)" />
    public async Task<OperateResult<string>> ReadProgramAsync(int program)
    {
        var socket = await CreateSocketAndConnectAsync(IpAddress, Port, ConnectTimeOut);
        if (!socket.IsSuccess) return socket.ConvertFailed<string>();

        var ini1 = await ReadFromCoreServerAsync(socket.Content, "a0 a0 a0 a0 00 01 01 01 00 02 00 01".ToHexBytes());
        if (!ini1.IsSuccess) return OperateResult.CreateFailedResult<string>(ini1);

        var read1 = await ReadFromCoreServerAsync(socket.Content, BuildReadProgramPre(program));
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<string>(read1);

        // 检测错误信息
        var err = read1.Content[12] * 256 + read1.Content[13];
        if (err != 0)
        {
            socket.Content?.Close();
            return new OperateResult<string>(err, StringResources.Language.UnknownError);
        }

        var sb = new StringBuilder();
        while (true)
        {
            var read2 = await ReadFromCoreServerAsync(socket.Content, null);
            if (!read2.IsSuccess) return OperateResult.CreateFailedResult<string>(read2);

            if (read2.Content[6] == 0x16)
                sb.Append(Encoding.ASCII.GetString(read2.Content, 10, read2.Content.Length - 10));
            else if (read2.Content[6] == 0x17)
                break;
        }

        var send = await SendAsync(socket.Content, new byte[] {0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x17, 0x02, 0x00, 0x00});
        if (!send.IsSuccess) return OperateResult.CreateFailedResult<string>(send);

        socket.Content?.Close();
        return OperateResult.CreateSuccessResult(sb.ToString());
    }

    /// <inheritdoc cref="DeleteProgram(int)" />
    public async Task<OperateResult> DeleteProgramAsync(int program)
    {
        var read = await ReadFromCoreServerAsync(
            BuildReadArray(BuildReadSingle(0x05, program, 0, 0, 0, 0)));
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, string>(read);

        var result = ExtraContentArray(read.Content.RemoveBegin(10))[0];
        var err = ByteTransform.TransInt16(result, 6);

        if (err == 0) return OperateResult.CreateSuccessResult();
        return new OperateResult(err, StringResources.Language.UnknownError);
    }

#endif

    #endregion

    #region Build Command

    /// <summary>
    ///     构建读取一个命令的数据内容
    /// </summary>
    /// <param name="code">命令码</param>
    /// <param name="a">第一个参数内容</param>
    /// <param name="b">第二个参数内容</param>
    /// <param name="c">第三个参数内容</param>
    /// <param name="d">第四个参数内容</param>
    /// <param name="e">第五个参数内容</param>
    /// <returns>总报文信息</returns>
    public byte[] BuildReadSingle(ushort code, int a, int b, int c, int d, int e)
    {
        var buffer = new byte[28];
        buffer[1] = 0x1C;
        buffer[3] = 0x01;
        buffer[5] = 0x01; //通道号
        var f = ByteTransform.TransByte(code);
        f.CopyTo(buffer, 6);
        ByteTransform.TransByte(a).CopyTo(buffer, 8);
        ByteTransform.TransByte(b).CopyTo(buffer, 12);
        ByteTransform.TransByte(c).CopyTo(buffer, 16);
        ByteTransform.TransByte(d).CopyTo(buffer, 20);
        ByteTransform.TransByte(e).CopyTo(buffer, 24);
        return buffer;
    }

    /// <summary>
    ///     构建读取多个命令的数据内容
    /// </summary>
    /// <param name="mode">模式</param>
    /// <param name="code">命令码</param>
    /// <param name="a">第一个参数内容</param>
    /// <param name="b">第二个参数内容</param>
    /// <param name="c">第三个参数内容</param>
    /// <param name="d">第四个参数内容</param>
    /// <param name="e">第五个参数内容</param>
    /// <returns>总报文信息</returns>
    private byte[] BuildReadMulti(ushort mode, ushort code, int a, int b, int c, int d, int e)
    {
        var buffer = new byte[28];
        buffer[1] = 0x1C;
        ByteTransform.TransByte(mode).CopyTo(buffer, 2);
        buffer[5] = 0x01;
        ByteTransform.TransByte(code).CopyTo(buffer, 6);
        ByteTransform.TransByte(a).CopyTo(buffer, 8);
        ByteTransform.TransByte(b).CopyTo(buffer, 12);
        ByteTransform.TransByte(c).CopyTo(buffer, 16);
        ByteTransform.TransByte(d).CopyTo(buffer, 20);
        ByteTransform.TransByte(e).CopyTo(buffer, 24);
        return buffer;
    }

    /// <summary>
    ///     创建写入byte[]数组的报文信息
    /// </summary>
    /// <param name="mode">模式</param>
    /// <param name="code">命令码</param>
    /// <param name="a">第一个参数内容</param>
    /// <param name="b">第二个参数内容</param>
    /// <param name="c">第三个参数内容</param>
    /// <param name="d">第四个参数内容</param>
    /// <param name="data">等待写入的byte数组信息</param>
    /// <returns>总报文信息</returns>
    private byte[] BuildWriteSingle(ushort mode, ushort code, int a, int b, int c, int d, byte[] data)
    {
        var buffer = new byte[28 + data.Length];
        ByteTransform.TransByte((ushort) buffer.Length).CopyTo(buffer, 0);
        ByteTransform.TransByte(mode).CopyTo(buffer, 2);
        buffer[5] = 0x01;
        ByteTransform.TransByte(code).CopyTo(buffer, 6);
        ByteTransform.TransByte(a).CopyTo(buffer, 8);
        ByteTransform.TransByte(b).CopyTo(buffer, 12);
        ByteTransform.TransByte(c).CopyTo(buffer, 16);
        ByteTransform.TransByte(d).CopyTo(buffer, 20);
        ByteTransform.TransByte(data.Length).CopyTo(buffer, 24);
        if (data.Length > 0) data.CopyTo(buffer, 28);
        return buffer;
    }

    public byte[] BuildWriteByte(ushort code, int a, int b, int c, int d, byte[] data)
    {
        var buffer = new byte[28 + data.Length];
        ByteTransform.TransByte((ushort) buffer.Length).CopyTo(buffer, 0);
        buffer[3] = 0x02;
        buffer[5] = 0x01;
        ByteTransform.TransByte(code).CopyTo(buffer, 6);
        ByteTransform.TransByte(a).CopyTo(buffer, 8);
        ByteTransform.TransByte(b).CopyTo(buffer, 12);
        ByteTransform.TransByte(c).CopyTo(buffer, 16);
        ByteTransform.TransByte(d).CopyTo(buffer, 20);
        ByteTransform.TransByte(data.Length).CopyTo(buffer, 24);
        if (data.Length > 0) data.CopyTo(buffer, 28);
        return buffer;
    }


    /// <summary>
    ///     创建写入单个double数组的报文信息
    /// </summary>
    /// <param name="code">功能码</param>
    /// <param name="a">第一个参数内容</param>
    /// <param name="b">第二个参数内容</param>
    /// <param name="c">第三个参数内容</param>
    /// <param name="d">第四个参数内容</param>
    /// <param name="data">等待写入的double数组信息</param>
    /// <returns>总报文信息</returns>
    private byte[] BuildWriteSingle(ushort code, int a, int b, int c, int d, double[] data)
    {
        var buffer = new byte[data.Length * 8];
        for (var i = 0; i < data.Length; i++) CreateFromFanucDouble(data[i]).CopyTo(buffer, 0);
        return BuildWriteSingle(0x01, code, a, b, c, d, buffer);
    }


    private byte[] BulidWriteProgramFilePre()
    {
        var ms = new MemoryStream();
        ms.Write(new byte[] {0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x11, 0x01, 0x02, 0x04}, 0, 10);
        ms.Write(new byte[] {0x00, 0x00, 0x00, 0x01}, 0, 4);
        for (var i = 0; i < 512; i++) ms.WriteByte(0x00);
        return ms.ToArray();
    }

    /// <summary>
    ///     创建读取运行程序的报文信息
    /// </summary>
    /// <param name="program">程序号</param>
    /// <returns>总报文</returns>
    private byte[] BuildReadProgramPre(int program)
    {
        var ms = new MemoryStream();
        ms.Write(new byte[] {0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x15, 0x01, 0x02, 0x04}, 0, 10);
        ms.Write(new byte[] {0x00, 0x00, 0x00, 0x01}, 0, 4);
        for (var i = 0; i < 512; i++) ms.WriteByte(0x00);
        var buffer = ms.ToArray();
        var pro = "O" + program + "-" + "O" + program;
        Encoding.ASCII.GetBytes(pro).CopyTo(buffer, 14);
        return buffer;
    }

    private List<byte[]> BulidWriteProgram(byte[] program, int everyWriteSize)
    {
        var list = new List<byte[]>();
        var lengths = SoftBasic.SplitIntegerToArray(program.Length, everyWriteSize);
        var index = 0;
        for (var i = 0; i < lengths.Length; i++)
        {
            var ms = new MemoryStream();
            ms.Write(new byte[] {0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x12, 0x04, 0x00, 0x00}, 0, 10);
            ms.Write(program, index, lengths[i]);
            var buffer = ms.ToArray();
            ByteTransform.TransByte((ushort) (buffer.Length - 10)).CopyTo(buffer, 8);

            list.Add(buffer);
            index += lengths[i];
        }

        return list;
    }


    /// <summary>
    ///     产量清零报文
    /// </summary>
    /// <returns></returns>
    public byte[] BuildProductZero()
    {
        var buffer = @"
  0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01,
  0x01, 0x26, 0x00, 0x01, 0x01, 0x24, 0x00, 0x01,
  0x00, 0x01, 0x00, 0x8e, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x08,
  0x00, 0x00, 0x1a, 0x37, 0x00, 0x00, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff,
  0x00, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00,
  0x02, 0x00, 0x00, 0x00, 0xe8, 0xd9, 0xaf, 0x00,
  0x00, 0x01, 0x01, 0x01, 0x5c, 0xd9, 0xaf, 0x00,
  0x74, 0x09, 0xfe, 0x63, 0x28, 0xe2, 0x41, 0x05,
  0x02, 0x00, 0x00, 0x00, 0x18, 0xdb, 0xc8, 0x00,
  0x30, 0x09, 0xfe, 0x63, 0xe0, 0xd9, 0xaf, 0x00,
  0x84, 0x32, 0x09, 0x64, 0x28, 0xe2, 0x41, 0x05,
  0x02, 0x00, 0x00, 0x00, 0x80, 0xd9, 0xaf, 0x00,
  0x98, 0xd9, 0xaf, 0x00, 0x00, 0x09, 0xfe, 0x63,
  0xc8, 0xdc, 0xaf, 0x00, 0x00, 0xdc, 0xaf, 0x00,
  0xe0, 0xd9, 0xaf, 0x00, 0x68, 0xdc, 0xaf, 0x00,
  0x18, 0xdc, 0xaf, 0x00, 0xe0, 0xd9, 0xaf, 0x00,
  0x03, 0x0d, 0xfe, 0x63, 0x00, 0x00, 0x00, 0x00,
  0x02, 0x00, 0x00, 0x00, 0xa4, 0xd9, 0xaf, 0x00,
  0x97, 0xac, 0x07, 0x64, 0x00, 0xdc, 0xaf, 0x00,
  0x84, 0x00, 0x00, 0x00, 0xb8, 0xd9, 0xc8, 0x00,
  0x10, 0xfc, 0x38, 0x64, 0x1b, 0xfc, 0x38, 0x64,
  0xb8, 0xd9, 0xaf, 0x00, 0x5e, 0x06, 0x53, 0x64,
  0xe0, 0xd9, 0xaf, 0x00, 0xa8, 0x19, 0xca, 0x00,
  0xf4, 0x1c, 0xfd, 0x63, 0x01, 0x00, 0x00, 0x00,
  0x80, 0xdc, 0xaf, 0x00, 0xa8, 0x19, 0xca, 0x00,
  0xce, 0x07, 0xfe, 0x63, 0xb8, 0xd9, 0xc8, 0x00,
  0xe0, 0x07, 0xfe, 0x63, 0x00, 0x00, 0x00, 0x00,
  0xc7, 0xdc, 0xaf, 0x00, 0x20, 0x06, 0x53, 0x64,
  0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
  0x0d, 0x00, 0x00, 0x00, 0x60, 0x01, 0x00, 0x00,
  0x08, 0xda, 0xaf, 0x00, 0x1c, 0x00, 0x00, 0x00,
  0x98, 0xd9, 0xce, 0x00, 0x30, 0xb4, 0xfd, 0x00,
  0x5c, 0xda, 0xaf, 0x00, 0x38, 0x00, 0xee, 0x00,
  0x2c, 0xda, 0xaf, 0x00, 0xb7, 0x7e, 0x0b, 0x64,
  0x28, 0xe2, 0x41, 0x05, 0x02, 0x00, 0x00, 0x00
".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     读取进给倍率报文
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadFeedRate()
    {
        var buffer = @"
                            0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01,
                            0x00, 0x1e, 0x00, 0x01, 0x00, 0x1c, 0x00, 0x02,
                            0x00, 0x01, 0x80, 0x01, 0x00, 0x00, 0x00, 0x0c,
                            0x00, 0x00, 0x00, 0x0d, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00

".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     读取主轴倍率报文
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSpindleRate()
    {
        var buffer = @"
                            0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01,
                            0x00, 0x1e, 0x00, 0x01, 0x00, 0x1c, 0x00, 0x02,
                            0x00, 0x01, 0x80, 0x01, 0x00, 0x00, 0x00, 0x1e,
                            0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     读取进给速度报文
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActfSpeed()
    {
        var buffer = @"
                            0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01,
                            0x00, 0x1e, 0x00, 0x01, 0x00, 0x1c, 0x00, 0x01,
                            0x00, 0x01, 0x00, 0x24, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00

                           ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     读取主轴电机温度报文
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadStemper()
    {
        var buffer = @"
                            0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01,
                            0x00, 0x1e, 0x00, 0x01, 0x00, 0x1c, 0x00, 0x01,
                            0x00, 0x01, 0x00, 0x93, 0x00, 0x00, 0x01, 0x93,
                            0x00, 0x00, 0x01, 0x93, 0x00, 0x00, 0x00, 0x01,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00

                           ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     读取伺服电机温度报文
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadFtemper()
    {
        // 报文第4-8行从0x03->0x01
        var buffer = @"
                             0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01,
                             0x00, 0x1e, 0x00, 0x01, 0x00, 0x1c, 0x00, 0x01,
                             0x00, 0x01, 0x00, 0x93, 0x00, 0x00, 0x01, 0x34,
                             0x00, 0x00, 0x01, 0x34, 0x00, 0x00, 0x00, 0x01,
                             0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
                           ".ToHexBytes();

        return buffer;
    }

    public byte[] BuildReadSload()
    {
        var buffer = @"
                            0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01,
                            0x00, 0x72, 0x00, 0x04, 0x00, 0x1c, 0x00, 0x01,
                            0x00, 0x01, 0x00, 0xa4, 0x00, 0x00, 0x00, 0x01,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x1c, 0x00, 0x01, 0x00, 0x01, 0x00, 0x8a,
                            0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x01,
                            0x00, 0x01, 0x00, 0x40, 0x00, 0x00, 0x00, 0x04,
                            0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x1c, 0x00, 0x01, 0x00, 0x01, 0x00, 0xa4,
                            0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                             0x00, 0x00, 0x00, 0x00

                           ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     读取刀具号报文
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadToolnum()
    {
        var buffer = @"
                             0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01,
                             0x00, 0x1e, 0x00, 0x01, 0x00, 0x1c, 0x00, 0x01,
                             0x00, 0x01, 0x00, 0x22, 0x00, 0x00, 0x00, 0x16,
                             0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                             0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00

                           ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     创建多个命令报文的总报文信息
    /// </summary>
    /// <param name="commands">报文命令的数组</param>
    /// <returns>总报文信息</returns>
    public byte[] BuildReadArray(params byte[][] commands)
    {
        var ms = new MemoryStream();
        ms.Write(new byte[] {0xa0, 0xa0, 0xa0, 0xa0, 0x00, 0x01, 0x21, 0x01, 0x00, 0x1e}, 0, 10);
        var buffer2 = ms.ToArray();
        ms.Write(ByteTransform.TransByte((ushort) commands.Length), 0, 2);
        var buffer1 = ms.ToArray();
        for (var i = 0; i < commands.Length; i++) ms.Write(commands[i], 0, commands[i].Length);
        var buffer = ms.ToArray();
        ByteTransform.TransByte((ushort) (buffer.Length - 10)).CopyTo(buffer, 8);
        return buffer;
    }


    /// <summary>
    ///     从机床返回的数据里解析出实际的数据内容，去除了一些多余的信息报文。
    /// </summary>
    /// <param name="content">返回的报文信息</param>
    /// <returns>解析之后的报文信息</returns>
    public List<byte[]> ExtraContentArray(byte[] content)
    {
        var list = new List<byte[]>();
        int count = ByteTransform.TransUInt16(content, 0);
        var index = 2;

        for (var i = 0; i < count; i++)
        {
            var length = ByteTransform.TransUInt16(content, index);
            list.Add(content.SelectMiddle(index + 2, length - 2));
            index += length;
        }

        return list;
    }

    private OperateResult<CutterInfo[]> ExtraCutterInfos(byte[] content1, byte[] content2, byte[] content3, byte[] content4, int cutterNumber)
    {
        // 先提取出各个数据信息
        var result1 = ExtraContentArray(content1.RemoveBegin(10));
        var result2 = ExtraContentArray(content2.RemoveBegin(10));
        var result3 = ExtraContentArray(content3.RemoveBegin(10));
        var result4 = ExtraContentArray(content4.RemoveBegin(10));

        // 校验数据是否有效
        var check1 = ByteTransform.TransInt16(result1[0], 6) == 0x00;
        var check2 = ByteTransform.TransInt16(result2[0], 6) == 0x00;
        var check3 = ByteTransform.TransInt16(result3[0], 6) == 0x00;
        var check4 = ByteTransform.TransInt16(result4[0], 6) == 0x00;

        // 如果数据有效，则显示出来
        var cutters = new CutterInfo[cutterNumber];
        for (var i = 0; i < cutters.Length; i++)
        {
            cutters[i] = new CutterInfo();
            cutters[i].LengthSharpOffset = check1 ? GetFanucDouble(result1[0], 14 + 8 * i) : double.NaN;
            cutters[i].LengthWearOffset = check2 ? GetFanucDouble(result2[0], 14 + 8 * i) : double.NaN;
            cutters[i].RadiusSharpOffset = check3 ? GetFanucDouble(result3[0], 14 + 8 * i) : double.NaN;
            cutters[i].RadiusWearOffset = check4 ? GetFanucDouble(result4[0], 14 + 8 * i) : double.NaN;
        }

        return OperateResult.CreateSuccessResult(cutters);
    }

    private OperateResult CheckSingleResultLeagle(byte[] result)
    {
        var status = result[6] * 256 + result[7];
        if (status != 0x00) return new OperateResult<int>(status, StringResources.Language.UnknownError);

        return OperateResult.CreateSuccessResult();
    }

    #endregion

    #region Private Member

    #endregion
}