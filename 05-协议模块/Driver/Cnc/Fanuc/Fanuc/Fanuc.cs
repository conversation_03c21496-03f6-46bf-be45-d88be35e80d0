using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Common.Enums;
using Common.Extension;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Enum;
using Driver.Core.Models;
using DriversInterface;
using Feng.Common.Util;
using Furion.FriendlyException;
using Furion.JsonSerialization;
using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.CNC.Fanuc;
using FanucSeries0i = Fanuc.Base.FanucSeries0i;
using SysAllCoors = Fanuc.Base.SysAllCoors;

namespace Fanuc;

/* 更新日志
 *版本:v1.3.1
 *  1.调整 `伺服负载(AxisLoad)` 数据类型double改为string
 *  1.修复 `程序号数据类型错误`
 */
[DriverSupported("Fanuc")]
[DriverInfo("Fanuc", "V1.3.1", "发那科(Fanuc)")]
public class Fanuc : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 8193;

    [ConfigParameter("DNC管理", GroupName = "高级配置")]
    public BoolEnum Dnc { get; set; } = BoolEnum.True;

    [ConfigParameter("轴数量")] public int AxisCount { get; set; } = 3;

    #endregion

    public Fanuc(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    /// </summary>
    public override bool IsConnected => Driver != null && _driver.GetConnectStatus();

    private FanucSeries0i _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (FanucSeries0i)value;
    }


    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            _driver ??= new FanucSeries0i(IpAddress, Port)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout
            };
            OperateResult = _driver.ConnectServer();
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接失败:【{ex.Message}】" };
        }

        return ResConnect();
    }

    /// <summary>
    ///     清空数据
    /// </summary>
    /// <returns></returns>
    [Method("Clear", name: "Clear")]
    public Task Clear()
    {
        _statusInfo = null;
        _systemProgramCurrent = null;
        _sysAllCoors = null;
        _systemAlarm = null;
        _sysInfo = null;
        return Task.CompletedTask;
    }

    #region Dnc管理

    /// <summary>
    ///     路径信息
    /// </summary>
    /// <returns></returns>
    [Method("FileRouteInfo", description: "路径信息", name: "FileRouteInfo")]
    public override async Task<object> FileRouteInfo(string path)
    {
        //路径信息集合返回
        var fileRouteInfo = new List<FileRouteInfoOutput>();
        if (path == "")
            path = "//CNC_MEM/";
        var read = await _driver.ReadAllDirectoryAndFileAsync(path);
        if (read.IsSuccess)
            foreach (var fileDirInfo in read.Content)
            {
                var output = new FileRouteInfoOutput
                {
                    Name = fileDirInfo.Name,
                    IsDirectory = fileDirInfo.IsDirectory,
                    Size = (int)(fileDirInfo.Size / 1024.0) == 0 ? 1 : (int)(fileDirInfo.Size / 1024.0),
                    LastModified = fileDirInfo.LastModified,
                    Children = new List<FileRouteInfoOutput>(),
                    Path = path + fileDirInfo.Name + "/"
                };
                if (fileDirInfo.IsDirectory)
                    output.Children.AddRange(await FileRouteInfoRead(output.Path));
                fileRouteInfo.Add(output);
            }
        else
            throw Oops.Oh($"【读取路径信息】 失败:{read.ToMessageShowString()}");

        return await FileRouteInfoRead(path);
    }

    /// <summary>
    ///     组装文件结构
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    private async Task<List<FileRouteInfoOutput>> FileRouteInfoRead(string path)
    {
        try
        {
            var outputList = new List<FileRouteInfoOutput>();
            var read = await _driver.ReadAllDirectoryAndFileAsync(path);
            if (read.IsSuccess)
                foreach (var fileDirInfo in read.Content)
                {
                    var output = new FileRouteInfoOutput
                    {
                        Name = fileDirInfo.Name,
                        IsDirectory = fileDirInfo.IsDirectory,
                        Size = fileDirInfo.Size,
                        LastModified = fileDirInfo.LastModified,
                        Path = path + fileDirInfo.Name + "/"
                    };
                    if (fileDirInfo.IsDirectory)
                    {
                        var fileRouteInfoReadList = await FileRouteInfoRead(output.Path);
                        if (fileRouteInfoReadList.Any())
                        {
                            output.Children = new List<FileRouteInfoOutput>();
                            output.Children.AddRange(fileRouteInfoReadList);
                        }
                    }

                    outputList.Add(output);
                }
            else
                throw Oops.Oh($"【读取路径信息】 失败:{read.ToMessageShowString()}");

            return outputList;
        }
        catch (Exception e)
        {
            throw Oops.Oh($"【读取路径信息】 Error:{e.Message}");
        }
    }

    /// <summary>
    ///     将非标的程序号转换为程序可识别程序号
    /// </summary>
    /// <param name="programNumber"></param>
    /// <returns></returns>
    private ushort ConvertProgramNumber(string programNumber)
    {
        if (ushort.TryParse(programNumber, out var programNum)) return programNum;
        try
        {
            //安装协议规则强制替换O
            programNum = Convert.ToUInt16(programNumber.Replace("O", "").Replace("o", "").Trim());
        }
        catch
        {
            throw Oops.Oh("主程序号输入错误");
        }

        return programNum;
    }

    /// <summary>
    ///     将非标的地址转换为程序可识别地址
    /// </summary>
    /// <param name="programNumber"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    private string ConvertPath(string programNumber, string path)
    {
        if (!path.Contains(programNumber)) return path;
        var index = path.IndexOf(programNumber, StringComparison.Ordinal);
        var extractedString = path.Substring(0, index);
        return extractedString;
    }

    /// <summary>
    ///     读取程序内容
    /// </summary>
    /// <returns></returns>
    [Method("FileRead", description: "读取程序内容", name: "FileRead")]
    public override async Task<string> FileRead(FileReadInput input)
    {
        var programNum = ConvertProgramNumber(input.FileName);
        // 读取程序
        var path = ConvertPath(input.FileName, input.Path);
        var read = await _driver.ReadProgramAsync(programNum, path);
        if (read.IsSuccess)
            return read.Content;
        throw Oops.Oh(read.ToMessageShowString());
    }

    /// <summary>
    ///     删除程序内容
    /// </summary>
    /// <returns></returns>
    [Method("FileDel", description: "删除程序内容", name: "FileDel")]
    public override async Task<bool> FileDel(FileDelInput input)
    {
        if (input.Path.EndsWith("/"))
            input.Path = input.Path.TrimEnd('/');
        // 删除程序
        var read = await _driver.DeleteFileAsync(input.Path);
        if (read.IsSuccess) return true;
        throw Oops.Oh(read.ToMessageShowString());
    }

    /// <summary>
    ///     写入程序内容
    /// </summary>
    /// <returns></returns>
    [Method("FileWrite", description: "写入程序内容", name: "FileWrite")]
    public override async Task<bool> FileWrite(FileWriteInput input)
    {
        // 下载程序
        var write = await _driver.WriteProgramFileAsync(input.Content, 512, input.Path);
        if (write.IsSuccess)
            return true;
        throw Oops.Oh(write.Message);
    }

    /// <summary>
    ///     设置为主程序
    /// </summary>
    /// <returns></returns>
    [Method("SetCurrentProgram", description: "设置为主程序", name: "SetCurrentProgram")]
    public override async Task<bool> SetCurrentProgram(SetCurrentProgramInput input)
    {
        var programNum = ConvertProgramNumber(input.Name);
        // 设置为主程序
        var set = await _driver.SetCurrentProgramAsync(programNum);
        if (set.IsSuccess)
            return true;
        throw Oops.Oh(set.Message);
    }

    #endregion

    #region 采集点

    /// <summary>
    ///     读取模式,运行状态,报警状态
    /// </summary>
    private OperateResult<SysStatusInfo> _statusInfo;

    /// <summary>
    ///     读取程序名,程序号
    /// </summary>
    private OperateResult<string, int> _systemProgramCurrent;

    /// <summary>
    ///     读取绝对坐标,机械坐标,相对坐标
    /// </summary>
    private OperateResult<SysAllCoors> _sysAllCoors;

    /// <summary>
    ///     读取报警信息,报警Id,报警类型Id
    /// </summary>
    private OperateResult<SysAlarm[]> _systemAlarm;

    /// <summary>
    ///     读取系统信息
    /// </summary>
    private OperateResult<FanucSysInfo> _sysInfo;

    #region 读取模式,运行状态,报警状态

    /// <summary>
    ///     读取模式,运行状态,报警状态
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> SysStatusInfo(string identifier)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _statusInfo ??= await _driver.ReadSysStatusInfoAsync();
                if (!_statusInfo.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _statusInfo.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "WorkMode":
                            ret.Value = Convert.ToInt32(_statusInfo.Content.WorkMode);
                            break;
                        case "RunStatus":
                            ret.Value = Convert.ToInt32(_statusInfo.Content.RunStatus);
                            break;
                        case "AlarmStatus":
                            ret.Value = _statusInfo.Content.Alarm;
                            break;
                        case "Emergency":
                            ret.Value = _statusInfo.Content.Emergency;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     模式
    /// </summary>
    /// <returns></returns>
    [Method("WorkMode", description: "0:手动输入；1:自动循环；3:程序编辑；4:×100;5:连续进给；6:TeachInJog；7:TeachInHandle；8:InCfeed；9:ReFerence；10:ReMoTe", name: "模式")]
    public async Task<DriverReturnValueModel> ReadWorkMode()
    {
        return await SysStatusInfo("WorkMode");
    }

    /// <summary>
    ///     运行状态
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", description: "0:Reset；1:Stop；2:Hold；3:Start；4:Mstr；", name: "状态")]
    public async Task<DriverReturnValueModel> ReadRunStatus()
    {
        return await SysStatusInfo("RunStatus");
    }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    [Method("AlarmStatus", name: "报警状态")]
    public async Task<DriverReturnValueModel> ReadAlarmStatus()
    {
        return await SysStatusInfo("AlarmStatus");
    }

    /// <summary>
    ///     紧急停止状态
    /// </summary>
    /// <returns></returns>
    [Method("Emergency", name: "紧急停止状态", description: "0:正常；1:急停")]
    public async Task<DriverReturnValueModel> ReadEmergency()
    {
        return await SysStatusInfo("Emergency");
    }

    #endregion

    #region 读取程序名,程序号

    /// <summary>
    ///     读取程序名,程序号
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSystemProgramCurrent(string identifier)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                _systemProgramCurrent ??= await _driver.ReadSystemProgramCurrentAsync();
                if (!_systemProgramCurrent.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _systemProgramCurrent.Message;
                }
                else
                {
                    ret.Value = identifier switch
                    {
                        "MainName" => _systemProgramCurrent.Content1,
                        "CurPgm" => _systemProgramCurrent.Content2,
                        _ => ret.Value
                    };
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    [Method("MainName", TransPondDataTypeEnum.String, name: "程序名")]
    public async Task<DriverReturnValueModel> ReadMainName()
    {
        return await ReadSystemProgramCurrent("MainName");
    }

    /// <summary>
    ///     程序号
    /// </summary>
    /// <returns></returns>
    [Method("CurPgm", name: "程序号")]
    public async Task<DriverReturnValueModel> ReadCurPgm()
    {
        var output = await ReadSystemProgramCurrent("CurPgm");
        output.DataType = DataTypeEnum.Int32;
        return output;
    }

    #endregion

    #region 读取绝对坐标,机械坐标,相对坐标，剩余坐标

    /// <summary>
    ///     读取绝对坐标,机械坐标,相对坐标，剩余坐标
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSysAllCoorsAsync(string identifier)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                _sysAllCoors ??= await _driver.ReadSysAllCoorsAsync(AxisCount);
                if (!_sysAllCoors.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _sysAllCoors.Message;
                }
                else
                {
                    ret.Value = identifier switch
                    {
                        "AbsPos" => JSON.Serialize(_sysAllCoors.Content.Absolute),
                        "MachPos" => JSON.Serialize(_sysAllCoors.Content.Machine),
                        "RelPos" => JSON.Serialize(_sysAllCoors.Content.Relative),
                        "Remaining" => JSON.Serialize(_sysAllCoors.Content.Remaining),
                        _ => ret.Value
                    };
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     绝对坐标
    /// </summary>
    /// <returns></returns>
    [Method("AbsPos", TransPondDataTypeEnum.String, name: "绝对坐标")]
    public async Task<DriverReturnValueModel> ReadAbsPos()
    {
        return await ReadSysAllCoorsAsync("AbsPos");
    }

    /// <summary>
    ///     机械坐标
    /// </summary>
    /// <returns></returns>
    [Method("MachPos", TransPondDataTypeEnum.String, name: "机械坐标")]
    public async Task<DriverReturnValueModel> ReadMachPos()
    {
        return await ReadSysAllCoorsAsync("MachPos");
    }

    /// <summary>
    ///     相对坐标
    /// </summary>
    /// <returns></returns>
    [Method("RelPos", TransPondDataTypeEnum.String, name: "相对坐标")]
    public async Task<DriverReturnValueModel> ReadRelPos()
    {
        return await ReadSysAllCoorsAsync("MachPos");
    }

    /// <summary>
    ///     剩余坐标
    /// </summary>
    /// <returns></returns>
    [Method("Remaining", TransPondDataTypeEnum.String, name: "剩余坐标")]
    public async Task<DriverReturnValueModel> ReadRemaining()
    {
        return await ReadSysAllCoorsAsync("Remaining");
    }

    #endregion

    #region 读取报警信息,报警Id,报警类型Id

    /// <summary>
    ///     读取报警信息,报警Id,报警类型Id
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSystemAlarmAsync(string identifier)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                _systemAlarm ??= await _driver.ReadSystemAlarmAsync();
                if (!_systemAlarm.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _systemAlarm.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "Alarm":
                            ret.Value = JSON.Serialize(_systemAlarm.Content);
                            break;
                        case "AlarmId":
                        {
                            var content = _systemAlarm.Content.FirstOrDefault();
                            ret.Value = content != null ? content.AlarmId.ToString() : "";
                            break;
                        }
                        case "AlarmType":
                        {
                            var typeVal = "";
                            foreach (var item in _systemAlarm.Content)
                                if (typeVal.IsNotNull())
                                    typeVal = typeVal + "," + EnumUtil.GetEnumDesc((SysAlarmType)item.Type) + item.AlarmId;
                                else
                                    typeVal = EnumUtil.GetEnumDesc((SysAlarmType)item.Type) + item.AlarmId;
                            ret.Value = typeVal;
                            break;
                        }
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "Tcp连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     报警信息
    /// </summary>
    /// <returns></returns>
    [Method("Alarm", TransPondDataTypeEnum.String, name: "报警信息")]
    public async Task<DriverReturnValueModel> ReadAlarm()
    {
        return await ReadSystemAlarmAsync("Alarm");
    }

    /// <summary>
    ///     报警Id
    /// </summary>
    /// <returns></returns>
    [Method("AlarmId", TransPondDataTypeEnum.String, name: "报警Id")]
    public async Task<DriverReturnValueModel> ReadAlarmId()
    {
        return await ReadSystemAlarmAsync("AlarmId");
    }

    /// <summary>
    ///     报警类型Id
    /// </summary>
    /// <returns></returns>
    [Method("AlarmType", TransPondDataTypeEnum.String, name: "报警类型Id")]
    public async Task<DriverReturnValueModel> ReadAlarmType()
    {
        return await ReadSystemAlarmAsync("AlarmType");
    }

    #endregion


    /// <summary>
    ///     读取行号
    /// </summary>
    /// <returns></returns>
    [Method("ProgramNumber", name: "程序行号")]
    public async Task<DriverReturnValueModel> ReadProgramNumber()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadProgramNumberAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     产量
    /// </summary>
    /// <returns></returns>
    [Method("CurrentNum", name: "产量")]
    public async Task<DriverReturnValueModel> ReadCurrentNum()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadCurrentProduceCountAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴温度
    /// </summary>
    /// <returns></returns>
    [Method("SpinTemp", name: "主轴温度")]
    public async Task<DriverReturnValueModel> ReadSpinTemp()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadStemperAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     伺服温度
    /// </summary>
    /// <returns></returns>
    [Method("SvTemp", name: "伺服温度")]
    public async Task<DriverReturnValueModel> ReadSvTemp()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadFtemperAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     刀具号
    /// </summary>
    /// <returns></returns>
    [Method("ToolNum", name: "刀具号")]
    public async Task<DriverReturnValueModel> ReadToolNum()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadToolnumAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴倍率
    /// </summary>
    /// <returns></returns>
    [Method("OvSpin", name: "主轴倍率")]
    public async Task<DriverReturnValueModel> ReadOvSpin()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadSpindleRateAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给速度
    /// </summary>
    /// <returns></returns>
    [Method("ActFeed", name: "进给速度")]
    public async Task<DriverReturnValueModel> ReadActFeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadActfSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     设定进给速度
    /// </summary>
    /// <returns></returns>
    [Method("SetFSpeed", name: "进给设定速度")]
    public async Task<DriverReturnValueModel> ReadSetFSpeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadSetfSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴速度
    /// </summary>
    /// <returns></returns>
    [Method("sSpeed", TransPondDataTypeEnum.Double, name: "主轴速度")]
    public async Task<DriverReturnValueModel> ReadsSpeed()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Double
        };
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadSpindleSpeedAndFeedRateAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content1;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     设定主轴速度
    /// </summary>
    /// <returns></returns>
    [Method("SetsSpeed", name: "主轴设定速度")]
    public async Task<DriverReturnValueModel> ReadSetsSpeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadSetsSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给倍率
    /// </summary>
    /// <returns></returns>
    [Method("OvFeed", name: "进给倍率")]
    public async Task<DriverReturnValueModel> ReadOvFeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadFeedRateAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     伺服负载
    /// </summary>
    /// <returns></returns>
    [Method("AxisLoad", TransPondDataTypeEnum.String, name: "伺服负载")]
    public async Task<DriverReturnValueModel> ReadAxisLoad()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadFanucAxisLoadAsync(AxisCount);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = JSON.Serialize(read.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     上电时间
    /// </summary>
    /// <returns></returns>
    [Method("AliveTime", name: "上电时间")]
    public async Task<DriverReturnValueModel> ReadAliveTime()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int64
        };
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadTimeDataAsync(0);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    [Method("CycleTime", name: "循环时间")]
    public async Task<DriverReturnValueModel> ReadCycleTime()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int64
        };
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadTimeDataAsync(3);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     切削时间
    /// </summary>
    /// <returns></returns>
    [Method("CutTime", name: "切削时间")]
    public async Task<DriverReturnValueModel> ReadCutTime()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int64
        };
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadTimeDataAsync(2);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     运行时间
    /// </summary>
    /// <returns></returns>
    [Method("RunTime", name: "运行时间")]
    public async Task<DriverReturnValueModel> ReadCycSec()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int64
        };
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadTimeDataAsync(1);
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴负载
    /// </summary>
    /// <returns></returns>
    [Method("SpindleLoad", name: "主轴负载")]
    public async Task<DriverReturnValueModel> ReadSpindleLoad()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadSpindleLoadAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion

    /// <summary>
    ///     读PMC变量
    /// </summary>
    /// <returns></returns>
    [Method("R", name: "读PMC变量", description: "地址支持:G,F,Y,X,A,R,T,K,C,D,E")]
    public async Task<DriverReturnValueModel> R(DriverAddressIoArgModel val)
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.String };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var readResult = await _driver.ReadPMCDataAsync(val.Address, val.Length);
            if (!readResult.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = readResult.Message;
                return ret;
            }

            ret.Value = SoftBasic.ByteToHexString(readResult.Content, ' ');
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     读宏变量
    /// </summary>
    /// <returns></returns>
    [Method("Macro", name: "读宏变量")]
    public async Task<DriverReturnValueModel> Macro(DriverAddressIoArgModel val)
    {
        var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Double };

        if (!IsConnected)
        {
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Message = "TCP连接异常";
            return ret;
        }

        try
        {
            var readResult = await _driver.ReadSystemMacroValueAsync(Convert.ToInt32(val.Address));
            if (!readResult.IsSuccess)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Error;
                ret.Message = readResult.Message;
                return ret;
            }

            ret.Value = readResult.Content;
        }
        catch (Exception ex) when (ex is not OperationCanceledException)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #region 读取系统信息

    /// <summary>
    ///     读取系统信息
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadSysInfo(string identifier)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _sysInfo ??= _driver.ReadSysInfo();
                if (!_sysInfo.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _sysInfo.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "Series":
                            ret.Value = _sysInfo.Content.Series;
                            break;
                        case "Version":
                            ret.Value = _sysInfo.Content.Version;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     系列信息
    /// </summary>
    /// <returns></returns>
    [Method("Series", TransPondDataTypeEnum.String, name: "系列信息")]
    public async Task<DriverReturnValueModel> ReadSeries()
    {
        var ret = await ReadSysInfo("Series");
        ret.DataType = DataTypeEnum.String;
        return ret;
    }

    /// <summary>
    ///     版本号信息
    /// </summary>
    /// <returns></returns>
    [Method("Version", TransPondDataTypeEnum.String, name: "版本号信息")]
    public async Task<DriverReturnValueModel> ReadVersion()
    {
        var ret = await ReadSysInfo("Version");
        ret.DataType = DataTypeEnum.String;
        return ret;
    }

    #endregion

    /// <summary>
    ///     读取系统轴名称
    /// </summary>
    /// <returns></returns>
    [Method("AxisName", TransPondDataTypeEnum.String, name: "系统轴名称")]
    public async Task<DriverReturnValueModel> ReadAxisName()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var axisNames = await Task.FromResult(_driver.ReadAxisNames());
                if (!axisNames.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = axisNames.Message;
                }
                else
                {
                    ret.Value = axisNames.Content;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
}