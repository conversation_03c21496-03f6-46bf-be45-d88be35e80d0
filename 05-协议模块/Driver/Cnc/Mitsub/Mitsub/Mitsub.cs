using System;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Mitsub.Base;

namespace Mitsub;

[DriverSupported("Mitsub")]
[DriverInfo("Mitsub", "V1.2.0", "三菱CNC(Mitsub)")]
public class Mitsub : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 102;

    #endregion

    public Mitsub(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     MitsubBase
    /// </summary>
    private MitsubBase _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (MitsubBase)value;
    }

    public override bool IsConnected => _driver != null && (bool) _driver.GetConnectStatus();

    public DeviceConnectDto Connect()
    {
        try
        {
            _driver ??= new MitsubBase(IpAddress, Port)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout
            };
            OperateResult = _driver.ConnectServer();
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接失败:【{ex.Message}】"};
        }

        return ResConnect();
    }

    /// <summary>
    ///     模式
    /// </summary>
    /// <returns></returns>
    [Method("WorkMode", description: "1:加工模式;2:自动模式; 3:MDI; 4:手动模式 ;5:寸动模式;6:手轮模式; 7:原点模式;", name: "模式")]
    public async Task<DriverReturnValueModel> ReadWorkMode()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int16
        };
        try
        {
            if (IsConnected)
            {
                var read = await _driver.ReadSysModeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     运行状态
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", description: "1:ready复位准备 2.auto 运行 3.hold 暂停", name: "状态")]
    public async Task<DriverReturnValueModel> ReadRunStatus()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSysStatusAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     工件数量
    /// </summary>
    /// <returns></returns>
    [Method("CurrentNum", name: "工件数量")]
    public async Task<DriverReturnValueModel> ReadCurrentNum()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCurrentProduceCountAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    [Method("ActSpeed", name: "主轴转速")]
    public async Task<DriverReturnValueModel> ReadActSpeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadActSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    [Method("MainName", TransPondDataTypeEnum.String, name: "程序名")]
    public async Task<DriverReturnValueModel> ReadMainName()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSystemProgramCurrentAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     上电时间
    /// </summary>
    /// <returns></returns>
    [Method("AliveTime", name: "上电时间")]
    public async Task<DriverReturnValueModel> ReadAliveTime()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadAliveTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    [Method("CycleTime", name: "循环时间")]
    public async Task<DriverReturnValueModel> ReadCycleTime()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCycleTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     切削时间
    /// </summary>
    /// <returns></returns>
    [Method("CutTime", name: "切削时间")]
    public async Task<DriverReturnValueModel> ReadCutTime()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCutTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     运行时间
    /// </summary>
    /// <returns></returns>
    [Method("CycSec", name: "运行时间")]
    public async Task<DriverReturnValueModel> ReadCycSec()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadRunTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    [Method("SysAlarm", name: "报警状态")]
    public async Task<DriverReturnValueModel> ReadSysAlarm()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSysAlarmAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给率
    /// </summary>
    /// <returns></returns>
    [Method("ActFspeed", TransPondDataTypeEnum.Double, name: "进给率")]
    public async Task<DriverReturnValueModel> ReadActFspeed()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Float
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadActFspeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     报警信息
    /// </summary>
    /// <returns></returns>
    [Method("SystemAlarmInfo", TransPondDataTypeEnum.String, name: "报警信息")]
    public async Task<DriverReturnValueModel> ReadSystemAlarmInfo()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSystemAlarmInfoAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
}