using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace Mitsub.Base;

/// <summary>
///     一个三菱CNC的机床通讯库,目前仅限于测试三菱M700
/// </summary>
public class MitsubBase : NetworkDoubleBase
{
    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public MitsubBase(string ipAddress, int port = 683)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    #endregion

    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }

    #region Read Write Support

    /// <summary>
    ///     读取当前系统状态
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysStatusAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysStatus());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(36);
        // byte[] result = read.Content;
        //return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取报警状态
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysAlarmAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysAlarm());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);

        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取当前工作模式
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<short>> ReadSysModeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysMode());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<short>(read);
        var result = read.Content.RemoveBegin(36);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt16(result, 0));
    }

    /// <summary>
    ///     读取当前工件数量
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCurrentProduceCountAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(36);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取程序名及程序号
    /// </summary>
    /// <returns>程序名及程序号</returns>
    public async Task<OperateResult<string>> ReadSystemProgramCurrentAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSystemProgram());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(40);

        var name = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(name);
    }

    /// <summary>
    ///     读取上电时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadAliveTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadAliveTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(36);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取运行时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadRunTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadRunTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取切削时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCutTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCutTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(36);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取循环时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCycleTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCycleTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(36);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取主轴速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadActSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取进给率
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadActFspeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000f);
    }

    /// <summary>
    ///     读取报警信息
    /// </summary>
    /// <returns>读取报警信息</returns>
    public async Task<OperateResult<string>> ReadSystemAlarmInfoAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadAlarmInfo());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(28);

        //string name = Encoding.Default.GetString(result).Replace("\u0000", "");
        var name = Encoding.Default.GetString(result).TrimEnd('\0');
        return OperateResult.CreateSuccessResult(name);
    }

    #endregion

    #region Build Command

    /// <summary>
    ///     系统状态
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysStatus()
    {
        var buffer = @"
                           0x47, 0x49, 0x4f, 0x50, 0x01, 0x00, 0x01, 0x00,
                           0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0xfc, 0x06, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                           0x04, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                           0x0d, 0x00, 0x00, 0x00, 0x6d, 0x6f, 0x63, 0x68,
                           0x61, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
                           0x00, 0xdc, 0x83, 0x03, 0x00, 0x00, 0x00, 0x00,
                           0x23, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00,
                           0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysAlarm()
    {
        //byte[] buffer = @"
        //                0x14, 0x00, 0x00, 0x00, 0x10, 0x00, 0x92, 0x0b,
        //                0xc8, 0x00, 0xf1, 0x07, 0xc8, 0x00, 0x00, 0x00,
        //                0x4a, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        //                0x08, 0x00, 0x00, 0x00, 0x0a, 0x00, 0x00, 0x00

        //                ".ToHexBytes();

        var buffer = @"
                            0x14, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x35, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x4a, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00

                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     系统模式
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysMode()
    {
        var buffer = @"
                            0x47, 0x49, 0x4F, 0x50, 0x01, 0x00, 0x01, 0x00, 
                            0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
                            0x88, 0x32, 0x00, 0x00, 0x01, 0xCA, 0x15, 0x03, 
                            0x04, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 
                            0x0D, 0x00, 0x00, 0x00, 0x6D, 0x6F, 0x63, 0x68, 
                            0x61, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
                            0x23, 0x00, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
                            0x00, 0x00, 0x00, 0x00, 0x1A, 0x00, 0x00, 0x00
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     工件产量
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadProduct()
    {
        var buffer = @"
                            0x47, 0x49, 0x4f, 0x50, 0x01, 0x00, 0x01, 0x00,
                            0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0xfc, 0x06, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                            0x04, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                            0x0d, 0x00, 0x00, 0x00, 0x6d, 0x6f, 0x63, 0x68,
                            0x61, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
                            0x00, 0xdd, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x7e, 0x00, 0x00, 0x00, 0x42, 0x1f, 0x00, 0x00,
                            0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSystemProgram()
    {
        var buffer = @"
                            0x47, 0x49, 0x4f, 0x50, 0x01, 0x00, 0x01, 0x00,
                            0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0xd0, 0x13, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                            0x04, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                            0x0d, 0x00, 0x00, 0x00, 0x6d, 0x6f, 0x63, 0x68,
                            0x61, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
                            0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00,
                            0x2d, 0x00, 0x00, 0x00, 0x65, 0x00, 0x00, 0x00,
                            0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     上电时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAliveTime()
    {
        var buffer = @"
                            0x47, 0x49, 0x4f, 0x50, 0x01, 0x00, 0x01, 0x00,
                            0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x48, 0x0c, 0x00, 0x00, 0x01, 0x00, 0x23, 0x07,
                            0x04, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                            0x0d, 0x00, 0x00, 0x00, 0x6d, 0x6f, 0x63, 0x68,
                            0x61, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
                            0x00, 0x17, 0x23, 0x07, 0x00, 0x00, 0x00, 0x00,
                            0x28, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     运行时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadRunTime()
    {
        var buffer = @"
                           0x47, 0x49, 0x4f, 0x50, 0x01, 0x00, 0x01, 0x00,
                           0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x48, 0x0c, 0x00, 0x00, 0x01, 0x00, 0x23, 0x07,
                           0x04, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                           0x0d, 0x00, 0x00, 0x00, 0x6d, 0x6f, 0x63, 0x68,
                           0x61, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
                           0x00, 0x17, 0x23, 0x07, 0x00, 0x00, 0x00, 0x00,
                           0x28, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     切削时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCutTime()
    {
        var buffer = @"
                            0x47, 0x49, 0x4f, 0x50, 0x01, 0x00, 0x01, 0x00,
                            0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x48, 0x0c, 0x00, 0x00, 0x01, 0x00, 0x23, 0x07,
                            0x04, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                            0x0d, 0x00, 0x00, 0x00, 0x6d, 0x6f, 0x63, 0x68,
                            0x61, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
                            0x00, 0x00, 0x00, 0x4a, 0x00, 0x00, 0x00, 0x00,
                            0x28, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCycleTime()
    {
        var buffer = @"
                           0x47, 0x49, 0x4f, 0x50, 0x01, 0x00, 0x01, 0x00,
                           0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0xf4, 0x06, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                           0x04, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                           0x0d, 0x00, 0x00, 0x00, 0x6d, 0x6f, 0x63, 0x68,
                           0x61, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
                           0x00, 0x00, 0x00, 0x0a, 0x00, 0x00, 0x00, 0x00,
                           0x28, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActSpeed()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x82, 0x09, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00,
                            0x16, 0x04, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     进给率
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActFspeed()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x86, 0x09,
                            0xc8, 0x00, 0x7f, 0x09, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xbc, 0x02, 0x00, 0x00,
                            0x16, 0x04, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAlarmInfo()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x29, 0x77,
                            0xc8, 0x00, 0x04, 0x09, 0xc8, 0x00, 0x00, 0x00,
                            0x28, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x78, 0x1c, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00,
                            0x73, 0x00, 0x67, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    #endregion
}