using System;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Syntec6MA.Base;
using DateTime = Common.Extension.DateTime;

namespace Syntec6MA;

[DriverSupported("Syntec6MA")]
[DriverInfo("Syntec6MA", "V1.1.0", "新代(Syntec)")]
public class Syntec : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 5566;

    #endregion

    public Syntec(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     SyntecBase
    /// </summary>
    private SyntecBase _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (SyntecBase)value;
    }

    public override bool IsConnected => _driver != null && _driver.GetConnectStatus();
    
    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
                _driver = new SyntecBase(IpAddress, Port)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
            OperateResult = _driver.ConnectServer();
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接失败:【{ex.Message}】"};
        }

        return ResConnect();
    }

    /// <summary>
    ///     模式
    /// </summary>
    /// <returns></returns>
    [Method("WorkMode", description: "", name: "模式")]
    public async Task<DriverReturnValueModel> ReadWorkMode()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSysModeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     运行状态
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", description: "", name: "状态")]
    public async Task<DriverReturnValueModel> ReadRunStatus()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSysStatusAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     产量
    /// </summary>
    /// <returns></returns>
    [Method("CurrentNum", name: "产量")]
    public async Task<DriverReturnValueModel> ReadCurrentNum()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCurrentProduceCountAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    [Method("ActSpeed", name: "主轴转速")]
    public async Task<DriverReturnValueModel> ReadsSpeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadActSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     上电时间
    /// </summary>
    /// <returns></returns>
    [Method("AliveTime", name: "上电时间")]
    public async Task<DriverReturnValueModel> ReadAliveTime()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadAliveTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    [Method("CycleTime", name: "循环时间")]
    public async Task<DriverReturnValueModel> ReadCycleTime()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCycleTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     切削时间
    /// </summary>
    /// <returns></returns>
    [Method("CutTime", name: "切削时间")]
    public async Task<DriverReturnValueModel> ReadCutTime()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCutTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    // /// <summary>
    // ///     运行时间
    // /// </summary>
    // /// <returns></returns>
    // [Method("CycSec", name: "运行时间")]
    // public async Task<DriverReturnValueModel> ReadCycSec()
    // {
    //     DriverReturnValueModel ret = new()
    //     {
    //         VariableStatus = VariableStatusTypeEnum.Good,
    //         DataType = DataTypeEnum.Int32,
    //         ReadTime = DateTime.ToTimeStampLong(DateTime.Now())
    //     };
    //     try
    //     {
    //         if (IsConnected)
    //         {
    //             var read = await Driver.ReadRunTimeAsync();
    //             if (!read.IsSuccess)
    //             {
    //                 ret.VariableStatus = VariableStatusTypeEnum.Error;
    //                 ret.Message = read.Message;
    //             }
    //
    //             ret.Value = read.Content;
    //         }
    //         else
    //         {
    //             ret.VariableStatus = VariableStatusTypeEnum.Bad;
    //             ret.Message = "TCP连接异常";
    //         }
    //     }
    //     catch (Exception ex)
    //     {
    //         ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
    //         ret.Message = ex.Message;
    //     }
    //
    //     return ret;
    // }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    [Method("AlarmStatus", name: "报警状态")]
    public async Task<DriverReturnValueModel> ReadAlarmStatus()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSysAlarmAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    [Method("MainName", TransPondDataTypeEnum.String, name: "程序名")]
    public async Task<DriverReturnValueModel> ReadMainName()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadMainProgramAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     当前程序名
    /// </summary>
    /// <returns></returns>
    [Method("CurrentProgram", TransPondDataTypeEnum.String, name: "当前程序名")]
    public async Task<DriverReturnValueModel> ReadCurrentProgram()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCurrentProgramAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    // /// <summary>
    // ///     报警信息
    // /// </summary>
    // /// <returns></returns>
    // [Method("Alarm", TransPondDataTypeEnum.String, name: "报警信息")]
    // public async Task<DriverReturnValueModel> ReadAlarm()
    // {
    //     DriverReturnValueModel ret = new()
    //     {
    //         VariableStatus = VariableStatusTypeEnum.Good,
    //         DataType = DataTypeEnum.String,
    //         ReadTime = DateTime.ToTimeStampLong(DateTime.Now())
    //     };
    //     try
    //     {
    //         if (IsConnected)
    //         {
    //             var read = await Driver.ReadSystemAlarmInfoAsync();
    //             if (!read.IsSuccess)
    //             {
    //                 ret.VariableStatus = VariableStatusTypeEnum.Error;
    //                 ret.Message = read.Message;
    //             }
    //
    //             ret.Value = read.Content;
    //         }
    //         else
    //         {
    //             ret.VariableStatus = VariableStatusTypeEnum.Bad;
    //             ret.Message = "TCP连接异常";
    //         }
    //     }
    //     catch (Exception ex)
    //     {
    //         ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
    //         ret.Message = ex.Message;
    //     }
    //
    //     return ret;
    // }


    // /// <summary>
    // ///     总工件数量
    // /// </summary>
    // /// <returns></returns>
    // [Method("TotalProduce", name: "总工件数量")]
    // public async Task<DriverReturnValueModel> ReadTotalProduceCount()
    // {
    //     DriverReturnValueModel ret = new()
    //     {
    //         VariableStatus = VariableStatusTypeEnum.Good,
    //         DataType = DataTypeEnum.Int32,
    //         ReadTime = DateTime.ToTimeStampLong(DateTime.Now())
    //     };
    //     try
    //     {
    //         if (IsConnected)
    //         {
    //             var read = await Driver.ReadTotalProduceCountAsync();
    //             if (!read.IsSuccess)
    //             {
    //                 ret.VariableStatus = VariableStatusTypeEnum.Error;
    //                 ret.Message = read.Message;
    //             }
    //
    //             ret.Value = read.Content;
    //         }
    //         else
    //         {
    //             ret.VariableStatus = VariableStatusTypeEnum.Bad;
    //             ret.Message = "TCP连接异常";
    //         }
    //     }
    //     catch (Exception ex)
    //     {
    //         ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
    //         ret.Message = ex.Message;
    //     }
    //
    //     return ret;
    // }

    /// <summary>
    ///     主轴设定速度
    /// </summary>
    /// <returns></returns>
    [Method("SetSpeed", name: "主轴设定速度")]
    public async Task<DriverReturnValueModel> ReadSetSpeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSetSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给设定速度
    /// </summary>
    /// <returns></returns>
    [Method("SetFspeed", TransPondDataTypeEnum.Double, name: "进给设定速度")]
    public async Task<DriverReturnValueModel> ReadSetFspeed()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Float
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSetFspeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给速度
    /// </summary>
    /// <returns></returns>
    [Method("ActFspeed", TransPondDataTypeEnum.Double, name: "进给速度")]
    public async Task<DriverReturnValueModel> ReadActFspeed()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Float
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadActFspeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
}