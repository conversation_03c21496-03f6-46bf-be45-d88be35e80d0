using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace Syntec6MA.Base;

/// <summary>
///     一个Syntec的机床通信类对象
/// </summary>
public class SyntecBase : NetworkDoubleBase
{
    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }
    
    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public SyntecBase(string ipAddress, int port = 5566)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    #endregion

    #region Read Write Support

    /// <summary>
    ///     读取当前系统状态
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysStatusAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysStatus());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取报警状态,有可能是次数，大于0报警
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysAlarmAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysAlarm());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取当前工作模式
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysModeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysMode());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取当前工件数量
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCurrentProduceCountAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取程序名
    /// </summary>
    /// <returns>程序名及程序号</returns>
    public async Task<OperateResult<string>> ReadMainProgramAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSystemProgram());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(20).RemoveLast(4);

        var name = Encoding.GetEncoding("gb2312").GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(name);
    }

    /// <summary>
    ///     读取当前程序名
    /// </summary>
    /// <returns>程序名及程序号</returns>
    public async Task<OperateResult<string>> ReadCurrentProgramAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCurProgram());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(20).RemoveLast(4);

        var name = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(name);
    }

    ///// <summary>
    ///// 
    ///// </summary>
    ///// <returns></returns>
    //public OperateResult<string> ReadVersion()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadVersion());
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

    //    byte[] result = read.Content.RemoveBegin(88).RemoveLast(4);

    //    string name = Encoding.ASCII.GetString(result);//.Replace("\u0000", "");
    //    return OperateResult.CreateSuccessResult(name);
    //}
    
    /// <summary>
    ///     读取上电时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadAliveTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadAliveTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    ///// <summary>
    ///// 读取运行时间
    ///// </summary>
    ///// <returns></returns>
    //public OperateResult<int> ReadRunTime()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadRunTime());
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
    //    byte[] result = read.Content.RemoveBegin(20);
    //    // Array.Reverse(result, 0, 4);
    //    return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    //}

    /// <summary>
    ///     读取切削时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCutTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCutTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取循环时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCycleTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCycleTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
   
    /// <summary>
    ///     读取设定主轴速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSetSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSetSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取主轴速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadActSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取进给率
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadSetFspeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSetFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000f);
    }

    /// <summary>
    ///     读取进给率
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadActFspeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000f);
    }

    ///// <summary>
    ///// 读取报警信息
    ///// </summary>
    ///// <returns>读取报警信息</returns>
    //public OperateResult<string> ReadSystemAlarmInfo()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadAlarmInfo());
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

    //    byte[] result = read.Content.RemoveBegin(28);

    //    //string name = Encoding.Default.GetString(result).Replace("\u0000", "");
    //    string name = Encoding.Default.GetString(result).TrimEnd('\0');
    //    return OperateResult.CreateSuccessResult(name);
    //}

    #endregion


    #region Build Command

    /// <summary>
    ///     系统状态
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysStatus()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0xd3, 0x00,
                            0xc8, 0x00, 0x6f, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x03, 0x00, 0x00, 0x00, 0x7a, 0x53, 0x1c, 0xbc
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysAlarm()
    {
        var buffer = @"
                            0x14, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x70, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x4a, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x1d, 0x2d, 0xcb, 0x9a
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     系统模式
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysMode()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0xd3, 0x00,
                            0xc8, 0x00, 0x6e, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00,
                            0x03, 0x00, 0x00, 0x00, 0x73, 0xf5, 0xab, 0x97
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     工件产量
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadProduct()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x94, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x0d, 0x00, 0x00, 0x00,
                            0x03, 0x00, 0x00, 0x00, 0x39, 0xcf, 0xba, 0xaa
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSystemProgram()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x6b, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x25, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x04, 0x02, 0x00, 0x00, 0x50, 0x00, 0x00, 0x00,
                            0x03, 0x00, 0x00, 0x00, 0x9e, 0xe9, 0xbf, 0xd7
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     当前程序名
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCurProgram()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x6c, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x26, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x04, 0x02, 0x00, 0x00, 0x50, 0x00, 0x00, 0x00,
                            0x03, 0x00, 0x00, 0x00, 0x72, 0xdf, 0xd5, 0xa0
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     上电时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAliveTime()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x85, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xf4, 0x03, 0x00, 0x00,
                            0x03, 0x00, 0x00, 0x00, 0xa2, 0x8a, 0xae, 0x60
                            ".ToHexBytes();
        return buffer;
    }

    ///// <summary>
    ///// 运行时间1
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadRunTime()
    //{
    //    byte[] buffer = @"
    //                   0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0xb5, 0x09,
    //                   0xc8, 0x00, 0x5b, 0x09, 0xc8, 0x00, 0x00, 0x00,
    //                   0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
    //                   0x08, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00,
    //                   0x01, 0x00, 0x00, 0x00
    //                    ".ToHexBytes();
    //    return buffer;
    //}
    /// <summary>
    ///     切削时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCutTime()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x86, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xf3, 0x03, 0x00, 0x00,
                            0x03, 0x00, 0x00, 0x00, 0x43, 0x6e, 0x3c, 0x98
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCycleTime()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x87, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xf2, 0x03, 0x00, 0x00,
                            0x03, 0x00, 0x00, 0x00, 0x4a, 0xc8, 0x8b, 0xb3
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     进给率设定
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSetFspeed()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x9b, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00,
                            0x73, 0x00, 0x67, 0x00, 0x46, 0xe7, 0xe2, 0x0f
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     主轴转速设定
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSetSpeed()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x9c, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00,
                            0x73, 0x00, 0x67, 0x00, 0xe7, 0x95, 0x4e, 0x11
                            ".ToHexBytes();
        return buffer;
    }


    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActSpeed()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0xd3, 0x00,
                            0xc8, 0x00, 0xa0, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00,
                            0x73, 0x00, 0x67, 0x00, 0x34, 0x49, 0xd0, 0x8b
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     进给率
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActFspeed()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x9d, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xbc, 0x02, 0x00, 0x00,
                            0x73, 0x00, 0x67, 0x00, 0x76, 0x73, 0x59, 0x82
                            ".ToHexBytes();
        return buffer;
    }

    ///// <summary>
    ///// 报警信息
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadAlarmInfo()
    //{
    //    byte[] buffer = @"
    //                    0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x29, 0x77,
    //                    0xc8, 0x00, 0x04, 0x09, 0xc8, 0x00, 0x00, 0x00,
    //                    0x28, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
    //                    0x78, 0x1c, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00,
    //                    0x73, 0x00, 0x67, 0x00
    //                    ".ToHexBytes();
    //    return buffer;
    //}
    ///// <summary>
    ///// 版本信息
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadVersion()
    //{
    //    byte[] buffer = @"
    //                    0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x30, 0x07,
    //                    0xc8, 0x00, 0x17, 0x07, 0xc8, 0x00, 0x00, 0x00,
    //                    0x4f, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
    //                    0x50, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
    //                    0x00, 0x00, 0x00, 0x00, 0x3f, 0x76, 0x25, 0x0e
    //                    ".ToHexBytes();
    //    return buffer;
    //}

    #endregion
}