using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace SyntecV3.Base;

/// <summary>
///     一个Syntec的机床通信类对象
/// </summary>
public sealed class SyntecBase : NetworkDoubleBase
{
    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public SyntecBase(string ipAddress, int port = 5566)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    #endregion

    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }

    #region Read Write Support

    /// <summary>
    ///     读取当前系统状态
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSysStatus()
    {
        var read = ReadFromCoreServer(BuildReadSysStatus());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取当前系统状态
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysStatusAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysStatus());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取报警状态
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSysAlarm()
    {
        var read = ReadFromCoreServer(BuildReadSysAlarm());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取报警状态
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysAlarmAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysAlarm());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取当前工作模式
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSysMode()
    {
        var read = ReadFromCoreServer(BuildReadSysMode());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取当前工作模式
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysModeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysMode());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取当前工件数量
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadCurrentProduceCount()
    {
        var read = ReadFromCoreServer(BuildReadProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取当前工件数量
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCurrentProduceCountAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取程序名及程序号
    /// </summary>
    /// <returns>程序名及程序号</returns>
    public OperateResult<string> ReadSystemProgramCurrent()
    {
        var read = ReadFromCoreServer(BuildReadSystemProgram());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(20).RemoveLast(4);

        var name = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(name);
    }

    /// <summary>
    ///     读取程序名及程序号
    /// </summary>
    /// <returns>程序名及程序号</returns>
    public async Task<OperateResult<string>> ReadSystemProgramCurrentAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSystemProgram());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(20).RemoveLast(4);

        var name = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(name);
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public OperateResult<string> ReadVersion()
    {
        var read = ReadFromCoreServer(BuildReadVersion());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(88).RemoveLast(4);

        var v1 = ByteTransform.TransInt16(result, 0);
        var v2 = ByteTransform.TransInt16(result, 2);
        var v3 = ByteTransform.TransInt16(result, 4);
        var v4 = ByteTransform.TransInt16(result, 6);
        var name = v1 + v2.ToString() + v3 + v4;
        return OperateResult.CreateSuccessResult(name);
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<string>> ReadVersionAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadVersion());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(88).RemoveLast(4);

        var v1 = ByteTransform.TransInt16(result, 0);
        var v2 = ByteTransform.TransInt16(result, 2);
        var v3 = ByteTransform.TransInt16(result, 4);
        var v4 = ByteTransform.TransInt16(result, 6);
        var name = v1 + v2.ToString() + v3 + v4;
        return OperateResult.CreateSuccessResult(name);
    }

    /// <summary>
    ///     读取上电时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadAliveTime()
    {
        var read = ReadFromCoreServer(BuildReadAliveTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取上电时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadAliveTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadAliveTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取运行时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadRunTime()
    {
        var read = ReadFromCoreServer(BuildReadRunTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取运行时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadRunTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadRunTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取切削时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadCutTime()
    {
        var read = ReadFromCoreServer(BuildReadCutTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取切削时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCutTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCutTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }


    /// <summary>
    ///     读取循环时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadCycleTime()
    {
        var read = ReadFromCoreServer(BuildReadCycleTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取循环时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCycleTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCycleTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取主轴速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadActSpeed()
    {
        var read = ReadFromCoreServer(BuildReadActSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取主轴速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadActSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取进给率
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadActFspeed()
    {
        var read = ReadFromCoreServer(BuildReadActFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000f);
    }

    /// <summary>
    ///     读取进给率
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadActFspeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000f);
    }

    /// <summary>
    ///     读取报警信息
    /// </summary>
    /// <returns>读取报警信息</returns>
    public OperateResult<string> ReadSystemAlarmInfo()
    {
        var read = ReadFromCoreServer(BuildReadAlarmInfo());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(28);

        //string name = Encoding.Default.GetString(result).Replace("\u0000", "");
        var name = Encoding.Default.GetString(result).TrimEnd('\0');
        return OperateResult.CreateSuccessResult(name);
    }


    /// <summary>
    ///     读取报警信息
    /// </summary>
    /// <returns>读取报警信息</returns>
    public async Task<OperateResult<string>> ReadSystemAlarmInfoAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadAlarmInfo());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(28);

        //string name = Encoding.Default.GetString(result).Replace("\u0000", "");
        var name = Encoding.Default.GetString(result).TrimEnd('\0');
        return OperateResult.CreateSuccessResult(name);
    }

    #endregion


    #region Build Command

    /// <summary>
    ///     系统状态
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysStatus()
    {
        var buffer = @"
                             0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x99, 0x07,
                             0xc8, 0x00, 0x1c, 0x07, 0xc8, 0x00, 0x00, 0x00,
                             0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                             0x08, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                             0x01, 0x00, 0x00, 0x00, 0x06, 0xdd, 0x0f, 0xaf
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysAlarm()
    {
        var buffer = @"
                            0x14, 0x00, 0x00, 0x00, 0x10, 0x00, 0x96, 0x00,
                            0xc8, 0x00, 0x29, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x4a, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x95, 0x48, 0x50, 0x90

                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     系统模式
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysMode()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x99, 0x07,
                            0xc8, 0x00, 0x1b, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00,
                            0x01, 0x00, 0x00, 0x00, 0xbe, 0xa6, 0x66, 0xbb
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     工件产量
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadProduct()
    {
        var buffer = @"
                              0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                              0xc8, 0x00, 0x22, 0x07, 0xc8, 0x00, 0x00, 0x00,
                              0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                              0x08, 0x00, 0x00, 0x00, 0xe8, 0x03, 0x00, 0x00,
                              0x01, 0x00, 0x00, 0x00, 0xdd, 0x65, 0x60, 0xe4
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSystemProgram()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x99, 0x07,
                            0xc8, 0x00, 0x19, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x8c, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x04, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                            0x01, 0x00, 0x00, 0x00, 0x3f, 0xcc, 0x83, 0x2e
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     上电时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAliveTime()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x3c, 0x01,
                            0xc8, 0x00, 0x2f, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xf4, 0x03, 0x00, 0x00,
                            0x01, 0x00, 0x00, 0x00, 0x4f, 0x53, 0x12, 0x6c
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     运行时间1
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadRunTime()
    {
        var buffer = @"
                           0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0xb5, 0x09,
                           0xc8, 0x00, 0x5b, 0x09, 0xc8, 0x00, 0x00, 0x00,
                           0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                           0x08, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00,
                           0x01, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     切削时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCutTime()
    {
        var buffer = @"
                           0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x3c, 0x01,
                           0xc8, 0x00, 0x30, 0x07, 0xc8, 0x00, 0x00, 0x00,
                           0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                           0x08, 0x00, 0x00, 0x00, 0xf3, 0x03, 0x00, 0x00,
                           0x01, 0x00, 0x00, 0x00, 0xb4, 0x57, 0x6f, 0x41
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCycleTime()
    {
        var buffer = @"
                           0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x3c, 0x01,
                           0xc8, 0x00, 0x31, 0x07, 0xc8, 0x00, 0x00, 0x00,
                           0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                           0x08, 0x00, 0x00, 0x00, 0xf2, 0x03, 0x00, 0x00,
                           0x01, 0x00, 0x00, 0x00, 0xbd, 0xf1, 0xd8, 0x6a
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActSpeed()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x2a, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00,
                            0x01, 0x00, 0x00, 0x00, 0x56, 0x71, 0xf3, 0x6f
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     进给率
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActFspeed()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x37, 0x01,
                            0xc8, 0x00, 0x27, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xbc, 0x02, 0x00, 0x00,
                            0x01, 0x00, 0x00, 0x00, 0x06, 0x97, 0xe3, 0x19
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAlarmInfo()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x29, 0x77,
                            0xc8, 0x00, 0x04, 0x09, 0xc8, 0x00, 0x00, 0x00,
                            0x28, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x78, 0x1c, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00,
                            0x73, 0x00, 0x67, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     版本信息
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadVersion()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x34, 0x01,
                            0xc8, 0x00, 0x2e, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x4f, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x50, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0x01, 0x00, 0x00, 0x00, 0x01, 0x2d, 0xc8, 0x41
                            ".ToHexBytes();
        return buffer;
    }

    #endregion
}