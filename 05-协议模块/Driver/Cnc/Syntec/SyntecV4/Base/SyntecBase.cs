using System;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace SyntecV4.Base;

/// <summary>
///     一个Syntec的机床通信类对象
/// </summary>
public sealed class SyntecBase : NetworkDoubleBase
{
    #region Constructor

    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public SyntecBase(string ipAddress, int port = 5566)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    /// <inheritdoc/>
    // protected override INetMessage GetNewNetMessage() => new CNCFanucSeriesMessage();

    #endregion

    #region Read Write Support

    /// <summary>
    ///     读取当前系统状态
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysStatusAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysStatus());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取报警状态
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysAlarmAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysAlarm());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取当前工作模式
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysModeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysMode());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取当前工件数量
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCurrentProduceCountAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadProduct22TA());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取总工件数量
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadTotalProduceCountAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadTotalProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取程序名及程序号
    /// </summary>
    /// <returns>程序名及程序号</returns>
    public async Task<OperateResult<string>> ReadSystemProgramCurrentAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSystemProgram());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(20);

        string name = Encoding.GetEncoding("unicode").GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(name);
    }

    /// <summary>
    ///     读取上电时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadAliveTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadAliveTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取运行时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadRunTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadRunTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取切削时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCutTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCutTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取循环时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCycleTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCycleTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    /// 读取主轴设定速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSetSpeedAsync()
    {
        OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadSetSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        byte[] result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取主轴速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadActSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    /// 读取进给率设定值
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSetFspeedAsync()
    {
        OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildReadSetFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        byte[] result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取进给率
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadActFspeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000f);
    }
    
    /// <summary>
    /// 读取主轴负载
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSpindleLoadAsync()
    {
        OperateResult<byte[]> read = await ReadFromCoreServerAsync(BuildSpindleLoad());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        byte[] result = read.Content.RemoveBegin(24);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    // /// <summary>
    // ///     读取报警信息
    // /// </summary>
    // /// <returns>读取报警信息</returns>
    // public async Task<OperateResult<string>> ReadSystemAlarmInfoAsync()
    // {
    //     var read = await ReadFromCoreServerAsync(BuildReadAlarmInfo());
    //     if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
    //
    //     var result = read.Content.RemoveBegin(28);
    //
    //     //string name = Encoding.Default.GetString(result).Replace("\u0000", "");
    //     var name = Encoding.Default.GetString(result).TrimEnd('\0');
    //     return OperateResult.CreateSuccessResult(name);
    // }

    /// <summary>
    ///     读取机械坐标
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float[]>> ReadMachinePosAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadMachinePos());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float[]>(read);
        var result = read.Content.RemoveBegin(20);
        var axes = ByteTransform.TransInt32(result, 0);
        var pos = new float[axes];
        pos[0] = ByteTransform.TransInt32(result, 4) / 1000f;
        pos[1] = ByteTransform.TransInt32(result, 8) / 1000f;
        pos[2] = ByteTransform.TransInt32(result, 12) / 1000f;
        if (axes > 3)
        {
            pos[3] = ByteTransform.TransInt32(result, 16) / 1000f;
            pos[4] = ByteTransform.TransInt32(result, 20) / 1000f;
        }

        return OperateResult.CreateSuccessResult(pos);
    }

    /// <summary>
    ///     读取相对坐标
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float[]>> ReadRelativePosAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildRelativePos());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float[]>(read);
        var result = read.Content.RemoveBegin(20);
        var axes = ByteTransform.TransInt32(result, 0);
        var pos = new float[axes];
        pos[0] = ByteTransform.TransInt32(result, 4) / 1000f;
        pos[1] = ByteTransform.TransInt32(result, 8) / 1000f;
        pos[2] = ByteTransform.TransInt32(result, 12) / 1000f;
        if (axes > 3)
        {
            pos[3] = ByteTransform.TransInt32(result, 16) / 1000f;
            pos[4] = ByteTransform.TransInt32(result, 20) / 1000f;
        }

        return OperateResult.CreateSuccessResult(pos);
    }


    /// <summary>
    ///     读取绝对坐标
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float[]>> ReadAbsolutePosAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildAbsolutePos());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float[]>(read);
        var result = read.Content.RemoveBegin(20);
        var axes = ByteTransform.TransInt32(result, 0);
        var pos = new float[axes];
        //3轴是X,Y,Z,5轴是X,Y,Z,C,B
        pos[0] = ByteTransform.TransInt32(result, 4) / 1000f;
        pos[1] = ByteTransform.TransInt32(result, 8) / 1000f;
        pos[2] = ByteTransform.TransInt32(result, 12) / 1000f;
        if (axes > 3)
        {
            pos[3] = ByteTransform.TransInt32(result, 16) / 1000f;
            pos[4] = ByteTransform.TransInt32(result, 20) / 1000f;
        }

        return OperateResult.CreateSuccessResult(pos);
    }

    #endregion

    #region Build Command

    /// <summary>
    ///     系统状态
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysStatus()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x67, 0x07,
                            0xc8, 0x00, 0x12, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x01, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysAlarm()
    {
        //byte[] buffer = @"
        //                0x14, 0x00, 0x00, 0x00, 0x10, 0x00, 0x92, 0x0b,
        //                0xc8, 0x00, 0xf1, 0x07, 0xc8, 0x00, 0x00, 0x00,
        //                0x4a, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        //                0x08, 0x00, 0x00, 0x00, 0x0a, 0x00, 0x00, 0x00

        //                ".ToHexBytes();

        var buffer = @"
                            0x14, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x35, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x4a, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00

                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     系统模式
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysMode()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x67, 0x07,
                            0xc8, 0x00, 0x11, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00,
                            0x01, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     工件当前产量
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadProduct()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x78, 0x69,
                            0xc8, 0x00, 0x00, 0x08, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xe8, 0x03, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     工件当前产量22TA
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadProduct22TA()
    {
        var buffer = @"
                                  0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x2d, 0x64,
                                  0xc8, 0x00, 0x1b, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                                  0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                                  0x08, 0x00, 0x00, 0x00, 0xe8, 0x03, 0x00, 0x00,
                                  0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();

        return buffer;
    }


    /// <summary>
    ///     工件总产量高版本
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadTotalProduct()
    {
        var buffer = @"
                                  0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x2d, 0x64,
                                  0xc8, 0x00, 0x1b, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                                  0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                                  0x08, 0x00, 0x00, 0x00, 0xe8, 0x03, 0x00, 0x00,
                                  0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();

        return buffer;
    }


    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSystemProgram()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x67, 0x07,
                            0xc8, 0x00, 0x0f, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x8c, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x04, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                            0x01, 0x00, 0x00, 0x00
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     上电时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAliveTime()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x6f, 0x69,
                            0xc8, 0x00, 0x1d, 0x09, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xf4, 0x03, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     运行时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadRunTime()
    {
        var buffer = @"
                           0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0xb5, 0x09,
                           0xc8, 0x00, 0x5b, 0x09, 0xc8, 0x00, 0x00, 0x00,
                           0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                           0x08, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00,
                           0x01, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     切削时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCutTime()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x77, 0x09,
                            0xc8, 0x00, 0x0f, 0x09, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xf3, 0x03, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCycleTime()
    {
        var buffer = @"
                           0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x86, 0x09,
                           0xc8, 0x00, 0x58, 0x09, 0xc8, 0x00, 0x00, 0x00,
                           0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                           0x08, 0x00, 0x00, 0x00, 0xf2, 0x03, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActSpeed()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x82, 0x09, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00,
                            0x16, 0x04, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     进给率
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActFspeed()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x86, 0x09,
                            0xc8, 0x00, 0x7f, 0x09, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xbc, 0x02, 0x00, 0x00,
                            0x16, 0x04, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    ///// <summary>
    ///// 报警信息（未验证暂时不启用）
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadAlarmInfo()
    //{
    //    byte[] buffer = @"
    //                    0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x29, 0x77,
    //                    0xc8, 0x00, 0x04, 0x09, 0xc8, 0x00, 0x00, 0x00,
    //                    0x28, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
    //                    0x78, 0x1c, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00,
    //                    0x73, 0x00, 0x67, 0x00
    //                    ".ToHexBytes();
    //    return buffer;
    //}

    /// <summary>
    /// 机械坐标
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadMachinePos()
    {
        byte[] buffer = @"
                          0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                          0xc8, 0x00, 0x24, 0x00, 0xc8, 0x00, 0x00, 0x00,
                          0x05, 0x04, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00,
                          0x1c, 0x00, 0x00, 0x00, 0x65, 0x00, 0x00, 0x00,
                          0x05, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }
        
    /// <summary>
    /// 相对坐标
    /// </summary>
    /// <returns></returns>
    public byte[] BuildRelativePos()
    {
        byte[] buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x27, 0x00, 0xc8, 0x00, 0x00, 0x00,
                            0x05, 0x04, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00,
                            0x1c, 0x00, 0x00, 0x00, 0x8d, 0x00, 0x00, 0x00,
                            0x05, 0x00, 0x00, 0x00
                          ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    /// 绝对坐标
    /// </summary>
    /// <returns></returns>
    public byte[] BuildAbsolutePos()
    {
        byte[] buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x26, 0x00, 0xc8, 0x00, 0x00, 0x00,
                            0x05, 0x04, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00,
                            0x1c, 0x00, 0x00, 0x00, 0xb5, 0x00, 0x00, 0x00,
                            0x05, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }
    
    /// <summary>
    /// 主轴负载率 通过PLC读取R5141 主轴负载
    /// </summary>
    /// <returns></returns>
    public byte[] BuildSpindleLoad()
    {
        byte[] buffer = @"
                             0x1c, 0x00, 0x00, 0x00, 0x10, 0x00, 0xc3, 0x05,
                             0xc8, 0x00, 0x03, 0x0a, 0xc8, 0x00, 0x00, 0x00,
                             0x0f, 0x04, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00,
                             0x0c, 0x00, 0x00, 0x00, 0x15, 0x14, 0x00, 0x00,
                             0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00

                            ".ToHexBytes();
        return buffer;
    }
    
    /// <summary>
    /// 进给率设定
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSetFspeed()
    {
        byte[] buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x38, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00,
                            0x01, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    /// 主轴转速设定
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSetSpeed()
    {
        byte[] buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x39, 0x07, 0xc8, 0x00, 0x00, 0x00,
                            0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00,
                            0x01, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    #endregion
}