using System;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Furion.JsonSerialization;
using StackExchange.Profiling.Internal;
using SyntecV4.Base;
using DateTime = Common.Extension.DateTime;

namespace SyntecV4;
/* 更新日志
 *版本:v1.1.0
 *  1.增加机械坐标，移动坐标..坐标相关点位
 *
 * 版本:v1.2.0
 *  1.禁用报警信息(暂未支持)
 *  2.修改三个坐标的底层,修改程序号的编码格式
 *
 *  版本:v1.3.0
 *  新代V4删除报警信息点位，增加主轴负载点位
 *
 *  版本:v1.3.1
 *  增加主轴负载点位、进给率设定点位、主轴速度设定点位.删除主轴倍率点位
 */
[DriverSupported("Syntec")]
[DriverInfo("Syntec", "V1.3.1", "新代(Syntec)")]
public class Syntec : BaseCncProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 5566;

    #endregion

    public Syntec(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     SyntecBase
    /// </summary>
    private SyntecBase _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (SyntecBase)value;
    }

    public override bool IsConnected => _driver != null && (bool) _driver.GetConnectStatus();
    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
                _driver = new SyntecBase(IpAddress, Port)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
            OperateResult = _driver.ConnectServer();
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接失败:【{ex.Message}】"};
        }

        return ResConnect();
    }

    /// <summary>
    ///     模式
    /// </summary>
    /// <returns></returns>
    [Method("WorkMode", description: "", name: "模式")]
    public async Task<DriverReturnValueModel> ReadWorkMode()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSysModeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     运行状态
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", description: "", name: "状态")]
    public async Task<DriverReturnValueModel> ReadRunStatus()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSysStatusAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     产量
    /// </summary>
    /// <returns></returns>
    [Method("CurrentNum", name: "产量")]
    public async Task<DriverReturnValueModel> ReadCurrentNum()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCurrentProduceCountAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    [Method("ActSpeed", name: "主轴转速")]
    public async Task<DriverReturnValueModel> ReadsSpeed()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadActSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     上电时间
    /// </summary>
    /// <returns></returns>
    [Method("AliveTime", name: "上电时间")]
    public async Task<DriverReturnValueModel> ReadAliveTime()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadAliveTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    [Method("CycleTime", name: "循环时间")]
    public async Task<DriverReturnValueModel> ReadCycleTime()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCycleTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     切削时间
    /// </summary>
    /// <returns></returns>
    [Method("CutTime", name: "切削时间")]
    public async Task<DriverReturnValueModel> ReadCutTime()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadCutTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     运行时间
    /// </summary>
    /// <returns></returns>
    [Method("CycSec", name: "运行时间")]
    public async Task<DriverReturnValueModel> ReadCycSec()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadRunTimeAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    [Method("AlarmStatus", name: "报警状态")]
    public async Task<DriverReturnValueModel> ReadAlarmStatus()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSysAlarmAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    [Method("MainName", TransPondDataTypeEnum.String, name: "程序名")]
    public async Task<DriverReturnValueModel> ReadMainName()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSystemProgramCurrentAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
    
    /// <summary>
    ///     主轴设定速度
    /// </summary>
    /// <returns></returns>
    [Method("SetSpeed", name: "主轴设定速度")]
    public async Task<DriverReturnValueModel> ReadSetSpeed()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSetSpeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
    
    /// <summary>
    ///     进给率设定值
    /// </summary>
    /// <returns></returns>
    [Method("SetFspeed", name: "进给率设定值")]
    public async Task<DriverReturnValueModel> ReadSetFspeed()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSetFspeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
    
    /// <summary>
    ///     主轴负载
    /// </summary>
    /// <returns></returns>
    [Method("SpindleLoad", name: "主轴负载")]
    public async Task<DriverReturnValueModel> ReadSpindleLoad()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Int32
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadSpindleLoadAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    // /// <summary>
    // ///     报警信息
    // /// </summary>
    // /// <returns></returns>
    // [Method("Alarm", TransPondDataTypeEnum.String, name: "报警信息")]
    // public async Task<DriverReturnValueModel> ReadAlarm()
    // {
    //     DriverReturnValueModel ret = new()
    //     {
    //         DataType = DataTypeEnum.String
    //     };
    //     try
    //     {
    //         if (IsConnected)
    //         {
    //             var read = await Driver.ReadSystemAlarmInfoAsync();
    //             if (!read.IsSuccess)
    //             {
    //                 ret.VariableStatus = VariableStatusTypeEnum.Error;
    //                 ret.Message = read.Message;
    //             }
    //
    //             ret.Value = read.Content;
    //         }
    //         else
    //         {
    //             ret.VariableStatus = VariableStatusTypeEnum.Bad;
    //             ret.Message = "TCP连接异常";
    //         }
    //     }
    //     catch (Exception ex)
    //     {
    //         ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
    //         ret.Message = ex.Message;
    //     }
    //
    //     return ret;
    // }

    /// <summary>
    ///     总工件数量
    /// </summary>
    /// <returns></returns>
    [Method("TotalProduce", name: "总工件数量")]
    public async Task<DriverReturnValueModel> ReadTotalProduceCount()
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadTotalProduceCountAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     进给率
    /// </summary>
    /// <returns></returns>
    [Method("ActFspeed", TransPondDataTypeEnum.Double, name: "进给率")]
    public async Task<DriverReturnValueModel> ReadActFspeed()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.Float
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadActFspeedAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = read.Content;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
    
    /// <summary>
    ///     机械坐标
    /// </summary>
    /// <returns></returns>
    [Method("MachinePos", TransPondDataTypeEnum.String, name: "机械坐标")]
    public async Task<DriverReturnValueModel> ReadMachinePos()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadMachinePosAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                
                ret.Value = JSON.Serialize(read.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
    
    /// <summary>
    ///     相对坐标
    /// </summary>
    /// <returns></returns>
    [Method("RelativePos", TransPondDataTypeEnum.String, name: "相对坐标")]
    public async Task<DriverReturnValueModel> ReadRelativePos()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadRelativePosAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = JSON.Serialize(read.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
    
    /// <summary>
    ///     绝对坐标
    /// </summary>
    /// <returns></returns>
    [Method("AbsolutePos", TransPondDataTypeEnum.String, name: "绝对坐标")]
    public async Task<DriverReturnValueModel> ReadAbsolutePos()
    {
        DriverReturnValueModel ret = new()
        {
            DataType = DataTypeEnum.String
        };
        try
        {
            if (IsConnected)
            {
                var read = await Driver.ReadAbsolutePosAsync();
                if (!read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = read.Message;
                }

                ret.Value = JSON.Serialize(read.Content);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }
}