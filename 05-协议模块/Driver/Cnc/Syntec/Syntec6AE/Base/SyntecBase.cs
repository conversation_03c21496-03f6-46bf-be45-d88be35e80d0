using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace Syntec6AE.Base;

/// <summary>
///     一个Syntec的机床通信类对象
/// </summary>
public class SyntecBase : NetworkDoubleBase
{
    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public SyntecBase(string ipAddress, int port = 5566)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    // protected override INetMessage GetNewNetMessage() => new CNCFanucSeriesMessage();

    #endregion

    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }

    #region Read Write Support

    /// <summary>
    ///     读取当前系统状态
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSysStatus()
    {
        var read = ReadFromCoreServer(BuildReadSysStatus());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取当前系统状态
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysStatusAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysStatus());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取报警状态
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSysAlarm()
    {
        var read = ReadFromCoreServer(BuildReadSysAlarm());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取报警状态
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysAlarmAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysAlarm());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取当前工作模式
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSysMode()
    {
        var read = ReadFromCoreServer(BuildReadSysMode());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取当前工作模式
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSysModeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSysMode());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取当前工件数量
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadCurrentProduceCount()
    {
        var read = ReadFromCoreServer(BuildReadProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取当前工件数量
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCurrentProduceCountAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取总工件数量
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadTotalProduceCount()
    {
        var read = ReadFromCoreServer(BuildReadTotalProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取总工件数量
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadTotalProduceCountAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadTotalProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取设定工件产量
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadRequireProduceCount()
    {
        var read = ReadFromCoreServer(BuildReadRequireProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取设定工件产量
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadRequireProduceCountAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadRequireProduct());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     当前程序名
    /// </summary>
    /// <returns>程序名及程序号</returns>
    public OperateResult<string> ReadSystemProgramCurrent()
    {
        var read = ReadFromCoreServer(BuildReadCurrentProgram());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(20);

        var name = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(name);
    }

    /// <summary>
    ///     当前程序名
    /// </summary>
    /// <returns>程序名及程序号</returns>
    public async Task<OperateResult<string>> ReadSystemProgramCurrentAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCurrentProgram());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(20);

        var name = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(name);
    }
    
    /// <summary>
    ///     读取上电时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadAliveTime()
    {
        var read = ReadFromCoreServer(BuildReadAliveTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取上电时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadAliveTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadAliveTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取工作时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadRunTime()
    {
        //读取总的上电时间
        var read = ReadFromCoreServer(BuildReadSumTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        //读取总的空闲时间
        var read1 = ReadFromCoreServer(BuildReadSumFreeTime());
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<int>(read1);

        var result = read.Content.RemoveBegin(20);
        var sumtime = ByteTransform.TransInt32(result, 0);
        var result1 = read.Content.RemoveBegin(20);
        var freetime = ByteTransform.TransInt32(result1, 0);

        return OperateResult.CreateSuccessResult(sumtime - freetime);
    }
    
    /// <summary>
    ///     读取工作时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadRunTimeAsync()
    {
        //读取总的上电时间
        var read = await ReadFromCoreServerAsync(BuildReadSumTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        //读取总的空闲时间
        var read1 = await ReadFromCoreServerAsync(BuildReadSumFreeTime());
        if (!read1.IsSuccess) return OperateResult.CreateFailedResult<int>(read1);

        var result = read.Content.RemoveBegin(20);
        var sumtime = ByteTransform.TransInt32(result, 0);
        var result1 = read.Content.RemoveBegin(20);
        var freetime = ByteTransform.TransInt32(result1, 0);

        return OperateResult.CreateSuccessResult(sumtime - freetime);
    }

    /// <summary>
    ///     读取切削时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadCutTime()
    {
        var read = ReadFromCoreServer(BuildReadCutTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取切削时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCutTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCutTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取循环时间
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadCycleTime()
    {
        var read = ReadFromCoreServer(BuildReadCycleTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取循环时间
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadCycleTimeAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadCycleTime());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取主轴设定速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadSetSpeed()
    {
        var read = ReadFromCoreServer(BuildReadSetSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取主轴设定速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadSetSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSetSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取进给设定速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadSetFspeed()
    {
        var read = ReadFromCoreServer(BuildReadSetFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000f);
    }
    
    /// <summary>
    ///     读取进给设定速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadSetFspeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadSetFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000f);
    }

    /// <summary>
    ///     读取主轴速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<int> ReadActSpeed()
    {
        var read = ReadFromCoreServer(BuildReadActSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }

    /// <summary>
    ///     读取主轴速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadActSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    }
    
    /// <summary>
    ///     读取进给速度
    /// </summary>
    /// <returns></returns>
    public OperateResult<float> ReadActFspeed()
    {
        var read = ReadFromCoreServer(BuildReadActFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000f);
    }
    
    /// <summary>
    ///     读取进给速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<float>> ReadActFspeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
        var result = read.Content.RemoveBegin(20);
        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0) / 1000f);
    }

    /// <summary>
    ///     读取报警信息N
    /// </summary>
    /// <returns>读取报警信息</returns>
    public OperateResult<string> ReadSystemAlarmInfo()
    {
        var read = ReadFromCoreServer(BuildReadAlarmInfo());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(28);

        //string name = Encoding.Default.GetString(result).Replace("\u0000", "");
        // string name = Encoding.Default.GetString(result).TrimEnd('\0');
        var name = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(name);
    }

    /// <summary>
    ///     读取报警信息N
    /// </summary>
    /// <returns>读取报警信息</returns>
    public async Task<OperateResult<string>> ReadSystemAlarmInfoAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadAlarmInfo());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

        var result = read.Content.RemoveBegin(28);

        //string name = Encoding.Default.GetString(result).Replace("\u0000", "");
        // string name = Encoding.Default.GetString(result).TrimEnd('\0');
        var name = Encoding.Default.GetString(result).Replace("\u0000", "");
        return OperateResult.CreateSuccessResult(name);
    }

    #endregion

    #region Build Command

    /// <summary>
    ///     系统状态no
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysStatus()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0xd7, 0x78,
                            0xc8, 0x00, 0x12, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警状态
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysAlarm()
    {
        var buffer = @"
                            0x14, 0x00, 0x00, 0x00, 0x10, 0x00, 0x01, 0x55,
                            0xc8, 0x00, 0x32, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x4a, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     系统模式
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSysMode()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0xd7, 0x78,
                            0xc8, 0x00, 0x11, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     工件当前产量
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadProduct()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x3b, 0x07,
                            0xc8, 0x00, 0x2b, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xe8, 0x03, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     工件设定产量
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadRequireProduct()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x3b, 0x07,
                            0xc8, 0x00, 0x2c, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xea, 0x03, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();

        return buffer;
    }


    /// <summary>
    ///     工件总产量
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadTotalProduct()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x3b, 0x07,
                            0xc8, 0x00, 0x2d, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xec, 0x03, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();

        return buffer;
    }


    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSystemProgram()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x4f, 0x00,
                            0xc8, 0x00, 0x0e, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x8c, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x04, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();

        return buffer;
    }


    /// <summary>
    ///     当前程序名
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCurrentProgram()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x4f, 0x00,
                            0xc8, 0x00, 0x0f, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x8c, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x04, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();

        return buffer;
    }

    /// <summary>
    ///     上电时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAliveTime()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x1a, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xf4, 0x03, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     总上电时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSumTime()
    {
        var buffer = @"
                           0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0xb9, 0x00,
                           0xc8, 0x00, 0x1d, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                           0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                           0x08, 0x00, 0x00, 0x00, 0x09, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     总空闲时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSumFreeTime()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x8f, 0x00,
                            0xc8, 0x00, 0x1e, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x38, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x24, 0x27, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     切削时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCutTime()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x1b, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xf3, 0x03, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     循环时间
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadCycleTime()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x1c, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xf2, 0x03, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     主轴设定转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSetSpeed()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x26, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     进给设定速度
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadSetFspeed()
    {
        var buffer = @"
                           0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0xc6, 0x08,
                           0xc8, 0x00, 0x25, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                           0x07, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                           0x08, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00,
                           0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActSpeed()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
                            0xc8, 0x00, 0x2a, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     进给速度
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActFspeed()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x99, 0x0b,
                            0xc8, 0x00, 0x27, 0x0b, 0xc8, 0x00, 0x00, 0x00,
                            0x1a, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x08, 0x00, 0x00, 0x00, 0xbc, 0x02, 0x00, 0x00,
                            0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    /// <summary>
    ///     报警信息NO
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadAlarmInfo()
    {
        var buffer = @"
                            0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x29, 0x77,
                            0xc8, 0x00, 0x04, 0x09, 0xc8, 0x00, 0x00, 0x00,
                            0x28, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
                            0x78, 0x1c, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00,
                            0x73, 0x00, 0x67, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    #endregion
}