using System;
using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace FagorNet.Base;

/// <summary>
///     一个FagorNet的机床通信类对象，暂时还未完善
/// </summary>
public sealed class FagorNetBase : NetworkDoubleBase
{
    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }

    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public FagorNetBase(string ipAddress, int port = 3873)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    #endregion


    #region Read Write Support

    // /// <summary>
    // /// 读取当前系统状态  0:RESET复位,1:STOP停止 2:RUN 3:HOLD 进给保持
    // /// </summary>
    // /// <returns></returns>
    //public OperateResult<int> ReadSysStatus()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadSysStatus());
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read); 
    //    byte[] result = read.Content.RemoveBegin(15);
    //    return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    //}
    // /// <summary>
    // /// 读取报警状态
    // /// </summary>
    // /// <returns></returns>
    //public OperateResult<int> ReadSysAlarm()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadSysAlarm());
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
    //    if (read.Content.Length <= 6) return OperateResult.CreateFailedResult<int>(read);
    //    byte[] result = read.Content.RemoveBegin(6);
    //    //获取错误个数
    //    int errornum = ByteTransform.TransInt32(result, 0);
    //    //获取警告个数
    //    int warnnum = ByteTransform.TransInt32(result, 4);

    //    return OperateResult.CreateSuccessResult(errornum+ warnnum);
    //}

    // /// <summary>
    // /// 读取当前工作模式 模式 0:EDIT,1:MEM 2:MDI 3:DNC 4:JOG,5：HANDLE,6:REF
    // /// </summary>
    // /// <returns></returns>
    //public OperateResult<int> ReadSysMode()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadSysMode());
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);

    //    byte[] result = read.Content.RemoveBegin(15);

    //    return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    //}

    // /// <summary>
    // /// 读取当前工件数量
    // /// </summary>
    // /// <returns></returns>
    //public OperateResult<int> ReadCurrentProduceCount()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadProduct());
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
    //    byte[] result = read.Content.RemoveBegin(15);
    //    return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    //}

    // /// <summary>
    // /// 读取程序名及程序号
    // /// </summary>
    // /// <returns>程序名及程序号</returns>
    //public OperateResult<string> ReadSystemProgramCurrent()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadSystemProgram());
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);
    //    byte[] result = read.Content.RemoveBegin(15);
    //    short[] length = ByteTransform.TransInt16(result,0, 2);
    //    string name = Encoding.Default.GetString(result.SelectMiddle(2, length[0])).Replace("\u0000", "");
    //    return OperateResult.CreateSuccessResult(name);
    //}


    // /// <summary>
    // /// 读取运行时间
    // /// </summary>
    // /// <returns></returns>
    // public OperateResult<int> ReadRunTime()
    // {
    //     OperateResult<byte[]> read = ReadFromCoreServer(BuildReadRunTime());
    //     if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
    //     byte[] result = read.Content.RemoveBegin(15);
    //     return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    // }

    // /// <summary>
    // /// 读取切削时间
    // /// </summary>
    // /// <returns></returns>
    // public OperateResult<int> ReadCutTime()
    // {
    //     OperateResult<byte[]> read = ReadFromCoreServer(BuildReadCutTime());
    //     if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
    //     byte[] result = read.Content.RemoveBegin(15);
    //     // Array.Reverse(result, 0, 4);
    //     return OperateResult.CreateSuccessResult(ByteTransform.TransInt32(result, 0));
    // }
    ///// <summary>
    // //// 读取主轴倍率
    ///// / </summary>
    // /// <returns></returns>
    // public OperateResult<float> ReadSpindleRate()
    // {
    //     OperateResult<byte[]> read = ReadFromCoreServer(BuildSpindleRate());
    //     if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
    //     byte[] result = read.Content.RemoveBegin(15);
    //     return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    // }
    // /// <summary>
    // //// 读取进给倍率
    // /// / </summary>
    // /// <returns></returns>
    // public OperateResult<float> ReadFeedRate()
    // {
    //     OperateResult<byte[]> read = ReadFromCoreServer(BuildReadFeedRate());
    //     if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
    //     byte[] result = read.Content.RemoveBegin(15);
    //     return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    // }

    /// <summary>
    ///     读取主轴速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadActSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActSpeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(15);
        var speed = Encoding.Default.GetString(result.SelectMiddle(0, result.Length - 2));

        return OperateResult.CreateSuccessResult(Convert.ToInt32(speed));
    }

    ///// <summary>
    ///// / 读取主轴编程速度
    ////// </summary>
    ////// <returns></returns>
    //public OperateResult<float> ReadSetSpeed()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadSetSpeed());
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
    //    byte[] result = read.Content.RemoveBegin(15);
    //    return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    //}

    /// <summary>
    ///     读取进给速度
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<int>> ReadActFSpeedAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadActFspeed());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<int>(read);
        var result = read.Content.RemoveBegin(14);
        var feed = Encoding.Default.GetString(result.SelectMiddle(0, result.Length - 2));
        return OperateResult.CreateSuccessResult(Convert.ToInt32(feed));
    }

    ///// <summary>
    ///// 读取进给编程速度
    ///// </summary>
    ///// <returns></returns>
    //public OperateResult<float> ReadSetFspeed()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadSetFspeed());
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<float>(read);
    //    byte[] result = read.Content.RemoveBegin(15);
    //    return OperateResult.CreateSuccessResult(ByteTransform.TransSingle(result, 0));
    //}

    ///// <summary>
    ///// 读取报警信息
    ///// </summary>
    ///// <returns>读取报警信息</returns>
    //public OperateResult<string> ReadSystemAlarmInfo()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadAlarmInfo());
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<string>(read);

    //    byte[] result = read.Content.RemoveBegin(28);

    //    //string name = Encoding.Default.GetString(result).Replace("\u0000", "");
    //    string name = Encoding.Default.GetString(result).TrimEnd('\0');
    //    return OperateResult.CreateSuccessResult(name);
    //}

    #endregion


    #region Build Command

    // /// <summary>
    // /// 系统状态
    // /// </summary>
    // /// <returns></returns>
    //public byte[] BuildReadSysStatus()
    //{
    //    byte[] buffer = @"
    //                     0x93, 0x00, 0x0a, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
    //                     0x1e, 0x17, 0x10, 0x17, 0x11, 0x00, 0x55, 0xaa
    //                    ".ToHexBytes();
    //    return buffer;
    //}
    ///// <summary>
    ///// 报警状态
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadSysAlarm()
    //{
    //    byte[] buffer = @"
    //                   0x17, 0x10, 0x00, 0x81
    //                    ".ToHexBytes();
    //    return buffer;
    //}

    ///// <summary>
    ///// 系统模式
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadSysMode()
    //{
    //    byte[] buffer = @"
    //                    0x93, 0x00, 0x0a, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
    //                    0x1e, 0x17, 0x10, 0x17, 0x10, 0x00, 0x55, 0xaa
    //                    ".ToHexBytes();
    //    return buffer;
    //}

    ///// <summary>
    ///// 工件产量
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadProduct()
    //{
    //    byte[] buffer = @"
    //                   0x93, 0x00, 0x0a, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
    //                   0x1e, 0x17, 0x10, 0x17, 0x16, 0x00, 0x55, 0xaa
    //                 ".ToHexBytes();

    //    return buffer;

    //}
    ///// <summary>
    ///// 程序名
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadSystemProgram()
    //{
    //    byte[] buffer = @"
    //                     0x93, 0x00, 0x0a, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
    //                     0x1e, 0x17, 0x10, 0x17, 0x12, 0x00, 0x55, 0xaa
    //                    ".ToHexBytes();

    //    return buffer;
    //}

    ///// <summary>
    ///// 运行时间
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadRunTime()
    //{
    //    byte[] buffer = @"
    //                    0x93, 0x00, 0x0a, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
    //                    0x1e, 0x17, 0x10, 0x17, 0x18, 0x00, 0x55, 0xaa
    //                    ".ToHexBytes();
    //    return buffer;
    //}
    ///// <summary>
    ///// 切削时间
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadCutTime()
    //{
    //    byte[] buffer = @"
    //                   0x93, 0x00, 0x0a, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
    //                   0x1e, 0x17, 0x10, 0x17, 0x19, 0x00, 0x55, 0xaa
    //                    ".ToHexBytes();
    //    return buffer;
    //}
    ///// <summary>
    ///// 读取主轴倍率报文
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildSpindleRate()
    //{
    //    byte[] buffer = @"
    //                   0x93, 0x00, 0x0c, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
    //                   0x1e, 0x17, 0x10, 0x17, 0x1a, 0x05, 0x00, 0x00,
    //                   0x55, 0xaa
    //                    ".ToHexBytes();
    //    return buffer;
    //}
    ///// <summary>
    ///// 读取进给倍率报文
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadFeedRate()
    //{
    //    byte[] buffer = @"
    //                     0x93, 0x00, 0x0c, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
    //                     0x1e, 0x17, 0x10, 0x17, 0x1a, 0x02, 0x00, 0x00,
    //                     0x55, 0xaa
    //                   ".ToHexBytes();
    //    return buffer;
    //}
    /// <summary>
    ///     主轴转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActSpeed()
    {
        var buffer = @"
                            0x00, 0x0e, 0x01, 0x52, 0x2c, 0x56, 0x41, 0x52,
                            0x2c, 0x53, 0x50, 0x45, 0x45, 0x44, 0x2c, 0x03
                            ".ToHexBytes();
        return buffer;
    }
    ///// <summary>
    ///// 主轴编程转速
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadSetSpeed()
    //{
    //    byte[] buffer = @"
    //                     0x93, 0x00, 0x0c, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
    //                     0x1e, 0x17, 0x10, 0x17, 0x1a, 0x00, 0x00, 0x00,
    //                     0x55, 0xaa
    //                    ".ToHexBytes();
    //    return buffer;
    //}

    /// <summary>
    ///     进给转速
    /// </summary>
    /// <returns></returns>
    public byte[] BuildReadActFspeed()
    {
        var buffer = @"
                            0x00, 0x0d, 0x01, 0x52, 0x2c, 0x56, 0x41, 0x52,
                            0x2c, 0x46, 0x45, 0x45, 0x44, 0x2c, 0x03
                            ".ToHexBytes();
        return buffer;
    }
    ///// <summary>
    ///// 进给编程转速
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadSetFspeed()
    //{
    //    byte[] buffer = @"
    //                     0x93, 0x00, 0x0c, 0x00, 0x6f, 0xc8, 0x1e, 0x64,
    //                     0x1e, 0x17, 0x10, 0x17, 0x1a, 0x00, 0x00, 0x00,
    //                     0x55, 0xaa
    //                    ".ToHexBytes();
    //    return buffer;
    //}
    ///// <summary>
    ///// 报警信息
    ///// </summary>
    ///// <returns></returns>
    //public byte[] BuildReadAlarmInfo()
    //{
    //    byte[] buffer = @"
    //                    0x18, 0x00, 0x00, 0x00, 0x10, 0x00, 0x29, 0x77,
    //                    0xc8, 0x00, 0x04, 0x09, 0xc8, 0x00, 0x00, 0x00,
    //                    0x28, 0x04, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00,
    //                    0x78, 0x1c, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00,
    //                    0x73, 0x00, 0x67, 0x00
    //                    ".ToHexBytes();
    //    return buffer;
    //}

    #endregion
}