using System;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Hnc.Base;
using HslCommunication;

namespace Hnc;

[DriverSupported("Hnc")]
[DriverInfo("Hnc", "V1.1.0", "华中(Hnc)")]
public class Hnc : BaseCncProtocolCollector, IDriver
{
    /// <summary>
    ///     读取通道信息：主轴倍率、进给倍率、运行状态、产量、总产量、程序号、程序名
    /// </summary>
    private OperateResult<short, short, short, short, short, short, string> _readRunInfo;

    public Hnc(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    /// </summary>
    public override bool IsConnected => Driver != null && (bool) Driver.GetConnectStatus();

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver == null)
            {
                var udp = new HncUdp(IpAddress, Port);
                var init = udp.HncUdpInitialization();
                if (init.IsSuccess)
                    Driver = new HncTcp(IpAddress, Port)
                    {
                        ReceiveTimeOut = Timeout,
                        ConnectTimeOut = Timeout
                    };
            }

            OperateResult = Driver.ConnectServer();
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接失败:【{ex.Message}】"};
        }

        return ResConnect();
    }

    /// <summary>
    ///     清空数据
    /// </summary>
    /// <returns></returns>
    [Method("Clear", name: "Clear")]
    public Task Clear()
    {
        _readRunInfo = null;
        return Task.CompletedTask;
    }

    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 10001;

    #endregion

    #region 读取通道信息：主轴倍率、进给倍率、运行状态、产量、总产量、程序号、程序名

    /// <summary>
    ///     读取通道信息：主轴倍率、进给倍率、产量、总产量、运行状态、程序号、程序名
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadRunInfo(string identifier)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (IsConnected)
            {
                _readRunInfo ??= await Driver.ReadRunInfoAsync();
                if (!_readRunInfo.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = _readRunInfo.Message;
                }
                else
                {
                    switch (identifier)
                    {
                        case "OvSpin":
                            ret.Value = Convert.ToInt32(_readRunInfo.Content1);
                            break;
                        case "OvFeed":
                            ret.Value = Convert.ToInt32(_readRunInfo.Content2);
                            break;
                        case "CurrentNum":
                            ret.Value = Convert.ToInt32(_readRunInfo.Content3);
                            break;
                        case "CurrentNumTotal":
                            ret.Value = Convert.ToInt32(_readRunInfo.Content4);
                            break;
                        case "RunStatus":
                            ret.Value = Convert.ToInt32(_readRunInfo.Content5);
                            break;
                        case "CurPgm":
                            ret.Value = Convert.ToInt32(_readRunInfo.Content6);
                            break;
                        case "MainName":
                            ret.Value = _readRunInfo.Content6;
                            ret.DataType = DataTypeEnum.String;
                            break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     主轴倍率
    /// </summary>
    /// <returns></returns>
    [Method("OvSpin", name: "主轴倍率")]
    public async Task<DriverReturnValueModel> ReadOvSpin()
    {
        return await ReadRunInfo("OvSpin");
    }

    /// <summary>
    ///     进给倍率
    /// </summary>
    /// <returns></returns>
    [Method("OvFeed", name: "进给倍率")]
    public async Task<DriverReturnValueModel> ReadOvFeed()
    {
        return await ReadRunInfo("OvFeed");
    }

    /// <summary>
    ///     运行状态
    /// </summary>
    /// <returns></returns>
    [Method("RunStatus", description: "0停止;1运行", name: "状态")]
    public async Task<DriverReturnValueModel> ReadRunStatus()
    {
        return await ReadRunInfo("RunStatus");
    }

    /// <summary>
    ///     产量
    /// </summary>
    /// <returns></returns>
    [Method("CurrentNum", name: "产量")]
    public async Task<DriverReturnValueModel> ReadCurrentNum()
    {
        return await ReadRunInfo("CurrentNum");
    }

    /// <summary>
    ///     总产量
    /// </summary>
    /// <returns></returns>
    [Method("CurrentNumTotal", name: "总产量")]
    public async Task<DriverReturnValueModel> ReadCurrentNumTotal()
    {
        return await ReadRunInfo("CurrentNumTotal");
    }

    /// <summary>
    ///     程序号
    /// </summary>
    /// <returns></returns>
    [Method("CurPgm", name: "程序号")]
    public async Task<DriverReturnValueModel> ReadCurPgm()
    {
        return await ReadRunInfo("CurPgm");
    }

    /// <summary>
    ///     程序名
    /// </summary>
    /// <returns></returns>
    [Method("MainName", TransPondDataTypeEnum.String, name: "程序名")]
    public async Task<DriverReturnValueModel> ReadMainName()
    {
        return await ReadRunInfo("MainName");
    }

    #endregion
}