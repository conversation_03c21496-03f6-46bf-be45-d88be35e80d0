using System.Text;
using System.Threading.Tasks;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace Hnc.Base;

/// <summary>
///     一个华中CNC的机床通讯库，目前仅限于测试8系列
/// </summary>
public class HncTcp : NetworkDoubleBase
{
    /// <summary>
    ///     获取设备连接状态
    /// </summary>
    /// <returns></returns>
    public bool GetConnectStatus()
    {
        return pipeSocket != null && (pipeSocket.Socket?.Connected ?? false);
    }

    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public HncTcp(string ipAddress, int port = 10001)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    #endregion

    #region Read Write Support

    /// <summary>
    ///     读取通道信息：主轴倍率、进给倍率、产量、总产量、运行状态、程序号、程序名
    /// </summary>
    /// <returns></returns>
    public async Task<OperateResult<short, short, short, short, short, short, string>> ReadRunInfoAsync()
    {
        var read = await ReadFromCoreServerAsync(BuildReadChannelInfo());
        if (!read.IsSuccess) return OperateResult.CreateFailedResult<short, short, short, short, short, short, string>(read);
        var result = read.Content.RemoveBegin(8);
        int length = ByteTransform.TransUInt16(result, 0);
        if (length == 904)
        {
            var spindlerate = ByteTransform.TransInt16(result, 74); //主轴倍率
            var feedrate = ByteTransform.TransInt16(result, 78); //进给倍率
            var result1 = read.Content.RemoveBegin(152);
            var tagproduce = ByteTransform.TransInt16(result1, 38); //产量
            var curproduce = ByteTransform.TransInt16(result1, 42); //总产量
            var runstatus = ByteTransform.TransInt16(result1, 2); //运行状态
            var number = ByteTransform.TransInt16(result1, 30); //程序号
            var name = Encoding.Default.GetString(result1, 54, 26).TrimEnd('\0'); //程序名;//程序名
            return OperateResult.CreateSuccessResult(spindlerate, feedrate, tagproduce, curproduce, runstatus, number, name);
        }

        return OperateResult.CreateFailedResult<short, short, short, short, short, short, string>(read);
    }


    ///// <summary>
    ///// 读取主轴信息主轴速度、主轴负载、主轴温度
    ///// </summary>
    ///// <returns></returns>
    //public OperateResult<double,double,double> ReadSpindInfo()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(0x2a));
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<double, double, double>(read);
    //    byte[] result = read.Content.RemoveBegin(1288);
    //    double actspeed = ByteTransform.TransDouble(result, 0);//主轴速度
    //    double sload = ByteTransform.TransDouble(result, 8);//主轴负载
    //    double stemper = ByteTransform.TransDouble(result, 16);//主轴温度


    //    return OperateResult.CreateSuccessResult(actspeed, sload, stemper);
    //}

    ///// <summary>
    ///// 读取程序信息包括程序号、块号、顺序号、程序名、注释
    ///// </summary>
    ///// <returns></returns>
    //public OperateResult<int, int, int,string,string> ReadProgramInfo()
    //{
    //    OperateResult<byte[]> read = ReadFromCoreServer(BuildReadArray(0x2b));
    //    if (!read.IsSuccess) return OperateResult.CreateFailedResult<int, int, int, string, string>(read);
    //    byte[] result = read.Content.RemoveBegin(1292);
    //    int wno = ByteTransform.TransInt32(result, 0);//程序号
    //    int blockno = ByteTransform.TransInt32(result, 4);//块号
    //    int seqno = ByteTransform.TransInt32(result, 8);//顺序号
    //    string name = Encoding.Default.GetString(result,16,64).TrimEnd('\0'); //程序名
    //    string annotation = Encoding.Default.GetString(result, 82, 64).TrimEnd('\0'); //程序注释
    //    return OperateResult.CreateSuccessResult(wno, blockno, seqno, name, annotation);
    //}

    #endregion


    #region Build Command

    private byte[] BuildReadChannelInfo()
    {
        var buffer = @"  0x04, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                                0x14, 0x00, 0x01, 0x00, 0x21, 0x00, 0x14, 0x00,
                                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                                0x00, 0x00, 0x00, 0x00, 0x00, 0x00
                            ".ToHexBytes();
        return buffer;
    }

    #endregion
}