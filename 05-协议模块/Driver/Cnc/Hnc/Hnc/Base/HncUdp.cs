using HslCommunication;
using HslCommunication.Core;
using HslCommunication.Core.Net;

namespace Hnc.Base;

/// <summary>
///     一个HncUdp的机床通信类对象
/// </summary>
public class HncUdp : NetworkDoubleBase
{
    #region Constructor

    /// <summary>
    ///     根据IP及端口来实例化一个对象内容
    /// </summary>
    /// <param name="ipAddress">Ip地址信息</param>
    /// <param name="port">端口号</param>
    public HncUdp(string ipAddress, int port = 10001)
    {
        IpAddress = ipAddress;
        Port = port;
        // this.ByteTransform = new ReverseBytesTransform();
        ByteTransform = new RegularByteTransform();
    }

    #endregion
    
    #region Read Write Support

    /// <summary>
    ///     前置报文，必须要UDP先连接上
    /// </summary>
    /// <returns></returns>
    public OperateResult HncUdpInitialization()
    {
        var read1 = ReadFromCoreServer("0x02, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,0x00, 0x00".ToHexBytes());
        if (!read1.IsSuccess) return read1;

        var read2 = ReadFromCoreServer("0x00, 0x00, 0xc0, 0xa8, 0x01, 0x0b, 0x11, 0x27,0x05, 0x00, 0x48, 0x45, 0x4c, 0x4c, 0x4f, 0x00".ToHexBytes());
        if (!read2.IsSuccess) return read2;

        // Array.Reverse(result, 0, 4);
        return OperateResult.CreateSuccessResult();
    }

    #endregion
}