// using System;
// using System.Collections.Generic;
// using System.IO.Ports;
// using System.Linq;
// using System.Net.Sockets;
// using System.Text;
// using System.Text.RegularExpressions;
// using System.Threading;
// using Driver.Core.Models;
// using Furion.Logging;
// using HslCommunication.BasicFramework;
//
// namespace KeQiang_T6F3;
//
// /// <summary>
// /// </summary>
// public class ClientHandlerback : IDisposable
// {
//     private readonly KeQiangT6F3Data _hxData;
//     private readonly StringBuilder _receivedData = new();
//     private readonly DriverInfoDto _driverInfo;
//     private readonly CancellationTokenSource _tokenSource = new();
//     private string _ip;
//
//     /// <summary>
//     ///     连接状态
//     /// </summary>
//     public bool IsConnected
//     {
//         get
//         {
//             try
//             {
//                 if (_tcpClient == null && _serialPort == null)
//                     return false;
//                 return _tcpClient == null || _tcpClient.Connected;
//             }
//             catch
//             {
//                 return false;
//             }
//         }
//     }
//
//     public ClientHandlerback(KeQiangT6F3Data hxData, DriverInfoDto driverInfo)
//     {
//         _hxData = hxData;
//         _driverInfo = driverInfo;
//     }
//
//     #region 网口
//
//     private TcpClient _tcpClient;
//
//     public void Connect(string ipAddress, int port)
//     {
//         _ip = ipAddress + ":" + port;
//         _tcpClient = new TcpClient();
//         _tcpClient.Connect(ipAddress, port);
//         _ = _driverInfo.Socket.Send("Connected to server: " + ipAddress + ":" + port, _driverInfo.DeviceId + "_Logs");
//         _receivedData.Clear();
//         var receiveThread = new Thread(ReceiveData);
//         receiveThread.Start();
//         var threadB = new Thread(WaitAndProcessData);
//         threadB.Start();
//     }
//
//     private void ReceiveData()
//     {
//         var stream = _tcpClient.GetStream();
//         while (!_tokenSource.IsCancellationRequested)
//         {
//             try
//             {
//                 var buffer = new byte[2048];
//                 var bytesRead = stream.Read(buffer, 0, buffer.Length);
//                 if (bytesRead > 0)
//                 {
//                     var resizedBuffer = new byte[bytesRead];
//                     Array.Copy(buffer, resizedBuffer, resizedBuffer.Length);
//                     buffer = resizedBuffer;
//                     var data = SoftBasic.ByteToHexString(buffer, ' ');
//                     _ = _driverInfo.Socket.Send($"来源:[{_ip}]\t 时间：{DateTime.Now}\t【收】:" + data, _driverInfo.DeviceId + "_Logs");
//                     if (_receivedData.Length > 0)
//                         _receivedData.Append(' ');
//                     lock (Lock)
//                     {
//                         _receivedData.Append(data);
//                     }
//
//                     _dataAvailable = true;
//                     Monitor.Pulse(Lock); // 通知等待的线程 
//                 }
//             }
//             catch (Exception ex)
//             {
//                 if (!_tcpClient.Connected)
//                 {
//                     _ = _driverInfo.Socket.Send("连接已断开", _driverInfo.DeviceId + "_Logs");
//                     Log.Information("连接已经断开");
//                     break;
//                 }
//
//                 _ = _driverInfo.Socket.Send("【收】 接收数据发生错误: " + ex.Message, _driverInfo.DeviceId + "_Logs");
//             }
//
//             Thread.Sleep(20);
//         }
//     }
//
//     #endregion 网口
//
//     #region 串口
//
//     private SerialPort _serialPort;
//
//     public void Connect(string serialNumber)
//     {
//         Log.Information($"初始化 串口号: {serialNumber}");
//         _serialPort = new SerialPort();
//         _serialPort.PortName = serialNumber;
//         _serialPort.BaudRate = 38400;
//         _serialPort.DataBits = 8;
//         _serialPort.StopBits = StopBits.One;
//         _serialPort.Parity = Parity.None;
//         _serialPort.Open();
//         _ = _driverInfo.Socket.Send("Connected to serial port: " + _serialPort.PortName, _driverInfo.DeviceId + "_Logs");
//         var receiveThread = new Thread(SerialReceiveData);
//         receiveThread.Start();
//         var threadB = new Thread(WaitAndProcessData);
//         threadB.Start();
//     }
//
//     /// <summary>
//     ///     锁
//     /// </summary>
//     private static readonly object Lock = new();
//
//     /// <summary>
//     ///     标记是否收到新的报文
//     /// </summary>
//     private static bool _dataAvailable;
//
//     private void SerialReceiveData()
//     {
//         var stream = _serialPort;
//         _ = _driverInfo.Socket.Send($"来源:[{stream.PortName}]\t 时间：{DateTime.Now}\t 接收数据中·······", _driverInfo.DeviceId + "_Logs");
//         while (!_tokenSource.IsCancellationRequested)
//         {
//             try
//             {
//                 var buffer = new byte[2048];
//                 var bytesRead = stream.Read(buffer, 0, buffer.Length);
//                 if (bytesRead > 0)
//                     lock (Lock)
//                     {
//                         var resizedBuffer = new byte[bytesRead];
//                         Array.Copy(buffer, resizedBuffer, resizedBuffer.Length);
//                         buffer = resizedBuffer;
//                         var data = SoftBasic.ByteToHexString(buffer, ' ');
//                         _ = _driverInfo.Socket.Send($"来源:[{_serialPort?.PortName}]\t 时间：{DateTime.Now}\t【收】: " + data, _driverInfo.DeviceId + "_Logs");
//                         if (_receivedData.Length > 0)
//                             _receivedData.Append(' ');
//                         _receivedData.Append(data);
//                         _dataAvailable = true;
//                         Monitor.Pulse(Lock); // 通知等待的线程
//                     }
//             }
//             catch (Exception ex)
//             {
//                 if (!stream.IsOpen)
//                 {
//                     _ = _driverInfo.Socket.Send("连接已断开", _driverInfo.DeviceId + "_Logs");
//                     break;
//                 }
//
//                 _ = _driverInfo.Socket.Send("【收】 接收数据发生错误:" + ex.Message, _driverInfo.DeviceId + "_Logs");
//             }
//
//             Thread.Sleep(20);
//         }
//     }
//
//     #endregion 串口
//
//     private void WaitAndProcessData()
//     {
//         lock (Lock)
//         {
//             while (true)
//                 try
//                 {
//                     if (!_dataAvailable)
//                     {
//                         Monitor.Wait(Lock); // 等待通知
//                         continue;
//                     }
//
//                     var copiedData = GetData();
//                     ClearData();
//                     _dataAvailable = false;
//                     ProcessData(copiedData); // 调用处理数据的方法
//                 }
//                 catch
//                 {
//                     ClearData();
//                 }
//         }
//     }
//
//     private string GetData()
//     {
//         lock (Lock)
//         {
//             return _receivedData.ToString();
//         }
//     }
//
//     private void ClearData()
//     {
//         lock (Lock)
//         {
//             _receivedData.Clear();
//         }
//     }
//
//     /// <summary>
//     ///     解析报文
//     /// </summary>
//     private void ProcessData(string data)
//     {
//         // 使用正则表达式匹配符合条件的报文头
//         var match = Regex.Match(data, "(F6 6F 02|F6 6F)");
//         if (!match.Success)
//             return;
//         // 移除不符合条件的报文
//         data = data.Remove(0, match.Index);
//         // 移除后的数据是否符合报文起始
//         if (!data.StartsWith("F6 6F"))
//             return;
//
//         // 检查是否存在多个报文
//         var pattern = "F6 6F(.*?)F6 6F";
//         // 固定顺序码[42]- 无长度字段，只能靠判断下一段报文头来验证
//         if (data.StartsWith("F6 6F 02"))
//             ProcessF6F02Data(ref data, pattern);
//         else if (data.StartsWith("F6 6F") && (data.Substring(27, 5) == "FB CA" || data.Substring(27, 5) == "FB 29"))
//             ProcessF6F14Data(ref data);
//         else if (data.StartsWith("F6 6F") && data.Substring(9, 5) == "05 13" && data.Substring(27, 5) != "00 00")
//             ProcessF6F05Data(ref data);
//         else if (data.StartsWith("F6 6F")) ProcessF6FDefaultData(ref data);
//     }
//     
//     private void ProcessF6F02Data(ref string data, string pattern)
//     {
//         var bytes = ToBytes(data);
//         DealData02(bytes);
//
//         var patternMatch = Regex.Match(data, pattern);
//         if (patternMatch.Success)
//             data = data.Remove(0, patternMatch.Value.Length - 5);
//         else
//             lock (Lock)
//             {
//                 _receivedData.Insert(0, data);
//             }
//     }
//
//     private void ProcessF6F14Data(ref string data)
//     {
//         _driverInfo.Socket.Send($"来源:[{_serialPort?.PortName}]\t 时间：{DateTime.Now}\t【解析 F6 6F 14】: " + data, _driverInfo.DeviceId + "_Logs");
//
//         if (data.Length < 3104)
//         {
//             lock (Lock)
//             {
//                 _receivedData.Insert(0, data);
//             }
//
//             _driverInfo.Socket.Send($"来源:[{_serialPort?.PortName}]\t 时间：{DateTime.Now}\t【解析 F6 6F 14】: 长度不够，等待完整报文... ", _driverInfo.DeviceId + "_Logs");
//             return;
//         }
//
//         var bytes = ToBytes(data);
//         DealData14(bytes);
//         data = data.Remove(0, 3104);
//     }
//
//     private void ProcessF6F05Data(ref string data)
//     {
//         _driverInfo.Socket.Send($"来源:[{_serialPort?.PortName}]\t 时间：{DateTime.Now}\t【解析 F6 6F 05 13 00 00······】: " + data, _driverInfo.DeviceId + "_Logs");
//
//         if (data.Length < 3104)
//         {
//             lock (Lock)
//             {
//                 _receivedData.Insert(0, data);
//             }
//
//             _driverInfo.Socket.Send($"来源:[{_serialPort?.PortName}]\t 时间：{DateTime.Now}\t【解析 F6 6F 05 13 00 00······】: 长度不够，等待完整报文... ", _driverInfo.DeviceId + "_Logs");
//             return;
//         }
//
//         _driverInfo.Socket.Send($"来源:[{_serialPort?.PortName}]\t 时间：{DateTime.Now}\t【解析 F6 6F 05 13 00 00······】: 准备解析了。。。。。。... ", _driverInfo.DeviceId + "_Logs");
//
//         var bytes = ToBytes(data);
//         DealData04(bytes);
//         data = data.Remove(0, 3104);
//     }
//
//     private void ProcessF6FDefaultData(ref string data)
//     {
//         var payload = data;
//         // 长度符合就直接解析
//         if (payload.Length == 44)
//         {
//             SetValue(payload);
//             return;
//         }
//         // 取出两个报文头中的内容
//         var patternMatch = Regex.Match(data, @"(F6 6F.*?)F6 6F");
//         if (!patternMatch.Success)
//         {
//             lock (Lock)
//             {
//                 _receivedData.Insert(0, data);
//             }
//
//             return;
//         }
//         // 再次检查是否符合条件
//         payload = patternMatch.Groups[1].Value.TrimEnd();
//         if (payload.Length == 44)
//             SetValue(payload);
//         
//     }
//
//     private void SetValue(string data)
//     {
//         var bytes = ToBytes(data);
//         var key = data.Substring(18, 5);
//         Log.Information($"解析报文key：{key}");
//         switch (key)
//         {
//             #region 合模位置
//
//             case "62 00": // 合模位置1
//                 _hxData.ClosingPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.001, RoundLength);
//                 return;
//             case "44 00": // 合模位置2
//                 _hxData.ClosingPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.001, RoundLength);
//                 return;
//             case "47 00": // 合模位置3
//                 _hxData.ClosingPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.001, RoundLength);
//                 return;
//             case "4A 00": // 合模位置4
//                 _hxData.ClosingPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.001, RoundLength);
//                 return;
//             case "4D 00": // 合模位置5
//                 _hxData.ClosingPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.001, RoundLength);
//                 return;
//
//             #endregion
//
//             #region 合模压力
//
//             case "40 00": // 合模压力1
//                 _hxData.ClosingPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4));
//                 return;
//             case "42 00": // 合模压力2
//                 _hxData.ClosingPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "45 00": // 合模压力3
//                 _hxData.ClosingPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "48 00": // 合模压力4
//                 _hxData.ClosingPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "4B 00": // 合模压力5
//                 _hxData.ClosingPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//
//             #endregion
//
//             #region 合模速度
//
//             case "41 00": // 合模速度1
//                 _hxData.ClosingSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "43 00": // 合模速度2
//                 _hxData.ClosingSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "46 00": // 合模速度3
//                 _hxData.ClosingSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "49 00": // 合模速度4
//                 _hxData.ClosingSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "4C 00": // 合模速度5
//                 _hxData.ClosingSpeed5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//
//             #endregion
//
//             #region 开模位置
//
//             case "61 00": // 开模位置4
//                 _hxData.OpeningPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "5E 00": // 开模位置3
//                 _hxData.OpeningPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "5B 00": // 开模位置2
//                 _hxData.OpeningPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "58 00": // 开模位置1
//                 _hxData.OpeningPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//
//             #endregion
//
//             #region 开模压力
//
//             case "5F 00": // 开模压力5
//                 _hxData.OpeningPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "5C 00": // 开模压力4
//                 _hxData.OpeningPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "59 00": // 开模压力3
//                 _hxData.OpeningPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "56 00": // 开模压力2
//                 _hxData.OpeningPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "54 00": // 开模压力1
//                 _hxData.OpeningPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//
//             #endregion
//
//             #region 开模速度
//
//             case "60 00": // 开模速度5
//                 _hxData.OpeningSpeed5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "5D 00": // 开模速度4
//                 _hxData.OpeningSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "5A 00": // 开模速度3
//                 _hxData.OpeningSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "57 00": // 开模速度2
//                 _hxData.OpeningSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//             case "55 00": // 开模速度1
//                 _hxData.OpeningSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//
//             #endregion
//
//             case "6C 00": // 射出一段压力
//                 _hxData.InjectionPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4)) * 0.01, RoundLength);
//                 return;
//
//             case "12 00": // 目标产量
//             {
//                 Log.Information($"修改目标产量为: {BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4))}");
//                 _hxData.ProductionTarget = BitConverter.ToInt32(GetHexStringFromByteArray(bytes, 9, 4));
//                 return;
//             }
//         }
//     }
//
//     private byte[] ToBytes(string data)
//     {
//         var parts = data.Split(' ', StringSplitOptions.RemoveEmptyEntries);
//         var bytes = new byte[parts.Length];
//         for (var i = 0; i < parts.Length; i++) bytes[i] = Convert.ToByte(parts[i], 16);
//         return bytes;
//     }
//
//     /// <summary>
//     ///     处理控制器发出的数据 F6 6F 02
//     /// </summary>
//     /// <param name="buff"></param>
//     private void DealData02(byte[] buff)
//     {
//         // byte[] sequence = {0x8D, 0x00};
//         // var indexes = FindIndex(buff, sequence);
//         // foreach (var index in indexes) Console.WriteLine("下标地址: " + index);
//         _hxData.AnalogToDigital = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 39, 4));
//         _hxData.AnalogToDigital = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 39, 4));
//         _hxData.Pbar = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 67, 2));
//         _hxData.Fbar = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 69, 2));
//         _hxData.BP = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 71, 2));
//         _hxData.OilTemperature = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 161, 2));
//
//         _hxData.TemperatureSegment1 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 163, 2));
//         _hxData.TemperatureSegment2 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 165, 2));
//         _hxData.TemperatureSegment3 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 167, 2));
//         _hxData.TemperatureSegment4 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 169, 2));
//         _hxData.TemperatureSegment5 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 171, 2));
//         _hxData.TemperatureSegment6 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 173, 2));
//         _hxData.TemperatureSegment7 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 175, 2));
//         _hxData.CycleTime = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(buff, 393, 4)) * 0.1, RoundLength);
//
//         _hxData.InjectionTime = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 429, 4)) * 0.1, RoundLength);
//         _hxData.InjectionEndPosition = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 435, 4)) * 0.1, RoundLength);
//         _hxData.MaxInjectionSpeed = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 441, 5)) * 0.1, RoundLength);
//         _hxData.MaterialStorageTime = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 447, 4)) * 0.1, RoundLength);
//         _hxData.MaterialStorageEnd = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 453, 4)) * 0.1, RoundLength);
//         _hxData.SwitchHoldPosition = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 471, 4)) * 0.1, RoundLength);
//
//     }
//
//     /// <summary>
//     ///     小数保留位数
//     /// </summary>
//     private const int RoundLength = 1;
//
//     /// <summary>
//     ///     开机的数据 F6 6F 74 05 13
//     ///     模座数据
//     /// </summary>
//     /// <param name="buff"></param>
//     private void DealData14(byte[] buff)
//     {
//         #region 合模位置
//
//         _hxData.ClosingPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 401, 4)) * 0.001, RoundLength);
//         _hxData.ClosingPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 281, 4)) * 0.001, RoundLength);
//         _hxData.ClosingPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 293, 4)) * 0.001, RoundLength);
//         _hxData.ClosingPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 305, 4)) * 0.001, RoundLength);
//         _hxData.ClosingPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 317, 4)) * 0.001, RoundLength);
//
//         #endregion
//
//         #region 合模压力
//
//         _hxData.ClosingPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 265, 4));
//         _hxData.ClosingPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 273, 4));
//         _hxData.ClosingPressure3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 285, 4));
//         _hxData.ClosingPressure4 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 297, 4));
//         _hxData.ClosingPressure5 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 309, 4));
//
//         #endregion
//
//         _hxData.ClosingSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 269, 4));
//         _hxData.ClosingSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 277, 4));
//         _hxData.ClosingSpeed3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 289, 4));
//         _hxData.ClosingSpeed4 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 301, 4));
//         _hxData.ClosingSpeed5 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 313, 4));
//
//         _hxData.OpeningPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 345, 4));
//         _hxData.OpeningSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 349, 4));
//         _hxData.OpeningPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 353, 4));
//         _hxData.OpeningSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 357, 4));
//         // 开模位置1
//         _hxData.OpeningPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 361, 4)) * 0.001, RoundLength);
//         _hxData.OpeningPressure3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 365, 4));
//         _hxData.OpeningSpeed3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 369, 4));
//         //
//         _hxData.OpeningPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 373, 4)) * 0.001, RoundLength);
//         _hxData.OpeningPressure4 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 377, 4));
//         _hxData.OpeningSpeed4 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 381, 4));
//         //
//         _hxData.OpeningPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 385, 4)) * 0.001, RoundLength);
//         _hxData.OpeningPressure5 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 389, 4));
//         _hxData.OpeningSpeed5 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 393, 4));
//         //
//         _hxData.OpeningPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 397, 4)) * 0.001, RoundLength);
//         _hxData.OpeningStroke = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 401, 4));
//
//         //
//         _hxData.OpeningPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 401, 4)) * 0.001, RoundLength);
//         _hxData.InjectionPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 441, 4));
//         _hxData.InjectionSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 445, 4));
//         _hxData.InjectionPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 449, 4));
//         _hxData.InjectionSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 453, 4));
//         // 注射位置1
//         _hxData.InjectionPosition1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 457, 4));
//         _hxData.InjectionPressure3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 361, 4));
//         _hxData.InjectionSpeed3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 465, 4));
//         //
//         _hxData.InjectionPosition2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 469, 4));
//         _hxData.InjectionPressure4 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 473, 4));
//         _hxData.InjectionSpeed4 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 477, 5));
//         // 
//         _hxData.InjectionPosition3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 481, 4));
//         _hxData.InjectionPressure5 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 485, 5));
//         _hxData.InjectionSpeed5 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 489, 5));
//         //
//         _hxData.InjectionPosition4 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 493, 4));
//         _hxData.InjectionPressure6 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 497, 4));
//         _hxData.InjectionSpeed6 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 501, 4));
//         // 
//         _hxData.InjectionPosition5 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 505, 4));
//         _hxData.RetractionPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 573, 4));
//         _hxData.RetractionSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 577, 4));
//         _hxData.RetractionTime1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 581, 4));
//         _hxData.RetractionPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 585, 4));
//         _hxData.RetractionSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 589, 4));
//         _hxData.RetractionTime2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 593, 4));
//         _hxData.HoldingPressure5 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 781, 4));
//         _hxData.HoldingSpeed5 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 785, 4));
//         _hxData.HoldingBackPressure5 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 789, 4));
//         _hxData.HoldingPressure4 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 797, 4));
//         _hxData.HoldingSpeed4 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 801, 4));
//         _hxData.HoldingBackPressure4 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 805, 4));
//         // 储料位置4
//         _hxData.HoldingPosition4 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 809, 4));
//         // 储料位置5
//         _hxData.HoldingPosition5 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 893, 4));
//         _hxData.HoldingPressure = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 937, 4));
//         // 储料速度
//         _hxData.HoldingSpeed = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 941, 4));
//         // 储料距离
//         _hxData.HoldingDistance = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 945, 4));
//         _hxData.TopInPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 949, 4));
//         _hxData.TopInSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 997, 4));
//         _hxData.TopInPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1001, 4));
//         _hxData.TopInSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1005, 4));
//         // 顶进位置1
//         _hxData.TopInPosition1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1009, 4));
//         // 顶进位置2
//         _hxData.TopInPosition2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1013, 4));
//         _hxData.TopOutPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1017, 4));
//         _hxData.TopOutSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1021, 4));
//         _hxData.TopOutPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1025, 4));
//     }
//
//     /// <summary>
//     ///     开机的数据 F6 6F 04 05 13
//     /// </summary>
//     /// <param name="buff"></param>
//     private void DealData04(byte[] buff)
//     {
//         _hxData.TopOutSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 9, 4));
//         // 顶退位置1
//         _hxData.TopOutPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 13, 4)) * 0.001, RoundLength);
//         // 顶退位置2
//         _hxData.TopOutPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 17, 4)) * 0.001, RoundLength);
//         // 目标产量
//         _hxData.ProductionTarget = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 81, 4));
//         _hxData.BedInPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 109, 4));
//         _hxData.BedInPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 133, 4));
//         _hxData.BedInSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 113, 4));
//         _hxData.BedInSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 137, 4));
//         _hxData.BedInTime1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 117, 4));
//         _hxData.BedInTime2 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 129, 4));
//         _hxData.BedInTime3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 141, 4));
//         // _hxData.BedInPressure3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 133, 4)) ;
//         //_hxData.BedInSpeed3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 137, 4)) ;
//         _hxData.BedOutPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 145, 4));
//         _hxData.BedOutSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 149, 4));
//         _hxData.BedOutTime1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 169, 4)) * 0.00001, RoundLength);
//         _hxData.MoldOutPressure = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 193, 4));
//         _hxData.MoldOutSpeed = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 197, 4));
//         _hxData.MoldInPressure = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 201, 4));
//         _hxData.MoldInSpeed = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 205, 4));
//         _hxData.MoldInSlowSpeed = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 209, 4));
//     }
//
//     /// <summary>
//     ///     查找数组中下标
//     /// </summary>
//     /// <param name="byteArray"></param>
//     /// <param name="sequence"></param>
//     /// <returns></returns>
//     private static List<int> FindIndex(byte[] byteArray, byte[] sequence)
//     {
//         var indexes = new List<int>();
//         for (var i = 0; i <= byteArray.Length - sequence.Length; i++)
//         {
//             var found = true;
//             for (var j = 0; j < sequence.Length; j++)
//                 if (byteArray[i + j] != sequence[j])
//                 {
//                     found = false;
//                     break;
//                 }
//
//             if (found) indexes.Add(i);
//         }
//
//         return indexes;
//     }
//
//     /// <summary>
//     /// </summary>
//     /// <param name="byteArray"></param>
//     /// <param name="startIndex"></param>
//     /// <param name="length"></param>
//     /// <returns></returns>
//     private static byte[] GetHexStringFromByteArray(byte[] byteArray, int startIndex, int length)
//     {
//         var subData = new byte[length];
//         Array.Copy(byteArray, startIndex, subData, 0, length);
//         //byte[] reversedData = SoftBasic.BytesReverseByWord(subData);
//         return subData;
//     }
//
//     public void Dispose()
//     {
//         _tokenSource.Cancel();
//         Thread.Sleep(200);
//         _serialPort?.Close();
//         _serialPort?.Dispose();
//     }
// }