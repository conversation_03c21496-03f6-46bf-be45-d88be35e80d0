using System;
using System.Collections.Concurrent;
using System.IO;
using System.IO.Ports;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Driver.Core.Models;
using HslCommunication.BasicFramework;
using KeQiangT6F3;

namespace KeQiang_T6F3;

/// <summary>
/// </summary>
public class ClientHandler : IDisposable
{
    private readonly KeQiangT6F3Data _entity;
    private readonly DriverInfoDto _driverInfo;
    private readonly CancellationTokenSource _tokenSource = new();
    private readonly BlockingCollection<byte[]> _messageQueue = new();
    private readonly MemoryStream _memoryStream = new();
    private readonly Task _parseTask;
    private readonly Task _processTask;
    private readonly ConcurrentQueue<byte[]> _receivedDataQueue = new();
    private readonly byte[] FrameHeader = { 0xF6, 0x6F };
    private volatile bool _isRunning = true;
    private bool _disposed;
    private SerialPort _serialPort;
    private TcpClient _tcpClient;
    private DateTime _lastReceiveTime = DateTime.Now;

    private const int RoundLength = 1;

    /// <summary>
    ///     连接状态
    /// </summary>
    public bool IsConnected
    {
        get
        {
            try
            {
                if (_tcpClient == null && _serialPort == null)
                    return false;
                return _tcpClient == null || (_tcpClient.Connected && DateTime.Now - _lastReceiveTime < TimeSpan.FromSeconds(30)) || _serialPort.IsOpen;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    ///     ip地址
    /// </summary>
    private string _ipAddress;

    /// <summary>
    ///     初始化
    /// </summary>
    public ClientHandler(KeQiangT6F3Data entity, DriverInfoDto driverInfo)
    {
        _entity = entity;
        _driverInfo = driverInfo;

        // 启动解析和处理线程
        _parseTask = Task.Run(ParseMessages, _tokenSource.Token);
        _processTask = Task.Run(ProcessMessages, _tokenSource.Token);
    }

    /// <summary>
    ///     连接
    /// </summary>
    /// <param name="ipAddress"></param>
    /// <param name="port"></param>
    public void Connect(string ipAddress, int port)
    {
        _tcpClient = new TcpClient(ipAddress, port);
        _ipAddress = ipAddress + ":" + port;
        _ = _driverInfo.Socket.Send($"ip:{_ipAddress} Connected to server: " + ipAddress + ":" + port, _driverInfo.DeviceId + "_Logs");
        _ = Task.Run(ProcessMessages); // 启动消息处理任务
        _ = ReceiveDataAsync();
    }

    /// <summary>
    ///     接收数据
    /// </summary>
    private async Task ReceiveDataAsync()
    {
        try
        {
            await using var stream = _tcpClient.GetStream();
            var buffer = new byte[2048];
            while (!_tokenSource.Token.IsCancellationRequested)
            {
                var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, _tokenSource.Token);
                if (bytesRead == 0) break;

                // 更新最后接收时间
                _lastReceiveTime = DateTime.Now;

                // 将数据复制到新数组并加入队列
                var receivedData = new byte[bytesRead];
                Array.Copy(buffer, receivedData, bytesRead);
                _receivedDataQueue.Enqueue(receivedData);
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消,不需要处理
        }
        catch (Exception ex)
        {
            _ = _driverInfo.Socket.Send($"接收数据异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
        }
    }

    /// <summary>
    ///     解析报文
    /// </summary>
    private async Task ParseMessages()
    {
        while (_isRunning)
            try
            {
                // 将队列中的数据写入内存流
                while (_receivedDataQueue.TryDequeue(out var data))
                {
                    _ = _driverInfo.Socket.Send($"ip:{_ipAddress} {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} 收：{SoftBasic.ByteToHexString(data, ' ')}", _driverInfo.DeviceId + "_Logs");
                    _memoryStream.Write(data, 0, data.Length);
                }

                // 获取内存流长度
                var streamLength = _memoryStream.Length;

                // 如果数据太少，等待下次解析
                if (streamLength < 6)
                {
                    await Task.Delay(10);
                    continue;
                }

                // 重置流位置到开始
                _memoryStream.Position = 0;
                // 持续解析直到无法找到完整报文
                while (true)
                {
                    // 更新当前可读取的数据长度
                    streamLength = _memoryStream.Length - _memoryStream.Position;

                    // _ = _driverInfo.Socket.Send($"当前可读取数据长度: {streamLength}, 当前位置: {_memoryStream.Position}", _driverInfo.DeviceId + "_Logs");
                    // 如果剩余数据太少，退出当前循环
                    if (streamLength < 6) break;

                    // 查找帧头
                    var headerIndex = FindFrameHeader(_memoryStream, (int)_memoryStream.Position);
                    if (headerIndex == -1)
                    {
                        // 未找到帧头,清空缓存
                        _memoryStream.SetLength(0);
                        break;
                    }

                    // 移动到帧头位置
                    _memoryStream.Position = headerIndex;

                    // 数据不足时保留
                    if (_memoryStream.Length < headerIndex + 6) // 需要至少6字节:帧头(2)+长度(2)+命令字(2)
                    {
                        _ = _driverInfo.Socket.Send($"ip:{_ipAddress} 帧头后数据不足6字节，保留数据等待下次解析", _driverInfo.DeviceId + "_Logs");
                        break; // 改用break而不是goto，保留数据等待下次解析
                    }

                    // 读取命令字节
                    var messageTypeByte = new byte[2];
                    _memoryStream.Position = headerIndex + 4; // 跳过帧头(2字节)和长度(2字节)
                    _memoryStream.Read(messageTypeByte, 0, 2);

                    // 读取长度
                    var lengthBytes = new byte[2];
                    _memoryStream.Position = headerIndex + 2; // 跳过帧头2字节
                    _memoryStream.Read(lengthBytes, 0, 2); // 读取长度
                    // 计算长度：(第一个字节&0x0F)*256 + 第二个字节 + 6
                    var messageLength = (((lengthBytes[0] & 0x0F) << 8) | lengthBytes[1]) + 6;

                    // 打印长度相关信息进行调试
                    //_ = _driverInfo.Socket.Send($"报文长度解析: 长度字节[{SoftBasic.ByteToHexString(lengthBytes, ' ')}], 第一字节&0x0F=[{lengthBytes[0] & 0x0F:X2}], 计算长度:{messageLength}", _driverInfo.DeviceId + "_Logs");
                    // 根据消息类型字节判断报文类型
                    switch (messageTypeByte[0])
                    {
                        case 0xED when messageTypeByte[1] == 0x00:
                            break;
                        case 0x13 when messageTypeByte[1] == 0x00:
                            break;
                        case 0x13 when messageTypeByte[1] == 0x01:
                            break;
                        default:
                            {
                                // 如果报文长度不为15，则跳过当前帧头继续查找
                                if (messageLength != 15)
                                {
                                    // 跳过当前帧头继续查找
                                    _memoryStream.Position = headerIndex + 2;
                                    continue;
                                }

                                break;
                            }
                    }

                    // 检查异常长度
                    if (messageLength < 15 || messageLength > 2048)
                    {
                        // _ = _driverInfo.Socket.Send($"报文长度异常: {messageLength}, 长度字节:[{SoftBasic.ByteToHexString(lengthBytes, ' ')}]", _driverInfo.DeviceId + "_Logs");
                        _memoryStream.Position = headerIndex + 2; // 跳过当前帧头继续查找
                        continue;
                    }

                    // 检查是否有完整报文
                    if (_memoryStream.Length < headerIndex + messageLength) goto ContinueOuterLoop; // 直接退出内层循环，保留数据等待更多数据

                    // 读取完整消息
                    var message = new byte[messageLength];
                    _memoryStream.Position = headerIndex;
                    _memoryStream.Read(message, 0, messageLength);

                    // 验证读取的数据长度
                    if (message.Length != messageLength)
                    {
                        // _ = _driverInfo.Socket.Send($"读取的数据长度不正确，期望{messageLength}字节，实际读取{message.Length}字节", _driverInfo.DeviceId + "_Logs");
                        _memoryStream.Position = headerIndex + 2;
                        continue;
                    }

                    // 校验CRC
                    var isValid = CRC16Helper.ValidateCRC16(message);
                    if (!isValid)
                    {
                        // 增加更详细的错误信息
                        _ = _driverInfo.Socket.Send($"ip:{_ipAddress} CRC校验失败，报文长度:{messageLength}, 报文内容: {SoftBasic.ByteToHexString(message, ' ')}", _driverInfo.DeviceId + "_Logs");

                        // 尝试查找下一个帧头
                        _memoryStream.Position = headerIndex + 2;
                        var nextHeaderIndex = FindFrameHeader(_memoryStream, (int)_memoryStream.Position);
                        if (nextHeaderIndex != -1)
                        {
                            _ = _driverInfo.Socket.Send($"ip:{_ipAddress} 找到下一个帧头，位置: {nextHeaderIndex}", _driverInfo.DeviceId + "_Logs");
                            _memoryStream.Position = nextHeaderIndex;
                        }
                        else
                        {
                            _memoryStream.Position = headerIndex + messageLength;
                        }

                        continue;
                    }
                    // 添加到消息队列
                    _messageQueue.Add(message);

                    // 移动到下一个位置
                    _memoryStream.Position = headerIndex + messageLength;
                }

                // 只有在成功处理完一个完整报文后才清理数据
                if (_memoryStream.Position > 0) CleanUpMemoryStream();

                // 短暂等待以避免CPU占用过高
                await Task.Delay(10);

            ContinueOuterLoop:
                {
                    // 短暂等待以避免CPU占用过高
                    await Task.Delay(100);
                    continue;
                }
            }
            catch (Exception ex)
            {
                _ = _driverInfo.Socket.Send($"ip:{_ipAddress} 解析报文异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
                // 发生异常时不清空缓存，保留数据等待下次解析
                await Task.Delay(100);
            }
    }

    /// <summary>
    ///     查找帧头
    /// </summary>
    /// <param name="stream"></param>
    /// <param name="startIndex"></param>
    /// <returns></returns>
    private int FindFrameHeader(MemoryStream stream, int startIndex = 0)
    {
        // 搜索帧头 F6 6F
        var buffer = stream.ToArray();
        for (var i = startIndex; i <= buffer.Length - FrameHeader.Length; i++)
            if (buffer[i] == FrameHeader[0] && buffer[i + 1] == FrameHeader[1])
                return i; // 找到帧头，返回索引

        return -1; // 未找到帧头
    }

    /// <summary>
    ///     清理未读取的部分
    /// </summary>
    private void CleanUpMemoryStream()
    {
        if (_memoryStream.Position > 0 && _memoryStream.Position < _memoryStream.Length)
        {
            var remainingLength = (int)(_memoryStream.Length - _memoryStream.Position);
            if (remainingLength > 2048) // 设置一个合理的阈值
            {
                // 取出不完整报文
                var remainingBytes = _memoryStream.ToArray()[(int)_memoryStream.Position..];
                _memoryStream.SetLength(0);
                // 重新放入到开头
                _memoryStream.Write(remainingBytes, 0, remainingBytes.Length);
            }
        }
        else if (_memoryStream.Length > 4096) // 如果数据流过大但没有有效数据，则清空
        {
            _memoryStream.SetLength(0);
        }
    }

    /// <summary>
    ///     解析实际报文
    /// </summary>
    private void ProcessMessages()
    {
        // 遍历消息队列
        foreach (var message in _messageQueue.GetConsumingEnumerable(_tokenSource.Token))
            try
            {
                // 添加基本的长度检查
                if (message == null || message.Length < 3) continue;

                // 打印解析报文
                _ = _driverInfo.Socket.Send($"ip:{_ipAddress} 解析报文: {SoftBasic.ByteToHexString(message, ' ')}", _driverInfo.DeviceId + "_Logs");
                // 长度为15时，直接设置值
                if (message.Length == 15)
                {
                    _ = _driverInfo.Socket.Send($"ip:{_ipAddress} 长度为15时，直接设置值", _driverInfo.DeviceId + "_Logs");
                    SetValue(message);
                }
                else
                {
                    // 根据消息类型处理不同的报文
                    switch (message[4])
                    {
                        case 0xED when message[5] == 0x00:
                            ProcessControllerData(message);
                            break;
                        case 0x13 when message[5] == 0x00:
                            DealData1300(message);
                            break;
                        case 0x13 when message[5] == 0x01:
                            DealData1301(message);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _ = _driverInfo.Socket.Send($"ip:{_ipAddress} 时间：{DateTime.Now}\t消息处理异常: {ex.Message}\n{ex.StackTrace}", _driverInfo.DeviceId + "_Logs");

                if (ex is OperationCanceledException) break;
                Thread.Sleep(200); // 发生异常时增加更长的延时
            }
    }

    /// <summary>
    ///     开机的数据 F6 6F 74 05 13 00
    ///     模座数据
    /// </summary>
    private void DealData1300(byte[] byteData)
    {
        #region 合模位置

        _entity.ClosingPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 401, 4)) * 0.001, RoundLength);
        _entity.ClosingPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 281, 4)) * 0.001, RoundLength);
        _entity.ClosingPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 293, 4)) * 0.001, RoundLength);
        _entity.ClosingPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 305, 4)) * 0.001, RoundLength);
        _entity.ClosingPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 317, 4)) * 0.001, RoundLength);

        #endregion

        #region 合模压力

        _entity.ClosingPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 265, 4));
        _entity.ClosingPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 273, 4));
        _entity.ClosingPressure3 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 285, 4));
        _entity.ClosingPressure4 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 297, 4));
        _entity.ClosingPressure5 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 309, 4));

        #endregion

        _entity.ClosingSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 269, 4));
        _entity.ClosingSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 277, 4));
        _entity.ClosingSpeed3 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 289, 4));
        _entity.ClosingSpeed4 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 301, 4));
        _entity.ClosingSpeed5 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 313, 4));

        _entity.OpeningPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 345, 4));
        _entity.OpeningSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 349, 4));
        _entity.OpeningPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 353, 4));
        _entity.OpeningSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 357, 4));
        // 开模位置1
        _entity.OpeningPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 361, 4)) * 0.001, RoundLength);
        _entity.OpeningPressure3 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 365, 4));
        _entity.OpeningSpeed3 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 369, 4));
        //
        _entity.OpeningPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 373, 4)) * 0.001, RoundLength);
        _entity.OpeningPressure4 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 377, 4));
        _entity.OpeningSpeed4 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 381, 4));
        //
        _entity.OpeningPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 385, 4)) * 0.001, RoundLength);
        _entity.OpeningPressure5 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 389, 4));
        _entity.OpeningSpeed5 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 393, 4));
        //
        _entity.OpeningPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 397, 4)) * 0.001, RoundLength);
        _entity.OpeningStroke = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 401, 4));

        //
        _entity.OpeningPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 401, 4)) * 0.001, RoundLength);
        _entity.InjectionPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 441, 4));
        _entity.InjectionSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 445, 4));
        _entity.InjectionPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 449, 4));
        _entity.InjectionSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 453, 4));
        // 注射位置1
        _entity.InjectionPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 457, 4)) * 0.001, RoundLength);
        _entity.InjectionPressure3 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 361, 4));
        _entity.InjectionSpeed3 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 465, 4));
        // 注射位置2
        _entity.InjectionPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 469, 4)) * 0.001, RoundLength);
        _entity.InjectionPressure4 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 473, 4));
        _entity.InjectionSpeed4 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 477, 5));
        // 注射位置3
        _entity.InjectionPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 481, 4)) * 0.001, RoundLength);
        _entity.InjectionPressure5 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 485, 5));
        _entity.InjectionSpeed5 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 489, 5));
        // 注射位置4
        _entity.InjectionPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 493, 4)) * 0.001, RoundLength);
        _entity.InjectionPressure6 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 497, 4));
        _entity.InjectionSpeed6 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 501, 4));
        // 注射位置5
        _entity.InjectionPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 505, 4)) * 0.001, RoundLength);
        _entity.RetractionPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 573, 4));
        _entity.RetractionSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 577, 4));
        _entity.RetractionTime1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 581, 4));
        _entity.RetractionPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 585, 4));
        _entity.RetractionSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 589, 4));
        _entity.RetractionTime2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 593, 4));
        _entity.HoldingPressure5 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 781, 4));
        _entity.HoldingSpeed5 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 785, 4));
        _entity.HoldingBackPressure5 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 789, 4));
        _entity.HoldingPressure4 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 797, 4));
        _entity.HoldingSpeed4 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 801, 4));
        _entity.HoldingBackPressure4 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 805, 4));
        // 储料位置4
        _entity.HoldingPosition4 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 809, 4));
        // 储料位置5
        _entity.HoldingPosition5 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 893, 4));
        _entity.HoldingPressure = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 937, 4));
        // 储料速度
        _entity.HoldingSpeed = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 941, 4));
        // 储料距离
        _entity.HoldingDistance = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 945, 4));
        _entity.TopInPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 949, 4));
        _entity.TopInSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 997, 4));
        _entity.TopInPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1001, 4));
        _entity.TopInSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1005, 4));
        // 顶进位置1
        _entity.TopInPosition1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1009, 4));
        // 顶进位置2
        _entity.TopInPosition2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1013, 4));
        _entity.TopOutPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1017, 4));
        _entity.TopOutSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1021, 4));
        _entity.TopOutPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 1025, 4));
    }

    /// <summary>
    ///     开机的数据 F6 6F 04 05 13 01
    /// </summary>
    private void DealData1301(byte[] byteData)
    {
        _entity.TopOutSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4));
        // 顶退位置1
        _entity.TopOutPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 13, 4)) * 0.001, RoundLength);
        // 顶退位置2
        _entity.TopOutPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 17, 4)) * 0.001, RoundLength);
        // 目标产量
        _entity.ProductionTarget = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 81, 4));
        _entity.BedInPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 109, 4));
        _entity.BedInPressure2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 133, 4));
        _entity.BedInSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 113, 4));
        _entity.BedInSpeed2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 137, 4));
        _entity.BedInTime1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 117, 4));
        _entity.BedInTime2 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 129, 4));
        _entity.BedInTime3 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 141, 4));
        // entity.BedInPressure3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 133, 4)) ;
        //entity.BedInSpeed3 = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 137, 4)) ;
        _entity.BedOutPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 145, 4));
        _entity.BedOutSpeed1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 149, 4));
        _entity.BedOutTime1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 169, 4)) * 0.00001, RoundLength);
        _entity.MoldOutPressure = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 193, 4));
        _entity.MoldOutSpeed = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 197, 4));
        _entity.MoldInPressure = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 201, 4));
        _entity.MoldInSpeed = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 205, 4));
        _entity.MoldInSlowSpeed = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 209, 4));
    }

    /// <summary>
    ///     处理控制器发出的数据 F6 6F 02
    /// </summary>
    /// <param name="byteData"></param>
    private void ProcessControllerData(byte[] byteData)
    {
        // byte[] sequence = {0x8D, 0x00};
        // var indexes = FindIndex(buff, sequence);
        // foreach (var index in indexes) Console.WriteLine("下标地址: " + index);
        _entity.AnalogToDigital = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 39, 4));
        _entity.Pbar = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 67, 2));
        _entity.Fbar = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 69, 2));
        _entity.BP = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 71, 2));
        _entity.OilTemperature = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 161, 2));

        _entity.TemperatureSegment1 = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 163, 2));
        _entity.TemperatureSegment2 = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 165, 2));
        _entity.TemperatureSegment3 = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 167, 2));
        _entity.TemperatureSegment4 = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 169, 2));
        _entity.TemperatureSegment5 = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 171, 2));
        _entity.TemperatureSegment6 = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 173, 2));
        _entity.TemperatureSegment7 = BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 175, 2));
        _entity.CycleTime = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(byteData, 393, 4)) * 0.1, RoundLength);

        _entity.InjectionTime = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 429, 4)) * 0.1, RoundLength);
        _entity.InjectionEndPosition = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 435, 4)) * 0.1, RoundLength);
        _entity.MaxInjectionSpeed = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 441, 5)) * 0.1, RoundLength);
        _entity.MaterialStorageTime = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 447, 4)) * 0.1, RoundLength);
        _entity.MaterialStorageEnd = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 453, 4)) * 0.1, RoundLength);
        _entity.SwitchHoldPosition = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 471, 4)) * 0.1, RoundLength);
    }

    /// <summary>
    ///     下写数据
    /// </summary>
    /// <param name="byteData"></param>
    private void SetValue(byte[] byteData)
    {
        var key = GetHexStringFromByteArray(byteData, 6, 2);

        switch (key[0])
        {
            #region 合模位置

            // 合模位置1   
            case 0x62 when key[1] == 0x00:
                _entity.ClosingPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                break;
            // 合模位置2
            case 0x44 when key[1] == 0x00:
                _entity.ClosingPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                break;
            // 合模位置3
            case 0x47 when key[1] == 0x00:
                _entity.ClosingPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                break;
            // 合模位置4
            case 0x4A when key[1] == 0x00:
                _entity.ClosingPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                break;
            // 合模位置5
            case 0x4D when key[1] == 0x00:
                _entity.ClosingPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                break;

            #endregion

            #region 合模压力

            // 合模压力1
            case 0x40 when key[1] == 0x00:
                _entity.ClosingPressure1 = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4));
                return;
            // 合模压力2
            case 0x42 when key[1] == 0x00:
                _entity.ClosingPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模压力3
            case 0x45 when key[1] == 0x00:
                _entity.ClosingPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模压力4
            case 0x48 when key[1] == 0x00:
                _entity.ClosingPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模压力5
            case 0x4B when key[1] == 0x00:
                _entity.ClosingPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;

            #endregion

            #region 合模速度

            // 合模速度1
            case 0x41 when key[1] == 0x00:
                _entity.ClosingSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模速度2
            case 0x43 when key[1] == 0x00:
                _entity.ClosingSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模速度3
            case 0x46 when key[1] == 0x00:
                _entity.ClosingSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模速度4
            case 0x49 when key[1] == 0x00:
                _entity.ClosingSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 合模速度5
            case 0x4C when key[1] == 0x00:
                _entity.ClosingSpeed5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;

            #endregion

            #region 开模位置

            // 开模位置4
            case 0x61 when key[1] == 0x00:
                _entity.OpeningPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模位置3
            case 0x5E when key[1] == 0x00:
                _entity.OpeningPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模位置2
            case 0x5B when key[1] == 0x00:
                _entity.OpeningPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模位置1
            case 0x58 when key[1] == 0x00:
                _entity.OpeningPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;

            #endregion

            #region 开模压力

            // 开模压力5
            case 0x5F when key[1] == 0x00:
                _entity.OpeningPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模压力4
            case 0x5C when key[1] == 0x00:
                _entity.OpeningPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模压力3
            case 0x59 when key[1] == 0x00:
                _entity.OpeningPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模压力2
            case 0x56 when key[1] == 0x00:
                _entity.OpeningPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模压力1
            case 0x54 when key[1] == 0x00:
                _entity.OpeningPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;

            #endregion

            #region 开模速度

            // 开模速度5
            case 0x60 when key[1] == 0x00:
                _entity.OpeningSpeed5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模速度4
            case 0x5D when key[1] == 0x00:
                _entity.OpeningSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模速度3
            case 0x5A when key[1] == 0x00:
                _entity.OpeningSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模速度2
            case 0x57 when key[1] == 0x00:
                _entity.OpeningSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;
            // 开模速度1
            case 0x55 when key[1] == 0x00:
                _entity.OpeningSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;

            #endregion

            // 射出一段压力
            case 0x6C when key[1] == 0x00:
                _entity.InjectionPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                return;

            // 目标产量
            case 0x12 when key[1] == 0x00:
                {
                    _entity.ProductionTarget = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4));
                    return;
                }
        }
    }

    /// <summary>
    ///     获取字节数组中的指定长度的子数组
    /// </summary>
    /// <param name="byteArray"></param>
    /// <param name="startIndex"></param>
    /// <param name="length"></param>
    /// <returns></returns>
    private byte[] GetHexStringFromByteArray(byte[] byteArray, int startIndex, int length)
    {
        try
        {
            // 添加参数检查
            if (byteArray == null)
                throw new ArgumentNullException(nameof(byteArray));

            if (startIndex < 0)
                throw new ArgumentOutOfRangeException(nameof(startIndex));

            if (length < 0)
                throw new ArgumentOutOfRangeException(nameof(length));

            if (startIndex + length > byteArray.Length) throw new ArgumentException($"请求的数据范围超出数组边界。数组长度:{byteArray.Length}, 起始位置:{startIndex}, 请求长度:{length}");

            var subData = new byte[length];
            Array.Copy(byteArray, startIndex, subData, 0, length);

            // 记录提取的数据
            var hexString = SoftBasic.ByteToHexString(subData, ' ');

            return subData;
        }
        catch (Exception ex)
        {
            _ = _driverInfo.Socket.Send($"ip:{_ipAddress} [{DateTime.Now:HH:mm:ss.fff}] 提取数据异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
            throw;
        }
    }

    /// <summary>
    ///     释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _isRunning = false;
            _tokenSource.Cancel();

            try
            {
                Task.WaitAll(new[] { _parseTask, _processTask }, 1000);
            }
            catch (AggregateException)
            {
            }

            _tokenSource.Dispose();
            _messageQueue.Dispose();
            _memoryStream.Dispose();

            if (_serialPort != null && _serialPort.IsOpen)
            {
                _serialPort.Close();
                _serialPort.Dispose();
            }

            if (_tcpClient != null) _tcpClient.Dispose();
        }

        _disposed = true;
    }

    ~ClientHandler()
    {
        Dispose(false);
    }

    /// <summary>
    ///     连接串口
    /// </summary>
    /// <param name="portName"></param>
    /// <param name="baudRate"></param>
    /// <param name="dataBits"></param>
    /// <param name="stopBits"></param>
    /// <param name="parity"></param>
    public void ConnectSerial(string portName, int baudRate = 38400, int dataBits = 8, StopBits stopBits = StopBits.One, Parity parity = Parity.Odd)
    {
        try
        {
            // 创建串口
            _serialPort = new SerialPort
            {
                PortName = portName, // 串口名称
                BaudRate = baudRate, // 波特率
                DataBits = dataBits, // 数据位
                StopBits = stopBits, // 停止位
                Parity = parity // 校验位
            };

            // 注册事件
            _serialPort.DataReceived += SerialPort_DataReceived;
            // 打开串口
            _serialPort.Open();

            _ = _driverInfo.Socket.Send($"Connected to serial port: {portName}", _driverInfo.DeviceId + "_Logs");
            // 启动消息处理任务
            _ = Task.Run(ProcessMessages);
        }
        catch (Exception ex)
        {
            _ = _driverInfo.Socket.Send($"Failed to connect to serial port: {ex.Message}", _driverInfo.DeviceId + "_Logs");
            throw;
        }
    }

    /// <summary>
    ///     串口数据接收事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
    {
        try
        {
            var bytesToRead = _serialPort.BytesToRead;
            if (bytesToRead == 0) return;

            var buffer = new byte[bytesToRead];
            var bytesRead = _serialPort.Read(buffer, 0, buffer.Length);

            // 复制接收到的数据并加入队列
            var receivedData = new byte[bytesRead];
            Array.Copy(buffer, receivedData, bytesRead);
            _receivedDataQueue.Enqueue(receivedData);

            // 记录日志
            var hexString = SoftBasic.ByteToHexString(receivedData, ' ');
            _ = _driverInfo.Socket.Send($"时间：{DateTime.Now}\t【收】:" + hexString, _driverInfo.DeviceId + "_Logs");
        }
        catch (Exception ex)
        {
            _ = _driverInfo.Socket.Send($"串口数据接收错误: {ex.Message}", _driverInfo.DeviceId + "_Logs");
        }
    }
}