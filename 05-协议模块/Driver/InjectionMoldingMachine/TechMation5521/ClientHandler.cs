using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using Driver.Core.Models;
using PacketDotNet;
using SharpPcap;

namespace TechMation5521;

/// <summary>
/// 络抓包客户端处理类
/// 负责网络数据包的捕获和解析
/// </summary>
public class ClientHandler : IDisposable
{
  // 存储解析后的数据
  private readonly TechMation5521Data _data;
  // 驱动信息，用于日志记录等
  private readonly DriverInfoDto _driverInfo;
  // 用于控制抓包线程的取消标记
  private readonly CancellationTokenSource _tokenSource = new();
  // 网络抓包设备实例
  private ILiveDevice _captureDevice;
  // 要监听的IP地址
  private string _ipAddress;
  // 抓包工作线程
  private Thread _captureThread;

  /// <summary>
  /// 判断抓包是否正在进行
  /// </summary>
  public bool IsConnected => _captureDevice != null && _captureThread?.IsAlive == true;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="data">数据存储对象</param>
  /// <param name="driverInfo">驱动信息对象</param>
  public ClientHandler(TechMation5521Data data, DriverInfoDto driverInfo)
  {
    _data = data;
    _driverInfo = driverInfo;
  }

  /// <summary>
  /// 连接并开始抓包
  /// </summary>
  /// <param name="interfaceName">网络接口名称</param>
  /// <param name="ipAddress">要监听的IP地址</param>
  /// <param name="port">要监听的端口</param>
  public void Connect(string interfaceName, string ipAddress, int port)
  {
    _ipAddress = ipAddress;

    Console.WriteLine("5521-准备读取网卡列表");
    // 获取系统中所有可用的网络接口
    var devices = CaptureDeviceList.Instance;
    if (devices.Count == 0)
    {
      Console.WriteLine("5521-没有找到可用的网络接口");
      throw new Exception("没有找到可用的网络接口");
    }

    // 列出所有可用的网络接口供参考和调试
    var interfaceList = string.Join("\n", devices.Select((d, i) =>
      $"{i}. {d.Name} {d.Description ?? "[No Description]"}"));

    Console.WriteLine($"可用网络接口列表:\n{interfaceList}");
    _ = _driverInfo.Socket.Send($"可用网络接口列表:\n{interfaceList}", _driverInfo.DeviceId + "_Logs");

    // 根据提供的接口名称查找对应的设备（不区分大小写）
    _captureDevice = devices.FirstOrDefault(d => (d.Description != null &&
                                                  d.Description.Contains(interfaceName, StringComparison.OrdinalIgnoreCase)) ||
                                                 (d.Name != null && d.Name.Contains(interfaceName, StringComparison.OrdinalIgnoreCase)));
    if (_captureDevice == null)
    {
      Console.WriteLine($"未找到网络接口: {interfaceName}");
      throw new Exception($"未找到网络接口: {interfaceName}");
    }

    try
    {
      Console.WriteLine("5521-打开网络接口");
      // 以混杂模式打开设备，设置1秒超时
      _captureDevice.Open(new DeviceConfiguration
      {
        Mode = DeviceModes.Promiscuous,
        ReadTimeout = 1000,
      });
      Console.WriteLine("5521-已连接到网络接口");
      _ = _driverInfo.Socket.Send($"已连接到网络接口: {_captureDevice.Description}", _driverInfo.DeviceId + "_Logs");
      // 设置BPF过滤器，只捕获指定IP和端口的TCP/UDP数据包
      string filter = $"host {ipAddress} and udp port {port}";
      Console.WriteLine("5521-设置过滤器filter：" + filter);

      _captureDevice.Filter = filter;
      _ = _driverInfo.Socket.Send($"使用过滤器: {filter}", _driverInfo.DeviceId + "_Logs");

      // 注册数据包到达事件处理器
      _captureDevice.OnPacketArrival += Device_OnPacketArrival;

      // 在后台线程中启动抓包
      _captureThread = new Thread(StartCapture)
      {
        IsBackground = true
      };
      _captureThread.Start();

      _ = _driverInfo.Socket.Send($"开始监听网络接口: {_captureDevice.Description}", _driverInfo.DeviceId + "_Logs");
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"启动抓包失败: {ex.Message}", _driverInfo.DeviceId + "_Logs");
      throw;
    }
  }

  /// <summary>
  /// 抓包工作线程的主循环
  /// </summary>
  private void StartCapture()
  {
    try
    {
      _captureDevice.StartCapture();

      // 持续运行直到收到取消信号
      while (!_tokenSource.Token.IsCancellationRequested)
      {
        Thread.Sleep(100);
      }

      _captureDevice.StopCapture();
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"抓包过程发生异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
    }
  }

  /// <summary>
  /// 数据包到达事件处理方法
  /// </summary>
  private void Device_OnPacketArrival(object sender, PacketCapture e)
  {
    try
    {
      // 获取原始数据包
      var rawPacket = e.GetPacket();
      // 解析数据包
      var packet = Packet.ParsePacket(rawPacket.LinkLayerType, rawPacket.Data);

      // 提取TCP和IP层数据
      var udpPacket = packet.Extract<UdpPacket>();
      // 提取IP层数据
      var ipPacket = packet.Extract<IPPacket>();

      // 只处理包含有效负载的TCP数据包
      if (udpPacket != null && ipPacket != null && udpPacket.PayloadData.Length > 0)
      {
        // 获取TCP数据包的有效负载
        var data = udpPacket.PayloadData;

        // 判断数据包的方向
        bool isFromDevice = ipPacket.SourceAddress.ToString() == _ipAddress;
        string direction = isFromDevice ? "设备->上位机" : "上位机->设备";

        // 构建数据包信息日志
        var sb = new StringBuilder();
        sb.AppendLine($"\n捕获到数据包 [{direction}]:");
        sb.AppendLine($"时间: {rawPacket.Timeval.Date:yyyy-MM-dd HH:mm:ss.fff}");
        sb.AppendLine($"源地址: {ipPacket.SourceAddress}:{udpPacket.SourcePort}");
        sb.AppendLine($"目标地址: {ipPacket.DestinationAddress}:{udpPacket.DestinationPort}");
        sb.AppendLine($"数据长度: {data.Length} 字节");

        // 记录原始数据
        sb.AppendLine("原始数据:");
        for (int i = 0; i < data.Length; i += 16)
        {
          byte[] line = data.Skip(i).Take(16).ToArray();
          sb.Append($"{i:X4}: {BitConverter.ToString(line).Replace("-", " ").PadRight(48)}  ");
          foreach (byte b in line)
          {
            char c = (b >= 32 && b <= 126) ? (char)b : '.';
            sb.Append(c);
          }
          sb.AppendLine();
        }

        // 记录原始数据以便调试
        _ = _driverInfo.Socket.Send(sb.ToString(), _driverInfo.DeviceId + "_Logs");

        // 根据数据包方向和内容进行不同的处理
        if (isFromDevice)
        {
          ParseHMIToDevicePacket(data);
        }
        else
        {
          ParseDeviceToHMIPacket(data);
        }
      }
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"处理数据包异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
    }
  }

  /// <summary>
  /// 解析从设备发送到HMI的数据包
  /// </summary>
  private void ParseDeviceToHMIPacket(byte[] data)
  {
    try
    {
      // 检查数据包长度是否至少有基本的头部信息
      if (data.Length < 8)
      {
        return;
      }

      // 解析包头和命令
      var packetHeader = ParsePacketHeader(data);
      if (packetHeader == null) return;

      // 根据命令字进行不同的处理
      switch (packetHeader.Command)
      {
        case "32-33": // 设定数据命令
          if (data.Length == 18)
          {
            // 18字节的是参数下写报文
            ParseWriteParameterPacket(data, packetHeader);
          }
          else
          {
            // 其他长度的是设定数据1报文
            ParseSettingData1Packet(data, packetHeader);
          }
          break;
        case "33-33": // 设定数据2命令
          ParseSettingData2Packet(data, packetHeader);
          break;
        case "32-34": // 温度设定命令
          ParseTemperatureSettingPacket(data, packetHeader);
          break;
        default:
          _ = _driverInfo.Socket.Send($"未知的命令字: {packetHeader.Command}", _driverInfo.DeviceId + "_Logs");
          break;
      }
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"解析设备到HMI数据包异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
    }
  }

  /// <summary>
  /// 解析设定数据1数据包
  /// </summary>
  private void ParseSettingData1Packet(byte[] data, PacketHeader header)
  {
    try
    {
      var resultLog = new StringBuilder();
      resultLog.AppendLine("\n解析结果:");
      resultLog.AppendLine($"包头: {header.Header}");
      resultLog.AppendLine($"长度: {header.Length}");
      resultLog.AppendLine($"顺序码: {header.Unknown}");
      resultLog.AppendLine($"命令: {header.Command} (设定数据1)");

      // 储料参数解析 (起始偏移量 0xD0)
      // 每段8字节，共5段
      // 字节结构: [压力2字节][背压2字节][速度2字节][位置2字节]
      resultLog.AppendLine("\n储料参数:");
      for (int i = 0; i < 5; i++)
      {
        int baseOffset = 172 + i * 8;
        byte[] pressureBytes = GetHexStringFromByteArray(data, baseOffset, 2);     // 压力值，整数
        byte[] backPressureBytes = GetHexStringFromByteArray(data, baseOffset + 2, 2); // 背压值，整数
        byte[] speedBytes = GetHexStringFromByteArray(data, baseOffset + 4, 2);    // 速度值，整数
        byte[] posBytes = GetHexStringFromByteArray(data, baseOffset + 6, 2);      // 位置值，需乘以0.1

        int pressure = BitConverter.ToInt16(pressureBytes, 0);         // 储料压力，单位: bar
        int backPressure = BitConverter.ToInt16(backPressureBytes, 0); // 储料背压，单位: bar
        float speed = BitConverter.ToInt16(speedBytes, 0) * 0.1f;            // 储料速度，单位: rpm
        float pos = BitConverter.ToInt16(posBytes, 0) * 0.1f;         // 储料位置，单位: mm

        resultLog.AppendLine($"储料段{i + 1}:");
        resultLog.AppendLine($"  压力: {pressure} bar");
        resultLog.AppendLine($"  背压: {backPressure} bar");
        resultLog.AppendLine($"  速度: {speed:F1} rpm");
        resultLog.AppendLine($"  位置: {pos:F1} mm");

        UpdateChargingData(i + 1, speed, pos, pressure, backPressure);
      }

      // 合模参数解析 (起始偏移量 0x200)
      // 每段8字节，共5段
      // 字节结构: [压力2字节][速度2字节][位置2字节][预留2字节]
      resultLog.AppendLine("\n合模参数:");
      for (int i = 0; i < 5; i++)
      {
        int baseOffset = 268 + i * 6;
        byte[] pressureBytes = GetHexStringFromByteArray(data, baseOffset, 2);     // 压力值，整数
        byte[] speedBytes = GetHexStringFromByteArray(data, baseOffset + 2, 2);    // 速度值，需乘以0.1
        byte[] posBytes = GetHexStringFromByteArray(data, baseOffset + 4, 2);      // 位置值，需乘以0.1

        int pressure = BitConverter.ToInt16(pressureBytes, 0);    // 合模压力，单位: bar
        float speed = BitConverter.ToInt16(speedBytes, 0) * 0.1f; // 合模速度，单位: %
        float pos = BitConverter.ToInt16(posBytes, 0) * 0.1f;    // 合模位置，单位: mm

        resultLog.AppendLine($"合模段{i + 1}:");
        resultLog.AppendLine($"  压力: {pressure} bar");
        resultLog.AppendLine($"  速度: {speed:F1} %");
        resultLog.AppendLine($"  位置: {pos:F1} mm");

        UpdateMoldClampData(i + 1, pressure, speed, pos);
      }

      // 托模参数解析
      // 托模进退各2段，每段6字节
      // 字节结构: [压力2字节][速度2字节][位置2字节]
      resultLog.AppendLine("\n托模参数:");

      // 托模进参数
      int ejectorForwardOffset = 844;
      byte[] ejectorForwardPressure1Bytes = GetHexStringFromByteArray(data, ejectorForwardOffset, 2);
      byte[] ejectorForwardSpeed1Bytes = GetHexStringFromByteArray(data, ejectorForwardOffset + 2, 2);
      byte[] ejectorForwardPos1Bytes = GetHexStringFromByteArray(data, ejectorForwardOffset + 4, 2);
      byte[] ejectorForwardPressure2Bytes = GetHexStringFromByteArray(data, ejectorForwardOffset + 6, 2);
      byte[] ejectorForwardSpeed2Bytes = GetHexStringFromByteArray(data, ejectorForwardOffset + 8, 2);
      byte[] ejectorForwardPos2Bytes = GetHexStringFromByteArray(data, ejectorForwardOffset + 10, 2);

      _data.EjectorForwardPressure1 = BitConverter.ToInt16(ejectorForwardPressure1Bytes, 0); // 托模进压力1
      _data.EjectorForwardSpeed1 = BitConverter.ToInt16(ejectorForwardSpeed1Bytes, 0) * 0.1f; // 托模进速度1
      _data.EjectorForwardPos1 = BitConverter.ToInt16(ejectorForwardPos1Bytes, 0) * 0.1f; // 托模进位置1
      _data.EjectorForwardPressure2 = BitConverter.ToInt16(ejectorForwardPressure2Bytes, 0); // 托模进压力2
      _data.EjectorForwardSpeed2 = BitConverter.ToInt16(ejectorForwardSpeed2Bytes, 0) * 0.1f; // 托模进速度2
      _data.EjectorForwardPos2 = BitConverter.ToInt16(ejectorForwardPos2Bytes, 0) * 0.1f; // 托模进位置2

      resultLog.AppendLine("托模进:");
      resultLog.AppendLine($"  段1压力: {_data.EjectorForwardPressure1} bar");
      resultLog.AppendLine($"  段1速度: {_data.EjectorForwardSpeed1:F1} %");
      resultLog.AppendLine($"  段1位置: {_data.EjectorForwardPos1:F1} mm");
      resultLog.AppendLine($"  段2压力: {_data.EjectorForwardPressure2} bar");
      resultLog.AppendLine($"  段2速度: {_data.EjectorForwardSpeed2:F1} %");
      resultLog.AppendLine($"  段2位置: {_data.EjectorForwardPos2:F1} mm");

      // 托模退参数
      int ejectorBackwardOffset = 856;
      byte[] ejectorBackwardPressure1Bytes = GetHexStringFromByteArray(data, ejectorBackwardOffset, 2);
      byte[] ejectorBackwardSpeed1Bytes = GetHexStringFromByteArray(data, ejectorBackwardOffset + 2, 2);
      byte[] ejectorBackwardPos1Bytes = GetHexStringFromByteArray(data, ejectorBackwardOffset + 4, 2);
      byte[] ejectorBackwardPressure2Bytes = GetHexStringFromByteArray(data, ejectorBackwardOffset + 6, 2);
      byte[] ejectorBackwardSpeed2Bytes = GetHexStringFromByteArray(data, ejectorBackwardOffset + 8, 2);
      byte[] ejectorBackwardPos2Bytes = GetHexStringFromByteArray(data, ejectorBackwardOffset + 10, 2);

      _data.EjectorBackwardPressure1 = BitConverter.ToInt16(ejectorBackwardPressure1Bytes, 0); // 托模退压力1
      _data.EjectorBackwardSpeed1 = BitConverter.ToInt16(ejectorBackwardSpeed1Bytes, 0) * 0.1f; // 托模退速度1
      _data.EjectorBackwardPos1 = BitConverter.ToInt16(ejectorBackwardPos1Bytes, 0) * 0.1f; // 托模退位置1
      _data.EjectorBackwardPressure2 = BitConverter.ToInt16(ejectorBackwardPressure2Bytes, 0); // 托模退压力2
      _data.EjectorBackwardSpeed2 = BitConverter.ToInt16(ejectorBackwardSpeed2Bytes, 0) * 0.1f; // 托模退速度2
      _data.EjectorBackwardPos2 = BitConverter.ToInt16(ejectorBackwardPos2Bytes, 0) * 0.1f; // 托模退位置2

      resultLog.AppendLine("托模退:");
      resultLog.AppendLine($"  段1压力: {_data.EjectorBackwardPressure1} bar");
      resultLog.AppendLine($"  段1速度: {_data.EjectorBackwardSpeed1:F1} %");
      resultLog.AppendLine($"  段1位置: {_data.EjectorBackwardPos1:F1} mm");
      resultLog.AppendLine($"  段2压力: {_data.EjectorBackwardPressure2} bar");
      resultLog.AppendLine($"  段2速度: {_data.EjectorBackwardSpeed2:F1} %");
      resultLog.AppendLine($"  段2位置: {_data.EjectorBackwardPos2:F1} mm");

      // 托模方式和次数
      byte[] modeBytes = GetHexStringFromByteArray(data, 868, 2);
      byte[] countBytes = GetHexStringFromByteArray(data, 870, 2);
      _data.EjectorMode = BitConverter.ToInt16(modeBytes, 0);
      _data.EjectorCount = BitConverter.ToInt16(countBytes, 0);
      resultLog.AppendLine($"托模方式: {_data.EjectorMode}");
      resultLog.AppendLine($"托模次数: {_data.EjectorCount}");


      // 保压参数解析 (起始偏移量 0x7F8)
      // 每段12字节，共6段
      // 字节结构: [压力2字节][速度2字节][时间4字节][预留4字节]
      resultLog.AppendLine("\n保压参数:");
      for (int i = 0; i < 6; i++)
      {
        int baseOffset = 1196 + i * 8;
        byte[] pressureBytes = GetHexStringFromByteArray(data, baseOffset, 2);     // 压力值，整数
        byte[] speedBytes = GetHexStringFromByteArray(data, baseOffset + 2, 2);    // 速度值，需乘以0.1
        byte[] timeBytes = GetHexStringFromByteArray(data, baseOffset + 4, 4);     // 时间值，需乘以0.01

        int pressure = BitConverter.ToInt16(pressureBytes, 0);    // 保压压力，单位: bar
        float speed = BitConverter.ToInt16(speedBytes, 0) * 0.1f; // 保压速度，单位: %
        float time = BitConverter.ToInt32(timeBytes, 0) * 0.01f; // 保压时间，单位: s

        resultLog.AppendLine($"保压段{i + 1}:");
        resultLog.AppendLine($"  压力: {pressure} bar");
        resultLog.AppendLine($"  速度: {speed:F1} %");
        resultLog.AppendLine($"  时间: {time:F2} s");

        UpdatePressureHoldData(i + 1, pressure, speed, time);
      }

      _ = _driverInfo.Socket.Send(resultLog.ToString(), _driverInfo.DeviceId + "_Logs");
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"解析设定数据1数据包异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
    }
  }

  /// <summary>
  /// 解析设定数据2数据包
  /// </summary>
  private void ParseSettingData2Packet(byte[] data, PacketHeader header)
  {
    try
    {
      var resultLog = new StringBuilder();
      resultLog.AppendLine("\n解析结果:");
      resultLog.AppendLine($"包头: {header.Header}");
      resultLog.AppendLine($"长度: {header.Length}");
      resultLog.AppendLine($"顺序码: {header.Unknown}");
      resultLog.AppendLine($"命令: {header.Command} (设定数据2)");

      // 射出参数
      resultLog.AppendLine("\n射出参数:");
      for (int i = 0; i < 6; i++)
      {
        int baseOffset = 44 + i * 12;
        byte[] pressureBytes = GetHexStringFromByteArray(data, baseOffset, 2);
        byte[] speedBytes = GetHexStringFromByteArray(data, baseOffset + 2, 2);
        byte[] posBytes = GetHexStringFromByteArray(data, baseOffset + 8, 2);

        int pressure = BitConverter.ToInt16(pressureBytes, 0);
        float speed = BitConverter.ToInt16(speedBytes, 0) * 0.1f; // 注意这里是1.0而不是0.1
        float pos = BitConverter.ToInt16(posBytes, 0) * 0.1f;

        resultLog.AppendLine($"射出段{i + 1}:");
        resultLog.AppendLine($"  压力: {pressure}");
        resultLog.AppendLine($"  速度: {speed:F1}");
        resultLog.AppendLine($"  位置: {pos:F1}");

        // 更新数据对象
        UpdateInjectionData(i + 1, pressure, speed, pos);
      }

      // 座台参数
      resultLog.AppendLine("\n座台参数:");
      // 座台进
      int seatBaseOffset = 300;
      byte[] seatForwardPressure1Bytes = GetHexStringFromByteArray(data, seatBaseOffset, 2);
      byte[] seatForwardSpeed1Bytes = GetHexStringFromByteArray(data, seatBaseOffset + 2, 2);
      byte[] seatForwardPressure2Bytes = GetHexStringFromByteArray(data, seatBaseOffset + 6, 2);
      byte[] seatForwardSpeed2Bytes = GetHexStringFromByteArray(data, seatBaseOffset + 8, 2);
      byte[] seatForwardTimeBytes = GetHexStringFromByteArray(data, seatBaseOffset + 10, 2);

      int seatForwardPressure1 = BitConverter.ToInt16(seatForwardPressure1Bytes, 0);
      float seatForwardSpeed1 = BitConverter.ToInt16(seatForwardSpeed1Bytes, 0) * 0.1f;
      int seatForwardPressure2 = BitConverter.ToInt16(seatForwardPressure2Bytes, 0);
      float seatForwardSpeed2 = BitConverter.ToInt16(seatForwardSpeed2Bytes, 0) * 0.1f;
      float seatForwardTime = BitConverter.ToInt16(seatForwardTimeBytes, 0) * 0.01f;

      resultLog.AppendLine("座台进:");
      resultLog.AppendLine($"  压力1: {seatForwardPressure1}");
      resultLog.AppendLine($"  速度1: {seatForwardSpeed1:F1}");
      resultLog.AppendLine($"  压力2: {seatForwardPressure2}");
      resultLog.AppendLine($"  速度2: {seatForwardSpeed2:F1}");
      resultLog.AppendLine($"  时间: {seatForwardTime:F2}s");

      // 座台退
      int seatBackOffset = 316;
      byte[] seatBackPressure1Bytes = GetHexStringFromByteArray(data, seatBackOffset, 2);
      byte[] seatBackSpeed1Bytes = GetHexStringFromByteArray(data, seatBackOffset + 2, 2);
      byte[] seatBackTimeBytes = GetHexStringFromByteArray(data, seatBackOffset + 4, 2);
      byte[] seatBackDelayTimeBytes = GetHexStringFromByteArray(data, seatBackOffset + 6, 2);

      int seatBackPressure1 = BitConverter.ToInt16(seatBackPressure1Bytes, 0);
      float seatBackSpeed1 = BitConverter.ToInt16(seatBackSpeed1Bytes, 0) * 0.1f;
      float seatBackTime = BitConverter.ToInt16(seatBackTimeBytes, 0) * 0.01f;
      float seatBackDelayTime = BitConverter.ToInt16(seatBackDelayTimeBytes, 0) * 0.01f;

      resultLog.AppendLine("座台退:");
      resultLog.AppendLine($"  压力1: {seatBackPressure1}");
      resultLog.AppendLine($"  速度1: {seatBackSpeed1:F1}");
      resultLog.AppendLine($"  时间: {seatBackTime:F2}s");
      resultLog.AppendLine($"  延迟时间: {seatBackDelayTime:F2}s");

      // 更新座台数据
      UpdateSeatData(
        seatForwardPressure1, seatForwardSpeed1,
        seatForwardPressure2, seatForwardSpeed2,
        seatForwardTime,
        seatBackPressure1, seatBackSpeed1,
        seatBackTime, seatBackDelayTime
      );

      // 开模参数
      resultLog.AppendLine("\n开模参数:");
      for (int i = 0; i < 5; i++)
      {
        int baseOffset = 364 + i * 6;
        byte[] pressureBytes = GetHexStringFromByteArray(data, baseOffset, 2);
        byte[] speedBytes = GetHexStringFromByteArray(data, baseOffset + 2, 2);
        byte[] posBytes = GetHexStringFromByteArray(data, baseOffset + 4, 2);

        int pressure = BitConverter.ToInt16(pressureBytes, 0);
        float speed = BitConverter.ToInt16(speedBytes, 0) * 0.1f;
        float pos = BitConverter.ToInt16(posBytes, 0) * 0.1f;

        resultLog.AppendLine($"开模段{i + 1}:");
        resultLog.AppendLine($"  压力: {pressure}");
        resultLog.AppendLine($"  速度: {speed:F1}");
        resultLog.AppendLine($"  位置: {pos:F1}");

        // 更新数据对象
        UpdateMoldOpenData(i + 1, pressure, speed, pos);
      }

      _ = _driverInfo.Socket.Send(resultLog.ToString(), _driverInfo.DeviceId + "_Logs");
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"解析设定数据2数据包异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
    }
  }

  private void UpdateChargingData(int segment, float speed, float position, int pressure, int backPressure)
  {
    switch (segment)
    {
      case 1:
        _data.ChargingSpeed1 = speed;
        _data.ChargingPosition1 = position;
        _data.ChargingPressure1 = pressure;
        _data.ChargingBackPressure1 = backPressure;
        break;
      // 储料段2
      case 2:
        _data.ChargingSpeed2 = speed;
        _data.ChargingPosition2 = position;
        _data.ChargingPressure2 = pressure;
        _data.ChargingBackPressure2 = backPressure;
        break;
      // 储料段3
      case 3:
        _data.ChargingSpeed3 = speed;
        _data.ChargingPosition3 = position;
        _data.ChargingPressure3 = pressure;
        _data.ChargingBackPressure3 = backPressure;
        break;
      // 储料段4
      case 4:
        _data.ChargingSpeed4 = speed;
        _data.ChargingPosition4 = position;
        _data.ChargingPressure4 = pressure;
        _data.ChargingBackPressure4 = backPressure;
        break;
      // 储料段5
      case 5:
        _data.ChargingSpeed5 = speed;
        _data.ChargingPosition5 = position;
        _data.ChargingPressure5 = pressure;
        _data.ChargingBackPressure5 = backPressure;
        break;
    }
  }

  private void UpdateMoldClampData(int segment, int pressure, float speed, float position)
  {
    switch (segment)
    {
      case 1:
        _data.MoldClampPressure1 = pressure;
        _data.MoldClampSpeed1 = speed;
        _data.MoldClampPosition1 = position;
        break;
      // 合模段2
      case 2:
        _data.MoldClampPressure2 = pressure;
        _data.MoldClampSpeed2 = speed;
        _data.MoldClampPosition2 = position;
        break;
      // 合模段3
      case 3:
        _data.MoldClampPressure3 = pressure;
        _data.MoldClampSpeed3 = speed;
        _data.MoldClampPosition3 = position;
        break;
      // 合模段4
      case 4:
        _data.MoldClampPressure4 = pressure;
        _data.MoldClampSpeed4 = speed;
        _data.MoldClampPosition4 = position;
        break;
      // 合模段5
      case 5:
        _data.MoldClampPressure5 = pressure;
        _data.MoldClampSpeed5 = speed;
        _data.MoldClampPosition5 = position;
        break;
    }
  }

  /// <summary>
  /// 更新保压数据
  /// </summary>
  /// <param name="segment">段位(1-5)</param>
  /// <param name="pressure">压力(bar)</param>
  /// <param name="speed">速度(%)</param>
  /// <param name="time">时间(s)</param>
  private void UpdatePressureHoldData(int segment, int pressure, float speed, float time)
  {
    switch (segment)
    {
      case 1:
        _data.PressureHoldPressure1 = pressure;
        _data.PressureHoldSpeed1 = speed;
        _data.PressureHoldTime1 = time;
        break;
      // 保压段2
      case 2:
        _data.PressureHoldPressure2 = pressure;
        _data.PressureHoldSpeed2 = speed;
        _data.PressureHoldTime2 = time;
        break;
      // 保压段3
      case 3:
        _data.PressureHoldPressure3 = pressure;
        _data.PressureHoldSpeed3 = speed;
        _data.PressureHoldTime3 = time;
        break;
      // 保压段4
      case 4:
        _data.PressureHoldPressure4 = pressure;
        _data.PressureHoldSpeed4 = speed;
        _data.PressureHoldTime4 = time;
        break;
      // 保压段5
      case 5:
        _data.PressureHoldPressure5 = pressure;
        _data.PressureHoldSpeed5 = speed;
        _data.PressureHoldTime5 = time;
        break;
      // 保压段6
      case 6:
        _data.PressureHoldPressure6 = pressure;
        _data.PressureHoldSpeed6 = speed;
        _data.PressureHoldTime6 = time;
        break;

    }
  }

  private void UpdateInjectionData(int segment, int pressure, float speed, float position)
  {
    switch (segment)
    {
      case 1:
        _data.InjectionPressure1 = pressure;
        _data.InjectionSpeed1 = speed;
        _data.InjectionPosition1 = position;
        break;
      // 射出段2
      case 2:
        _data.InjectionPressure2 = pressure;
        _data.InjectionSpeed2 = speed;
        _data.InjectionPosition2 = position;
        break;
      // 射出段3
      case 3:
        _data.InjectionPressure3 = pressure;
        _data.InjectionSpeed3 = speed;
        _data.InjectionPosition3 = position;
        break;
      // 射出段4
      case 4:
        _data.InjectionPressure4 = pressure;
        _data.InjectionSpeed4 = speed;
        _data.InjectionPosition4 = position;
        break;
      // 射出段5
      case 5:
        _data.InjectionPressure5 = pressure;
        _data.InjectionSpeed5 = speed;
        _data.InjectionPosition5 = position;
        break;
      // 射出段6
      case 6:
        _data.InjectionPressure6 = pressure;
        _data.InjectionSpeed6 = speed;
        _data.InjectionPosition6 = position;
        break;
    }
  }

  /// <summary>
  /// 更新开模数据
  /// </summary>
  /// <param name="segment">段位(1-5)</param>
  /// <param name="pressure">压力(bar)</param>
  /// <param name="speed">速度(%)</param>
  /// <param name="position">位置(mm)</param>
  private void UpdateMoldOpenData(int segment, int pressure, float speed, float position)
  {
    switch (segment)
    {
      case 1:
        _data.MoldOpenPressure1 = pressure;
        _data.MoldOpenSpeed1 = speed;
        _data.MoldOpenPosition1 = position;
        break;
      // 开模段2
      case 2:
        _data.MoldOpenPressure2 = pressure;
        _data.MoldOpenSpeed2 = speed;
        _data.MoldOpenPosition2 = position;
        break;

      // 开模段3
      case 3:
        _data.MoldOpenPressure3 = pressure;
        _data.MoldOpenSpeed3 = speed;
        _data.MoldOpenPosition3 = position;
        break;
      // 开模段4
      case 4:
        _data.MoldOpenPressure4 = pressure;
        _data.MoldOpenSpeed4 = speed;
        _data.MoldOpenPosition4 = position;
        break;
      // 开模段5
      case 5:
        _data.MoldOpenPressure5 = pressure;
        _data.MoldOpenSpeed5 = speed;
        _data.MoldOpenPosition5 = position;
        break;
    }
  }

  /// <summary>
  /// 更新座台数据
  /// </summary>
  /// <param name="forwardPressure1">座台进压力1</param>
  /// <param name="forwardSpeed1">座台进速度1</param>
  /// <param name="forwardPressure2">座台进压力2</param>
  /// <param name="forwardSpeed2">座台进速度2</param>
  /// <param name="forwardTime">座台进时间</param>
  /// <param name="backPressure1">座台退压力1</param>
  /// <param name="backSpeed1">座台退速度1</param>
  /// <param name="backTime">座台退时间</param>
  /// <param name="backDelayTime">座台退延迟时间</param>
  private void UpdateSeatData(
    int forwardPressure1, float forwardSpeed1,
    int forwardPressure2, float forwardSpeed2,
    float forwardTime,
    int backPressure1, float backSpeed1,
    float backTime, float backDelayTime)
  {
    _data.SeatForwardPressure1 = forwardPressure1; // 座台进压力1
    _data.SeatForwardSpeed1 = forwardSpeed1;       // 座台进速度1
    _data.SeatForwardPressure2 = forwardPressure2; // 座台进压力2
    _data.SeatForwardSpeed2 = forwardSpeed2;       // 座台进速度2
    _data.SeatForwardTime = forwardTime;            // 座台进时间
    _data.SeatBackPressure1 = backPressure1;       // 座台退压力1
    _data.SeatBackSpeed1 = backSpeed1;             // 座台退速度1
    _data.SeatBackTime = backTime;                  // 座台退时间
    _data.SeatBackDelayTime = backDelayTime;        // 座台退延迟时间

  }

  /// <summary>
  /// 解析从HMI发送到设备的数据包
  /// </summary>
  private void ParseHMIToDevicePacket(byte[] data)
  {
    try
    {
      // 检查数据包长度是否至少有基本的头部信息
      if (data.Length < 8)
      {
        return;
      }

      // 解析包头和命令
      var packetHeader = ParsePacketHeader(data);
      if (packetHeader == null) return;

      // 根据命令字进行不同的处理
      switch (packetHeader.Command)
      {
        case "41-43": // 合模数量命令
          ParseMoldCountPacket(data, packetHeader);
          break;
        case "42-43": // 实时温度命令
          ParseRealTimeDataPacket(data, packetHeader);
          break;
        case "42-45": // SPC数据命令
          ParseSPCDataPacket(data, packetHeader);
          break;
        default:
          _ = _driverInfo.Socket.Send($"未知的命令字: {packetHeader.Command}", _driverInfo.DeviceId + "_Logs");
          break;
      }
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"解析HMI到设备数据包异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
    }
  }

  /// <summary>
  /// 解析数据包头部信息
  /// </summary>
  private PacketHeader ParsePacketHeader(byte[] data)
  {
    try
    {
      byte[] header = GetHexStringFromByteArray(data, 0, 2);
      byte[] length = GetHexStringFromByteArray(data, 2, 2);
      byte[] unknown = GetHexStringFromByteArray(data, 4, 2);
      byte[] command = GetHexStringFromByteArray(data, 6, 2);

      return new PacketHeader
      {
        Header = BitConverter.ToString(header),
        Length = BitConverter.ToString(length),
        Unknown = BitConverter.ToString(unknown),
        Command = BitConverter.ToString(command)
      };
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"解析数据包头部异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
      return null;
    }
  }

  /// <summary>
  /// 解析合模数量数据包
  /// 数据包结构:
  /// 1. 包头: 2字节
  /// 2. 长度: 2字节
  /// 3. 顺序码: 2字节
  /// 4. 命令字: 2字节 (0x41 0x43)
  /// 5. 合模数量: 从数据包末尾往前第10个字节开始的4字节
  /// </summary>
  /// <param name="data">原始数据包字节数组</param>
  /// <param name="header">数据包头信息</param>
  private void ParseMoldCountPacket(byte[] data, PacketHeader header)
  {
    try
    {
      // 从数据包末尾往前第10个字节开始取4字节作为合模数量
      int moldCountOffset = data.Length - 12;
      if (moldCountOffset < 0)
      {
        _ = _driverInfo.Socket.Send("合模数量数据包长度异常", _driverInfo.DeviceId + "_Logs");
        return;
      }

      byte[] moldCountBytes = GetHexStringFromByteArray(data, moldCountOffset, 4);
      int moldCount = BitConverter.ToInt32(moldCountBytes, 0);

      // 记录解析结果
      var resultLog = new StringBuilder();
      resultLog.AppendLine("\n解析结果:");
      resultLog.AppendLine($"包头: {header.Header}");
      resultLog.AppendLine($"长度: {header.Length}");
      resultLog.AppendLine($"顺序码: {header.Unknown}");
      resultLog.AppendLine($"命令: {header.Command} (合模数量)");
      resultLog.AppendLine($"数据包总长度: {data.Length}字节");
      resultLog.AppendLine($"合模数量偏移: {moldCountOffset}");
      resultLog.AppendLine($"合模数量: {moldCount}");

      _ = _driverInfo.Socket.Send(resultLog.ToString(), _driverInfo.DeviceId + "_Logs");
      // 排查bug中 -- 正常不会超过10万
      if (moldCount > 100000)
      {
        _ = _driverInfo.Socket.Send($"合模数量异常: {moldCount}", _driverInfo.DeviceId + "_Logs");
        // 记录到一个文本文件中
        var filePath = Path.Combine(AppContext.BaseDirectory, "Logs", "MoldCount.txt");
        File.AppendAllText(filePath, $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - 合模数量异常: {moldCount}\n");
        // 打印报文信息到文本文件中
        File.AppendAllText(filePath, $"{resultLog.ToString()}\n");
      }
      else
      {
        // 更新数据对象中的合模数量
        _data.MoldCount = moldCount;
      }

    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"解析合模数量数据包异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
    }
  }

  /// <summary>
  /// 解析实时数据数据包
  /// </summary>
  private void ParseRealTimeDataPacket(byte[] data, PacketHeader header)
  {
    try
    {

      // 解析各个数据字段
      var resultLog = new StringBuilder();
      resultLog.AppendLine("\n解析结果:");
      resultLog.AppendLine($"包头: {header.Header}");
      resultLog.AppendLine($"长度: {header.Length}");
      resultLog.AppendLine($"顺序码: {header.Unknown}");
      resultLog.AppendLine($"命令: {header.Command} (实时温度)");

      // 解析顶针位置 (0x28)
      byte[] ejectorPosBytes = GetHexStringFromByteArray(data, 44, 2);
      float ejectorPos = BitConverter.ToInt16(ejectorPosBytes, 0) * 0.1f;
      resultLog.AppendLine($"顶针位置: {ejectorPos:F1}");

      // 解析螺杆位置 (0x30)
      byte[] screwPosBytes = GetHexStringFromByteArray(data, 48, 2);
      float screwPos = BitConverter.ToInt16(screwPosBytes, 0) * 0.1f;
      resultLog.AppendLine($"螺杆位置: {screwPos:F1}");

      // 解析温度数据 (0x1E0)
      byte[] temp1Bytes = GetHexStringFromByteArray(data, 252, 2);
      byte[] temp2Bytes = GetHexStringFromByteArray(data, 254, 2);
      byte[] temp3Bytes = GetHexStringFromByteArray(data, 256, 2);
      byte[] temp4Bytes = GetHexStringFromByteArray(data, 258, 2);
      byte[] temp5Bytes = GetHexStringFromByteArray(data, 260, 2);
      byte[] oilTempBytes = GetHexStringFromByteArray(data, 270, 2);

      float temp1 = BitConverter.ToInt16(temp1Bytes, 0) * 0.1f;
      float temp2 = BitConverter.ToInt16(temp2Bytes, 0) * 0.1f;
      float temp3 = BitConverter.ToInt16(temp3Bytes, 0) * 0.1f;
      float temp4 = BitConverter.ToInt16(temp4Bytes, 0) * 0.1f;
      float temp5 = BitConverter.ToInt16(temp5Bytes, 0) * 0.1f;
      float oilTemp = BitConverter.ToInt16(oilTempBytes, 0) * 0.1f;

      resultLog.AppendLine($"温度1段: {temp1:F1}℃");
      resultLog.AppendLine($"温度2段: {temp2:F1}℃");
      resultLog.AppendLine($"温度3段: {temp3:F1}℃");
      resultLog.AppendLine($"温度4段: {temp4:F1}℃");
      resultLog.AppendLine($"温度5段: {temp5:F1}℃");
      resultLog.AppendLine($"油温: {oilTemp:F1}℃");

      _ = _driverInfo.Socket.Send(resultLog.ToString(), _driverInfo.DeviceId + "_Logs");

      // 更新数据对象
      _data.EjectorPosition = ejectorPos;
      _data.ScrewPosition = screwPos;
      _data.Temperature1 = temp1;
      _data.Temperature2 = temp2;
      _data.Temperature3 = temp3;
      _data.Temperature4 = temp4;
      _data.Temperature5 = temp5;
      _data.OilTemperature = oilTemp;
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"解析实时数据数据包异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
    }
  }

  /// <summary>
  /// 解析SPC数据数据包
  /// </summary>
  private void ParseSPCDataPacket(byte[] data, PacketHeader header)
  {
    try
    {

      var resultLog = new StringBuilder();
      resultLog.AppendLine("\n解析结果:");
      resultLog.AppendLine($"包头: {header.Header}");
      resultLog.AppendLine($"长度: {header.Length}");
      resultLog.AppendLine($"顺序码: {header.Unknown}");
      resultLog.AppendLine($"命令: {header.Command} (SPC数据)");

      // 解析时间相关数据
      byte[] cycleTimeBytes = GetHexStringFromByteArray(data, 12, 4);  // 修改偏移量为0x0C
      float cycleTime = BitConverter.ToInt32(cycleTimeBytes, 0) * 0.01f; // 4558 * 0.01 = 45.58
      resultLog.AppendLine($"上模循环时间: {cycleTime:F2}s");

      byte[] injectionTimeBytes = GetHexStringFromByteArray(data, 16, 4);
      float injectionTime = BitConverter.ToInt32(injectionTimeBytes, 0) * 0.01f;
      resultLog.AppendLine($"上模注射保压时间: {injectionTime:F2}s");

      byte[] injectionOnlyTimeBytes = GetHexStringFromByteArray(data, 20, 4);
      float injectionOnlyTime = BitConverter.ToInt32(injectionOnlyTimeBytes, 0) * 0.01f;
      resultLog.AppendLine($"上模注射时间: {injectionOnlyTime:F2}s");

      byte[] chargingTimeBytes = GetHexStringFromByteArray(data, 24, 4);
      float chargingTime = BitConverter.ToInt32(chargingTimeBytes, 0) * 0.01f;
      resultLog.AppendLine($"上模储料时间: {chargingTime:F2}s");

      // 解析位置相关数据
      byte[] injectionStartPosBytes = GetHexStringFromByteArray(data, 40, 4);
      float injectionStartPos = BitConverter.ToInt32(injectionStartPosBytes, 0) * 0.1f;
      resultLog.AppendLine($"上模射出起点: {injectionStartPos:F1}mm");

      byte[] pressureHoldStartPosBytes = GetHexStringFromByteArray(data, 44, 4);
      float pressureHoldStartPos = BitConverter.ToInt32(pressureHoldStartPosBytes, 0) * 0.1f;
      resultLog.AppendLine($"上模保压起点: {pressureHoldStartPos:F1}mm");

      byte[] injectionMonitorPosBytes = GetHexStringFromByteArray(data, 48, 4);
      float injectionMonitorPos = BitConverter.ToInt32(injectionMonitorPosBytes, 0) * 0.1f;
      resultLog.AppendLine($"上模射出监控: {injectionMonitorPos:F1}mm");

      byte[] injectionEndPosBytes = GetHexStringFromByteArray(data, 52, 4);
      float injectionEndPos = BitConverter.ToInt32(injectionEndPosBytes, 0) * 0.1f;
      resultLog.AppendLine($"上模射出终点: {injectionEndPos:F1}mm");

      // 解析压力相关数据
      byte[] pressureHoldSwitchBytes = GetHexStringFromByteArray(data, 60, 2);
      int pressureHoldSwitch = BitConverter.ToInt16(pressureHoldSwitchBytes, 0);
      resultLog.AppendLine($"上模转保压力: {pressureHoldSwitch}");

      byte[] injectionPeakPressureBytes = GetHexStringFromByteArray(data, 70, 1);
      int injectionPeakPressure = injectionPeakPressureBytes[0];
      resultLog.AppendLine($"上模射出尖压: {injectionPeakPressure}");

      byte[] chargingPeakPressureBytes = GetHexStringFromByteArray(data, 72, 1);
      int chargingPeakPressure = chargingPeakPressureBytes[0];
      resultLog.AppendLine($"上模储料尖压: {chargingPeakPressure}");

      _ = _driverInfo.Socket.Send(resultLog.ToString(), _driverInfo.DeviceId + "_Logs");

      // 更新数据对象
      _data.CycleTime = cycleTime;
      _data.InjectionTime = injectionTime;
      _data.InjectionOnlyTime = injectionOnlyTime;
      _data.ChargingTime = chargingTime;
      _data.InjectionStartPosition = injectionStartPos;
      _data.PressureHoldStartPosition = pressureHoldStartPos;
      _data.InjectionMonitorPosition = injectionMonitorPos;
      _data.InjectionEndPosition = injectionEndPos;
      _data.PressureHoldSwitchPressure = pressureHoldSwitch;
      _data.InjectionPeakPressure = injectionPeakPressure;
      _data.ChargingPeakPressure = chargingPeakPressure;
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"解析SPC数据数据包异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
    }
  }

  /// <summary>
  /// 解析温度设定数据包
  /// </summary>
  private void ParseTemperatureSettingPacket(byte[] data, PacketHeader header)
  {
    try
    {
      var resultLog = new StringBuilder();
      resultLog.AppendLine("\n解析结果:");
      resultLog.AppendLine($"包头: {header.Header}");
      resultLog.AppendLine($"长度: {header.Length}");
      resultLog.AppendLine($"顺序码: {header.Unknown}");
      resultLog.AppendLine($"命令: {header.Command} (温度设定)");

      // 解析温度设定值 (396开始)
      byte[] temp1Bytes = GetHexStringFromByteArray(data, 396, 2);
      byte[] temp2Bytes = GetHexStringFromByteArray(data, 398, 2);
      byte[] temp3Bytes = GetHexStringFromByteArray(data, 400, 2);
      byte[] temp4Bytes = GetHexStringFromByteArray(data, 402, 2);
      byte[] temp5Bytes = GetHexStringFromByteArray(data, 404, 2);

      float temp1 = BitConverter.ToInt16(temp1Bytes, 0) * 0.1f;
      float temp2 = BitConverter.ToInt16(temp2Bytes, 0) * 0.1f;
      float temp3 = BitConverter.ToInt16(temp3Bytes, 0) * 0.1f;
      float temp4 = BitConverter.ToInt16(temp4Bytes, 0) * 0.1f;
      float temp5 = BitConverter.ToInt16(temp5Bytes, 0) * 0.1f;

      resultLog.AppendLine($"温度1设定值: {temp1:F1}℃");
      resultLog.AppendLine($"温度2设定值: {temp2:F1}℃");
      resultLog.AppendLine($"温度3设定值: {temp3:F1}℃");
      resultLog.AppendLine($"温度4设定值: {temp4:F1}℃");
      resultLog.AppendLine($"温度5设定值: {temp5:F1}℃");

      _ = _driverInfo.Socket.Send(resultLog.ToString(), _driverInfo.DeviceId + "_Logs");

      // 更新数据对象
      _data.TemperatureSetting1 = temp1;
      _data.TemperatureSetting2 = temp2;
      _data.TemperatureSetting3 = temp3;
      _data.TemperatureSetting4 = temp4;
      _data.TemperatureSetting5 = temp5;
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"解析温度设定数据包异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
    }
  }

  /// <summary>
  /// 数据包头部信息类
  /// </summary>
  private class PacketHeader
  {
    public string Header { get; set; }    // 包头
    public string Length { get; set; }    // 长度
    public string Unknown { get; set; }   // 未知字段
    public string Command { get; set; }   // 命令字
  }

  private static byte[] GetHexStringFromByteArray(byte[] byteArray, int startIndex, int length)
  {
    var subData = new byte[length];
    Array.Copy(byteArray, startIndex, subData, 0, length);
    return subData;
  }

  /// <summary>
  /// 处理32-33命令的下写报文
  /// 报文固定长度18字节:
  /// - 包头: 2字节 (0x02,0x00)
  /// - 长度: 2字节 (0x09,0x00)
  /// - 序号: 2字节
  /// - 命令: 2字节 (0x32,0x33)
  /// - 标识: 2字节 (参数标识)
  /// - 固定: 2字节 (0x01,0x00)
  /// - 数据: 2字节 (参数值)
  /// - 校验: 2字节
  /// - 结束: 2字节 (0x03,0x00)
  /// </summary>
  private void ParseWriteParameterPacket(byte[] data, PacketHeader header)
  {
    try
    {
      if (data.Length != 18)
      {
        _ = _driverInfo.Socket.Send($"参数下写报文长度错误: {data.Length}", _driverInfo.DeviceId + "_Logs");
        return;
      }

      // 获取参数标识和数据
      byte[] identifierBytes = GetHexStringFromByteArray(data, 8, 2);
      byte[] valueBytes = GetHexStringFromByteArray(data, 12, 2);

      string identifier = BitConverter.ToString(identifierBytes);
      int value = BitConverter.ToInt16(valueBytes, 0);

      var resultLog = new StringBuilder();
      resultLog.AppendLine("\n解析结果:");
      resultLog.AppendLine($"包头: {header.Header}");
      resultLog.AppendLine($"长度: {header.Length}");
      resultLog.AppendLine($"序号: {header.Unknown}");
      resultLog.AppendLine($"命令: {header.Command} (参数下写)");
      resultLog.AppendLine($"标识: {identifier}");
      resultLog.AppendLine($"数据: {value}");

      // 根据标识符处理不同参数
      switch (identifier)
      {
        // 合模位置
        case "82-00": // 合模位置1
          _data.MoldClampPosition1 = value * 0.1f;
          resultLog.AppendLine($"合模位置1: {_data.MoldClampPosition1:F1}mm");
          break;
        case "85-00": // 合模位置2
          _data.MoldClampPosition2 = value * 0.1f;
          resultLog.AppendLine($"合模位置2: {_data.MoldClampPosition2:F1}mm");
          break;
        case "88-00": // 合模位置3
          _data.MoldClampPosition3 = value * 0.1f;
          resultLog.AppendLine($"合模位置3: {_data.MoldClampPosition3:F1}mm");
          break;
        case "8B-00": // 合模位置4
          _data.MoldClampPosition4 = value * 0.1f;
          resultLog.AppendLine($"合模位置4: {_data.MoldClampPosition4:F1}mm");
          break;

        // 合模压力
        case "80-00": // 合模压力1
          _data.MoldClampPressure1 = value;
          resultLog.AppendLine($"合模压力1: {_data.MoldClampPressure1}bar");
          break;
        case "83-00": // 合模压力2
          _data.MoldClampPressure2 = value;
          resultLog.AppendLine($"合模压力2: {_data.MoldClampPressure2}bar");
          break;
        case "86-00": // 合模压力3
          _data.MoldClampPressure3 = value;
          resultLog.AppendLine($"合模压力3: {_data.MoldClampPressure3}bar");
          break;
        case "89-00": // 合模压力4
          _data.MoldClampPressure4 = value;
          resultLog.AppendLine($"合模压力4: {_data.MoldClampPressure4}bar");
          break;
        case "8C-00": // 合模压力5
          _data.MoldClampPressure5 = value;
          resultLog.AppendLine($"合模压力5: {_data.MoldClampPressure5}bar");
          break;

        // 合模速度1
        case "81-00": // 合模速度1
          _data.MoldClampSpeed1 = value * 0.1f;
          resultLog.AppendLine($"合模速度1: {_data.MoldClampSpeed1:F1}%");
          break;

        // 合模速度2
        case "84-00": // 合模速度2
          _data.MoldClampSpeed2 = value * 0.1f;
          resultLog.AppendLine($"合模速度2: {_data.MoldClampSpeed2:F1}%");
          break;

        // 合模速度3
        case "87-00": // 合模速度3
          _data.MoldClampSpeed3 = value * 0.1f;
          resultLog.AppendLine($"合模速度3: {_data.MoldClampSpeed3:F1}%");
          break;

        // 合模速度4
        case "8A-00": // 合模速度4
          _data.MoldClampSpeed4 = value * 0.1f;
          resultLog.AppendLine($"合模速度4: {_data.MoldClampSpeed4:F1}%");
          break;

        // 合模速度5
        case "8D-00": // 合模速度5
          _data.MoldClampSpeed5 = value * 0.1f;
          resultLog.AppendLine($"合模速度5: {_data.MoldClampSpeed5:F1}%");
          break;


        // 储料相关参数
        case "63-00": // 储料位置5
          _data.ChargingPosition5 = value * 0.1f;
          resultLog.AppendLine($"储料位置5: {_data.ChargingPosition5:F1}mm");
          break;

        // 储料位置4
        case "5F-00": // 储料位置4
          _data.ChargingPosition4 = value * 0.1f;
          resultLog.AppendLine($"储料位置4: {_data.ChargingPosition4:F1}mm");
          break;

        // 储料位置3
        case "5B-00": // 储料位置3
          _data.ChargingPosition3 = value * 0.1f;
          resultLog.AppendLine($"储料位置3: {_data.ChargingPosition3:F1}mm");
          break;

        // 储料位置2
        case "57-00": // 储料位置2
          _data.ChargingPosition2 = value * 0.1f;
          resultLog.AppendLine($"储料位置2: {_data.ChargingPosition2:F1}mm");
          break;

        // 储料位置1
        case "53-00": // 储料位置1
          _data.ChargingPosition1 = value * 0.1f;
          resultLog.AppendLine($"储料位置1: {_data.ChargingPosition1:F1}mm");
          break;


        case "60-00": // 储料压力5
          _data.ChargingPressure5 = value;
          resultLog.AppendLine($"储料压力5: {_data.ChargingPressure5}bar");
          break;
        case "5C-00": // 储料压力4
          _data.ChargingPressure4 = value;
          resultLog.AppendLine($"储料压力4: {_data.ChargingPressure4}bar");
          break;
        case "58-00": // 储料压力3
          _data.ChargingPressure3 = value;
          resultLog.AppendLine($"储料压力3: {_data.ChargingPressure3}bar");
          break;
        case "54-00": // 储料压力2
          _data.ChargingPressure2 = value;
          resultLog.AppendLine($"储料压力2: {_data.ChargingPressure2}bar");
          break;
        case "50-00": // 储料压力1
          _data.ChargingPressure1 = value;
          resultLog.AppendLine($"储料压力1: {_data.ChargingPressure1}bar");
          break;


        case "62-00": // 储料速度5
          _data.ChargingSpeed5 = value;
          resultLog.AppendLine($"储料速度5: {_data.ChargingSpeed5}rpm");
          break;

        // 储料速度4
        case "5E-00": // 储料速度4
          _data.ChargingSpeed4 = value;
          resultLog.AppendLine($"储料速度4: {_data.ChargingSpeed4}rpm");
          break;

        // 储料速度3
        case "5A-00": // 储料速度3
          _data.ChargingSpeed3 = value;
          resultLog.AppendLine($"储料速度3: {_data.ChargingSpeed3}rpm");
          break;

        // 储料速度2
        case "56-00": // 储料速度2
          _data.ChargingSpeed2 = value;
          resultLog.AppendLine($"储料速度2: {_data.ChargingSpeed2}rpm");
          break;

        // 储料速度1
        case "52-00": // 储料速度1
          _data.ChargingSpeed1 = value;

          resultLog.AppendLine($"储料背压5: {_data.ChargingBackPressure5}bar");
          break;

        // 储料背压5
        case "61-00": // 储料背压5
          _data.ChargingBackPressure5 = value;
          resultLog.AppendLine($"储料背压5: {_data.ChargingBackPressure5}bar");
          break;

        // 储料背压4
        case "5D-00": // 储料背压4
          _data.ChargingBackPressure4 = value;
          resultLog.AppendLine($"储料背压4: {_data.ChargingBackPressure4}bar");
          break;

        // 储料背压3
        case "59-00": // 储料背压3
          _data.ChargingBackPressure3 = value;
          resultLog.AppendLine($"储料背压3: {_data.ChargingBackPressure3}bar");
          break;

        // 储料背压2
        case "55-00": // 储料背压2
          _data.ChargingBackPressure2 = value;
          resultLog.AppendLine($"储料背压2: {_data.ChargingBackPressure2}bar");
          break;


        // 储料背压1
        case "51-00": // 储料背压1
          _data.ChargingBackPressure1 = value;
          resultLog.AppendLine($"储料背压1: {_data.ChargingBackPressure1}bar");
          break;

        // 储料储前冷却
        case "B8-00": // 储料储前冷却
          _data.ChargingFrontCooling = value * 0.01f;
          resultLog.AppendLine($"储料储前冷却: {_data.ChargingFrontCooling:F2}℃");
          break;

        // 储料再次储料计时
        case "6A-00": // 储料再次储料计时
          _data.ChargingRechargeTime = value * 0.01f;
          resultLog.AppendLine($"储料再次储料计时: {_data.ChargingRechargeTime:F2}s");
          break;

        // 保压相关参数
        case "50-02": // 保压压力1
          _data.PressureHoldPressure1 = value;
          resultLog.AppendLine($"保压压力1: {_data.PressureHoldPressure1}bar");
          break;
        case "54-02": // 保压压力2
          _data.PressureHoldPressure2 = value;
          resultLog.AppendLine($"保压压力2: {_data.PressureHoldPressure2}bar");
          break;
        case "58-02": // 保压压力3
          _data.PressureHoldPressure3 = value;
          resultLog.AppendLine($"保压压力3: {_data.PressureHoldPressure3}bar");
          break;
        case "5C-02": // 保压压力4
          _data.PressureHoldPressure4 = value;
          resultLog.AppendLine($"保压压力4: {_data.PressureHoldPressure4}bar");
          break;
        case "60-02": // 保压压力5
          _data.PressureHoldPressure5 = value;
          resultLog.AppendLine($"保压压力5: {_data.PressureHoldPressure5}bar");
          break;
        case "64-02": // 保压压力6
          _data.PressureHoldPressure6 = value;
          resultLog.AppendLine($"保压压力6: {_data.PressureHoldPressure6}bar");
          break;


        case "51-02": // 保压速度1
          _data.PressureHoldSpeed1 = value * 0.1f;
          resultLog.AppendLine($"保压速度1: {_data.PressureHoldSpeed1:F1}%");
          break;

        // 保压速度2
        case "55-02": // 保压速度2
          _data.PressureHoldSpeed2 = value * 0.1f;
          resultLog.AppendLine($"保压速度2: {_data.PressureHoldSpeed2:F1}%");
          break;

        // 保压速度3
        case "59-02": // 保压速度3
          _data.PressureHoldSpeed3 = value * 0.1f;
          resultLog.AppendLine($"保压速度3: {_data.PressureHoldSpeed3:F1}%");
          break;

        // 保压速度4
        case "5D-02": // 保压速度4
          _data.PressureHoldSpeed4 = value * 0.1f;
          resultLog.AppendLine($"保压速度4: {_data.PressureHoldSpeed4:F1}%");
          break;

        // 保压速度5
        case "61-02": // 保压速度5
          _data.PressureHoldSpeed5 = value * 0.1f;
          resultLog.AppendLine($"保压速度5: {_data.PressureHoldSpeed5:F1}%");
          break;

        // 保压速度6
        case "65-02": // 保压速度6
          _data.PressureHoldSpeed6 = value * 0.1f;
          resultLog.AppendLine($"保压速度6: {_data.PressureHoldSpeed6:F1}%");
          break;

        // 保压时间1
        case "52-02": // 保压时间1
          _data.PressureHoldTime1 = value * 0.01f;
          resultLog.AppendLine($"保压时间1: {_data.PressureHoldTime1:F2}s");
          break;

        // 保压时间2
        case "56-02": // 保压时间2
          _data.PressureHoldTime2 = value * 0.01f;
          resultLog.AppendLine($"保压时间2: {_data.PressureHoldTime2:F2}s");
          break;

        // 保压时间3
        case "5A-02": // 保压时间3
          _data.PressureHoldTime3 = value * 0.01f;
          resultLog.AppendLine($"保压时间3: {_data.PressureHoldTime3:F2}s");
          break;

        // 保压时间4
        case "5E-02": // 保压时间4
          _data.PressureHoldTime4 = value * 0.01f;
          resultLog.AppendLine($"保压时间4: {_data.PressureHoldTime4:F2}s");
          break;

        // 保压时间5
        case "62-02": // 保压时间5
          _data.PressureHoldTime5 = value * 0.01f;
          resultLog.AppendLine($"保压时间5: {_data.PressureHoldTime5:F2}s");
          break;

        // 保压时间6
        case "66-02": // 保压时间6
          _data.PressureHoldTime6 = value * 0.01f;
          resultLog.AppendLine($"保压时间6: {_data.PressureHoldTime6:F2}s");
          break;

        // 托模次数
        case "AD-01": // 托模次数
          _data.EjectorCount = value;
          resultLog.AppendLine($"托模次数: {_data.EjectorCount}");
          break;

        // 托模进位置1
        case "A2-01": // 托模进位置1
          _data.EjectorForwardPos1 = value * 0.1f;
          resultLog.AppendLine($"托模进位置1: {_data.EjectorForwardPos1:F1}mm");
          break;


        // 托模进位置2
        case "A5-01": // 托模进位置2
          _data.EjectorForwardPos2 = value * 0.1f;
          resultLog.AppendLine($"托模进位置2: {_data.EjectorForwardPos2:F1}mm");
          break;

        // 托模进压力2
        case "A3-01": // 托模进压力2
          _data.EjectorForwardPressure2 = value;
          resultLog.AppendLine($"托模进压力2: {_data.EjectorForwardPressure2}bar");
          break;

        // 托模进压力1
        case "A0-01": // 托模进压力1
          _data.EjectorForwardPressure1 = value;
          resultLog.AppendLine($"托模进压力1: {_data.EjectorForwardPressure1}bar");
          break;

        // 托模进速度2*0.1
        case "A4-01": // 托模进速度2*0.1
          _data.EjectorForwardSpeed2 = value * 0.1f;
          resultLog.AppendLine($"托模进速度2: {_data.EjectorForwardSpeed2:F1}%");
          break;

        // 托模进速度1*0.1
        case "A1-01": // 托模进速度1*0.1
          _data.EjectorForwardSpeed1 = value * 0.1f;
          resultLog.AppendLine($"托模进速度1: {_data.EjectorForwardSpeed1:F1}%");
          break;

        // 托模进延时时间*0.01
        case "AE-01": // 托模进延时时间*0.01
          _data.EjectorForwardDelayTime = value * 0.01f;
          resultLog.AppendLine($"托模进延时时间: {_data.EjectorForwardDelayTime:F2}s");
          break;

        // 托模出位置2*0.1
        case "AB-01": // 托模出位置2*0.1
          _data.EjectorBackwardPos2 = value * 0.1f;
          resultLog.AppendLine($"托模出位置2: {_data.EjectorBackwardPos2:F1}mm");
          break;

        // 托模出位置1*0.1
        case "A8-01": // 托模出位置1*0.1
          _data.EjectorBackwardPos1 = value * 0.1f;
          resultLog.AppendLine($"托模出位置1: {_data.EjectorBackwardPos1:F1}mm");
          break;

        // 托模出压力1
        case "A6-01": // 托模出压力1
          _data.EjectorBackwardPressure1 = value;
          resultLog.AppendLine($"托模出压力1: {_data.EjectorBackwardPressure1}bar");
          break;

        // 托模出压力2
        case "A9-01": // 托模出压力2
          _data.EjectorBackwardPressure2 = value;
          resultLog.AppendLine($"托模出压力2: {_data.EjectorBackwardPressure2}bar");
          break;

        // 托模出速度2*0.1
        case "AA-01": // 托模出速度2*0.1
          _data.EjectorBackwardSpeed2 = value * 0.1f;
          resultLog.AppendLine($"托模出速度2: {_data.EjectorBackwardSpeed2:F1}%");
          break;

        // 托模出速度1*0.1
        case "A7-01": // 托模出速度1*0.1
          _data.EjectorBackwardSpeed1 = value * 0.1f;
          resultLog.AppendLine($"托模出速度1: {_data.EjectorBackwardSpeed1:F1}%");
          break;

        // 托模出延时时间*0.01
        case "AF-01": // 托模出延时时间*0.01
          _data.EjectorBackwardDelayTime = value * 0.01f;
          resultLog.AppendLine($"托模出延时时间: {_data.EjectorBackwardDelayTime:F2}s");
          break;

        default:
          resultLog.AppendLine($"未处理的参数标识: {identifier}");
          break;
      }

      _ = _driverInfo.Socket.Send(resultLog.ToString(), _driverInfo.DeviceId + "_Logs");
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"解析参数下写报文异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
    }
  }

  /// <summary>
  /// 释放资源
  /// </summary>
  public void Dispose()
  {
    _tokenSource.Cancel();
    Thread.Sleep(200); // 等待抓包线程结束

    try
    {
      if (_captureDevice != null)
      {
        if (_captureDevice.Started)
        {
          _captureDevice.StopCapture();
        }
        _captureDevice.Close();
      }
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"关闭抓包设备异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
    }

    _tokenSource.Dispose();
  }
}