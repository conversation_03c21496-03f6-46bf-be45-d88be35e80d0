using System;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using HslCommunication;
using System.Text.Json;
using System.IO;

namespace TechMation5521;

/// <summary>
/// 卷绕机驱动
/// </summary>
[DriverInfo("TechMation5521", "V1.0.0", "注塑机")]
public class TechMation5521 : BaseDeviceProtocolCollector, IDriver
{
  private readonly TechMation5521Data _techMation5521Data = new();
  private ClientHandler _client;

  public TechMation5521(DriverInfoDto driverInfo)
  {
    Console.WriteLine("5521初始化了");
    DriverInfo = driverInfo;
  }

  /// <summary>
  /// 重连周期(s)
  /// </summary>
  [ConfigParameter("重连周期(s)", GroupName = "高级配置", Remark = "当设备关机后下一轮进行重新连接的时间")]
  public override int ReConnTime { get; set; } = 5;

  /// <summary>
  /// 写入间隔周期(ms)
  /// </summary>
  [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置", Display = false)]
  public override int WriteInterval { get; set; } = 120;

  /// <summary>
  /// 启用控制台日志
  /// </summary>
  [ConfigParameter("启用控制台日志", GroupName = "高级配置", Remark = "是否在控制台输出日志信息")]
  public BoolEnum EnableConsoleLog { get; set; } = BoolEnum.False;

  /// <summary>
  /// IP地址
  /// </summary>
  [ConfigParameter("IP地址")]
  public string IpAddress { get; set; } = "127.0.0.1";

  /// <summary>
  /// 网口名称
  /// </summary>
  [ConfigParameter("网口名称")]
  public string InterfaceName { get; set; } = "";

  /// <summary>
  /// 端口号
  /// </summary>
  [ConfigParameter("端口号")]
  public int Port { get; set; } = 2311;

  /// <summary>
  /// 批量读取
  /// </summary>
  [ConfigParameter("批量读取", GroupName = "高级配置", Display = false, Remark = "不连续报文组成合并包，减少查询包次数，缩短轮询周期")]
  public virtual BoolEnum BulkRead { get; set; } = BoolEnum.True;

  /// <summary>
  /// 是否连接
  /// </summary>
  public override bool IsConnected { get; set; }

  /// <summary>
  /// 简单的控制台日志输出
  /// </summary>
  private void LogConsole(string message)
  {
    // 发送到Socket
    _ = DriverInfo.Socket.Send(message, DriverInfo.DeviceId + "_Logs");

    // 如果启用了控制台日志，则同时输出到控制台
    if (EnableConsoleLog == BoolEnum.True)
    {
      System.Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}");
    }
  }

  /// <summary>
  /// 连接
  /// </summary>
  /// <returns></returns>
  public DeviceConnectDto Connect()
  {
    try
    {
      LogConsole("连接中...");
      // 尝试从文件加载初始数据
      string dataFilePath = "/etc/injection_molding_machine/data/device_data.json";
      if (File.Exists(dataFilePath))
      {
        LogConsole($"尝试从文件加载初始数据: {dataFilePath}");
        try
        {
          var json = File.ReadAllText(dataFilePath);
          var data = JsonSerializer.Deserialize<TechMation5521Data>(json);
          if (data != null)
          {
            LogConsole($"文件加载成功: {dataFilePath}");
            // 复制数据到_techMation5521Data
            var properties = typeof(TechMation5521Data).GetProperties();
            foreach (var prop in properties)
            {
              prop.SetValue(_techMation5521Data, prop.GetValue(data));
            }
            LogConsole("已从文件加载初始数据");
          }
        }
        catch (Exception ex)
        {
          LogConsole($"加载数据文件异常: {ex.Message}");
        }
      }

      LogConsole("创建客户端");
      // 创建客户端
      _client = new ClientHandler(_techMation5521Data, DriverInfo);
      LogConsole("连接");
      // 连接
      _client.Connect(InterfaceName, IpAddress, Port);
      LogConsole("设置连接状态");
      // 设置连接状态
      IsConnected = true;
      OperateResult = new OperateResult
      {
        // 设置操作结果
        IsSuccess = IsConnected,
        // 设置操作结果消息
        Message = IsConnected ? "连接成功" : "连接失败"
      };

      LogConsole($"连接状态: {(IsConnected ? "成功" : "失败")}");
    }
    catch (Exception ex)
    {
      LogConsole($"连接异常:【{ex.Message}】");
      // 设置操作结果
      return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
    }

    return ResConnect();
  }

  /// <summary>
  /// 断开连接
  /// </summary>
  public override void Close()
  {
    _client?.Dispose();
    LogConsole("连接已断开");
  }

  #region 采集点位

  #region 合模数量数据 (41-43)
  /// <summary>
  /// 合模数量
  /// </summary>
  [Method("MOLD_COUNT", name: "合模数量", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> MoldCount()
  {
    return await PowerOnData("MoldCount");
  }
  #endregion

  #region 实时温度数据 (42-43)
  /// <summary>
  /// 顶针位置 (mm)
  /// </summary>
  [Method("EJECTOR_POSITION", name: "顶针位置", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> EjectorPosition()
  {
    return await PowerOnData("EjectorPosition");
  }

  /// <summary>
  /// 螺杆位置 (mm)
  /// </summary>
  [Method("SCREW_POSITION", name: "螺杆位置", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ScrewPosition()
  {
    return await PowerOnData("ScrewPosition");
  }

  /// <summary>
  /// 温度1实际值 (℃)
  /// </summary>
  [Method("TEMPERATURE1", name: "温度1实际值", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> Temperature1()
  {
    return await PowerOnData("Temperature1");
  }

  /// <summary>
  /// 温度2实际值 (℃)
  /// </summary>
  [Method("TEMPERATURE2", name: "温度2实际值", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> Temperature2()
  {
    return await PowerOnData("Temperature2");
  }

  /// <summary>
  /// 温度3实际值 (℃)
  /// </summary>
  [Method("TEMPERATURE3", name: "温度3实际值", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> Temperature3()
  {
    return await PowerOnData("Temperature3");
  }

  /// <summary>
  /// 温度4实际值 (℃)
  /// </summary>
  [Method("TEMPERATURE4", name: "温度4实际值", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> Temperature4()
  {
    return await PowerOnData("Temperature4");
  }

  /// <summary>
  /// 温度5实际值 (℃)
  /// </summary>
  [Method("TEMPERATURE5", name: "温度5实际值", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> Temperature5()
  {
    return await PowerOnData("Temperature5");
  }

  /// <summary>
  /// 油温 (℃)
  /// </summary>
  [Method("OIL_TEMPERATURE", name: "油温", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> OilTemperature()
  {
    return await PowerOnData("OilTemperature");
  }
  #endregion

  #region SPC数据 (42-45)
  /// <summary>
  /// 上模循环时间 (s)
  /// </summary>
  [Method("CYCLE_TIME", name: "上模循环时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> CycleTime()
  {
    return await PowerOnData("CycleTime");
  }

  /// <summary>
  /// 上模注射保压时间 (s)
  /// </summary>
  [Method("INJECTION_TIME", name: "上模注射保压时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionTime()
  {
    return await PowerOnData("InjectionTime");
  }

  /// <summary>
  /// 上模注射时间 (s)
  /// </summary>
  [Method("INJECTION_ONLY_TIME", name: "上模注射时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionOnlyTime()
  {
    return await PowerOnData("InjectionOnlyTime");
  }

  /// <summary>
  /// 上模储料时间 (s)
  /// </summary>
  [Method("CHARGING_TIME", name: "上模储料时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ChargingTime()
  {
    return await PowerOnData("ChargingTime");
  }

  /// <summary>
  /// 上模射出起点 (mm)
  /// </summary>
  [Method("INJECTION_START_POSITION", name: "上模射出起点", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionStartPosition()
  {
    return await PowerOnData("InjectionStartPosition");
  }

  /// <summary>
  /// 上模保压起点 (mm)
  /// </summary>
  [Method("PRESSURE_HOLD_START_POSITION", name: "上模保压起点", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> PressureHoldStartPosition()
  {
    return await PowerOnData("PressureHoldStartPosition");
  }

  /// <summary>
  /// 上模射出监控 (mm)
  /// </summary>
  [Method("INJECTION_MONITOR_POSITION", name: "上模射出监控", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionMonitorPosition()
  {
    return await PowerOnData("InjectionMonitorPosition");
  }

  /// <summary>
  /// 上模射出终点 (mm)
  /// </summary>
  [Method("INJECTION_END_POSITION", name: "上模射出终点", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionEndPosition()
  {
    return await PowerOnData("InjectionEndPosition");
  }

  /// <summary>
  /// 上模转保压力 (bar)
  /// </summary>
  [Method("PRESSURE_HOLD_SWITCH_PRESSURE", name: "上模转保压力", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> PressureHoldSwitchPressure()
  {
    return await PowerOnData("PressureHoldSwitchPressure");
  }


  /// <summary>
  /// 上模射出尖压 (bar)
  /// </summary>
  [Method("INJECTION_PEAK_PRESSURE", name: "上模射出尖压", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> InjectionPeakPressure()
  {
    return await PowerOnData("InjectionPeakPressure");
  }

  /// <summary>
  /// 上模储料尖压 (bar)
  /// </summary>
  [Method("CHARGING_PEAK_PRESSURE", name: "上模储料尖压", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> ChargingPeakPressure()
  {
    return await PowerOnData("ChargingPeakPressure");
  }
  #endregion

  #region 温度设定数据 (32-34)
  /// <summary>
  /// 温度1设定值 (℃)
  /// </summary>
  [Method("TEMPERATURE_SETTING1", name: "温度1设定值", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> TemperatureSetting1()
  {
    return await PowerOnData("TemperatureSetting1");
  }

  /// <summary>
  /// 温度2设定值 (℃)
  /// </summary>
  [Method("TEMPERATURE_SETTING2", name: "温度2设定值", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> TemperatureSetting2()
  {
    return await PowerOnData("TemperatureSetting2");
  }

  /// <summary>
  /// 温度3设定值 (℃)
  /// </summary>
  [Method("TEMPERATURE_SETTING3", name: "温度3设定值", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> TemperatureSetting3()
  {
    return await PowerOnData("TemperatureSetting3");
  }

  /// <summary>
  /// 温度4设定值 (℃)
  /// </summary>
  [Method("TEMPERATURE_SETTING4", name: "温度4设定值", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> TemperatureSetting4()
  {
    return await PowerOnData("TemperatureSetting4");
  }

  /// <summary>
  /// 温度5设定值 (℃)
  /// </summary>
  [Method("TEMPERATURE_SETTING5", name: "温度5设定值", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> TemperatureSetting5()
  {
    return await PowerOnData("TemperatureSetting5");
  }
  #endregion

  #region 设定数据2 (33-33)

  #region  射出参数

  #region 射出1
  /// <summary>
  /// 射出压力1 (bar)
  /// </summary>
  [Method("INJECTION_PRESSURE1", name: "射出压力1", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> InjectionPressure1()
  {
    return await PowerOnData("InjectionPressure1");
  }

  /// <summary>
  /// 射出速度1 (%)
  /// </summary>
  [Method("INJECTION_SPEED1", name: "射出速度1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionSpeed1()
  {
    return await PowerOnData("InjectionSpeed1");
  }

  /// <summary>
  /// 射出位置1 (mm)
  /// </summary>
  [Method("INJECTION_POSITION1", name: "射出位置1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionPosition1()
  {
    return await PowerOnData("InjectionPosition1");
  }

  #endregion

  #region 射出2
  /// <summary>
  /// 射出压力2 (bar)
  /// </summary>
  [Method("INJECTION_PRESSURE2", name: "射出压力2", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> InjectionPressure2()
  {
    return await PowerOnData("InjectionPressure2");
  }

  /// <summary>
  /// 射出速度2 (%)
  /// </summary>
  [Method("INJECTION_SPEED2", name: "射出速度2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionSpeed2()
  {
    return await PowerOnData("InjectionSpeed2");
  }

  /// <summary>
  /// 射出位置2 (mm)
  /// </summary>
  [Method("INJECTION_POSITION2", name: "射出位置2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionPosition2()
  {
    return await PowerOnData("InjectionPosition2");
  }
  #endregion

  #region 射出3
  /// <summary>
  /// 射出压力3 (bar)
  /// </summary>
  [Method("INJECTION_PRESSURE3", name: "射出压力3", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> InjectionPressure3()
  {
    return await PowerOnData("InjectionPressure3");
  }

  /// <summary>
  /// 射出速度3 (%)
  /// </summary>
  [Method("INJECTION_SPEED3", name: "射出速度3", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionSpeed3()
  {
    return await PowerOnData("InjectionSpeed3");
  }

  /// <summary>
  /// 射出位置3 (mm)
  /// </summary>
  [Method("INJECTION_POSITION3", name: "射出位置3", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionPosition3()
  {
    return await PowerOnData("InjectionPosition3");
  }
  #endregion

  #region 射出4
  /// <summary>
  /// 射出压力4 (bar)
  /// </summary>
  [Method("INJECTION_PRESSURE4", name: "射出压力4", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> InjectionPressure4()
  {
    return await PowerOnData("InjectionPressure4");
  }

  /// <summary>
  /// 射出速度4 (%)
  /// </summary>
  [Method("INJECTION_SPEED4", name: "射出速度4", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionSpeed4()
  {
    return await PowerOnData("InjectionSpeed4");
  }

  /// <summary>
  /// 射出位置4 (mm)
  /// </summary>
  [Method("INJECTION_POSITION4", name: "射出位置4", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionPosition4()
  {
    return await PowerOnData("InjectionPosition4");
  }
  #endregion

  #region 射出5
  /// <summary>
  /// 射出压力5 (bar)
  /// </summary>
  [Method("INJECTION_PRESSURE5", name: "射出压力5", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> InjectionPressure5()
  {
    return await PowerOnData("InjectionPressure5");
  }

  /// <summary>
  /// 射出速度5 (%)
  /// </summary>
  [Method("INJECTION_SPEED5", name: "射出速度5", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionSpeed5()
  {
    return await PowerOnData("InjectionSpeed5");
  }

  /// <summary>
  /// 射出位置5 (mm)
  /// </summary>
  [Method("INJECTION_POSITION5", name: "射出位置5", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionPosition5()
  {
    return await PowerOnData("InjectionPosition5");
  }
  #endregion

  #region 射出6
  /// <summary>
  /// 射出压力6 (bar)
  /// </summary>
  [Method("INJECTION_PRESSURE6", name: "射出压力6", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> InjectionPressure6()
  {
    return await PowerOnData("InjectionPressure6");
  }

  /// <summary>
  /// 射出速度6 (%)
  /// </summary>
  [Method("INJECTION_SPEED6", name: "射出速度6", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionSpeed6()
  {
    return await PowerOnData("InjectionSpeed6");
  }

  /// <summary>
  /// 射出位置6 (mm)
  /// </summary>
  [Method("INJECTION_POSITION6", name: "射出位置6", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> InjectionPosition6()
  {
    return await PowerOnData("InjectionPosition6");
  }
  #endregion

  #endregion

  #region 座台参数

  #region 座台1

  #region 座台进压力1
  /// <summary>
  /// 座台进压力1 (bar)
  /// </summary>
  [Method("SEAT_FORWARD_PRESSURE1", name: "座台进压力1", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> SeatForwardPressure1()
  {
    return await PowerOnData("SeatForwardPressure1");
  }

  /// <summary>
  /// 座台进速度1 (%)
  /// </summary>
  [Method("SEAT_FORWARD_SPEED1", name: "座台进速度1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SeatForwardSpeed1()
  {
    return await PowerOnData("SeatForwardSpeed1");
  }
  #endregion

  #region 座台进压力2
  /// <summary>
  /// 座台进压力2 (bar)
  /// </summary>
  [Method("SEAT_FORWARD_PRESSURE2", name: "座台进压力2", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> SeatForwardPressure2()
  {
    return await PowerOnData("SeatForwardPressure2");
  }

  /// <summary>
  /// 座台进速度2 (%)
  /// </summary>
  [Method("SEAT_FORWARD_SPEED2", name: "座台进速度2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SeatForwardSpeed2()
  {
    return await PowerOnData("SeatForwardSpeed2");
  }
  #endregion

  /// <summary>
  /// 座台进时间 (s)
  /// </summary>
  [Method("SEAT_FORWARD_TIME", name: "座台进时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SeatForwardTime()
  {
    return await PowerOnData("SeatForwardTime");
  }
  #endregion

  #region 开模参数

  #region 开模1
  /// <summary>
  /// 开模压力1 (bar)
  /// </summary>
  [Method("MOLD_OPEN_PRESSURE1", name: "开模压力1", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> MoldOpenPressure1()
  {
    return await PowerOnData("MoldOpenPressure1");
  }

  /// <summary>
  /// 开模速度1 (%)
  /// </summary>
  [Method("MOLD_OPEN_SPEED1", name: "开模速度1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldOpenSpeed1()
  {
    return await PowerOnData("MoldOpenSpeed1");
  }

  /// <summary>
  /// 开模位置1 (mm)
  /// </summary>
  [Method("MOLD_OPEN_POSITION1", name: "开模位置1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldOpenPosition1()
  {
    return await PowerOnData("MoldOpenPosition1");
  }
  #endregion

  #region 开模2
  /// <summary>
  /// 开模压力2 (bar)
  /// </summary>
  [Method("MOLD_OPEN_PRESSURE2", name: "开模压力2", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> MoldOpenPressure2()
  {
    return await PowerOnData("MoldOpenPressure2");
  }

  /// <summary>
  /// 开模速度2 (%)
  /// </summary>
  [Method("MOLD_OPEN_SPEED2", name: "开模速度2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldOpenSpeed2()
  {
    return await PowerOnData("MoldOpenSpeed2");
  }

  /// <summary>
  /// 开模位置2 (mm)
  /// </summary>
  [Method("MOLD_OPEN_POSITION2", name: "开模位置2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldOpenPosition2()
  {
    return await PowerOnData("MoldOpenPosition2");
  }
  #endregion

  #region 开模3
  /// <summary>
  /// 开模压力3 (bar)
  /// </summary>
  [Method("MOLD_OPEN_PRESSURE3", name: "开模压力3", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> MoldOpenPressure3()
  {
    return await PowerOnData("MoldOpenPressure3");
  }

  /// <summary>
  /// 开模速度3 (%)
  /// </summary>
  [Method("MOLD_OPEN_SPEED3", name: "开模速度3", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldOpenSpeed3()
  {
    return await PowerOnData("MoldOpenSpeed3");
  }

  /// <summary>
  /// 开模位置3 (mm)
  /// </summary>
  [Method("MOLD_OPEN_POSITION3", name: "开模位置3", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldOpenPosition3()
  {
    return await PowerOnData("MoldOpenPosition3");
  }
  #endregion

  #region 开模4
  /// <summary>
  /// 开模压力4 (bar)
  /// </summary>
  [Method("MOLD_OPEN_PRESSURE4", name: "开模压力4", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> MoldOpenPressure4()
  {
    return await PowerOnData("MoldOpenPressure4");
  }

  /// <summary>
  /// 开模速度4 (%)
  /// </summary>
  [Method("MOLD_OPEN_SPEED4", name: "开模速度4", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldOpenSpeed4()
  {
    return await PowerOnData("MoldOpenSpeed4");
  }

  /// <summary>
  /// 开模位置4 (mm)
  /// </summary>
  [Method("MOLD_OPEN_POSITION4", name: "开模位置4", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldOpenPosition4()
  {
    return await PowerOnData("MoldOpenPosition4");
  }
  #endregion

  #region 开模5
  /// <summary>
  /// 开模压力5 (bar)
  /// </summary>
  [Method("MOLD_OPEN_PRESSURE5", name: "开模压力5", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> MoldOpenPressure5()
  {
    return await PowerOnData("MoldOpenPressure5");
  }

  /// <summary>
  /// 开模速度5 (%)
  /// </summary>
  [Method("MOLD_OPEN_SPEED5", name: "开模速度5", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldOpenSpeed5()
  {
    return await PowerOnData("MoldOpenSpeed5");
  }

  /// <summary>
  /// 开模位置5 (mm)
  /// </summary>
  [Method("MOLD_OPEN_POSITION5", name: "开模位置5", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldOpenPosition5()
  {
    return await PowerOnData("MoldOpenPosition5");
  }
  #endregion

  /// <summary>
  /// 座台退压力1 (bar)
  /// </summary>
  [Method("SEAT_BACK_PRESSURE1", name: "座台退压力1", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> SeatBackPressure1()
  {
    return await PowerOnData("SeatBackPressure1");
  }

  /// <summary>
  /// 座台退速度1 (%)
  /// </summary>
  [Method("SEAT_BACK_SPEED1", name: "座台退速度1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SeatBackSpeed1()
  {
    return await PowerOnData("SeatBackSpeed1");
  }

  /// <summary>
  /// 座台退时间 (s)
  /// </summary>
  [Method("SEAT_BACK_TIME", name: "座台退时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SeatBackTime()
  {
    return await PowerOnData("SeatBackTime");
  }

  /// <summary>
  /// 座台退延迟时间 (s)
  /// </summary>
  [Method("SEAT_BACK_DELAY_TIME", name: "座台退延迟时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SeatBackDelayTime()
  {
    return await PowerOnData("SeatBackDelayTime");
  }

  #endregion

  #endregion

  #endregion

  #region 设定数据1 (32-33)

  #region  储料参数

  #region 储料1
  /// <summary>
  /// 储料压力1 (bar)
  /// </summary>
  [Method("CHARGING_PRESSURE1", name: "储料压力1", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> ChargingPressure1()
  {
    return await PowerOnData("ChargingPressure1");
  }

  /// <summary>
  /// 储料背压1 (bar)
  /// </summary>
  [Method("CHARGING_BACK_PRESSURE1", name: "储料背压1", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> ChargingBackPressure1()
  {
    return await PowerOnData("ChargingBackPressure1");
  }

  /// <summary>
  /// 储料速度1 (rpm)
  /// </summary>
  [Method("CHARGING_SPEED1", name: "储料速度1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ChargingSpeed1()
  {
    return await PowerOnData("ChargingSpeed1");
  }

  /// <summary>
  /// 储料位置1 (mm)
  /// </summary>
  [Method("CHARGING_POSITION1", name: "储料位置1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ChargingPosition1()
  {
    return await PowerOnData("ChargingPosition1");
  }
  #endregion

  #region 储料2

  /// <summary>
  /// 储料压力2 (bar)
  /// </summary>
  [Method("CHARGING_PRESSURE2", name: "储料压力2", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> ChargingPressure2()
  {
    return await PowerOnData("ChargingPressure2");
  }

  /// <summary>
  /// 储料背压2 (bar)
  /// </summary>
  [Method("CHARGING_BACK_PRESSURE2", name: "储料背压2", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> ChargingBackPressure2()
  {
    return await PowerOnData("ChargingBackPressure2");
  }

  /// <summary>
  /// 储料速度2 (rpm)
  /// </summary>
  [Method("CHARGING_SPEED2", name: "储料速度2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ChargingSpeed2()
  {
    return await PowerOnData("ChargingSpeed2");
  }

  /// <summary>
  /// 储料位置2 (mm)
  /// </summary>
  [Method("CHARGING_POSITION2", name: "储料位置2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ChargingPosition2()
  {
    return await PowerOnData("ChargingPosition2");
  }
  #endregion

  #region 储料3

  /// <summary>
  /// 储料压力3 (bar)
  /// </summary>
  [Method("CHARGING_PRESSURE3", name: "储料压力3", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> ChargingPressure3()
  {
    return await PowerOnData("ChargingPressure3");
  }

  /// <summary>
  /// 储料背压3 (bar)
  /// </summary>
  [Method("CHARGING_BACK_PRESSURE3", name: "储料背压3", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> ChargingBackPressure3()
  {
    return await PowerOnData("ChargingBackPressure3");
  }

  /// <summary>
  /// 储料速度3 (rpm)
  /// </summary>
  [Method("CHARGING_SPEED3", name: "储料速度3", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ChargingSpeed3()
  {
    return await PowerOnData("ChargingSpeed3");
  }

  /// <summary>
  /// 储料位置3 (mm)
  /// </summary>
  [Method("CHARGING_POSITION3", name: "储料位置3", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ChargingPosition3()
  {
    return await PowerOnData("ChargingPosition3");
  }
  #endregion

  #region 储料4

  /// <summary>
  /// 储料压力4 (bar)
  /// </summary>
  [Method("CHARGING_PRESSURE4", name: "储料压力4", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> ChargingPressure4()
  {
    return await PowerOnData("ChargingPressure4");
  }

  /// <summary>
  /// 储料背压4 (bar)
  /// </summary>
  [Method("CHARGING_BACK_PRESSURE4", name: "储料背压4", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> ChargingBackPressure4()
  {
    return await PowerOnData("ChargingBackPressure4");
  }

  /// <summary>
  /// 储料速度4 (rpm)
  /// </summary>
  [Method("CHARGING_SPEED4", name: "储料速度4", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ChargingSpeed4()
  {
    return await PowerOnData("ChargingSpeed4");
  }

  /// <summary>
  /// 储料位置4 (mm)
  /// </summary>
  [Method("CHARGING_POSITION4", name: "储料位置4", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ChargingPosition4()
  {
    return await PowerOnData("ChargingPosition4");
  }
  #endregion

  #region 储料5

  /// <summary>
  /// 储料压力5 (bar)
  /// </summary>
  [Method("CHARGING_PRESSURE5", name: "储料压力5", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> ChargingPressure5()
  {
    return await PowerOnData("ChargingPressure5");
  }

  /// <summary>
  /// 储料背压5 (bar)
  /// </summary>
  [Method("CHARGING_BACK_PRESSURE5", name: "储料背压5", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> ChargingBackPressure5()
  {
    return await PowerOnData("ChargingBackPressure5");
  }

  /// <summary>
  /// 储料速度5 (rpm)
  /// </summary>  
  [Method("CHARGING_SPEED5", name: "储料速度5", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ChargingSpeed5()
  {
    return await PowerOnData("ChargingSpeed5");
  }

  /// <summary>
  /// 储料位置5 (mm)
  /// </summary>
  [Method("CHARGING_POSITION5", name: "储料位置5", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ChargingPosition5()
  {
    return await PowerOnData("ChargingPosition5");
  }
  #endregion

  #endregion

  #region  合模参数

  #region 合模1

  /// <summary>
  /// 合模压力1 (bar)
  /// </summary>
  [Method("MOLD_CLAMP_PRESSURE1", name: "合模压力1", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> MoldClampPressure1()
  {
    return await PowerOnData("MoldClampPressure1");
  }

  /// <summary>
  /// 合模速度1 (%)
  /// </summary>
  [Method("MOLD_CLAMP_SPEED1", name: "合模速度1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldClampSpeed1()
  {
    return await PowerOnData("MoldClampSpeed1");
  }

  /// <summary>
  /// 合模位置1 (mm)
  /// </summary>
  [Method("MOLD_CLAMP_POSITION1", name: "合模位置1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldClampPosition1()
  {
    return await PowerOnData("MoldClampPosition1");
  }

  #endregion

  #region 合模2
  /// <summary>
  /// 合模压力2 (bar)
  /// </summary>
  [Method("MOLD_CLAMP_PRESSURE2", name: "合模压力2", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> MoldClampPressure2()
  {
    return await PowerOnData("MoldClampPressure2");
  }

  /// <summary>
  /// 合模速度2 (%)
  /// </summary>
  [Method("MOLD_CLAMP_SPEED2", name: "合模速度2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldClampSpeed2()
  {
    return await PowerOnData("MoldClampSpeed2");
  }

  /// <summary>
  /// 合模位置2 (mm)
  /// </summary>
  [Method("MOLD_CLAMP_POSITION2", name: "合模位置2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldClampPosition2()
  {
    return await PowerOnData("MoldClampPosition2");
  }


  #endregion

  #region 合模3

  /// <summary>
  /// 合模压力3 (bar)
  /// </summary>
  [Method("MOLD_CLAMP_PRESSURE3", name: "合模压力3", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> MoldClampPressure3()
  {
    return await PowerOnData("MoldClampPressure3");
  }

  /// <summary>
  /// 合模速度3 (%)
  /// </summary>
  [Method("MOLD_CLAMP_SPEED3", name: "合模速度3", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldClampSpeed3()
  {
    return await PowerOnData("MoldClampSpeed3");
  }

  /// <summary>
  /// 合模位置3 (mm)
  /// </summary>
  [Method("MOLD_CLAMP_POSITION3", name: "合模位置3", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldClampPosition3()
  {
    return await PowerOnData("MoldClampPosition3");
  }

  #endregion

  #region 合模4

  /// <summary>
  /// 合模压力4 (bar)
  /// </summary>
  [Method("MOLD_CLAMP_PRESSURE4", name: "合模压力4", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> MoldClampPressure4()
  {
    return await PowerOnData("MoldClampPressure4");
  }

  /// <summary>
  /// 合模速度4 (%)
  /// </summary>
  [Method("MOLD_CLAMP_SPEED4", name: "合模速度4", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldClampSpeed4()
  {
    return await PowerOnData("MoldClampSpeed4");
  }

  /// <summary>
  /// 合模位置4 (mm)
  /// </summary>
  [Method("MOLD_CLAMP_POSITION4", name: "合模位置4", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldClampPosition4()
  {
    return await PowerOnData("MoldClampPosition4");
  }

  #endregion

  #region 合模5

  /// <summary>
  /// 合模压力5 (bar)
  /// </summary>
  [Method("MOLD_CLAMP_PRESSURE5", name: "合模压力5", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> MoldClampPressure5()
  {
    return await PowerOnData("MoldClampPressure5");
  }

  /// <summary>
  /// 合模速度5 (%)
  /// </summary>
  [Method("MOLD_CLAMP_SPEED5", name: "合模速度5", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldClampSpeed5()
  {
    return await PowerOnData("MoldClampSpeed5");
  }

  /// <summary>
  /// 合模位置5 (mm)
  /// </summary>
  [Method("MOLD_CLAMP_POSITION5", name: "合模位置5", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MoldClampPosition5()
  {
    return await PowerOnData("MoldClampPosition5");
  }
  #endregion

  #endregion

  /// <summary>
  /// 储料储前冷却 (℃)
  /// </summary>
  [Method("CHARGING_FRONT_COOLING", name: "储料储前冷却", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ChargingFrontCooling()
  {
    return await PowerOnData("ChargingFrontCooling");
  }

  /// <summary>
  /// 储料再次储料计时
  /// </summary>
  [Method("CHARGING_RECHARGE_TIME", name: "储料再次储料计时", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ChargingRechargeTime()
  {
    return await PowerOnData("ChargingRechargeTime");
  }

  #region 保压

  #region 保压1

  /// <summary>
  /// 保压压力1 (bar)
  /// </summary>
  [Method("PRESSURE_HOLD_PRESSURE1", name: "保压压力1", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> PressureHoldPressure1()
  {
    return await PowerOnData("PressureHoldPressure1");
  }

  /// <summary>
  /// 保压速度1 (%)
  /// </summary>
  [Method("PRESSURE_HOLD_SPEED1", name: "保压速度1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> PressureHoldSpeed1()
  {
    return await PowerOnData("PressureHoldSpeed1");
  }

  /// <summary>
  /// 保压时间1 (s)
  /// </summary>
  [Method("PRESSURE_HOLD_TIME1", name: "保压时间1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> PressureHoldTime1()
  {
    return await PowerOnData("PressureHoldTime1");
  }

  #endregion

  #region 保压2

  /// <summary>
  /// 保压压力2 (bar)
  /// </summary>
  [Method("PRESSURE_HOLD_PRESSURE2", name: "保压压力2", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> PressureHoldPressure2()

  {
    return await PowerOnData("PressureHoldPressure2");
  }

  /// <summary>
  /// 保压速度2 (%)
  /// </summary>
  [Method("PRESSURE_HOLD_SPEED2", name: "保压速度2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> PressureHoldSpeed2()
  {
    return await PowerOnData("PressureHoldSpeed2");
  }

  /// <summary>
  /// 保压时间2 (s)
  /// </summary>
  [Method("PRESSURE_HOLD_TIME2", name: "保压时间2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> PressureHoldTime2()
  {
    return await PowerOnData("PressureHoldTime2");
  }

  #endregion

  #region 保压3

  /// <summary>
  /// 保压压力3 (bar)
  /// </summary>
  [Method("PRESSURE_HOLD_PRESSURE3", name: "保压压力3", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> PressureHoldPressure3()
  {
    return await PowerOnData("PressureHoldPressure3");
  }

  /// <summary>
  /// 保压速度3 (%)
  /// </summary>
  [Method("PRESSURE_HOLD_SPEED3", name: "保压速度3", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> PressureHoldSpeed3()
  {
    return await PowerOnData("PressureHoldSpeed3");
  }

  /// <summary>
  /// 保压时间3 (s)
  /// </summary>
  [Method("PRESSURE_HOLD_TIME3", name: "保压时间3", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> PressureHoldTime3()
  {
    return await PowerOnData("PressureHoldTime3");
  }

  #endregion

  #region 保压压力4

  /// <summary>
  /// 保压压力4 (bar)
  /// </summary>
  [Method("PRESSURE_HOLD_PRESSURE4", name: "保压压力4", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> PressureHoldPressure4()
  {
    return await PowerOnData("PressureHoldPressure4");
  }

  /// <summary>
  /// 保压速度4 (%)
  /// </summary>
  [Method("PRESSURE_HOLD_SPEED4", name: "保压速度4", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> PressureHoldSpeed4()
  {
    return await PowerOnData("PressureHoldSpeed4");
  }

  /// <summary>
  /// 保压时间4 (s)
  /// </summary>
  [Method("PRESSURE_HOLD_TIME4", name: "保压时间4", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> PressureHoldTime4()
  {
    return await PowerOnData("PressureHoldTime4");
  }
  #endregion

  #region 保压压力5
  /// <summary>
  /// 保压压力5 (bar)
  /// </summary>
  [Method("PRESSURE_HOLD_PRESSURE5", name: "保压压力5", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> PressureHoldPressure5()
  {
    return await PowerOnData("PressureHoldPressure5");
  }

  /// <summary>
  /// 保压速度5 (%)
  /// </summary>
  [Method("PRESSURE_HOLD_SPEED5", name: "保压速度5", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> PressureHoldSpeed5()
  {
    return await PowerOnData("PressureHoldSpeed5");
  }

  /// <summary>
  /// 保压时间5 (s)
  /// </summary>
  [Method("PRESSURE_HOLD_TIME5", name: "保压时间5", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> PressureHoldTime5()
  {
    return await PowerOnData("PressureHoldTime5");
  }
  #endregion

  #region 保压压力6
  /// <summary>
  /// 保压压力6 (bar)
  /// </summary>
  [Method("PRESSURE_HOLD_PRESSURE6", name: "保压压力6", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> PressureHoldPressure6()
  {
    return await PowerOnData("PressureHoldPressure6");
  }

  /// <summary>
  /// 保压速度6 (%)
  /// </summary>
  [Method("PRESSURE_HOLD_SPEED6", name: "保压速度6", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> PressureHoldSpeed6()
  {
    return await PowerOnData("PressureHoldSpeed6");
  }

  /// <summary>
  /// 保压时间6 (s)
  /// </summary>
  [Method("PRESSURE_HOLD_TIME6", name: "保压时间6", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> PressureHoldTime6()
  {
    return await PowerOnData("PressureHoldTime6");
  }
  #endregion
  #endregion

  #endregion

  #region 托模参数
  /// <summary>
  /// 托模进压力1 (bar)
  /// </summary>
  [Method("EJECTOR_FORWARD_PRESSURE1", name: "托模进压力1", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> EjectorForwardPressure1()
  {
    return await PowerOnData("EjectorForwardPressure1");
  }

  /// <summary>
  /// 托模进速度1 (%)
  /// </summary>
  [Method("EJECTOR_FORWARD_SPEED1", name: "托模进速度1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> EjectorForwardSpeed1()
  {
    return await PowerOnData("EjectorForwardSpeed1");
  }

  /// <summary>
  /// 托模进位置1 (mm)
  /// </summary>
  [Method("EJECTOR_FORWARD_POSITION1", name: "托模进位置1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> EjectorForwardPosition1()
  {
    return await PowerOnData("EjectorForwardPos1");
  }

  /// <summary>
  /// 托模进压力2 (bar)
  /// </summary>
  [Method("EJECTOR_FORWARD_PRESSURE2", name: "托模进压力2", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> EjectorForwardPressure2()
  {
    return await PowerOnData("EjectorForwardPressure2");
  }

  /// <summary>
  /// 托模进速度2 (%)
  /// </summary>
  [Method("EJECTOR_FORWARD_SPEED2", name: "托模进速度2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> EjectorForwardSpeed2()
  {
    return await PowerOnData("EjectorForwardSpeed2");
  }

  /// <summary>
  /// 托模进位置2 (mm)
  /// </summary>
  [Method("EJECTOR_FORWARD_POSITION2", name: "托模进位置2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> EjectorForwardPosition2()
  {
    return await PowerOnData("EjectorForwardPos2");
  }

  /// <summary>
  /// 托模退压力1 (bar)
  /// </summary>
  [Method("EJECTOR_BACKWARD_PRESSURE1", name: "托模退压力1", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> EjectorBackwardPressure1()
  {
    return await PowerOnData("EjectorBackwardPressure1");
  }

  /// <summary>
  /// 托模退速度1 (%)
  /// </summary>
  [Method("EJECTOR_BACKWARD_SPEED1", name: "托模退速度1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> EjectorBackwardSpeed1()
  {
    return await PowerOnData("EjectorBackwardSpeed1");
  }

  /// <summary>
  /// 托模退位置1 (mm)
  /// </summary>
  [Method("EJECTOR_BACKWARD_POSITION1", name: "托模退位置1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> EjectorBackwardPosition1()
  {
    return await PowerOnData("EjectorBackwardPos1");
  }

  /// <summary>
  /// 托模退压力2 (bar)
  /// </summary>
  [Method("EJECTOR_BACKWARD_PRESSURE2", name: "托模退压力2", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> EjectorBackwardPressure2()
  {
    return await PowerOnData("EjectorBackwardPressure2");
  }

  /// <summary>
  /// 托模退速度2 (%)
  /// </summary>
  [Method("EJECTOR_BACKWARD_SPEED2", name: "托模退速度2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> EjectorBackwardSpeed2()
  {
    return await PowerOnData("EjectorBackwardSpeed2");
  }

  /// <summary>
  /// 托模退位置2 (mm)
  /// </summary>
  [Method("EJECTOR_BACKWARD_POSITION2", name: "托模退位置2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> EjectorBackwardPosition2()
  {
    return await PowerOnData("EjectorBackwardPos2");
  }

  /// <summary>
  /// 托模方式
  /// </summary>
  [Method("EJECTOR_MODE", name: "托模方式", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> EjectorMode()
  {
    return await PowerOnData("EjectorMode");
  }

  /// <summary>
  /// 托模次数
  /// </summary>
  [Method("EJECTOR_COUNT", name: "托模次数", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> EjectorCount()
  {
    return await PowerOnData("EjectorCount");
  }

  /// <summary>
  /// 托模进延时时间 (s)
  /// </summary>
  [Method("EJECTOR_FORWARD_DELAY_TIME", name: "托模进延时时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> EjectorForwardDelayTime()
  {
    return await PowerOnData("EjectorForwardDelayTime");
  }

  /// <summary>
  /// 托模退延时时间 (s)
  /// </summary>
  [Method("EJECTOR_BACKWARD_DELAY_TIME", name: "托模退延时时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> EjectorBackwardDelayTime()
  {
    return await PowerOnData("EjectorBackwardDelayTime");
  }
  #endregion

  #endregion
  /// <summary>
  /// 获取数据
  /// </summary>
  /// <param name="identifier"></param>
  /// <returns></returns>
  private async Task<DriverReturnValueModel> PowerOnData(string identifier)
  {
    // 默认返回Double类型
    var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Double };
    try
    {
      // 检查是否连接
      if (IsConnected)
      {
        // 检查是否是数组访问
        if (identifier.Contains("["))
        {
          var arrayName = identifier.Substring(0, identifier.IndexOf("["));
          var indexStr = identifier.Substring(identifier.IndexOf("[") + 1, identifier.Length - identifier.IndexOf("[") - 2);
          var index = int.Parse(indexStr);

          var propertyInfo = typeof(TechMation5521Data).GetProperty(arrayName);
          if (propertyInfo == null)
          {
            ret.ErrorCode = 9999;
            ret.Message = $"属性：{arrayName} 未找到";
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Value = null;
          }
          else
          {
            var array = propertyInfo.GetValue(_techMation5521Data) as Array;
            if (array != null && index < array.Length)
            {
              ret.Value = array.GetValue(index);
              ret.VariableStatus = VariableStatusTypeEnum.Good;
            }
            else
            {
              ret.ErrorCode = 9999;
              ret.Message = $"数组索引越界或非数组属性：{identifier}";
              ret.VariableStatus = VariableStatusTypeEnum.Bad;
              ret.Value = null;
            }
          }
        }
        else
        {
          var propertyInfo = typeof(TechMation5521Data).GetProperty(identifier);
          if (propertyInfo == null)
          {
            ret.ErrorCode = 9999;
            ret.Message = $"属性：{identifier} 未找到";
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Value = null;
          }
          else
          {
            ret.Value = propertyInfo.GetValue(_techMation5521Data);
            ret.VariableStatus = VariableStatusTypeEnum.Good;
          }
        }
      }
      else
      {
        ret.ErrorCode = 9999;
        ret.Message = "设备未连接";
        ret.VariableStatus = VariableStatusTypeEnum.Bad;
        ret.Value = null;
      }
    }
    catch (Exception ex)
    {
      ret.ErrorCode = 9999;
      ret.Message = ex.Message;
      ret.VariableStatus = VariableStatusTypeEnum.Bad;
      ret.Value = null;
    }

    return ret;
  }
}