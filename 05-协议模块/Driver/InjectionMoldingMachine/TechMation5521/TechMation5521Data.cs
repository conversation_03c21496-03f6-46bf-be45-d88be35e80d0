namespace TechMation5521;

/// <summary>
/// TechMation5521注塑机数据类
/// </summary>
public class TechMation5521Data
{
  #region 合模数量数据 (41-43)
  /// <summary>
  /// 合模数量
  /// </summary>
  public int MoldCount { get; set; }
  #endregion

  #region 实时温度数据 (42-43)
  /// <summary>
  /// 顶针位置 (mm)
  /// </summary>
  public float EjectorPosition { get; set; }

  /// <summary>
  /// 螺杆位置 (mm)
  /// </summary>
  public float ScrewPosition { get; set; }

  /// <summary>
  /// 温度1实际值 (℃)
  /// </summary>
  public float Temperature1 { get; set; }

  /// <summary>
  /// 温度2实际值 (℃)
  /// </summary>
  public float Temperature2 { get; set; }

  /// <summary>
  /// 温度3实际值 (℃)
  /// </summary>
  public float Temperature3 { get; set; }

  /// <summary>
  /// 温度4实际值 (℃)
  /// </summary>
  public float Temperature4 { get; set; }

  /// <summary>
  /// 温度5实际值 (℃)
  /// </summary>
  public float Temperature5 { get; set; }

  /// <summary>
  /// 油温 (℃)
  /// </summary>
  public float OilTemperature { get; set; }
  #endregion

  #region SPC数据 (42-45)
  /// <summary>
  /// 上模循环时间 (s)
  /// </summary>
  public float CycleTime { get; set; }

  /// <summary>
  /// 上模注射保压时间 (s)
  /// </summary>
  public float InjectionTime { get; set; }

  /// <summary>
  /// 上模注射时间 (s)
  /// </summary>
  public float InjectionOnlyTime { get; set; }


  /// <summary>
  /// 上模储料时间 (s)
  /// </summary>
  public float ChargingTime { get; set; }

  /// <summary>
  /// 上模射出起点 (mm)
  /// </summary>
  public float InjectionStartPosition { get; set; }

  /// <summary>
  /// 上模保压起点 (mm)
  /// </summary>
  public float PressureHoldStartPosition { get; set; }

  /// <summary>
  /// 上模射出监控 (mm)
  /// </summary>
  public float InjectionMonitorPosition { get; set; }


  /// <summary>
  /// 上模射出终点 (mm)
  /// </summary>
  public float InjectionEndPosition { get; set; }


  /// <summary>
  /// 上模转保压力 (bar)
  /// </summary>
  public int PressureHoldSwitchPressure { get; set; }


  /// <summary>
  /// 上模射出尖压 (bar)
  /// </summary>
  public int InjectionPeakPressure { get; set; }


  /// <summary>
  /// 上模储料尖压 (bar)
  /// </summary>
  public int ChargingPeakPressure { get; set; }
  #endregion

  #region 温度设定数据 (32-34)
  /// <summary>
  /// 温度1设定值 (℃)
  /// </summary>
  public float TemperatureSetting1 { get; set; }

  /// <summary>
  /// 温度2设定值 (℃)
  /// </summary>
  public float TemperatureSetting2 { get; set; }

  /// <summary>
  /// 温度3设定值 (℃)
  /// </summary>
  public float TemperatureSetting3 { get; set; }

  /// <summary>
  /// 温度4设定值 (℃)
  /// </summary>
  public float TemperatureSetting4 { get; set; }

  /// <summary>
  /// 温度5设定值 (℃)
  /// </summary>
  public float TemperatureSetting5 { get; set; }
  #endregion

  #region 设定数据1 (32-33)
  // 储料参数
  /// <summary>
  /// 储料压力1 (bar)
  /// </summary>
  public int ChargingPressure1 { get; set; }

  /// <summary>
  /// 储料背压1 (bar)
  /// </summary>
  public int ChargingBackPressure1 { get; set; }

  /// <summary>
  /// 储料速度1 (rpm)
  /// </summary>
  public float ChargingSpeed1 { get; set; }

  /// <summary>
  /// 储料位置1 (mm)
  /// </summary>
  public float ChargingPosition1 { get; set; }

  /// <summary>
  /// 储料压力2 (bar)
  /// </summary>
  public int ChargingPressure2 { get; set; }

  /// <summary>
  /// 储料背压2 (bar)
  /// </summary>
  public int ChargingBackPressure2 { get; set; }

  /// <summary>
  /// 储料速度2 (rpm)
  /// </summary>
  public float ChargingSpeed2 { get; set; }

  /// <summary>
  /// 储料位置2 (mm)
  /// </summary>
  public float ChargingPosition2 { get; set; }

  /// <summary>
  /// 储料压力3 (bar)
  /// </summary>
  public int ChargingPressure3 { get; set; }

  /// <summary>
  /// 储料背压3 (bar)
  /// </summary>
  public int ChargingBackPressure3 { get; set; }

  /// <summary>
  /// 储料速度3 (rpm)
  /// </summary>
  public float ChargingSpeed3 { get; set; }

  /// <summary>
  /// 储料位置3 (mm)
  /// </summary>
  public float ChargingPosition3 { get; set; }

  /// <summary>
  /// 储料压力4 (bar)
  /// </summary>
  public int ChargingPressure4 { get; set; }

  /// <summary>
  /// 储料背压4 (bar)
  /// </summary>
  public int ChargingBackPressure4 { get; set; }

  /// <summary>
  /// 储料速度4 (rpm)
  /// </summary>
  public float ChargingSpeed4 { get; set; }

  /// <summary>
  /// 储料位置4 (mm)
  /// </summary>
  public float ChargingPosition4 { get; set; }

  /// <summary>
  /// 储料压力5 (bar)
  /// </summary>
  public int ChargingPressure5 { get; set; }

  /// <summary>
  /// 储料背压5 (bar)
  /// </summary>
  public int ChargingBackPressure5 { get; set; }

  /// <summary>
  /// 储料速度5 (rpm)
  /// </summary>
  public float ChargingSpeed5 { get; set; }

  /// <summary>
  /// 储料位置5 (mm)
  /// </summary>
  public float ChargingPosition5 { get; set; }

  // 合模参数
  /// <summary>
  /// 合模压力1 (bar)
  /// </summary>
  public int MoldClampPressure1 { get; set; }

  /// <summary>
  /// 合模速度1 (%)
  /// </summary>
  public float MoldClampSpeed1 { get; set; }

  /// <summary>
  /// 合模位置1 (mm)
  /// </summary>
  public float MoldClampPosition1 { get; set; }

  /// <summary>
  /// 合模压力2 (bar)
  /// </summary>
  public int MoldClampPressure2 { get; set; }

  /// <summary>
  /// 合模速度2 (%)
  /// </summary>
  public float MoldClampSpeed2 { get; set; }

  /// <summary>
  /// 合模位置2 (mm)
  /// </summary>
  public float MoldClampPosition2 { get; set; }

  /// <summary>
  /// 合模压力3 (bar)
  /// </summary>
  public int MoldClampPressure3 { get; set; }

  /// <summary>
  /// 合模速度3 (%)
  /// </summary>
  public float MoldClampSpeed3 { get; set; }

  /// <summary>
  /// 合模位置3 (mm)
  /// </summary>
  public float MoldClampPosition3 { get; set; }

  /// <summary>
  /// 合模压力4 (bar)
  /// </summary>
  public int MoldClampPressure4 { get; set; }

  /// <summary>
  /// 合模速度4 (%)
  /// </summary>
  public float MoldClampSpeed4 { get; set; }

  /// <summary>
  /// 合模位置4 (mm)
  /// </summary>
  public float MoldClampPosition4 { get; set; }

  /// <summary>
  /// 合模压力5 (bar)
  /// </summary>
  public int MoldClampPressure5 { get; set; }

  /// <summary>
  /// 合模速度5 (%)
  /// </summary>
  public float MoldClampSpeed5 { get; set; }

  /// <summary>
  /// 合模位置5 (mm)
  /// </summary>
  public float MoldClampPosition5 { get; set; }

  /// <summary>
  /// 储料储前冷却 (℃)
  /// </summary>
  public float ChargingFrontCooling { get; set; }

  /// <summary>
  /// 储料储后冷却 (℃)
  /// </summary>
  public float ChargingBackCooling { get; set; }

  /// <summary>
  /// 储料再次储料计时 (s)
  /// </summary>
  public float ChargingRechargeTime { get; set; }

  // 保压参数
  /// <summary>
  /// 保压压力1 (bar)
  /// </summary>
  public int PressureHoldPressure1 { get; set; }

  /// <summary>
  /// 保压速度1 (%)
  /// </summary>
  public float PressureHoldSpeed1 { get; set; }

  /// <summary>
  /// 保压时间1 (s)
  /// </summary>
  public float PressureHoldTime1 { get; set; }

  /// <summary>
  /// 保压压力2 (bar)
  /// </summary>
  public int PressureHoldPressure2 { get; set; }

  /// <summary>
  /// 保压速度2 (%)
  /// </summary>
  public float PressureHoldSpeed2 { get; set; }

  /// <summary>
  /// 保压时间2 (s)
  /// </summary>
  public float PressureHoldTime2 { get; set; }

  /// <summary>
  /// 保压压力3 (bar)
  /// </summary>
  public int PressureHoldPressure3 { get; set; }

  /// <summary>
  /// 保压速度3 (%)
  /// </summary>
  public float PressureHoldSpeed3 { get; set; }

  /// <summary>
  /// 保压时间3 (s)
  /// </summary>
  public float PressureHoldTime3 { get; set; }

  /// <summary>
  /// 保压压力4 (bar)
  /// </summary>
  public int PressureHoldPressure4 { get; set; }

  /// <summary>
  /// 保压速度4 (%)
  /// </summary>
  public float PressureHoldSpeed4 { get; set; }

  /// <summary>
  /// 保压时间4 (s)
  /// </summary>
  public float PressureHoldTime4 { get; set; }

  /// <summary>
  /// 保压压力5 (bar)
  /// </summary>
  public int PressureHoldPressure5 { get; set; }

  /// <summary>
  /// 保压速度5 (%)
  /// </summary>
  public float PressureHoldSpeed5 { get; set; }

  /// <summary>
  /// 保压时间5 (s)
  /// </summary>
  public float PressureHoldTime5 { get; set; }

  /// <summary>
  /// 保压压力6 (bar)
  /// </summary>
  public int PressureHoldPressure6 { get; set; }

  /// <summary>
  /// 保压速度6 (%)
  /// </summary>
  public float PressureHoldSpeed6 { get; set; }

  /// <summary>
  /// 保压时间6 (s)
  /// </summary>
  public float PressureHoldTime6 { get; set; }
  #endregion

  #region 设定数据2 (33-33)
  // 射出参数
  /// <summary>
  /// 射出压力1 (bar)
  /// </summary>
  public int InjectionPressure1 { get; set; }

  /// <summary>
  /// 射出速度1 (%)
  /// </summary>
  public float InjectionSpeed1 { get; set; }

  /// <summary>
  /// 射出位置1 (mm)
  /// </summary>
  public float InjectionPosition1 { get; set; }

  /// <summary>
  /// 射出压力2 (bar)
  /// </summary>
  public int InjectionPressure2 { get; set; }

  /// <summary>
  /// 射出速度2 (%)
  /// </summary>
  public float InjectionSpeed2 { get; set; }

  /// <summary>
  /// 射出位置2 (mm)
  /// </summary>
  public float InjectionPosition2 { get; set; }

  /// <summary>
  /// 射出压力3 (bar)
  /// </summary>
  public int InjectionPressure3 { get; set; }

  /// <summary>
  /// 射出速度3 (%)
  /// </summary>
  public float InjectionSpeed3 { get; set; }

  /// <summary>
  /// 射出位置3 (mm)
  /// </summary>
  public float InjectionPosition3 { get; set; }

  /// <summary>
  /// 射出压力4 (bar)
  /// </summary>
  public int InjectionPressure4 { get; set; }

  /// <summary>
  /// 射出速度4 (%)
  /// </summary>
  public float InjectionSpeed4 { get; set; }

  /// <summary>
  /// 射出位置4 (mm)
  /// </summary>
  public float InjectionPosition4 { get; set; }

  /// <summary>
  /// 射出压力5 (bar)
  /// </summary>
  public int InjectionPressure5 { get; set; }

  /// <summary>
  /// 射出速度5 (%)
  /// </summary>
  public float InjectionSpeed5 { get; set; }

  /// <summary>
  /// 射出位置5 (mm)
  /// </summary>
  public float InjectionPosition5 { get; set; }

  /// <summary>
  /// 射出压力6 (bar)
  /// </summary>
  public int InjectionPressure6 { get; set; }

  /// <summary>
  /// 射出速度6 (%)
  /// </summary>
  public float InjectionSpeed6 { get; set; }

  /// <summary>
  /// 射出位置6 (mm)
  /// </summary>
  public float InjectionPosition6 { get; set; }

  // 座台参数
  /// <summary>
  /// 座台进压力1 (bar)
  /// </summary>
  public int SeatForwardPressure1 { get; set; }

  /// <summary>
  /// 座台进速度1 (%)
  /// </summary>
  public float SeatForwardSpeed1 { get; set; }

  /// <summary>
  /// 座台进压力2 (bar)
  /// </summary>
  public int SeatForwardPressure2 { get; set; }

  /// <summary>
  /// 座台进速度2 (%)
  /// </summary>
  public float SeatForwardSpeed2 { get; set; }

  /// <summary>
  /// 座台进时间 (s)
  /// </summary>
  public float SeatForwardTime { get; set; }

  /// <summary>
  /// 座台退压力1 (bar)
  /// </summary>
  public int SeatBackPressure1 { get; set; }

  /// <summary>
  /// 座台退速度1 (%)
  /// </summary>
  public float SeatBackSpeed1 { get; set; }

  /// <summary>
  /// 座台退时间 (s)
  /// </summary>
  public float SeatBackTime { get; set; }

  /// <summary>
  /// 座台退延迟时间 (s)
  /// </summary>
  public float SeatBackDelayTime { get; set; }

  // 开模参数
  /// <summary>
  /// 开模压力1 (bar)
  /// </summary>
  public int MoldOpenPressure1 { get; set; }

  /// <summary>
  /// 开模速度1 (%)
  /// </summary>
  public float MoldOpenSpeed1 { get; set; }

  /// <summary>
  /// 开模位置1 (mm)
  /// </summary>
  public float MoldOpenPosition1 { get; set; }

  /// <summary>
  /// 开模压力2 (bar)
  /// </summary>
  public int MoldOpenPressure2 { get; set; }

  /// <summary>
  /// 开模速度2 (%)
  /// </summary>
  public float MoldOpenSpeed2 { get; set; }

  /// <summary>
  /// 开模位置2 (mm)
  /// </summary>
  public float MoldOpenPosition2 { get; set; }

  /// <summary>
  /// 开模压力3 (bar)
  /// </summary>
  public int MoldOpenPressure3 { get; set; }

  /// <summary>
  /// 开模速度3 (%)
  /// </summary>
  public float MoldOpenSpeed3 { get; set; }

  /// <summary>
  /// 开模位置3 (mm)
  /// </summary>
  public float MoldOpenPosition3 { get; set; }

  /// <summary>
  /// 开模压力4 (bar)
  /// </summary>
  public int MoldOpenPressure4 { get; set; }

  /// <summary>
  /// 开模速度4 (%)
  /// </summary>
  public float MoldOpenSpeed4 { get; set; }

  /// <summary>
  /// 开模位置4 (mm)
  /// </summary>
  public float MoldOpenPosition4 { get; set; }

  /// <summary>
  /// 开模压力5 (bar)
  /// </summary>
  public int MoldOpenPressure5 { get; set; }

  /// <summary>
  /// 开模速度5 (%)
  /// </summary>
  public float MoldOpenSpeed5 { get; set; }

  /// <summary>
  /// 开模位置5 (mm)
  /// </summary>
  public float MoldOpenPosition5 { get; set; }
  #endregion

  #region 托模参数
  /// <summary>
  /// 托模进压力1 (bar)
  /// </summary>
  public int EjectorForwardPressure1 { get; set; }

  /// <summary>
  /// 托模进速度1 (%)
  /// </summary>
  public float EjectorForwardSpeed1 { get; set; }

  /// <summary>
  /// 托模进位置1 (mm)
  /// </summary>
  public float EjectorForwardPos1 { get; set; }

  /// <summary>
  /// 托模进压力2 (bar)
  /// </summary>
  public int EjectorForwardPressure2 { get; set; }

  /// <summary>
  /// 托模进速度2 (%)
  /// </summary>
  public float EjectorForwardSpeed2 { get; set; }

  /// <summary>
  /// 托模进位置2 (mm)
  /// </summary>
  public float EjectorForwardPos2 { get; set; }

  /// <summary>
  /// 托模退压力1 (bar)
  /// </summary>
  public int EjectorBackwardPressure1 { get; set; }

  /// <summary>
  /// 托模退速度1 (%)
  /// </summary>
  public float EjectorBackwardSpeed1 { get; set; }

  /// <summary>
  /// 托模退位置1 (mm)
  /// </summary>
  public float EjectorBackwardPos1 { get; set; }

  /// <summary>
  /// 托模退压力2 (bar)
  /// </summary>
  public int EjectorBackwardPressure2 { get; set; }

  /// <summary>
  /// 托模退速度2 (%)
  /// </summary>
  public float EjectorBackwardSpeed2 { get; set; }

  /// <summary>
  /// 托模退位置2 (mm)
  /// </summary>
  public float EjectorBackwardPos2 { get; set; }

  /// <summary>
  /// 托模方式
  /// </summary>
  public int EjectorMode { get; set; }

  /// <summary>
  /// 托模次数
  /// </summary>
  public int EjectorCount { get; set; }

  /// <summary>
  /// 托模进延时时间 (s)
  /// </summary>
  public float EjectorForwardDelayTime { get; set; }

  /// <summary>
  /// 托模退延时时间 (s)
  /// </summary>
  public float EjectorBackwardDelayTime { get; set; }
  #endregion

}