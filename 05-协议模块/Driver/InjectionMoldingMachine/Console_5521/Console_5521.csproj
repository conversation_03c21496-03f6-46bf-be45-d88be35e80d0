<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="PacketDotNet" Version="1.4.7" />
      <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="7.0.0" />
      <PackageReference Include="SharpPcap" Version="6.3.3" />
    </ItemGroup>

    <ItemGroup>
      <None Update="appsettings.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>
