// using System.Text;
// using System.Text.Json;
// using InjectionMoldingMachine.Console_5521;
// using PacketDotNet;
// using SharpPcap;
// using Microsoft.Extensions.Configuration;
//
// /// <summary>
// /// 5521注塑机数据采集程序
// /// </summary>
// class Program
// {
//   /// <summary>
//   /// 配置对象
//   /// </summary>
//   private static IConfiguration _configuration;
//
//   /// <summary>
//   /// 设备IP地址
//   /// </summary>
//   private static string _ipAddress;
//
//   /// <summary>
//   /// 网卡名称
//   /// </summary>
//   private static string _interfaceName;
//
//   /// <summary>
//   /// 端口号
//   /// </summary>
//   private static int _port;
//
//   /// <summary>
//   /// 日志文件路径
//   /// </summary>
//   private static string _logFilePath;
//
//   /// <summary>
//   /// 日志文件锁对象
//   /// </summary>
//   private static readonly object _logLock = new();
//
//   static async Task Main(string[] args)
//   {
//     ICaptureDevice device = null;
//     try
//     {
//       Console.WriteLine("=================================================");
//       Console.WriteLine("           5521注塑机数据采集程序启动            ");
//       Console.WriteLine("=================================================");
//       Console.WriteLine($"启动时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
//       Console.WriteLine();
//
//       // 添加自动结束定时器
//       var autoStopTimer = new System.Timers.Timer(120 * 1000); // 60秒 = 1分钟
//       autoStopTimer.Elapsed += (s, e) =>
//       {
//         Console.WriteLine("\n=================================================");
//         Console.WriteLine("              程序运行满1分钟，自动结束           ");
//         Console.WriteLine("=================================================");
//         Environment.Exit(0); // 正常退出程序
//       };
//       autoStopTimer.AutoReset = false; // 只触发一次
//       autoStopTimer.Start();
//       Console.WriteLine("程序将在1分钟后自动结束");
//       Console.WriteLine();
//
//       // 加载配置文件
//       Console.WriteLine("正在加载配置文件...");
//       _configuration = new ConfigurationBuilder()
//           .SetBasePath(Directory.GetCurrentDirectory())
//           .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
//           .Build();
//
//       // 从配置文件读取设置
//       var deviceSettings = _configuration.GetSection("DeviceSettings");
//       _ipAddress = deviceSettings["IpAddress"];
//       _interfaceName = deviceSettings["InterfaceName"];
//       _port = int.Parse(deviceSettings["Port"]);
//
//       WriteLog("=================================================");
//       WriteLog("           5521注塑机数据采集程序启动            ");
//       WriteLog("=================================================");
//       WriteLog($"启动时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
//       WriteLog("");
//       
//       // 获取网卡设备
//       Console.WriteLine("正在扫描网卡设备...");
//       var devices = CaptureDeviceList.Instance;
//       Console.WriteLine("可用网卡列表:");
//       foreach (var devicec in devices)
//       {
//         Console.WriteLine($"├─{devicec.Name}");
//         Console.WriteLine($"│  └─{devicec.Description}");
//       }
//       Console.WriteLine();
//
//       Console.WriteLine($"正在查找目标网卡: {_interfaceName}");
//       device = devices.FirstOrDefault(d => (d.Description != null &&
//                                                 d.Description.Contains(_interfaceName, StringComparison.OrdinalIgnoreCase)) ||
//                                                  (d.Name != null && d.Name.Contains(_interfaceName, StringComparison.OrdinalIgnoreCase)));
//       if (device == null)
//       {
//         Console.WriteLine($"[错误] 未找到网卡: {_interfaceName}");
//         return;
//       }
//       Console.WriteLine($"已找到目标网卡");
//       Console.WriteLine();
//
//       // 打开设备
//       Console.WriteLine("正在打开网卡设备...");
//       device.Open(new DeviceConfiguration
//       {
//         Mode = DeviceModes.Promiscuous,
//         BufferSize = 4096
//       });
//       Console.WriteLine($"网卡已打开: {_interfaceName}");
//
//       // 设置过滤器
//       string filter = $"host {_ipAddress} and (tcp port {_port} or udp port {_port})";
//       device.Filter = filter;
//       Console.WriteLine($"已设置数据包过滤器: {filter}");
//
//       // 注册数据包处理事件
//       device.OnPacketArrival += Device_OnPacketArrival;
//       Console.WriteLine("已注册数据包处理事件");
//       Console.WriteLine();
//
//       // 开始捕获
//       Console.WriteLine("正在启动数据包捕获...");
//       device.StartCapture();
//       Console.WriteLine("数据包捕获已启动");
//       Console.WriteLine();
//       Console.WriteLine("=================================================");
//       Console.WriteLine("        程序运行中, 1分钟后自动退出              ");
//       Console.WriteLine("=================================================");
//
//       // 等待程序结束
//       await Task.Delay(121000); // 稍微多等1秒，确保自动结束定时器能触发
//
//       Console.WriteLine();
//       Console.WriteLine("正在停止数据采集...");
//       device.StopCapture();
//       device.Close();
//  
//     }
//     catch (Exception ex)
//     {
//       WriteLog("");
//       WriteLog("[严重错误] 程序发生异常:");
//       WriteLog($"├─异常类型: {ex.GetType().Name}");
//       WriteLog($"├─异常信息: {ex.Message}");
//       WriteLog($"└─堆栈跟踪: {ex.StackTrace}");
//     }
//     finally
//     {
//       if (device != null)
//       {
//         try
//         {
//           Console.WriteLine("正在清理资源...");
//           device.StopCapture();
//           device.Close();
//           Console.WriteLine("网卡设备已关闭");
//         }
//         catch (Exception ex)
//         {
//           Console.WriteLine($"[警告] 关闭设备时发生异常: {ex.Message}");
//         }
//       }
//       Console.WriteLine();
//       WriteLog("=================================================");
//       WriteLog("                  程序已安全退出                 ");
//       WriteLog("=================================================");
//     }
//   }
//
//   /// <summary>
//   /// 设备数据包到达事件
//   /// </summary>
//   /// <param name="sender"></param>
//   /// <param name="e"></param>
//   private static void Device_OnPacketArrival(object sender, PacketCapture e)
//   {
//     try
//     {
//       // 添加短暂休眠，减少CPU占用
//       Thread.Sleep(10);  // 改为10毫秒
//
//       var rawPacket = e.GetPacket();
//       if (rawPacket == null || rawPacket.Data == null)
//       {
//         return;
//       }
//
//       // 解析数据包
//       var packet = Packet.ParsePacket(rawPacket.LinkLayerType, rawPacket.Data);
//       if (packet == null)
//       {
//         return;
//       }
//
//       // 提取UDP和IP层数据
//       var udpPacket = packet.Extract<UdpPacket>();
//       var ipPacket = packet.Extract<IPPacket>();
//
//       // 只处理包含有效负载的UDP数据包
//       if (udpPacket != null && ipPacket != null && udpPacket.PayloadData != null && udpPacket.PayloadData.Length > 0)
//       {
//         Console.WriteLine("收到数据啦！！！");
//       }
//     }
//     catch (Exception ex)
//     {
//       WriteLog($"处理数据包异常: {ex.Message}");
//     }
//   }
//
//   /// <summary>
//   /// 写入日志到文件
//   /// </summary>
//   /// <param name="message">日志消息</param>
//   private static void WriteLog(string message)
//   {
//     try
//     {
//       // 输出到控制台
//       Console.WriteLine(message);
//     }
//     catch (Exception ex)
//     {
//       Console.WriteLine($"写入日志异常: {ex.Message}");
//     }
//   }
// }