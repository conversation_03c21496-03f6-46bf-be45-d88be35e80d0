using System;
using System.IO;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Timers;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Feng.Common.Util;
using Furion.JsonSerialization;
using Furion.Logging;
using HslCommunication;
using Timer = System.Timers.Timer;

namespace PORCHESON_Ps660Bm;

/* 宝捷信-Ps660Bm
 * 更新日志
 * 版本:v1.1.0
 *  修复串口只收一路数据问题
 * 版本:v1.1.1
 *  注塑机协议根据要求强制改为根据命名写入某个固定文件 - 2024-05-21 -wangj
 */
[DriverSupported("PORCHESON_Ps660Bm")]
[DriverInfo("PORCHESON_Ps660Bm", "V1.1.0", "注塑机")]
public class PORCHESON_Ps660Bm : BaseDeviceProtocolCollector, IDriver
{
    [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置", Display = false)]
    public override int WriteInterval { get; set; } = 120;

    [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    [ConfigParameter("连接方式",  order:1)]
    public DriverConnectTypeEnum ConnectType { get; set; } = DriverConnectTypeEnum.SerialPort;

    #region 网口配置

    [ConfigParameter("IP地址", Display = false, DisplayExpress = "{\"ConnectType\": [\"1\"]}")]
    public string IpAddress { get; set; } = "127.0.0.1";

    [ConfigParameter("端口号", Display = false, DisplayExpress = "{\"ConnectType\": [\"1\"]}")]
    public int Port { get; set; } = 8001;

    [ConfigParameter("端口号2", Display = false, DisplayExpress = "{\"ConnectType\": [\"1\"]}")]
    public int Port2 { get; set; } = 8002;

    #endregion 网口配置

    #region 串口配置

    [ConfigParameter("串口号", Display = false, Remark = "", DisplayExpress = "{\"ConnectType\": [\"2\"]}")]
    public SerialNumberEnum SerialNumber { get; set; } = SerialNumberEnum.ttyS3;

    [ConfigParameter("串口号2", Display = false, Remark = "", DisplayExpress = "{\"ConnectType\": [\"2\"]}")]
    public SerialNumberEnum SerialNumber2 { get; set; } = SerialNumberEnum.ttyS4;

    #endregion 串口配置

    private ClientHandlerPs660Bm _client1;
    private ClientHandlerPs660Bm _client2;
    private PorchesonData _porCheSonData = new();
    private readonly Ping _ping = new();
    private bool _pingIp = true;

    public PORCHESON_Ps660Bm(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
        try
        {
            // 读取缓存内容
            ReadContent();
            // 创建一个 Timer 实例，设置定时器间隔为 10 秒
            _timer = new Timer(1000 * 60);
            _timer.Elapsed += TimerElapsed;

            // 启动定时器
            _timer.Start();
        }
        catch (Exception e)
        {
            Log.Error($"【宝捷信BM】 初始化异常:{e.Message}");
        }
    }

    public override bool IsConnected => _client1 != null && _client2 != null && (_client1.IsConnected || _client2.IsConnected) && _pingIp;

    public DeviceConnectDto Connect()
    {
        try
        {
            _client1 = new ClientHandlerPs660Bm(_porCheSonData, DriverInfo);
            _client2 = new ClientHandlerPs660Bm(_porCheSonData, DriverInfo);
            if (ConnectType == DriverConnectTypeEnum.NetWork)
            {
                _client1.Connect(IpAddress, Port);
                _client2.Connect(IpAddress, Port2);
            }
            else
            {
                _client1.Connect(EnumUtil.GetEnumDesc(SerialNumber));
                _client2.Connect(EnumUtil.GetEnumDesc(SerialNumber2));
            }

            IsConnected = _client1.IsConnected && _client2.IsConnected;
            OperateResult.IsSuccess = IsConnected;
            OperateResult.Message = IsConnected ? "连接成功" : "连接失败";
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }

    #region 采集点位

    #region 上电数据

    /// <summary>
    ///     快速合模压力
    /// </summary>
    /// <returns></returns>
    [Method("QuickCombinePressure", name: "快速合模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> QuickCombinePressure()
    {
        return await PowerOnData("QuickCombinePressure");
    }

    /// <summary>
    ///     快速合模流量
    /// </summary>
    /// <returns></returns>
    [Method("QuickCombineFlow", name: "快速合模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> QuickCombineFlow()
    {
        return await PowerOnData("QuickCombineFlow");
    }

    /// <summary>
    ///     低压合模压力
    /// </summary>
    /// <returns></returns>
    [Method("LowCombinePressure", name: "低压合模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LowCombinePressure()
    {
        return await PowerOnData("LowCombinePressure");
    }

    /// <summary>
    ///     低压合模流量
    /// </summary>
    /// <returns></returns>
    [Method("LowCombineFlow", name: "低压合模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LowCombineFlow()
    {
        return await PowerOnData("LowCombineFlow");
    }

    /// <summary>
    ///     高压合模压力
    /// </summary>
    /// <returns></returns>
    [Method("HighCombinePressure", name: "高压合模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HighCombinePressure()
    {
        return await PowerOnData("HighCombinePressure");
    }

    /// <summary>
    ///     高压合模流量
    /// </summary>
    /// <returns></returns>
    [Method("HighCombineFlow", name: "高压合模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HighCombineFlow()
    {
        return await PowerOnData("HighCombineFlow");
    }

    /// <summary>
    ///     高压合模时间
    /// </summary>
    /// <returns></returns>
    [Method("HighCombineTime", name: "高压合模时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HighCombineTime()
    {
        return await PowerOnData("HighCombineTime");
    }

    /// <summary>
    ///     低速开模压力
    /// </summary>
    /// <returns></returns>
    [Method("LowSpeedOpenPressure", name: "低速开模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LowSpeedOpenPressure()
    {
        return await PowerOnData("LowSpeedOpenPressure");
    }

    /// <summary>
    ///     低速开模流量
    /// </summary>
    /// <returns></returns>
    [Method("LowSpeedOpenFlow", name: "低速开模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LowSpeedOpenFlow()
    {
        return await PowerOnData("LowSpeedOpenFlow");
    }

    /// <summary>
    ///     卸荷时间
    /// </summary>
    /// <returns></returns>
    [Method("UnloadTime", name: "卸荷时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> UnloadTime()
    {
        return await PowerOnData("UnloadTime");
    }

    /// <summary>
    ///     快速开模压力
    /// </summary>
    /// <returns></returns>
    [Method("FastMoldPressure", name: "快速开模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> FastMoldPressure()
    {
        return await PowerOnData("FastMoldPressure");
    }

    /// <summary>
    ///     快速开模流量
    /// </summary>
    /// <returns></returns>
    [Method("FastMoldFlow", name: "快速开模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> FastMoldFlow()
    {
        return await PowerOnData("FastMoldFlow");
    }

    /// <summary>
    ///     慢速开模时间
    /// </summary>
    /// <returns></returns>
    [Method("SlowOpenTime", name: "慢速开模时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> SlowOpenTime()
    {
        return await PowerOnData("SlowOpenTime");
    }

    /// <summary>
    ///     低速开模时间
    /// </summary>
    /// <returns></returns>
    [Method("LowSpeedOpenTime", name: "低速开模时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LowSpeedOpenTime()
    {
        return await PowerOnData("LowSpeedOpenTime");
    }

    /// <summary>
    ///     低压保护时间
    /// </summary>
    /// <returns></returns>
    [Method("LowPressureProtectionTime", name: "低压保护时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LowPressureProtectionTime()
    {
        return await PowerOnData("LowPressureProtectionTime");
    }

    /// <summary>
    ///     开合模限时
    /// </summary>
    /// <returns></returns>
    [Method("MoldClosingTimeLimit", name: "开合模限时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldClosingTimeLimit()
    {
        return await PowerOnData("MoldClosingTimeLimit");
    }

    /// <summary>
    ///     慢速开模时间
    /// </summary>
    /// <returns></returns>
    [Method("SlowMoldOpeningTime", name: "慢速开模时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> SlowMoldOpeningTime()
    {
        return await PowerOnData("SlowMoldOpeningTime");
    }

    /// <summary>
    ///     低速开模时间
    /// </summary>
    /// <returns></returns>
    [Method("LowSpeedMoldOpeningTime", name: "低速开模时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LowSpeedMoldOpeningTime()
    {
        return await PowerOnData("LowSpeedMoldOpeningTime");
    }

    /// <summary>
    ///     射出压力1
    /// </summary>
    /// <returns></returns>
    [Method("InjectionPressure1", name: "射出压力1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure1()
    {
        return await PowerOnData("InjectionPressure1");
    }

    /// <summary>
    ///     射出流量1
    /// </summary>
    /// <returns></returns>
    [Method("InjectionFlow1", name: "射出流量1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionFlow1()
    {
        return await PowerOnData("InjectionFlow1");
    }

    /// <summary>
    ///     射出时间1
    /// </summary>
    /// <returns></returns>
    [Method("InjectionTime1", name: "射出时间1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionTime1()
    {
        return await PowerOnData("InjectionTime1");
    }

    /// <summary>
    ///     射出压力2
    /// </summary>
    /// <returns></returns>
    [Method("InjectionPressure2", name: "射出压力2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure2()
    {
        return await PowerOnData("InjectionPressure2");
    }

    /// <summary>
    ///     射出流量2
    /// </summary>
    /// <returns></returns>
    [Method("InjectionFlow2", name: "射出流量2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionFlow2()
    {
        return await PowerOnData("InjectionFlow2");
    }

    /// <summary>
    ///     射出时间2
    /// </summary>
    /// <returns></returns>
    [Method("InjectionTime2", name: "射出时间2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionTime2()
    {
        return await PowerOnData("InjectionTime2");
    }

    /// <summary>
    ///     射出压力3
    /// </summary>
    /// <returns></returns>
    [Method("InjectionPressure3", name: "射出压力3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure3()
    {
        return await PowerOnData("InjectionPressure3");
    }

    /// <summary>
    ///     射出流量3
    /// </summary>
    /// <returns></returns>
    [Method("InjectionFlow3", name: "射出流量3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionFlow3()
    {
        return await PowerOnData("InjectionFlow3");
    }

    /// <summary>
    ///     射出时间3
    /// </summary>
    /// <returns></returns>
    [Method("InjectionTime3", name: "射出时间3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionTime3()
    {
        return await PowerOnData("InjectionTime3");
    }

    /// <summary>
    ///     保压压力3
    /// </summary>
    /// <returns></returns>
    [Method("HoldingPressure3", name: "保压压力3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingPressure3()
    {
        return await PowerOnData("HoldingPressure3");
    }

    /// <summary>
    ///     保压流量3
    /// </summary>
    /// <returns></returns>
    [Method("HoldingFlow3", name: "保压流量3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingFlow3()
    {
        return await PowerOnData("HoldingFlow3");
    }

    /// <summary>
    ///     保压时间3
    /// </summary>
    /// <returns></returns>
    [Method("HoldingTime3", name: "保压时间3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingTime3()
    {
        return await PowerOnData("HoldingTime3");
    }

    /// <summary>
    ///     射出总时
    /// </summary>
    /// <returns></returns>
    [Method("TotalInjectionTime", name: "射出总时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TotalInjectionTime()
    {
        return await PowerOnData("TotalInjectionTime");
    }

    /// <summary>
    ///     射出监测点
    /// </summary>
    /// <returns></returns>
    [Method("InjectionMonitoringPoint", name: "射出监测点", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionMonitoringPoint()
    {
        return await PowerOnData("InjectionMonitoringPoint");
    }

    /// <summary>
    ///     储料后射退压力
    /// </summary>
    /// <returns></returns>
    [Method("PostShootingBackPressure", name: "储料后射退压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PostShootingBackPressure()
    {
        return await PowerOnData("PostShootingBackPressure");
    }

    /// <summary>
    ///     储料后射退流量
    /// </summary>
    /// <returns></returns>
    [Method("PostShootingBackFlow", name: "储料后射退流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PostShootingBackFlow()
    {
        return await PowerOnData("PostShootingBackFlow");
    }

    /// <summary>
    ///     储料后射退时间
    /// </summary>
    /// <returns></returns>
    [Method("PostShootingBackTime", name: "储料后射退时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PostShootingBackTime()
    {
        return await PowerOnData("PostShootingBackTime");
    }

    /// <summary>
    ///     储料压力
    /// </summary>
    /// <returns></returns>
    [Method("MaterialStoragePressure", name: "储料压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialStoragePressure()
    {
        return await PowerOnData("MaterialStoragePressure");
    }

    /// <summary>
    ///     储料流量
    /// </summary>
    /// <returns></returns>
    [Method("MaterialStorageFlow", name: "储料流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialStorageFlow()
    {
        return await PowerOnData("MaterialStorageFlow");
    }

    /// <summary>
    ///     储料前射退压力
    /// </summary>
    /// <returns></returns>
    [Method("PreShootingBackPressure", name: "储料前射退压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PreShootingBackPressure()
    {
        return await PowerOnData("PreShootingBackPressure");
    }

    /// <summary>
    ///     储料前射退流量
    /// </summary>
    /// <returns></returns>
    [Method("PreShootingBackFlow", name: "储料前射退流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PreShootingBackFlow()
    {
        return await PowerOnData("PreShootingBackFlow");
    }

    /// <summary>
    ///     储料前射退时间
    /// </summary>
    /// <returns></returns>
    [Method("PreShootingBackTime", name: "储料前射退时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PreShootingBackTime()
    {
        return await PowerOnData("PreShootingBackTime");
    }

    /// <summary>
    ///     储料限时
    /// </summary>
    /// <returns></returns>
    [Method("MaterialStorageTimeLimit", name: "储料限时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialStorageTimeLimit()
    {
        return await PowerOnData("MaterialStorageTimeLimit");
    }

    /// <summary>
    ///     先冷却时间
    /// </summary>
    /// <returns></returns>
    [Method("PreCoolingTime", name: "先冷却时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PreCoolingTime()
    {
        return await PowerOnData("PreCoolingTime");
    }

    /// <summary>
    ///     后冷却时间
    /// </summary>
    /// <returns></returns>
    [Method("PostCoolingTime", name: "后冷却时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PostCoolingTime()
    {
        return await PowerOnData("PostCoolingTime");
    }

    /// <summary>
    ///     清料射退压力
    /// </summary>
    /// <returns></returns>
    [Method("PurgeBackPressure", name: "清料射退压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PurgeBackPressure()
    {
        return await PowerOnData("PurgeBackPressure");
    }

    /// <summary>
    ///     清料射退流量
    /// </summary>
    /// <returns></returns>
    [Method("PurgeBackFlow", name: "清料射退流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PurgeBackFlow()
    {
        return await PowerOnData("PurgeBackFlow");
    }

    /// <summary>
    ///     清料射退时间
    /// </summary>
    /// <returns></returns>
    [Method("PurgeBackTime", name: "清料射退时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PurgeBackTime()
    {
        return await PowerOnData("PurgeBackTime");
    }

    /// <summary>
    ///     清料压力
    /// </summary>
    /// <returns></returns>
    [Method("PurgePressure", name: "清料压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PurgePressure()
    {
        return await PowerOnData("PurgePressure");
    }

    /// <summary>
    ///     清料流量
    /// </summary>
    /// <returns></returns>
    [Method("PurgeFlow", name: "清料流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PurgeFlow()
    {
        return await PowerOnData("PurgeFlow");
    }

    /// <summary>
    ///     清料时间
    /// </summary>
    /// <returns></returns>
    [Method("PurgeTime", name: "清料时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PurgeTime()
    {
        return await PowerOnData("PurgeTime");
    }

    /// <summary>
    ///     清料射出压力
    /// </summary>
    /// <returns></returns>
    [Method("PurgeInjectionPressure", name: "清料射出压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PurgeInjectionPressure()
    {
        return await PowerOnData("PurgeInjectionPressure");
    }

    /// <summary>
    ///     清料射出流量
    /// </summary>
    /// <returns></returns>
    [Method("PurgeInjectionFlow", name: "清料射出流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PurgeInjectionFlow()
    {
        return await PowerOnData("PurgeInjectionFlow");
    }

    /// <summary>
    ///     清料射出时间
    /// </summary>
    /// <returns></returns>
    [Method("PurgeInjectionTime", name: "清料射出时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PurgeInjectionTime()
    {
        return await PowerOnData("PurgeInjectionTime");
    }

    /// <summary>
    ///     清料次数
    /// </summary>
    /// <returns></returns>
    [Method("PurgeCount", name: "清料次数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> PurgeCount()
    {
        return await PowerOnData("PurgeCount");
    }


    /// <summary>
    ///     上电数据
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> PowerOnData(string identifier)
    {
        DriverReturnValueModel ret = new() {DataType = DataTypeEnum.Double};
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    case "QuickCombinePressure":
                        ret.Value = _porCheSonData.QuickCombinePressure;
                        break;
                    case "QuickCombineFlow":
                        ret.Value = _porCheSonData.QuickCombineFlow;
                        break;
                    case "LowCombinePressure":
                        ret.Value = _porCheSonData.LowCombinePressure;
                        break;
                    case "LowCombineFlow":
                        ret.Value = _porCheSonData.LowCombineFlow;
                        break;
                    case "HighCombinePressure":
                        ret.Value = _porCheSonData.HighCombinePressure;
                        break;
                    case "HighCombineFlow":
                        ret.Value = _porCheSonData.HighCombineFlow;
                        break;
                    case "HighCombineTime":
                        ret.Value = _porCheSonData.HighCombineTime;
                        break;
                    case "LowSpeedOpenPressure":
                        ret.Value = _porCheSonData.LowSpeedOpenPressure;
                        break;
                    case "LowSpeedOpenFlow":
                        ret.Value = _porCheSonData.LowSpeedOpenFlow;
                        break;
                    case "UnloadTime":
                        ret.Value = _porCheSonData.UnloadTime;
                        break;
                    case "FastMoldPressure":
                        ret.Value = _porCheSonData.FastMoldPressure;
                        break;
                    case "FastMoldFlow":
                        ret.Value = _porCheSonData.FastMoldFlow;
                        break;
                    case "SlowOpenTime":
                        ret.Value = _porCheSonData.SlowOpenTime;
                        break;
                    case "LowSpeedOpenTime":
                        ret.Value = _porCheSonData.LowSpeedOpenTime;
                        break;
                    case "LowPressureProtectionTime":
                        ret.Value = _porCheSonData.LowPressureProtectionTime;
                        break;
                    case "MoldClosingTimeLimit":
                        ret.Value = _porCheSonData.MoldClosingTimeLimit;
                        break;
                    case "SlowMoldOpeningTime":
                        ret.Value = _porCheSonData.SlowMoldOpeningTime;
                        break;
                    case "LowSpeedMoldOpeningTime":
                        ret.Value = _porCheSonData.LowSpeedMoldOpeningTime;
                        break;
                    case "InjectionPressure1":
                        ret.Value = _porCheSonData.InjectionPressure1;
                        break;
                    case "InjectionFlow1":
                        ret.Value = _porCheSonData.InjectionFlow1;
                        break;
                    case "InjectionTime1":
                        ret.Value = _porCheSonData.InjectionTime1;
                        break;
                    case "InjectionPressure2":
                        ret.Value = _porCheSonData.InjectionPressure2;
                        break;
                    case "InjectionFlow2":
                        ret.Value = _porCheSonData.InjectionFlow2;
                        break;
                    case "InjectionTime2":
                        ret.Value = _porCheSonData.InjectionTime2;
                        break;
                    case "InjectionPressure3":
                        ret.Value = _porCheSonData.InjectionPressure3;
                        break;
                    case "InjectionFlow3":
                        ret.Value = _porCheSonData.InjectionFlow3;
                        break;
                    case "InjectionTime3":
                        ret.Value = _porCheSonData.InjectionTime3;
                        break;
                    case "HoldingPressure3":
                        ret.Value = _porCheSonData.HoldingPressure3;
                        break;
                    case "HoldingFlow3":
                        ret.Value = _porCheSonData.HoldingFlow3;
                        break;
                    case "HoldingTime3":
                        ret.Value = _porCheSonData.HoldingTime3;
                        break;
                    case "TotalInjectionTime":
                        ret.Value = _porCheSonData.TotalInjectionTime;
                        break;
                    case "InjectionMonitoringPoint":
                        ret.Value = _porCheSonData.InjectionMonitoringPoint;
                        break;
                    case "PostShootingBackPressure":
                        ret.Value = _porCheSonData.PostShootingBackPressure;
                        break;
                    case "PostShootingBackFlow":
                        ret.Value = _porCheSonData.PostShootingBackFlow;
                        break;
                    case "PostShootingBackTime":
                        ret.Value = _porCheSonData.PostShootingBackTime;
                        break;
                    case "MaterialStoragePressure":
                        ret.Value = _porCheSonData.MaterialStoragePressure;
                        break;
                    case "MaterialStorageFlow":
                        ret.Value = _porCheSonData.MaterialStorageFlow;
                        break;
                    case "PreShootingBackPressure":
                        ret.Value = _porCheSonData.PreShootingBackPressure;
                        break;
                    case "PreShootingBackFlow":
                        ret.Value = _porCheSonData.PreShootingBackFlow;
                        break;
                    case "PreShootingBackTime":
                        ret.Value = _porCheSonData.PreShootingBackTime;
                        break;
                    case "MaterialStorageTimeLimit":
                        ret.Value = _porCheSonData.MaterialStorageTimeLimit;
                        break;
                    case "PreCoolingTime":
                        ret.Value = _porCheSonData.PreCoolingTime;
                        break;
                    case "PostCoolingTime":
                        ret.Value = _porCheSonData.PostCoolingTime;
                        break;
                    case "PurgeBackPressure":
                        ret.Value = _porCheSonData.PurgeBackPressure;
                        break;
                    case "PurgeBackFlow":
                        ret.Value = _porCheSonData.PurgeBackFlow;
                        break;
                    case "PurgeBackTime":
                        ret.Value = _porCheSonData.PurgeBackTime;
                        break;
                    case "PurgePressure":
                        ret.Value = _porCheSonData.PurgePressure;
                        break;
                    case "PurgeFlow":
                        ret.Value = _porCheSonData.PurgeFlow;
                        break;
                    case "PurgeTime":
                        ret.Value = _porCheSonData.PurgeTime;
                        break;
                    case "PurgeInjectionPressure":
                        ret.Value = _porCheSonData.PurgeInjectionPressure;
                        break;
                    case "PurgeInjectionFlow":
                        ret.Value = _porCheSonData.PurgeInjectionFlow;
                        break;
                    case "PurgeInjectionTime":
                        ret.Value = _porCheSonData.PurgeInjectionTime;
                        break;
                    case "PurgeCount":
                        ret.Value = _porCheSonData.PurgeCount;
                        ret.DataType = DataTypeEnum.Int32;
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion 上电数据

    #region 座台/托模设定

    /// <summary>
    ///     座台退压力
    /// </summary>
    /// <returns></returns>
    [Method("TableRetreatPressure", name: "座台退压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableRetreatPressure()
    {
        return await MoldOrTableSetting("TableRetreatPressure");
    }

    /// <summary>
    ///     座台退流量
    /// </summary>
    /// <returns></returns>
    [Method("TableRetreatFlow", name: "座台退流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableRetreatFlow()
    {
        return await MoldOrTableSetting("TableRetreatFlow");
    }

    /// <summary>
    ///     座台退时间
    /// </summary>
    /// <returns></returns>
    [Method("TableRetreatTime", name: "座台退时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableRetreatTime()
    {
        return await MoldOrTableSetting("TableRetreatTime");
    }

    /// <summary>
    ///     座台进压力
    /// </summary>
    /// <returns></returns>
    [Method("TableAdvancePressure", name: "座台进压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableAdvancePressure()
    {
        return await MoldOrTableSetting("TableAdvancePressure");
    }

    /// <summary>
    ///     座台进流量
    /// </summary>
    /// <returns></returns>
    [Method("TableAdvanceFlow", name: "座台进流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableAdvanceFlow()
    {
        return await MoldOrTableSetting("TableAdvanceFlow");
    }

    /// <summary>
    ///     座台进时间
    /// </summary>
    /// <returns></returns>
    [Method("TableAdvanceTime", name: "座台进时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableAdvanceTime()
    {
        return await MoldOrTableSetting("TableAdvanceTime");
    }

    /// <summary>
    ///     托模保持压力
    /// </summary>
    /// <returns></returns>
    [Method("MoldHoldPressure", name: "托模保持压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldHoldPressure()
    {
        return await MoldOrTableSetting("MoldHoldPressure");
    }

    /// <summary>
    ///     托模保持流量
    /// </summary>
    /// <returns></returns>
    [Method("MoldHoldFlow", name: "托模保持流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldHoldFlow()
    {
        return await MoldOrTableSetting("MoldHoldFlow");
    }

    /// <summary>
    ///     托模保持时间
    /// </summary>
    /// <returns></returns>
    [Method("MoldHoldTime", name: "托模保持时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldHoldTime()
    {
        return await MoldOrTableSetting("MoldHoldTime");
    }

    /// <summary>
    ///     托模进压力
    /// </summary>
    /// <returns></returns>
    [Method("MoldAdvancePressure", name: "托模进压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldAdvancePressure()
    {
        return await MoldOrTableSetting("MoldAdvancePressure");
    }

    /// <summary>
    ///     托模进流量
    /// </summary>
    /// <returns></returns>
    [Method("MoldAdvanceFlow", name: "托模进流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldAdvanceFlow()
    {
        return await MoldOrTableSetting("MoldAdvanceFlow");
    }

    /// <summary>
    ///     托模进时间
    /// </summary>
    /// <returns></returns>
    [Method("MoldAdvanceTime", name: "托模进时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldAdvanceTime()
    {
        return await MoldOrTableSetting("MoldAdvanceTime");
    }

    /// <summary>
    ///     托模退压力
    /// </summary>
    /// <returns></returns>
    [Method("MoldRetreatPressure", name: "托模退压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldRetreatPressure()
    {
        return await MoldOrTableSetting("MoldRetreatPressure");
    }

    /// <summary>
    ///     托模退流量
    /// </summary>
    /// <returns></returns>
    [Method("MoldRetreatFlow", name: "托模退流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldRetreatFlow()
    {
        return await MoldOrTableSetting("MoldRetreatFlow");
    }

    /// <summary>
    ///     托模退时间
    /// </summary>
    /// <returns></returns>
    [Method("MoldRetreatTime", name: "托模退时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldRetreatTime()
    {
        return await MoldOrTableSetting("MoldRetreatTime");
    }

    /// <summary>
    ///     托模次数
    /// </summary>
    /// <returns></returns>
    [Method("MoldCount", name: "托模次数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldCount()
    {
        return await MoldOrTableSetting("MoldCount");
    }

    /// <summary>
    ///     托模进延迟时间
    /// </summary>
    /// <returns></returns>
    [Method("MoldAdvanceDelayTime", name: "托模进延迟时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldAdvanceDelayTime()
    {
        return await MoldOrTableSetting("MoldAdvanceDelayTime");
    }

    /// <summary>
    ///     托模退延迟时间
    /// </summary>
    /// <returns></returns>
    [Method("MoldRetreatDelayTime", name: "托模退延迟时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldRetreatDelayTime()
    {
        return await MoldOrTableSetting("MoldRetreatDelayTime");
    }

    /// <summary>
    ///     座台/托模设定
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> MoldOrTableSetting(string identifier)
    {
        DriverReturnValueModel ret = new() {DataType = DataTypeEnum.Double};
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    case "TableRetreatPressure":
                        ret.Value = _porCheSonData.TableRetreatPressure;
                        break;
                    case "TableRetreatFlow":
                        ret.Value = _porCheSonData.TableRetreatFlow;
                        break;
                    case "TableRetreatTime":
                        ret.Value = _porCheSonData.TableRetreatTime;
                        break;
                    case "TableAdvancePressure":
                        ret.Value = _porCheSonData.TableAdvancePressure;
                        break;
                    case "TableAdvanceFlow":
                        ret.Value = _porCheSonData.TableAdvanceFlow;
                        break;
                    case "TableAdvanceTime":
                        ret.Value = _porCheSonData.TableAdvanceTime;
                        break;
                    case "MoldHoldPressure":
                        ret.Value = _porCheSonData.MoldHoldPressure;
                        break;
                    case "MoldHoldFlow":
                        ret.Value = _porCheSonData.MoldHoldFlow;
                        break;
                    case "MoldHoldTime":
                        ret.Value = _porCheSonData.MoldHoldTime;
                        break;
                    case "MoldAdvancePressure":
                        ret.Value = _porCheSonData.MoldAdvancePressure;
                        break;
                    case "MoldAdvanceFlow":
                        ret.Value = _porCheSonData.MoldAdvanceFlow;
                        break;
                    case "MoldAdvanceTime":
                        ret.Value = _porCheSonData.MoldAdvanceTime;
                        break;
                    case "MoldRetreatPressure":
                        ret.Value = _porCheSonData.MoldRetreatPressure;
                        break;
                    case "MoldRetreatFlow":
                        ret.Value = _porCheSonData.MoldRetreatFlow;
                        break;
                    case "MoldRetreatTime":
                        ret.Value = _porCheSonData.MoldRetreatTime;
                        break;
                    case "MoldCount":
                        ret.Value = _porCheSonData.MoldCount;
                        ret.DataType = DataTypeEnum.Int32;
                        break;
                    case "MoldAdvanceDelayTime":
                        ret.Value = _porCheSonData.MoldAdvanceDelayTime;
                        break;
                    case "MoldRetreatDelayTime":
                        ret.Value = _porCheSonData.MoldRetreatDelayTime;
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion 座台/托模设定

    #region 计时/计数设定，温度设定

    /// <summary>
    ///     润滑计时
    /// </summary>
    /// <returns></returns>
    [Method("LubricationTiming", name: "润滑计时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LubricationTiming()
    {
        return await TimeOrCountSetting("LubricationTiming");
    }

    /// <summary>
    ///     润滑模数
    /// </summary>
    /// <returns></returns>
    [Method("LubricationCycle", name: "润滑模数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> LubricationCycle()
    {
        return await TimeOrCountSetting("LubricationCycle");
    }

    /// <summary>
    ///     循环等待时间
    /// </summary>
    /// <returns></returns>
    [Method("CycleWaitTime", name: "循环等待时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> CycleWaitTime()
    {
        return await TimeOrCountSetting("CycleWaitTime");
    }

    /// <summary>
    ///     手动动作限时
    /// </summary>
    /// <returns></returns>
    [Method("ManualActionTimeLimit", name: "手动动作限时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ManualActionTimeLimit()
    {
        return await TimeOrCountSetting("ManualActionTimeLimit");
    }

    /// <summary>
    ///     周期时间
    /// </summary>
    /// <returns></returns>
    [Method("CycleTime", name: "周期时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> CycleTime()
    {
        return await TimeOrCountSetting("CycleTime");
    }

    /// <summary>
    ///     故障告警时间
    /// </summary>
    /// <returns></returns>
    [Method("FaultWarningTime", name: "故障告警时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> FaultWarningTime()
    {
        return await TimeOrCountSetting("FaultWarningTime");
    }

    /// <summary>
    ///     射咀
    /// </summary>
    /// <returns></returns>
    [Method("Nozzle", name: "射咀", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> Nozzle()
    {
        return await TimeOrCountSetting("Nozzle");
    }

    /// <summary>
    ///     螺杆冷启动时间
    /// </summary>
    /// <returns></returns>
    [Method("ScrewColdStartUpTime", name: "螺杆冷启动时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ScrewColdStartUpTime()
    {
        return await TimeOrCountSetting("ScrewColdStartUpTime");
    }

    /// <summary>
    ///     设定模数
    /// </summary>
    /// <returns></returns>
    [Method("SetMoldCount", name: "设定模数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> SetMoldCount()
    {
        return await TimeOrCountSetting("SetMoldCount");
    }

    /// <summary>
    ///     产量
    /// </summary>
    /// <returns></returns>
    [Method("OpenedMoldCount", name: "产量", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> OpenedMoldCount()
    {
        return await TimeOrCountSetting("OpenedMoldCount");
    }

    /// <summary>
    ///     卸荷压力
    /// </summary>
    /// <returns></returns>
    [Method("UnloadPressure", name: "卸荷压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> UnloadPressure()
    {
        return await TimeOrCountSetting("UnloadPressure");
    }

    /// <summary>
    ///     卸荷流量
    /// </summary>
    /// <returns></returns>
    [Method("UnloadFlow", name: "卸荷流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> UnloadFlow()
    {
        return await TimeOrCountSetting("UnloadFlow");
    }

    /// <summary>
    ///     计时/计数设定，温度设定
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> TimeOrCountSetting(string identifier)
    {
        DriverReturnValueModel ret = new() {DataType = DataTypeEnum.Double};
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    case "LubricationTiming":
                        ret.Value = _porCheSonData.LubricationTiming;
                        break;
                    case "LubricationCycle":
                        ret.Value = _porCheSonData.LubricationCycle;
                        ret.DataType = DataTypeEnum.Int32;
                        break;
                    case "CycleWaitTime":
                        ret.Value = _porCheSonData.CycleWaitTime;
                        break;
                    case "ManualActionTimeLimit":
                        ret.Value = _porCheSonData.ManualActionTimeLimit;
                        break;
                    case "CycleTime":
                        ret.Value = _porCheSonData.CycleTime;
                        break;
                    case "FaultWarningTime":
                        ret.Value = _porCheSonData.FaultWarningTime;
                        break;
                    case "Nozzle":
                        ret.Value = _porCheSonData.Nozzle;
                        ret.DataType = DataTypeEnum.Int32;
                        break;
                    case "ScrewColdStartUpTime":
                        ret.Value = _porCheSonData.ScrewColdStartUpTime;
                        break;
                    case "SetMoldCount":
                        ret.Value = _porCheSonData.SetMoldCount;
                        ret.DataType = DataTypeEnum.Int32;
                        break;
                    case "OpenedMoldCount":
                        ret.Value = _porCheSonData.OpenedMoldCount;
                        ret.DataType = DataTypeEnum.Int32;
                        break;
                    case "UnloadPressure":
                        ret.Value = _porCheSonData.UnloadPressure;
                        break;
                    case "UnloadFlow":
                        ret.Value = _porCheSonData.UnloadFlow;
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion 计时/计数设定，温度设定

    #region 射出/保压设定

    /// <summary>
    ///     获取保压压力1
    /// </summary>
    /// <returns>保压压力1的值</returns>
    [Method("HoldingPressure1", name: "保压压力1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetHoldingPressure1()
    {
        return await LubricationOrScrewColdSetting("HoldingPressure1");
    }

    /// <summary>
    ///     获取保压流量1
    /// </summary>
    /// <returns>保压流量1的值</returns>
    [Method("HoldingFlow1", name: "保压流量1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetHoldingFlow1()
    {
        return await LubricationOrScrewColdSetting("HoldingFlow1");
    }

    /// <summary>
    ///     获取保压时间1
    /// </summary>
    /// <returns>保压时间1的值</returns>
    [Method("HoldingTime1", name: "保压时间1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetHoldingTime1()
    {
        return await LubricationOrScrewColdSetting("HoldingTime1");
    }

    /// <summary>
    ///     获取保压压力2
    /// </summary>
    /// <returns>保压压力2的值</returns>
    [Method("HoldingPressure2", name: "保压压力2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetHoldingPressure2()
    {
        return await LubricationOrScrewColdSetting("HoldingPressure2");
    }

    /// <summary>
    ///     获取保压流量2
    /// </summary>
    /// <returns>保压流量2的值</returns>
    [Method("HoldingFlow2", name: "保压流量2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetHoldingFlow2()
    {
        return await LubricationOrScrewColdSetting("HoldingFlow2");
    }

    /// <summary>
    ///     获取保压时间2
    /// </summary>
    /// <returns>保压时间2的值</returns>
    [Method("HoldingTime2", name: "保压时间2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetHoldingTime2()
    {
        return await LubricationOrScrewColdSetting("HoldingTime2");
    }

    /// <summary>
    ///     获取合模压力
    /// </summary>
    /// <returns>合模压力的值</returns>
    [Method("MoldClosingPressure", name: "合模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetMoldClosingPressure()
    {
        return await LubricationOrScrewColdSetting("MoldClosingPressure");
    }

    /// <summary>
    ///     获取合模流量
    /// </summary>
    /// <returns>合模流量的值</returns>
    [Method("MoldClosingFlow", name: "合模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetMoldClosingFlow()
    {
        return await LubricationOrScrewColdSetting("MoldClosingFlow");
    }

    /// <summary>
    ///     获取开模压力
    /// </summary>
    /// <returns>开模压力的值</returns>
    [Method("MoldOpeningPressure", name: "开模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetMoldOpeningPressure()
    {
        return await LubricationOrScrewColdSetting("MoldOpeningPressure");
    }

    /// <summary>
    ///     获取开模流量
    /// </summary>
    /// <returns>开模流量的值</returns>
    [Method("MoldOpeningFlow", name: "开模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetMoldOpeningFlow()
    {
        return await LubricationOrScrewColdSetting("MoldOpeningFlow");
    }


    /// <summary>
    ///     射出/保压设定
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> LubricationOrScrewColdSetting(string identifier)
    {
        DriverReturnValueModel ret = new() {DataType = DataTypeEnum.Double};
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    case "HoldingPressure1":
                        ret.Value = _porCheSonData.HoldingPressure1;
                        break;
                    case "HoldingFlow1":
                        ret.Value = _porCheSonData.HoldingFlow1;
                        break;
                    case "HoldingTime1":
                        ret.Value = _porCheSonData.HoldingTime1;
                        break;
                    case "HoldingPressure2":
                        ret.Value = _porCheSonData.HoldingPressure2;
                        break;
                    case "HoldingFlow2":
                        ret.Value = _porCheSonData.HoldingFlow2;
                        break;
                    case "HoldingTime2":
                        ret.Value = _porCheSonData.HoldingTime2;
                        break;
                    case "MoldClosingPressure":
                        ret.Value = _porCheSonData.MoldClosingPressure;
                        break;
                    case "MoldClosingFlow":
                        ret.Value = _porCheSonData.MoldClosingFlow;
                        break;
                    case "MoldOpeningPressure":
                        ret.Value = _porCheSonData.MoldOpeningPressure;
                        break;
                    case "MoldOpeningFlow":
                        ret.Value = _porCheSonData.MoldOpeningFlow;
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion 射出/保压设定

    #region 温度设定

    /// <summary>
    ///     获取温度设定1
    /// </summary>
    /// <returns>温度设定1的值</returns>
    [Method("TemperatureSetting1", name: "温度设定1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting1()
    {
        return await TemperatureSetting("TemperatureSetting1");
    }

    /// <summary>
    ///     获取温度设定2
    /// </summary>
    /// <returns>温度设定2的值</returns>
    [Method("TemperatureSetting2", name: "温度设定2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting2()
    {
        return await TemperatureSetting("TemperatureSetting2");
    }

    /// <summary>
    ///     获取温度设定3
    /// </summary>
    /// <returns>温度设定3的值</returns>
    [Method("TemperatureSetting3", name: "温度设定3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting3()
    {
        return await TemperatureSetting("TemperatureSetting3");
    }

    /// <summary>
    ///     获取温度设定4
    /// </summary>
    /// <returns>温度设定4的值</returns>
    [Method("TemperatureSetting4", name: "温度设定4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting4()
    {
        return await TemperatureSetting("TemperatureSetting4");
    }

    /// <summary>
    ///     获取温度设定5
    /// </summary>
    /// <returns>温度设定5的值</returns>
    [Method("TemperatureSetting5", name: "温度设定5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting5()
    {
        return await TemperatureSetting("TemperatureSetting5");
    }

    /// <summary>
    ///     获取温度设定6
    /// </summary>
    /// <returns>温度设定6的值</returns>
    [Method("TemperatureSetting6", name: "温度设定6", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting6()
    {
        return await TemperatureSetting("TemperatureSetting6");
    }

    /// <summary>
    ///     获取温度设定7
    /// </summary>
    /// <returns>温度设定7的值</returns>
    [Method("TemperatureSetting7", name: "温度设定7", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting7()
    {
        return await TemperatureSetting("TemperatureSetting7");
    }

    /// <summary>
    ///     获取温度设定8
    /// </summary>
    /// <returns>温度设定8的值</returns>
    [Method("TemperatureSetting8", name: "温度设定8", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting8()
    {
        return await TemperatureSetting("TemperatureSetting8");
    }

    /// <summary>
    ///     获取温度设定9
    /// </summary>
    /// <returns>温度设定9的值</returns>
    [Method("TemperatureSetting9", name: "温度设定9", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting9()
    {
        return await TemperatureSetting("TemperatureSetting9");
    }

    /// <summary>
    ///     获取温度设定10
    /// </summary>
    /// <returns>温度设定10的值</returns>
    [Method("TemperatureSetting10", name: "温度设定10", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting10()
    {
        return await TemperatureSetting("TemperatureSetting10");
    }

    /// <summary>
    ///     获取温度设定上限1
    /// </summary>
    /// <returns>温度设定上限1的值</returns>
    [Method("TemperatureUpperLimit1", name: "温度设定上限1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureUpperLimit1()
    {
        return await TemperatureSetting("TemperatureUpperLimit1");
    }

    /// <summary>
    ///     获取温度设定上限2
    /// </summary>
    /// <returns>温度设定上限2的值</returns>
    [Method("TemperatureUpperLimit2", name: "温度设定上限2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureUpperLimit2()
    {
        return await TemperatureSetting("TemperatureUpperLimit2");
    }

    /// <summary>
    ///     获取温度设定上限3
    /// </summary>
    /// <returns>温度设定上限3的值</returns>
    [Method("TemperatureUpperLimit3", name: "温度设定上限3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureUpperLimit3()
    {
        return await TemperatureSetting("TemperatureUpperLimit3");
    }

    /// <summary>
    ///     获取温度设定下限1
    /// </summary>
    /// <returns>温度设定下限1的值</returns>
    [Method("TemperatureLowerLimit1", name: "温度设定下限1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureLowerLimit1()
    {
        return await TemperatureSetting("TemperatureLowerLimit1");
    }

    /// <summary>
    ///     获取温度设定下限2
    /// </summary>
    /// <returns>温度设定下限2的值</returns>
    [Method("TemperatureLowerLimit2", name: "温度设定下限2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureLowerLimit2()
    {
        return await TemperatureSetting("TemperatureLowerLimit2");
    }

    /// <summary>
    ///     获取温度设定下限3
    /// </summary>
    /// <returns>温度设定下限3的值</returns>
    [Method("TemperatureLowerLimit3", name: "温度设定下限3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureLowerLimit3()
    {
        return await TemperatureSetting("TemperatureLowerLimit3");
    }

    /// <summary>
    ///     温度设定
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> TemperatureSetting(string identifier)
    {
        DriverReturnValueModel ret = new() {DataType = DataTypeEnum.Double};
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    case "TemperatureSetting1":
                        ret.Value = _porCheSonData.TemperatureSetting1;
                        break;
                    case "TemperatureSetting2":
                        ret.Value = _porCheSonData.TemperatureSetting2;
                        break;
                    case "TemperatureSetting3":
                        ret.Value = _porCheSonData.TemperatureSetting3;
                        break;
                    case "TemperatureSetting4":
                        ret.Value = _porCheSonData.TemperatureSetting4;
                        break;
                    case "TemperatureSetting5":
                        ret.Value = _porCheSonData.TemperatureSetting5;
                        break;
                    case "TemperatureSetting6":
                        ret.Value = _porCheSonData.TemperatureSetting6;
                        break;
                    case "TemperatureSetting7":
                        ret.Value = _porCheSonData.TemperatureSetting7;
                        break;
                    case "TemperatureSetting8":
                        ret.Value = _porCheSonData.TemperatureSetting8;
                        break;
                    case "TemperatureSetting9":
                        ret.Value = _porCheSonData.TemperatureSetting9;
                        break;
                    case "TemperatureSetting10":
                        ret.Value = _porCheSonData.TemperatureSetting10;
                        break;
                    case "TemperatureUpperLimit1":
                        ret.Value = _porCheSonData.TemperatureUpperLimit1;
                        break;
                    case "TemperatureUpperLimit2":
                        ret.Value = _porCheSonData.TemperatureUpperLimit2;
                        break;
                    case "TemperatureUpperLimit3":
                        ret.Value = _porCheSonData.TemperatureUpperLimit3;
                        break;
                    case "TemperatureLowerLimit1":
                        ret.Value = _porCheSonData.TemperatureLowerLimit1;
                        break;
                    case "TemperatureLowerLimit2":
                        ret.Value = _porCheSonData.TemperatureLowerLimit2;
                        break;
                    case "TemperatureLowerLimit3":
                        ret.Value = _porCheSonData.TemperatureLowerLimit3;
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion 温度设定

    #region 实时温度

    /// <summary>
    ///     获取温度1
    /// </summary>
    /// <returns>温度1的值</returns>
    [Method("Temperature1", name: "温度1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature1()
    {
        return await RealTimeTemperature("Temperature1");
    }

    /// <summary>
    ///     获取温度2
    /// </summary>
    /// <returns>温度2的值</returns>
    [Method("Temperature2", name: "温度2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature2()
    {
        return await RealTimeTemperature("Temperature2");
    }

    /// <summary>
    ///     获取温度3
    /// </summary>
    /// <returns>温度3的值</returns>
    [Method("Temperature3", name: "温度3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature3()
    {
        return await RealTimeTemperature("Temperature3");
    }

    /// <summary>
    ///     获取温度4
    /// </summary>
    /// <returns>温度4的值</returns>
    [Method("Temperature4", name: "温度4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature4()
    {
        return await RealTimeTemperature("Temperature4");
    }

    /// <summary>
    ///     获取温度5
    /// </summary>
    /// <returns>温度5的值</returns>
    [Method("Temperature5", name: "温度5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature5()
    {
        return await RealTimeTemperature("Temperature5");
    }

    /// <summary>
    ///     获取温度6
    /// </summary>
    /// <returns>温度6的值</returns>
    [Method("Temperature6", name: "温度6", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature6()
    {
        return await RealTimeTemperature("Temperature6");
    }

    /// <summary>
    ///     获取温度7
    /// </summary>
    /// <returns>温度7的值</returns>
    [Method("Temperature7", name: "温度7", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature7()
    {
        return await RealTimeTemperature("Temperature7");
    }

    /// <summary>
    ///     获取温度8
    /// </summary>
    /// <returns>温度8的值</returns>
    [Method("Temperature8", name: "温度8", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature8()
    {
        return await RealTimeTemperature("Temperature8");
    }

    /// <summary>
    ///     获取温度9
    /// </summary>
    /// <returns>温度9的值</returns>
    [Method("Temperature9", name: "温度9", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature9()
    {
        return await RealTimeTemperature("Temperature9");
    }

    /// <summary>
    ///     获取温度10
    /// </summary>
    /// <returns>温度10的值</returns>
    [Method("Temperature10", name: "温度10", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature10()
    {
        return await RealTimeTemperature("Temperature10");
    }

    /// <summary>
    ///     获取良品数
    /// </summary>
    /// <returns>良品数的值</returns>
    [Method("GoodCount", name: "良品数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetGoodCount()
    {
        return await RealTimeTemperature("GoodCount");
    }

    /// <summary>
    ///     获取预产模数
    /// </summary>
    /// <returns>预产模数的值</returns>
    [Method("PlannedMoldCount", name: "预产模数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetPlannedMoldCount()
    {
        return await RealTimeTemperature("PlannedMoldCount");
    }

    /// <summary>
    ///     实时温度
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> RealTimeTemperature(string identifier)
    {
        DriverReturnValueModel ret = new() {DataType = DataTypeEnum.Double};
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    case "Temperature1":
                        ret.Value = _porCheSonData.Temperature1;
                        break;
                    case "Temperature2":
                        ret.Value = _porCheSonData.Temperature2;
                        break;
                    case "Temperature3":
                        ret.Value = _porCheSonData.Temperature3;
                        break;
                    case "Temperature4":
                        ret.Value = _porCheSonData.Temperature4;
                        break;
                    case "Temperature5":
                        ret.Value = _porCheSonData.Temperature5;
                        break;
                    case "Temperature6":
                        ret.Value = _porCheSonData.Temperature6;
                        break;
                    case "Temperature7":
                        ret.Value = _porCheSonData.Temperature7;
                        break;
                    case "Temperature8":
                        ret.Value = _porCheSonData.Temperature8;
                        break;
                    case "Temperature9":
                        ret.Value = _porCheSonData.Temperature9;
                        break;
                    case "Temperature10":
                        ret.Value = _porCheSonData.Temperature10;
                        break;
                    case "GoodCount":
                        ret.Value = _porCheSonData.GoodCount;
                        break;
                    case "PlannedMoldCount":
                        ret.Value = _porCheSonData.PlannedMoldCount;
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion

    /// <summary>
    ///     模式
    /// </summary>
    /// <returns></returns>
    [Method("Mode", name: "模式", dataType: TransPondDataTypeEnum.Int, description: "模式 1：马达开；2：手动；3：半自动；4：全自动")]
    public async Task<DriverReturnValueModel> Mode()
    {
        DriverReturnValueModel ret = new() {DataType = DataTypeEnum.Int32};
        try
        {
            if (IsConnected)
            {
                ret.Value = (int) _porCheSonData.Mode;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion 采集点位

    /// <summary>
    ///     关闭连接,串口协议需要自行重写该方法
    /// </summary>
    public override void Close()
    {
        _ = SaveContent();
        _client1?.Dispose();
        _client2?.Dispose();
    }

    /// <summary>
    ///     释放连接,串口协议需要自行重写该方法
    /// </summary>
    public override void Dispose()
    {
        if (_timer != null)
        {
            // 停止定时器
            _timer.Elapsed -= TimerElapsed;
            _timer.Stop();
            _timer.Dispose();
        }

        _client1?.Dispose();
        _client2?.Dispose();
    }

    private readonly Timer _timer;

    /// <summary>
    ///     持久化内容
    /// </summary>
    private async Task SaveContent()
    {
        // 持久化最后的数据
        var data = _porCheSonData.ToJsonString();
        // 设置文件路径
        var filePath = "/Edge/Porcheson_Ps660Bm.txt";
        // 写入数据到文件
        await File.WriteAllTextAsync(filePath, data);
    }

    /// <summary>
    ///     读取持久化内容
    /// </summary>
    private void ReadContent()
    {
        // 设置文件路径
        var filePath = "/Edge/Porcheson_Ps660Bm.txt";
        if (File.Exists(filePath))
        {
            var data = File.ReadAllText(filePath);
            _porCheSonData = JSON.Deserialize<PorchesonData>(data);    
        }
        else
            File.WriteAllTextAsync(filePath, _porCheSonData.ToJsonString());
        File.WriteAllTextAsync("/Edge/type.txt", "Porcheson_Ps660Bm");
    }

    /// <summary>
    ///     持久化内容
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void TimerElapsed(object sender, ElapsedEventArgs e)
    {
        if (_client1 is {IsConnected: true} || _client2 is {IsConnected: true})
        {
            var pingRest = _ping.Send(IpAddress, 1000);
            var pingRes = pingRest!.Status == IPStatus.Success;
            _pingIp = pingRes;
        }

        _ = SaveContent();
    }
}