using System;
using System.IO;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Timers;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Feng.Common.Util;
using Furion.JsonSerialization;
using Furion.Logging;
using HslCommunication;

namespace PORCHESON_Ps660Am;

/* 宝捷信-PS660AM
 * 更新日志
 * 版本:v1.1.0
 *  修复串口只收一路数据问题
 * 版本:v1.1.1
 *  注塑机协议根据要求强制改为根据命名写入某个固定文件 - 2024-05-21 -wangj
 */
[DriverSupported("PORCHESON_Ps660Am")]
[DriverInfo("PORCHESON_Ps660Am", "V1.1.1", "注塑机")]
public class PORCHESON_Ps660Am : BaseDeviceProtocolCollector, IDriver
{
    [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置", Display = false)]
    public override int WriteInterval { get; set; } = 120;

    [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    [ConfigParameter("连接方式", order: 1)] public DriverConnectTypeEnum ConnectType { get; set; } = DriverConnectTypeEnum.SerialPort;

    #region 网口配置

    [ConfigParameter("IP地址", Display = false, DisplayExpress = "{\"ConnectType\": [\"1\"]}")]
    public string IpAddress { get; set; } = "127.0.0.1";

    [ConfigParameter("端口号", Display = false, DisplayExpress = "{\"ConnectType\": [\"1\"]}")]
    public int Port { get; set; } = 8001;

    [ConfigParameter("端口号2", Display = false, DisplayExpress = "{\"ConnectType\": [\"1\"]}")]
    public int Port2 { get; set; } = 8002;

    #endregion 网口配置

    #region 串口配置

    [ConfigParameter("串口号", Display = false, Remark = "", DisplayExpress = "{\"ConnectType\": [\"2\"]}")]
    public SerialNumberEnum SerialNumber { get; set; } = SerialNumberEnum.ttyS3;

    [ConfigParameter("串口号2", Display = false, Remark = "", DisplayExpress = "{\"ConnectType\": [\"2\"]}")]
    public SerialNumberEnum SerialNumber2 { get; set; } = SerialNumberEnum.ttyS4;

    #endregion 串口配置

    private ClientHandlerPs660Am _client1;
    private ClientHandlerPs660Am _client2;
    private PorchesonData _porCheSonData = new();
    private readonly Ping _ping = new();

    public PORCHESON_Ps660Am(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
        try
        {
            ReadContent();
            // 创建一个 Timer 实例，设置定时器间隔为 10 秒
            _timer = new Timer(1000 * 10);
            _timer.Elapsed += TimerElapsed;

            // 启动定时器
            _timer.Start();
        }
        catch (Exception e)
        {
            Log.Error($"【宝捷信AM】 初始化异常:{e.Message}");
        }
    }

    public override bool IsConnected => _client1 != null && _client2 != null && (_client1.IsConnected || _client2.IsConnected);

    public DeviceConnectDto Connect()
    {
        try
        {
            _client1 = new ClientHandlerPs660Am(_porCheSonData, DriverInfo);
            _client2 = new ClientHandlerPs660Am(_porCheSonData, DriverInfo);
            if (ConnectType == DriverConnectTypeEnum.NetWork)
            {
                _client1.Connect(IpAddress, Port);
                _client2.Connect(IpAddress, Port2);
            }
            else
            {
                _client1.Connect(EnumUtil.GetEnumDesc(SerialNumber));
                _client2.Connect(EnumUtil.GetEnumDesc(SerialNumber2));
            }

            IsConnected = _client1.IsConnected && _client2.IsConnected;
            OperateResult.IsSuccess = IsConnected;
            OperateResult.Message = IsConnected ? "连接成功" : "连接失败";
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }

    #region 采集点位

    #region 上电数据

    /// <summary>
    ///     低速开模压力
    /// </summary>
    /// <returns></returns>
    [Method("LowSpeedMoldPressure", name: "低速开模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LowSpeedMoldPressure()
    {
        return await PowerOnData("LowSpeedMoldPressure");
    }

    /// <summary>
    ///     中速开模压力
    /// </summary>
    /// <returns></returns>
    [Method("MediumSpeedMoldPressure", name: "中速开模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MediumSpeedMoldPressure()
    {
        return await PowerOnData("MediumSpeedMoldPressure");
    }

    /// <summary>
    ///     快速开模压力
    /// </summary>
    /// <returns></returns>
    [Method("FastSpeedMoldPressure", name: "快速开模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> FastSpeedMoldPressure()
    {
        return await PowerOnData("FastSpeedMoldPressure");
    }

    /// <summary>
    ///     慢速开模压力
    /// </summary>
    /// <returns></returns>
    [Method("SlowSpeedMoldPressure", name: "慢速开模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> SlowSpeedMoldPressure()
    {
        return await PowerOnData("SlowSpeedMoldPressure");
    }

    /// <summary>
    ///     低速开模流量
    /// </summary>
    /// <returns></returns>
    [Method("LowSpeedMoldFlow", name: "低速开模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LowSpeedMoldFlow()
    {
        return await PowerOnData("LowSpeedMoldFlow");
    }

    /// <summary>
    ///     中速开模流量
    /// </summary>
    /// <returns></returns>
    [Method("MediumSpeedMoldFlow", name: "中速开模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MediumSpeedMoldFlow()
    {
        return await PowerOnData("MediumSpeedMoldFlow");
    }

    /// <summary>
    ///     快速开模流量
    /// </summary>
    /// <returns></returns>
    [Method("FastSpeedMoldFlow", name: "快速开模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> FastSpeedMoldFlow()
    {
        return await PowerOnData("FastSpeedMoldFlow");
    }

    /// <summary>
    ///     慢速开模流量
    /// </summary>
    /// <returns></returns>
    [Method("SlowSpeedMoldFlow", name: "慢速开模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> SlowSpeedMoldFlow()
    {
        return await PowerOnData("SlowSpeedMoldFlow");
    }

    /// <summary>
    ///     低速开模位置
    /// </summary>
    /// <returns></returns>
    [Method("LowSpeedMoldPosition", name: "低速开模位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LowSpeedMoldPosition()
    {
        return await PowerOnData("LowSpeedMoldPosition");
    }

    /// <summary>
    ///     中速开模位置
    /// </summary>
    /// <returns></returns>
    [Method("MediumSpeedMoldPosition", name: "中速开模位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MediumSpeedMoldPosition()
    {
        return await PowerOnData("MediumSpeedMoldPosition");
    }

    /// <summary>
    ///     快速开模位置
    /// </summary>
    /// <returns></returns>
    [Method("FastSpeedMoldPosition", name: "快速开模位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> FastSpeedMoldPosition()
    {
        return await PowerOnData("FastSpeedMoldPosition");
    }

    /// <summary>
    ///     慢速开模位置
    /// </summary>
    /// <returns></returns>
    [Method("SlowSpeedMoldPosition", name: "慢速开模位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> SlowSpeedMoldPosition()
    {
        return await PowerOnData("SlowSpeedMoldPosition");
    }

    /// <summary>
    ///     慢速合模压力
    /// </summary>
    /// <returns></returns>
    [Method("SlowCombinePressure", name: "慢速合模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> SlowCombinePressure()
    {
        return await PowerOnData("SlowCombinePressure");
    }

    /// <summary>
    ///     慢速合模流量
    /// </summary>
    /// <returns></returns>
    [Method("SlowCombineFlow", name: "慢速合模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> SlowCombineFlow()
    {
        return await PowerOnData("SlowCombineFlow");
    }

    /// <summary>
    ///     差动锁模
    /// </summary>
    /// <returns></returns>
    [Method("DifferentialModeLock", name: "差动锁模", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> DifferentialModeLock()
    {
        return await PowerOnData("DifferentialModeLock");
    }


    /// <summary>
    ///     快速合模压力
    /// </summary>
    /// <returns></returns>
    [Method("QuickCombinePressure", name: "快速合模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> QuickCombinePressure()
    {
        return await PowerOnData("QuickCombinePressure");
    }

    /// <summary>
    ///     快速合模流量
    /// </summary>
    /// <returns></returns>
    [Method("QuickCombineFlow", name: "快速合模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> QuickCombineFlow()
    {
        return await PowerOnData("QuickCombineFlow");
    }

    /// <summary>
    ///     低压合模压力
    /// </summary>
    /// <returns></returns>
    [Method("LowCombinePressure", name: "低压合模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LowCombinePressure()
    {
        return await PowerOnData("LowCombinePressure");
    }

    /// <summary>
    ///     低压合模流量
    /// </summary>
    /// <returns></returns>
    [Method("LowCombineFlow", name: "低压合模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LowCombineFlow()
    {
        return await PowerOnData("LowCombineFlow");
    }

    /// <summary>
    ///     高压合模压力
    /// </summary>
    /// <returns></returns>
    [Method("HighCombinePressure", name: "高压合模压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HighCombinePressure()
    {
        return await PowerOnData("HighCombinePressure");
    }

    /// <summary>
    ///     高压合模流量
    /// </summary>
    /// <returns></returns>
    [Method("HighCombineFlow", name: "高压合模流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HighCombineFlow()
    {
        return await PowerOnData("HighCombineFlow");
    }

    /// <summary>
    ///     低压保护时间
    /// </summary>
    /// <returns></returns>
    [Method("LowPressureProtectionTime", name: "低压保护时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LowPressureProtectionTime()
    {
        return await PowerOnData("LowPressureProtectionTime");
    }

    /// <summary>
    ///     开合模限时
    /// </summary>
    /// <returns></returns>
    [Method("MoldClosingTimeLimit", name: "开合模限时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldClosingTimeLimit()
    {
        return await PowerOnData("MoldClosingTimeLimit");
    }


    /// <summary>
    ///     射出压力1
    /// </summary>
    /// <returns></returns>
    [Method("InjectionPressure1", name: "射出压力1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure1()
    {
        return await PowerOnData("InjectionPressure1");
    }

    /// <summary>
    ///     射出流量1
    /// </summary>
    /// <returns></returns>
    [Method("InjectionFlow1", name: "射出流量1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionFlow1()
    {
        return await PowerOnData("InjectionFlow1");
    }

    /// <summary>
    ///     射出压力2
    /// </summary>
    /// <returns></returns>
    [Method("InjectionPressure2", name: "射出压力2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure2()
    {
        return await PowerOnData("InjectionPressure2");
    }

    /// <summary>
    ///     射出流量2
    /// </summary>
    /// <returns></returns>
    [Method("InjectionFlow2", name: "射出流量2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionFlow2()
    {
        return await PowerOnData("InjectionFlow2");
    }

    /// <summary>
    ///     射出压力3
    /// </summary>
    /// <returns></returns>
    [Method("InjectionPressure3", name: "射出压力3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure3()
    {
        return await PowerOnData("InjectionPressure3");
    }

    /// <summary>
    ///     射出流量3
    /// </summary>
    /// <returns></returns>
    [Method("InjectionFlow3", name: "射出流量3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionFlow3()
    {
        return await PowerOnData("InjectionFlow3");
    }

    /// <summary>
    ///     上电数据
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> PowerOnData(string identifier)
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    case "LowSpeedMoldPressure":
                        ret.Value = _porCheSonData.LowSpeedMoldPressure;
                        break;
                    case "MediumSpeedMoldPressure":
                        ret.Value = _porCheSonData.MediumSpeedMoldPressure;
                        break;
                    case "FastSpeedMoldPressure":
                        ret.Value = _porCheSonData.FastSpeedMoldPressure;
                        break;
                    case "SlowSpeedMoldPressure":
                        ret.Value = _porCheSonData.SlowSpeedMoldPressure;
                        break;
                    case "LowSpeedMoldFlow":
                        ret.Value = _porCheSonData.LowSpeedMoldFlow;
                        break;
                    case "MediumSpeedMoldFlow":
                        ret.Value = _porCheSonData.MediumSpeedMoldFlow;
                        break;
                    case "FastSpeedMoldFlow":
                        ret.Value = _porCheSonData.FastSpeedMoldFlow;
                        break;
                    case "SlowSpeedMoldFlow":
                        ret.Value = _porCheSonData.SlowSpeedMoldFlow;
                        break;
                    case "LowSpeedMoldPosition":
                        ret.Value = _porCheSonData.LowSpeedMoldPosition;
                        break;
                    case "MediumSpeedMoldPosition":
                        ret.Value = _porCheSonData.MediumSpeedMoldPosition;
                        break;
                    case "FastSpeedMoldPosition":
                        ret.Value = _porCheSonData.FastSpeedMoldPosition;
                        break;
                    case "SlowSpeedMoldPosition":
                        ret.Value = _porCheSonData.SlowSpeedMoldPosition;
                        break;
                    case "SlowCombinePressure":
                        ret.Value = _porCheSonData.SlowCombinePressure;
                        break;
                    case "SlowCombineFlow":
                        ret.Value = _porCheSonData.SlowCombineFlow;
                        break;
                    case "DifferentialModeLock":
                        ret.Value = _porCheSonData.DifferentialModeLock;
                        ret.DataType = DataTypeEnum.Int32;
                        break;
                    case "QuickCombinePressure":
                        ret.Value = _porCheSonData.QuickCombinePressure;
                        break;
                    case "QuickCombineFlow":
                        ret.Value = _porCheSonData.QuickCombineFlow;
                        break;
                    case "LowCombinePressure":
                        ret.Value = _porCheSonData.LowCombinePressure;
                        break;
                    case "LowCombineFlow":
                        ret.Value = _porCheSonData.LowCombineFlow;
                        break;
                    case "HighCombinePressure":
                        ret.Value = _porCheSonData.HighCombinePressure;
                        break;
                    case "HighCombineFlow":
                        ret.Value = _porCheSonData.HighCombineFlow;
                        break;
                    case "LowPressureProtectionTime":
                        ret.Value = _porCheSonData.LowPressureProtectionTime;
                        break;
                    case "MoldClosingTimeLimit":
                        ret.Value = _porCheSonData.MoldClosingTimeLimit;
                        break;
                    case "InjectionPressure1":
                        ret.Value = _porCheSonData.InjectionPressure1;
                        break;
                    case "InjectionFlow1":
                        ret.Value = _porCheSonData.InjectionFlow1;
                        break;
                    case "InjectionPressure2":
                        ret.Value = _porCheSonData.InjectionPressure2;
                        break;
                    case "InjectionFlow2":
                        ret.Value = _porCheSonData.InjectionFlow2;
                        break;
                    case "InjectionPressure3":
                        ret.Value = _porCheSonData.InjectionPressure3;
                        break;
                    case "InjectionFlow3":
                        ret.Value = _porCheSonData.InjectionFlow3;
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion 上电数据

    #region 坐台/托模设定

    /// <summary>
    ///     托进快压力
    /// </summary>
    /// <returns></returns>
    [Method("MoldAdvanceFastPressure", name: "托进快压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldAdvanceFastPressure()
    {
        return await MoldOrTableSetting("MoldAdvanceFastPressure");
    }

    /// <summary>
    ///     托进慢压力
    /// </summary>
    /// <returns></returns>
    [Method("MoldAdvanceSlowPressure", name: "托进慢压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldAdvanceSlowPressure()
    {
        return await MoldOrTableSetting("MoldAdvanceSlowPressure");
    }

    /// <summary>
    ///     托进快流量
    /// </summary>
    /// <returns></returns>
    [Method("MoldAdvanceFastFlow", name: "托进快流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldAdvanceFastFlow()
    {
        return await MoldOrTableSetting("MoldAdvanceFastFlow");
    }

    /// <summary>
    ///     托进慢流量
    /// </summary>
    /// <returns></returns>
    [Method("MoldAdvanceSlowFlow", name: "托进慢流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldAdvanceSlowFlow()
    {
        return await MoldOrTableSetting("MoldAdvanceSlowFlow");
    }

    /// <summary>
    ///     托模退位置
    /// </summary>
    /// <returns></returns>
    [Method("MoldRetreatPosition", name: "托模退位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldRetreatPosition()
    {
        return await MoldOrTableSetting("MoldRetreatPosition");
    }

    /// <summary>
    ///     托进快位置
    /// </summary>
    /// <returns></returns>
    [Method("MoldAdvanceFastPosition", name: "托进快位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldAdvanceFastPosition()
    {
        return await MoldOrTableSetting("MoldAdvanceFastPosition");
    }

    /// <summary>
    ///     托进慢位置
    /// </summary>
    /// <returns></returns>
    [Method("MoldAdvanceSlowPosition", name: "托进快流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldAdvanceSlowPosition()
    {
        return await MoldOrTableSetting("MoldAdvanceSlowPosition");
    }

    /// <summary>
    ///     座台进慢压力
    /// </summary>
    /// <returns></returns>
    [Method("TableAdvanceSlowPressure", name: "座台进慢压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableAdvanceSlowPressure()
    {
        return await MoldOrTableSetting("TableAdvanceSlowPressure");
    }

    /// <summary>
    ///     座台进快压力
    /// </summary>
    /// <returns></returns>
    [Method("TableAdvanceFastPressure", name: "座台进快压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableAdvanceFastPressure()
    {
        return await MoldOrTableSetting("TableAdvanceFastPressure");
    }

    /// <summary>
    ///     座台进慢流量
    /// </summary>
    /// <returns></returns>
    [Method("TableAdvanceSlowFlow", name: "座台进慢流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableAdvanceSlowFlow()
    {
        return await MoldOrTableSetting("TableAdvanceSlowFlow");
    }

    /// <summary>
    ///     座台进快流量
    /// </summary>
    /// <returns></returns>
    [Method("TableAdvanceFastFlow", name: "座台进快流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableAdvanceFastFlow()
    {
        return await MoldOrTableSetting("TableAdvanceFastFlow");
    }

    /// <summary>
    ///     座台进慢时间
    /// </summary>
    /// <returns></returns>
    [Method("TableAdvanceSlowTime", name: "座台进慢时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableAdvanceSlowTime()
    {
        return await MoldOrTableSetting("TableAdvanceSlowTime");
    }

    /// <summary>
    ///     座台进快时间
    /// </summary>
    /// <returns></returns>
    [Method("TableAdvanceFastTime", name: "座台进快时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableAdvanceFastTime()
    {
        return await MoldOrTableSetting("TableAdvanceFastTime");
    }


    /// <summary>
    ///     座台退压力
    /// </summary>
    /// <returns></returns>
    [Method("TableRetreatPressure", name: "座台退压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableRetreatPressure()
    {
        return await MoldOrTableSetting("TableRetreatPressure");
    }

    /// <summary>
    ///     座台退流量
    /// </summary>
    /// <returns></returns>
    [Method("TableRetreatFlow", name: "座台退流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableRetreatFlow()
    {
        return await MoldOrTableSetting("TableRetreatFlow");
    }

    /// <summary>
    ///     座台退时间
    /// </summary>
    /// <returns></returns>
    [Method("TableRetreatTime", name: "座台退时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TableRetreatTime()
    {
        return await MoldOrTableSetting("TableRetreatTime");
    }


    /// <summary>
    ///     托模保持压力
    /// </summary>
    /// <returns></returns>
    [Method("MoldHoldPressure", name: "托模保持压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldHoldPressure()
    {
        return await MoldOrTableSetting("MoldHoldPressure");
    }


    /// <summary>
    ///     托模退压力
    /// </summary>
    /// <returns></returns>
    [Method("MoldRetreatPressure", name: "托模退压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldRetreatPressure()
    {
        return await MoldOrTableSetting("MoldRetreatPressure");
    }

    /// <summary>
    ///     托模退流量
    /// </summary>
    /// <returns></returns>
    [Method("MoldRetreatFlow", name: "托模退流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldRetreatFlow()
    {
        return await MoldOrTableSetting("MoldRetreatFlow");
    }

    /// <summary>
    ///     托模设定
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> MoldOrTableSetting(string identifier)
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    case "MoldAdvanceFastPressure":
                        ret.Value = _porCheSonData.MoldAdvanceFastPressure;
                        break;
                    case "MoldAdvanceSlowPressure":
                        ret.Value = _porCheSonData.MoldAdvanceSlowPressure;
                        break;
                    case "MoldAdvanceFastFlow":
                        ret.Value = _porCheSonData.MoldAdvanceFastFlow;
                        break;
                    case "MoldAdvanceSlowFlow":
                        ret.Value = _porCheSonData.MoldAdvanceSlowFlow;
                        break;
                    case "MoldRetreatPosition":
                        ret.Value = _porCheSonData.MoldRetreatPosition;
                        break;
                    case "MoldAdvanceFastPosition":
                        ret.Value = _porCheSonData.MoldAdvanceFastPosition;
                        break;
                    case "MoldAdvanceSlowPosition":
                        ret.Value = _porCheSonData.MoldAdvanceSlowPosition;
                        break;
                    case "TableRetreatPressure":
                        ret.Value = _porCheSonData.TableRetreatPressure;
                        break;
                    case "TableRetreatFlow":
                        ret.Value = _porCheSonData.TableRetreatFlow;
                        break;
                    case "TableRetreatTime":
                        ret.Value = _porCheSonData.TableRetreatTime;
                        break;
                    case "MoldHoldPressure":
                        ret.Value = _porCheSonData.MoldHoldPressure;
                        break;
                    case "MoldRetreatPressure":
                        ret.Value = _porCheSonData.MoldRetreatPressure;
                        break;
                    case "MoldRetreatFlow":
                        ret.Value = _porCheSonData.MoldRetreatFlow;
                        break;

                    case "TableAdvanceSlowPressure":
                        ret.Value = _porCheSonData.TableAdvanceSlowPressure;
                        break;
                    case "TableAdvanceFastPressure":
                        ret.Value = _porCheSonData.TableAdvanceFastPressure;
                        break;
                    case "TableAdvanceSlowFlow":
                        ret.Value = _porCheSonData.TableAdvanceSlowFlow;
                        break;
                    case "TableAdvanceFastFlow":
                        ret.Value = _porCheSonData.TableAdvanceFastFlow;
                        break;
                    case "TableAdvanceSlowTime":
                        ret.Value = _porCheSonData.TableAdvanceSlowTime;
                        break;
                    case "TableAdvanceFastTime":
                        ret.Value = _porCheSonData.TableAdvanceFastTime;
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion 托模设定

    #region 计时/计数设定

    /// <summary>
    ///     润滑总时
    /// </summary>
    /// <returns></returns>
    [Method("TotalLubricationTime", name: "润滑总时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TotalLubricationTime()
    {
        return await TimeOrCountSetting("TotalLubricationTime");
    }

    /// <summary>
    ///     润滑时间
    /// </summary>
    /// <returns></returns>
    [Method("LubricationTime", name: "润滑时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LubricationTime()
    {
        return await TimeOrCountSetting("LubricationTime");
    }

    /// <summary>
    ///     润滑间歇
    /// </summary>
    /// <returns></returns>
    [Method("LubricationInterval", name: "润滑间歇", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> LubricationInterval()
    {
        return await TimeOrCountSetting("LubricationInterval");
    }


    /// <summary>
    ///     润滑模数
    /// </summary>
    /// <returns></returns>
    [Method("LubricationCycle", name: "润滑模数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> LubricationCycle()
    {
        return await TimeOrCountSetting("LubricationCycle");
    }

    /// <summary>
    ///     循环等待时间
    /// </summary>
    /// <returns></returns>
    [Method("CycleWaitTime", name: "循环等待时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> CycleWaitTime()
    {
        return await TimeOrCountSetting("CycleWaitTime");
    }

    /// <summary>
    ///     手动动作限时
    /// </summary>
    /// <returns></returns>
    [Method("ManualActionTimeLimit", name: "手动动作限时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ManualActionTimeLimit()
    {
        return await TimeOrCountSetting("ManualActionTimeLimit");
    }

    /// <summary>
    ///     周期时间
    /// </summary>
    /// <returns></returns>
    [Method("CycleTime", name: "周期时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> CycleTime()
    {
        return await TimeOrCountSetting("CycleTime");
    }

    /// <summary>
    ///     故障告警时间
    /// </summary>
    /// <returns></returns>
    [Method("FaultWarningTime", name: "故障告警时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> FaultWarningTime()
    {
        return await TimeOrCountSetting("FaultWarningTime");
    }

    /// <summary>
    ///     产量
    /// </summary>
    /// <returns></returns>
    [Method("OpenedMoldCount", name: "产量", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> OpenedMoldCount()
    {
        return await TimeOrCountSetting("OpenedMoldCount");
    }

    /// <summary>
    ///     计时/计数设定
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> TimeOrCountSetting(string identifier)
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    case "TotalLubricationTime":
                        ret.Value = _porCheSonData.TotalLubricationTime;
                        break;
                    case "LubricationTime":
                        ret.Value = _porCheSonData.LubricationTime;
                        break;
                    case "LubricationInterval":
                        ret.Value = _porCheSonData.LubricationInterval;
                        break;
                    case "LubricationCycle":
                        ret.Value = _porCheSonData.LubricationCycle;
                        ret.DataType = DataTypeEnum.Int32;
                        break;
                    case "CycleWaitTime":
                        ret.Value = _porCheSonData.CycleWaitTime;
                        break;
                    case "ManualActionTimeLimit":
                        ret.Value = _porCheSonData.ManualActionTimeLimit;
                        break;
                    case "CycleTime":
                        ret.Value = _porCheSonData.CycleTime;
                        break;
                    case "FaultWarningTime":
                        ret.Value = _porCheSonData.FaultWarningTime;
                        break;
                    case "OpenedMoldCount":
                        ret.Value = _porCheSonData.OpenedMoldCount;
                        ret.DataType = DataTypeEnum.Int32;
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion

    #region 储料、射退、冷却设定

    /// <summary>
    ///     储料前射退压力
    /// </summary>
    /// <returns></returns>
    [Method("PreInjectionRetreatPressure", name: "储料前射退压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PreInjectionRetreatPressure()
    {
        return await ReadLubricationOrScrewColdSetting("PreInjectionRetreatPressure");
    }

    /// <summary>
    ///     储料压力1
    /// </summary>
    /// <returns></returns>
    [Method("MaterialPressure1", name: "储料压力1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialPressure1()
    {
        return await ReadLubricationOrScrewColdSetting("MaterialPressure1");
    }

    /// <summary>
    ///     储料压力2
    /// </summary>
    /// <returns></returns>
    [Method("MaterialPressure2", name: "储料压力2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialPressure2()
    {
        return await ReadLubricationOrScrewColdSetting("MaterialPressure2");
    }

    /// <summary>
    ///     储料后射退压力
    /// </summary>
    /// <returns></returns>
    [Method("PostInjectionRetreatPressure", name: "储料后射退压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PostInjectionRetreatPressure()
    {
        return await ReadLubricationOrScrewColdSetting("PostInjectionRetreatPressure");
    }

    /// <summary>
    ///     储料前射退背压
    /// </summary>
    /// <returns></returns>
    [Method("PreInjectionRetreatBackPressure", name: "储料前射退背压", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PreInjectionRetreatBackPressure()
    {
        return await ReadLubricationOrScrewColdSetting("PreInjectionRetreatBackPressure");
    }

    /// <summary>
    ///     储料背压1
    /// </summary>
    /// <returns></returns>
    [Method("MaterialBackPressure1", name: "储料背压1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialBackPressure1()
    {
        return await ReadLubricationOrScrewColdSetting("MaterialBackPressure1");
    }

    /// <summary>
    ///     储料背压2
    /// </summary>
    /// <returns></returns>
    [Method("MaterialBackPressure2", name: "储料背压2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialBackPressure2()
    {
        return await ReadLubricationOrScrewColdSetting("MaterialBackPressure2");
    }

    /// <summary>
    ///     储料后射退背压
    /// </summary>
    /// <returns></returns>
    [Method("PostInjectionRetreatBackPressure", name: "储料后射退背压", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PostInjectionRetreatBackPressure()
    {
        return await ReadLubricationOrScrewColdSetting("PostInjectionRetreatBackPressure");
    }

    /// <summary>
    ///     储料前射退流量
    /// </summary>
    /// <returns></returns>
    [Method("PreInjectionRetreatFlow", name: "储料前射退流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PreInjectionRetreatFlow()
    {
        return await ReadLubricationOrScrewColdSetting("PreInjectionRetreatFlow");
    }

    /// <summary>
    ///     储料流量1
    /// </summary>
    /// <returns></returns>
    [Method("MaterialFlow1", name: "储料流量1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialFlow1()
    {
        return await ReadLubricationOrScrewColdSetting("MaterialFlow1");
    }

    /// <summary>
    ///     储料流量2
    /// </summary>
    /// <returns></returns>
    [Method("MaterialFlow2", name: "储料流量2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialFlow2()
    {
        return await ReadLubricationOrScrewColdSetting("MaterialFlow2");
    }

    /// <summary>
    ///     储料后射退流量
    /// </summary>
    /// <returns></returns>
    [Method("PostInjectionRetreatFlow", name: "储料后射退流量", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PostInjectionRetreatFlow()
    {
        return await ReadLubricationOrScrewColdSetting("PostInjectionRetreatFlow");
    }

    /// <summary>
    ///     储料前射退位置
    /// </summary>
    /// <returns></returns>
    [Method("PreInjectionRetreatPosition", name: "储料前射退位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PreInjectionRetreatPosition()
    {
        return await ReadLubricationOrScrewColdSetting("PreInjectionRetreatPosition");
    }

    /// <summary>
    ///     储料位置1
    /// </summary>
    /// <returns></returns>
    [Method("MaterialPosition1", name: "储料位置1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialPosition1()
    {
        return await ReadLubricationOrScrewColdSetting("MaterialPosition1");
    }

    /// <summary>
    ///     储料位置2
    /// </summary>
    /// <returns></returns>
    [Method("MaterialPosition2", name: "储料位置2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialPosition2()
    {
        return await ReadLubricationOrScrewColdSetting("MaterialPosition2");
    }

    /// <summary>
    ///     储料后射退位置
    /// </summary>
    /// <returns></returns>
    [Method("PostInjectionRetreatPosition", name: "储料后射退位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> PostInjectionRetreatPosition()
    {
        return await ReadLubricationOrScrewColdSetting("PostInjectionRetreatPosition");
    }

    /// <summary>
    ///     冷却时间
    /// </summary>
    /// <returns></returns>
    [Method("CoolingTime", name: "冷却时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> CoolingTime()
    {
        return await ReadLubricationOrScrewColdSetting("CoolingTime");
    }

    /// <summary>
    ///     储料限时
    /// </summary>
    /// <returns></returns>
    [Method("MaterialLimitTime", name: "储料限时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialLimitTime()
    {
        return await ReadLubricationOrScrewColdSetting("MaterialLimitTime");
    }


    /// <summary>
    ///     储料、射退、冷却设定
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> ReadLubricationOrScrewColdSetting(string identifier)
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    case "PreInjectionRetreatPressure":
                        ret.Value = _porCheSonData.PreInjectionRetreatPressure;
                        break;
                    case "MaterialPressure1":
                        ret.Value = _porCheSonData.MaterialPressure1;
                        break;
                    case "MaterialPressure2":
                        ret.Value = _porCheSonData.MaterialPressure2;
                        break;

                    case "PostInjectionRetreatPressure":
                        ret.Value = _porCheSonData.PostInjectionRetreatPressure;
                        break;
                    case "PreInjectionRetreatBackPressure":
                        ret.Value = _porCheSonData.PreInjectionRetreatBackPressure;
                        break;
                    case "MaterialBackPressure1":
                        ret.Value = _porCheSonData.MaterialBackPressure1;
                        break;
                    case "MaterialBackPressure2":
                        ret.Value = _porCheSonData.MaterialBackPressure2;
                        break;
                    case "PostInjectionRetreatBackPressure":
                        ret.Value = _porCheSonData.PostInjectionRetreatBackPressure;
                        break;
                    case "PreInjectionRetreatFlow":
                        ret.Value = _porCheSonData.PreInjectionRetreatFlow;
                        break;
                    case "MaterialFlow1":
                        ret.Value = _porCheSonData.MaterialFlow1;
                        break;
                    case "MaterialFlow2":
                        ret.Value = _porCheSonData.MaterialFlow2;
                        break;
                    case "PostInjectionRetreatFlow":
                        ret.Value = _porCheSonData.PostInjectionRetreatFlow;
                        break;
                    case "PreInjectionRetreatPosition":
                        ret.Value = _porCheSonData.PreInjectionRetreatPosition;
                        break;
                    case "MaterialPosition1":
                        ret.Value = _porCheSonData.MaterialPosition1;
                        break;
                    case "MaterialPosition2":
                        ret.Value = _porCheSonData.MaterialPosition2;
                        break;
                    case "PostInjectionRetreatPosition":
                        ret.Value = _porCheSonData.PostInjectionRetreatPosition;
                        break;
                    case "CoolingTime":
                        ret.Value = _porCheSonData.CoolingTime;
                        break;
                    case "MaterialLimitTime":
                        ret.Value = _porCheSonData.MaterialLimitTime;
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion 储料、射退、冷却设定

    #region 温度设定

    /// <summary>
    ///     获取温度设定1
    /// </summary>
    /// <returns>温度设定1的值</returns>
    [Method("TemperatureSetting1", name: "温度设定1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting1()
    {
        return await TemperatureSetting("TemperatureSetting1");
    }

    /// <summary>
    ///     获取温度设定2
    /// </summary>
    /// <returns>温度设定2的值</returns>
    [Method("TemperatureSetting2", name: "温度设定2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting2()
    {
        return await TemperatureSetting("TemperatureSetting2");
    }

    /// <summary>
    ///     获取温度设定3
    /// </summary>
    /// <returns>温度设定3的值</returns>
    [Method("TemperatureSetting3", name: "温度设定3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting3()
    {
        return await TemperatureSetting("TemperatureSetting3");
    }

    /// <summary>
    ///     获取温度设定4
    /// </summary>
    /// <returns>温度设定4的值</returns>
    [Method("TemperatureSetting4", name: "温度设定4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureSetting4()
    {
        return await TemperatureSetting("TemperatureSetting4");
    }

    /// <summary>
    ///     获取温度设定上限1
    /// </summary>
    /// <returns>温度设定上限1的值</returns>
    [Method("TemperatureUpperLimit1", name: "温度设定上限1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureUpperLimit1()
    {
        return await TemperatureSetting("TemperatureUpperLimit1");
    }

    /// <summary>
    ///     获取温度设定上限2
    /// </summary>
    /// <returns>温度设定上限2的值</returns>
    [Method("TemperatureUpperLimit2", name: "温度设定上限2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureUpperLimit2()
    {
        return await TemperatureSetting("TemperatureUpperLimit2");
    }

    /// <summary>
    ///     获取温度设定上限3
    /// </summary>
    /// <returns>温度设定上限3的值</returns>
    [Method("TemperatureUpperLimit3", name: "温度设定上限3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureUpperLimit3()
    {
        return await TemperatureSetting("TemperatureUpperLimit3");
    }

    /// <summary>
    ///     获取温度设定下限1
    /// </summary>
    /// <returns>温度设定下限1的值</returns>
    [Method("TemperatureLowerLimit1", name: "温度设定下限1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureLowerLimit1()
    {
        return await TemperatureSetting("TemperatureLowerLimit1");
    }

    /// <summary>
    ///     获取温度设定下限2
    /// </summary>
    /// <returns>温度设定下限2的值</returns>
    [Method("TemperatureLowerLimit2", name: "温度设定下限2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureLowerLimit2()
    {
        return await TemperatureSetting("TemperatureLowerLimit2");
    }

    /// <summary>
    ///     获取温度设定下限3
    /// </summary>
    /// <returns>温度设定下限3的值</returns>
    [Method("TemperatureLowerLimit3", name: "温度设定下限3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperatureLowerLimit3()
    {
        return await TemperatureSetting("TemperatureLowerLimit3");
    }

    /// <summary>
    ///     温度设定
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> TemperatureSetting(string identifier)
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    case "TemperatureSetting1":
                        ret.Value = _porCheSonData.TemperatureSetting1;
                        break;
                    case "TemperatureSetting2":
                        ret.Value = _porCheSonData.TemperatureSetting2;
                        break;
                    case "TemperatureSetting3":
                        ret.Value = _porCheSonData.TemperatureSetting3;
                        break;
                    case "TemperatureSetting4":
                        ret.Value = _porCheSonData.TemperatureSetting4;
                        break;
                    case "TemperatureUpperLimit1":
                        ret.Value = _porCheSonData.TemperatureUpperLimit1;
                        break;
                    case "TemperatureUpperLimit2":
                        ret.Value = _porCheSonData.TemperatureUpperLimit2;
                        break;
                    case "TemperatureUpperLimit3":
                        ret.Value = _porCheSonData.TemperatureUpperLimit3;
                        break;
                    case "TemperatureLowerLimit1":
                        ret.Value = _porCheSonData.TemperatureLowerLimit1;
                        break;
                    case "TemperatureLowerLimit2":
                        ret.Value = _porCheSonData.TemperatureLowerLimit2;
                        break;
                    case "TemperatureLowerLimit3":
                        ret.Value = _porCheSonData.TemperatureLowerLimit3;
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion 温度设定

    #region 实时温度

    /// <summary>
    ///     实际射砠温度
    /// </summary>
    /// <returns>温度1的值</returns>
    [Method("ActualInjectionTemperature", name: "实际射砠温度", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetActualInjectionTemperature()
    {
        return await RealTimeTemperature("ActualInjectionTemperature");
    }

    /// <summary>
    ///     获取温度1
    /// </summary>
    /// <returns>温度1的值</returns>
    [Method("Temperature1", name: "温度1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature1()
    {
        return await RealTimeTemperature("Temperature1");
    }

    /// <summary>
    ///     获取温度2
    /// </summary>
    /// <returns>温度2的值</returns>
    [Method("Temperature2", name: "温度2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature2()
    {
        return await RealTimeTemperature("Temperature2");
    }

    /// <summary>
    ///     获取温度3
    /// </summary>
    /// <returns>温度3的值</returns>
    [Method("Temperature3", name: "温度3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature3()
    {
        return await RealTimeTemperature("Temperature3");
    }

    /// <summary>
    ///     获取温度4
    /// </summary>
    /// <returns>温度4的值</returns>
    [Method("Temperature4", name: "温度4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetTemperature4()
    {
        return await RealTimeTemperature("Temperature4");
    }

    /// <summary>
    ///     获取良品数
    /// </summary>
    /// <returns>良品数的值</returns>
    [Method("GoodCount", name: "良品数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetGoodCount()
    {
        return await RealTimeTemperature("GoodCount");
    }


    /// <summary>
    ///     实时温度
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> RealTimeTemperature(string identifier)
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    case "ActualInjectionTemperature":
                        ret.Value = _porCheSonData.ActualInjectionTemperature;
                        break;
                    case "Temperature1":
                        ret.Value = _porCheSonData.Temperature1;
                        break;
                    case "Temperature2":
                        ret.Value = _porCheSonData.Temperature2;
                        break;
                    case "Temperature3":
                        ret.Value = _porCheSonData.Temperature3;
                        break;
                    case "Temperature4":
                        ret.Value = _porCheSonData.Temperature4;
                        break;
                    case "GoodCount":
                        ret.Value = _porCheSonData.GoodCount;
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion

    /// <summary>
    ///     模式
    /// </summary>
    /// <returns></returns>
    [Method("Mode", name: "模式", dataType: TransPondDataTypeEnum.Int, description: "模式 1：马达开；2：手动；3：半自动；4：全自动")]
    public async Task<DriverReturnValueModel> Mode()
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Int32 };
        try
        {
            if (IsConnected)
            {
                ret.Value = (int)_porCheSonData.Mode;
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion 采集点位

    /// <summary>
    ///     关闭连接,串口协议需要自行重写该方法
    /// </summary>
    public override void Close()
    {
        _ = SaveContent();
        _client1?.Dispose();
        _client2?.Dispose();
    }

    /// <summary>
    ///     释放连接,串口协议需要自行重写该方法
    /// </summary>
    public override void Dispose()
    {
        // 停止定时器
        if (_timer != null)
        {
            _timer.Elapsed -= TimerElapsed;
            _timer.Stop();
            _timer.Dispose();
        }

        _client1?.Dispose();
        _client2?.Dispose();
    }

    private readonly Timer _timer;

    /// <summary>
    ///     持久化内容
    /// </summary>
    private async Task SaveContent()
    {
        // 持久化最后的数据
        var data = _porCheSonData.ToJsonString();
        // 设置文件路径
        var filePath = "/Edge/Porcheson_Ps660Am.txt";
        // 写入数据到文件
        await File.WriteAllTextAsync(filePath, data);
    }

    /// <summary>
    ///     读取持久化内容
    /// </summary>
    private void ReadContent()
    {
        try
        {
            // 设置文件路径
            var filePath = "/Edge/Porcheson_Ps660Am.txt";
            if (File.Exists(filePath))
            {
                // 读取文件内容
                var data = File.ReadAllText(filePath);
                if (!string.IsNullOrWhiteSpace(data))
                {
                    try
                    {
                        _porCheSonData = JSON.Deserialize<PorchesonData>(data);
                    }
                    catch
                    {
                        // JSON解析失败时使用新实例
                        _porCheSonData = new PorchesonData();
                        // 重新写入文件
                        File.WriteAllTextAsync(filePath, _porCheSonData.ToJsonString());
                    }
                }
                else
                {
                    // 文件为空时使用新实例
                    _porCheSonData = new PorchesonData();
                    File.WriteAllTextAsync(filePath, _porCheSonData.ToJsonString());
                }
            }
            else
            {
                // 文件不存在时创建新文件
                _porCheSonData = new PorchesonData();
                File.WriteAllTextAsync(filePath, _porCheSonData.ToJsonString());
            }

            File.WriteAllTextAsync("/Edge/type.txt", "Porcheson_Ps660Am");
        }
        catch (Exception ex)
        {
            Log.Error($"【宝捷信AM】读取持久化内容异常: {ex.Message}");
            // 发生异常时使用新实例
            _porCheSonData = new PorchesonData();
        }
    }

    /// <summary>
    ///     持久化内容
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void TimerElapsed(object sender, ElapsedEventArgs e)
    {
        _ = SaveContent();
    }
}