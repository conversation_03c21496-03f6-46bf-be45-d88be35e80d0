using System;
using System.IO;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Timers;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Feng.Common.Util;
using Furion.JsonSerialization;
using Furion.Logging;
using HslCommunication;
using Timer = System.Timers.Timer;

namespace HongXunAkDouble;

/* 弘讯
 * 更新日志
 * 版本:v1.2.1
 *  修复串口只收一路数据问题
 * 版本:v1.1.1
 *  注塑机协议根据要求强制改为根据命名写入某个固定文件 - 2024-05-21 -wangj
 */
[DriverSupported("HongXunAkDouble")]
[DriverInfo("HongXunAkDouble", "V1.2.1", "注塑机")]
public class HongXunAkDouble : BaseDeviceProtocolCollector, IDriver
{
    /// <summary>
    ///     重连周期
    /// </summary>
    [ConfigParameter("重连周期(s)", GroupName = "高级配置", Remark = "当设备关机后下一轮进行重新连接的时间")]
    public override int ReConnTime { get; set; } = 5;

    [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置", Display = false)]
    public override int WriteInterval { get; set; } = 120;

    [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    [ConfigParameter("连接方式", order: 1)] public DriverConnectTypeEnum ConnectType { get; set; } = DriverConnectTypeEnum.SerialPort;

    #region 网口配置

    [ConfigParameter("IP地址", Display = false, DisplayExpress = "{\"ConnectType\": [\"1\"]}")]
    public string IpAddress { get; set; } = "127.0.0.1";

    [ConfigParameter("端口号", Display = false, DisplayExpress = "{\"ConnectType\": [\"1\"]}")]
    public int Port { get; set; } = 8001;

    [ConfigParameter("端口号2", Display = false, DisplayExpress = "{\"ConnectType\": [\"1\"]}")]
    public int Port2 { get; set; } = 8002;

    #endregion 网口配置

    #region 串口配置

    [ConfigParameter("串口号", Display = false, Remark = "", DisplayExpress = "{\"ConnectType\": [\"2\"]}")]
    public SerialNumberEnum SerialNumber { get; set; } = SerialNumberEnum.ttyS3;

    [ConfigParameter("串口号2", Display = false, Remark = "", DisplayExpress = "{\"ConnectType\": [\"2\"]}")]
    public SerialNumberEnum SerialNumber2 { get; set; } = SerialNumberEnum.ttyS4;

    #endregion 串口配置

    private ClientHandler _client1;
    private ClientHandler _client2;
    private HongXunAkDoubleData _techMationAkData = new();
    private bool _pingIp = true;

    public HongXunAkDouble(DriverInfoDto driverInfo)
    {
        try
        {
            DriverInfo = driverInfo;
            ReadContent();

            // 创建一个 Timer 实例，设置定时器间隔为 10 秒
            _timer = new Timer(1000 * 10);
            _timer.Elapsed += TimerElapsed;

            // 启动定时器
            _timer.Start();
        }
        catch (Exception e)
        {
            Log.Error($"【宏讯AK】 初始化异常:{e.Message}");
            // 确保即使发生异常也有默认数据
            _techMationAkData = new HongXunAkDoubleData();
        }
    }

    public override bool IsConnected => _client1 != null && _client2 != null && (_client1.IsConnected || _client2.IsConnected) && _pingIp;

    public DeviceConnectDto Connect()
    {
        try
        {
            _client1 = new ClientHandler(_techMationAkData, DriverInfo);
            _client2 = new ClientHandler(_techMationAkData, DriverInfo);
            if (ConnectType == DriverConnectTypeEnum.NetWork)
            {
                _client1.Connect(IpAddress, Port);
                _client2.Connect(IpAddress, Port2);
            }
            else
            {
                _client1.Connect(EnumUtil.GetEnumDesc(SerialNumber));
                _client2.Connect(EnumUtil.GetEnumDesc(SerialNumber2));
            }

            IsConnected = _client1.IsConnected && _client2.IsConnected;
            OperateResult.IsSuccess = IsConnected;
            OperateResult.Message = IsConnected ? "连接成功" : "连接失败";
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }

    /// <summary>
    ///     关闭连接,串口协议需要自行重写该方法
    /// </summary>
    public override void Close()
    {
        _ = SaveContent();
        _client1?.Dispose();
        _client2?.Dispose();
    }

    /// <summary>
    ///     释放连接,串口协议需要自行重写该方法
    /// </summary>
    public override void Dispose()
    {
        // 停止定时器
        if (_timer != null)
        {
            _timer.Elapsed -= TimerElapsed;
            _timer.Stop();
            _timer.Dispose();
        }

        _client1?.Dispose();
        _client2?.Dispose();
    }

    private readonly Timer _timer;
    private readonly Ping _ping = new();

    /// <summary>
    ///     持久化内容
    /// </summary>
    private async Task SaveContent()
    {
        // 区分是网口还是串口
        if (ConnectType == DriverConnectTypeEnum.NetWork)
        {
            // 持久化最后的数据
            var data = _techMationAkData.ToJsonString();
            // 设置文件路径
            var filePath = $"/Edge/{DriverInfo.DeviceName}.txt";
            // 写入数据到文件
            await File.WriteAllTextAsync(filePath, data);
        }
        else
        {
            // 持久化最后的数据
            var data = _techMationAkData.ToJsonString();
            // 设置文件路径
            var filePath = "/Edge/HongXunAkDouble.txt";
            // 写入数据到文件
            await File.WriteAllTextAsync(filePath, data);
        }
    }

    /// <summary>
    ///     读取持久化内容
    /// </summary>
    private void ReadContent()
    {
        try
        {
            // 设置文件路径
            string filePath = "";
            if (ConnectType == DriverConnectTypeEnum.NetWork)
            {
                filePath = $"/Edge/{DriverInfo.DeviceName}.txt";
            }
            else
            {
                filePath = "/Edge/HongXunAkDouble.txt";
            }

            // 初始化默认数据
            _techMationAkData = new HongXunAkDoubleData();
            // 如果文件存在，则读取文件内容
            if (File.Exists(filePath))
            {
                // 读取文件内容
                var data = File.ReadAllText(filePath);
                // 如果文件内容不为空，则尝试反序列化
                if (!string.IsNullOrWhiteSpace(data))
                {
                    try
                    {
                        // 反序列化
                        _techMationAkData = JSON.Deserialize<HongXunAkDoubleData>(data);
                    }
                    catch (Exception ex)
                    {
                        Log.Warning($"【宏讯AK】 解析缓存文件失败:{ex.Message}，将使用默认值");
                    }
                }
            }

            // 无论文件是否存在或解析是否成功，都确保写入当前数据
            File.WriteAllTextAsync(filePath, _techMationAkData.ToJsonString());
            File.WriteAllTextAsync("/Edge/type.txt", "HongXunAkDouble");
        }
        catch (Exception ex)
        {
            Log.Error($"【宏讯AK】 读取缓存文件异常:{ex.Message}");
            // 发生异常时确保有默认数据
            _techMationAkData = new HongXunAkDoubleData();
        }
    }

    /// <summary>
    ///     持久化内容
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void TimerElapsed(object sender, ElapsedEventArgs e)
    {
        try
        {
            // 检查是否连接成功
            if (_client1 is { IsConnected: true } || _client2 is { IsConnected: true })
            {
                // 发送ping请求
                var ping = _ping.Send(IpAddress, 1000);
                // 检查ping请求是否成功
                var pingRes = ping!.Status == IPStatus.Success;
                // 更新ping状态
                _pingIp = pingRes;
            }
        }
        catch
        {
            // ignored
        }

        _ = SaveContent();
    }

    #region 采集点位

    #region 座台设定

    /// <summary>
    ///     座台进动作时间
    /// </summary>
    /// <returns></returns>
    [Method("SeatAdvanceActionTime", name: "座台进动作时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> SeatAdvanceActionTime()
    {
        return await PowerOnData("SeatAdvanceActionTime");
    }

    /// <summary>
    ///     座台进一段压力
    /// </summary>
    /// <returns></returns>
    [Method("CoreAForwardPressure1", name: "座台进一段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> CoreAForwardPressure1()
    {
        return await PowerOnData("CoreAForwardPressure1");
    }

    /// <summary>
    ///     座台进一段速度
    /// </summary>
    /// <returns></returns>
    [Method("CoreAForwardSpeed1", name: "座台进一段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> CoreAForwardSpeed1()
    {
        return await PowerOnData("CoreAForwardSpeed1");
    }

    /// <summary>
    ///     座台进二段压力
    /// </summary>
    /// <returns></returns>
    [Method("CoreAForwardPressure2", name: "座台进二段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> CoreAForwardPressure2()
    {
        return await PowerOnData("CoreAForwardPressure2");
    }

    /// <summary>
    ///     座台进二段速度
    /// </summary>
    /// <returns></returns>
    [Method("CoreAForwardSpeed2", name: "座台进二段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> CoreAForwardSpeed2()
    {
        return await PowerOnData("CoreAForwardSpeed2");
    }

    /// <summary>
    ///     座台退一段压力
    /// </summary>
    /// <returns></returns>
    [Method("CoreAReversePressure1", name: "座台退一段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> CoreAReversePressure1()
    {
        return await PowerOnData("CoreAReversePressure1");
    }

    /// <summary>
    ///     座台退一段速度
    /// </summary>
    /// <returns></returns>
    [Method("CoreAReverseSpeed1", name: "座台退一段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> CoreAReverseSpeed1()
    {
        return await PowerOnData("CoreAReverseSpeed1");
    }

    /// <summary>
    ///     座台退动作时间
    /// </summary>
    /// <returns></returns>
    [Method("CoreAReverseTime", name: "座台退动作时间", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> CoreAReverseTime()
    {
        return await PowerOnData("CoreAReverseTime");
    }

    #endregion

    #region 温度设定1 -7

    /// <summary>
    ///     A腔温度设定1
    /// </summary>
    /// <returns></returns>
    [Method("A_TempSet1", name: "A腔温度设定1", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempSet1()
    {
        return await TimeOrCountSetting("A_TempSet1");
    }

    /// <summary>
    ///     A腔温度设定2
    /// </summary>
    /// <returns></returns>
    [Method("A_TempSet2", name: "A腔温度设定2", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempSet2()
    {
        return await TimeOrCountSetting("A_TempSet2");
    }

    /// <summary>
    ///     温度设定3
    /// </summary>
    /// <returns></returns>
    [Method("A_TempSet3", name: "A腔温度设定3", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempSet3()
    {
        return await TimeOrCountSetting("A_TempSet3");
    }

    /// <summary>
    ///     A腔温度设定4
    /// </summary>
    /// <returns></returns>
    [Method("A_TempSet4", name: "A腔温度设定4", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempSet4()
    {
        return await TimeOrCountSetting("A_TempSet4");
    }

    /// <summary>
    ///     A腔温度设定5
    /// </summary>
    /// <returns></returns>
    [Method("A_TempSet5", name: "A腔温度设定5", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempSet5()
    {
        return await TimeOrCountSetting("A_TempSet5");
    }

    /// <summary>
    ///     A腔温度设定6
    /// </summary>
    /// <returns></returns>
    [Method("A_TempSet6", name: "A腔温度设定6", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempSet6()
    {
        return await TimeOrCountSetting("A_TempSet6");
    }

    /// <summary>
    ///     A腔温度设定7
    /// </summary>
    /// <returns></returns>
    [Method("A_TempSet7", name: "A腔温度设定7", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempSet7()
    {
        return await TimeOrCountSetting("A_TempSet7");
    }

    #endregion 温度设定1 -7

    #region A_实际温度1-7

    /// <summary>
    ///     A腔实际温度1
    /// </summary>
    /// <returns></returns>
    [Method("A_TempAct1", name: "A腔实际温度1", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempAct1()
    {
        return await TimeOrCountSetting("A_TempAct1");
    }

    /// <summary>
    ///     A腔实际温度2
    /// </summary>
    /// <returns></returns>
    [Method("A_TempAct2", name: "A腔实际温度2", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempAct2()
    {
        return await TimeOrCountSetting("A_TempAct2");
    }

    /// <summary>
    ///     A腔实际温度3
    /// </summary>
    /// <returns></returns>
    [Method("A_TempAct3", name: "A腔实际温度3", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempAct3()
    {
        return await TimeOrCountSetting("A_TempAct3");
    }

    /// <summary>
    ///     A腔实际温度4
    /// </summary>
    /// <returns></returns>
    [Method("A_TempAct4", name: "A腔实际温度4", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempAct4()
    {
        return await TimeOrCountSetting("A_TempAct4");
    }

    /// <summary>
    ///     A腔实际温度5
    /// </summary>
    /// <returns></returns>
    [Method("A_TempAct5", name: "A腔实际温度5", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempAct5()
    {
        return await TimeOrCountSetting("A_TempAct5");
    }

    /// <summary>
    ///     A腔实际温度6
    /// </summary>
    /// <returns></returns>
    [Method("A_TempAct6", name: "A腔实际温度6", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempAct6()
    {
        return await TimeOrCountSetting("A_TempAct6");
    }

    /// <summary>
    ///     A腔实际温度7
    /// </summary>
    /// <returns></returns>
    [Method("A_TempAct7", name: "A腔实际温度7", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> A_TempAct7()
    {
        return await TimeOrCountSetting("A_TempAct7");
    }

    #endregion A_实际温度1-7

    #region B腔温度设定1-7

    /// <summary>
    ///     B腔温度设定1
    /// </summary>
    /// <returns></returns>
    [Method("B_TempSet1", name: "B腔温度设定1", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempSet1()
    {
        return await TimeOrCountSetting("B_TempSet1");
    }

    /// <summary>
    ///     B腔温度设定2
    /// </summary>
    /// <returns></returns>
    [Method("B_TempSet2", name: "B腔温度设定2", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempSet2()
    {
        return await TimeOrCountSetting("B_TempSet2");
    }

    /// <summary>
    ///     B腔温度设定3
    /// </summary>
    /// <returns></returns>
    [Method("B_TempSet3", name: "B腔温度设定3", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempSet3()
    {
        return await TimeOrCountSetting("B_TempSet3");
    }

    /// <summary>
    ///     B腔温度设定4
    /// </summary>
    /// <returns></returns>
    [Method("B_TempSet4", name: "B腔温度设定4", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempSet4()
    {
        return await TimeOrCountSetting("B_TempSet4");
    }

    /// <summary>
    ///     B腔温度设定5
    /// </summary>
    /// <returns></returns>
    [Method("B_TempSet5", name: "B腔温度设定5", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempSet5()
    {
        return await TimeOrCountSetting("B_TempSet5");
    }

    /// <summary>
    ///     B腔温度设定6
    /// </summary>
    /// <returns></returns>
    [Method("B_TempSet6", name: "B腔温度设定6", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempSet6()
    {
        return await TimeOrCountSetting("B_TempSet6");
    }

    /// <summary>
    ///     B腔温度设定7
    /// </summary>
    /// <returns></returns>
    [Method("B_TempSet7", name: "B腔温度设定7", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempSet7()
    {
        return await TimeOrCountSetting("B_TempSet7");
    }

    #endregion B腔温度设定1-7

    #region B腔实际温度1-7

    /// <summary>
    ///     B腔实际温度1
    /// </summary>
    /// <returns></returns>
    [Method("B_TempAct1", name: "B腔实际温度1", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempAct1()
    {
        return await TimeOrCountSetting("B_TempAct1");
    }

    /// <summary>
    ///     B腔实际温度2
    /// </summary>
    /// <returns></returns>
    [Method("B_TempAct2", name: "B腔实际温度2", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempAct2()
    {
        return await TimeOrCountSetting("B_TempAct2");
    }

    /// <summary>
    ///     B腔实际温度3
    /// </summary>
    /// <returns></returns>
    [Method("B_TempAct3", name: "B腔实际温度3", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempAct3()
    {
        return await TimeOrCountSetting("B_TempAct3");
    }

    /// <summary>
    ///     B腔实际温度4
    /// </summary>
    /// <returns></returns>
    [Method("B_TempAct4", name: "B腔实际温度4", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempAct4()
    {
        return await TimeOrCountSetting("B_TempAct4");
    }

    /// <summary>
    ///     B腔实际温度5
    /// </summary>
    /// <returns></returns>
    [Method("B_TempAct5", name: "B腔实际温度5", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempAct5()
    {
        return await TimeOrCountSetting("B_TempAct5");
    }

    /// <summary>
    ///     B腔实际温度6
    /// </summary>
    /// <returns></returns>
    [Method("B_TempAct6", name: "B腔实际温度6", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempAct6()
    {
        return await TimeOrCountSetting("B_TempAct6");
    }

    /// <summary>
    ///     B腔实际温度7
    /// </summary>
    /// <returns></returns>
    [Method("B_TempAct7", name: "B腔实际温度7", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> B_TempAct7()
    {
        return await TimeOrCountSetting("B_TempAct7");
    }

    #endregion B腔实际温度1-7

    #region 开关模设定

    /// <summary>
    ///     关模一段位置
    /// </summary>
    [Method("MoldClosingPosition1", name: "关模一段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldClosingPosition1()
    {
        return await TimeOrCountSetting("MoldClosingPosition1");
    }

    /// <summary>
    ///     关模二段位置
    /// </summary>
    [Method("MoldClosingPosition2", name: "关模二段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldClosingPosition2()
    {
        return await TimeOrCountSetting("MoldClosingPosition2");
    }

    /// <summary>
    ///     关模三段位置
    /// </summary>
    [Method("MoldClosingPosition3", name: "关模三段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldClosingPosition3()
    {
        return await TimeOrCountSetting("MoldClosingPosition3");
    }

    /// <summary>
    ///     关模四段位置
    /// </summary>
    [Method("MoldClosingPosition4", name: "关模四段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldClosingPosition4()
    {
        return await TimeOrCountSetting("MoldClosingPosition4");
    }

    /// <summary>
    ///     关模五段位置
    /// </summary>
    [Method("MoldClosingPosition5", name: "关模五段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldClosingPosition5()
    {
        return await TimeOrCountSetting("MoldClosingPosition5");
    }

    /// <summary>
    ///     关模一段压力
    /// </summary>
    [Method("MoldClosingPressure1", name: "关模一段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldClosingPressure1()
    {
        return await TimeOrCountSetting("MoldClosingPressure1");
    }

    /// <summary>
    ///     关模二段压力
    /// </summary>
    [Method("MoldClosingPressure2", name: "关模二段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldClosingPressure2()
    {
        return await TimeOrCountSetting("MoldClosingPressure2");
    }

    /// <summary>
    ///     关模三段压力
    /// </summary>
    [Method("MoldClosingPressure3", name: "关模三段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldClosingPressure3()
    {
        return await TimeOrCountSetting("MoldClosingPressure3");
    }

    /// <summary>
    ///     关模四段压力
    /// </summary>
    [Method("MoldClosingPressure4", name: "关模四段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldClosingPressure4()
    {
        return await TimeOrCountSetting("MoldClosingPressure4");
    }

    /// <summary>
    ///     关模五段压力
    /// </summary>
    [Method("MoldClosingPressure5", name: "关模五段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldClosingPressure5()
    {
        return await TimeOrCountSetting("MoldClosingPressure5");
    }

    /// <summary>
    ///     关模一段速度
    /// </summary>
    [Method("MoldClosingSpeed1", name: "关模一段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldClosingSpeed1()
    {
        return await TimeOrCountSetting("MoldClosingSpeed1");
    }

    /// <summary>
    ///     关模二段速度
    /// </summary>
    [Method("MoldClosingSpeed2", name: "关模二段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldClosingSpeed2()
    {
        return await TimeOrCountSetting("MoldClosingSpeed2");
    }

    /// <summary>
    ///     关模三段速度
    /// </summary>
    [Method("MoldClosingSpeed3", name: "关模三段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldClosingSpeed3()
    {
        return await TimeOrCountSetting("MoldClosingSpeed3");
    }

    /// <summary>
    ///     关模四段速度
    /// </summary>
    [Method("MoldClosingSpeed4", name: "关模四段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldClosingSpeed4()
    {
        return await TimeOrCountSetting("MoldClosingSpeed4");
    }

    /// <summary>
    ///     关模五段速度
    /// </summary>
    [Method("MoldClosingSpeed5", name: "关模五段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldClosingSpeed5()
    {
        return await TimeOrCountSetting("MoldClosingSpeed5");
    }

    /// <summary>
    ///     模具冷却时间
    /// </summary>
    [Method("MoldCoolingTime", name: "模具冷却时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldCoolingTime()
    {
        return await TimeOrCountSetting("MoldCoolingTime");
    }

    /// <summary>
    ///     再循环计时
    /// </summary>
    [Method("RecyclingTime", name: "再循环计时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> RecyclingTime()
    {
        return await TimeOrCountSetting("RecyclingTime");
    }

    /// <summary>
    ///     开模一段压力
    /// </summary>
    [Method("MoldOpeningPressure1", name: "开模一段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldOpeningPressure1()
    {
        return await TimeOrCountSetting("MoldOpeningPressure1");
    }

    /// <summary>
    ///     开模二段压力
    /// </summary>
    [Method("MoldOpeningPressure2", name: "开模二段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldOpeningPressure2()
    {
        return await TimeOrCountSetting("MoldOpeningPressure2");
    }

    /// <summary>
    ///     开模三段压力
    /// </summary>
    [Method("MoldOpeningPressure3", name: "开模三段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldOpeningPressure3()
    {
        return await TimeOrCountSetting("MoldOpeningPressure3");
    }

    /// <summary>
    ///     开模四段压力
    /// </summary>
    [Method("MoldOpeningPressure4", name: "开模四段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldOpeningPressure4()
    {
        return await TimeOrCountSetting("MoldOpeningPressure4");
    }

    /// <summary>
    ///     开模五段压力
    /// </summary>
    [Method("MoldOpeningPressure5", name: "开模五段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldOpeningPressure5()
    {
        return await TimeOrCountSetting("MoldOpeningPressure5");
    }

    /// <summary>
    ///     开模一段速度
    /// </summary>
    [Method("MoldOpeningSpeed1", name: "开模一段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldOpeningSpeed1()
    {
        return await TimeOrCountSetting("MoldOpeningSpeed1");
    }

    /// <summary>
    ///     开模二段速度
    /// </summary>
    [Method("MoldOpeningSpeed2", name: "开模二段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldOpeningSpeed2()
    {
        return await TimeOrCountSetting("MoldOpeningSpeed2");
    }

    /// <summary>
    ///     开模三段速度
    /// </summary>
    [Method("MoldOpeningSpeed3", name: "开模三段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldOpeningSpeed3()
    {
        return await TimeOrCountSetting("MoldOpeningSpeed3");
    }

    /// <summary>
    ///     开模四段速度
    /// </summary>
    [Method("MoldOpeningSpeed4", name: "开模四段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldOpeningSpeed4()
    {
        return await TimeOrCountSetting("MoldOpeningSpeed4");
    }

    /// <summary>
    ///     开模五段速度
    /// </summary>
    [Method("MoldOpeningSpeed5", name: "开模五段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldOpeningSpeed5()
    {
        return await TimeOrCountSetting("MoldOpeningSpeed5");
    }

    /// <summary>
    ///     开模一段位置
    /// </summary>
    [Method("MoldOpeningPosition1", name: "开模一段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldOpeningPosition1()
    {
        return await TimeOrCountSetting("MoldOpeningPosition1");
    }

    /// <summary>
    ///     开模二段位置
    /// </summary>
    [Method("MoldOpeningPosition2", name: "开模二段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldOpeningPosition2()
    {
        return await TimeOrCountSetting("MoldOpeningPosition2");
    }

    /// <summary>
    ///     开模三段位置
    /// </summary>
    [Method("MoldOpeningPosition3", name: "开模三段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldOpeningPosition3()
    {
        return await TimeOrCountSetting("MoldOpeningPosition3");
    }

    /// <summary>
    ///     开模四段位置
    /// </summary>
    [Method("MoldOpeningPosition4", name: "开模四段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldOpeningPosition4()
    {
        return await TimeOrCountSetting("MoldOpeningPosition4");
    }

    /// <summary>
    ///     开模五段位置
    /// </summary>
    [Method("MoldOpeningPosition5", name: "开模五段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldOpeningPosition5()
    {
        return await TimeOrCountSetting("MoldOpeningPosition5");
    }

    /// <summary>
    ///     开模行程
    /// </summary>
    [Method("MoldOpeningStroke", name: "开模行程", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldOpeningStroke()
    {
        return await TimeOrCountSetting("MoldOpeningStroke");
    }

    /// <summary>
    ///     模具冷却计时低位数据
    /// </summary>
    [Method("MoldCoolingTimeLow", name: "模具冷却计时低位数据", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldCoolingTimeLow()
    {
        return await TimeOrCountSetting("MoldCoolingTimeLow");
    }

    /// <summary>
    ///     模具冷却计时低位数据 true = 低；false = 高
    /// </summary>
    [Method("GetMoldCoolingTimeLow", name: "模具冷却计时低位数据 true = 低；false = 高", dataType: TransPondDataTypeEnum.Bool)]
    public async Task<DriverReturnValueModel> GetMoldCoolingTimeLow()
    {
        return await TimeOrCountSetting("GetMoldCoolingTimeLow");
    }

    /// <summary>
    ///     模具冷却计时高位数据
    /// </summary>
    [Method("MoldCoolingTimeHigh", name: "模具冷却计时高位数据", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> MoldCoolingTimeHigh()
    {
        return await TimeOrCountSetting("MoldCoolingTimeHigh");
    }

    #endregion

    #region 45_43数据

    /// <summary>
    ///     上模循环时间
    /// </summary>
    [Method("MsCycleTime", name: "上模循环时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsCycleTime()
    {
        return await TimeOrCountSetting("MsCycleTime");
    }

    /// <summary>
    ///     上模射出时间
    /// </summary>
    [Method("MsInjectionTime", name: "上模射出时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsInjectionTime()
    {
        return await TimeOrCountSetting("MsInjectionTime");
    }

    /// <summary>
    ///     上模转保时间
    /// </summary>
    [Method("MsHoldingTime", name: "上模转保时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsHoldingTime()
    {
        return await TimeOrCountSetting("MsHoldingTime");
    }

    /// <summary>
    ///     上模储料时间
    /// </summary>
    [Method("MsStorageTime", name: "上模储料时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsStorageTime()
    {
        return await TimeOrCountSetting("MsStorageTime");
    }

    /// <summary>
    ///     上模关模计时
    /// </summary>
    [Method("MsMoldCloseTime", name: "上模关模计时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsMoldCloseTime()
    {
        return await TimeOrCountSetting("MsMoldCloseTime");
    }

    /// <summary>
    ///     上模低压计时
    /// </summary>
    [Method("MsLowPressureTime", name: "上模低压计时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsLowPressureTime()
    {
        return await TimeOrCountSetting("MsLowPressureTime");
    }

    /// <summary>
    ///     上模高压计时
    /// </summary>
    [Method("MsHighPressureTime", name: "上模高压计时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsHighPressureTime()
    {
        return await TimeOrCountSetting("MsHighPressureTime");
    }

    /// <summary>
    ///     上模推力座位置
    /// </summary>
    [Method("MsEjectorPos", name: "上模推力座位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsEjectorPos()
    {
        return await TimeOrCountSetting("MsEjectorPos");
    }

    /// <summary>
    ///     上模开模计时
    /// </summary>
    [Method("MsMoldOpenTime", name: "上模开模计时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsMoldOpenTime()
    {
        return await TimeOrCountSetting("MsMoldOpenTime");
    }

    /// <summary>
    ///     上模转保压力
    /// </summary>
    [Method("MsHoldingPressure", name: "上模转保压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsHoldingPressure()
    {
        return await TimeOrCountSetting("MsHoldingPressure");
    }

    /// <summary>
    ///     上模射出起点
    /// </summary>
    [Method("MsInjectionStartPos", name: "上模射出起点", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsInjectionStartPos()
    {
        return await TimeOrCountSetting("MsInjectionStartPos");
    }

    /// <summary>
    ///     上模保压起点
    /// </summary>
    [Method("MsHoldingStartPos", name: "上模保压起点", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsHoldingStartPos()
    {
        return await TimeOrCountSetting("MsHoldingStartPos");
    }

    /// <summary>
    ///     上模射出终点位置
    /// </summary>
    [Method("MsInjectionEndPos", name: "上模射出终点位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsInjectionEndPos()
    {
        return await TimeOrCountSetting("MsInjectionEndPos");
    }

    /// <summary>
    ///     上模射出监控位置
    /// </summary>
    [Method("MsInjectionMonPos", name: "上模射出监控位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsInjectionMonPos()
    {
        return await TimeOrCountSetting("MsInjectionMonPos");
    }

    /// <summary>
    ///     上模射出尖压
    /// </summary>
    [Method("MsInjectionPeakPressure", name: "上模射出尖压", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsInjectionPeakPressure()
    {
        return await TimeOrCountSetting("MsInjectionPeakPressure");
    }

    /// <summary>
    ///     上模储料尖压
    /// </summary>
    [Method("MsStoragePeakPressure", name: "上模储料尖压", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsStoragePeakPressure()
    {
        return await TimeOrCountSetting("MsStoragePeakPressure");
    }

    /// <summary>
    ///     上模最大射速
    /// </summary>
    [Method("MsInjectionSpeedMax", name: "上模最大射速", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsInjectionSpeedMax()
    {
        return await TimeOrCountSetting("MsInjectionSpeedMax");
    }

    /// <summary>
    ///     上模取件时间
    /// </summary>
    [Method("MsPickUpTime", name: "上模取件时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MsPickUpTime()
    {
        return await TimeOrCountSetting("MsPickUpTime");
    }

    #endregion

    #region 43_41

    /// <summary>
    ///     全程计时
    /// </summary>
    [Method("FullTime", name: "全程计时", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> FullTime()
    {
        return await PowerOnData("FullTime");
    }

    /// <summary>
    ///     获取模式值
    /// </summary>
    /// <returns>模式值</returns>
    [Method("Mode", name: "模式", description: "模式：0:为手动，3:为半自动，5为电眼自动，9为时间自动，16为调模", dataType: TransPondDataTypeEnum.String)]
    public async Task<DriverReturnValueModel> GetMode()
    {
        return await PowerOnData("Mode");
    }

    /// <summary>
    ///     获取报警信息值
    /// </summary>
    /// <returns>报警信息值</returns>
    [Method("AlarmStr", name: "报警信息", dataType: TransPondDataTypeEnum.String)]
    public async Task<DriverReturnValueModel> GetAlarmStr()
    {
        return await PowerOnData("AlarmStr");
    }

    /// <summary>
    ///     获取输出压力值
    /// </summary>
    /// <returns>输出压力值</returns>
    [Method("OutputPressure", name: "输出压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetOutputPressure()
    {
        return await PowerOnData("OutputPressure");
    }

    /// <summary>
    ///     获取输出速度值
    /// </summary>
    /// <returns>输出速度值</returns>
    [Method("OutputSpeed", name: "输出速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetOutputSpeed()
    {
        return await PowerOnData("OutputSpeed");
    }

    /// <summary>
    ///     获取输出背压值
    /// </summary>
    /// <returns>输出背压值</returns>
    [Method("OutputBackPressure", name: "输出背压", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetOutputBackPressure()
    {
        return await PowerOnData("OutputBackPressure");
    }

    /// <summary>
    ///     获取射出位置实时值
    /// </summary>
    /// <returns>射出位置实时值</returns>
    [Method("InjectionPositionAct", name: "射出位置实时值", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetInjectionPositionAct()
    {
        return await PowerOnData("InjectionPositionAct");
    }

    /// <summary>
    ///     获取推力座位置值
    /// </summary>
    /// <returns>推力座位置值</returns>
    [Method("MoldPosition", name: "推力座位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetMoldPosition()
    {
        return await PowerOnData("MoldPosition");
    }

    /// <summary>
    ///     获取托模位置值
    /// </summary>
    /// <returns>托模位置值</returns>
    [Method("EjectorPosition", name: "托模位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetEjectorPosition()
    {
        return await PowerOnData("EjectorPosition");
    }

    /// <summary>
    ///     获取开模总数值
    /// </summary>
    /// <returns>开模总数值</returns>
    [Method("Product", name: "产量", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetMoldOpeningTotal()
    {
        return await PowerOnData("MoldOpeningTotal");
    }

    #endregion 43_41

    #region 射出设定F3F2

    /// <summary>
    ///     保压一段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingPressure1", name: "保压一段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> HoldingPressure1()
    {
        return await PowerOnData("HoldingPressure1");
    }

    /// <summary>
    ///     保压二段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingPressure2", name: "保压二段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> HoldingPressure2()
    {
        return await PowerOnData("HoldingPressure2");
    }

    /// <summary>
    ///     保压三段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingPressure3", name: "保压三段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> HoldingPressure3()
    {
        return await PowerOnData("HoldingPressure3");
    }

    /// <summary>
    ///     保压四段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingPressure4", name: "保压四段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> HoldingPressure4()
    {
        return await PowerOnData("HoldingPressure4");
    }

    /// <summary>
    ///     保压五段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingPressure5", name: "保压五段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> HoldingPressure5()
    {
        return await PowerOnData("HoldingPressure5");
    }

    /// <summary>
    ///     保压六段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingPressure6", name: "保压六段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> HoldingPressure6()
    {
        return await PowerOnData("HoldingPressure6");
    }

    /// <summary>
    ///     保压一段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingSpeed1", name: "保压一段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> HoldingSpeed1()
    {
        return await PowerOnData("HoldingSpeed1");
    }

    /// <summary>
    ///     保压二段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingSpeed2", name: "保压二段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> HoldingSpeed2()
    {
        return await PowerOnData("HoldingSpeed2");
    }

    /// <summary>
    ///     保压三段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingSpeed3", name: "保压三段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> HoldingSpeed3()
    {
        return await PowerOnData("HoldingSpeed3");
    }

    /// <summary>
    ///     保压四段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingSpeed4", name: "保压四段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> HoldingSpeed4()
    {
        return await PowerOnData("HoldingSpeed4");
    }

    /// <summary>
    ///     保压五段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingSpeed5", name: "保压五段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> HoldingSpeed5()
    {
        return await PowerOnData("HoldingSpeed5");
    }

    /// <summary>
    ///     保压六段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingSpeed6", name: "保压六段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> HoldingSpeed6()
    {
        return await PowerOnData("HoldingSpeed6");
    }

    /// <summary>
    ///     保压一段时间
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingTime1", name: "保压一段时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingTime1()
    {
        return await PowerOnData("HoldingTime1");
    }

    /// <summary>
    ///     保压二段时间
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingTime2", name: "保压二段时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingTime2()
    {
        return await PowerOnData("HoldingTime2");
    }

    /// <summary>
    ///     保压三段时间
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingTime3", name: "保压三段时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingTime3()
    {
        return await PowerOnData("HoldingTime3");
    }

    /// <summary>
    ///     保压四段时间
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingTime4", name: "保压四段时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingTime4()
    {
        return await PowerOnData("HoldingTime4");
    }

    /// <summary>
    ///     保压五段时间
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingTime5", name: "保压五段时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingTime5()
    {
        return await PowerOnData("HoldingTime5");
    }

    /// <summary>
    ///     保压六段时间
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("HoldingTime6", name: "保压六段时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingTime6()
    {
        return await PowerOnData("HoldingTime6");
    }

    /// <summary>
    ///     射出一段压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionPressure1", name: "射出一段压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure1()
    {
        return await PowerOnData("InjectionPressure1");
    }

    /// <summary>
    ///     射出二段压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionPressure2", name: "射出二段压力值", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure2()
    {
        return await PowerOnData("InjectionPressure2");
    }

    /// <summary>
    ///     射出三段压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionPressure3", name: "射出三段压力值", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure3()
    {
        return await PowerOnData("InjectionPressure3");
    }

    /// <summary>
    ///     射出四段压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionPressure4", name: "射出四段压力值", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure4()
    {
        return await PowerOnData("InjectionPressure4");
    }

    /// <summary>
    ///     射出五段压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionPressure5", name: "射出五段压力值", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure5()
    {
        return await PowerOnData("InjectionPressure5");
    }

    /// <summary>
    ///     射出六段压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionPressure6", name: "射出六段压力值", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure6()
    {
        return await PowerOnData("InjectionPressure6");
    }

    /// <summary>
    ///     射出一段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionSpeed1", name: "射出一段速度", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionSpeed1()
    {
        return await PowerOnData("InjectionSpeed1");
    }

    /// <summary>
    ///     射出二段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionSpeed2", name: "射出二段速度", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionSpeed2()
    {
        return await PowerOnData("InjectionSpeed2");
    }

    /// <summary>
    ///     射出三段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionSpeed3", name: "射出三段速度", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionSpeed3()
    {
        return await PowerOnData("InjectionSpeed3");
    }

    /// <summary>
    ///     射出四段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionSpeed4", name: "射出四段速度", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionSpeed4()
    {
        return await PowerOnData("InjectionSpeed4");
    }

    /// <summary>
    ///     射出五段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionSpeed5", name: "射出五段速度", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionSpeed5()
    {
        return await PowerOnData("InjectionSpeed5");
    }

    /// <summary>
    ///     射出六段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionSpeed6", name: "射出六段速度", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionSpeed6()
    {
        return await PowerOnData("InjectionSpeed6");
    }

    /// <summary>
    ///     射出一段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionPosition1", name: "射出一段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPosition1()
    {
        return await PowerOnData("InjectionPosition1");
    }

    /// <summary>
    ///     射出二段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionPosition2", name: "射出二段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPosition2()
    {
        return await PowerOnData("InjectionPosition2");
    }

    /// <summary>
    ///     射出三段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionPosition3", name: "射出三段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPosition3()
    {
        return await PowerOnData("InjectionPosition3");
    }

    /// <summary>
    ///     射出四段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionPosition4", name: "射出四段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPosition4()
    {
        return await PowerOnData("InjectionPosition4");
    }

    /// <summary>
    ///     射出五段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionPosition5", name: "射出五段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPosition5()
    {
        return await PowerOnData("InjectionPosition5");
    }

    /// <summary>
    ///     射出六段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionPosition6", name: "射出六段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPosition6()
    {
        return await PowerOnData("InjectionPosition6");
    }

    /// <summary>
    ///     获取转保压压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("SwitchHoldingPressure", name: "转保压压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetSwitchHoldingPressure()
    {
        return await PowerOnData("SwitchHoldingPressure");
    }

    /// <summary>
    ///     获取转保压位置值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("SwitchHoldingPosition", name: "转保压位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetSwitchHoldingPosition()
    {
        return await PowerOnData("SwitchHoldingPosition");
    }

    /// <summary>
    ///     获取转保压选择值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("SwitchHoldingSelection", name: "转保压选择", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetSwitchHoldingSelection()
    {
        return await PowerOnData("SwitchHoldingSelection");
    }

    /// <summary>
    ///     获取转保压时间设定值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("SwitchHoldingTime", name: "转保压时间设定", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetSwitchHoldingTime()
    {
        return await PowerOnData("SwitchHoldingTime");
    }

    #endregion 射出设定F3F2

    #region 储料设定，包括储料和射退

    /// <summary>
    ///     储料一段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StoragePressure1", name: "储料一段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StoragePressure1()
    {
        return await PowerOnData("StoragePressure1");
    }

    /// <summary>
    ///     储料二段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StoragePressure2", name: "储料二段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StoragePressure2()
    {
        return await PowerOnData("StoragePressure2");
    }

    /// <summary>
    ///     储料三段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StoragePressure3", name: "储料三段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StoragePressure3()
    {
        return await PowerOnData("StoragePressure3");
    }

    /// <summary>
    ///     储料四段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StoragePressure4", name: "储料四段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StoragePressure4()
    {
        return await PowerOnData("StoragePressure4");
    }

    /// <summary>
    ///     储料五段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StoragePressure5", name: "储料五段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StoragePressure5()
    {
        return await PowerOnData("StoragePressure5");
    }


    /// <summary>
    ///     储料一段背压
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StorageBackPressure1", name: "储料一段背压", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StorageBackPressure1()
    {
        return await PowerOnData("StorageBackPressure1");
    }

    /// <summary>
    ///     储料二段背压
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StorageBackPressure2", name: "储料二段背压", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StorageBackPressure2()
    {
        return await PowerOnData("StorageBackPressure2");
    }

    /// <summary>
    ///     储料三段背压
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StorageBackPressure3", name: "储料三段背压", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StorageBackPressure3()
    {
        return await PowerOnData("StorageBackPressure3");
    }

    /// <summary>
    ///     储料四段背压
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StorageBackPressure4", name: "储料四段背压", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StorageBackPressure4()
    {
        return await PowerOnData("StorageBackPressure4");
    }

    /// <summary>
    ///     储料五段背压
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StorageBackPressure5", name: "储料五段背压", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StorageBackPressure5()
    {
        return await PowerOnData("StorageBackPressure5");
    }

    /// <summary>
    ///     储料一段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StorageSpeed1", name: "储料一段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StorageSpeed1()
    {
        return await PowerOnData("StorageSpeed1");
    }

    /// <summary>
    ///     储料二段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StorageSpeed2", name: "储料二段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StorageSpeed2()
    {
        return await PowerOnData("StorageSpeed2");
    }

    /// <summary>
    ///     储料三段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StorageSpeed3", name: "储料三段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StorageSpeed3()
    {
        return await PowerOnData("StorageSpeed3");
    }

    /// <summary>
    ///     储料四段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StorageSpeed4", name: "储料四段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StorageSpeed4()
    {
        return await PowerOnData("StorageSpeed4");
    }

    /// <summary>
    ///     储料五段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StorageSpeed5", name: "储料五段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> StorageSpeed5()
    {
        return await PowerOnData("StorageSpeed5");
    }

    /// <summary>
    ///     储料一段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StoragePosition1", name: "储料一段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> StoragePosition1()
    {
        return await PowerOnData("StoragePosition1");
    }

    /// <summary>
    ///     储料二段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StoragePosition2", name: "储料二段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> StoragePosition2()
    {
        return await PowerOnData("StoragePosition2");
    }

    /// <summary>
    ///     储料三段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StoragePosition3", name: "储料三段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> StoragePosition3()
    {
        return await PowerOnData("StoragePosition3");
    }

    /// <summary>
    ///     储料四段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StoragePosition4", name: "储料四段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> StoragePosition4()
    {
        return await PowerOnData("StoragePosition4");
    }

    /// <summary>
    ///     储料五段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("StoragePosition5", name: "储料五段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> StoragePosition5()
    {
        return await PowerOnData("StoragePosition5");
    }

    /// <summary>
    ///     获取储前冷却值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoolingBeforeStorage", name: "储前冷却", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> CoolingBeforeStorage()
    {
        return await PowerOnData("CoolingBeforeStorage");
    }

    /// <summary>
    ///     获取射退压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionBackPressure", name: "射退压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> InjectionBackPressure()
    {
        return await PowerOnData("InjectionBackPressure");
    }

    /// <summary>
    ///     获取射退速度值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionBackSpeed", name: "射退速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> InjectionBackSpeed()
    {
        return await PowerOnData("InjectionBackSpeed");
    }

    /// <summary>
    ///     获取射退距离值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionBackDistance", name: "射退距离", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionBackDistance()
    {
        return await PowerOnData("InjectionBackDistance");
    }

    /// <summary>
    ///     获取射退模式值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionBackMode", name: "射退模式", description: "0:表示储料后；1:表示冷却后", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionBackMode()
    {
        return await PowerOnData("InjectionBackMode");
    }

    /// <summary>
    ///     获取储前射退距离值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("InjectionBackDistanceBeforeStorage", name: "储前射退距离", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionBackDistanceBeforeStorage()
    {
        return await PowerOnData("InjectionBackDistanceBeforeStorage");
    }

    #endregion

    #region 托模设定F5F2

    /// <summary>
    ///     托模进一段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorForwardPressure1", name: "托模进一段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorForwardPressure1()
    {
        return await PowerOnData("EjectorForwardPressure1");
    }

    /// <summary>
    ///     托模进二段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorForwardPressure2", name: "托模进二段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorForwardPressure2()
    {
        return await PowerOnData("EjectorForwardPressure2");
    }


    /// <summary>
    ///     托模进三段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorForwardPressure3", name: "托模进三段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorForwardPressure3()
    {
        return await PowerOnData("EjectorForwardPressure3");
    }


    /// <summary>
    ///     托模进四段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorForwardPressure4", name: "托模进四段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorForwardPressure4()
    {
        return await PowerOnData("EjectorForwardPressure4");
    }

    /// <summary>
    ///     托模退一段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorBackwardPressure1", name: "托模退一段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorBackwardPressure1()
    {
        return await PowerOnData("EjectorBackwardPressure1");
    }

    /// <summary>
    ///     托模退二段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorBackwardPressure2", name: "托模退二段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorBackwardPressure2()
    {
        return await PowerOnData("EjectorBackwardPressure2");
    }

    /// <summary>
    ///     托模退三段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorBackwardPressure3", name: "托模退三段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorBackwardPressure3()
    {
        return await PowerOnData("EjectorBackwardPressure3");
    }

    /// <summary>
    ///     托模退四段压力
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorBackwardPressure4", name: "托模退四段压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorBackwardPressure4()
    {
        return await PowerOnData("EjectorBackwardPressure4");
    }


    /// <summary>
    ///     托模进一段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorForwardSpeed1", name: "托模进一段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorForwardSpeed1()
    {
        return await PowerOnData("EjectorForwardSpeed1");
    }

    /// <summary>
    ///     托模进二段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorForwardSpeed2", name: "托模进二段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorForwardSpeed2()
    {
        return await PowerOnData("EjectorForwardSpeed2");
    }

    /// <summary>
    ///     托模进三段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorForwardSpeed3", name: "托模进三段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorForwardSpeed3()
    {
        return await PowerOnData("EjectorForwardSpeed3");
    }

    /// <summary>
    ///     托模进四段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorForwardSpeed4", name: "托模进四段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorForwardSpeed4()
    {
        return await PowerOnData("EjectorForwardSpeed4");
    }

    /// <summary>
    ///     托模退一段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorBackwardSpeed1", name: "托模退一段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorBackwardSpeed1()
    {
        return await PowerOnData("EjectorBackwardSpeed1");
    }

    /// <summary>
    ///     托模退二段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorBackwardSpeed2", name: "托模退二段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorBackwardSpeed2()
    {
        return await PowerOnData("EjectorBackwardSpeed2");
    }

    /// <summary>
    ///     托模退三段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorBackwardSpeed3", name: "托模退三段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorBackwardSpeed3()
    {
        return await PowerOnData("EjectorBackwardSpeed3");
    }

    /// <summary>
    ///     托模退四段速度
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorBackwardSpeed4", name: "托模退四段速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> EjectorBackwardSpeed4()
    {
        return await PowerOnData("EjectorBackwardSpeed4");
    }

    /// <summary>
    ///     托模进一段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorForwardPosition1", name: "托模进一段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> EjectorForwardPosition1()
    {
        return await PowerOnData("EjectorForwardPosition1");
    }

    /// <summary>
    ///     托模进二段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorForwardPosition2", name: "托模进二段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> EjectorForwardPosition2()
    {
        return await PowerOnData("EjectorForwardPosition2");
    }

    /// <summary>
    ///     托模进三段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorForwardPosition3", name: "托模进三段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> EjectorForwardPosition3()
    {
        return await PowerOnData("EjectorForwardPosition3");
    }

    /// <summary>
    ///     托模进四段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorForwardPosition4", name: "托模进四段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> EjectorForwardPosition4()
    {
        return await PowerOnData("EjectorForwardPosition4");
    }

    /// <summary>
    ///     托模退一段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorBackwardPosition1", name: "托模退一段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> EjectorBackwardPosition1()
    {
        return await PowerOnData("EjectorBackwardPosition1");
    }

    /// <summary>
    ///     托模退二段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorBackwardPosition2", name: "托模退二段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> EjectorBackwardPosition2()
    {
        return await PowerOnData("EjectorBackwardPosition2");
    }

    /// <summary>
    ///     托模退三段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorBackwardPosition3", name: "托模退三段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> EjectorBackwardPosition3()
    {
        return await PowerOnData("EjectorBackwardPosition3");
    }

    /// <summary>
    ///     托模退四段位置
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorBackwardPosition4", name: "托模退四段位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> EjectorBackwardPosition4()
    {
        return await PowerOnData("EjectorBackwardPosition4");
    }

    /// <summary>
    ///     托模进延时时间
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorForwardDelayTime", name: "托模进延时时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> EjectorForwardDelayTime()
    {
        return await PowerOnData("EjectorForwardDelayTime");
    }

    /// <summary>
    ///     托模退延时时间
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("EjectorBackwardDelayTime", name: "托模退延时时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> EjectorBackwardDelayTime()
    {
        return await PowerOnData("EjectorBackwardDelayTime");
    }

    #endregion

    #region 中子设定F6F2

    /// <summary>
    ///     获取中子A进动作位置值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreAForwardPosition", name: "中子A进动作位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreAForwardPosition()
    {
        return await PowerOnData("CoreAForwardPosition");
    }

    /// <summary>
    ///     获取中子A进压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreAForwardPressure", name: "中子A进压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreAForwardPressure()
    {
        return await PowerOnData("CoreAForwardPressure");
    }

    /// <summary>
    ///     获取中子A进速度值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreAForwardSpeed", name: "中子A进速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreAForwardSpeed()
    {
        return await PowerOnData("CoreAForwardSpeed");
    }

    /// <summary>
    ///     获取中子A进动作时间值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreAForwardTime", name: "中子A进动作时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreAForwardTime()
    {
        return await PowerOnData("CoreAForwardTime");
    }

    /// <summary>
    ///     获取中子A进绞牙计数值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreAForwardCount", name: "中子A进绞牙计数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreAForwardCount()
    {
        return await PowerOnData("CoreAForwardCount");
    }

    /// <summary>
    ///     获取中子A退动作位置值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreABackwardPosition", name: "中子A退动作位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreABackwardPosition()
    {
        return await PowerOnData("CoreABackwardPosition");
    }

    /// <summary>
    ///     获取中子A退压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreABackwardPressure", name: "中子A退压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreABackwardPressure()
    {
        return await PowerOnData("CoreABackwardPressure");
    }

    /// <summary>
    ///     获取中子A退速度值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreABackwardSpeed", name: "中子A退速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreABackwardSpeed()
    {
        return await PowerOnData("CoreABackwardSpeed");
    }

    /// <summary>
    ///     获取中子A退动作时间值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreABackwardTime", name: "中子A退动作时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreABackwardTime()
    {
        return await PowerOnData("CoreABackwardTime");
    }

    /// <summary>
    ///     获取中子A退绞牙计数值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreABackwardCount", name: "中子A退绞牙计数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreABackwardCount()
    {
        return await PowerOnData("CoreABackwardCount");
    }

    /// <summary>
    ///     获取中子A退绞牙退二值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreABackward2", name: "中子A退绞牙退二", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreABackward2()
    {
        return await PowerOnData("CoreABackward2");
    }

    /// <summary>
    ///     获取中子B进动作位置值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreBForwardPosition", name: "中子B进动作位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreBForwardPosition()
    {
        return await PowerOnData("CoreBForwardPosition");
    }

    /// <summary>
    ///     获取中子B进压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreBForwardPressure", name: "中子B进压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreBForwardPressure()
    {
        return await PowerOnData("CoreBForwardPressure");
    }

    /// <summary>
    ///     获取中子B进速度值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreBForwardSpeed", name: "中子B进速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreBForwardSpeed()
    {
        return await PowerOnData("CoreBForwardSpeed");
    }

    /// <summary>
    ///     获取中子B进动作时间值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreBForwardTime", name: "中子B进动作时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreBForwardTime()
    {
        return await PowerOnData("CoreBForwardTime");
    }

    /// <summary>
    ///     获取中子B进绞牙计数值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreBForwardCount", name: "中子B进绞牙计数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreBForwardCount()
    {
        return await PowerOnData("CoreBForwardCount");
    }

    /// <summary>
    ///     获取中子B退动作位置值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreBBackwardPosition", name: "中子B退动作位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreBBackwardPosition()
    {
        return await PowerOnData("CoreBBackwardPosition");
    }

    /// <summary>
    ///     获取中子B退压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreBBackwardPressure", name: "中子B退压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreBBackwardPressure()
    {
        return await PowerOnData("CoreBBackwardPressure");
    }

    /// <summary>
    ///     获取中子B退速度值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreBBackwardSpeed", name: "中子B退速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreBBackwardSpeed()
    {
        return await PowerOnData("CoreBBackwardSpeed");
    }

    /// <summary>
    ///     获取中子B退动作时间值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreBBackwardTime", name: "中子B退动作时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreBBackwardTime()
    {
        return await PowerOnData("CoreBBackwardTime");
    }

    /// <summary>
    ///     获取中子B退绞牙计数值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreBBackwardCount", name: "中子B退绞牙计数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreBBackwardCount()
    {
        return await PowerOnData("CoreBBackwardCount");
    }

    /// <summary>
    ///     获取中子C进动作位置值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreCForwardPosition", name: "中子C进动作位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreCForwardPosition()
    {
        return await PowerOnData("CoreCForwardPosition");
    }

    /// <summary>
    ///     获取中子C进压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreCForwardPressure", name: "中子C进压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreCForwardPressure()
    {
        return await PowerOnData("CoreCForwardPressure");
    }

    /// <summary>
    ///     获取中子C进速度值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreCForwardSpeed", name: "中子C进速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreCForwardSpeed()
    {
        return await PowerOnData("CoreCForwardSpeed");
    }

    /// <summary>
    ///     获取中子C进动作时间值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreCForwardTime", name: "中子C进动作时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreCForwardTime()
    {
        return await PowerOnData("CoreCForwardTime");
    }

    /// <summary>
    ///     获取中子C进绞牙计数值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreCForwardCount", name: "中子C进绞牙计数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreCForwardCount()
    {
        return await PowerOnData("CoreCForwardCount");
    }

    /// <summary>
    ///     获取中子C退动作位置值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreCBackwardPosition", name: "中子C退动作位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreCBackwardPosition()
    {
        return await PowerOnData("CoreCBackwardPosition");
    }

    /// <summary>
    ///     获取中子C退压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreCBackwardPressure", name: "中子C退压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreCBackwardPressure()
    {
        return await PowerOnData("CoreCBackwardPressure");
    }

    /// <summary>
    ///     获取中子C退速度值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreCBackwardSpeed", name: "中子C退速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreCBackwardSpeed()
    {
        return await PowerOnData("CoreCBackwardSpeed");
    }

    /// <summary>
    ///     获取中子C退动作时间值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreCBackwardTime", name: "中子C退动作时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreCBackwardTime()
    {
        return await PowerOnData("CoreCBackwardTime");
    }

    /// <summary>
    ///     获取中子C退绞牙计数值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreCBackwardCount", name: "中子C退绞牙计数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreCBackwardCount()
    {
        return await PowerOnData("CoreCBackwardCount");
    }

    /// <summary>
    ///     获取中子D进动作位置值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreDForwardPosition", name: "中子D进动作位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreDForwardPosition()
    {
        return await PowerOnData("CoreDForwardPosition");
    }

    /// <summary>
    ///     获取中子D进压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreDForwardPressure", name: "中子D进压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreDForwardPressure()
    {
        return await PowerOnData("CoreDForwardPressure");
    }

    /// <summary>
    ///     获取中子D进速度值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreDForwardSpeed", name: "中子D进速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreDForwardSpeed()
    {
        return await PowerOnData("CoreDForwardSpeed");
    }

    /// <summary>
    ///     获取中子D进动作时间值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreDForwardTime", name: "中子D进动作时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreDForwardTime()
    {
        return await PowerOnData("CoreDForwardTime");
    }

    /// <summary>
    ///     获取中子D进绞牙计数值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreDForwardCount", name: "中子D进绞牙计数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreDForwardCount()
    {
        return await PowerOnData("CoreDForwardCount");
    }

    /// <summary>
    ///     获取中子D退动作位置值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreDBackwardPosition", name: "中子D退动作位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreDBackwardPosition()
    {
        return await PowerOnData("CoreDBackwardPosition");
    }

    /// <summary>
    ///     获取中子D退压力值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreDBackwardPressure", name: "中子D退压力", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreDBackwardPressure()
    {
        return await PowerOnData("CoreDBackwardPressure");
    }

    /// <summary>
    ///     获取中子D退速度值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreDBackwardSpeed", name: "中子D退速度", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreDBackwardSpeed()
    {
        return await PowerOnData("CoreDBackwardSpeed");
    }

    /// <summary>
    ///     获取中子D退动作时间值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreDBackwardTime", name: "中子D退动作时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> GetCoreDBackwardTime()
    {
        return await PowerOnData("CoreDBackwardTime");
    }

    /// <summary>
    ///     获取中子D退绞牙计数值
    /// </summary>
    /// <returns>驱动器返回值模型</returns>
    [Method("CoreDBackwardCount", name: "中子D退绞牙计数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> GetCoreDBackwardCount()
    {
        return await PowerOnData("CoreDBackwardCount");
    }

    #endregion

    /// <summary>
    ///     温度设定， 开关模设定，45_43数据
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> TimeOrCountSetting(string identifier)
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Int32 };
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    #region 设定温度1-7

                    case "A_TempSet1":
                        ret.Value = _techMationAkData.A_TempSet.Length > 0 ? _techMationAkData.A_TempSet[0] : 0;
                        break;
                    case "A_TempSet2":
                        ret.Value = _techMationAkData.A_TempSet.Length > 0 ? _techMationAkData.A_TempSet[1] : 0;
                        break;
                    case "A_TempSet3":
                        ret.Value = _techMationAkData.A_TempSet.Length > 0 ? _techMationAkData.A_TempSet[2] : 0;
                        break;
                    case "A_TempSet4":
                        ret.Value = _techMationAkData.A_TempSet.Length > 0 ? _techMationAkData.A_TempSet[3] : 0;
                        break;
                    case "A_TempSet5":
                        ret.Value = _techMationAkData.A_TempSet.Length > 0 ? _techMationAkData.A_TempSet[4] : 0;
                        break;
                    case "A_TempSet6":
                        ret.Value = _techMationAkData.A_TempSet.Length > 0 ? _techMationAkData.A_TempSet[5] : 0;
                        break;
                    case "A_TempSet7":
                        ret.Value = _techMationAkData.A_TempSet.Length > 0 ? _techMationAkData.A_TempSet[6] : 0;
                        break;

                    #endregion

                    #region 实际温度1-7

                    case "A_TempAct1":
                        ret.Value = _techMationAkData.A_TempAct.Length > 0 ? _techMationAkData.A_TempAct[0] : 0;
                        break;
                    case "A_TempAct2":
                        ret.Value = _techMationAkData.A_TempAct.Length > 0 ? _techMationAkData.A_TempAct[1] : 0;
                        break;
                    case "A_TempAct3":
                        ret.Value = _techMationAkData.A_TempAct.Length > 0 ? _techMationAkData.A_TempAct[2] : 0;
                        break;
                    case "A_TempAct4":
                        ret.Value = _techMationAkData.A_TempAct.Length > 0 ? _techMationAkData.A_TempAct[3] : 0;
                        break;
                    case "A_TempAct5":
                        ret.Value = _techMationAkData.A_TempAct.Length > 0 ? _techMationAkData.A_TempAct[4] : 0;
                        break;
                    case "A_TempAct6":
                        ret.Value = _techMationAkData.A_TempAct.Length > 0 ? _techMationAkData.A_TempAct[5] : 0;
                        break;
                    case "A_TempAct7":
                        ret.Value = _techMationAkData.A_TempAct.Length > 0 ? _techMationAkData.A_TempAct[6] : 0;
                        break;

                    #endregion

                    #region 开关模设定F2F2

                    case "MoldClosingPressure1": // 关模压力
                        ret.Value = _techMationAkData.MoldClosingPressure[0];
                        break;
                    case "MoldClosingPressure2": // 关模压力
                        ret.Value = _techMationAkData.MoldClosingPressure[1];
                        break;
                    case "MoldClosingPressure3": // 关模压力
                        ret.Value = _techMationAkData.MoldClosingPressure[2];
                        break;
                    case "MoldClosingPressure4": // 关模压力
                        ret.Value = _techMationAkData.MoldClosingPressure[3];
                        break;
                    case "MoldClosingPressure5": // 关模压力
                        ret.Value = _techMationAkData.MoldClosingPressure[4];
                        break;

                    case "MoldClosingSpeed1": // 关模速度
                        ret.Value = _techMationAkData.MoldClosingSpeed[0];
                        break;
                    case "MoldClosingSpeed2": // 关模速度
                        ret.Value = _techMationAkData.MoldClosingSpeed[1];
                        break;
                    case "MoldClosingSpeed3": // 关模速度
                        ret.Value = _techMationAkData.MoldClosingSpeed[2];
                        break;
                    case "MoldClosingSpeed4": // 关模速度
                        ret.Value = _techMationAkData.MoldClosingSpeed[3];
                        break;
                    case "MoldClosingSpeed5": // 关模速度
                        ret.Value = _techMationAkData.MoldClosingSpeed[4];
                        break;
                    case "MoldClosingPosition1": // 关模位置
                        ret.Value = _techMationAkData.MoldClosingPosition[0];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldClosingPosition2": // 关模位置
                        ret.Value = _techMationAkData.MoldClosingPosition[1];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldClosingPosition3": // 关模位置
                        ret.Value = _techMationAkData.MoldClosingPosition[2];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldClosingPosition4": // 关模位置
                        ret.Value = _techMationAkData.MoldClosingPosition[3];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldClosingPosition5": // 关模位置
                        ret.Value = _techMationAkData.MoldClosingPosition[4];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldCoolingTime": // 模具冷却时间
                        ret.Value = _techMationAkData.MoldCoolingTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "RecyclingTime": // 再循环计时
                        ret.Value = _techMationAkData.RecyclingTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldOpeningPressure1": // 开模压力
                        ret.Value = _techMationAkData.MoldOpeningPressure[0];
                        break;
                    case "MoldOpeningPressure2": // 开模压力
                        ret.Value = _techMationAkData.MoldOpeningPressure[1];
                        break;
                    case "MoldOpeningPressure3": // 开模压力
                        ret.Value = _techMationAkData.MoldOpeningPressure[2];
                        break;
                    case "MoldOpeningPressure4": // 开模压力
                        ret.Value = _techMationAkData.MoldOpeningPressure[3];
                        break;
                    case "MoldOpeningPressure5": // 开模压力
                        ret.Value = _techMationAkData.MoldOpeningPressure[4];
                        break;
                    case "MoldOpeningSpeed1": // 开模速度
                        ret.Value = _techMationAkData.MoldOpeningSpeed[0];
                        break;
                    case "MoldOpeningSpeed2": // 开模速度
                        ret.Value = _techMationAkData.MoldOpeningSpeed[1];
                        break;
                    case "MoldOpeningSpeed3": // 开模速度
                        ret.Value = _techMationAkData.MoldOpeningSpeed[2];
                        break;
                    case "MoldOpeningSpeed4": // 开模速度
                        ret.Value = _techMationAkData.MoldOpeningSpeed[3];
                        break;
                    case "MoldOpeningSpeed5": // 开模速度
                        ret.Value = _techMationAkData.MoldOpeningSpeed[4];
                        break;
                    case "MoldOpeningPosition1": // 开模位置
                        ret.Value = _techMationAkData.MoldOpeningPosition[0];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldOpeningPosition2": // 开模位置
                        ret.Value = _techMationAkData.MoldOpeningPosition[1];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldOpeningPosition3": // 开模位置
                        ret.Value = _techMationAkData.MoldOpeningPosition[2];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldOpeningPosition4": // 开模位置
                        ret.Value = _techMationAkData.MoldOpeningPosition[3];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldOpeningPosition5": // 开模位置
                        ret.Value = _techMationAkData.MoldOpeningPosition[4];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldOpeningStroke": // 开模行程
                        ret.Value = _techMationAkData.MoldOpeningStroke;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldCoolingTimeLow": // 模具冷却计时低位数据
                        ret.Value = _techMationAkData.MoldCoolingTimeLow;
                        break;
                    case "GetMoldCoolingTimeLow": // 模具冷却计时低位数据 true = 低；false = 高
                        ret.Value = _techMationAkData.GetMoldCoolingTimeLow;
                        ret.DataType = DataTypeEnum.Bool;
                        break;
                    case "MoldCoolingTimeHigh": // 模具冷却计时高位数据
                        ret.Value = _techMationAkData.MoldCoolingTimeHigh;
                        break;

                    #endregion

                    case "MsCycleTime":
                        ret.Value = _techMationAkData.MsCycleTime;
                        break;
                    case "MsInjectionTime":
                        ret.Value = _techMationAkData.MsInjectionTime;
                        break;
                    case "MsHoldingTime":
                        ret.Value = _techMationAkData.MsHoldingTime;
                        break;
                    case "MsStorageTime":
                        ret.Value = _techMationAkData.MsStorageTime;
                        break;
                    case "MsMoldCloseTime":
                        ret.Value = _techMationAkData.MsMoldCloseTime;
                        break;
                    case "MsLowPressureTime":
                        ret.Value = _techMationAkData.MsLowPressureTime;
                        break;
                    case "MsHighPressureTime":
                        ret.Value = _techMationAkData.MsHighPressureTime;
                        break;
                    case "MsEjectorPos":
                        ret.Value = _techMationAkData.MsEjectorPos;
                        break;
                    case "MsMoldOpenTime":
                        ret.Value = _techMationAkData.MsMoldOpenTime;
                        break;
                    case "MsHoldingPressure":
                        ret.Value = _techMationAkData.MsHoldingPressure;
                        break;
                    case "MsInjectionStartPos":
                        ret.Value = _techMationAkData.MsInjectionStartPos;
                        break;
                    case "MsHoldingStartPos":
                        ret.Value = _techMationAkData.MsHoldingStartPos;
                        break;
                    case "MsInjectionEndPos":
                        ret.Value = _techMationAkData.MsInjectionEndPos;
                        break;
                    case "MsInjectionMonPos":
                        ret.Value = _techMationAkData.MsInjectionMonPos;
                        break;
                    case "MsInjectionPeakPressure":
                        ret.Value = _techMationAkData.MsInjectionPeakPressure;
                        break;
                    case "MsStoragePeakPressure":
                        ret.Value = _techMationAkData.MsStoragePeakPressure;
                        break;
                    case "MsInjectionSpeedMax":
                        ret.Value = _techMationAkData.MsInjectionSpeedMax;
                        break;
                    case "MsPickUpTime":
                        ret.Value = _techMationAkData.MsPickUpTime;
                        break;

                    #region B腔温度设定1-7

                    case "B_TempSet1":
                        ret.Value = _techMationAkData.B_TempSet.Length > 0 ? _techMationAkData.B_TempSet[0] : 0;
                        break;
                    case "B_TempSet2":
                        ret.Value = _techMationAkData.B_TempSet.Length > 0 ? _techMationAkData.B_TempSet[1] : 0;
                        break;
                    case "B_TempSet3":
                        ret.Value = _techMationAkData.B_TempSet.Length > 0 ? _techMationAkData.B_TempSet[2] : 0;
                        break;
                    case "B_TempSet4":
                        ret.Value = _techMationAkData.B_TempSet.Length > 0 ? _techMationAkData.B_TempSet[3] : 0;
                        break;
                    case "B_TempSet5":
                        ret.Value = _techMationAkData.B_TempSet.Length > 0 ? _techMationAkData.B_TempSet[4] : 0;
                        break;
                    case "B_TempSet6":
                        ret.Value = _techMationAkData.B_TempSet.Length > 0 ? _techMationAkData.B_TempSet[5] : 0;
                        break;
                    case "B_TempSet7":
                        ret.Value = _techMationAkData.B_TempSet.Length > 0 ? _techMationAkData.B_TempSet[6] : 0;
                        break;

                    #endregion

                    #region B腔实际温度1-7

                    case "B_TempAct1":
                        ret.Value = _techMationAkData.B_TempAct.Length > 0 ? _techMationAkData.B_TempAct[0] : 0;
                        break;
                    case "B_TempAct2":
                        ret.Value = _techMationAkData.B_TempAct.Length > 0 ? _techMationAkData.B_TempAct[1] : 0;
                        break;
                    case "B_TempAct3":
                        ret.Value = _techMationAkData.B_TempAct.Length > 0 ? _techMationAkData.B_TempAct[2] : 0;
                        break;
                    case "B_TempAct4":
                        ret.Value = _techMationAkData.B_TempAct.Length > 0 ? _techMationAkData.B_TempAct[3] : 0;
                        break;
                    case "B_TempAct5":
                        ret.Value = _techMationAkData.B_TempAct.Length > 0 ? _techMationAkData.B_TempAct[4] : 0;
                        break;
                    case "B_TempAct6":
                        ret.Value = _techMationAkData.B_TempAct.Length > 0 ? _techMationAkData.B_TempAct[5] : 0;
                        break;
                    case "B_TempAct7":
                        ret.Value = _techMationAkData.B_TempAct.Length > 0 ? _techMationAkData.B_TempAct[6] : 0;
                        break;

                        #endregion
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     43_41，射出设定F3F2
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> PowerOnData(string identifier)
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Int32 };
        try
        {
            if (IsConnected)
            {
                switch (identifier)
                {
                    #region 43_41
                    case "FullTime":
                        ret.Value = _techMationAkData.FullTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "Mode":
                        ret.Value = _techMationAkData.Mode;
                        ret.DataType = DataTypeEnum.String;
                        break;
                    case "AlarmStr":
                        ret.Value = _techMationAkData.AlarmStr;
                        ret.DataType = DataTypeEnum.String;
                        break;
                    case "MsHoldingTime":
                        ret.Value = _techMationAkData.MsHoldingTime;
                        break;
                    case "OutputPressure":
                        ret.Value = _techMationAkData.OutputPressure;
                        break;
                    case "OutputSpeed":
                        ret.Value = _techMationAkData.OutputSpeed;
                        break;
                    case "OutputBackPressure":
                        ret.Value = _techMationAkData.OutputBackPressure;
                        break;
                    case "InjectionPositionAct":
                        ret.Value = _techMationAkData.InjectionPositionAct;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldPosition":
                        ret.Value = _techMationAkData.MoldPosition;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "EjectorPosition":
                        ret.Value = _techMationAkData.EjectorPosition;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "MoldOpeningTotal":
                        ret.Value = _techMationAkData.MoldOpeningTotal;
                        break;

                    #endregion

                    #region 射出设定F3F2

                    case "HoldingPressure1":
                        ret.Value = _techMationAkData.HoldingPressure[0];
                        break;
                    case "HoldingPressure2":
                        ret.Value = _techMationAkData.HoldingPressure[1];
                        break;
                    case "HoldingPressure3":
                        ret.Value = _techMationAkData.HoldingPressure[2];
                        break;
                    case "HoldingPressure4":
                        ret.Value = _techMationAkData.HoldingPressure[3];
                        break;
                    case "HoldingPressure5":
                        ret.Value = _techMationAkData.HoldingPressure[4];
                        break;
                    case "HoldingPressure6":
                        ret.Value = _techMationAkData.HoldingPressure[5];
                        break;
                    case "HoldingSpeed1":
                        ret.Value = _techMationAkData.HoldingSpeed[0];
                        break;
                    case "HoldingSpeed2":
                        ret.Value = _techMationAkData.HoldingSpeed[1];
                        break;
                    case "HoldingSpeed3":
                        ret.Value = _techMationAkData.HoldingSpeed[2];
                        break;
                    case "HoldingSpeed4":
                        ret.Value = _techMationAkData.HoldingSpeed[3];
                        break;
                    case "HoldingSpeed5":
                        ret.Value = _techMationAkData.HoldingSpeed[4];
                        break;
                    case "HoldingSpeed6":
                        ret.Value = _techMationAkData.HoldingSpeed[5];
                        break;
                    case "HoldingTime1":
                        ret.Value = _techMationAkData.HoldingTime[0];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "HoldingTime2":
                        ret.Value = _techMationAkData.HoldingTime[1];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "HoldingTime3":
                        ret.Value = _techMationAkData.HoldingTime[2];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "HoldingTime4":
                        ret.Value = _techMationAkData.HoldingTime[3];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "HoldingTime5":
                        ret.Value = _techMationAkData.HoldingTime[4];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "HoldingTime6":
                        ret.Value = _techMationAkData.HoldingTime[5];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionPressure1":
                        ret.Value = _techMationAkData.InjectionPressure[0];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionPressure2":
                        ret.Value = _techMationAkData.InjectionPressure[1];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionPressure3":
                        ret.Value = _techMationAkData.InjectionPressure[2];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionPressure4":
                        ret.Value = _techMationAkData.InjectionPressure[3];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionPressure5":
                        ret.Value = _techMationAkData.InjectionPressure[4];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionPressure6":
                        ret.Value = _techMationAkData.InjectionPressure[5];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionSpeed1":
                        ret.Value = _techMationAkData.InjectionSpeed[0];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionSpeed2":
                        ret.Value = _techMationAkData.InjectionSpeed[1];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionSpeed3":
                        ret.Value = _techMationAkData.InjectionSpeed[2];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionSpeed4":
                        ret.Value = _techMationAkData.InjectionSpeed[3];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionSpeed5":
                        ret.Value = _techMationAkData.InjectionSpeed[4];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionSpeed6":
                        ret.Value = _techMationAkData.InjectionSpeed[5];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionPosition1":
                        ret.Value = _techMationAkData.InjectionPosition[0];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionPosition2":
                        ret.Value = _techMationAkData.InjectionPosition[1];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionPosition3":
                        ret.Value = _techMationAkData.InjectionPosition[2];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionPosition4":
                        ret.Value = _techMationAkData.InjectionPosition[3];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionPosition5":
                        ret.Value = _techMationAkData.InjectionPosition[4];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionPosition6":
                        ret.Value = _techMationAkData.InjectionPosition[5];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "SwitchHoldingPressure":
                        ret.Value = _techMationAkData.SwitchHoldingPressure;
                        break;
                    case "SwitchHoldingPosition":
                        ret.Value = _techMationAkData.SwitchHoldingPosition;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "SwitchHoldingSelection":
                        ret.Value = _techMationAkData.SwitchHoldingSelection;
                        break;
                    case "SwitchHoldingTime":
                        ret.Value = _techMationAkData.SwitchHoldingTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;

                    #endregion

                    #region 储料设定，包括储料和射退

                    case "CoolingBeforeStorage":
                        ret.Value = _techMationAkData.CoolingBeforeStorage;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionBackPressure":
                        ret.Value = _techMationAkData.InjectionBackPressure;
                        break;
                    case "InjectionBackSpeed":
                        ret.Value = _techMationAkData.InjectionBackSpeed;
                        break;
                    case "InjectionBackDistance":
                        ret.Value = _techMationAkData.InjectionBackDistance;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionBackMode":
                        ret.Value = _techMationAkData.InjectionBackMode;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "InjectionBackDistanceBeforeStorage":
                        ret.Value = _techMationAkData.InjectionBackDistanceBeforeStorage;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "StoragePressure1":
                        ret.Value = _techMationAkData.StoragePressure[0];
                        break;
                    case "StoragePressure2":
                        ret.Value = _techMationAkData.StoragePressure[1];
                        break;
                    case "StoragePressure3":
                        ret.Value = _techMationAkData.StoragePressure[2];
                        break;
                    case "StoragePressure4":
                        ret.Value = _techMationAkData.StoragePressure[3];
                        break;
                    case "StoragePressure5":
                        ret.Value = _techMationAkData.StoragePressure[4];
                        break;

                    case "StorageBackPressure1":
                        ret.Value = _techMationAkData.StorageBackPressure[0];
                        break;
                    case "StorageBackPressure2":
                        ret.Value = _techMationAkData.StorageBackPressure[1];
                        break;
                    case "StorageBackPressure3":
                        ret.Value = _techMationAkData.StorageBackPressure[2];
                        break;
                    case "StorageBackPressure4":
                        ret.Value = _techMationAkData.StorageBackPressure[3];
                        break;
                    case "StorageBackPressure5":
                        ret.Value = _techMationAkData.StorageBackPressure[4];
                        break;
                    case "StorageSpeed1":
                        ret.Value = _techMationAkData.StorageSpeed[0];
                        break;
                    case "StorageSpeed2":
                        ret.Value = _techMationAkData.StorageSpeed[1];
                        break;
                    case "StorageSpeed3":
                        ret.Value = _techMationAkData.StorageSpeed[2];
                        break;
                    case "StorageSpeed4":
                        ret.Value = _techMationAkData.StorageSpeed[3];
                        break;
                    case "StorageSpeed5":
                        ret.Value = _techMationAkData.StorageSpeed[4];
                        break;
                    case "StoragePosition1":
                        ret.Value = _techMationAkData.StoragePosition[0];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "StoragePosition2":
                        ret.Value = _techMationAkData.StoragePosition[1];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "StoragePosition3":
                        ret.Value = _techMationAkData.StoragePosition[2];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "StoragePosition4":
                        ret.Value = _techMationAkData.StoragePosition[3];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "StoragePosition5":
                        ret.Value = _techMationAkData.StoragePosition[4];
                        ret.DataType = DataTypeEnum.Double;
                        break;

                    #endregion

                    #region 托模设定F5F2

                    case "EjectorForwardPressure1":
                        ret.Value = _techMationAkData.EjectorForwardPressure[0];
                        break;
                    case "EjectorForwardPressure2":
                        ret.Value = _techMationAkData.EjectorForwardPressure[1];
                        break;
                    case "EjectorForwardPressure3":
                        ret.Value = _techMationAkData.EjectorForwardPressure[2];
                        break;
                    case "EjectorForwardPressure4":
                        ret.Value = _techMationAkData.EjectorForwardPressure[3];
                        break;
                    case "EjectorBackwardPressure1":
                        ret.Value = _techMationAkData.EjectorBackwardPressure[0];
                        break;
                    case "EjectorBackwardPressure2":
                        ret.Value = _techMationAkData.EjectorBackwardPressure[0];
                        break;
                    case "EjectorBackwardPressure3":
                        ret.Value = _techMationAkData.EjectorBackwardPressure[0];
                        break;
                    case "EjectorBackwardPressure4":
                        ret.Value = _techMationAkData.EjectorBackwardPressure[0];
                        break;
                    case "EjectorForwardSpeed1":
                        ret.Value = _techMationAkData.EjectorForwardSpeed[0];
                        break;
                    case "EjectorForwardSpeed2":
                        ret.Value = _techMationAkData.EjectorForwardSpeed[1];
                        break;
                    case "EjectorForwardSpeed3":
                        ret.Value = _techMationAkData.EjectorForwardSpeed[2];
                        break;
                    case "EjectorForwardSpeed4":
                        ret.Value = _techMationAkData.EjectorForwardSpeed[3];
                        break;
                    case "EjectorBackwardSpeed1":
                        ret.Value = _techMationAkData.EjectorBackwardSpeed[0];
                        break;
                    case "EjectorBackwardSpeed2":
                        ret.Value = _techMationAkData.EjectorBackwardSpeed[1];
                        break;
                    case "EjectorBackwardSpeed3":
                        ret.Value = _techMationAkData.EjectorBackwardSpeed[2];
                        break;
                    case "EjectorBackwardSpeed4":
                        ret.Value = _techMationAkData.EjectorBackwardSpeed[3];
                        break;
                    case "EjectorForwardPosition1":
                        ret.Value = _techMationAkData.EjectorForwardPosition[0];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "EjectorForwardPosition2":
                        ret.Value = _techMationAkData.EjectorForwardPosition[1];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "EjectorForwardPosition3":
                        ret.Value = _techMationAkData.EjectorForwardPosition[2];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "EjectorForwardPosition4":
                        ret.Value = _techMationAkData.EjectorForwardPosition[3];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "EjectorBackwardPosition1":
                        ret.Value = _techMationAkData.EjectorBackwardPosition[0];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "EjectorBackwardPosition2":
                        ret.Value = _techMationAkData.EjectorBackwardPosition[1];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "EjectorBackwardPosition3":
                        ret.Value = _techMationAkData.EjectorBackwardPosition[2];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "EjectorBackwardPosition4":
                        ret.Value = _techMationAkData.EjectorBackwardPosition[3];
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "EjectorForwardDelayTime":
                        ret.Value = _techMationAkData.EjectorForwardDelayTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "EjectorBackwardDelayTime":
                        ret.Value = _techMationAkData.EjectorBackwardDelayTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;

                    #endregion

                    #region 中子设定F6F2

                    case "CoreAForwardPosition":
                        ret.Value = _techMationAkData.CoreAForwardPosition;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreAForwardPressure":
                        ret.Value = _techMationAkData.CoreAForwardPressure;
                        break;
                    case "CoreAForwardSpeed":
                        ret.Value = _techMationAkData.CoreAForwardSpeed;
                        break;
                    case "CoreAForwardTime":
                        ret.Value = _techMationAkData.CoreAForwardTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreAForwardCount":
                        ret.Value = _techMationAkData.CoreAForwardCount;
                        break;
                    case "CoreABackwardPosition":
                        ret.Value = _techMationAkData.CoreABackwardPosition;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreABackwardPressure":
                        ret.Value = _techMationAkData.CoreABackwardPressure;
                        break;
                    case "CoreABackwardSpeed":
                        ret.Value = _techMationAkData.CoreABackwardSpeed;
                        break;
                    case "CoreABackwardTime":
                        ret.Value = _techMationAkData.CoreABackwardTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreABackwardCount":
                        ret.Value = _techMationAkData.CoreABackwardCount;
                        break;
                    case "CoreABackward2":
                        ret.Value = _techMationAkData.CoreABackward2;
                        break;
                    case "CoreBForwardPosition":
                        ret.Value = _techMationAkData.CoreBForwardPosition;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreBForwardPressure":
                        ret.Value = _techMationAkData.CoreBForwardPressure;
                        break;
                    case "CoreBForwardSpeed":
                        ret.Value = _techMationAkData.CoreBForwardSpeed;
                        break;
                    case "CoreBForwardTime":
                        ret.Value = _techMationAkData.CoreBForwardTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreBForwardCount":
                        ret.Value = _techMationAkData.CoreBForwardCount;
                        break;
                    case "CoreBBackwardPosition":
                        ret.Value = _techMationAkData.CoreBBackwardPosition;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreBBackwardPressure":
                        ret.Value = _techMationAkData.CoreBBackwardPressure;
                        break;
                    case "CoreBBackwardSpeed":
                        ret.Value = _techMationAkData.CoreBBackwardSpeed;
                        break;
                    case "CoreBBackwardTime":
                        ret.Value = _techMationAkData.CoreBBackwardTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreBBackwardCount":
                        ret.Value = _techMationAkData.CoreBBackwardCount;
                        break;
                    case "CoreCForwardPosition":
                        ret.Value = _techMationAkData.CoreCForwardPosition;
                        ret.DataType = DataTypeEnum.Double;
                        break;

                    case "CoreCForwardPressure":
                        ret.Value = _techMationAkData.CoreCForwardPressure;
                        break;
                    case "CoreCForwardSpeed":
                        ret.Value = _techMationAkData.CoreCForwardSpeed;
                        break;
                    case "CoreCForwardTime":
                        ret.Value = _techMationAkData.CoreCForwardTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreCForwardCount":
                        ret.Value = _techMationAkData.CoreCForwardCount;
                        break;
                    case "CoreCBackwardPosition":
                        ret.Value = _techMationAkData.CoreCBackwardPosition;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreCBackwardPressure":
                        ret.Value = _techMationAkData.CoreCBackwardPressure;
                        break;
                    case "CoreCBackwardSpeed":
                        ret.Value = _techMationAkData.CoreCBackwardSpeed;
                        break;
                    case "CoreCBackwardTime":
                        ret.Value = _techMationAkData.CoreCBackwardTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreCBackwardCount":
                        ret.Value = _techMationAkData.CoreCBackwardCount;
                        break;
                    case "CoreDForwardPosition":
                        ret.Value = _techMationAkData.CoreDForwardPosition;
                        ret.DataType = DataTypeEnum.Double;
                        break;

                    case "CoreDForwardPressure":
                        ret.Value = _techMationAkData.CoreDForwardPressure;
                        break;
                    case "CoreDForwardSpeed":
                        ret.Value = _techMationAkData.CoreDForwardSpeed;
                        break;
                    case "CoreDForwardTime":
                        ret.Value = _techMationAkData.CoreDForwardTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreDForwardCount":
                        ret.Value = _techMationAkData.CoreDForwardCount;
                        break;
                    case "CoreDBackwardPosition":
                        ret.Value = _techMationAkData.CoreDBackwardPosition;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreDBackwardPressure":
                        ret.Value = _techMationAkData.CoreDBackwardPressure;
                        break;
                    case "CoreDBackwardSpeed":
                        ret.Value = _techMationAkData.CoreDBackwardSpeed;
                        break;
                    case "CoreDBackwardTime":
                        ret.Value = _techMationAkData.CoreDBackwardTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreDBackwardCount":
                        ret.Value = _techMationAkData.CoreDBackwardCount;
                        break;

                    #endregion

                    #region 座台设置

                    case "SeatAdvanceActionTime":
                        ret.Value = _techMationAkData.SeatAdvanceActionTime;
                        ret.DataType = DataTypeEnum.Double;
                        break;
                    case "CoreAForwardPressure1":
                        ret.Value = _techMationAkData.CoreAForwardPressure1;
                        break;
                    case "CoreAForwardSpeed1":
                        ret.Value = _techMationAkData.CoreAForwardSpeed1;
                        break;
                    case "CoreAForwardPressure2":
                        ret.Value = _techMationAkData.CoreAForwardPressure2;
                        break;
                    case "CoreAForwardSpeed2":
                        ret.Value = _techMationAkData.CoreAForwardSpeed2;
                        break;
                    case "CoreAReversePressure1":
                        ret.Value = _techMationAkData.CoreAReversePressure1;
                        break;
                    case "CoreAReverseSpeed1":
                        ret.Value = _techMationAkData.CoreAReverseSpeed1;
                        break;
                    case "CoreAReverseTime":
                        ret.Value = _techMationAkData.CoreAReverseTime;
                        break;

                        #endregion
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #endregion 采集点位
}