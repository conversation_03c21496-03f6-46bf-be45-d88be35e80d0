using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Driver.Core.Models;
using Furion.Logging;
using HslCommunication.BasicFramework;
using KeQiang_T6H3;

/// <summary>
///     客户端处理类
/// </summary>
public class ClientHandler : IDisposable
{
    /// <summary>
    ///     数据对象
    /// </summary>
    private readonly KeQiangT6H3Data _hxData = new();

    /// <summary>
    ///     驱动信息
    /// </summary>
    private readonly DriverInfoDto _driverInfo;

    /// <summary>
    ///     取消令牌源
    /// </summary>
    private readonly CancellationTokenSource _tokenSource = new();

    /// <summary>
    ///     消息队列
    /// </summary>
    private readonly BlockingCollection<byte[]> _messageQueue = new();

    /// <summary>
    ///     内存流
    /// </summary>
    private readonly MemoryStream _memoryStream = new();

    /// <summary>
    ///     解析任务
    /// </summary>
    private readonly Task _parseTask;

    /// <summary>
    ///     处理任务
    /// </summary>
    private readonly Task _processTask;

    /// <summary>
    ///     是否已释放
    /// </summary>
    private bool _disposed;

    /// <summary>
    ///     连接状态
    /// </summary>
    public bool IsConnected
    {
        get
        {
            try
            {
                if (_tcpClient == null && _serialPort == null)
                    return false;
                return _tcpClient == null || (_tcpClient.Connected && DateTime.Now - _lastReceiveTime < TimeSpan.FromSeconds(30)) || _serialPort.IsOpen;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    ///     最后一次接收数据的时间
    /// </summary>
    private DateTime _lastReceiveTime = DateTime.Now;

    /// <summary>
    ///     添加新的字段用于存储接收到的数据
    /// </summary>
    private readonly ConcurrentQueue<byte[]> _receivedDataQueue = new();

    private volatile bool _isRunning = true;

    public ClientHandler(KeQiangT6H3Data hxData, DriverInfoDto driverInfo)
    {
        _hxData = hxData;
        _driverInfo = driverInfo;
        // 保存任务引用
        _parseTask = Task.Run(ParseMessages, _tokenSource.Token);
        _processTask = Task.Run(ProcessMessages, _tokenSource.Token);
    }

    private SerialPort _serialPort;

    #region 网口

    /// <summary>
    ///     客户端
    /// </summary>
    private TcpClient _tcpClient;

    /// <summary>
    ///     帧头
    /// </summary>
    private readonly byte[] FrameHeader = { 0xF6, 0x6F };

    /// <summary>
    ///     ip地址和端口信息
    /// </summary>
    private string _ipAddress;

    /// <summary>
    ///     连接
    /// </summary>
    /// <param name="ipAddress"></param>
    /// <param name="port"></param>
    public void Connect(string ipAddress, int port)
    {
        _ipAddress = ipAddress + ":" + port;
        try
        {
            // 设置连接超时
            _tcpClient = new TcpClient();
            var connectTask = _tcpClient.ConnectAsync(ipAddress, port);

            // 等待连接完成,设置5秒超时
            if (!connectTask.Wait(5000))
            {
                throw new TimeoutException("连接超时");
            }

            // 只有连接成功才启动消息处理
            if (_tcpClient.Connected)
            {
                _ = Task.Run(ProcessMessages, _tokenSource.Token);
                _ = ReceiveDataAsync();
            }
            else
            {
                throw new Exception("TCP连接失败");
            }
        }
        catch (Exception)
        {
            // 连接失败时清理资源
            if (_tcpClient != null)
            {
                _tcpClient.Dispose();
                _tcpClient = null;
            }
            throw;
        }
    }

    /// <summary>
    ///     接收数据
    /// </summary>
    /// <returns></returns>
    private async Task ReceiveDataAsync()
    {
        try
        {
            if (_tcpClient == null || !_tcpClient.Connected)
            {
                return;
            }

            await using var stream = _tcpClient.GetStream();
            var buffer = new byte[2048];

            while (!_tokenSource.Token.IsCancellationRequested && _tcpClient.Connected)
            {
                try
                {
                    var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, _tokenSource.Token);
                    if (bytesRead == 0) break;

                    // 更新最后一次接收数据的时间
                    _lastReceiveTime = DateTime.Now;

                    var receivedData = new byte[bytesRead];
                    Array.Copy(buffer, receivedData, bytesRead);
                    _receivedDataQueue.Enqueue(receivedData);
                }
                catch (Exception) when (!_tcpClient.Connected)
                {
                    break;
                }
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消,不需要处理
        }
        catch (Exception ex)
        {
            _ = _driverInfo.Socket.Send($"接收数据异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
        }
    }

    /// <summary>
    ///     解析报文
    /// </summary>
    private async Task ParseMessages()
    {
        while (_isRunning)
        {
            try
            {
                if (_tokenSource.Token.IsCancellationRequested)
                {
                    break;
                }

                // 将队列中的数据写入内存流
                while (_receivedDataQueue.TryDequeue(out var data))
                {
                    _ = _driverInfo.Socket.Send($"ip:{_ipAddress} {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} 收：{SoftBasic.ByteToHexString(data, ' ')}", _driverInfo.DeviceId + "_Logs");
                    _memoryStream.Write(data, 0, data.Length);
                }

                // 如果没有数据要处理，等待更长时间
                if (_memoryStream.Length < 6)
                {
                    await Task.Delay(100, _tokenSource.Token);
                    continue;
                }

                // 重置流位置到开始
                _memoryStream.Position = 0;
                // 持续解析直到无法找到完整报文
                while (true)
                {
                    // 更新当前可读取的数据长度
                    var streamLength = _memoryStream.Length - _memoryStream.Position;
                    // _ = _driverInfo.Socket.Send($"当前可读取数据长度: {streamLength}, 当前位置: {_memoryStream.Position}", _driverInfo.DeviceId + "_Logs");

                    // 如果剩余数据太少，退出当前循环
                    if (streamLength < 6) break;

                    // 查找帧头
                    var headerIndex = FindFrameHeader(_memoryStream, (int)_memoryStream.Position);
                    if (headerIndex == -1)
                    {
                        // 未找到帧头,清空缓存
                        _memoryStream.SetLength(0);
                        break;
                    }

                    // 移动到帧头位置
                    _memoryStream.Position = headerIndex;

                    // 数据不足时保留
                    if (_memoryStream.Length < headerIndex + 6) // 需要至少6字节:帧头(2)+长度(2)+命令字(2)
                    {
                        _ = _driverInfo.Socket.Send($"ip:{_ipAddress} 帧头后数据不足6字节，保留数据等待下次解析", _driverInfo.DeviceId + "_Logs");
                        break; // 改用break而不是goto，保留数据等待下次解析
                    }

                    // 读取命令字节
                    var messageTypeByte = new byte[2];
                    _memoryStream.Position = headerIndex + 4; // 跳过帧头(2字节)和长度(2字节)
                    _memoryStream.Read(messageTypeByte, 0, 2);

                    // 读取长度
                    var lengthBytes = new byte[2];
                    _memoryStream.Position = headerIndex + 2; // 跳过帧头2字节
                    _memoryStream.Read(lengthBytes, 0, 2); // 读取长度
                    // 计算长度：(第一个字节&0x0F)*256 + 第二个字节 + 6
                    var messageLength = (((lengthBytes[0] & 0x0F) << 8) | lengthBytes[1]) + 6;
                    // 打印长度相关信息进行调试
                    //_ = _driverInfo.Socket.Send($"报文长度解析: 长度字节[{SoftBasic.ByteToHexString(lengthBytes, ' ')}], 第一字节&0x0F=[{lengthBytes[0] & 0x0F:X2}], 计算长度:{messageLength}", _driverInfo.DeviceId + "_Logs");

                    // 根据消息类型字节判断报文类型
                    switch (messageTypeByte[0])
                    {
                        case 0xED when messageTypeByte[1] == 0x00:
                            break;
                        case 0x13 when messageTypeByte[1] == 0x00:
                            break;
                        case 0x13 when messageTypeByte[1] == 0x01:
                            break;
                        default:
                            {
                                // 如果报文长度不为15，则跳过当前帧头继续查找
                                if (messageLength != 15)
                                {
                                    // 跳过当前帧头继续查找
                                    _memoryStream.Position = headerIndex + 2;
                                    continue;
                                }
                                break;
                            }
                    }
                    // 检查异常长度
                    if (messageLength < 15 || messageLength > 2048)
                    {
                        // _ = _driverInfo.Socket.Send($"报文长度异常: {messageLength}, 长度字节:[{SoftBasic.ByteToHexString(lengthBytes, ' ')}]", _driverInfo.DeviceId + "_Logs");
                        _memoryStream.Position = headerIndex + 2; // 跳过当前帧头继续查找
                        continue;
                    }

                    // 检查是否有完整报文
                    if (_memoryStream.Length < headerIndex + messageLength)
                    {
                        goto ContinueOuterLoop;  // 直接退出内层循环，保留数据等待更多数据
                    }

                    // 读取完整消息
                    var message = new byte[messageLength];
                    _memoryStream.Position = headerIndex;
                    _memoryStream.Read(message, 0, messageLength);

                    // 验证读取的数据长度
                    if (message.Length != messageLength)
                    {
                        // _ = _driverInfo.Socket.Send($"读取的数据长度不正确，期望{messageLength}字节，实际读取{message.Length}字节", _driverInfo.DeviceId + "_Logs");
                        _memoryStream.Position = headerIndex + 2;
                        continue;
                    }

                    // 校验CRC
                    var isValid = CRC16Helper.ValidateCRC16(message);
                    if (!isValid)
                    {
                        // 增加更详细的错误信息
                        _ = _driverInfo.Socket.Send($"ip:{_ipAddress} CRC校验失败，报文长度:{messageLength}, 报文内容: {SoftBasic.ByteToHexString(message, ' ')}", _driverInfo.DeviceId + "_Logs");

                        // 尝试查找下一个帧头
                        _memoryStream.Position = headerIndex + 2;
                        var nextHeaderIndex = FindFrameHeader(_memoryStream, (int)_memoryStream.Position);
                        if (nextHeaderIndex != -1)
                        {
                            _ = _driverInfo.Socket.Send($"ip:{_ipAddress} 找到下一个帧头，位置: {nextHeaderIndex}", _driverInfo.DeviceId + "_Logs");
                            _memoryStream.Position = nextHeaderIndex;
                        }
                        else
                        {
                            _memoryStream.Position = headerIndex + messageLength;
                        }
                        continue;
                    }
                    // 添加到消息队列
                    _messageQueue.Add(message);

                    // 移动到下一个位置
                    _memoryStream.Position = headerIndex + messageLength;
                }

                // 只有在成功处理完一个完整报文后才清理数据
                if (_memoryStream.Position > 0)
                {
                    CleanUpMemoryStream();
                }

                // 使用带取消标记的延迟
                await Task.Delay(10, _tokenSource.Token);

            ContinueOuterLoop:
                {
                    // 短暂等待以避免CPU占用过高
                    await Task.Delay(100, _tokenSource.Token);
                    continue;
                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _ = _driverInfo.Socket.Send($"ip:{_ipAddress} 解析报文异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
                await Task.Delay(100, _tokenSource.Token);
            }
        }
    }

    /// <summary>
    ///     查找帧头
    /// </summary>
    /// <param name="stream"></param>
    /// <param name="startIndex"></param>
    /// <returns></returns>
    private int FindFrameHeader(MemoryStream stream, int startIndex = 0)
    {
        // 获取内存流字节数组
        var buffer = stream.ToArray();
        // 遍历字节数组
        for (var i = startIndex; i <= buffer.Length - FrameHeader.Length; i++)
            // 如果找到帧头，则返回帧头位置
            if (buffer[i] == FrameHeader[0] && buffer[i + 1] == FrameHeader[1])
                return i;

        return -1;
    }

    /// <summary>
    ///     清理内存流
    /// </summary>
    private void CleanUpMemoryStream()
    {
        // 如果内存流位置小于等于长度，则清空内存流
        if (_memoryStream.Position <= _memoryStream.Length)
        {
            // 获取剩余字节
            var remainingBytes = _memoryStream.ToArray()[(int)_memoryStream.Position..];
            // 清空内存流
            _memoryStream.SetLength(0);
            // 写入剩余字节
            _memoryStream.Write(remainingBytes, 0, remainingBytes.Length);
        }
    }

    /// <summary>
    ///     处理消息
    /// </summary>
    private void ProcessMessages()
    {
        try
        {
            foreach (var message in _messageQueue.GetConsumingEnumerable(_tokenSource.Token))
            {
                if (_tokenSource.Token.IsCancellationRequested)
                {
                    break;
                }

                try
                {
                    // 打印解析报文
                    _ = _driverInfo.Socket.Send($"ip:{_ipAddress} 解析报文: {SoftBasic.ByteToHexString(message, ' ')}", _driverInfo.DeviceId + "_Logs");
                    // 长度为15时，直接设置值
                    if (message.Length == 15)
                    {
                        _ = _driverInfo.Socket.Send($"ip:{_ipAddress} 长度为15时，直接设置值", _driverInfo.DeviceId + "_Logs");
                        SetValue(message);
                    }
                    else
                    {
                        switch (message[4])
                        {
                            case 0xED when message[5] == 0x00:
                                DealDataED(message);
                                break;
                            case 0x13 when message[5] == 0x00:
                                DealData1300(message);
                                break;
                            case 0x13 when message[5] == 0x01:
                                DealData1301(message);
                                break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _ = _driverInfo.Socket.Send($"ip:{_ipAddress} 消息处理异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
                }
            }
        }
        catch (OperationCanceledException)
        {
            // 正常的取消操作
        }
    }

    #endregion 网口

    /// <summary>
    ///     处理短报文数据
    /// </summary>
    /// <param name="byteData">报文字节数组</param>
    private void SetValue(byte[] byteData)
    {
        try
        {
            // 提取关键字节
            var key = GetHexStringFromByteArray(byteData, 6, 2);
            switch (key[0])
            {
                #region 合模位置

                // 合模位置1   
                case 0x62 when key[1] == 0x00:
                    _hxData.ClosingPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                    break;
                // 合模位置2
                case 0x44 when key[1] == 0x00:
                    _hxData.ClosingPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                    break;
                // 合模位置3
                case 0x47 when key[1] == 0x00:
                    _hxData.ClosingPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                    break;
                // 合模位置4
                case 0x4A when key[1] == 0x00:
                    _hxData.ClosingPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                    break;
                // 合模位置5
                case 0x4D when key[1] == 0x00:
                    _hxData.ClosingPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.001, RoundLength);
                    break;

                #endregion

                #region 合模压力

                case 0x40 when key[1] == 0x00: // 合模压力1
                    _hxData.ClosingPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x42 when key[1] == 0x00: // 合模压力2
                    _hxData.ClosingPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x45 when key[1] == 0x00: // 合模压力3
                    _hxData.ClosingPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x48 when key[1] == 0x00: // 合模压力4
                    _hxData.ClosingPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x4B when key[1] == 0x00: // 合模压力5
                    _hxData.ClosingPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;

                #endregion

                #region 合模速度

                case 0x41 when key[1] == 0x00: // 合模速度1
                    _hxData.ClosingSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x43 when key[1] == 0x00: // 合模速度2
                    _hxData.ClosingSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x46 when key[1] == 0x00: // 合模速度3
                    _hxData.ClosingSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x49 when key[1] == 0x00: // 合模速度4
                    _hxData.ClosingSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x4C when key[1] == 0x00: // 合模速度5
                    _hxData.ClosingSpeed5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;

                #endregion

                #region 开模位置

                case 0x61 when key[1] == 0x00: // 开模位置4
                    _hxData.OpeningPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x5E when key[1] == 0x00: // 开模位置3
                    _hxData.OpeningPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x5B when key[1] == 0x00: // 开模位置2
                    _hxData.OpeningPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x58 when key[1] == 0x00: // 开模位置1
                    _hxData.OpeningPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;

                #endregion

                #region 开模压力

                case 0x5F when key[1] == 0x00: // 开模压力5
                    _hxData.OpeningPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x5C when key[1] == 0x00: // 开模压力4
                    _hxData.OpeningPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x59 when key[1] == 0x00: // 开模压力3
                    _hxData.OpeningPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x56 when key[1] == 0x00: // 开模压力2
                    _hxData.OpeningPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x54 when key[1] == 0x00: // 开模压力1
                    _hxData.OpeningPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;

                #endregion

                #region 开模速度

                case 0x60 when key[1] == 0x00: // 开模速度5
                    _hxData.OpeningSpeed5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x5D when key[1] == 0x00: // 开模速度4
                    _hxData.OpeningSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x5A when key[1] == 0x00: // 开模速度3
                    _hxData.OpeningSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x57 when key[1] == 0x00: // 开模速度2
                    _hxData.OpeningSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;
                case 0x55 when key[1] == 0x00: // 开模速度1
                    _hxData.OpeningSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;

                #endregion

                case 0x6C when key[1] == 0x00: // 射出一段压力
                    _hxData.InjectionPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4)) * 0.01, RoundLength);
                    break;

                case 0x12 when key[1] == 0x00: // 目标产量
                    _ = _driverInfo.Socket.Send(
                        $"ip:{_ipAddress} 修改目标模数：{BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4))} ，原始报文：{SoftBasic.ByteToHexString(byteData, ' ')}，解析报文：{SoftBasic.ByteToHexString(GetHexStringFromByteArray(byteData, 9, 4), ' ')}",
                        _driverInfo.DeviceId + "_Logs");
                    Log.Warning(
                        $"ip:{_ipAddress} 修改目标模数：{BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4))} ，原始报文：{SoftBasic.ByteToHexString(byteData, ' ')}，解析报文：{SoftBasic.ByteToHexString(GetHexStringFromByteArray(byteData, 9, 4), ' ')}");
                    _hxData.ProductionTarget = BitConverter.ToInt32(GetHexStringFromByteArray(byteData, 9, 4));
                    break;

                default:
                    break;
            }
        }
        catch (Exception ex)
        {
        }
    }

    /// <summary>
    ///     处理控制器发出的数据 ED 00 功能码
    /// </summary>
    /// <param name="buff"></param>
    private void DealDataED(byte[] buff)
    {
        var analogToDigital = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 39, 4));
        _hxData.Pbar = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 67, 2));
        _hxData.Fbar = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 69, 2));
        _hxData.BP = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 71, 2));
        _hxData.OilTemperature = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 161, 2));
        _hxData.TemperatureSegment1 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 163, 2));
        _hxData.TemperatureSegment2 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 165, 2));
        _hxData.TemperatureSegment3 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 167, 2));
        _hxData.TemperatureSegment4 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 169, 2));
        _hxData.TemperatureSegment5 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 171, 2));
        _hxData.TemperatureSegment6 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 173, 2));
        _hxData.TemperatureSegment7 = BitConverter.ToInt16(GetHexStringFromByteArray(buff, 175, 2));
        _hxData.CycleTime = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(buff, 393, 4)) * 0.1, RoundLength);
        _hxData.InjectionTime = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 429, 4)) * 0.1, RoundLength);
        _hxData.InjectionEndPosition = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 435, 4)) * 0.1, RoundLength);
        _hxData.MaxInjectionSpeed = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 441, 5)) * 0.1, RoundLength);
        _hxData.MaterialStorageTime = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 447, 4)) * 0.1, RoundLength);
        _hxData.MaterialStorageEnd = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 453, 4)) * 0.1, RoundLength);
        _hxData.SwitchHoldPosition = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 471, 4)) * 0.1, RoundLength);
        _hxData.AnalogToDigital = analogToDigital;
    }

    /// <summary>
    ///     小数保留位数
    /// </summary>
    private const int RoundLength = 1;

    /// <summary>
    ///     开机的数据 F6 6F 74 05 13 00
    ///     模座数据
    /// </summary>
    /// <param name="buff"></param>
    private void DealData1300(byte[] buff)
    {
        _hxData.ClosingPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 265, 4)) * 0.01, RoundLength);

        _hxData.ClosingSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 269, 4)) * 0.01, RoundLength);

        _hxData.ClosingPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 273, 4)) * 0.01, RoundLength);

        _hxData.ClosingSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 277, 4)) * 0.01, RoundLength);

        _hxData.ClosingPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 281, 4)) * 0.001, RoundLength);

        _hxData.ClosingPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 285, 4)) * 0.01, RoundLength);

        _hxData.ClosingSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 289, 4)) * 0.01, RoundLength);

        _hxData.ClosingPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 293, 4)) * 0.001, RoundLength);

        _hxData.ClosingPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 297, 4)) * 0.01, RoundLength);

        _hxData.ClosingSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 301, 4)) * 0.01, RoundLength);

        _hxData.ClosingPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 305, 4)) * 0.001, RoundLength);

        _hxData.ClosingPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 309, 4)) * 0.01, RoundLength);

        _hxData.ClosingSpeed5 = Math.Round(BitConverter.ToInt16(GetHexStringFromByteArray(buff, 313, 4)) * 0.01, RoundLength);

        _hxData.ClosingPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 317, 4)) * 0.001, RoundLength);

        _hxData.OpeningPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 345, 4)) * 0.01, RoundLength);

        _hxData.OpeningSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 349, 4)) * 0.01, RoundLength);

        _hxData.OpeningPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 353, 4)) * 0.01, RoundLength);

        _hxData.OpeningSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 357, 4)) * 0.01, RoundLength);

        _hxData.OpeningPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 361, 4)) * 0.001, RoundLength);

        _hxData.OpeningPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 365, 4)) * 0.01, RoundLength);
        _hxData.OpeningSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 369, 4)) * 0.01, RoundLength);

        _hxData.OpeningPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 373, 4)) * 0.001, RoundLength);

        _hxData.OpeningPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 377, 4)) * 0.01, RoundLength);

        _hxData.OpeningSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 381, 4)) * 0.01, RoundLength);

        _hxData.OpeningPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 385, 4)) * 0.001, RoundLength);

        _hxData.OpeningPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 389, 4)) * 0.01, RoundLength);

        _hxData.OpeningSpeed5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 393, 4)) * 0.01, RoundLength);

        _hxData.OpeningPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 397, 4)) * 0.001, RoundLength);

        _hxData.OpeningStroke = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 401, 4)) * 0.001, RoundLength);

        _hxData.ClosingPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 401, 4)) * 0.001, RoundLength);

        _hxData.OpeningPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 401, 4)) * 0.001, RoundLength);

        _hxData.InjectionPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 441, 4)) * 0.01, RoundLength);

        _hxData.InjectionSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 445, 4)) * 0.01, RoundLength);

        _hxData.InjectionPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 449, 4)) * 0.01, RoundLength);

        _hxData.InjectionSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 453, 4)) * 0.01, RoundLength);

        _hxData.InjectionPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 457, 4)) * 0.001, RoundLength);

        _hxData.InjectionPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 361, 4)) * 0.01, RoundLength);

        _hxData.InjectionSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 465, 4)) * 0.01, RoundLength);

        _hxData.InjectionPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 469, 4)) * 0.001, RoundLength);

        _hxData.InjectionPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 473, 4)) * 0.01, RoundLength);

        _hxData.InjectionSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 477, 5)) * 0.01, RoundLength);

        _hxData.InjectionPosition3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 481, 4)) * 0.001, RoundLength);

        _hxData.InjectionPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 485, 5)) * 0.01, RoundLength);

        _hxData.InjectionSpeed5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 489, 5)) * 0.01, RoundLength);

        _hxData.InjectionPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 493, 4)) * 0.001, RoundLength);

        _hxData.InjectionPressure6 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 497, 4)) * 0.01, RoundLength);

        _hxData.InjectionSpeed6 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 501, 4)) * 0.01, RoundLength);

        _hxData.InjectionPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 505, 4)) * 0.001, RoundLength);

        _hxData.RetractionPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 573, 4)) * 0.01, RoundLength);

        _hxData.RetractionSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 577, 4)) * 0.01, RoundLength);

        _hxData.RetractionTime1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 581, 4)) * 0.01, RoundLength);

        _hxData.RetractionPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 585, 4)) * 0.01, RoundLength);

        _hxData.RetractionSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 589, 4)) * 0.01, RoundLength);

        _hxData.RetractionTime2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 593, 4)) * 0.01, RoundLength);

        _hxData.HoldingPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 781, 4)) * 0.01, RoundLength);

        _hxData.HoldingSpeed5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 785, 4)) * 0.01, RoundLength);

        _hxData.HoldingBackPressure5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 789, 4)) * 0.01, RoundLength);

        _hxData.HoldingPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 797, 4)) * 0.01, RoundLength);

        _hxData.HoldingSpeed4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 801, 4)) * 0.01, RoundLength);

        _hxData.HoldingBackPressure4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 805, 4)) * 0.01, RoundLength);

        _hxData.HoldingPosition4 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 809, 4)) * 0.001, RoundLength);

        _hxData.HoldingPosition5 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 893, 4)) * 0.001, RoundLength);

        _hxData.HoldingPressure = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 937, 4)) * 0.01, RoundLength);

        _hxData.HoldingSpeed = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 941, 4)) * 0.001, RoundLength);

        _hxData.HoldingDistance = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 945, 4)) * 0.001, RoundLength);

        _hxData.TopInPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 949, 4)) * 0.01, RoundLength);

        _hxData.TopInSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 997, 4)) * 0.01, RoundLength);

        _hxData.TopInPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1001, 4)) * 0.01, RoundLength);

        _hxData.TopInSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1005, 4)) * 0.01, RoundLength);

        _hxData.TopInPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1009, 4)) * 0.001, RoundLength);

        _hxData.TopInPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1013, 4)) * 0.001, RoundLength);

        _hxData.TopOutPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1017, 4)) * 0.01, RoundLength);

        _hxData.TopOutSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1021, 4)) * 0.01, RoundLength);

        _hxData.TopOutPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 1025, 4)) * 0.01, RoundLength);
    }

    /// <summary>
    ///     开机的数据 F6 6F 04 05 13 01
    /// </summary>
    /// <param name="buff"></param>
    private void DealData1301(byte[] buff)
    {
        _hxData.TopOutSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 9, 4)) * 0.01, RoundLength);
        _hxData.TopOutSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 9, 4)) * 0.01, RoundLength);

        _hxData.TopOutPosition1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 13, 4)) * 0.001, RoundLength);

        _hxData.TopOutPosition2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 17, 4)) * 0.001, RoundLength);
        Log.Warning(
            $"开机数据修改目标模数：{BitConverter.ToInt32(GetHexStringFromByteArray(buff, 81, 4))} ，原始报文：{SoftBasic.ByteToHexString(buff, ' ')}，解析报文：{SoftBasic.ByteToHexString(GetHexStringFromByteArray(buff, 81, 4), ' ')}");
        _hxData.ProductionTarget = BitConverter.ToInt32(GetHexStringFromByteArray(buff, 81, 4));

        _hxData.BedInPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 109, 4)) * 0.01, RoundLength);

        _hxData.BedInSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 113, 4)) * 0.01, RoundLength);

        _hxData.BedInTime1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 117, 4)) * 0.01, RoundLength);

        _hxData.BedInPressure2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 121, 4)) * 0.01, RoundLength);

        _hxData.BedInSpeed2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 125, 4)) * 0.01, RoundLength);

        _hxData.BedInTime2 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 129, 4)) * 0.01, RoundLength);

        _hxData.BedInPressure3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 133, 4)) * 0.01, RoundLength);

        _hxData.BedInSpeed3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 137, 4)) * 0.01, RoundLength);

        _hxData.BedInTime3 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 141, 4)) * 0.01, RoundLength);

        _hxData.BedOutPressure1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 145, 4)) * 0.01, RoundLength);

        _hxData.BedOutSpeed1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 149, 4)) * 0.01, RoundLength);

        _hxData.BedOutTime1 = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 169, 4)) * 0.01, RoundLength);

        _hxData.MoldOutPressure = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 193, 4)) * 0.01, RoundLength);

        _hxData.MoldOutSpeed = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 197, 4)) * 0.01, RoundLength);

        _hxData.MoldInPressure = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 201, 4)) * 0.01, RoundLength);

        _hxData.MoldInSpeed = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 205, 4)) * 0.01, RoundLength);

        _hxData.MoldInSlowSpeed = Math.Round(BitConverter.ToInt32(GetHexStringFromByteArray(buff, 209, 4)) * 0.01, RoundLength);
    }

    /// <summary>
    /// </summary>
    /// <param name="byteArray"></param>
    /// <param name="startIndex"></param>
    /// <param name="length"></param>
    /// <returns></returns>
    private static byte[] GetHexStringFromByteArray(byte[] byteArray, int startIndex, int length)
    {
        var subData = new byte[length];
        Array.Copy(byteArray, startIndex, subData, 0, length);
        return subData;
    }

    /// <summary>
    ///     连接串口
    /// </summary>
    /// <param name="portName"></param>
    public void ConnectSerial(string portName)
    {
        try
        {
            // 创建串口
            _serialPort = new SerialPort
            {
                PortName = portName, // 串口名称
                BaudRate = 115200, // 波特率
                DataBits = 8, // 数据位
                StopBits = StopBits.One, // 停止位
                Parity = Parity.Odd // 校验位
            };

            // 注册事件
            _serialPort.DataReceived += SerialPort_DataReceived;
            // 打开串口
            _serialPort.Open();

            _ = _driverInfo.Socket.Send($"Connected to serial port: {portName}", _driverInfo.DeviceId + "_Logs");
            // 启动消息处理任务
            _ = Task.Run(ProcessMessages);
        }
        catch (Exception ex)
        {
            _ = _driverInfo.Socket.Send($"Failed to connect to serial port: {ex.Message}", _driverInfo.DeviceId + "_Logs");
            throw;
        }
    }

    /// <summary>
    ///     串口数据接收事件
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
    {
        try
        {
            var bytesToRead = _serialPort.BytesToRead;
            if (bytesToRead == 0) return;

            var buffer = new byte[bytesToRead];
            var bytesRead = _serialPort.Read(buffer, 0, buffer.Length);

            // 复制接收到的数据并加入队列
            var receivedData = new byte[bytesRead];
            Array.Copy(buffer, receivedData, bytesRead);
            _receivedDataQueue.Enqueue(receivedData);

            // 记录日志
            var hexString = SoftBasic.ByteToHexString(receivedData, ' ');
            _ = _driverInfo.Socket.Send($"时间：{DateTime.Now}\t【收】:" + hexString, _driverInfo.DeviceId + "_Logs");
        }
        catch (Exception ex)
        {
            _ = _driverInfo.Socket.Send($"Error receiving serial data: {ex.Message}", _driverInfo.DeviceId + "_Logs");
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _isRunning = false;

            try
            {
                _tokenSource.Cancel();

                // 等待任务完成,但设置超时时间
                var tasks = new List<Task>();
                if (_parseTask != null && !_parseTask.IsCompleted)
                    tasks.Add(_parseTask);
                if (_processTask != null && !_processTask.IsCompleted)
                    tasks.Add(_processTask);

                if (tasks.Any())
                {
                    Task.WaitAll(tasks.ToArray(), 1000);
                }
            }
            catch (Exception ex)
            {
                _ = _driverInfo.Socket.Send($"ip:{_ipAddress} 关闭连接异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
            }
            finally
            {
                _tokenSource.Dispose();
                _messageQueue.Dispose();
                _memoryStream.Dispose();

                if (_serialPort != null)
                {
                    try
                    {
                        if (_serialPort.IsOpen)
                            _serialPort.Close();
                        _serialPort.Dispose();
                    }
                    catch { }
                    _serialPort = null;
                }

                if (_tcpClient != null)
                {
                    try
                    {
                        _tcpClient.Close();
                        _tcpClient.Dispose();
                    }
                    catch { }
                    _tcpClient = null;
                }
            }
        }

        _disposed = true;
    }

    ~ClientHandler()
    {
        Dispose(false);
    }
}