using System;
using System.IO;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Timers;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Feng.Common.Util;
using Furion.JsonSerialization;
using Furion.Logging;
using HslCommunication;
using Newtonsoft.Json;
using Timer = System.Timers.Timer;

namespace KeQiang_T6H3;

/* 科强T6H3
 * 更新日志
 * 版本:v1.0.0
 */
[DriverInfo("KeQiangT6H3", "V1.0.0", "注塑机")]
public class KeQiangT6H3 : BaseDeviceProtocolCollector, IDriver
{
    /// <summary>
    ///     重连周期
    /// </summary>
    [ConfigParameter("重连周期(s)", GroupName = "高级配置", Remark = "当设备关机后下一轮进行重新连接的时间")]
    public override int ReConnTime { get; set; } = 5;

    [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置", Display = false)]
    public override int WriteInterval { get; set; } = 120;

    [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    [ConfigParameter("连接方式", order: 1)] public DriverConnectTypeEnum ConnectType { get; set; } = DriverConnectTypeEnum.SerialPort;

    #region 网口配置

    [ConfigParameter("IP地址", Display = false, DisplayExpress = "{\"ConnectType\": [\"1\"]}")]
    public string IpAddress { get; set; } = "127.0.0.1";

    [ConfigParameter("端口号", Display = false, DisplayExpress = "{\"ConnectType\": [\"1\"]}")]
    public int Port { get; set; } = 8001;

    [ConfigParameter("端口号2", Display = false, DisplayExpress = "{\"ConnectType\": [\"1\"]}")]
    public int Port2 { get; set; } = 8002;

    #endregion 网口配置

    #region 串口配置

    [ConfigParameter("串口号", Display = false, Remark = "", DisplayExpress = "{\"ConnectType\": [\"2\"]}")]
    public SerialNumberEnum SerialNumber { get; set; } = SerialNumberEnum.ttyS3;

    [ConfigParameter("串口号2", Display = false, Remark = "", DisplayExpress = "{\"ConnectType\": [\"2\"]}")]
    public SerialNumberEnum SerialNumber2 { get; set; } = SerialNumberEnum.ttyS4;

    #endregion 串口配置

    private ClientHandler _client1;
    private ClientHandler _client2;
    private KeQiangT6H3Data _keQiangT6H3Data = new();


    public KeQiangT6H3(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
        try
        {
            ReadContent();
            // 创建一个 Timer 实例，设置定时器间隔为 10 秒
            _timer = new Timer(1000 * 10);
            _timer.Elapsed += TimerElapsed;
            // 启动定时器
            _timer.Start();
        }
        catch (Exception e)
        {
            Log.Error($"【科强】 初始化异常:{e.Message}");
        }
    }

    public override bool IsConnected => _client1 != null && _client2 != null && (_client1.IsConnected || _client2.IsConnected);

    public DeviceConnectDto Connect()
    {
        try
        {
            // 先关闭和清理旧的连接
            if (_client1 != null)
            {
                _client1.Dispose();
                _client1 = null;
            }
            if (_client2 != null)
            {
                _client2.Dispose();
                _client2 = null;
            }

            _client1 = new ClientHandler(_keQiangT6H3Data, DriverInfo);
            _client2 = new ClientHandler(_keQiangT6H3Data, DriverInfo);

            if (ConnectType == DriverConnectTypeEnum.NetWork)
            {
                _client1.Connect(IpAddress, Port);
                _client2.Connect(IpAddress, Port2);
            }
            else
            {
                _client1.ConnectSerial(EnumUtil.GetEnumDesc(SerialNumber));
                _client2.ConnectSerial(EnumUtil.GetEnumDesc(SerialNumber2));
            }

            IsConnected = _client1.IsConnected && _client2.IsConnected;
            OperateResult.IsSuccess = IsConnected;
            OperateResult.Message = IsConnected ? "连接成功" : "连接失败";
        }
        catch (Exception ex)
        {
            // 发生异常时也要清理连接
            if (_client1 != null)
            {
                _client1.Dispose();
                _client1 = null;
            }
            if (_client2 != null)
            {
                _client2.Dispose();
                _client2 = null;
            }
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }

    /// <summary>
    ///     关闭连接,串口协议需要自行重写该方法
    /// </summary>
    public override void Close()
    {
        _ = SaveContent();
        _client1?.Dispose();
        _client2?.Dispose();
        _ = DriverInfo.Socket.Send($"来源:[{IpAddress ?? SerialNumber.ToString()}]\t 时间：{DateTime.Now}\t 连接已关闭:", DriverInfo.DeviceId + "_Logs");
    }

    /// <summary>
    ///     释放连接,串口协议需要自行重写该方法
    /// </summary>
    public override void Dispose()
    {
        // 停止定时器
        if (_timer != null)
        {
            _timer.Elapsed -= TimerElapsed;
            _timer.Stop();
            _timer.Dispose();
        }

        _client1?.Dispose();
        _client2?.Dispose();
    }

    private readonly Timer _timer;

    /// <summary>
    ///     持久化内容
    /// </summary>
    private async Task SaveContent()
    {
        // 持久化最后的数据
        var data = _keQiangT6H3Data.ToJsonString();
        // 设置文件路径
        var filePath = $"/Edge/{DriverInfo.DeviceName}.txt";
        // 写入数据到文件
        await File.WriteAllTextAsync(filePath, data);
        // Log.Warning($"【科强T6H3】 设备:【{IpAddress}:{Port}】 持久化path:{filePath}缓存成功，设定温度1:{_keQiangT6H3Data.TempSet[1]}");
    }

    /// <summary>
    ///     读取持久化内容
    /// </summary>
    private void ReadContent()
    {
        try
        {
            // 设置文件路径
            var filePath = $"/Edge/{DriverInfo.DeviceName}.txt";

            // 初始化默认数据
            _keQiangT6H3Data = new KeQiangT6H3Data();

            // 如果文件存在且不为空，尝试读取
            if (File.Exists(filePath))
            {
                var data = File.ReadAllText(filePath);
                if (!string.IsNullOrEmpty(data))
                {
                    try
                    {
                        var deserializedData = JSON.Deserialize<KeQiangT6H3Data>(data);
                        if (deserializedData != null)
                        {
                            _keQiangT6H3Data = deserializedData;
                        }
                    }
                    catch (JsonReaderException ex)
                    {
                        Log.Error($"【科强-T6H3】 JSON解析异常，使用默认值。异常信息:{ex.Message}");
                        // JSON 解析失败时继续使用默认值
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"【科强-T6H3】 数据反序列化异常，使用默认值。异常信息:{ex.Message}");
                        // 其他异常时继续使用默认值
                    }
                }
            }

            // 无论如何都要确保写入类型文件
            try
            {
                File.WriteAllText("/Edge/type.txt", "KeQiangT6H3");
            }
            catch (Exception ex)
            {
                Log.Error($"【科强-T6H3】 写入type.txt文件异常:{ex.Message}");
            }

            // 如果文件不存在或为空，创建新文件并写入默认数据
            try
            {
                if (!File.Exists(filePath) || string.IsNullOrEmpty(File.ReadAllText(filePath)))
                {
                    File.WriteAllText(filePath, JSON.Serialize(_keQiangT6H3Data));
                }
            }
            catch (Exception ex)
            {
                Log.Error($"【科强-T6H3】 写入默认数据异常:{ex.Message}");
            }
        }
        catch (Exception ex)
        {
            Log.Error($"【科强-T6H3】 读取持久化内容异常:{ex.Message}");
            // 确保在任何异常情况下都有默认数据
            _keQiangT6H3Data = new KeQiangT6H3Data();
        }
    }

    /// <summary>
    ///     持久化内容
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void TimerElapsed(object sender, ElapsedEventArgs e)
    {
        _ = SaveContent();
    }

    #region 采集点位

    #region 实时数据

    /// <summary>
    ///     目标产量
    /// </summary>
    /// <returns></returns>
    [Method("ProductionTarget", name: "目标产量", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> ProductionTarget()
    {
        return await PowerOnData("ProductionTarget");
    }

    /// <summary>
    ///     实时数据
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> PowerOnData(string identifier)
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Int32 };
        try
        {
            if (IsConnected)
            {
                var propertyInfo = typeof(KeQiangT6H3Data).GetProperty(identifier);
                if (propertyInfo == null)
                {
                    ret.ErrorCode = 9999;
                    ret.Message = $"属性：{identifier} 未找到";
                    ret.VariableStatus = VariableStatusTypeEnum.Bad;
                    ret.Value = null;
                    return ret;
                }

                ret.Value = propertyInfo.GetValue(_keQiangT6H3Data);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     模数
    /// </summary>
    /// <returns></returns>
    [Method("CYCN", name: "模数", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> AnalogToDigital()
    {
        return await PowerOnData("AnalogToDigital");
    }

    /// <summary>
    ///     Pbar
    /// </summary>
    /// <returns></returns>
    [Method("Pbar", name: "Pbar", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> Pbar()
    {
        return await PowerOnData("Pbar");
    }

    /// <summary>
    ///     Fbar
    /// </summary>
    /// <returns></returns>
    [Method("Fbar", name: "Fbar", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> Fbar()
    {
        return await PowerOnData("Fbar");
    }

    /// <summary>
    ///     BP
    /// </summary>
    /// <returns></returns>
    [Method("BP", name: "BP", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> BP()
    {
        return await PowerOnData("BP");
    }

    /// <summary>
    ///     油温
    /// </summary>
    /// <returns></returns>
    [Method("OT", name: "油温", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> OilTemperature()
    {
        return await PowerOnData("OilTemperature");
    }

    /// <summary>
    ///     温度1段
    /// </summary>
    /// <returns></returns>
    [Method("T1", name: "温度1", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> TemperatureSegment1()
    {
        return await PowerOnData("TemperatureSegment1");
    }

    /// <summary>
    ///     温度2
    /// </summary>
    /// <returns></returns>
    [Method("T2", name: "温度2", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> TemperatureSegment2()
    {
        return await PowerOnData("TemperatureSegment2");
    }

    /// <summary>
    ///     温度3
    /// </summary>
    /// <returns></returns>
    [Method("T3", name: "温度3", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> TemperatureSegment3()
    {
        return await PowerOnData("TemperatureSegment3");
    }

    /// <summary>
    ///     温度4
    /// </summary>
    /// <returns></returns>
    [Method("T4", name: "温度4", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> TemperatureSegment4()
    {
        return await PowerOnData("TemperatureSegment4");
    }

    /// <summary>
    ///     温度5
    /// </summary>
    /// <returns></returns>
    [Method("T5", name: "温度5", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> TemperatureSegment5()
    {
        return await PowerOnData("TemperatureSegment5");
    }

    /// <summary>
    ///     温度6
    /// </summary>
    /// <returns></returns>
    [Method("T6", name: "温度6", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> TemperatureSegment6()
    {
        return await PowerOnData("TemperatureSegment6");
    }

    /// <summary>
    ///     温度7
    /// </summary>
    /// <returns></returns>
    [Method("T7", name: "温度7", dataType: TransPondDataTypeEnum.Int)]
    public async Task<DriverReturnValueModel> TemperatureSegment7()
    {
        return await PowerOnData("TemperatureSegment7");
    }

    /// <summary>
    ///     周期时间
    /// </summary>
    /// <returns></returns>
    [Method("ECYCT", name: "周期时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> CycleTime()
    {
        return await PowerOnData("CycleTime");
    }

    /// <summary>
    ///     射出时间
    /// </summary>
    /// <returns></returns>
    [Method("EIPT", name: "射出时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionTime()
    {
        return await PowerOnData("InjectionTime");
    }

    /// <summary>
    ///     射出终点位置
    /// </summary>
    /// <returns></returns>
    [Method("EIPSE", name: "射出终点位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionEndPosition()
    {
        return await PowerOnData("InjectionEndPosition");
    }

    /// <summary>
    ///     最大射速
    /// </summary>
    /// <returns></returns>
    [Method("EIVM", name: "最大射速", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaxInjectionSpeed()
    {
        return await PowerOnData("MaxInjectionSpeed");
    }

    /// <summary>
    ///     储料时间
    /// </summary>
    /// <returns></returns>
    [Method("EPLST", name: "储料时间", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialStorageTime()
    {
        return await PowerOnData("MaterialStorageTime");
    }

    /// <summary>
    ///     储料终点
    /// </summary>
    /// <returns></returns>
    [Method("EPLSSE", name: "储料终点", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MaterialStorageEnd()
    {
        return await PowerOnData("MaterialStorageEnd");
    }

    /// <summary>
    ///     转保压位置
    /// </summary>
    /// <returns></returns>
    [Method("ESIPS", name: "转保压位置", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> SwitchHoldPosition()
    {
        return await PowerOnData("SwitchHoldPosition");
    }

    #endregion

    #region 上电数据

    /// <summary>
    ///     开机数据
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    private async Task<DriverReturnValueModel> OpenData(string identifier)
    {
        DriverReturnValueModel ret = new() { DataType = DataTypeEnum.Double };
        try
        {
            if (IsConnected)
            {
                var propertyInfo = typeof(KeQiangT6H3Data).GetProperty(identifier);
                if (propertyInfo == null)
                {
                    ret.ErrorCode = 9999;
                    ret.Message = $"属性：{identifier} 未找到";
                    ret.VariableStatus = VariableStatusTypeEnum.Bad;
                    ret.Value = null;
                    return ret;
                }

                ret.Value = propertyInfo.GetValue(_keQiangT6H3Data);
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "TCP连接异常";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    #region 合模压力

    /// <summary>
    ///     合模压力1
    /// </summary>
    /// <returns></returns>
    [Method("MCP1", name: "合模压力1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingPressure1()
    {
        return await OpenData("ClosingPressure1");
    }

    /// <summary>
    ///     合模压力2
    /// </summary>
    /// <returns></returns>
    [Method("MCP2", name: "合模压力2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingPressure2()
    {
        return await OpenData("ClosingPressure2");
    }

    /// <summary>
    ///     合模压力3
    /// </summary>
    /// <returns></returns>
    [Method("MCP3", name: "合模压力3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingPressure3()
    {
        return await OpenData("ClosingPressure3");
    }

    /// <summary>
    ///     合模压力4
    /// </summary>
    /// <returns></returns>
    [Method("MCP4", name: "合模压力4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingPressure4()
    {
        return await OpenData("ClosingPressure4");
    }

    /// <summary>
    ///     合模压力5
    /// </summary>
    /// <returns></returns>
    [Method("MCP5", name: "合模压力5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingPressure5()
    {
        return await OpenData("ClosingPressure5");
    }

    #endregion

    #region 合模速度

    /// <summary>
    ///     合模速度1
    /// </summary>
    /// <returns></returns>
    [Method("MCV1", name: "合模速度1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingSpeed1()
    {
        return await OpenData("ClosingSpeed1");
    }

    /// <summary>
    ///     合模速度2
    /// </summary>
    /// <returns></returns>
    [Method("MCV2", name: "合模速度2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingSpeed2()
    {
        return await OpenData("ClosingSpeed2");
    }

    /// <summary>
    ///     合模速度3
    /// </summary>
    /// <returns></returns>
    [Method("MCV3", name: "合模速度3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingSpeed3()
    {
        return await OpenData("ClosingSpeed3");
    }

    /// <summary>
    ///     合模速度4
    /// </summary>
    /// <returns></returns>
    [Method("MCV4", name: "合模速度4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingSpeed4()
    {
        return await OpenData("ClosingSpeed4");
    }

    /// <summary>
    ///     合模速度5
    /// </summary>
    /// <returns></returns>
    [Method("MCV5", name: "合模速度5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingSpeed5()
    {
        return await OpenData("ClosingSpeed5");
    }

    #endregion

    #region 合模位置

    /// <summary>
    ///     合模位置1
    /// </summary>
    /// <returns></returns>
    [Method("MCS1", name: "合模位置1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingPosition1()
    {
        return await OpenData("ClosingPosition1");
    }

    /// <summary>
    ///     合模位置2
    /// </summary>
    /// <returns></returns>
    [Method("MCS2", name: "合模位置2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingPosition2()
    {
        return await OpenData("ClosingPosition2");
    }

    /// <summary>
    ///     合模位置3
    /// </summary>
    /// <returns></returns>
    [Method("MCS3", name: "合模位置3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingPosition3()
    {
        return await OpenData("ClosingPosition3");
    }

    /// <summary>
    ///     合模位置4
    /// </summary>
    /// <returns></returns>
    [Method("MCS4", name: "合模位置4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingPosition4()
    {
        return await OpenData("ClosingPosition4");
    }

    /// <summary>
    ///     合模位置5
    /// </summary>
    /// <returns></returns>
    [Method("MCS5", name: "合模位置5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> ClosingPosition5()
    {
        return await OpenData("ClosingPosition5");
    }

    #endregion

    #region 开模速度

    /// <summary>
    ///     开模速度1
    /// </summary>
    /// <returns></returns>
    [Method("MOV1", name: "开模速度1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningSpeed1()
    {
        return await OpenData("OpeningSpeed1");
    }

    /// <summary>
    ///     开模速度2
    /// </summary>
    /// <returns></returns>
    [Method("MOV2", name: "开模速度2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningSpeed2()
    {
        return await OpenData("OpeningSpeed2");
    }

    /// <summary>
    ///     开模速度3
    /// </summary>
    /// <returns></returns>
    [Method("MOV3", name: "开模速度3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningSpeed3()
    {
        return await OpenData("OpeningSpeed3");
    }

    /// <summary>
    ///     开模速度4
    /// </summary>
    /// <returns></returns>
    [Method("MOV4", name: "开模速度4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningSpeed4()
    {
        return await OpenData("OpeningSpeed4");
    }

    /// <summary>
    ///     开模速度5
    /// </summary>
    /// <returns></returns>
    [Method("MOV5", name: "开模速度5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningSpeed5()
    {
        return await OpenData("OpeningSpeed5");
    }

    #endregion

    #region 开模压力

    /// <summary>
    ///     开模压力1
    /// </summary>
    /// <returns></returns>
    [Method("MOP1", name: "开模压力1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningPressure1()
    {
        return await OpenData("OpeningPressure1");
    }

    /// <summary>
    ///     开模压力2
    /// </summary>
    /// <returns></returns>
    [Method("MOP2", name: "开模压力2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningPressure2()
    {
        return await OpenData("OpeningPressure2");
    }

    /// <summary>
    ///     开模压力3
    /// </summary>
    /// <returns></returns>
    [Method("MOP3", name: "开模压力3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningPressure3()
    {
        return await OpenData("OpeningPressure3");
    }

    /// <summary>
    ///     开模压力4
    /// </summary>
    /// <returns></returns>
    [Method("MOP4", name: "开模压力4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningPressure4()
    {
        return await OpenData("OpeningPressure4");
    }

    /// <summary>
    ///     开模压力5
    /// </summary>
    /// <returns></returns>
    [Method("MOP5", name: "开模压力5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningPressure5()
    {
        return await OpenData("OpeningPressure5");
    }

    #endregion

    #region 开模位置

    /// <summary>
    ///     开模位置1
    /// </summary>
    /// <returns></returns>
    [Method("MOS1", name: "开模位置1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningPosition1()
    {
        return await OpenData("OpeningPosition1");
    }

    /// <summary>
    ///     开模位置2
    /// </summary>
    /// <returns></returns>
    [Method("MOS2", name: "开模位置2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningPosition2()
    {
        return await OpenData("OpeningPosition2");
    }

    /// <summary>
    ///     开模位置3
    /// </summary>
    /// <returns></returns>
    [Method("MOS3", name: "开模位置3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningPosition3()
    {
        return await OpenData("OpeningPosition3");
    }

    /// <summary>
    ///     开模位置4
    /// </summary>
    /// <returns></returns>
    [Method("MOS4", name: "开模位置4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningPosition4()
    {
        return await OpenData("OpeningPosition4");
    }

    /// <summary>
    ///     开模位置5
    /// </summary>
    /// <returns></returns>
    [Method("MOS5", name: "开模位置5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningPosition5()
    {
        return await OpenData("OpeningPosition5");
    }

    #endregion

    #region 注射压力

    /// <summary>
    ///     注射压力1
    /// </summary>
    /// <returns></returns>
    [Method("IP1", name: "注射压力1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure1()
    {
        return await OpenData("InjectionPressure1");
    }

    /// <summary>
    ///     注射压力2
    /// </summary>
    /// <returns></returns>
    [Method("IP2", name: "注射压力2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure2()
    {
        return await OpenData("InjectionPressure2");
    }

    /// <summary>
    ///     注射压力3
    /// </summary>
    /// <returns></returns>
    [Method("IP3", name: "注射压力3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure3()
    {
        return await OpenData("InjectionPressure3");
    }

    /// <summary>
    ///     注射压力4
    /// </summary>
    /// <returns></returns>
    [Method("IP4", name: "注射压力4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure4()
    {
        return await OpenData("InjectionPressure4");
    }

    /// <summary>
    ///     注射压力5
    /// </summary>
    /// <returns></returns>
    [Method("IP5", name: "注射压力5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPressure5()
    {
        return await OpenData("InjectionPressure5");
    }

    #endregion

    #region 注射速度

    /// <summary>
    ///     注射速度1
    /// </summary>
    /// <returns></returns>
    [Method("IV1", name: "注射速度1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionSpeed1()
    {
        return await OpenData("InjectionSpeed1");
    }

    /// <summary>
    ///     注射速度2
    /// </summary>
    /// <returns></returns>
    [Method("IV2", name: "注射速度2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionSpeed2()
    {
        return await OpenData("InjectionSpeed2");
    }

    /// <summary>
    ///     注射速度3
    /// </summary>
    /// <returns></returns>
    [Method("IV3", name: "注射速度3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionSpeed3()
    {
        return await OpenData("InjectionSpeed3");
    }

    /// <summary>
    ///     注射速度4
    /// </summary>
    /// <returns></returns>
    [Method("IV4", name: "注射速度4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionSpeed4()
    {
        return await OpenData("InjectionSpeed4");
    }

    /// <summary>
    ///     注射速度5
    /// </summary>
    /// <returns></returns>
    [Method("IV5", name: "注射速度5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionSpeed5()
    {
        return await OpenData("InjectionSpeed5");
    }

    #endregion

    #region 注射位置

    /// <summary>
    ///     注射位置1
    /// </summary>
    /// <returns></returns>
    [Method("IS1", name: "注射位置1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPosition1()
    {
        return await OpenData("InjectionPosition1");
    }

    /// <summary>
    ///     注射位置2
    /// </summary>
    /// <returns></returns>
    [Method("IS2", name: "注射位置2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPosition2()
    {
        return await OpenData("InjectionPosition2");
    }

    /// <summary>
    ///     注射位置3
    /// </summary>
    /// <returns></returns>
    [Method("IS3", name: "注射位置3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPosition3()
    {
        return await OpenData("InjectionPosition3");
    }

    /// <summary>
    ///     注射位置4
    /// </summary>
    /// <returns></returns>
    [Method("IS4", name: "注射位置4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPosition4()
    {
        return await OpenData("InjectionPosition4");
    }

    /// <summary>
    ///     注射位置5
    /// </summary>
    /// <returns></returns>
    [Method("IS5", name: "注射位置5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> InjectionPosition5()
    {
        return await OpenData("InjectionPosition5");
    }

    #endregion

    #region 射退压力

    /// <summary>
    ///     射退压力1
    /// </summary>
    /// <returns></returns>
    [Method("SBP1", name: "射退压力1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> RetractionPressure1()
    {
        return await OpenData("RetractionPressure1");
    }

    /// <summary>
    ///     射退压力2
    /// </summary>
    /// <returns></returns>
    [Method("SBP2", name: "射退压力2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> RetractionPressure2()
    {
        return await OpenData("RetractionPressure2");
    }

    #endregion

    #region 射退速度

    /// <summary>
    ///     射退速度1
    /// </summary>
    /// <returns></returns>
    [Method("SBV1", name: "射退速度1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> RetractionSpeed1()
    {
        return await OpenData("RetractionSpeed1");
    }

    /// <summary>
    ///     射退速度2
    /// </summary>
    /// <returns></returns>
    [Method("SBV2", name: "射退速度2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> RetractionSpeed2()
    {
        return await OpenData("RetractionSpeed2");
    }

    #endregion

    #region 射退时间

    /// <summary>
    ///     射退时间1
    /// </summary>
    /// <returns></returns>
    [Method("SBT1", name: "射退时间1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> RetractionTime1()
    {
        return await OpenData("RetractionTime1");
    }

    /// <summary>
    ///     射退时间2
    /// </summary>
    /// <returns></returns>
    [Method("SBT2", name: "射退时间2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> RetractionTime2()
    {
        return await OpenData("RetractionTime2");
    }

    #endregion

    #region 储料压力

    /// <summary>
    ///     储料压力
    /// </summary>
    /// <returns></returns>
    [Method("PLP", name: "储料压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingPressure()
    {
        return await OpenData("HoldingPressure");
    }

    /// <summary>
    ///     储料压力4
    /// </summary>
    /// <returns></returns>
    [Method("PLP4", name: "储料压力4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingPressure4()
    {
        return await OpenData("HoldingPressure4");
    }

    /// <summary>
    ///     储料压力5
    /// </summary>
    /// <returns></returns>
    [Method("PLP5", name: "储料压力5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingPressure5()
    {
        return await OpenData("HoldingPressure5");
    }

    #endregion

    #region 储料速度

    /// <summary>
    ///     储料速度
    /// </summary>
    /// <returns></returns>
    [Method("PLV", name: "储料速度", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingSpeed()
    {
        return await OpenData("HoldingSpeed");
    }

    /// <summary>
    ///     储料速度4
    /// </summary>
    /// <returns></returns>
    [Method("PLV4", name: "储料速度4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingSpeed4()
    {
        return await OpenData("HoldingSpeed4");
    }

    /// <summary>
    ///     储料速度5
    /// </summary>
    /// <returns></returns>
    [Method("PLV5", name: "储料速度5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingSpeed5()
    {
        return await OpenData("HoldingSpeed5");
    }

    #endregion

    #region 储料背压

    /// <summary>
    ///     储料背压4
    /// </summary>
    /// <returns></returns>
    [Method("PLBP4", name: "储料背压4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingBackPressure4()
    {
        return await OpenData("HoldingBackPressure4");
    }

    /// <summary>
    ///     储料背压5
    /// </summary>
    /// <returns></returns>
    [Method("PLBP5", name: "储料背压5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingBackPressure5()
    {
        return await OpenData("HoldingBackPressure5");
    }

    #endregion

    #region 储料位置

    /// <summary>
    ///     储料位置4
    /// </summary>
    /// <returns></returns>
    [Method("PLS4", name: "储料位置4", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingPosition4()
    {
        return await OpenData("HoldingPosition4");
    }

    /// <summary>
    ///     储料位置5
    /// </summary>
    /// <returns></returns>
    [Method("PLS5", name: "储料位置5", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingPosition5()
    {
        return await OpenData("HoldingPosition5");
    }

    #endregion

    #region 顶进压力

    /// <summary>
    ///     顶进压力1
    /// </summary>
    /// <returns></returns>
    [Method("EFP1", name: "顶进压力1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TopInPressure1()
    {
        return await OpenData("TopInPressure1");
    }

    /// <summary>
    ///     顶进压力2
    /// </summary>
    /// <returns></returns>
    [Method("EFP2", name: "顶进压力2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TopInPressure2()
    {
        return await OpenData("TopInPressure2");
    }

    #endregion

    #region 顶进速度

    /// <summary>
    ///     顶进速度1
    /// </summary>
    /// <returns></returns>
    [Method("EFV1", name: "顶进速度1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TopInSpeed1()
    {
        return await OpenData("TopInSpeed1");
    }

    /// <summary>
    ///     顶进速度2
    /// </summary>
    /// <returns></returns>
    [Method("EFV2", name: "顶进速度2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TopInSpeed2()
    {
        return await OpenData("TopInSpeed2");
    }

    #endregion

    #region 顶进位置

    /// <summary>
    ///     顶进位置1
    /// </summary>
    /// <returns></returns>
    [Method("EFS1", name: "顶进位置1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TopInPosition1()
    {
        return await OpenData("TopInPosition1");
    }

    /// <summary>
    ///     顶进位置2
    /// </summary>
    /// <returns></returns>
    [Method("EFS2", name: "顶进位置2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TopInPosition2()
    {
        return await OpenData("TopInPosition2");
    }

    #endregion

    #region 顶退压力

    /// <summary>
    ///     顶退压力1
    /// </summary>
    /// <returns></returns>
    [Method("EBP1", name: "顶退压力1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TopOutPressure1()
    {
        return await OpenData("TopOutPressure1");
    }

    /// <summary>
    ///     顶退压力2
    /// </summary>
    /// <returns></returns>
    [Method("EBP2", name: "顶退压力2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TopOutPressure2()
    {
        return await OpenData("TopOutPressure2");
    }

    #endregion

    #region 顶退速度

    /// <summary>
    ///     顶退速度1
    /// </summary>
    /// <returns></returns>
    [Method("EBV1", name: "顶退速度1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TopOutSpeed1()
    {
        return await OpenData("TopOutSpeed1");
    }

    /// <summary>
    ///     顶退速度2
    /// </summary>
    /// <returns></returns>
    [Method("EBV2", name: "顶退速度2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TopOutSpeed2()
    {
        return await OpenData("TopOutSpeed2");
    }

    #endregion

    #region 顶退位置

    /// <summary>
    ///     顶退位置1
    /// </summary>
    /// <returns></returns>
    [Method("EBS1", name: "顶退位置1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TopOutPosition1()
    {
        return await OpenData("TopOutPosition1");
    }

    /// <summary>
    ///     顶退位置2
    /// </summary>
    /// <returns></returns>
    [Method("EBS2", name: "顶退位置2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> TopOutPosition2()
    {
        return await OpenData("TopOutPosition2");
    }

    #endregion

    #region 座台进压力

    /// <summary>
    ///     座台进压力1
    /// </summary>
    /// <returns></returns>
    [Method("BIP1", name: "座台进压力1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> BedInPressure1()
    {
        return await OpenData("BedInPressure1");
    }

    /// <summary>
    ///     座台进压力2
    /// </summary>
    /// <returns></returns>
    [Method("BIP2", name: "座台进压力2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> BedInPressure2()
    {
        return await OpenData("BedInPressure2");
    }

    /// <summary>
    ///     座台进压力3
    /// </summary>
    /// <returns></returns>
    [Method("BIP3", name: "座台进压力3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> BedInPressure3()
    {
        return await OpenData("BedInPressure3");
    }

    #endregion

    #region 座台进速度

    /// <summary>
    ///     座台进速度1
    /// </summary>
    /// <returns></returns>
    [Method("BIS1", name: "座台进速度1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> BedInSpeed1()
    {
        return await OpenData("BedInSpeed1");
    }

    /// <summary>
    ///     座台进速度2
    /// </summary>
    /// <returns></returns>
    [Method("BIS2", name: "座台进速度2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> BedInSpeed2()
    {
        return await OpenData("BedInSpeed2");
    }

    /// <summary>
    ///     座台进速度3
    /// </summary>
    /// <returns></returns>
    [Method("BIS3", name: "座台进速度3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> BedInSpeed3()
    {
        return await OpenData("BedInSpeed3");
    }

    #endregion

    #region 座台进时间

    /// <summary>
    ///     座台进时间1
    /// </summary>
    /// <returns></returns>
    [Method("BIT1", name: "座台进时间1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> BedInTime1()
    {
        return await OpenData("BedInTime1");
    }

    /// <summary>
    ///     座台进时间2
    /// </summary>
    /// <returns></returns>
    [Method("BIT2", name: "座台进时间2", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> BedInTime2()
    {
        return await OpenData("BedInTime2");
    }

    /// <summary>
    ///     座台进时间3
    /// </summary>
    /// <returns></returns>
    [Method("BIT3", name: "座台进时间3", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> BedInTime3()
    {
        return await OpenData("BedInTime3");
    }

    #endregion


    #region 其他

    /// <summary>
    ///     开模行程
    /// </summary>
    /// <returns></returns>
    [Method("OS", name: "开模行程", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> OpeningStroke()
    {
        return await OpenData("OpeningStroke");
    }

    /// <summary>
    ///     储料距离
    /// </summary>
    /// <returns></returns>
    [Method("HD", name: "储料距离", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> HoldingDistance()
    {
        return await OpenData("HoldingDistance");
    }

    /// <summary>
    ///     座台退压力1
    /// </summary>
    /// <returns></returns>
    [Method("BOP1", name: "座台退压力1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> BedOutPressure1()
    {
        return await OpenData("BedOutPressure1");
    }


    /// <summary>
    ///     座台退速度1
    /// </summary>
    /// <returns></returns>
    [Method("BOS1", name: "座台退速度1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> BedOutSpeed1()
    {
        return await OpenData("BedOutSpeed1");
    }

    /// <summary>
    ///     座台退时间1
    /// </summary>
    /// <returns></returns>
    [Method("BOT1", name: "座台退时间1", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> BedOutTime1()
    {
        return await OpenData("BedOutTime1");
    }

    /// <summary>
    ///     调模退压力
    /// </summary>
    /// <returns></returns>
    [Method("MOP", name: "调模退压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldOutPressure()
    {
        return await OpenData("MoldOutPressure");
    }

    /// <summary>
    ///     调模退速度
    /// </summary>
    /// <returns></returns>
    [Method("MOS", name: "调模退速度", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldOutSpeed()
    {
        return await OpenData("MoldOutSpeed");
    }

    /// <summary>
    ///     调模进压力
    /// </summary>
    /// <returns></returns>
    [Method("MIP", name: "调模进压力", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldInPressure()
    {
        return await OpenData("MoldInPressure");
    }

    /// <summary>
    ///     调模进速度
    /// </summary>
    /// <returns></returns>
    [Method("MIS", name: "调模进速度", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldInSpeed()
    {
        return await OpenData("MoldInSpeed");
    }

    /// <summary>
    ///     调模进慢速
    /// </summary>
    /// <returns></returns>
    [Method("MISS", name: "调模进慢速", dataType: TransPondDataTypeEnum.Double)]
    public async Task<DriverReturnValueModel> MoldInSlowSpeed()
    {
        return await OpenData("MoldInSlowSpeed");
    }

    #endregion

    #endregion

    #endregion 采集点位
}