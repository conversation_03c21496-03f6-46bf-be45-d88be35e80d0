using System;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Furion.Logging;
using HslCommunication;

namespace WarpingMachine;

/// <summary>
/// 卷绕机驱动
/// </summary>
[DriverInfo("WarpingMachine", "V1.0.0", "卷绕机")]
public class WarpingMachine : BaseDeviceProtocolCollector, IDriver
{
  private readonly WarpingMachineData _warpingMachineData = new();
  private ClientHandler _client;

  public WarpingMachine(DriverInfoDto driverInfo)
  {
    DriverInfo = driverInfo;
  }

  /// <summary>
  /// 重连周期(s)
  /// </summary>
  [ConfigParameter("重连周期(s)", GroupName = "高级配置", Remark = "当设备关机后下一轮进行重新连接的时间")]
  public override int ReConnTime { get; set; } = 5;

  /// <summary>
  /// 写入间隔周期(ms)
  /// </summary>
  [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置", Display = false)]
  public override int WriteInterval { get; set; } = 120;

  /// <summary>
  /// IP地址
  /// </summary>
  [ConfigParameter("IP地址")]
  public string IpAddress { get; set; } = "127.0.0.1";

  /// <summary>
  /// 网口名称
  /// </summary>
  [ConfigParameter("网口名称")]
  public string InterfaceName { get; set; } = "";

  /// <summary>
  /// 端口号
  /// </summary>
  [ConfigParameter("端口号")]
  public int Port { get; set; } = 8001;

  /// <summary>
  /// 批量读取
  /// </summary>
  [ConfigParameter("批量读取", GroupName = "高级配置", Display = false, Remark = "不连续报文组成合并包，减少查询包次数，缩短轮询周期")]
  public virtual BoolEnum BulkRead { get; set; } = BoolEnum.True;

  /// <summary>
  /// 是否连接
  /// </summary>
  public override bool IsConnected { get; set; }

  /// <summary>
  /// 连接
  /// </summary>
  /// <returns></returns>
  public DeviceConnectDto Connect()
  {
    try
    {
      // 记录连接开始
      Log.Information($"开始连接卷绕机设备，时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
      Log.Information($"连接参数 - 网口: {InterfaceName}, IP: {IpAddress}, 端口: {Port}");

      // 创建客户端
      _client = new ClientHandler(_warpingMachineData, DriverInfo);

      // 连接
      _client.Connect(InterfaceName, IpAddress, Port);

      // 设置连接状态
      IsConnected = true;
      OperateResult = new OperateResult
      {
        // 设置操作结果
        IsSuccess = IsConnected,
        // 设置操作结果消息
        Message = IsConnected ? "连接成功" : "连接失败"
      };

      // 记录连接结果
      Log.Information($"卷绕机设备连接{(IsConnected ? "成功" : "失败")}，时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
    }
    catch (Exception ex)
    {
      Log.Error($"连接卷绕机设备异常，时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
      Log.Error("连接异常: " + ex.Message);
      Log.Error("连接异常: " + ex.StackTrace);
      Log.Error("连接异常: " + ex.InnerException?.Message);
      Log.Error("连接异常: " + ex.InnerException?.StackTrace);

      // 尝试释放可能已创建的资源
      if (_client != null)
      {
        try
        {
          _client.Dispose();
          _client = null;
        }
        catch (Exception disposeEx)
        {
          Log.Error($"释放客户端资源异常: {disposeEx.Message}");
        }
      }

      // 设置操作结果
      return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
    }

    return ResConnect();
  }

  /// <summary>
  /// 断开连接
  /// </summary>
  public override void Close()
  {
    try
    {
      Log.Information($"开始断开连接，时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
      _client?.Dispose();
      _client = null;
      IsConnected = false;
      Log.Information($"连接已断开，时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
    }
    catch (Exception ex)
    {
      Log.Error($"断开连接时发生异常: {ex.Message}");
    }
  }

  #region 采集点位

  /// <summary>
  /// 机器运行状态
  /// </summary>
  [Method("STATUS", name: "机器运行状态", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> Status()
  {
    return await PowerOnData("Status");
  }

  /// <summary>
  /// 纺位号
  /// </summary>
  [Method("POSITION", name: "纺位号", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> SpinningPosition()
  {
    return await PowerOnData("SpinningPosition");
  }

  /// <summary>
  /// 配方号
  /// </summary>
  [Method("RECIPE", name: "配方号", dataType: TransPondDataTypeEnum.String)]
  public async Task<DriverReturnValueModel> Recipe()
  {
    var output = await PowerOnData("Recipe");
    output.DataType = DataTypeEnum.String;
    return output;
  }

  /// <summary>
  /// 卷绕直径
  /// </summary>
  [Method("DIAMETER", name: "卷绕直径", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> WarpingDiameter()
  {
    return await PowerOnData("WarpingDiameter");
  }

  /// <summary>
  /// 剩余卷绕时间
  /// </summary>
  [Method("REMAINING_TIME", name: "剩余卷绕时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> RemainingTime()
  {
    return await PowerOnData("RemainingTime");
  }

  /// <summary>
  /// 实际卷绕速度
  /// </summary>
  [Method("ACTUAL_SPEED", name: "实际卷绕速度", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ActualSpeed()
  {
    return await PowerOnData("ActualSpeed");
  }

  /// <summary>
  /// 设定卷绕速度
  /// </summary>
  [Method("SET_SPEED", name: "设定卷绕速度", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> WarpingSpeed()
  {
    return await PowerOnData("WarpingSpeed");
  }

  /// <summary>
  /// 开始爬升速度
  /// </summary>
  [Method("START_CLIMB_SPEED", name: "开始爬升速度", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> StartClimbSpeed()
  {
    return await PowerOnData("StartClimbSpeed");
  }

  /// <summary>
  /// 成型角-开始
  /// </summary>
  [Method("START_ANGLE", name: "成型角-开始", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> StartAngle()
  {
    return await PowerOnData("StartAngle");
  }

  /// <summary>
  /// 成型角-切换
  /// </summary>
  [Method("SWITCH_ANGLE", name: "成型角-切换", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SwitchAngle()
  {
    return await PowerOnData("SwitchAngle");
  }

  /// <summary>
  /// 升头
  /// </summary>
  [Method("LIFT_HEAD", name: "升头", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> LiftHead()
  {
    return await PowerOnData("LiftHead");
  }

  /// <summary>
  /// 超喂(切换)
  /// </summary>
  [Method("OVERFEED", name: "超喂", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> Overfeed()
  {
    return await PowerOnData("Overfeed");
  }

  /// <summary>
  /// 纸管直径
  /// </summary>
  [Method("TUBE_RADIUS", name: "纸管直径", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> TubeRadius()
  {
    return await PowerOnData("TubeRadius");
  }

  /// <summary>
  /// 丝饼数量
  /// </summary>
  [Method("YARN_CAKE_COUNT", name: "丝饼数量", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> YarnCakeCount()
  {
    return await PowerOnData("YarnCakeCount");
  }

  /// <summary>
  /// 方式A切换到方式B-卷绕直径
  /// </summary>
  [Method("SWITCH_DIAMETER_AB", name: "方式A切换到方式B-卷绕直径", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SwitchDiameterAB()
  {
    return await PowerOnData("SwitchDiameterAB");
  }

  /// <summary>
  /// 摆频防叠-振幅
  /// </summary>
  [Method("SWING_AMPLITUDE", name: "摆频防叠-振幅", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SwingAmplitude()
  {
    return await PowerOnData("SwingAmplitude");
  }

  /// <summary>
  /// 摆频防叠-频率
  /// </summary>
  [Method("SWING_FREQUENCY", name: "摆频防叠-频率", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SwingFrequency()
  {
    return await PowerOnData("SwingFrequency");
  }

  /// <summary>
  /// 成型角1
  /// </summary>
  [Method("FORMING_ANGLE_1", name: "成型角1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> FormingAngle1()
  {
    return await PowerOnData("FormingAngles[0]");
  }

  /// <summary>
  /// 成型角2
  /// </summary>
  [Method("FORMING_ANGLE_2", name: "成型角2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> FormingAngle2()
  {
    return await PowerOnData("FormingAngles[1]");
  }

  /// <summary>
  /// 成型角3
  /// </summary>
  [Method("FORMING_ANGLE_3", name: "成型角3", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> FormingAngle3()
  {
    return await PowerOnData("FormingAngles[2]");
  }

  /// <summary>
  /// 成型角4
  /// </summary>
  [Method("FORMING_ANGLE_4", name: "成型角4", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> FormingAngle4()
  {
    return await PowerOnData("FormingAngles[3]");
  }

  /// <summary>
  /// 成型角5
  /// </summary>
  [Method("FORMING_ANGLE_5", name: "成型角5", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> FormingAngle5()
  {
    return await PowerOnData("FormingAngles[4]");
  }

  /// <summary>
  /// 成型角6
  /// </summary>
  [Method("FORMING_ANGLE_6", name: "成型角6", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> FormingAngle6()
  {
    return await PowerOnData("FormingAngles[5]");
  }

  /// <summary>
  /// 成型角7
  /// </summary>
  [Method("FORMING_ANGLE_7", name: "成型角7", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> FormingAngle7()
  {
    return await PowerOnData("FormingAngles[6]");
  }

  /// <summary>
  /// 成型角8
  /// </summary>
  [Method("FORMING_ANGLE_8", name: "成型角8", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> FormingAngle8()
  {
    return await PowerOnData("FormingAngles[7]");
  }

  /// <summary>
  /// 切换直径1
  /// </summary>
  [Method("SWITCH_DIAMETER_1", name: "切换直径1", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SwitchDiameter1()
  {
    return await PowerOnData("SwitchDiameters[0]");
  }

  /// <summary>
  /// 切换直径2
  /// </summary>
  [Method("SWITCH_DIAMETER_2", name: "切换直径2", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SwitchDiameter2()
  {
    return await PowerOnData("SwitchDiameters[1]");
  }

  /// <summary>
  /// 切换直径3
  /// </summary>
  [Method("SWITCH_DIAMETER_3", name: "切换直径3", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SwitchDiameter3()
  {
    return await PowerOnData("SwitchDiameters[2]");
  }

  /// <summary>
  /// 切换直径4
  /// </summary>
  [Method("SWITCH_DIAMETER_4", name: "切换直径4", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SwitchDiameter4()
  {
    return await PowerOnData("SwitchDiameters[3]");
  }

  /// <summary>
  /// 切换直径5
  /// </summary>
  [Method("SWITCH_DIAMETER_5", name: "切换直径5", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SwitchDiameter5()
  {
    return await PowerOnData("SwitchDiameters[4]");
  }

  /// <summary>
  /// 切换直径6
  /// </summary>
  [Method("SWITCH_DIAMETER_6", name: "切换直径6", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SwitchDiameter6()
  {
    return await PowerOnData("SwitchDiameters[5]");
  }

  /// <summary>
  /// 切换直径7
  /// </summary>
  [Method("SWITCH_DIAMETER_7", name: "切换直径7", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SwitchDiameter7()
  {
    return await PowerOnData("SwitchDiameters[6]");
  }

  /// <summary>
  /// 切换直径8
  /// </summary>
  [Method("SWITCH_DIAMETER_8", name: "切换直径8", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> SwitchDiameter8()
  {
    return await PowerOnData("SwitchDiameters[7]");
  }

  /// <summary>
  /// RFR-表格
  /// </summary>
  [Method("RFR_TABLE", name: "RFR-表格", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> RfrTable()
  {
    return await PowerOnData("RfrTable");
  }

  /// <summary>
  /// RFR-阶跃
  /// </summary>
  [Method("RFR_STEP", name: "RFR-阶跃", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> RfrStep()
  {
    return await PowerOnData("RfrStep");
  }

  /// <summary>
  /// 微摆防叠-振幅
  /// </summary>
  [Method("MICRO_SWING_AMPLITUDE", name: "微摆防叠-振幅", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MicroSwingAmplitude()
  {
    return await PowerOnData("MicroSwingAmplitude");
  }

  /// <summary>
  /// 微摆防叠-频率
  /// </summary>
  [Method("MICRO_SWING_FREQUENCY", name: "微摆防叠-频率", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MicroSwingFrequency()
  {
    return await PowerOnData("MicroSwingFrequency");
  }

  /// <summary>
  /// 卷绕时间-随机落筒
  /// </summary>
  [Method("WARPING_TIME", name: "卷绕时间-随机落筒", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> WarpingTime()
  {
    return await PowerOnData("WarpingTime");
  }

  /// <summary>
  /// 报警时间
  /// </summary>
  [Method("ALARM_TIME", name: "报警时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> AlarmTime()
  {
    return await PowerOnData("AlarmTime");
  }

  /// <summary>
  /// 最大延长的卷绕时间
  /// </summary>
  [Method("MAX_EXTENDED_TIME", name: "最大延长的卷绕时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> MaxExtendedTime()
  {
    return await PowerOnData("MaxExtendedTime");
  }

  /// <summary>
  /// 爬升时间
  /// </summary>
  [Method("CLIMB_TIME", name: "爬升时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> ClimbTime()
  {
    return await PowerOnData("ClimbTime");
  }

  /// <summary>
  /// 废丝卷绕时间
  /// </summary>
  [Method("WASTE_YARN_TIME", name: "废丝卷绕时间", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> WasteYarnTime()
  {
    return await PowerOnData("WasteYarnTime");
  }

  /// <summary>
  /// 首次升头时废丝卷绕时间
  /// </summary>
  [Method("FIRST_LIFT_WASTE_TIME", name: "首次升头时废丝卷绕时间", dataType: TransPondDataTypeEnum.Int)]
  public async Task<DriverReturnValueModel> FirstLiftWasteTime()
  {
    return await PowerOnData("FirstLiftWasteTime");
  }

  /// <summary>
  /// 落筒通知的时刻-剩余时间
  /// </summary>
  [Method("DOFFING_NOTIFY_TIME", name: "落筒通知的时刻-剩余时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> DoffingNotifyTime()
  {
    return await PowerOnData("DoffingNotifyTime");
  }

  /// <summary>
  /// 网络1打开的延迟时间
  /// </summary>
  [Method("NETWORK1_OPEN_DELAY", name: "网络1打开的延迟时间", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> Network1OpenDelay()
  {
    return await PowerOnData("Network1OpenDelay");
  }

  /// <summary>
  /// 网络1延迟关闭的时间提前
  /// </summary>
  [Method("NETWORK1_CLOSE_ADVANCE", name: "网络1延迟关闭的时间提前", dataType: TransPondDataTypeEnum.Double)]
  public async Task<DriverReturnValueModel> Network1CloseAdvance()
  {
    return await PowerOnData("Network1CloseAdvance");
  }

  #endregion

  /// <summary>
  /// 获取数据
  /// </summary>
  /// <param name="identifier"></param>
  /// <returns></returns>
  private async Task<DriverReturnValueModel> PowerOnData(string identifier)
  {
    // 默认返回Double类型
    var ret = new DriverReturnValueModel { DataType = DataTypeEnum.Double };
    try
    {
      // 检查是否连接
      if (IsConnected)
      {
        // 检查是否是数组访问
        if (identifier.Contains("["))
        {
          var arrayName = identifier.Substring(0, identifier.IndexOf("["));
          var indexStr = identifier.Substring(identifier.IndexOf("[") + 1, identifier.Length - identifier.IndexOf("[") - 2);
          var index = int.Parse(indexStr);
          // 获取数组属性
          var propertyInfo = typeof(WarpingMachineData).GetProperty(arrayName);
          if (propertyInfo == null)
          {
            ret.ErrorCode = 9999;
            ret.Message = $"属性：{arrayName} 未找到";
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Value = null;
          }
          else
          {
            // 获取数组
            var array = propertyInfo.GetValue(_warpingMachineData) as Array;
            if (array != null && index < array.Length)
            {
              ret.Value = array.GetValue(index);
              ret.VariableStatus = VariableStatusTypeEnum.Good;
            }
            else
            {
              ret.ErrorCode = 9999;
              ret.Message = $"数组索引越界或非数组属性：{identifier}";
              ret.VariableStatus = VariableStatusTypeEnum.Bad;
              ret.Value = null;
            }
          }
        }
        else
        {
          var propertyInfo = typeof(WarpingMachineData).GetProperty(identifier);
          if (propertyInfo == null)
          {
            ret.ErrorCode = 9999;
            ret.Message = $"属性：{identifier} 未找到";
            ret.VariableStatus = VariableStatusTypeEnum.Bad;
            ret.Value = null;
          }
          else
          {
            ret.Value = propertyInfo.GetValue(_warpingMachineData);
            ret.VariableStatus = VariableStatusTypeEnum.Good;
          }
        }
      }
      else
      {
        ret.ErrorCode = 9999;
        ret.Message = "设备未连接";
        ret.VariableStatus = VariableStatusTypeEnum.Bad;
        ret.Value = null;
      }
    }
    catch (Exception ex)
    {
      ret.ErrorCode = 9999;
      ret.Message = ex.Message;
      ret.VariableStatus = VariableStatusTypeEnum.Bad;
      ret.Value = null;
    }

    return ret;
  }
}