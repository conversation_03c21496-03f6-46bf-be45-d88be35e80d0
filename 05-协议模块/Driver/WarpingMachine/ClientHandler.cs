using System;
using System.Linq;
using System.Text;
using System.Threading;
using SharpPcap;
using PacketDotNet;
using Driver.Core.Models;
using Furion.Logging;
using System.Collections.Generic;
using System.Timers;
using System.Threading.Tasks;

namespace WarpingMachine;

/// <summary>
/// 络抓包客户端处理类
/// 负责网络数据包的捕获和解析
/// </summary>
public class ClientHandler : IDisposable
{
  // 存储解析后的数据
  private readonly WarpingMachineData _data;
  // 驱动信息，用于日志记录等
  private readonly DriverInfoDto _driverInfo;
  // 用于控制抓包线程的取消标记
  private readonly CancellationTokenSource _tokenSource = new();
  // 网络抓包设备实例
  private ILiveDevice _captureDevice;
  // 要监听的IP地址
  private string _ipAddress;
  // 抓包工作线程
  private Thread _captureThread;
  // 用于累积分片数据包的缓冲区
  private readonly List<byte> _packetBuffer = new();
  private int _expectedPacketLength = 0;
  // 添加新的字段用于处理发送给设备的分包
  private readonly List<byte> _commandPacketBuffer = new();
  private int _expectedCommandLength = 0;

  // 添加状态监控相关字段
  private DateTime _lastPacketTime = DateTime.MinValue;
  private int _packetCount = 0;
  private System.Timers.Timer _watchdogTimer;
  private readonly int _watchdogInterval = 30000; // 看门狗检查间隔，默认30秒
  private readonly int _maxPacketIdleTime = 60000; // 最大数据包空闲时间，默认60秒
  private readonly object _stateLock = new object();
  private bool _isRestarting = false;

  // 存储当前连接信息以便重连
  private string _currentInterfaceName;
  private int _currentPort;

  /// <summary>
  /// 判断抓包是否正在进行
  /// </summary>
  public bool IsConnected => _captureDevice != null && _captureThread?.IsAlive == true;

  /// <summary>
  /// 构造函数
  /// </summary>
  /// <param name="data">数据存储对象</param>
  /// <param name="driverInfo">驱动信息对象</param>
  public ClientHandler(WarpingMachineData data, DriverInfoDto driverInfo)
  {
    _data = data;
    _driverInfo = driverInfo;
  }

  private string filter = "";

  /// <summary>
  /// 连接并开始抓包
  /// </summary>
  /// <param name="interfaceName">网络接口名称</param>
  /// <param name="ipAddress">要监听的IP地址</param>
  /// <param name="port">要监听的端口</param>
  public void Connect(string interfaceName, string ipAddress, int port)
  {
    _ipAddress = ipAddress;
    // 保存连接信息以便后续重连
    _currentInterfaceName = interfaceName;
    _currentPort = port;

    // 记录连接开始日志
    _ = _driverInfo.Socket.Send($"开始连接网络接口，时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}", _driverInfo.DeviceId + "_Logs");
    Log.Information($"开始连接网络接口 {interfaceName}，时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");

    // 获取系统中所有可用的网络接口
    var devices = CaptureDeviceList.Instance;
    if (devices.Count == 0)
    {
      throw new Exception("没有找到可用的网络接口");
    }

    // 列出所有可用的网络接口供参考和调试
    var interfaceList = string.Join("\n", devices.Select((d, i) =>
        $"{i}. {d.Name} {d.Description ?? "[No Description]"}"));
    _ = _driverInfo.Socket.Send($"可用网络接口列表:\n{interfaceList}", _driverInfo.DeviceId + "_Logs");

    // 根据提供的接口名称查找对应的设备（不区分大小写）
    _captureDevice = devices.FirstOrDefault(d => (d.Description != null &&
        d.Description.Contains(interfaceName, StringComparison.OrdinalIgnoreCase)) ||
        (d.Name != null && d.Name.Contains(interfaceName, StringComparison.OrdinalIgnoreCase)));
    if (_captureDevice == null)
    {
      throw new Exception($"未找到网络接口: {interfaceName}");
    }

    try
    {
      // 以混杂模式打开设备，设置1秒超时
      _captureDevice.Open(new DeviceConfiguration
      {
        Mode = DeviceModes.Promiscuous,
        ReadTimeout = 1000
      });

      _ = _driverInfo.Socket.Send($"已连接到网络接口: {_captureDevice.Description}", _driverInfo.DeviceId + "_Logs");

      try
      {
        // 设置BPF过滤器，只捕获指定IP和端口的TCP/UDP数据包
        filter = $"host {ipAddress} and tcp port {port} ";
        _captureDevice.Filter = filter;
        _ = _driverInfo.Socket.Send($"使用过滤器: {filter}", _driverInfo.DeviceId + "_Logs");
      }
      catch (Exception e)
      {
        // 打印详细的错误信息
        Log.Error("设置过滤器异常: " + e.Message);
        Log.Error("设置过滤器异常: " + e.StackTrace);
        Log.Error("设置过滤器异常: " + e.InnerException?.Message);
        Log.Error("设置过滤器异常: " + e.InnerException?.StackTrace);
      }

      // 注册数据包到达事件处理器
      _captureDevice.OnPacketArrival += Device_OnPacketArrival;

      // 在后台线程中启动抓包
      _captureThread = new Thread(StartCapture)
      {
        IsBackground = true
      };
      _captureThread.Start();

      // 重置统计信息
      _packetCount = 0;
      _lastPacketTime = DateTime.Now;

      // 初始化并启动看门狗定时器
      StartWatchdog();

      _ = _driverInfo.Socket.Send($"开始监听网络接口: {_captureDevice.Name} {_captureDevice.Description}", _driverInfo.DeviceId + "_Logs");
      Log.Information($"开始监听网络接口: {_captureDevice.Name} {_captureDevice.Description}");
    }
    catch (Exception ex)
    {
      Log.Error("抓到错误：" + ex.Message);
      _ = _driverInfo.Socket.Send($"启动抓包失败: {ex.Message}", _driverInfo.DeviceId + "_Logs");
      throw;
    }
  }

  /// <summary>
  /// 初始化并启动看门狗定时器
  /// </summary>
  private void StartWatchdog()
  {
    // 如果已经有定时器在运行，先停止它
    if (_watchdogTimer != null)
    {
      _watchdogTimer.Stop();
      _watchdogTimer.Dispose();
    }

    // 创建并配置新的定时器
    _watchdogTimer = new System.Timers.Timer(_watchdogInterval);
    _watchdogTimer.Elapsed += WatchdogTimerElapsed;
    _watchdogTimer.AutoReset = true;
    _watchdogTimer.Start();

    Log.Information($"看门狗定时器已启动，检查间隔: {_watchdogInterval}ms");
    _ = _driverInfo.Socket.Send($"看门狗定时器已启动，检查间隔: {_watchdogInterval}ms", _driverInfo.DeviceId + "_Logs");
  }

  /// <summary>
  /// 抓包工作线程的主循环
  /// </summary>
  private void StartCapture()
  {
    try
    {
      _ = _driverInfo.Socket.Send($"抓包线程已启动，时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}", _driverInfo.DeviceId + "_Logs");
      Log.Information($"抓包线程已启动，时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");

      _captureDevice.StartCapture();

      // 初始化计数
      int heartbeatCounter = 0;
      const int heartbeatInterval = 30; // 每30秒发送一次心跳日志

      // 持续运行直到收到取消信号
      while (!_tokenSource.Token.IsCancellationRequested)
      {
        Thread.Sleep(1000);

        // 定期发送心跳日志，确认线程仍在运行
        heartbeatCounter++;
        if (heartbeatCounter >= heartbeatInterval)
        {
          heartbeatCounter = 0;
          TimeSpan idleTime = DateTime.Now - _lastPacketTime;

          // 仅在日志中记录，不发送到Socket以减少通信量
          Log.Information($"抓包线程心跳: 运行中, 已接收 {_packetCount} 个数据包, 空闲时间: {idleTime.TotalSeconds:F1}秒");
        }
      }

      _captureDevice.StopCapture();
      _ = _driverInfo.Socket.Send($"抓包线程正常停止，时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}", _driverInfo.DeviceId + "_Logs");
      Log.Information($"抓包线程正常停止，时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"抓包线程异常退出: {ex.Message}, 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}", _driverInfo.DeviceId + "_Logs");
      Log.Error($"抓包线程异常退出: {ex.Message}, 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
      Log.Error($"异常详情: {ex.StackTrace}");

      // 如果不是由于外部取消导致的异常，尝试自动重启
      if (!_tokenSource.Token.IsCancellationRequested && !_isRestarting)
      {
        _ = _driverInfo.Socket.Send($"即将尝试自动重启抓包...", _driverInfo.DeviceId + "_Logs");
        Log.Warning($"即将尝试自动重启抓包...");

        // 使用线程池线程执行重启，避免阻塞当前线程
        ThreadPool.QueueUserWorkItem(_ =>
        {
          try
          {
            Thread.Sleep(3000); // 延迟3秒后重启
            RestartCapture();
          }
          catch (Exception innerEx)
          {
            _ = _driverInfo.Socket.Send($"自动重启过程中发生异常: {innerEx.Message}", _driverInfo.DeviceId + "_Logs");
            Log.Error($"自动重启过程中发生异常: {innerEx.Message}");
          }
        });
      }
    }
  }

  /// <summary>
  /// 数据包到达事件处理方法
  /// </summary>
  private void Device_OnPacketArrival(object sender, PacketCapture e)
  {
    TcpPacket tcpPacket = null;
    try
    {
      // 更新数据包接收时间和计数
      _lastPacketTime = DateTime.Now;
      _packetCount++;

      // 每1000个数据包记录一次统计信息
      if (_packetCount % 1000 == 0)
      {
        Log.Information($"已接收 {_packetCount} 个数据包，当前时间: {_lastPacketTime:yyyy-MM-dd HH:mm:ss.fff}");
      }

      // 获取原始数据包
      var rawPacket = e.GetPacket();
      // 解析数据包
      var packet = Packet.ParsePacket(rawPacket.LinkLayerType, rawPacket.Data);

      // 提取TCP和IP层数据
      tcpPacket = packet.Extract<TcpPacket>();
      // 提取IP层数据
      var ipPacket = packet.Extract<IPPacket>();

      // 只处理包含有效负载的TCP数据包
      if (tcpPacket != null && ipPacket != null && tcpPacket.PayloadData.Length > 0)
      {
        // 源ip和配置ip不同 并且
        if (ipPacket.SourceAddress.ToString() != _ipAddress  && ipPacket.SourceAddress.ToString() != "***************")
        {
          _ = _driverInfo.Socket.Send($"收到不匹配的源: {ipPacket.SourceAddress} 设置 {_ipAddress}", _driverInfo.DeviceId + "_Logs");
          return;
        }
        var data = tcpPacket.PayloadData;
        bool isFromDevice = ipPacket.SourceAddress.ToString() == _ipAddress;
        string direction = isFromDevice ? "设备->上位机" : "上位机->设备";

        if (isFromDevice)
        {
          // 检查是否是新的SIMOTION报文开始
          if (data.Length >= 8 && System.Text.Encoding.ASCII.GetString(data, 0, 8) == "SIMOTION")
          {
            // 清空缓冲区并开始新的数据包
            _packetBuffer.Clear();
            _packetBuffer.AddRange(data);

            if (data.Length >= 16)
            {
              _expectedPacketLength = BitConverter.ToInt32(data, 0x0C);
              // 启用调试日志，记录每个SIMOTION报文的开始
              Log.Debug($"开始新的SIMOTION报文, 预期长度: {_expectedPacketLength} 字节, 当前长度: {data.Length} 字节");
              // _ = _driverInfo.Socket.Send($"开始新的SIMOTION报文, 预期长度: {_expectedPacketLength} 字节, 当前长度: {data.Length} 字节", _driverInfo.DeviceId + "_Logs");
            }
          }
          else if (_packetBuffer.Count > 0)
          {
            // 累积数据到缓冲区
            _packetBuffer.AddRange(data);
            // 启用调试日志，记录数据累积情况
            Log.Debug($"累积数据到缓冲区, 当前长度: {_packetBuffer.Count}/{_expectedPacketLength} 字节, 新增: {data.Length} 字节");
            // _ = _driverInfo.Socket.Send($"累积数据到缓冲区, 当前长度: {_packetBuffer.Count}/{_expectedPacketLength} 字节, 新增: {data.Length} 字节", _driverInfo.DeviceId + "_Logs");
          }

          // 如果累积的数据达到预期长度，则处理完整的数据包
          if (_expectedPacketLength > 0 && _packetBuffer.Count >= _expectedPacketLength)
          {
            Log.Debug($"收到完整数据包, 长度: {_packetBuffer.Count}/{_expectedPacketLength} 字节");
            // _ = _driverInfo.Socket.Send($"收到完整数据包, 长度: {_packetBuffer.Count}/{_expectedPacketLength} 字节", _driverInfo.DeviceId + "_Logs");
            ParseSimotionPacket(_packetBuffer.ToArray());
            _packetBuffer.Clear();
            _expectedPacketLength = 0;
          }

          // 安全检查：如果缓冲区超过预期长度很多，可能是数据出错，此时清理缓冲区
          if (_expectedPacketLength > 0 && _packetBuffer.Count > _expectedPacketLength * 2)
          {
            Log.Warning($"缓冲区数据超过预期两倍，可能出错，清理缓冲区。大小: {_packetBuffer.Count}, 预期: {_expectedPacketLength}");
            _ = _driverInfo.Socket.Send($"警告：缓冲区数据超过预期两倍，可能出错，清理缓冲区。大小: {_packetBuffer.Count}, 预期: {_expectedPacketLength}", _driverInfo.DeviceId + "_Logs");
            _packetBuffer.Clear();
            _expectedPacketLength = 0;
          }
        }
        else
        {
          // 构建更详细的数据包信息日志
          var sb = new StringBuilder();
          sb.AppendLine($"\n捕获到数据包 [{direction}]:");
          sb.AppendLine($"时间: {rawPacket.Timeval.Date:yyyy-MM-dd HH:mm:ss.fff}");
          sb.AppendLine($"源地址: {ipPacket.SourceAddress}:{tcpPacket.SourcePort}");
          sb.AppendLine($"页面设置地址: {_ipAddress}");
          sb.AppendLine($"filter: {filter}");
          sb.AppendLine($"目标地址: {ipPacket.DestinationAddress}:{tcpPacket.DestinationPort}");
          sb.AppendLine($"数据长度: {data.Length} 字节");

          // // 记录原始数据（十六进制和ASCII）
          // sb.AppendLine("\n原始数据 (十六进制 | ASCII):");
          // for (int i = 0; i < data.Length; i += 16)
          // {
          //   byte[] line = data.Skip(i).Take(16).ToArray();
          //   sb.Append($"{i:X4}: {BitConverter.ToString(line).PadRight(47, ' ')} | ");
          //   foreach (byte b in line)
          //   {
          //     char c = (b >= 32 && b <= 126) ? (char)b : '.';
          //     sb.Append(c);
          //   }
          //   sb.AppendLine();
          // }

          // 如果是SIMOTION报文，添加额外的报文信息
          if (data.Length >= 16 && Encoding.ASCII.GetString(data, 0, 8) == "SIMOTION")
          {
            int declaredLength = BitConverter.ToInt32(data, 0x0C);
            if (declaredLength != data.Length)
            {
              sb.AppendLine($"警告: 数据包长度不匹配！预期 {declaredLength} 字节，实际接收到 {data.Length} 字节");
            }
          }

          _ = _driverInfo.Socket.Send(sb.ToString(), _driverInfo.DeviceId + "_Logs");

          // 检查是否是新的命令包开始
          if (data.Length >= 8 && Encoding.ASCII.GetString(data, 0, 8) == "SIMOTION")
          {
            // 清空缓冲区并开始新的数据包
            _commandPacketBuffer.Clear();
            _commandPacketBuffer.AddRange(data);

            _expectedCommandLength = BitConverter.ToInt32(data, 0x0C);
          }
          else if (_commandPacketBuffer.Count > 0)
          {
            // 累积数据到缓冲区
            _commandPacketBuffer.AddRange(data);
            _ = _driverInfo.Socket.Send($"累积命令数据到缓冲区, 当前长度: {_commandPacketBuffer.Count}/{_expectedCommandLength} 字节, 新增: {data.Length} 字节", _driverInfo.DeviceId + "_Logs");
          }
          else
          {
            // 可能是第二段报文，直接处理
            if (data.Length == 1456)
            {
              _ = _driverInfo.Socket.Send($"进入工艺参数第二段报文处理", _driverInfo.DeviceId + "_Logs");
              ParseProcessParamPacket2(data);
            }
            else
            {
              _ = _driverInfo.Socket.Send($"未知的报文长度: {data.Length}", _driverInfo.DeviceId + "_Logs");
            }
            return;
          }

          // 如果累积的数据达到预期长度，则处理完整的数据包
          if (_expectedCommandLength > 0 && _commandPacketBuffer.Count >= _expectedCommandLength)
          {
            var completeData = _commandPacketBuffer.ToArray();
            _ = _driverInfo.Socket.Send($"收到完整命令包, 长度: {completeData.Length}/{_expectedCommandLength} 字节", _driverInfo.DeviceId + "_Logs");

            if (_expectedCommandLength == 1550) // 卷绕时间参数报文 (1456+94)
            {
              _ = _driverInfo.Socket.Send($"进入卷绕时间参数报文处理", _driverInfo.DeviceId + "_Logs");
              ParseWarpingTimePacket(completeData);
            }
            else if (_expectedCommandLength is 1456 or 1694)
            {
              _ = _driverInfo.Socket.Send("进入工艺参数第一段报文处理", _driverInfo.DeviceId + "_Logs");
              ParseCommandPacket(completeData);
            }
            else
            {
              _ = _driverInfo.Socket.Send($"··未知的完整报文长度: {_expectedCommandLength}", _driverInfo.DeviceId + "_Logs");
            }

            // 清空缓冲区
            _commandPacketBuffer.Clear();
            _expectedCommandLength = 0;
          }
        }
      }
    }
    catch (Exception ex)
    {
      var errorSb = new StringBuilder();
      errorSb.AppendLine($"处理数据包异常: {ex.Message}");
      errorSb.AppendLine($"发生时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
      errorSb.AppendLine($"异常堆栈: {ex.StackTrace}");
      if (tcpPacket != null)
      {
        errorSb.AppendLine($"TCP数据包长度: {tcpPacket.PayloadData?.Length ?? 0} 字节");
      }
      if (_packetBuffer.Count > 0)
      {
        errorSb.AppendLine($"设备数据缓冲区状态: {_packetBuffer.Count}/{_expectedPacketLength} 字节");
      }
      if (_commandPacketBuffer.Count > 0)
      {
        errorSb.AppendLine($"命令数据缓冲区状态: {_commandPacketBuffer.Count}/{_expectedCommandLength} 字节");
      }

      // 记录错误日志
      Log.Error(errorSb.ToString());
      _ = _driverInfo.Socket.Send(errorSb.ToString(), _driverInfo.DeviceId + "_Logs");

      // 如果出现连续错误，考虑清理缓冲区
      if (_packetBuffer.Count > 1000 || _commandPacketBuffer.Count > 1000)
      {
        Log.Warning("检测到缓冲区可能堆积，执行清理操作");
        CheckAndClearBuffers();
      }
    }
  }

  /// <summary>
  /// 解析发送给设备的命令数据包（第一段）
  /// </summary>
  private void ParseCommandPacket(byte[] data)
  {
    try
    {
      // 首先检查数据包的最小长度
      if (data.Length < 16)
      {
        _ = _driverInfo.Socket.Send($"命令数据包长度过短: {data.Length} 字节", _driverInfo.DeviceId + "_Logs");
        return;
      }

      // 检查是否为工艺参数报文
      string header = Encoding.ASCII.GetString(data, 0, 8);
      if (header != "SIMOTION")
      {
        _ = _driverInfo.Socket.Send($"无效的命令报文头: {header}", _driverInfo.DeviceId + "_Logs");
        return;
      }

      // 解析工艺参数报文
      var sb = new StringBuilder();
      sb.AppendLine("\n解析工艺参数报文:");

      // 解析报文长度
      int packetLength = BitConverter.ToInt32(data, 0x0C);
      sb.AppendLine($"报文长度: {packetLength} 字节 (实际收到: {data.Length} 字节)");

      // 记录原始数据
      sb.AppendLine("\n原始数据 (十六进制 | ASCII):");
      for (int i = 0; i < data.Length; i += 16)
      {
        byte[] line = data.Skip(i).Take(16).ToArray();
        sb.Append($"{i:X4}: {BitConverter.ToString(line).PadRight(47, ' ')} | ");
        foreach (byte b in line)
        {
          char c = (b >= 32 && b <= 126) ? (char)b : '.';
          sb.Append(c);
        }
        sb.AppendLine();
      }

      // 解析基本参数
      if (data.Length >= 84)
      {
        _data.WarpingSpeed = BitConverter.ToDouble(data, 36);        // 卷绕速度
        _data.StartClimbSpeed = BitConverter.ToDouble(data, 44);     // 开始爬升速度
        _data.StartAngle = BitConverter.ToDouble(data, 68);          // 成型角-开始
        _data.SwitchAngle = BitConverter.ToDouble(data, 76);         // 成型角-切换

        sb.AppendLine("\n基本参数:");
        sb.AppendLine($"卷绕速度: {_data.WarpingSpeed:F1} m/min");
        sb.AppendLine($"开始爬升速度: {_data.StartClimbSpeed:F1} m/min");
        sb.AppendLine($"成型角-开始: {_data.StartAngle:F1}°");
        sb.AppendLine($"成型角-切换: {_data.SwitchAngle:F1}°");
      }

      // 解析成型角数组
      if (data.Length >= 228)
      {
        sb.AppendLine("\n成型角数组:");
        for (int i = 0; i < 8; i++)
        {
          double angle = BitConverter.ToDouble(data, 100 + i * 16);
          _data.FormingAngles[i] = angle;
          sb.AppendLine($"成型角{i + 1}: {angle:F1}°");
        }

        // 解析切换直径数组
        sb.AppendLine("\n切换直径数组:");
        for (int i = 0; i < 8; i++)
        {
          double diameter = BitConverter.ToDouble(data, 164 + i * 16);
          _data.SwitchDiameters[i] = diameter;
          sb.AppendLine($"切换直径{i + 1}: {diameter:F1} mm");
        }
      }

      // 解析其他参数
      if (data.Length >= 280)
      {
        _data.LiftHead = BitConverter.ToDouble(data, 228);           // 升头
        _data.Overfeed = BitConverter.ToDouble(data, 236);           // 超喂(切换)
        _data.TubeRadius = BitConverter.ToDouble(data, 260);         // 纸管直径
        _data.YarnCakeCount = BitConverter.ToInt32(data, 276);       // 丝饼数

        sb.AppendLine("\n其他参数:");
        sb.AppendLine($"升头: {_data.LiftHead:F1}");
        sb.AppendLine($"超喂(切换): {_data.Overfeed:F1}");
        sb.AppendLine($"纸管直径: {_data.TubeRadius:F1} mm");
        sb.AppendLine($"丝饼数: {_data.YarnCakeCount}");
      }

      _ = _driverInfo.Socket.Send(sb.ToString(), _driverInfo.DeviceId + "_Logs");
    }
    catch (Exception ex)
    {
      var errorSb = new StringBuilder();
      errorSb.AppendLine($"解析命令数据包失败: {ex.Message}");
      errorSb.AppendLine($"数据包长度: {data.Length} 字节");
      errorSb.AppendLine($"异常堆栈: {ex.StackTrace}");

      // 记录原始数据
      errorSb.AppendLine("\n原始数据: " + BitConverter.ToString(data));

      _ = _driverInfo.Socket.Send(errorSb.ToString(), _driverInfo.DeviceId + "_Logs");
      throw;
    }
  }

  /// <summary>
  /// 设备发给上位机的SIMOTION报文
  /// </summary>
  /// <param name="data">原始数据</param>
  private void ParseSimotionPacket(byte[] data)
  {
    try
    {
      // 首先检查数据包的最小长度
      if (data.Length < 16)
      {
        // _ = _driverInfo.Socket.Send($"数据包长度过短: {data.Length} 字节", _driverInfo.DeviceId + "_Logs");
        return;
      }

      // 检查SIMOTION标识
      string header = System.Text.Encoding.ASCII.GetString(data, 0, 8);
      if (header != "SIMOTION")
      {
        // _ = _driverInfo.Socket.Send($"无效的SIMOTION报文头: {header}", _driverInfo.DeviceId + "_Logs");
        return;
      }

      // 解析报文长度 (偏移量 0x0C)
      int packetLength = BitConverter.ToInt32(data, 0x0C);

      // 记录数据包信息
      var sb = new StringBuilder();
      sb.AppendLine("\n实时数据解析结果:");
      sb.AppendLine($"报文长度: {packetLength} 字节 (实际收到: {data.Length} 字节)");
      if (packetLength < 1000)
      {
        return;
      }
      // 安全地解析各个字段
      if (data.Length >= 50)  // 纺位号
      {
        string spinningPositionStr = System.Text.Encoding.ASCII.GetString(data, 48, 2);
        _data.SpinningPosition = ushort.TryParse(spinningPositionStr, out ushort position) ? position : (ushort)0;
        sb.AppendLine($"纺位号: {_data.SpinningPosition} (原始值: {spinningPositionStr})");
      }

      if (data.Length >= 64)  // 配方号
      {
        _data.Recipe = System.Text.Encoding.ASCII.GetString(data, 56, 8).Trim();
        sb.AppendLine($"配方号: {_data.Recipe}");
      }

      if (data.Length >= 126)  // 卷绕直径
      {
        byte[] diameterBytes = new byte[4];
        Array.Copy(data, 122, diameterBytes, 0, 4);
        Array.Reverse(diameterBytes);
        _data.WarpingDiameter = BitConverter.ToSingle(diameterBytes, 0);
        sb.AppendLine($"卷绕直径: {_data.WarpingDiameter:F2} mm");
      }

      if (data.Length >= 130)  // 剩余时间
      {
        byte[] timeBytes = new byte[4];
        Array.Copy(data, 126, timeBytes, 0, 4);
        Array.Reverse(timeBytes);
        float remainingSeconds = BitConverter.ToSingle(timeBytes, 0);
        _data.RemainingTime = remainingSeconds / 60.0f;
        sb.AppendLine($"剩余时间: {_data.RemainingTime:F2} 分钟");
      }

      if (data.Length >= 148)  // 实际卷绕速度
      {
        byte[] speedBytes = new byte[4];
        Array.Copy(data, 144, speedBytes, 0, 4);
        Array.Reverse(speedBytes);
        _data.ActualSpeed = BitConverter.ToSingle(speedBytes, 0);
        sb.AppendLine($"实际速度: {_data.ActualSpeed:F2} m/min");
      }

      if (data.Length >= 338)  // 机器状态
      {
        byte[] statusBytes = new byte[2];
        Array.Copy(data, 336, statusBytes, 0, 2);
        Array.Reverse(statusBytes);
        ushort status = BitConverter.ToUInt16(statusBytes, 0);
        _data.Status = (MachineStatus)status;
        sb.AppendLine($"机器状态: {_data.Status} ({status})");
      }

      // 记录原始数据以便调试
      // sb.AppendLine("\n原始数据(16进制):");
      // for (int i = 0; i < data.Length; i += 16)
      // {
      //   var line = data.Skip(i).Take(16).ToArray();
      //   sb.AppendLine($"{i:X4}: {BitConverter.ToString(line)}");
      // }

      _ = _driverInfo.Socket.Send(sb.ToString(), _driverInfo.DeviceId + "_Logs");
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"解析SIMOTION报文失败: {ex.Message}", _driverInfo.DeviceId + "_Logs");
      _ = _driverInfo.Socket.Send($"数据包长度: {data.Length} 字节", _driverInfo.DeviceId + "_Logs");
      _ = _driverInfo.Socket.Send($"异常堆栈: {ex.StackTrace}", _driverInfo.DeviceId + "_Logs");
      throw;
    }
  }

  /// <summary>
  /// 解析发送给设备的命令数据包（第二段）
  /// </summary>
  private void ParseProcessParamPacket2(byte[] data)
  {
    try
    {
      var sb = new StringBuilder();
      sb.AppendLine("\n工艺参数(第二段)解析结果:");
      sb.AppendLine($"数据包长度: {data.Length} 字节");

      // 记录原始数据
      sb.AppendLine("\n原始数据 (十六进制 | ASCII):");
      for (int i = 0; i < data.Length; i += 16)
      {
        byte[] line = data.Skip(i).Take(16).ToArray();
        sb.Append($"{i:X4}: {BitConverter.ToString(line).PadRight(47, ' ')} | ");
        foreach (byte b in line)
        {
          char c = (b >= 32 && b <= 126) ? (char)b : '.';
          sb.Append(c);
        }
        sb.AppendLine();
      }

      if (data.Length >= 1084)
      {
        // 解析各个参数
        _data.SwitchDiameterAB = BitConverter.ToDouble(data, 1032);
        _data.SwingAmplitude = BitConverter.ToDouble(data, 1040);
        _data.SwingFrequency = BitConverter.ToDouble(data, 1048);
        _data.RfrTable = BitConverter.ToInt32(data, 1056);
        _data.RfrStep = BitConverter.ToDouble(data, 1060);
        _data.MicroSwingAmplitude = BitConverter.ToDouble(data, 1068);
        _data.MicroSwingFrequency = BitConverter.ToDouble(data, 1076);

        sb.AppendLine("\n解析结果:");
        sb.AppendLine($"方式A切换到方式B-卷绕直径: {_data.SwitchDiameterAB:F1} mm");
        sb.AppendLine($"摆频防叠-振幅: {_data.SwingAmplitude:F1} %");
        sb.AppendLine($"摆频防叠-频率: {_data.SwingFrequency:F1} /min");
        sb.AppendLine($"RFR-表格: {_data.RfrTable}");
        sb.AppendLine($"RFR-阶跃: {_data.RfrStep:F1} %");
        sb.AppendLine($"微摆防叠-振幅: {_data.MicroSwingAmplitude:F1} %");
        sb.AppendLine($"微摆防叠-频率: {_data.MicroSwingFrequency:F1} /min");
      }
      else
      {
        sb.AppendLine("\n警告: 数据包长度不足，无法解析所有参数");
      }

      _ = _driverInfo.Socket.Send(sb.ToString(), _driverInfo.DeviceId + "_Logs");
    }
    catch (Exception ex)
    {
      var errorSb = new StringBuilder();
      errorSb.AppendLine($"解析工艺参数第二段报文失败: {ex.Message}");
      errorSb.AppendLine($"数据包长度: {data.Length} 字节");
      errorSb.AppendLine($"异常堆栈: {ex.StackTrace}");

      // 记录原始数据
      errorSb.AppendLine("\n原始数据: " + BitConverter.ToString(data));

      _ = _driverInfo.Socket.Send(errorSb.ToString(), _driverInfo.DeviceId + "_Logs");
      throw;
    }
  }

  /// <summary>
  /// 解析上位机发给设备的卷绕时间参数报文
  /// </summary>
  private void ParseWarpingTimePacket(byte[] data)
  {
    try
    {
      var sb = new StringBuilder();
      sb.AppendLine("\n卷绕时间参数报文解析:");
      sb.AppendLine($"数据包长度: {data.Length} 字节");

      // 记录原始数据
      sb.AppendLine("\n原始数据 (十六进制 | ASCII):");
      for (int i = 0; i < data.Length; i += 16)
      {
        byte[] line = data.Skip(i).Take(16).ToArray();
        sb.Append($"{i:X4}: {BitConverter.ToString(line).PadRight(47, ' ')} | ");
        foreach (byte b in line)
        {
          char c = (b >= 32 && b <= 126) ? (char)b : '.';
          sb.Append(c);
        }
        sb.AppendLine();
      }

      if (data.Length >= 16)
      {
        string header = Encoding.ASCII.GetString(data, 0, 8);
        int packetLength = BitConverter.ToInt32(data, 0x0C);
        sb.AppendLine($"\n报文头: {header}");
        sb.AppendLine($"声明的报文长度: {packetLength} 字节");
      }

      // 解析各个时间参数，添加详细的数值转换过程
      sb.AppendLine("\n时间参数解析:");

      if (data.Length >= 56)
      {
        int rawWarpingTime = BitConverter.ToInt32(data, 52);
        _data.WarpingTime = rawWarpingTime * 0.001f;
        sb.AppendLine($"卷绕时间-随机落筒: {rawWarpingTime} ms = {_data.WarpingTime:F3} 秒");
      }

      if (data.Length >= 64)
      {
        int rawAlarmTime = BitConverter.ToInt32(data, 60);
        _data.AlarmTime = rawAlarmTime * 0.001f;
        sb.AppendLine($"报警时间: {rawAlarmTime} ms = {_data.AlarmTime:F3} 秒");
      }

      if (data.Length >= 72)
      {
        int rawMaxExtendedTime = BitConverter.ToInt32(data, 68);
        _data.MaxExtendedTime = rawMaxExtendedTime * 0.001f;
        sb.AppendLine($"最大延长的卷绕时间: {rawMaxExtendedTime} ms = {_data.MaxExtendedTime:F3} 秒");
      }

      if (data.Length >= 84)
      {
        int rawClimbTime = BitConverter.ToInt32(data, 80);
        _data.ClimbTime = rawClimbTime * 0.001f;
        sb.AppendLine($"爬升时间: {rawClimbTime} ms = {_data.ClimbTime:F3} 秒");
      }

      if (data.Length >= 92)
      {
        _data.WasteYarnTime = BitConverter.ToInt32(data, 88);
        sb.AppendLine($"废丝卷绕时间: {_data.WasteYarnTime} ms");
      }

      if (data.Length >= 96)
      {
        _data.FirstLiftWasteTime = BitConverter.ToInt32(data, 92);
        sb.AppendLine($"首次升头时废丝卷绕时间: {_data.FirstLiftWasteTime} ms");
      }

      if (data.Length >= 100)
      {
        int rawDoffingNotifyTime = BitConverter.ToInt32(data, 96);
        _data.DoffingNotifyTime = rawDoffingNotifyTime * 0.001f;
        sb.AppendLine($"落筒通知的时刻-剩余时间: {rawDoffingNotifyTime} ms = {_data.DoffingNotifyTime:F3} 秒");
      }

      if (data.Length >= 194)
      {
        short rawNetwork1OpenDelay = BitConverter.ToInt16(data, 192);
        _data.Network1OpenDelay = rawNetwork1OpenDelay * 0.001f;
        sb.AppendLine($"网络1打开的延迟时间: {rawNetwork1OpenDelay} ms = {_data.Network1OpenDelay:F3} 秒");
      }

      if (data.Length >= 522)
      {
        short rawNetwork1CloseAdvance = BitConverter.ToInt16(data, 520);
        _data.Network1CloseAdvance = rawNetwork1CloseAdvance * 0.001f;
        sb.AppendLine($"网络1延迟关闭的时间提前: {rawNetwork1CloseAdvance} ms = {_data.Network1CloseAdvance:F3} 秒");
      }

      // 添加数据边界检查信息
      if (data.Length < 522)
      {
        sb.AppendLine("\n警告: 数据包长度不足，某些参数可能未能完整解析");
        sb.AppendLine($"需要至少522字节，实际收到{data.Length}字节");
      }

      _ = _driverInfo.Socket.Send(sb.ToString(), _driverInfo.DeviceId + "_Logs");
    }
    catch (Exception ex)
    {
      var errorSb = new StringBuilder();
      errorSb.AppendLine($"解析卷绕时间参数报文失败: {ex.Message}");
      errorSb.AppendLine($"数据包长度: {data.Length} 字节");
      errorSb.AppendLine($"异常堆栈: {ex.StackTrace}");

      _ = _driverInfo.Socket.Send(errorSb.ToString(), _driverInfo.DeviceId + "_Logs");
      throw;
    }
  }

  /// <summary>
  /// 看门狗定时器触发事件处理
  /// </summary>
  private void WatchdogTimerElapsed(object sender, ElapsedEventArgs e)
  {
    try
    {
      // 确保不会同时执行多个重启过程
      if (_isRestarting)
      {
        return;
      }

      // 检查抓包线程是否存活
      bool threadAlive = _captureThread?.IsAlive == true;
      // 检查设备是否可用
      bool deviceReady = _captureDevice != null && _captureDevice.Started;
      // 检查上次接收数据包的时间是否超过阈值
      TimeSpan idleTime = DateTime.Now - _lastPacketTime;
      bool dataTimeout = _lastPacketTime != DateTime.MinValue && idleTime.TotalMilliseconds > _maxPacketIdleTime;

      // 记录当前状态
      StringBuilder statusLog = new StringBuilder();
      statusLog.AppendLine($"\n[看门狗状态检查] {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
      statusLog.AppendLine($"抓包线程存活: {threadAlive}");
      statusLog.AppendLine($"抓包设备就绪: {deviceReady}");
      statusLog.AppendLine($"已接收数据包数: {_packetCount}");
      statusLog.AppendLine($"上次数据包时间: {_lastPacketTime:yyyy-MM-dd HH:mm:ss.fff}");
      statusLog.AppendLine($"数据包空闲时间: {idleTime.TotalSeconds:F1}秒");
      statusLog.AppendLine($"缓冲区状态: {_packetBuffer.Count}/{_expectedPacketLength} 字节");
      statusLog.AppendLine($"命令缓冲区状态: {_commandPacketBuffer.Count}/{_expectedCommandLength} 字节");

      _ = _driverInfo.Socket.Send(statusLog.ToString(), _driverInfo.DeviceId + "_Logs");
      Log.Information(statusLog.ToString());

      // 判断是否需要重启
      if (!threadAlive || !deviceReady || dataTimeout)
      {
        string reason = !threadAlive ? "抓包线程已停止" :
                        !deviceReady ? "抓包设备未就绪" :
                        "长时间未收到数据包";

        _ = _driverInfo.Socket.Send($"[看门狗触发] 需要重启抓包功能，原因: {reason}", _driverInfo.DeviceId + "_Logs");
        Log.Warning($"[看门狗触发] 需要重启抓包功能，原因: {reason}");

        // 执行重启
        RestartCapture();
      }

      // 检查并清理可能的缓冲区积压
      CheckAndClearBuffers();
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"看门狗检查异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
      Log.Error($"看门狗检查异常: {ex.Message}");
    }
  }

  /// <summary>
  /// 检查并清理可能积压的缓冲区
  /// </summary>
  private void CheckAndClearBuffers()
  {
    // 检查主数据包缓冲区，如果有积压且长时间未更新，则清理
    if (_packetBuffer.Count > 0)
    {
      TimeSpan bufferIdleTime = DateTime.Now - _lastPacketTime;
      if (bufferIdleTime.TotalSeconds > 30) // 如果超过30秒无数据更新且缓冲区有数据
      {
        int oldCount = _packetBuffer.Count;
        _packetBuffer.Clear();
        _expectedPacketLength = 0;
        _ = _driverInfo.Socket.Send($"清理主数据包缓冲区，原有数据: {oldCount} 字节, 闲置时间: {bufferIdleTime.TotalSeconds:F1}秒", _driverInfo.DeviceId + "_Logs");
        Log.Warning($"清理主数据包缓冲区，原有数据: {oldCount} 字节, 闲置时间: {bufferIdleTime.TotalSeconds:F1}秒");
      }
    }

    // 检查命令缓冲区
    if (_commandPacketBuffer.Count > 0)
    {
      TimeSpan bufferIdleTime = DateTime.Now - _lastPacketTime;
      if (bufferIdleTime.TotalSeconds > 30) // 如果超过30秒无数据更新且缓冲区有数据
      {
        int oldCount = _commandPacketBuffer.Count;
        _commandPacketBuffer.Clear();
        _expectedCommandLength = 0;
        _ = _driverInfo.Socket.Send($"清理命令数据包缓冲区，原有数据: {oldCount} 字节, 闲置时间: {bufferIdleTime.TotalSeconds:F1}秒", _driverInfo.DeviceId + "_Logs");
        Log.Warning($"清理命令数据包缓冲区，原有数据: {oldCount} 字节, 闲置时间: {bufferIdleTime.TotalSeconds:F1}秒");
      }
    }
  }

  /// <summary>
  /// 重启抓包功能
  /// </summary>
  private void RestartCapture()
  {
    lock (_stateLock)
    {
      if (_isRestarting)
      {
        return;
      }

      _isRestarting = true;
    }

    try
    {
      _ = _driverInfo.Socket.Send($"开始重启抓包功能, 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}", _driverInfo.DeviceId + "_Logs");
      Log.Warning($"开始重启抓包功能, 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");

      // 停止现有抓包
      try
      {
        if (_captureDevice != null)
        {
          if (_captureDevice.Started)
          {
            _captureDevice.StopCapture();
          }
          _captureDevice.Close();
        }
      }
      catch (Exception ex)
      {
        _ = _driverInfo.Socket.Send($"停止现有抓包时发生异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
        Log.Error($"停止现有抓包时发生异常: {ex.Message}");
      }

      // 清理资源
      if (_captureThread != null && _captureThread.IsAlive)
      {
        try
        {
          _captureThread.Join(3000); // 等待线程结束，最多3秒
        }
        catch (Exception ex)
        {
          _ = _driverInfo.Socket.Send($"等待抓包线程结束时发生异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
          Log.Error($"等待抓包线程结束时发生异常: {ex.Message}");
        }
      }

      // 清理缓冲区
      _packetBuffer.Clear();
      _commandPacketBuffer.Clear();
      _expectedPacketLength = 0;
      _expectedCommandLength = 0;

      // 重新连接并启动抓包
      if (!string.IsNullOrEmpty(_currentInterfaceName) && !string.IsNullOrEmpty(_ipAddress) && _currentPort > 0)
      {
        Thread.Sleep(1000); // 稍微延迟以确保资源完全释放
        Connect(_currentInterfaceName, _ipAddress, _currentPort);
        _ = _driverInfo.Socket.Send($"抓包功能已重启完成, 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}", _driverInfo.DeviceId + "_Logs");
        Log.Warning($"抓包功能已重启完成, 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
      }
      else
      {
        _ = _driverInfo.Socket.Send($"无法重启抓包，缺少必要连接信息", _driverInfo.DeviceId + "_Logs");
        Log.Error($"无法重启抓包，缺少必要连接信息");
      }
    }
    catch (Exception ex)
    {
      _ = _driverInfo.Socket.Send($"重启抓包功能时发生异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
      Log.Error($"重启抓包功能时发生异常: {ex.Message}");
    }
    finally
    {
      _isRestarting = false;
    }
  }

  /// <summary>
  /// 释放资源
  /// </summary>
  public void Dispose()
  {
    try
    {
      // 记录资源释放开始
      Log.Information($"开始释放抓包资源, 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");

      // 停止看门狗定时器
      if (_watchdogTimer != null)
      {
        _watchdogTimer.Stop();
        _watchdogTimer.Elapsed -= WatchdogTimerElapsed;
        _watchdogTimer.Dispose();
        _watchdogTimer = null;
        Log.Information("看门狗定时器已停止");
      }

      // 取消抓包线程
      _tokenSource.Cancel();
      Thread.Sleep(200); // 等待抓包线程结束

      try
      {
        if (_captureDevice != null)
        {
          if (_captureDevice.Started)
          {
            _captureDevice.StopCapture();
            Log.Information("抓包设备捕获已停止");
          }
          _captureDevice.Close();
          Log.Information("抓包设备已关闭");
        }
      }
      catch (Exception ex)
      {
        Log.Error($"关闭抓包设备异常: {ex.Message}");
        _ = _driverInfo.Socket.Send($"关闭抓包设备异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
      }

      // 释放线程资源
      if (_captureThread != null && _captureThread.IsAlive)
      {
        try
        {
          _captureThread.Join(3000); // 等待线程最多3秒
          Log.Information("抓包线程已终止");
        }
        catch (Exception ex)
        {
          Log.Error($"等待抓包线程结束异常: {ex.Message}");
        }
      }

      // 取消标记处理
      _tokenSource.Dispose();

      // 清理缓冲区
      _packetBuffer.Clear();
      _commandPacketBuffer.Clear();

      // 记录资源释放完成
      Log.Information($"抓包资源释放完成, 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
      _ = _driverInfo.Socket.Send($"抓包资源已释放, 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}", _driverInfo.DeviceId + "_Logs");
    }
    catch (Exception ex)
    {
      Log.Error($"释放资源时发生异常: {ex.Message}");
      _ = _driverInfo.Socket.Send($"释放资源时发生异常: {ex.Message}", _driverInfo.DeviceId + "_Logs");
    }
  }
}