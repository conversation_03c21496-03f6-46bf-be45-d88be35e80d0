using System;

namespace WarpingMachine
{
  public class WarpingMachineData
  {
    #region 运行状态参数
    /// <summary>
    /// 报文长度
    /// </summary>
    public int PacketLength { get; set; }

    /// <summary>
    /// 纺位号
    /// </summary>
    public int SpinningPosition { get; set; }

    /// <summary>
    /// 配方号
    /// </summary>
    public string Recipe { get; set; }

    /// <summary>
    /// 卷绕直径(mm)
    /// </summary>
    public float WarpingDiameter { get; set; }

    /// <summary>
    /// 剩余卷绕时间(分钟)
    /// </summary>
    public float RemainingTime { get; set; }

    /// <summary>
    /// 实际卷绕速度(m/min)
    /// </summary>
    public float ActualSpeed { get; set; }

    /// <summary>
    /// 机器状态
    /// </summary>
    public MachineStatus Status { get; set; }
    #endregion

    #region 上位机下发参数

    /// <summary>
    /// 设定卷绕速度(m/min)
    /// </summary>
    public double WarpingSpeed { get; set; }

    /// <summary>
    /// 开始爬升速度(m/min)
    /// </summary>
    public double StartClimbSpeed { get; set; }

    /// <summary>
    /// 成型角-开始(度)
    /// </summary>
    public double StartAngle { get; set; }

    /// <summary>
    /// 成型角-切换(度)
    /// </summary>
    public double SwitchAngle { get; set; }

    /// <summary>
    /// 成型角数组(度)
    /// </summary>
    public double[] FormingAngles { get; set; } = new double[8];

    /// <summary>
    /// 切换直径数组(mm)
    /// </summary>
    public double[] SwitchDiameters { get; set; } = new double[8];

    /// <summary>
    /// 升头
    /// </summary>
    public double LiftHead { get; set; }

    /// <summary>
    /// 超喂(切换)
    /// </summary>
    public double Overfeed { get; set; }

    /// <summary>
    /// 纸管直径(mm)
    /// </summary>
    public double TubeRadius { get; set; }

    /// <summary>
    /// 丝饼数量
    /// </summary>
    public int YarnCakeCount { get; set; }
    #endregion

    #region 上位机下发参数第二部分

    /// <summary>
    /// 方式A切换到方式B-卷绕直径(mm)
    /// </summary>
    public double SwitchDiameterAB { get; set; }

    /// <summary>
    /// 摆频防叠-振幅(%)
    /// </summary>
    public double SwingAmplitude { get; set; }

    /// <summary>
    /// 摆频防叠-频率(1/min)
    /// </summary>
    public double SwingFrequency { get; set; }

    /// <summary>
    /// RFR-表格
    /// </summary>
    public int RfrTable { get; set; }

    /// <summary>
    /// RFR-阶跃(%)
    /// </summary>
    public double RfrStep { get; set; }

    /// <summary>
    /// 微摆防叠-振幅(%)
    /// </summary>
    public double MicroSwingAmplitude { get; set; }

    /// <summary>
    /// 微摆防叠-频率(1/min)
    /// </summary>
    public double MicroSwingFrequency { get; set; }
    #endregion

    #region 卷绕时间参数
    /// <summary>
    /// 卷绕时间-随机落筒(秒)
    /// </summary>
    public float WarpingTime { get; set; }

    /// <summary>
    /// 报警时间(秒)
    /// </summary>
    public float AlarmTime { get; set; }

    /// <summary>
    /// 最大延长的卷绕时间(秒)
    /// </summary>
    public float MaxExtendedTime { get; set; }

    /// <summary>
    /// 爬升时间(秒)
    /// </summary>
    public float ClimbTime { get; set; }

    /// <summary>
    /// 废丝卷绕时间(毫秒)
    /// </summary>
    public int WasteYarnTime { get; set; }

    /// <summary>
    /// 首次升头时废丝卷绕时间(毫秒)
    /// </summary>
    public int FirstLiftWasteTime { get; set; }

    /// <summary>
    /// 落筒通知的时刻-剩余时间(秒)
    /// </summary>
    public float DoffingNotifyTime { get; set; }

    /// <summary>
    /// 网络1打开的延迟时间(秒)
    /// </summary>
    public float Network1OpenDelay { get; set; }

    /// <summary>
    /// 网络1延迟关闭的时间提前(秒)
    /// </summary>
    public float Network1CloseAdvance { get; set; }
    #endregion

  }

  /// <summary>
  /// 机器状态
  /// </summary>
  public enum MachineStatus
  {
    /// <summary>
    /// 运行
    /// </summary>
    Running = 0x08,

    /// <summary>
    /// 暂停
    /// </summary>
    Paused = 0x18,

    /// <summary>
    /// 报警
    /// </summary>
    Alarm = 0x06,

    /// <summary>
    /// 完成换卷
    /// </summary>
    ChangeCompleted = 0x48
  }
}