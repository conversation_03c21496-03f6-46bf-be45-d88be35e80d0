using System;
using System.Net;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using HslCommunication;
using SnmpSharpNet;
using DateTime = Common.Extension.DateTime;

namespace Snmp;

[DriverSupported("Snmp")]
[DriverInfo("Snmp", "V1.0.0", "自由协议")]
public class Snmp : BaseDeviceProtocolCollector, IDriver
{
    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 161;
    [ConfigParameter("版本")] public SnmpVersion Version { get; set; } = SnmpVersion.Ver1;
    [ConfigParameter("Octet")] public string Octet { get; set; } = "public";
    
    [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置",Display = false)]
    public override int WriteInterval { get; set; } = 120;
    
    [ConfigParameter("批量读取",GroupName = "高级配置",Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;
    
    private AgentParameters _param;

    /// <summary>
    /// </summary>
    /// <param name="driverInfo"></param>
    public Snmp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     Read
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Read", description: "Read", name: "读取")]
    public async Task<DriverReturnValueModel> ReadY(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    public override bool IsConnected { get; set; }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            var community = new OctetString(Octet);
            _param = new AgentParameters(community) {Version = Version};
            var agent = new IpAddress(IpAddress);
            Driver = new UdpTarget((IPAddress) agent, Port, Timeout, 1);
            IsConnected = true;
            OperateResult = new OperateResult {IsSuccess = true, Message = "连接成功"};
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }

    /// <summary>
    ///     读取数据
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public override async Task<DriverReturnValueModel> Read(DriverAddressIoArgModel val)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = val.DataType,
        };

        try
        {
            if (IsConnected)
            {
                var pdu = new Pdu(PduType.Get);
                pdu.VbList.Add(val.Address);
                var result = (SnmpV1Packet) Driver.Request(pdu, _param);
                foreach (var t in result.Pdu.VbList)
                    switch (val.DataType)
                    {
                        case DataTypeEnum.Bit:
                            ret.Value = Convert.ToBoolean(t.Value?.ToString()?.Trim()) ? 1 : 0;
                            break;
                        case DataTypeEnum.Bool:
                            ret.Value = Convert.ToBoolean(t.Value?.ToString()?.Trim());
                            break;
                        case DataTypeEnum.Uint16:
                            ret.Value = Convert.ToUInt16(t.Value?.ToString()?.Trim());
                            break;
                        case DataTypeEnum.Int16:
                            ret.Value = Convert.ToInt16(t.Value?.ToString()?.Trim());
                            break;
                        case DataTypeEnum.Uint32:
                            ret.Value = Convert.ToUInt32(t.Value?.ToString()?.Trim());
                            break;
                        case DataTypeEnum.Int32:
                            ret.Value = Convert.ToInt32(t.Value?.ToString()?.Trim());
                            break;
                        case DataTypeEnum.Float:
                            ret.Value = Convert.ToSingle(t.Value?.ToString()?.Trim());
                            break;
                        case DataTypeEnum.Uint64:
                            ret.Value = Convert.ToUInt64(t.Value?.ToString()?.Trim());
                            break;
                        case DataTypeEnum.Int64:
                            ret.Value = Convert.ToInt64(t.Value?.ToString()?.Trim());
                            break;
                        case DataTypeEnum.Double:
                            ret.Value = Convert.ToDouble(t.Value?.ToString()?.Trim());
                            break;
                        case DataTypeEnum.String:
                        default:
                            ret.Value = t.Value?.ToString()?.Trim() ?? string.Empty;
                            break;
                    }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "设备未连接";
                ret.ErrorCode = -1;
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
            ret.ErrorCode = -1;
            IsConnected = false;
        }

        return ret;
    }

    /// <summary>
    ///     关闭连接,串口协议需要自行重写该方法
    /// </summary>
    public override void Close()
    {
        Driver?.Close();
        Driver?.Dispose();
    }
}