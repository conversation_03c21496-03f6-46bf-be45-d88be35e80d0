<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <OutputPath>..\..\..\..\..\01-应用服务\IotGateway/bin/$(Configuration)/$(TargetFramework)/drivers/other/</OutputPath>
        <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="SnmpSharpNet-core" Version="1.0.0"/>
    </ItemGroup>

</Project>
