using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Feng.Common.OpcUaHelper;
using HslCommunication;
using Opc.Ua;
using WriteResponse = Driver.Core.Write.Dto.WriteResponse;

namespace OpcUaClient;

[DriverSupported("OpcUa")]
[DriverInfo("OpcUa", "V1.1.0", "自由协议")]
public class OpcUa : BaseDeviceProtocolCollector, IDriver
{
    public OpcUa(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    #region 配置参数

    [ConfigParameter("url")] public string Url { get; set; } = "opc.tcp://127.0.0.1:4840/Quickstarts/ReferenceServer";

    [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置", Display = false)]
    public override int WriteInterval { get; set; } = 120;

    #endregion

    public override bool IsConnected => Driver != null && Driver.Connected;

    // private OpcUaClientHelper Driver;

    public DeviceConnectDto Connect()
    {
        try
        {
            Driver = new OpcUaClientHelper();
            Driver.ConnectServer(Url).Wait(Timeout);
            IsConnected = (OpcUaClientHelper) Driver is {Connected: true};
            OperateResult = new OperateResult {IsSuccess = IsConnected};
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接失败:【{ex.Message}】"};
        }

        return ResConnect();
    }

    public override void Close()
    {
        Driver?.Disconnect();
    }

    public override void Dispose()
    {
        Driver = null!;
    }

    /// <summary>
    ///     读取节点
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Reader", name: "读取节点", description: "")]
    public override async Task<DriverReturnValueModel> Read(DriverAddressIoArgModel val)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = val.DataType
        };

        if (IsConnected)
        {
            try
            {
                var dataValue = Driver.ReadNode(val.Address);
                if (DataValue.IsGood(dataValue))
                {
                    ret.Value = dataValue.Value;
                }
                else
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Bad;
                    ret.Message = "读取失败";
                }
            }
            catch (Exception ex)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = $"读取失败,{ex.Message}";
            }
        }
        else
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = $"OpcUa连接异常,Url:{Url}";
        }

        return ret;
    }

    /// <summary>
    ///     设备写入方法
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public override async Task<WriteResponse> WriteAsync(DriverAddressIoArgModel val)
    {
        WriteResponse response = new() {IsSuccess = false};
        try
        {
            if (!IsConnected)
            {
                response.Description = "设备连接已断开";
                return response;
            }

            var write = await Driver.WriteNodeAsync(val.Address, val.Value);
            if (write)
                response.IsSuccess = true;
        }
        catch (Exception ex)
        {
            response.Description = $"写入失败:{ex.Message},请求数据:{val}";
        }

        return response;
    }

    /// <summary>
    ///     地址分组,只初始化一次
    /// </summary>
    /// <param name="groupVal"></param>
    [Method("Init", description: "地址初始化", name: "Init")]
    public bool InitGroup(List<IGrouping<string, DriverAddressIoArgModel>> groupVal)
    {
        _batchReadDeviceAddressPoints.Clear();

        _batchReadDeviceAddressPoints = groupVal;

        return true;
    }

    /// <summary>
    ///     批量读取设备点位地址
    /// </summary>
    private List<IGrouping<string, DriverAddressIoArgModel>> _batchReadDeviceAddressPoints = new();

    [Method("BatchRead", name: "BatchRead")]
    public async Task<List<DriverReturnValueModel>> BatchRead()
    {
        List<DriverReturnValueModel> resData = new();
        try
        {
            var driverAddressIoArgModel = _batchReadDeviceAddressPoints.SelectMany(s => s);
            var driverAddressIoArgModels = driverAddressIoArgModel.OrderBy(o => o.Address).ToList();
            resData.AddRange(driverAddressIoArgModels.Select(variable => new DriverReturnValueModel
            {
                VariableStatus = VariableStatusTypeEnum.Bad,
                DataType = variable.DataType,
                Id = variable.Id
            }));
            if (IsConnected)
            {
                List<DataValue> dataValues;
                try
                {
                    dataValues = await Driver.ReadNodesAsync(driverAddressIoArgModels.Select(driverAddressIoArg => new NodeId(driverAddressIoArg.Address)).ToArray());
                }
                catch (Exception ex)
                {
                    foreach (var ret in resData)
                        ret.Message = ex.Message;
                    return resData;
                }

                for (var i = 0; i < dataValues.Count; i++)
                {
                    var variable = driverAddressIoArgModels[i];
                    if (variable == null)
                        continue;
                    var res = resData.FirstOrDefault(f => f.Id == variable.Id);
                    if (res == null)
                    {
                        resData.Add(new DriverReturnValueModel
                        {
                            DataType = variable.DataType,
                            Id = variable.Id,
                            Value = dataValues[i].Value
                        });
                    }
                    else
                    {
                        res.Value = dataValues[i].Value;
                        res.VariableStatus = VariableStatusTypeEnum.Good;
                    }
                }
            }
            else
            {
                foreach (var ret in resData)
                {
                    ret.Message = $"OpcUa连接异常,Url:{Url}";
                    ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
                }

                return resData;
            }
        }
        catch (Exception ex)
        {
            resData.Add(new DriverReturnValueModel
            {
                VariableStatus = VariableStatusTypeEnum.UnKnow,
                Message = ex.Message
            });
        }

        return resData;
    }
}