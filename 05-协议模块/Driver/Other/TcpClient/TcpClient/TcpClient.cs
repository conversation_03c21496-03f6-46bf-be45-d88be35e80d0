using System;
using System.Globalization;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using Common.Enums;
using Common.Extension;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Feng.Common.Extension;
using Furion.Logging;
using HslCommunication.BasicFramework;
using SimpleTCP;
using DateTime = Common.Extension.DateTime;

namespace TcpClient;

[DriverSupported("TcpClient")]
[DriverInfo("TcpClient", "V1.0.0", "自由协议")]
public class TcpClient : BaseDeviceProtocolCollector, IDriver
{
    [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置",Display = false)]
    public override int WriteInterval { get; set; } = 120;
    
    [ConfigParameter("批量读取",GroupName = "高级配置",Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;
    
    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 102;
    [ConfigParameter("发送指令",required:false)] public string Command { get; set; }

    private byte[] _rcvData;

    /// <summary>
    /// </summary>
    /// <param name="driverInfo"></param>
    public TcpClient(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     Read
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method(description: "Read", name: "读取")]
    public async Task<DriverReturnValueModel> ReadY(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    public override bool IsConnected
    {
        get
        {
            try
            {
                if (Driver.TcpClient == null || Driver.TcpClient.Client == null || !Driver.TcpClient.Client.Connected) return false;
                if (!Driver.TcpClient.Client.Poll(0, SelectMode.SelectRead)) return true;
                var buff = new byte[1];
                return Driver.TcpClient.Client.Receive(buff, SocketFlags.Peek) != 0;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver != null)
            {
                var simpleTcpNet = (SimpleTcpClient) Driver;
                simpleTcpNet.DataReceived -= Client_DataReceived;
                Driver?.Disconnect();
            }

            Driver = new SimpleTcpClient().Connect(IpAddress, Port);
            IsConnected = Driver.TcpClient.Connected;
            OperateResult.IsSuccess = IsConnected;
            if (IsConnected)
                OperateResult.Message = "连接成功！";
            var tcpNet = (SimpleTcpClient) Driver;
            // tcpNet.TcpClient.Client.Poll
            tcpNet.TcpClient.SendTimeout = Timeout;
            tcpNet.TcpClient.ReceiveTimeout = Timeout;
            tcpNet.DataReceived += Client_DataReceived;
        }
        catch (Exception ex)
        {
            Log.Error($"TcpClient出错啦:{ex.Message}");
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }

    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    private async Task SocketSend(string message)
    {
        await DriverInfo.Socket.Send(message, DriverInfo.DeviceId + "_Logs");
    }

    /// <summary>
    ///     读取数据
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public override async Task<DriverReturnValueModel> Read(DriverAddressIoArgModel val)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (Command.IsNotNull() && IsConnected)
            {
                _ = SocketSend($"【{DateTime.ShangHai()}】,【{IpAddress}:{Port}】,【发】:{Command}");
                Driver.Write(Command);
            }

            ret.DataType = val.DataType;
            ret.Value = _rcvData;

            if (IsConnected)
            {
                switch (val.DataType)
                {
                    case DataTypeEnum.Byte:
                        // ret.Value = _rcvData;
                        ret.Value = SoftBasic.ByteToHexString(_rcvData,' ');
                        break;
                    default:
                        ret.Value = HexToStr(SoftBasic.ByteToHexString(_rcvData,' '));
                        break;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "设备未连接";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     关闭连接,串口协议需要自行重写该方法
    /// </summary>
    public override void Close()
    {
        if (Driver == null) return;
        var tcpNet = (SimpleTcpClient) Driver;
        tcpNet.DataReceived -= Client_DataReceived;
        Driver?.Disconnect();
    }

    /// <summary>
    ///     收到服务端数据
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void Client_DataReceived(object sender, Message e)
    {
        _rcvData = e.Data;
        _ = SocketSend($"【{DateTime.ShangHai()}】,【{IpAddress}:{Port}】,【收】:{SoftBasic.ByteToHexString(_rcvData)}");
    }

    /// <summary>
    /// </summary>
    /// <param name="mHex"></param>
    /// <returns></returns>
    private static string HexToStr(string mHex) // 返回十六进制代表的字符串
    {
        mHex = mHex.Replace(" ", "");
        if (mHex.Length <= 0) return "";
        var vBytes = new byte[mHex.Length / 2];
        for (var i = 0; i < mHex.Length; i += 2)
            if (!byte.TryParse(mHex.Substring(i, 2), NumberStyles.HexNumber, null, out vBytes[i / 2]))
                vBytes[i / 2] = 0;
        return Encoding.Default.GetString(vBytes).Trim();
    }
}