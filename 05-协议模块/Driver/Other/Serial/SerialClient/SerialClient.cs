using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Common.Enums;
using Common.Extension;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using Feng.Common.Extension;
using Feng.Common.Util;
using HslCommunication.BasicFramework;
using DateTime = Common.Extension.DateTime;

namespace SerialClient;

[DriverSupported("SerialClient")]
[DriverInfo("SerialClient", "V1.0.0", "自由协议")]
public class SerialClient : BaseDeviceProtocolCollector, IDriver
{
    #region 配置参数

    [ConfigParameter("串口号", Remark = "")] public SerialNumberEnum SerialNumber { get; set; } = SerialNumberEnum.ttyS1;
    [ConfigParameter("波特率")] public BaudRateEnum BaudRate { get; set; } = BaudRateEnum.Bps9600;
    [ConfigParameter("数据位")] public int DataBits { get; set; } = 8;
    [ConfigParameter("停止位")] public StopBits Stop { get; set; } = StopBits.One;
    [ConfigParameter("校验位")] public Parity Checkout { get; set; } = Parity.None;
    [ConfigParameter("Binary")] public BoolEnum IsBinary { get; set; } = BoolEnum.True;
    
    [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置",Display = false)]
    public override int WriteInterval { get; set; } = 120;
    
    [ConfigParameter("批量读取",GroupName = "高级配置",Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    #endregion

    private List<byte> _rcvData;

    /// <summary>
    /// </summary>
    /// <param name="driverInfo"></param>
    public SerialClient(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     Read
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method(description: "Read", name: "读取")]
    public async Task<DriverReturnValueModel> ReadY(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    public override bool IsConnected { get; set; }

    // private SerialPort Driver;
    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver != null)
            {
                var driver = (SerialPort) Driver;
                driver.DataReceived -= Client_DataReceived;
                Driver?.Close();
            }

            Driver = new SerialPort();
            Driver.PortName = EnumUtil.GetEnumDesc(SerialNumber);
            Driver.BaudRate = Convert.ToInt32(EnumUtil.GetEnumDesc(BaudRate));
            Driver.DataBits = DataBits;
            Driver.StopBits = Stop; // Driver.RtsEnable = checkBox5.Checked;
            Driver.Parity = Checkout;
            Driver.ReadTimeout = Timeout;
            Driver.WriteTimeout = Timeout;

            Driver.Open();

            IsConnected = Driver.IsOpen;
            OperateResult.IsSuccess = IsConnected;
            if (IsConnected)
                OperateResult.Message = "连接成功！";
            var serialPort = (SerialPort) Driver;
            serialPort.DataReceived += Client_DataReceived;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }

    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    private async Task SocketSend(string message)
    {
        await DriverInfo.Socket.Send(message, DriverInfo.DeviceId + "_Logs");
    }

    /// <summary>
    ///     读取数据
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public override async Task<DriverReturnValueModel> Read(DriverAddressIoArgModel val)
    {
        DriverReturnValueModel ret = new();
        try
        {
            if (val.Value.IsNotNull() && IsConnected)
            {
                _ = SocketSend($"【{DateTime.ShangHai()}】,【{EnumUtil.GetEnumDesc(SerialNumber)}】,【发】:{val.Value}");
                Driver.Write(val.Value);
            }

            ret.DataType = val.DataType;
            ret.Value = _rcvData;

            if (IsConnected)
            {
                ret.Value = IsBinary == BoolEnum.True ? SoftBasic.ByteToHexString(_rcvData.ToArray(), ' ') : SoftBasic.GetAsciiStringRender(_rcvData.ToArray());
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "设备未连接";
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }

    /// <summary>
    ///     关闭连接,串口协议需要自行重写该方法
    /// </summary>
    public override void Close()
    {
        if (Driver == null) return;
        var tcpNet = (SerialPort) Driver;
        tcpNet.DataReceived -= Client_DataReceived;
        Driver?.Close();
    }

    /// <summary>
    ///     收到服务端数据
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void Client_DataReceived(object sender, SerialDataReceivedEventArgs e)
    {
        // 接收数据
        var buffer = new List<byte>();
        var data = new byte[1024];
        while (true)
        {
            Thread.Sleep(20);
            if (Driver.BytesToRead < 1) break;

            int recCount = Driver.Read(data, 0, Math.Min(Driver.BytesToRead, data.Length));

            var buffer2 = new byte[recCount];
            Array.Copy(data, 0, buffer2, 0, recCount);
            buffer.AddRange(buffer2);
        }

        if (buffer.Count == 0) return;
        _rcvData = buffer;

        _ = SocketSend($"【{DateTime.ShangHai()}】,【{EnumUtil.GetEnumDesc(SerialNumber)}】,【收】:{GetTextHeader(0, buffer.ToArray())}");
    }

    private string GetTextHeader(int code, byte[] info)
    {
        return GetTextHeader(code, IsBinary == BoolEnum.True ? SoftBasic.ByteToHexString(info, ' ') : SoftBasic.GetAsciiStringRender(info));
    }

    /// <summary>
    ///     code = 0 表示 收，code = 1 时表示 发, code = 2 时表示关闭
    /// </summary>
    /// <param name="code">操作代码</param>
    /// <param name="info">消息</param>
    /// <returns>打包后的字符串</returns>
    private string GetTextHeader(int code, string info)
    {
        var op = string.Empty;
        op = code == 0 ? "收" : code == 1 ? "发" : code == 2 ? "关" : "None";
        var sb = new StringBuilder();
        sb.Append("[" + DateTime.NowString("yyyy-MM-dd HH:mm:ss.fff") + "] ");
        sb.Append($"[{op}]   ");
        sb.Append(info);
        sb.AppendLine();
        return sb.ToString();
    }
}