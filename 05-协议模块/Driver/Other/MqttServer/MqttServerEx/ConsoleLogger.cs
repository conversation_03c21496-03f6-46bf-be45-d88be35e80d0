using System;
using Feng.IotGateway.WebSocket;
using MQTTnet.Diagnostics;

namespace MqttServer;

public class ConsoleLogger : IMqttNetLogger
{
    private readonly SendMessageService _socket;
    private readonly long _deviceId;
    public ConsoleLogger(SendMessageService socket ,long deviceId)
    {
        _socket = socket;
        _deviceId = deviceId;
    }

    private readonly object _consoleSyncRoot = new();

    public bool IsEnabled => true;

    public void Publish(MqttNetLogLevel logLevel, string source, string message, object[]? parameters, Exception? exception)
    {
        var foregroundColor = ConsoleColor.White;
        switch (logLevel)
        {
            case MqttNetLogLevel.Verbose:
                foregroundColor = ConsoleColor.White;
                break;

            case MqttNetLogLevel.Info:
                foregroundColor = ConsoleColor.Green;
                break;

            case MqttNetLogLevel.Warning:
                foregroundColor = ConsoleColor.DarkYellow;
                break;

            case MqttNetLogLevel.Error:
                foregroundColor = ConsoleColor.Red;
                break;
        }

        if (parameters?.Length > 0) message = string.Format(message, parameters);

        lock (_consoleSyncRoot)
        {
            Console.ForegroundColor = foregroundColor;
            _ = _socket.Send(message, _deviceId + "_Logs");
            if (exception != null)
            {
                _ = _socket.Send(exception.Message, _deviceId + "_Logs");
            }
        }
    }
}