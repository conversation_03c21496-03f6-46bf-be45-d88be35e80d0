using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using HslCommunication;
using MQTTnet;
using MQTTnet.Protocol;
using MQTTnet.Server;
using DateTime = Common.Extension.DateTime;

namespace MqttServer;

[DriverSupported("MqttServer")]
[DriverInfo("MqttServer", "V1.0.0", "自由协议")]
public class MqttServer : BaseDeviceProtocolCollector, IDriver
{
    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 1883;

    [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置",Display = false)]
    public override int WriteInterval { get; set; } = 120;
    
    [ConfigParameter("批量读取",GroupName = "高级配置",Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;
    
    // [ConfigParameter("UserName")] public string UserName { get; set; } = "";
    // [ConfigParameter("Password")] public string Password { get; set; } = "";

    /// <summary>
    ///     用于存储Topic收到的消息,仅保留最后一条
    /// </summary>
    private readonly Dictionary<string, string> _payLoad = new();

    /// <summary>
    ///     Read
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method(description: "Read", name: "读取")]
    public async Task<DriverReturnValueModel> GetData(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    public override bool IsConnected => Driver != null && Driver.IsStarted;

    public MqttServer(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    // private MQTTnet.Server.MqttServer Driver;
    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver == null)
            {
                var mqttServerOptions = new MqttServerOptionsBuilder()
                    .WithDefaultEndpointPort(Port)
                    .WithDefaultCommunicationTimeout(TimeSpan.FromMilliseconds(Timeout))
                    .WithDefaultEndpoint();
                //创建MQTT服务
                Driver = (MQTTnet.Server.MqttServer) new MqttFactory(new ConsoleLogger(DriverInfo.Socket, DriverInfo.DeviceId)).CreateMqttServer(mqttServerOptions.Build());
                var tcpNet = (MQTTnet.Server.MqttServer) Driver;
                //校验连接账号密码
                tcpNet.ValidatingConnectionAsync += ValidatingConnectionAsync;
                //mqtt发布消息拦截
                tcpNet.InterceptingPublishAsync += InterceptingPublishAsync;
                //启动MQTT服务
                _ = Driver.StartAsync();
                // await mqttServer.StopAsync();
                OperateResult = new OperateResult
                {
                    Message = "MQTT 服务已启动！",
                    IsSuccess = true,
                    ErrorCode = 0
                };
                IsConnected = OperateResult.IsSuccess;
            }
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }
    
    /// <summary>
    ///     读取数据
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public override async Task<DriverReturnValueModel> Read(DriverAddressIoArgModel val)
    {
        DriverReturnValueModel ret = new()
        {
            VariableStatus = VariableStatusTypeEnum.Good,
            DataType = val.DataType,
            ReadTime = DateTime.ToLong()
        };

        try
        {
            if (IsConnected)
            {
                string value;
                //是否包含topic
                if (_payLoad.ContainsKey(val.Address))
                    value = _payLoad[val.Address];
                else
                    return ret;

                try
                {
                    switch (val.DataType)
                    {
                        case DataTypeEnum.Bit:
                            ret.Value = Convert.ToBoolean(value) ? 1 : 0;
                            break;
                        case DataTypeEnum.Bool:
                            ret.Value = Convert.ToBoolean(value);
                            break;
                        case DataTypeEnum.Uint16:
                            ret.Value = Convert.ToUInt16(value);
                            break;
                        case DataTypeEnum.Int16:
                            ret.Value = Convert.ToInt16(value);
                            break;
                        case DataTypeEnum.Uint32:
                            ret.Value = Convert.ToUInt32(value);
                            break;
                        case DataTypeEnum.Int32:
                            ret.Value = Convert.ToInt32(value);
                            break;
                        case DataTypeEnum.Float:
                            ret.Value = Convert.ToSingle(value);
                            break;
                        case DataTypeEnum.Uint64:
                            ret.Value = Convert.ToUInt64(value);
                            break;
                        case DataTypeEnum.Int64:
                            ret.Value = Convert.ToInt64(value);
                            break;
                        case DataTypeEnum.Double:
                            ret.Value = Convert.ToDouble(value);
                            break;
                        case DataTypeEnum.String:
                        default:
                            ret.Value = value ?? string.Empty;
                            break;
                    }
                }
                catch (Exception e)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Bad;
                    ret.ErrorCode = -1;
                    ret.Message = e.Message;
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "设备未连接";
                ret.ErrorCode = -1;
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
            ret.ErrorCode = -1;
        }

        return ret;
    }

    /// <summary>
    ///     校验连接账号密码
    /// </summary>
    /// <param name="e"></param>
    private async Task ValidatingConnectionAsync(ValidatingConnectionEventArgs e)
    {
        if (e.ClientId.Length < 10)
        {
            e.ReasonCode = MqttConnectReasonCode.ClientIdentifierNotValid;
            return;
        }

        // if (UserName.IsNotNull() && Password.IsNotNull())
        // {
        //     if (e.Username != UserName)
        //     {
        //         e.ReasonCode = MqttConnectReasonCode.BadUserNameOrPassword;
        //         return;
        //     }
        //
        //     if (e.Password != Password)
        //     {
        //         e.ReasonCode = MqttConnectReasonCode.BadUserNameOrPassword;
        //         return;
        //     }
        // }

        e.ReasonCode = MqttConnectReasonCode.Success;
    }

    /// <summary>
    /// </summary>
    /// <param name="e"></param>
    private async Task InterceptingPublishAsync(InterceptingPublishEventArgs e)
    {
        // if (!Topic.Contains(e.ApplicationMessage.Topic))
        //     e.ProcessPublish = false;
        // else
        _payLoad[e.ApplicationMessage.Topic] = Encoding.UTF8.GetString(e.ApplicationMessage.Payload);
    }

    public override void Close()
    {
        try
        {
            var stop = (MQTTnet.Server.MqttServer) Driver;
            stop?.StopAsync();
        }
        catch (Exception e)
        {
           
        }
    }

    public override void Dispose()
    {
        Driver?.Dispose();
    }
}