using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using HslCommunication;

namespace VirtualProtocol;

/// <summary>
///     虚拟协议
/// </summary>
[DriverSupported("VirtualProtocol")]
[DriverInfo("VirtualProtocol", "V1.0.0", "虚拟协议")]
public class VirtualProtocol : BaseDeviceProtocolCollector, IDriver
{
    public VirtualProtocol(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public override void Close()
    {
        IsConnected = false;
    }

    public DeviceConnectDto Connect()
    {
        IsConnected = true;
        OperateResult = new OperateResult {IsSuccess = true, Message = ""};
        return ResConnect();
    }


    public override void Dispose()
    {
        //
    }


    #region 配置参数

    [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置", Display = false)]
    public override int WriteInterval { get; set; }

    [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    public override BoolEnum BulkRead { get; set; }

    [ConfigParameter("超时次数", GroupName = "高级配置", Remark = "设备所有属性均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备", Display = false)]
    public override int TimeOutCount { get; set; }

    [ConfigParameter("退避时间(m)", GroupName = "高级配置", Remark = "当设备连续超时达到设置超时次数时，进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询里", Display = false)]
    public override int BackoffTime { get; set; }

    [ConfigParameter("重连周期(s)", GroupName = "高级配置", Remark = "当设备关机后下一轮进行重新连接的时间", Display = false)]
    public override int ReConnTime { get; set; }

    [ConfigParameter("超时时间(ms)", GroupName = "高级配置", Remark = "发报文收到响应报文的时间，超出算超时", Display = false)]
    public override int Timeout { get; set; }

    [ConfigParameter("等待时间(s)", GroupName = "高级配置", Remark = "设备每次进行连接前等待的时间，通常针对慢设备", Display = false)]
    public override int WaitTime { get; set; }

    #endregion
}