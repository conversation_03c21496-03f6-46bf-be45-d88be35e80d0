<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <!-- <OutputPath>..\..\..\..\..\01-应用服务\IotGateway/bin/$(Configuration)/$(TargetFramework)/drivers/other/</OutputPath> -->
        <RootNamespace>OPCDaClient</RootNamespace>
        <Platforms>AnyCPU;x86</Platforms>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
        <Prefer32bit>false</Prefer32bit>
        <PlatformTarget>x86</PlatformTarget>
        <LangVersion>preview</LangVersion>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
        <PlatformTarget>x86</PlatformTarget>
        <LangVersion>preview</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\..\DriversInterface\DriversInterface.csproj"/>
    </ItemGroup>

</Project>
