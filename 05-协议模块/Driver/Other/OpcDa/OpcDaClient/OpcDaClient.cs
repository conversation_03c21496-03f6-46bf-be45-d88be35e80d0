using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using HslCommunication;
using Newtonsoft.Json.Linq;
using OPCDaClient.opcDa.COM.Da;
using ThingsGateway.Foundation.OpcDa;
using WriteResponse = Driver.Core.Write.Dto.WriteResponse;

namespace OpcDaClient;

[DriverSupported("OpcDaClient")]
[DriverInfo("OpcDaClient", "V1.0.0", "自由协议")]
internal class OpcDaClient : BaseDeviceProtocolCollector, IDriver
{
    /// <summary>
    ///     连接状态
    /// </summary>
    public override bool IsConnected => Driver is { IsConnected: true };

    private static readonly CancellationToken _token = new();

    #region 配置参数

    [ConfigParameter("IP")] public string Ip { get; set; } = "127.0.0.1";
    [ConfigParameter("OpcServer")] public string OpcServerName { get; set; } = "ICONICS.SimulatorOPCDA.2";

    [ConfigParameter("写入间隔周期(ms)", GroupName = "高级配置", Display = false)]
    public override int WriteInterval { get; set; } = 120;

    /// <summary>
    ///     批量读取
    /// </summary>
    [ConfigParameter("批量读取", GroupName = "高级配置", Display = true)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.True;

    #endregion

    /// <summary>
    /// </summary>
    /// <param name="driverInfo"></param>
    public OpcDaClient(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver != null)
            {
                Driver.DataChangedHandler -= DataChangedHandler;
                Driver.Disconnect();
                Driver.Dispose();
            }

            Driver = new OpcDaMaster();
            Driver.DataChangedHandler += DataChangedHandler;
            //载入配置
            OpcDaProperty opcNode = new()
            {
                ActiveSubscribe = true,
                OpcIP = Ip,
                OpcName = OpcServerName
            };

            Driver.Init(opcNode);

            // 启动程序
            ProtectedBeforStartAsync();
            IsConnected = Driver.IsConnected;
            OperateResult = new OperateResult { IsSuccess = IsConnected, Message = IsConnected ? "连接成功" : "连接失败" };
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接失败:【{ex.Message}】" };
        }

        return ResConnect();
    }

    public override void Close()
    {
        if (Driver != null)
            Driver.DataChangedHandler -= DataChangedHandler;
        Driver?.Disconnect();
        Driver?.Dispose();
        Driver = null!;
    }

    private bool _disposed;

    public override void Dispose()
    {
        if (_disposed) return;

        if (Driver != null)
        {
            Driver.DataChangedHandler -= DataChangedHandler;
            Driver.Disconnect();
            Driver.Dispose();
            Driver = null!;
        }

        tags.Clear();
        addrList.Clear();

        _disposed = true;
        GC.SuppressFinalize(this);
    }

    public void ProtectedBeforStartAsync()
    {
        Driver.Connect();
    }

    public Dictionary<string, List<OpcItem>> ProtectedLoadSourceRead()
    {
        return !tags.IsEmpty ? Driver.AddItemsWithSave(tags.Select(s => s.Key).ToList()) : new Dictionary<string, List<OpcItem>>();
    }

    private ConcurrentDictionary<string, string> tags = new();

    private new OpcDaMaster Driver;

    /// <summary>
    ///     读取节点
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Reader", name: "读取节点", description: "")]
    public override async Task<DriverReturnValueModel> Read(DriverAddressIoArgModel val)
    {
        SetTags(val.Address);
        DriverReturnValueModel ret = new()
        {
            DataType = val.DataType
        };

        if (IsConnected)
        {
            try
            {
                var dataValue = GetValue(val.Address);
                if (dataValue != null)
                    switch (val.DataType)
                    {
                        case DataTypeEnum.Bit:
                            ret.Value = dataValue == "True" ? 1 : 0;
                            break;
                        case DataTypeEnum.Bool:
                            ret.Value = dataValue == "True";
                            break;
                        case DataTypeEnum.Uint16:
                            if (dataValue != null) ret.Value = ushort.Parse(dataValue);
                            break;
                        case DataTypeEnum.Int16:
                            if (dataValue != null) ret.Value = short.Parse(dataValue);
                            break;
                        case DataTypeEnum.Uint32:
                            if (dataValue != null) ret.Value = uint.Parse(dataValue);
                            break;
                        case DataTypeEnum.Int32:
                            if (dataValue != null) ret.Value = int.Parse(dataValue);
                            break;
                        case DataTypeEnum.Float:
                            if (dataValue != null) ret.Value = float.Parse(dataValue);
                            break;
                        case DataTypeEnum.Uint64:
                            if (dataValue != null) ret.Value = ulong.Parse(dataValue);
                            break;
                        case DataTypeEnum.Int64:
                            if (dataValue != null) ret.Value = long.Parse(dataValue);
                            break;
                        case DataTypeEnum.Double:
                            if (dataValue != null) ret.Value = double.Parse(dataValue);
                            break;
                        case DataTypeEnum.Byte:
                            if (dataValue != null) ret.Value = byte.Parse(dataValue);
                            break;
                        case DataTypeEnum.String:
                        case DataTypeEnum.Bcd:
                        default:
                            ret.Value = dataValue;
                            break;
                    }
            }
            catch (Exception ex)
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = $"读取失败,{ex.Message}";
            }
        }
        else
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = "OpcDa连接异常";
        }

        return ret;
    }

    private void SetTags(string addr)
    {
        if (!tags.TryAdd(addr, addr)) return;
        // 添加变量
        var itemDic = ProtectedLoadSourceRead();
        if (itemDic.Count > 0) Driver.AddItems(itemDic);
    }

    private string GetValue(string addr)
    {
        return Driver.tagValues.TryGetValue(addr, out var value) ? value.ToString() : null;
    }

    /// <summary>
    ///     设备写入方法
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public override async Task<WriteResponse> WriteAsync(DriverAddressIoArgModel val)
    {
        WriteResponse response = new() { IsSuccess = false, Description = "驱动暂未实现写入功能,敬请期待" };
        return response;
    }

    private void DataChangedHandler(string name, int serverGroupHandle, List<ItemReadResult> values)
    {
        try
        {
            if (_token.IsCancellationRequested)
                return;

            Console.WriteLine($"DataChangedHandler被调用，组名：{name}，值数量：{values.Count}");

            foreach (var data in values)
            {
                if (_token.IsCancellationRequested)
                    return;
                var jToken = JToken.FromObject(data.Value);
                object newValue;
                if (jToken is JValue jValue)
                    newValue = jValue.Value;
                else
                    newValue = jToken;

                Driver.tagValues[data.Name] = newValue;
                Console.WriteLine($"更新tagValues：键='{data.Name}'，值='{newValue}'");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"DataChangedHandler异常：{ex.Message}");
        }
    }

    #region 地址初始化

    /// <summary>
    ///     地址分组,只初始化一次
    /// </summary>
    /// <param name="groupVal"></param>
    [Method("Init", description: "地址初始化", name: "Init")]
    public bool InitGroup(List<IGrouping<string, DriverAddressIoArgModel>> groupVal)
    {
        Console.WriteLine($"开始初始化地址组，组数量：{groupVal.Count}");
        addrList.Clear();
        foreach (var group in groupVal) addrList.AddRange(group.ToList());
        Console.WriteLine($"地址列表填充完成，总地址数：{addrList.Count}");

        tags = new ConcurrentDictionary<string, string>(addrList
            .GroupBy(s => s.Address) // Group by Address
            .Select(g => g.First()) // Take the first occurrence
            .ToDictionary(s => s.Address, s => s.Address));
        Console.WriteLine($"标签字典创建完成，标签数量：{tags.Count}");

        // 打印前10个标签
        int count = 0;
        foreach (var tag in tags)
        {
            if (count < 10)
            {
                Console.WriteLine($"标签[{count}]: 键='{tag.Key}', 值='{tag.Value}'");
                count++;
            }
            else
            {
                break;
            }
        }

        // 添加变量
        Console.WriteLine($"开始调用Driver.AddItemsWithSave添加变量...");
        var itemDic = Driver.AddItemsWithSave(tags.Select(s => s.Key).ToList());
        Console.WriteLine($"Driver.AddItemsWithSave返回结果，组数：{itemDic.Count}");

        if (itemDic.Count > 0)
        {
            Console.WriteLine($"开始调用Driver.AddItems添加项目...");
            Driver.AddItems(itemDic);
            Console.WriteLine($"Driver.AddItems调用完成");
        }
        else
        {
            Console.WriteLine($"警告：itemDic为空，未调用Driver.AddItems");
        }

        try
        {
            // 读取组内变量
            Console.WriteLine($"开始调用Driver.ReadItemsWithGroup读取组内变量...");
            Driver.ReadItemsWithGroup();
            Console.WriteLine($"Driver.ReadItemsWithGroup调用完成，当前tagValues数量：{Driver.tagValues.Count}");

            // 打印前10个tagValues
            count = 0;
            foreach (var tag in Driver.tagValues)
            {
                if (count < 10)
                {
                    Console.WriteLine($"tagValues[{count}]: 键='{tag.Key}', 值='{tag.Value}'");
                    count++;
                }
                else
                {
                    break;
                }
            }
        }
        catch (Exception e)
        {
            Console.WriteLine($"读取组内变量出错: {e.Message}");
            Console.WriteLine($"错误堆栈: {e.StackTrace}");
        }
        return true;
    }

    #endregion

    private List<DriverAddressIoArgModel> addrList = new();

    #region 批量读取

    /// <summary>
    /// </summary>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    [Method("Batch", description: "批量读取", name: "BatchRead")]
    public async Task<List<DriverReturnValueModel>> BatchRead()
    {
        Console.WriteLine($"批量读取开始，地址数：{addrList.Count}");

        // 主动读取所有组的变量，刷新tagValues字典
        try
        {
            Driver.ReadItemsWithGroup();
            await Task.Delay(200);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"主动读取失败: {ex.Message}");
        }

        ConcurrentBag<DriverReturnValueModel> resData = new();

        // 尝试直接从Driver.tagValues获取数据
        foreach (var addr in addrList)
        {
            if (Driver.tagValues.TryGetValue(addr.Address, out var value) && value != null)
            {
                // 确保值不是表示错误的字符串
                if (value is string strValue && strValue.StartsWith("添加失败:"))
                {
                    continue;
                }

                DriverReturnValueModel ret = new()
                {
                    DataType = addr.DataType,
                    Id = addr.Id
                };

                try
                {
                    var dataValue = value.ToString();
                    ret.Value = addr.DataType switch
                    {
                        DataTypeEnum.Bit => dataValue == "True" ? 1 : 0,
                        DataTypeEnum.Bool => dataValue == "True",
                        DataTypeEnum.Uint16 => ushort.Parse(dataValue),
                        DataTypeEnum.Int16 => short.Parse(dataValue),
                        DataTypeEnum.Uint32 => uint.Parse(dataValue),
                        DataTypeEnum.Int32 => int.Parse(dataValue),
                        DataTypeEnum.Float => float.Parse(dataValue),
                        DataTypeEnum.Uint64 => ulong.Parse(dataValue),
                        DataTypeEnum.Int64 => long.Parse(dataValue),
                        DataTypeEnum.Double => double.Parse(dataValue),
                        DataTypeEnum.Byte => byte.Parse(dataValue),
                        _ => dataValue
                    };
                    resData.Add(ret);
                }
                catch (Exception ex)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Bad;
                    ret.Message = $"读取失败,{ex.Message}";
                }
            }
        }

        Console.WriteLine($"批量读取完成：变量数：{addrList.Count}, 采集数：{resData.Count}");

        return resData.ToList();
    }

    #endregion 批量读取
}