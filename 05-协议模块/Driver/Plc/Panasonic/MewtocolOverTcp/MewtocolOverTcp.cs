using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Panasonic;
using DriversInterface;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Panasonic;

namespace MewtocolOverTcp;

[DriverSupported("MewtocolOverTcp")]
[DriverInfo("MewtocolOverTcp", "V1.2.0", "松下PLC(Panasonic)")]
public class MewtocolOverTcp : PanasonicNetworkDeviceBase, IDriver
{
    public MewtocolOverTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     PanasonicMewtocolOverTcp
    /// </summary>
    private PanasonicMewtocolOverTcp _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (PanasonicMewtocolOverTcp)value;
    }

    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new PanasonicMewtocolOverTcp(IpAddress, Port, StationNo)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}