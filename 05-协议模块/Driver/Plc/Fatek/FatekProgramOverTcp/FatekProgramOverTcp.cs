using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Fatek;
using DriversInterface;
using HslCommunication.LogNet;

namespace FatekProgramOverTcp;

[DriverSupported("FatekProgramOverTcp")]
[DriverInfo("FatekProgramOverTcp", "V1.1.0", "永宏(Fatek)")]
public class FatekProgramOverTcp : FatekNetworkDeviceBase, IDriver
{
    public FatekProgramOverTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    // private HslCommunication.Profinet.FATEK.FatekProgramOverTcp Driver;
    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    private HslCommunication.Profinet.FATEK.FatekProgramOverTcp _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.Profinet.FATEK.FatekProgramOverTcp)value;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new HslCommunication.Profinet.FATEK.FatekProgramOverTcp(IpAddress, Port)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}