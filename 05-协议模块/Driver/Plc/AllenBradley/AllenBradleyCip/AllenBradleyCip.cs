using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.AllenBradley;
using DriversInterface;
using HslCommunication.LogNet;
using HslCommunication.Profinet.AllenBradley;

namespace AllenBradleyCip;

[DriverSupported("AllenBradleyCip")]
[DriverInfo("AllenBradleyCip", "V1.1.0", "罗克韦尔(allen_bradley)")]
public class AllenBradleyCip : AllenBradleyNetworkDeviceBase, IDriver
{
    /// <summary>
    /// </summary>
    /// <param name="driverInfo"></param>
    public AllenBradleyCip(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     AllenBradleyNet
    /// </summary>
    private AllenBradleyNet _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (AllenBradleyNet)value;
    }

    /// <summary>
    /// </summary>
    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    public DeviceConnectDto Connect()
    {
        try
        {
            _driver ??= new AllenBradleyNet(IpAddress, Port)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout
            };
            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
            _driver.LogNet = new LogNetSingle("");
            _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接失败:【{ex.Message}】" };
        }

        return ResConnect();
    }
}