using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.AllenBradley;
using DriversInterface;
using HslCommunication.LogNet;
using HslCommunication.Profinet.AllenBradley;

namespace AllenBradleyPccc;

[DriverSupported("AllenBradleyPccc")]
[DriverInfo("AllenBradleyPccc", "V1.2.0", "罗克韦尔(allen_bradley)")]
public class AllenBradleyPccc : AllenBradleyNetworkDeviceBase, IDriver
{
    /// <summary>
    /// </summary>
    /// <param name="driverInfo"></param>
    public AllenBradleyPccc(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     AllenBradleyPcccNet
    /// </summary>
    private AllenBradleyPcccNet _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (AllenBradleyPcccNet)value;
    }

    public override bool IsConnected => _driver != null && (OperateResult?.IsSuccess ?? false);

    public DeviceConnectDto Connect()
    {
        try
        {
            _driver ??= new AllenBradleyPcccNet(IpAddress, Port)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout
            };
            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
            _driver.LogNet = new LogNetSingle("");
            _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接失败:【{ex.Message}】" };
        }

        return ResConnect();
    }
}