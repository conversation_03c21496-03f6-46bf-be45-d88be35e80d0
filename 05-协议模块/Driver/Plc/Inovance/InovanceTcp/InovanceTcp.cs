using System;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Inovance;
using DriversInterface;
using HslCommunication.Core;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Inovance;

namespace InovanceTcp;

[DriverSupported("InovanceTcp")]
[DriverInfo("InovanceTcp", "V1.1.0", "汇川(Inovance)")]
public class InovanceTcp : InovanceNetworkBase, IDriver
{
    public InovanceTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    //private InovanceTcpNet Driver;
    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    private InovanceTcpNet _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (InovanceTcpNet)value;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new InovanceTcpNet(IpAddress, Port, Station)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout,
                    DataFormat = DataFormat,
                    Series = Series
                };
                if (ByteTransform == BoolEnum.True)
                    _driver.ByteTransform = new RegularByteTransform();
                if (StringReverse == BoolEnum.True)
                    _driver.IsStringReverse = true;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}