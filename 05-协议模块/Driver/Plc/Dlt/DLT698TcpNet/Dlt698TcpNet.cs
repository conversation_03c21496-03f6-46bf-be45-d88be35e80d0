using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Dlt;
using DriversInterface;
using HslCommunication.LogNet;

namespace DLT698TcpNet;

[DriverSupported("Dlt698TcpNet")]
[DriverInfo("Dlt698TcpNet", "V1.1.0", "DLT(电力协议)")]
public class Dlt698TcpNet : DltNetworkBase, IDriver
{
    public Dlt698TcpNet(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    private HslCommunication.Instrument.DLT.DLT698TcpNet _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.Instrument.DLT.DLT698TcpNet)value;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new HslCommunication.Instrument.DLT.DLT698TcpNet(IpAddress, Port, Station)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}