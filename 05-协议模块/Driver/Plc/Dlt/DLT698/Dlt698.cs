using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Dlt;
using DriversInterface;
using Feng.Common.Util;
using HslCommunication.LogNet;

namespace DLT698;

[DriverSupported("Dlt698")]
[DriverInfo("Dlt698", "V1.0.0", "DLT(电力协议)")]
public class Dlt698 : DltSerialBase, IDriver
{
    public override bool IsConnected { get; set; }

    public Dlt698(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    private HslCommunication.Instrument.DLT.DLT698 _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.Instrument.DLT.DLT698)value;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            /*
             * 相同串口号只实例化一个
             * 1.判断是否存在相同实例化协议,不存在就新建一个,存在就使用同一个实例
             */
            if (!Dlt698Extension.DicDlt698.ContainsKey(SerialNumber))
            {
                _driver = new HslCommunication.Instrument.DLT.DLT698(Station);
                _driver.SerialPortInni(EnumUtil.GetEnumDesc(SerialNumber), Convert.ToInt32(EnumUtil.GetEnumDesc(BaudRate)), DataBits, Stop, Checkout);
                _driver.ReceiveTimeOut = Timeout;
                OperateResult = _driver.Open();
                IsConnected = OperateResult.IsSuccess;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
                Dlt698Extension.DicDlt698.TryAdd(SerialNumber, Driver);
            }
            else
            {
                Driver = Dlt698Extension.DicDlt698[SerialNumber];
                OperateResult = _driver.Open();
            }
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}