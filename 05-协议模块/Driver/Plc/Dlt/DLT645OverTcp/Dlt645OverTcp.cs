using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Dlt;
using DriversInterface;
using HslCommunication.LogNet;

namespace DLT645OverTcp;

[DriverSupported("Dlt645OverTcp")]
[DriverInfo("Dlt645OverTcp", "V1.1.0", "DLT(电力协议)")]
public class Dlt645OverTcp : DltNetworkBase, IDriver
{
    public Dlt645OverTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    private HslCommunication.Instrument.DLT.DLT645OverTcp _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.Instrument.DLT.DLT645OverTcp)value;
    }

    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            _driver ??= new HslCommunication.Instrument.DLT.DLT645OverTcp(IpAddress, Port, Station)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout
            };
            _driver.LogNet = new LogNetSingle("");
            _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}