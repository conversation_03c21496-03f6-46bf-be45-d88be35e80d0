using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Dlt;
using DriversInterface;
using HslCommunication.LogNet;

namespace DLT698OverTcp;

[DriverSupported("Dlt698OverTcp")]
[DriverInfo("Dlt698OverTcp", "V1.1.0", "DLT(电力协议)")]
public class Dlt698OverTcp : DltNetworkBase, IDriver
{
    public Dlt698OverTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    private HslCommunication.Instrument.DLT.DLT698OverTcp _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.Instrument.DLT.DLT698OverTcp)value;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new HslCommunication.Instrument.DLT.DLT698OverTcp(IpAddress, Port, Station)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}