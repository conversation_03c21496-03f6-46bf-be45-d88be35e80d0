using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Dlt;
using DriversInterface;
using Feng.Common.Util;
using HslCommunication.LogNet;

namespace DLT645;

[DriverSupported("Dlt645")]
[DriverInfo("Dlt645", "V1.0.0", "DLT(电力协议)")]
public class Dlt645 : DltSerialBase, IDriver
{
    public override bool IsConnected { get; set; }

    public Dlt645(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    private HslCommunication.Instrument.DLT.DLT645 _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.Instrument.DLT.DLT645)value;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            /*
             * 相同串口号只实例化一个
             * 1.判断是否存在相同实例化协议,不存在就新建一个,存在就使用同一个实例
             */
            if (!Dlt645Extension.DicDlt645.ContainsKey(SerialNumber))
            {
                _driver = new HslCommunication.Instrument.DLT.DLT645(Station);
                _driver.SerialPortInni(EnumUtil.GetEnumDesc(SerialNumber), Convert.ToInt32(EnumUtil.GetEnumDesc(BaudRate)), DataBits, Stop, Checkout);
                _driver.ReceiveTimeOut = Timeout;
                OperateResult = _driver.Open();
                IsConnected = OperateResult.IsSuccess;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
                Dlt645Extension.DicDlt645.TryAdd(SerialNumber, Driver);
            }
            else
            {
                Driver = Dlt645Extension.DicDlt645[SerialNumber];
                OperateResult = _driver.Open();
            }
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}