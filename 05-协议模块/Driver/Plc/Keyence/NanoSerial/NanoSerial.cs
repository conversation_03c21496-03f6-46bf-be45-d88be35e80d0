using System;
using System.IO.Ports;
using System.Text;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Keyence;
using DriversInterface;
using Feng.Common.Util;
using HslCommunication;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Keyence;

namespace NanoSerial;

[DriverSupported("NanoSerial")]
[DriverInfo("NanoSerial", "V1.0.0", "基恩士(Keyence)")]
public class NanoSerial : KeyenceSerialBase, IDriver
{
    #region 配置参数

    [ConfigParameter("串口号", Remark = "")] public SerialNumberEnum SerialNumber { get; set; } = SerialNumberEnum.ttyS1;
    [ConfigParameter("波特率")] public BaudRateEnum BaudRate { get; set; } = BaudRateEnum.Bps9600;
    [ConfigParameter("数据位")] public int DataBits { get; set; } = 8;
    [ConfigParameter("停止位")] public StopBits Stop { get; set; } = StopBits.Two;
    [ConfigParameter("校验位")] public Parity Checkout { get; set; } = Parity.None;

    #endregion

    public NanoSerial(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public override bool IsConnected { get; set; }

    private KeyenceNanoSerial _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (KeyenceNanoSerial)value;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            /*
             * 相同串口号只实例化一个
             * 1.判断是否存在相同实例化协议,不存在就新建一个,存在就使用同一个实例
             */
            if (!KeyenceSerialExtension.DicRtu.TryGetValue(SerialNumber, out var value))
            {
                _driver = new KeyenceNanoSerial();
                _driver.SerialPortInni(EnumUtil.GetEnumDesc(SerialNumber), Convert.ToInt32(EnumUtil.GetEnumDesc(BaudRate)), DataBits, Stop, Checkout);
                _driver.ReceiveTimeOut = Timeout;
                _driver.Station = Station;
                OperateResult = _driver.Open();
                IsConnected = OperateResult.IsSuccess;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
                KeyenceSerialExtension.DicRtu.TryAdd(SerialNumber, _driver);
            }
            else
            {
                _driver = value;
                if (!_driver.IsOpen())
                {
                    OperateResult = _driver.Open();
                    IsConnected = OperateResult.IsSuccess;
                }
                else
                {
                    IsConnected = true;
                }
            }
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }

    /// <summary>
    ///     读取设备属性,循环单个读取
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public override async Task<DriverReturnValueModel> Read(DriverAddressIoArgModel val)
    {
        // 同步读取
        return await ReadSync(val);
    }

    /// <summary>
    ///     同步读取
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private Task<DriverReturnValueModel> ReadSync(DriverAddressIoArgModel val)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = val.DataType,
            Id = val.Id
        };
        switch (val.DataType)
        {
            case DataTypeEnum.Bool:
            {
                var boolRead = Driver.ReadBool(val.Address);
                if (!boolRead.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = boolRead.Message;
                    ret.ErrorCode = boolRead.ErrorCode;
                }

                ret.Value = boolRead.Content;
                break;
            }
            case DataTypeEnum.Bit:
            {
                var boolRead = Driver.ReadBool(val.Address);
                if (!boolRead.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = boolRead.Message;
                    ret.ErrorCode = boolRead.ErrorCode;
                }

                ret.Value = boolRead.Content ? 1 : 0;
                break;
            }
            case DataTypeEnum.Uint16:
                var uint16Read = Driver.ReadUInt16(val.Address);
                if (!uint16Read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = uint16Read.Message;
                    ret.ErrorCode = uint16Read.ErrorCode;
                }

                ret.Value = uint16Read.Content;
                break;
            case DataTypeEnum.Int16:
                var int16Read = Driver.ReadInt16(val.Address);
                if (!int16Read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = int16Read.Message;
                    ret.ErrorCode = int16Read.ErrorCode;
                }

                ret.Value = int16Read.Content;
                break;
            case DataTypeEnum.Uint32:
                var uint32Read = Driver.ReadUInt32(val.Address);
                if (!uint32Read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = uint32Read.Message;
                    ret.ErrorCode = uint32Read.ErrorCode;
                }

                ret.Value = uint32Read.Content;
                break;
            case DataTypeEnum.Int32:
                var int32Read = Driver.ReadInt32(val.Address);
                if (!int32Read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = int32Read.Message;
                    ret.ErrorCode = int32Read.ErrorCode;
                }

                ret.Value = int32Read.Content;
                break;
            case DataTypeEnum.Float:
                var floatRead = Driver.ReadFloat(val.Address);
                if (!floatRead.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = floatRead.Message;
                    ret.ErrorCode = floatRead.ErrorCode;
                }

                if (float.IsNaN(floatRead.Content))
                    floatRead.Content = 0;
                ret.Value = val.Length > 0
                    ? Math.Round(Convert.ToSingle(floatRead.Content), val.Length)
                    : floatRead.Content;
                break;
            case DataTypeEnum.Uint64:
                var uint64Read = Driver.ReadUInt64(val.Address);
                if (!uint64Read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = uint64Read.Message;
                    ret.ErrorCode = uint64Read.ErrorCode;
                }

                ret.Value = uint64Read.Content;
                break;
            case DataTypeEnum.Int64:
                var int64Read = Driver.ReadInt64(val.Address);
                if (!int64Read.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = int64Read.Message;
                    ret.ErrorCode = int64Read.ErrorCode;
                }

                ret.Value = int64Read.Content;
                break;
            case DataTypeEnum.Double:
                var doubleRead = Driver.ReadDouble(val.Address);
                if (!doubleRead.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = doubleRead.Message;
                    ret.ErrorCode = doubleRead.ErrorCode;
                }

                if (double.IsNaN(doubleRead.Content))
                    doubleRead.Content = 0;
                ret.Value = val.Length > 0
                    ? Math.Round(Convert.ToDouble(doubleRead.Content), val.Length)
                    : doubleRead.Content;
                break;
            case DataTypeEnum.String:
            {
                OperateResult<string> stringRead;
                //字符串默认编码
                val.Length = val.Length == 0 ? val.Length = 10 : val.Length;
                switch (val.Encoding)
                {
                    case StringEnum.Unicode:
                        stringRead = Driver.ReadString(val.Address, val.Length, Encoding.Unicode);
                        break;
                    case StringEnum.Ascii:
                        stringRead = Driver.ReadString(val.Address, val.Length, Encoding.ASCII);
                        break;
                    case StringEnum.UnicodeBig:
                        stringRead = Driver.ReadString(val.Address, val.Length,
                            Encoding.BigEndianUnicode);
                        break;
                    case StringEnum.Utf32:
                        stringRead = Driver.ReadString(val.Address, val.Length, Encoding.UTF32);
                        break;
                    case StringEnum.Gb2312:
                        stringRead = Driver.ReadString(val.Address, val.Length,
                            Encoding.GetEncoding("gb2312"));
                        break;
                    case StringEnum.Utf8:
                    default:
                        stringRead = Driver.ReadString(val.Address, val.Length, Encoding.UTF8);
                        break;
                }

                if (!stringRead.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = stringRead.Message;
                    ret.ErrorCode = stringRead.ErrorCode;
                }

                ret.Value = stringRead.Content;
                break;
            }
            case DataTypeEnum.Bcd:
            {
                OperateResult<byte[]> bcdRead = Driver.Read(val.Address, 1);
                if (!bcdRead.IsSuccess)
                {
                    ret.VariableStatus = VariableStatusTypeEnum.Error;
                    ret.Message = bcdRead.Message;
                    ret.ErrorCode = bcdRead.ErrorCode;
                }

                try
                {
                    ret.Value = Convert.ToInt32(bcdRead.Content.ToHexString());
                }
                catch
                {
                    ret.Value = bcdRead.Content.ToHexString();
                }

                break;
            }
        }

        return Task.FromResult(ret);
    }

    /// <summary>
    ///     断开连接
    /// </summary>
    /// <returns></returns>
    public override void Close()
    {
        Driver?.Close();
        if (KeyenceSerialExtension.DicRtu.ContainsKey(SerialNumber))
            KeyenceSerialExtension.DicRtu.TryRemove(SerialNumber, out _);
    }

    /// <summary>
    ///     释放对象
    /// </summary>
    public override void Dispose()
    {
        Driver?.Dispose();
    }
}