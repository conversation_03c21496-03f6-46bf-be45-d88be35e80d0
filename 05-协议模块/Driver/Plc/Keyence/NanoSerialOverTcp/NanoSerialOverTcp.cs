using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Keyence;
using DriversInterface;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Keyence;

namespace NanoSerialOverTcp;

[DriverSupported("NanoSerialOverTcp")]
[DriverInfo("NanoSerialOverTcp", "V1.0.0", "基恩士(Keyence)")]
public class NanoSerialOverTcp : KeyenceSerialBase, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 8501;

    #endregion

    public NanoSerialOverTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    private KeyenceNanoSerialOverTcp _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (KeyenceNanoSerialOverTcp)value;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new KeyenceNanoSerialOverTcp(IpAddress, Port)
                {
                    Station = Station,
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}