using System;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Keyence;
using DriversInterface;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Keyence;

namespace McNet;

[DriverSupported("McNet")]
[DriverInfo("McNet", "V1.0.0", "基恩士(Keyence)")]
public class McNet : KeyenceNetWorkBase, IDriver
{
    public McNet(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public override bool IsConnected => Driver != null && (OperateResult?.IsSuccess ?? false);

    private KeyenceMcNet _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (KeyenceMcNet)value;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new KeyenceMcNet(IpAddress, Port)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                if (StringReverse == BoolEnum.True)
                    _driver.ByteTransform.IsStringReverseByteWord = true;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (_, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}