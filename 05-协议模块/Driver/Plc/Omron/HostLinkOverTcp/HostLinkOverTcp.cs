using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Omron;
using DriversInterface;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Omron;

namespace HostLinkOverTcp;

[DriverSupported("HostLinkOverTcp")]
[DriverInfo("HostLinkOverTcp", "V1.2.0", "欧姆龙PLC(Omron)")]
public class HostLinkOverTcp : OmronNetworkDeviceBase, IDriver
{
    /// <summary>
    /// </summary>
    /// <param name="driverInfo"></param>
    public HostLinkOverTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    private OmronHostLinkOverTcp _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (OmronHostLinkOverTcp)value;
    }

    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new OmronHostLinkOverTcp(IpAddress, Port)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                _driver.ByteTransform.DataFormat = DataFormat;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}