using System;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Omron;
using DriversInterface;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Omron;

namespace FinsUdp;

[DriverSupported("FinsUdp")]
[DriverInfo("FinsUdp", "V1.2.2", "欧姆龙PLC(Omron)")]
public class FinsUdp : OmronNetworkUdpDeviceBase, IDriver
{
    public FinsUdp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     OmronFinsUdp
    /// </summary>
    private OmronFinsUdp _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (OmronFinsUdp)value;
    }

    public override bool IsConnected { get; set; }
    [ConfigParameter("解析类型")] public DataFormat DataFormat { get; set; } = DataFormat.ABCD;

    [ConfigParameter("字符串颠倒")] public BoolEnum StringReverse { get; set; } = BoolEnum.False;

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new OmronFinsUdp(IpAddress, Port);
                _driver.SA1 = Sa1;
                _driver.ReceiveTimeOut = Timeout;
                _driver.ByteTransform.DataFormat = DataFormat;
                _driver.ByteTransform.IsStringReverseByteWord = StringReverse == BoolEnum.True;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            IsConnected = true;
            OperateResult = new OperateResult
            {
                IsSuccess = true,
                Message = "UDP协议尝试读取中..."
            };
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}