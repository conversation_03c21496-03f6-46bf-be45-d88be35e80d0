using System;
using System.Threading.Tasks;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using HslCommunication.Core;
using HslCommunication.LogNet;

namespace CipNet;

[DriverSupported("OmronCipNet")]
[DriverInfo("OmronCipNet", "V1.3.0", "欧姆龙PLC(Omron)")]
public class OmronCipNet : BasePlcProtocolCollector, IDriver
{
    public OmronCipNet(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     HslCommunication.Profinet.Omron.OmronCipNet
    /// </summary>
    private HslCommunication.Profinet.Omron.OmronCipNet _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.Profinet.Omron.OmronCipNet)value;
    }

    public override bool IsConnected => _driver != null && OperateResult.IsSuccess;

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new HslCommunication.Profinet.Omron.OmronCipNet(IpAddress, Port)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }

    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 44818;

    #endregion

    #region CIP协议地址方法

    /// <summary>
    ///     A1:全局变量
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("A1", description: "类型：完整和PLC实际数据对应上", name: "A1:全局变量")]
    public async Task<DriverReturnValueModel> A1(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     type=0xDA:A2:指令类型
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("type=0xDA:A2", description: "当前变量类型存在，可以手动指定类型才能正确导入", name: "type=0xDA:A2:指令类型")]
    public async Task<DriverReturnValueModel> TypeA2(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     x=0x52:A3[0]:使用片段数
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("x=0x52:A3[0]", description: "当地址数据非标准大小的时候，可以使用片段数，地址前加x=0x52;", name: "x=0x52:A3[0]:使用片段数")]
    public async Task<DriverReturnValueModel> XA3(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     class=0x6b:0x68f:符号实例地址
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("class=0x6b:0x68f", description: "也可以写成class=107;63119，class是实例ID，右侧是实例ID", name: "class=0x6b:0x68f:符号实例地址")]
    public async Task<DriverReturnValueModel> ClassInstance(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Program:MainProgram...:局部变量名
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Program:MainProgram", description: "对局部变量是有效的，局部变量上级名称", name: "Program:MainProgram:局部变量名")]
    public async Task<DriverReturnValueModel> ProgramMainProgram(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     slot=2:A1:全局变量名
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("slot=2:A1", description: "地址也可以指定特殊的slot信息", name: "slot=2:A1:全局变量名")]
    public async Task<DriverReturnValueModel> SlotA1(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     I=A[0]:全局变量名
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("I=A[0]", description: "如果是PLC是数组uint类型bool数组，可以使用这种访问每个位", name: "I=A[0]:全局变量名")]
    public async Task<DriverReturnValueModel> IA(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     B[0]:全局变量名
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("B[0]", description: "如果是数据类型，则可以使用这种访问每个元素", name: "B[0]:全局变量名")]
    public async Task<DriverReturnValueModel> B(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     C[0,1]:全局变量名
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C[0,1]", description: "如果是二维数据，则可以使用这种访问每个元素", name: "C[0,1]:全局变量名")]
    public async Task<DriverReturnValueModel> C(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    #endregion
}