using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Omron;
using DriversInterface;
using Feng.Common.Util;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Omron;

namespace HostLink;

[DriverSupported("HostLink")]
[DriverInfo("HostLink", "V1.1.0", "欧姆龙PLC(Omron)")]
public class HostLink : OmronSerialDeviceBase, IDriver
{
    public override bool IsConnected { get; set; }

    public HostLink(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    private OmronHostLink _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (OmronHostLink)value;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new OmronHostLink();
                _driver.SerialPortInni(EnumUtil.GetEnumDesc(SerialNumber), Convert.ToInt32(EnumUtil.GetEnumDesc(BaudRate)), DataBits, Stop, Checkout);
                OperateResult = _driver.Open();
                _driver.ByteTransform.DataFormat = DataFormat;
                IsConnected = OperateResult.IsSuccess;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }
            else
            {
                OperateResult = _driver.Open();
            }
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}