using System;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Omron;
using DriversInterface;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Omron;

namespace FinsNet;

[DriverSupported("FinsNet")]
[DriverInfo("FinsNet", "V1.4.1", "欧姆龙PLC(Omron)")]
public class FinsNet : OmronNetworkDeviceBase, IDriver
{
    /// <summary>
    ///     本地存储记录sa标识
    /// </summary>
    private byte _sa1;

    /// <summary>
    /// </summary>
    /// <param name="driverInfo"></param>
    public FinsNet(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     OmronFinsNet
    /// </summary>
    private OmronFinsNet _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (OmronFinsNet)value;
    }

    [ConfigParameter("Is String Reverse", order: 999)]
    public BoolEnum IsStringReverseByteWord { get; set; } = BoolEnum.True;

    public override bool IsConnected => _driver != null && OperateResult.IsSuccess;

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new OmronFinsNet(IpAddress, Port)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout,
                    DA2 = Da2
                };
                _driver.ByteTransform.DataFormat = DataFormat;
                _driver.ByteTransform.IsStringReverseByteWord = IsStringReverseByteWord == BoolEnum.True;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
            _sa1 = _driver.SA1;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }

    /// <summary>
    ///     读取设备属性,循环单个读取
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public override async Task<DriverReturnValueModel> Read(DriverAddressIoArgModel val)
    {
        // 该协议节点地址是否发生改变，发生改变后就断开连接
        if (_driver.SA1 != _sa1)
            await _driver.ConnectCloseAsync();
        return await base.Read(val);
    }
}