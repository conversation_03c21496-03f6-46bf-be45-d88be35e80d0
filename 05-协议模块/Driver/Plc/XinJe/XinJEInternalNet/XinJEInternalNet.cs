using System;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core;
using Driver.Core.Attributes;
using Driver.Core.Models;
using DriversInterface;
using HslCommunication.LogNet;

namespace XinJEInternalNet;

/*信捷(XinJE)
 *XinJEInternalNet（专用）
 * v1.2.0
 * 修复没有读取方法，重构不继承通用信捷base方法
 */
[DriverSupported("XinJEInternalNet")]
[DriverInfo("XinJEInternalNet", "V1.2.0", "信捷(XinJE)")]
public class XinJEInternalNet : BasePlcProtocolCollector, IDriver
{
    [ConfigParameter("批量读取", GroupName = "高级配置", Display = false)]
    public override BoolEnum BulkRead { get; set; } = BoolEnum.False;

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 102;

    [ConfigParameter("站号")] public byte Station { get; set; } = 1;

    /// <summary>
    /// </summary>
    /// <param name="driverInfo"></param>
    public XinJEInternalNet(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     PanasonicMewtocolOverTcp
    /// </summary>
    private HslCommunication.Profinet.XINJE.XinJEInternalNet _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.Profinet.XINJE.XinJEInternalNet)value;
    }

    // HslCommunication v12.0 - 使用安全的空检查
    public override bool IsConnected => _driver != null && (OperateResult?.IsSuccess ?? false);

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new HslCommunication.Profinet.XINJE.XinJEInternalNet(IpAddress, Port, Station)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }

    #region 方法

    /// <summary>
    ///     M:内部继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("M", description: "范围：M0-M69999", name: "M:内部继电器")]
    public async Task<DriverReturnValueModel> M(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     X:输入线圈
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("X", description: "8进制地址，也可以带小数点表示", name: "X:输入线圈")]
    public async Task<DriverReturnValueModel> X(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Y:输出线圈
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Y", description: "8进制地址，也可以带小数点表示", name: "Y:输出线圈")]
    public async Task<DriverReturnValueModel> Y(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     S:流程继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("S", description: "范围：S0-S7999", name: "S:流程继电器")]
    public async Task<DriverReturnValueModel> S(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     HS 流程继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Hs", description: "范围：HS0-HS999", name: "HS:流程继电器")]
    public async Task<DriverReturnValueModel> Hs(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SM:特殊继电器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Sm", description: "范围：SM0-SM4999", name: "SM:特殊继电器")]
    public async Task<DriverReturnValueModel> Sm(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     T:定时器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("T", description: "范围：T0-T4999", name: "T:定时器")]
    public async Task<DriverReturnValueModel> T(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     C计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("C", description: "范围：C0-C4999", name: "C:计数器")]
    public async Task<DriverReturnValueModel> C(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     ET精确定时器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Et", description: "范围：ET0-ET39", name: "ET:精确定时器")]
    public async Task<DriverReturnValueModel> Et(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SEM计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Sem", description: "范围：SEM0-SEM31", name: "SEM:顺序功能块专用指令")]
    public async Task<DriverReturnValueModel> Sem(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     HM:计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Hm", description: "范围：HM0-HM11999", name: "HM:内部继电器")]
    public async Task<DriverReturnValueModel> Hm(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     HT 定时器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Ht", description: "范围：HT0-HT1999", name: "HT:定时器")]
    public async Task<DriverReturnValueModel> Ht(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     Hc 计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Hc", description: "范围：HT0-HC1999", name: "Hc:计数器")]
    public async Task<DriverReturnValueModel> Hc(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     HSC 高速计数器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Hsc", description: "范围：HST0-HSC39", name: "HSC:高速计数器")]
    public async Task<DriverReturnValueModel> Hsc(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     D 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("D", description: "一般范围：D0-D69999", name: "D:数据寄存器")]
    public async Task<DriverReturnValueModel> D(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     ID 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Id", description: "ID0-ID99（本体），ID10X00（X为0-9表示10个模块）", name: "ID:本体/扩展模块/扩展BD/扩展ED")]
    public async Task<DriverReturnValueModel> Id(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     QD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Qd", description: "QD0-QD99（本体），QD10X00（X为0-9表示10个模块）", name: "QD:本体/扩展模块/扩展BD/扩展ED")]
    public async Task<DriverReturnValueModel> Qd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SD 特殊寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Sd", description: "一般范围：SD0-SD4999", name: "SD:特殊寄存器")]
    public async Task<DriverReturnValueModel> Sd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     TD 定时器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Td", description: "一般范围：TD0-TD4999", name: "TD:定时器当前值")]
    public async Task<DriverReturnValueModel> Td(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     CD 计数器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Cd", description: "一般范围：CD0-CD4999", name: "CD:计数器当前值")]
    public async Task<DriverReturnValueModel> Cd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     ETD 精确定时器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Etd", description: "一般范围：ETD0-ETD39", name: "ETD:精确定时器当前值")]
    public async Task<DriverReturnValueModel> Etd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     HD 数据寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Hd", description: "一般范围：HD0-HD24999", name: "HD:数据寄存器")]
    public async Task<DriverReturnValueModel> Hd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     HSD 特殊用寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Hsd", description: "一般范围：HSD0-HSD1023", name: "HSD:特殊用寄存器")]
    public async Task<DriverReturnValueModel> Hsd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     HTD 定时器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Htd", description: "一般范围：HTD0-HTD1999", name: "HTD:定时器当前值")]
    public async Task<DriverReturnValueModel> Htd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     HD 计数器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Hcd", description: "一般范围：HCD0-HCD1999", name: "HCD:计数器当前值")]
    public async Task<DriverReturnValueModel> Hcd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     HSCD 高速计数器当前值
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Hscd", description: "一般范围：HSCD0-HSCD39", name: "HSCD:高速计数器当前值")]
    public async Task<DriverReturnValueModel> Hscd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     FD FlashROM寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Fd", description: "一般范围：FD0-FD8191", name: "FD:FlashROM寄存器")]
    public async Task<DriverReturnValueModel> Fd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     SFD 特殊用FlashROM寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Sfd", description: "一般范围：SFD0-SFD5999", name: "SFD:特殊用FlashROM寄存器")]
    public async Task<DriverReturnValueModel> Sfd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     FSD 特殊保密寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("Fsd", description: "一般范围：FSD0-FSD47", name: "FSD:特殊保密寄存器")]
    public async Task<DriverReturnValueModel> Fsd(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    #endregion
}