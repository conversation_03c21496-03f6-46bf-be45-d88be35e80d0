using System;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.XinJE;
using DriversInterface;
using HslCommunication.Core;
using HslCommunication.LogNet;

namespace XinJETcpNet;

[DriverSupported("XinJETcpNet")]
[DriverInfo("XinJETcpNet", "V1.1.0", "信捷(XinJE)")]
public class XinJETcpNet : XinJeNetworkDeviceBase, IDriver
{
    /// <summary>
    /// </summary>
    /// <param name="driverInfo"></param>
    public XinJETcpNet(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    // private HslCommunication.Profinet.XINJE.XinJETcpNet Driver;
    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver == null)
            {
                Driver = new HslCommunication.Profinet.XINJE.XinJETcpNet(IpAddress, Port, Station)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout,
                    DataFormat = DataFormat
                };
                Driver.Series = Series;
                if (ByteTransform == BoolEnum.True)
                    Driver.ByteTransform = new RegularByteTransform();
                if (StringReverse == BoolEnum.True)
                    Driver.IsStringReverse = true;
                Driver.LogNet = new LogNetSingle("");
                var tcpNet = (HslCommunication.Profinet.XINJE.XinJETcpNet) Driver;
                tcpNet.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = Driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }
}