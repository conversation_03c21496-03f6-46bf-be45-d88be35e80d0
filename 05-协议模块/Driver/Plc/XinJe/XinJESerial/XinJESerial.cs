using System;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.XinJE;
using DriversInterface;
using Feng.Common.Util;
using HslCommunication.Core;
using HslCommunication.LogNet;

namespace XinJESerial;

[DriverSupported("XinJESerial")]
[DriverInfo("XinJESerial", "V1.0.0", "信捷(XinJE)")]
public class XinJESerial : XinJeSerialDeviceBase, IDriver
{
    public override bool IsConnected { get; set; }

    /// <summary>
    /// </summary>
    /// <param name="driverInfo"></param>
    public XinJESerial(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    // private HslCommunication.Profinet.XINJE.XinJESerial Driver;
    
    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver == null)
            {
                Driver = new HslCommunication.Profinet.XINJE.XinJESerial(Station);
                Driver.SerialPortInni(EnumUtil.GetEnumDesc(SerialNumber), Convert.ToInt32(EnumUtil.GetEnumDesc(BaudRate)), DataBits, Stop, Checkout);
                Driver.DataFormat = DataFormat;
                if (ByteTransform == BoolEnum.True)
                    Driver.ByteTransform = new RegularByteTransform();
                if (StringReverse == BoolEnum.True)
                    Driver.IsStringReverse = true;
                Driver.Series = Series;
                OperateResult = Driver.Open();
                IsConnected = OperateResult.IsSuccess;
                Driver.LogNet = new LogNetSingle("");
                var xinJeSerial = (HslCommunication.Profinet.XINJE.XinJESerial) Driver;
                xinJeSerial.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }
            else
            {
                OperateResult = Driver.Open();
            }
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }
}