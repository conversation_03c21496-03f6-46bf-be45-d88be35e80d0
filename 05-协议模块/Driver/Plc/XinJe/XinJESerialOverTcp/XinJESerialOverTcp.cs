using System;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.XinJE;
using DriversInterface;
using HslCommunication.Core;
using HslCommunication.LogNet;

namespace XinJESerialOverTcp;

[DriverSupported("XinJESerialOverTcp ")]
[DriverInfo("XinJESerialOverTcp", "V1.1.0", "信捷(XinJE)")]
public class XinJESerialOverTcp : XinJeNetworkDeviceBase, IDriver
{
    /// <summary>
    /// </summary>
    /// <param name="driverInfo"></param>
    public XinJESerialOverTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    // private HslCommunication.Profinet.XINJE.XinJESerialOverTcp Driver;
    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;
    
    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver == null)
            {
                Driver = new HslCommunication.Profinet.XINJE.XinJESerialOverTcp(IpAddress, Port, Station)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout,
                    DataFormat = DataFormat
                };
                Driver.Series = Series;
                if (ByteTransform == BoolEnum.True)
                    Driver.ByteTransform = new RegularByteTransform();
                if (StringReverse == BoolEnum.True)
                    Driver.IsStringReverse = true;
                Driver.LogNet = new LogNetSingle("");
                var tcpNet = (HslCommunication.Profinet.XINJE.XinJESerialOverTcp) Driver;
                tcpNet.LogNet.BeforeSaveToFile += async (object sender, HslEventArgs e) => { await SocketSend(e); };
            }

            OperateResult = Driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }
}