<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <OutputPath>..\..\..\..\..\01-应用服务\IotGateway/bin/$(Configuration)/$(TargetFramework)/drivers/other/</OutputPath>
        <RootNamespace>DeltaSerialAsciiOverTcp</RootNamespace>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\..\DriversInterface\DriversInterface.csproj"/>
    </ItemGroup>

</Project>
