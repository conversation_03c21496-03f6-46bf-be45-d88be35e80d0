using System;
using System.Text;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Delta;
using DriversInterface;
using HslCommunication;
using HslCommunication.LogNet;

namespace DeltaSerialAsciiOverTcp;

/* 更新日志
 *  版本:v1.1.0
 *  - 🌟 调整 `增加同步读取方法，支持同步/异步读取` ⏱️`2024-05-13`
 */
[DriverSupported("DeltaSerialAsciiOverTcp")]
[DriverInfo("DeltaSerialAsciiOverTcp", "V1.1.0", "台达(Delta)")]
public class DeltaSerialAsciiOverTcp : DeltaBase, IDriver
{
    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 502;

    [ConfigParameter("异步读取", GroupName = "高级配置", Remark = "同串口接入多个设备，建议关闭异步读取")]
    public BoolEnum IsAsync { get; set; } = BoolEnum.True;

    #endregion

    public DeltaSerialAsciiOverTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     DeltaSerialAscii
    /// </summary>
    private HslCommunication.Profinet.Delta.DeltaSerialAsciiOverTcp _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.Profinet.Delta.DeltaSerialAsciiOverTcp)value;
    }

    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            _driver ??= new HslCommunication.Profinet.Delta.DeltaSerialAsciiOverTcp(IpAddress, Port, Station)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout,
                Series = Series
            };
            _driver.LogNet = new LogNetSingle("");
            _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }

    /// <summary>
    ///     读取设备属性,循环单个读取
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public override async Task<DriverReturnValueModel> Read(DriverAddressIoArgModel val)
    {
        // 异步读取
        if (IsAsync == BoolEnum.True)
            return await base.Read(val);
        // 同步读取
        return await ReadSync(val);
    }

    /// <summary>
    ///     同步读取设备属性（早期命名问题，后续统一调整）
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public Task<DriverReturnValueModel> ReadSync(DriverAddressIoArgModel val)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = val.DataType,
            Id = val.Id
        };
        try
        {
            switch (val.DataType)
            {
                case DataTypeEnum.Bool:
                {
                    var boolRead = Driver.ReadBool(val.Address);
                    if (!boolRead.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = boolRead.Message;
                        ret.ErrorCode = boolRead.ErrorCode;
                    }

                    ret.Value = boolRead.Content;
                    break;
                }
                case DataTypeEnum.Bit:
                {
                    var boolRead = Driver.ReadBool(val.Address);
                    if (!boolRead.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = boolRead.Message;
                        ret.ErrorCode = boolRead.ErrorCode;
                    }

                    ret.Value = boolRead.Content ? 1 : 0;
                    break;
                }
                case DataTypeEnum.Uint16:
                    var uint16Read = Driver.ReadUInt16(val.Address);
                    if (!uint16Read.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = uint16Read.Message;
                        ret.ErrorCode = uint16Read.ErrorCode;
                    }

                    ret.Value = uint16Read.Content;
                    break;
                case DataTypeEnum.Int16:
                    var int16Read = Driver.ReadInt16(val.Address);
                    if (!int16Read.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = int16Read.Message;
                        ret.ErrorCode = int16Read.ErrorCode;
                    }

                    ret.Value = int16Read.Content;
                    break;
                case DataTypeEnum.Uint32:
                    var uint32Read = Driver.ReadUInt32(val.Address);
                    if (!uint32Read.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = uint32Read.Message;
                        ret.ErrorCode = uint32Read.ErrorCode;
                    }

                    ret.Value = uint32Read.Content;
                    break;
                case DataTypeEnum.Int32:
                    var int32Read = Driver.ReadInt32(val.Address);
                    if (!int32Read.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = int32Read.Message;
                        ret.ErrorCode = int32Read.ErrorCode;
                    }

                    ret.Value = int32Read.Content;
                    break;
                case DataTypeEnum.Float:
                    var floatRead = Driver.ReadFloat(val.Address);
                    if (!floatRead.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = floatRead.Message;
                        ret.ErrorCode = floatRead.ErrorCode;
                    }

                    if (float.IsNaN(floatRead.Content))
                        floatRead.Content = 0;
                    ret.Value = floatRead.Content;
                    break;
                case DataTypeEnum.Uint64:
                    var uint64Read = Driver.ReadUInt64(val.Address);
                    if (!uint64Read.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = uint64Read.Message;
                        ret.ErrorCode = uint64Read.ErrorCode;
                    }

                    ret.Value = uint64Read.Content;
                    break;
                case DataTypeEnum.Int64:
                    var int64Read = Driver.ReadInt64(val.Address);
                    if (!int64Read.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = int64Read.Message;
                        ret.ErrorCode = int64Read.ErrorCode;
                    }

                    ret.Value = int64Read.Content;
                    break;
                case DataTypeEnum.Double:
                    var doubleRead = Driver.ReadDouble(val.Address);
                    if (!doubleRead.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = doubleRead.Message;
                        ret.ErrorCode = doubleRead.ErrorCode;
                    }

                    if (double.IsNaN(doubleRead.Content))
                        doubleRead.Content = 0;
                    ret.Value = doubleRead.Content;
                    break;
                case DataTypeEnum.String:
                {
                    OperateResult<string> stringRead;
                    //字符串默认编码
                    val.Length = val.Length == 0 ? val.Length = 10 : val.Length;
                    switch (val.Encoding)
                    {
                        case StringEnum.Unicode:
                            stringRead = Driver.ReadString(val.Address, val.Length, Encoding.Unicode);
                            break;
                        case StringEnum.Ascii:
                            stringRead = Driver.ReadString(val.Address, val.Length, Encoding.ASCII);
                            break;
                        case StringEnum.UnicodeBig:
                            stringRead = Driver.ReadString(val.Address, val.Length,
                                Encoding.BigEndianUnicode);
                            break;
                        case StringEnum.Utf32:
                            stringRead = Driver.ReadString(val.Address, val.Length, Encoding.UTF32);
                            break;
                        case StringEnum.Gb2312:
                            stringRead = Driver.ReadString(val.Address, val.Length,
                                Encoding.GetEncoding("gb2312"));
                            break;
                        case StringEnum.Utf8:
                        default:
                            stringRead = Driver.ReadString(val.Address, val.Length, Encoding.UTF8);
                            break;
                    }

                    if (!stringRead.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = stringRead.Message;
                        ret.ErrorCode = stringRead.ErrorCode;
                    }

                    ret.Value = stringRead.Content;
                    break;
                }
                case DataTypeEnum.Bcd:
                {
                    OperateResult<byte[]> bcdRead = Driver.Read(val.Address, 1);
                    if (!bcdRead.IsSuccess)
                    {
                        ret.VariableStatus = VariableStatusTypeEnum.Error;
                        ret.Message = bcdRead.Message;
                        ret.ErrorCode = bcdRead.ErrorCode;
                    }

                    try
                    {
                        ret.Value = Convert.ToInt32(bcdRead.Content.ToHexString());
                    }
                    catch
                    {
                        ret.Value = bcdRead.Content.ToHexString();
                    }

                    break;
                }
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = "同步:" + ex.Message;
        }

        return Task.FromResult(ret);
    }
}