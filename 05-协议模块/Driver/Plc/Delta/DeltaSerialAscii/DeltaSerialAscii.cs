using System;
using System.IO.Ports;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Delta;
using DriversInterface;
using Feng.Common.Util;
using HslCommunication.LogNet;

namespace DeltaSerialAscii;

[DriverSupported("DeltaSerialAscii")]
[DriverInfo("DeltaSerialAscii", "V1.1.0", "台达(Delta)")]
public class DeltaSerialAscii : DeltaBase, IDriver
{
    public override bool IsConnected => Driver?.IsOpen() ?? false;

    public DeltaSerialAscii(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     DeltaSerialAscii
    /// </summary>
    private HslCommunication.Profinet.Delta.DeltaSerialAscii _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.Profinet.Delta.DeltaSerialAscii)value;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new HslCommunication.Profinet.Delta.DeltaSerialAscii(Station);
                _driver.SerialPortInni(EnumUtil.GetEnumDesc(SerialNumber), Convert.ToInt32(EnumUtil.GetEnumDesc(BaudRate)), DataBits, Stop, Checkout);
                _driver.Series = Series;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.Open();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }

    public override void Close()
    {
        Driver?.Close();
    }

    public override void Dispose()
    {
        Driver?.Dispose();
    }

    #region 配置参数

    [ConfigParameter("串口号")] public SerialNumberEnum SerialNumber { get; set; } = SerialNumberEnum.ttyS1;
    [ConfigParameter("波特率")] public BaudRateEnum BaudRate { get; set; } = BaudRateEnum.Bps9600;
    [ConfigParameter("数据位")] public int DataBits { get; set; } = 8;

    [ConfigParameter("停止位")] public StopBits Stop { get; set; } = StopBits.Two;
    [ConfigParameter("校验位")] public Parity Checkout { get; set; } = Parity.None;

    #endregion
}