using System;
using System.Text;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Delta;
using DriversInterface;
using HslCommunication;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Delta;

namespace DeltaTcp;

[DriverSupported("DeltaTcp")]
[DriverInfo("DeltaTcp", "V1.2.0", "台达(Delta)")]
public class DeltaTcp : DeltaBase, IDriver
{
    public DeltaTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }


    /// <summary>
    ///     DeltaTcpNet
    /// </summary>
    private DeltaTcpNet _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (DeltaTcpNet)value;
    }

    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            _driver ??= new DeltaTcpNet(IpAddress, Port, Station)
            {
                ReceiveTimeOut = Timeout,
                ConnectTimeOut = Timeout,
                Series = Series
            };
            _driver.LogNet = new LogNetSingle("");
            _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }

    #region Methods

    /// <summary>
    ///     X0-X177:输入寄存器
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    [Method("X", description: "只读操作,地址8进制", name: "X0-X177:输入寄存器")]
    public async Task<DriverReturnValueModel> ReadCoil(DriverAddressIoArgModel val)
    {
        return await Read(val);
    }

    /// <summary>
    ///     读取设备属性,循环单个读取
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public async Task<DriverReturnValueModel> ReadAs(DriverAddressIoArgModel val)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = val.DataType,
            Id = val.Id
        };
        try
        {
            if (IsConnected || DriverInfo.ConnectType is ConnectTypeEnum.SerialPort or ConnectTypeEnum.Udp)
            {
                switch (val.DataType)
                {
                    case DataTypeEnum.Bool:
                    {
                        var boolRead = Driver.ReadBool(val.Address);
                        if (!boolRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = boolRead.Message;
                            ret.ErrorCode = boolRead.ErrorCode;
                        }

                        ret.Value = boolRead.Content;
                        break;
                    }
                    case DataTypeEnum.Bit:
                    {
                        var boolRead = Driver.ReadBool(val.Address);
                        if (!boolRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = boolRead.Message;
                            ret.ErrorCode = boolRead.ErrorCode;
                        }

                        ret.Value = boolRead.Content ? 1 : 0;
                        break;
                    }
                    case DataTypeEnum.Uint16:
                        var uint16Read = Driver.ReadUInt16(val.Address);
                        if (!uint16Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = uint16Read.Message;
                            ret.ErrorCode = uint16Read.ErrorCode;
                        }

                        ret.Value = uint16Read.Content;
                        break;
                    case DataTypeEnum.Int16:
                        var int16Read = Driver.ReadInt16(val.Address);
                        if (!int16Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = int16Read.Message;
                            ret.ErrorCode = int16Read.ErrorCode;
                        }

                        ret.Value = int16Read.Content;
                        break;
                    case DataTypeEnum.Uint32:
                        var uint32Read = Driver.ReadUInt32(val.Address);
                        if (!uint32Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = uint32Read.Message;
                            ret.ErrorCode = uint32Read.ErrorCode;
                        }

                        ret.Value = uint32Read.Content;
                        break;
                    case DataTypeEnum.Int32:
                        var int32Read = Driver.ReadInt32(val.Address);
                        if (!int32Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = int32Read.Message;
                            ret.ErrorCode = int32Read.ErrorCode;
                        }

                        ret.Value = int32Read.Content;
                        break;
                    case DataTypeEnum.Float:
                        var floatRead = Driver.ReadFloat(val.Address);
                        if (!floatRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = floatRead.Message;
                            ret.ErrorCode = floatRead.ErrorCode;
                        }

                        if (float.IsNaN(floatRead.Content))
                            floatRead.Content = 0;
                        ret.Value = floatRead.Content;
                        break;
                    case DataTypeEnum.Uint64:
                        var uint64Read = Driver.ReadUInt64(val.Address);
                        if (!uint64Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = uint64Read.Message;
                            ret.ErrorCode = uint64Read.ErrorCode;
                        }

                        ret.Value = uint64Read.Content;
                        break;
                    case DataTypeEnum.Int64:
                        var int64Read = Driver.ReadInt64(val.Address);
                        if (!int64Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = int64Read.Message;
                            ret.ErrorCode = int64Read.ErrorCode;
                        }

                        ret.Value = int64Read.Content;
                        break;
                    case DataTypeEnum.Double:
                        var doubleRead = Driver.ReadDouble(val.Address);
                        if (!doubleRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = doubleRead.Message;
                            ret.ErrorCode = doubleRead.ErrorCode;
                        }

                        if (double.IsNaN(doubleRead.Content))
                            doubleRead.Content = 0;
                        ret.Value = doubleRead.Content;
                        break;
                    case DataTypeEnum.String:
                    {
                        OperateResult<string> stringRead;
                        //字符串默认编码
                        val.Length = val.Length == 0 ? val.Length = 10 : val.Length;
                        switch (val.Encoding)
                        {
                            case StringEnum.Unicode:
                                stringRead = Driver.ReadString(val.Address, val.Length, Encoding.Unicode);
                                break;
                            case StringEnum.Ascii:
                                stringRead = Driver.ReadString(val.Address, val.Length, Encoding.ASCII);
                                break;
                            case StringEnum.UnicodeBig:
                                stringRead = Driver.ReadString(val.Address, val.Length,
                                    Encoding.BigEndianUnicode);
                                break;
                            case StringEnum.Utf32:
                                stringRead = Driver.ReadString(val.Address, val.Length, Encoding.UTF32);
                                break;
                            case StringEnum.Gb2312:
                                stringRead = Driver.ReadString(val.Address, val.Length,
                                    Encoding.GetEncoding("gb2312"));
                                break;
                            case StringEnum.Utf8:
                            default:
                                stringRead = Driver.ReadString(val.Address, val.Length, Encoding.UTF8);
                                break;
                        }

                        if (!stringRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = stringRead.Message;
                            ret.ErrorCode = stringRead.ErrorCode;
                        }

                        ret.Value = stringRead.Content;
                        break;
                    }

                    case DataTypeEnum.Bcd:
                    {
                        OperateResult<byte[]> bcdRead = Driver.Read(val.Address, 1);
                        if (!bcdRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = bcdRead.Message;
                            ret.ErrorCode = bcdRead.ErrorCode;
                        }

                        try
                        {
                            ret.Value = Convert.ToInt32(bcdRead.Content.ToHexString());
                        }
                        catch
                        {
                            ret.Value = bcdRead.Content.ToHexString();
                        }

                        break;
                    }
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "设备未连接";
                ret.ErrorCode = -1;
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return ret;
    }


    /// <summary>
    ///     读取设备属性,循环单个读取
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public override async Task<DriverReturnValueModel> Read(DriverAddressIoArgModel val)
    {
        if (ReadAsync == BoolEnum.True) return await base.Read(val);
        return await ReadAs(val);
    }

    #endregion Methods

    #region 配置参数

    [ConfigParameter("IP地址")] public string IpAddress { get; set; } = "127.0.0.1";
    [ConfigParameter("端口号")] public int Port { get; set; } = 502;

    #endregion
}