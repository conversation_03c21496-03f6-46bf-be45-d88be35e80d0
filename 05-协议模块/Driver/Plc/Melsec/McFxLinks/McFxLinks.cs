using System;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Melsec;
using DriversInterface;
using Feng.Common.Util;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Melsec;

namespace McFxLinks;

[DriverSupported("McFxLinks")]
[DriverInfo("McFxLinks", "V1.1.0", "三菱(Melsec)")]
public class McFxLinks : MelseSerialDeviceBase, IDriver
{
    public override bool IsConnected { get; set; }

    [ConfigParameter("和校验")] public BoolEnum SumCheck { get; set; } = BoolEnum.True;

    public McFxLinks(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    // private MelsecFxLinks Driver;
    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver == null)
            {
                Driver = new MelsecFxLinks();
                Driver.SerialPortInni(EnumUtil.GetEnumDesc(SerialNumber), Convert.ToInt32(EnumUtil.GetEnumDesc(BaudRate)), DataBits, Stop, Checkout);
                OperateResult = Driver.Open();
                IsConnected = OperateResult.IsSuccess;
                Driver.SumCheck = SumCheck == BoolEnum.True;
                Driver.LogNet = new LogNetSingle("");
                var tcpNet = (MelsecFxLinks)Driver;
                tcpNet.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }
            else
            {
                OperateResult = Driver.Open();
            }
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}