using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Melsec;
using DriversInterface;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Melsec;

namespace MelsecA1EAscii;

[DriverSupported("MelsecA1EAscii")]
[DriverInfo("MelsecA1EAscii", "V1.1.0", "三菱(Melsec)")]
public class MelsecA1EAscii : MelsecNetworkDeviceBase, IDriver
{
    public MelsecA1EAscii(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    private MelsecA1EAsciiNet _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (MelsecA1EAsciiNet)value;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new MelsecA1EAsciiNet(IpAddress, Port)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}