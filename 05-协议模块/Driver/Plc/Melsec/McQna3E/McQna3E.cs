using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Melsec;
using DriversInterface;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Melsec;

namespace McQna3E;

/*
 * 更新日志
 * 版本:v1.2.0
 * 🐞 修复 `批量读取D区.地址无法正确读取`， `协议版本:v1.2.0` ⏱️`2024-05-14`
 */
[DriverSupported("McQna3E")]
[DriverInfo("McQna3E", "V1.2.0", "三菱(Melsec)")]
public class McQna3E : MelsecNetworkDeviceBase, IDriver
{
    public McQna3E(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    private MelsecMcNet _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (MelsecMcNet)value;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new MelsecMcNet(IpAddress, Port)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}