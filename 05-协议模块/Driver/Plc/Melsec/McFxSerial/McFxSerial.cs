using System;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Melsec;
using DriversInterface;
using Feng.Common.Util;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Melsec;

namespace McFxSerial;

[DriverSupported("McFxSerial")]
[DriverInfo("McFxSerial", "V1.0.0", "三菱(Melsec)")]
public class McFxSerial : MelseSerialDeviceBase, IDriver
{
    public override bool IsConnected { get; set; }

    [ConfigParameter("自动切换波特率")] public BoolEnum AutoChangeBaudRate { get; set; } = BoolEnum.False;

    public McFxSerial(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    private MelsecFxSerial _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (MelsecFxSerial)value;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new MelsecFxSerial();
                _driver.AutoChangeBaudRate = AutoChangeBaudRate == BoolEnum.True;
                _driver.SerialPortInni(EnumUtil.GetEnumDesc(SerialNumber),
                    Convert.ToInt32(EnumUtil.GetEnumDesc(BaudRate)),
                    DataBits,
                    Stop,
                    Checkout
                );
                OperateResult = _driver.Open();
                IsConnected = OperateResult.IsSuccess;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }
            else
            {
                OperateResult = _driver.Open();
            }
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }

    public override void Close()
    {
        Driver?.Close();
    }

    public override void Dispose()
    {
        Driver?.Dispose();
    }
}