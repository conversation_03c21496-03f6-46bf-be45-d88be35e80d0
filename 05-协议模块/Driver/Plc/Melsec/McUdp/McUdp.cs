using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Melsec;
using DriversInterface;
using HslCommunication;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Melsec;

namespace McUdp;

[DriverSupported("McUdp")]
[DriverInfo("McUdp", "V1.1.0", "三菱(Melsec)")]
public class McUdp : MelseNetworkUdpDeviceBase, IDriver
{
    public McUdp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public override bool IsConnected { get; set; }

    private MelsecMcUdp _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (MelsecMcUdp)value;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new MelsecMcUdp(IpAddress, Port);
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
                IsConnected = true;
            }

            OperateResult = new OperateResult
            {
                IsSuccess = true,
                Message = "UDP协议尝试读取中..."
            };
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}