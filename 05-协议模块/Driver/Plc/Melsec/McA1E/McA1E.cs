using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Melsec;
using DriversInterface;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Melsec;

namespace McA1E;

[DriverSupported("McA1E")]
[DriverInfo("McA1E", "V1.1.0", "三菱(Melsec)")]
public class McA1E : MelsecNetworkDeviceBase, IDriver
{
    public McA1E(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    private MelsecA1ENet _driver;

    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (MelsecA1ENet)value;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new MelsecA1ENet(IpAddress, Port)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (_, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}