using System;
using System.Text;
using System.Threading.Tasks;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Modbus;
using DriversInterface;
using HslCommunication;
using HslCommunication.Core;
using HslCommunication.LogNet;
using HslCommunication.ModBus;

namespace ModbusTcp;

[DriverSupported("ModbusTcp")]
[DriverInfo("ModbusTcp", "V1.0.0", "Modbus驱动")]
public class ModbusTcp : ModBusNetworkBase, IDriver
{
    [ConfigParameter("异步读取")] public BoolEnum IsAsyncRead { get; set; } = BoolEnum.True;

    public ModbusTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    /// <summary>
    ///     OmronFinsNet
    /// </summary>
    private ModbusTcpNet _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (ModbusTcpNet)value;
    }

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new ModbusTcpNet(IpAddress, Port, Station)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout,
                    DataFormat = DataFormat
                };
                if (ByteTransform == BoolEnum.True)
                    _driver.ByteTransform = new RegularByteTransform();
                if (StringReverse == BoolEnum.True)
                    _driver.IsStringReverse = true;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }

    /// <summary>
    ///     读取
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public override async Task<DriverReturnValueModel> Read(DriverAddressIoArgModel val)
    {
        if (IsAsyncRead == BoolEnum.True)
        {
            _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 读取地址:【{val.Address}】 数据类型:【{val.DataType}】", DriverInfo.DeviceId);
            return await base.Read(val);
        }

        _ = DriverInfo.Socket.DeviceConsole($"【Modbus】 同步读取地址:【{val.Address}】 数据类型:【{val.DataType}】", DriverInfo.DeviceId);
        return await ReadSync(val);
    }

    /// <summary>
    ///     同步方法读取
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public Task<DriverReturnValueModel> ReadSync(DriverAddressIoArgModel val)
    {
        DriverReturnValueModel ret = new()
        {
            DataType = val.DataType,
            Id = val.Id
        };
        try
        {
            if (IsConnected || DriverInfo.ConnectType is ConnectTypeEnum.SerialPort or ConnectTypeEnum.Udp)
            {
                switch (val.DataType)
                {
                    case DataTypeEnum.Bool:
                    {
                        var boolRead = _driver.ReadBool(val.Address);
                        if (!boolRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = boolRead.Message;
                            ret.ErrorCode = boolRead.ErrorCode;
                        }

                        ret.Value = boolRead.Content;
                        break;
                    }
                    case DataTypeEnum.Bit:
                    {
                        var boolRead = _driver.ReadBool(val.Address);
                        if (!boolRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = boolRead.Message;
                            ret.ErrorCode = boolRead.ErrorCode;
                        }

                        ret.Value = boolRead.Content ? 1 : 0;
                        break;
                    }
                    case DataTypeEnum.Uint16:
                        var uint16Read = _driver.ReadUInt16(val.Address);
                        if (!uint16Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = uint16Read.Message;
                            ret.ErrorCode = uint16Read.ErrorCode;
                        }

                        ret.Value = uint16Read.Content;
                        break;
                    case DataTypeEnum.Int16:
                        var int16Read = _driver.ReadInt16(val.Address);
                        if (!int16Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = int16Read.Message;
                            ret.ErrorCode = int16Read.ErrorCode;
                        }

                        ret.Value = int16Read.Content;
                        break;
                    case DataTypeEnum.Uint32:
                        var uint32Read = _driver.ReadUInt32(val.Address);
                        if (!uint32Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = uint32Read.Message;
                            ret.ErrorCode = uint32Read.ErrorCode;
                        }

                        ret.Value = uint32Read.Content;
                        break;
                    case DataTypeEnum.Int32:
                        var int32Read = _driver.ReadInt32(val.Address);
                        if (!int32Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = int32Read.Message;
                            ret.ErrorCode = int32Read.ErrorCode;
                        }

                        ret.Value = int32Read.Content;
                        break;
                    case DataTypeEnum.Float:
                        var floatRead = _driver.ReadFloat(val.Address);
                        if (!floatRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = floatRead.Message;
                            ret.ErrorCode = floatRead.ErrorCode;
                        }

                        if (float.IsNaN(floatRead.Content))
                            floatRead.Content = 0;
                        ret.Value = floatRead.Content;
                        break;
                    case DataTypeEnum.Uint64:
                        var uint64Read = _driver.ReadUInt64(val.Address);
                        if (!uint64Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = uint64Read.Message;
                            ret.ErrorCode = uint64Read.ErrorCode;
                        }

                        ret.Value = uint64Read.Content;
                        break;
                    case DataTypeEnum.Int64:
                        var int64Read = _driver.ReadInt64(val.Address);
                        if (!int64Read.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = int64Read.Message;
                            ret.ErrorCode = int64Read.ErrorCode;
                        }

                        ret.Value = int64Read.Content;
                        break;
                    case DataTypeEnum.Double:
                        var doubleRead = _driver.ReadDouble(val.Address);
                        if (!doubleRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = doubleRead.Message;
                            ret.ErrorCode = doubleRead.ErrorCode;
                        }

                        if (double.IsNaN(doubleRead.Content))
                            doubleRead.Content = 0;
                        ret.Value = doubleRead.Content;
                        break;
                    case DataTypeEnum.String:
                    {
                        OperateResult<string> stringRead;
                        // 字符串默认编码
                        val.Length = val.Length == 0 ? val.Length = 10 : val.Length;
                        switch (val.Encoding)
                        {
                            case StringEnum.Unicode:
                                stringRead = _driver.ReadString(val.Address, val.Length, Encoding.Unicode);
                                break;
                            case StringEnum.Ascii:
                                stringRead = _driver.ReadString(val.Address, val.Length, Encoding.ASCII);
                                break;
                            case StringEnum.UnicodeBig:
                                stringRead = _driver.ReadString(val.Address, val.Length,
                                    Encoding.BigEndianUnicode);
                                break;
                            case StringEnum.Utf32:
                                stringRead = _driver.ReadString(val.Address, val.Length, Encoding.UTF32);
                                break;
                            case StringEnum.Gb2312:
                                stringRead = _driver.ReadString(val.Address, val.Length,
                                    Encoding.GetEncoding("gb2312"));
                                break;
                            case StringEnum.Utf8:
                            default:
                                stringRead = _driver.ReadString(val.Address, val.Length, Encoding.UTF8);
                                break;
                        }

                        if (!stringRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = stringRead.Message;
                            ret.ErrorCode = stringRead.ErrorCode;
                        }

                        ret.Value = stringRead.Content;
                        break;
                    }
                    case DataTypeEnum.Bcd:
                    {
                        var bcdRead = _driver.Read(val.Address, 1);
                        if (!bcdRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = bcdRead.Message;
                            ret.ErrorCode = bcdRead.ErrorCode;
                        }

                        try
                        {
                            ret.Value = Convert.ToInt32(bcdRead.Content.ToHexString());
                        }
                        catch
                        {
                            ret.Value = bcdRead.Content.ToHexString();
                        }

                        break;
                    }
                    case DataTypeEnum.Bcd_32:
                    {
                        var bcdRead = _driver.Read(val.Address, 2);
                        if (!bcdRead.IsSuccess)
                        {
                            ret.VariableStatus = VariableStatusTypeEnum.Error;
                            ret.Message = bcdRead.Message;
                            ret.ErrorCode = bcdRead.ErrorCode;
                        }

                        var sortContent = ConvertAbBcdToCDab(bcdRead.Content);
                        ret.Value = Convert.ToInt32(sortContent.ToHexString());

                        break;
                    }
                    default:
                        throw new ArgumentOutOfRangeException();
                }
            }
            else
            {
                ret.VariableStatus = VariableStatusTypeEnum.Bad;
                ret.Message = "设备未连接";
                ret.ErrorCode = -1;
            }
        }
        catch (Exception ex)
        {
            ret.VariableStatus = VariableStatusTypeEnum.UnKnow;
            ret.Message = ex.Message;
        }

        return Task.FromResult(ret);
    }
}