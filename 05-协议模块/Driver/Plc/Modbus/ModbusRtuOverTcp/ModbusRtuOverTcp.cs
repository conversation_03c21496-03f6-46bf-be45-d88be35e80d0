using System;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Modbus;
using DriversInterface;
using HslCommunication.Core;
using HslCommunication.LogNet;

namespace ModbusRtuOverTcp;

[DriverSupported("ModbusRtuOverTcp")]
[DriverInfo("ModbusRtuOverTcp", "V1.1.0", "Modbus驱动")]
public class ModbusRtuOverTcp : ModBusNetworkBase, IDriver
{
    public ModbusRtuOverTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     OmronFinsNet
    /// </summary>
    private HslCommunication.ModBus.ModbusRtuOverTcp _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.ModBus.ModbusRtuOverTcp)value;
    }
    
    public override bool IsConnected => _driver != null && OperateResult.IsSuccess;

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new HslCommunication.ModBus.ModbusRtuOverTcp(IpAddress, Port, Station)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                if (ByteTransform == BoolEnum.True)
                    _driver.ByteTransform = new RegularByteTransform();
                if (StringReverse == BoolEnum.True)
                    _driver.IsStringReverse = true;

                _driver.DataFormat = DataFormat;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }
}