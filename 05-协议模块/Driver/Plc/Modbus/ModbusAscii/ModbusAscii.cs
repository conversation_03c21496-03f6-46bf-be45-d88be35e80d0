using System;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Modbus;
using DriversInterface;
using Feng.Common.Util;
using HslCommunication.Core;
using HslCommunication.LogNet;

namespace ModbusAscii;

[DriverSupported("ModbusAscii")]
[DriverInfo("ModbusAscii", "V1.1.0", "Modbus驱动")]
public class ModbusAscii : ModBusSerialBase, IDriver
{
    public ModbusAscii(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     OmronFinsNet
    /// </summary>
    private HslCommunication.ModBus.ModbusAscii _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.ModBus.ModbusAscii)value;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new HslCommunication.ModBus.ModbusAscii(Station);
                _driver.SerialPortInni(EnumUtil.GetEnumDesc(SerialNumber), Convert.ToInt32(EnumUtil.GetEnumDesc(BaudRate)), DataBits, Stop, Checkout);
                if (ByteTransform == BoolEnum.True)
                    _driver.ByteTransform = new RegularByteTransform();
                if (StringReverse == BoolEnum.True)
                    _driver.IsStringReverse = true;
                _driver.DataFormat = DataFormat;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.Open();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}