using System;
using Common.Enums;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Modbus;
using DriversInterface;
using HslCommunication.Core;
using HslCommunication.LogNet;

namespace ModbusAsciiOverTcp;

[DriverSupported("ModbusAsciiOverTcp")]
[DriverInfo("ModbusAsciiOverTcp", "V1.2.0", "Modbus驱动")]
public class ModbusAsciiOverTcp : ModBusNetworkBase, IDriver
{
    public ModbusAsciiOverTcp(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     OmronFinsNet
    /// </summary>
    private HslCommunication.ModBus.ModbusAsciiOverTcp _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (HslCommunication.ModBus.ModbusAsciiOverTcp)value;
    }

    public override bool IsConnected => Driver != null && OperateResult.IsSuccess;

    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new HslCommunication.ModBus.ModbusAsciiOverTcp(IpAddress, Port, Station)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout
                };
                if (ByteTransform == BoolEnum.True)
                    _driver.ByteTransform = new RegularByteTransform();
                if (StringReverse == BoolEnum.True)
                    _driver.IsStringReverse = true;
                _driver.DataFormat = DataFormat;
                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto { Message = $"连接异常:【{ex.Message}】" };
        }

        return ResConnect();
    }
}