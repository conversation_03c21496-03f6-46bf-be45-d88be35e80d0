using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Siemens;
using DriversInterface;
using Feng.Common.Util;
using HslCommunication.LogNet;

namespace SiemensPPI;

[DriverSupported("SiemensPPI")]
[DriverInfo("SiemensPPI", "V1.0.0", "西门子(Siemens)")]
public class SiemensPPI : SiemensSerialDeviceBase, IDriver
{
    public override bool IsConnected { get; set; }

    public SiemensPPI(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (Driver == null)
            {
                Driver = new HslCommunication.Profinet.Siemens.SiemensPPI {Station = Station};
                Driver.SerialPortInni(EnumUtil.GetEnumDesc(SerialNumber), Convert.ToInt32(EnumUtil.GetEnumDesc(BaudRate)), DataBits, Stop, Checkout);
                OperateResult = Driver.Open();
                IsConnected = OperateResult.IsSuccess;
                Driver.LogNet = new LogNetSingle("");
                var tcpNet = (HslCommunication.Profinet.Siemens.SiemensPPI) Driver;
                tcpNet.LogNet.BeforeSaveToFile += async (object sender, HslEventArgs e) => { await SocketSend(e); };
            }
            else
            {
                OperateResult = Driver.Open();
            }
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }
}