using System;
using Driver.Core.Attributes;
using Driver.Core.Models;
using Driver.Core.ReadBase.Siemens;
using DriversInterface;
using HslCommunication.LogNet;
using HslCommunication.Profinet.Siemens;

namespace SiemensS7;

[DriverSupported("SiemensS7")]
[DriverInfo("SiemensS7", "V1.4.0", "西门子(Siemens)")]
public class SiemensS7 : SiemensNetworkDeviceBase, IDriver
{
    /// <summary>
    /// </summary>
    /// <param name="driverInfo"></param>
    public SiemensS7(DriverInfoDto driverInfo)
    {
        DriverInfo = driverInfo;
    }

    /// <summary>
    ///     SiemensS7Net
    /// </summary>
    private SiemensS7Net _driver;

    /// <summary>
    /// </summary>
    public override dynamic Driver
    {
        get => _driver;
        set => _driver = (SiemensS7Net)value;
    }

    // HslCommunication v12.0 - GetPipeSocket()方法已被移除，使用OperateResult来判断连接状态
    public override bool IsConnected => _driver != null && (OperateResult?.IsSuccess ?? false);

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public DeviceConnectDto Connect()
    {
        try
        {
            if (_driver == null)
            {
                _driver = new SiemensS7Net(TypeRelationName, IpAddress)
                {
                    ReceiveTimeOut = Timeout,
                    ConnectTimeOut = Timeout,
                    Port = Port
                };
                if (TypeRelationName == SiemensPLCS.S200)
                {
                    _driver.LocalTSAP = Convert.ToInt32(LocalTsap, 16);
                    _driver.DestTSAP = Convert.ToInt32(DestTsap, 16);
                }

                _driver.LogNet = new LogNetSingle("");
                _driver.LogNet.BeforeSaveToFile += async (sender, e) => { await SocketSend(e); };
            }

            OperateResult = _driver.ConnectServer();
            IsConnected = OperateResult.IsSuccess;
        }
        catch (Exception ex)
        {
            return new DeviceConnectDto {Message = $"连接异常:【{ex.Message}】"};
        }

        return ResConnect();
    }
}